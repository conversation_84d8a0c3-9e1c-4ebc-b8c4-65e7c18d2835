import { useState } from "react";
import { DashboardLayout } from "@/components/layouts/dashboard-layout";
import { SlipUploader } from "@/components/slip/slip-uploader";
import { SlipResult } from "@/components/slip/slip-result";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { AlertTriangle, FileImage, Code, Info, ArrowRight } from "lucide-react";
import { useQuery } from "@tanstack/react-query";

interface VerificationResult {
  status: number;
  data?: any;
  message?: string;
}

interface UserPackage {
  isActive: boolean;
  requestsUsed: number;
  package: {
    requestsLimit: number;
    name: string;
  }
}

export default function SlipVerifyPage() {
  const [verificationResult, setVerificationResult] = useState<VerificationResult | null>(null);
  
  // ดึงข้อมูลแพ็กเกจที่ใช้งานอยู่
  const { data: activePackage } = useQuery<UserPackage>({
    queryKey: ['/api/user/active-package'],
  });
  
  // ตรวจสอบว่ามีแพ็กเกจใช้งานอยู่หรือไม่
  const hasActivePackage = activePackage && activePackage.isActive;
  
  // ตรวจสอบว่าครบจำนวนการใช้งานหรือไม่
  const hasReachedLimit = hasActivePackage && 
    activePackage.requestsUsed >= activePackage.package.requestsLimit;
  
  // คำนวณจำนวนการใช้งานที่เหลือ
  const remainingRequests = hasActivePackage 
    ? activePackage.package.requestsLimit - activePackage.requestsUsed
    : 0;
  
  // จัดการผลลัพธ์การตรวจสอบ
  const handleVerificationComplete = (result: VerificationResult) => {
    setVerificationResult(result);
  };
  
  return (
    <DashboardLayout>
      <div className="space-y-8">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">ตรวจสอบสลิปธนาคาร</h1>
          <p className="text-muted-foreground">
            อัปโหลดรูปภาพสลิปเพื่อตรวจสอบความถูกต้องและดึงข้อมูลอัตโนมัติ
          </p>
        </div>
        
        {/* แสดงการแจ้งเตือนกรณีไม่มีแพ็กเกจหรือใช้งานเกินโควต้า */}
        {!hasActivePackage && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>ไม่มีแพ็กเกจที่ใช้งานได้</AlertTitle>
            <AlertDescription>
              คุณยังไม่ได้สมัครแพ็กเกจหรือแพ็กเกจของคุณหมดอายุ กรุณาสมัครแพ็กเกจเพื่อใช้งานระบบตรวจสอบสลิป
            </AlertDescription>
          </Alert>
        )}
        
        {hasReachedLimit && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>ใช้งานครบจำนวนที่กำหนด</AlertTitle>
            <AlertDescription>
              คุณได้ใช้งานครบตามจำนวนที่กำหนดในแพ็กเกจ {activePackage.package.name} แล้ว 
              กรุณาอัปเกรดหรือต่ออายุแพ็กเกจเพื่อใช้งานเพิ่มเติม
            </AlertDescription>
          </Alert>
        )}
        
        {hasActivePackage && !hasReachedLimit && (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>แพ็กเกจปัจจุบัน: {activePackage.package.name}</AlertTitle>
            <AlertDescription>
              คุณมีการใช้งานคงเหลือ {remainingRequests} ครั้ง จากทั้งหมด {activePackage.package.requestsLimit} ครั้ง
            </AlertDescription>
          </Alert>
        )}
        
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
          {/* ส่วนอัปโหลดและผลลัพธ์ */}
          <div className="space-y-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileImage className="h-5 w-5 mr-2" />
                  อัปโหลดสลิปธนาคาร
                </CardTitle>
                <CardDescription>
                  อัปโหลดภาพสลิปธนาคารที่ต้องการตรวจสอบ รองรับไฟล์ JPG, PNG, GIF
                </CardDescription>
              </CardHeader>
              <CardContent>
                <SlipUploader 
                  onVerificationComplete={handleVerificationComplete} 
                />
              </CardContent>
            </Card>
            
            {/* แสดงผลลัพธ์การตรวจสอบ */}
            {verificationResult && (
              <SlipResult result={verificationResult} />
            )}
          </div>
          
          {/* ส่วนคำแนะนำการใช้งาน */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>คำแนะนำการใช้งาน</CardTitle>
                <CardDescription>
                  เพื่อให้การตรวจสอบเป็นไปอย่างแม่นยำและรวดเร็ว โปรดปฏิบัติตามคำแนะนำต่อไปนี้
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="image">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="image">คำแนะนำรูปภาพ</TabsTrigger>
                    <TabsTrigger value="api">การใช้งาน API</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="image" className="mt-4 space-y-4">
                    <div className="space-y-2">
                      <h3 className="font-semibold">คุณภาพของรูปภาพ</h3>
                      <p className="text-sm text-muted-foreground">
                        ตรวจสอบให้แน่ใจว่ารูปภาพมีความชัดเจน ไม่มืด ไม่เบลอ และไม่มีแสงสะท้อน
                      </p>
                    </div>
                    
                    <Separator />
                    
                    <div className="space-y-2">
                      <h3 className="font-semibold">ข้อมูลครบถ้วน</h3>
                      <p className="text-sm text-muted-foreground">
                        ภาพสลิปต้องแสดงข้อมูลสำคัญทั้งหมด เช่น ชื่อธนาคาร วันที่ เวลา จำนวนเงิน 
                        ชื่อผู้ส่ง ชื่อผู้รับ และเลขอ้างอิงธุรกรรม
                      </p>
                    </div>
                    
                    <Separator />
                    
                    <div className="space-y-2">
                      <h3 className="font-semibold">ธนาคารที่รองรับ</h3>
                      <p className="text-sm text-muted-foreground">
                        ระบบรองรับการตรวจสอบสลิปจากธนาคารทั้งหมดในประเทศไทย รวมถึงพร้อมเพย์และแอปพลิเคชันธนาคารต่างๆ
                      </p>
                    </div>
                    
                    <Separator />
                    
                    <div className="space-y-2">
                      <h3 className="font-semibold">ขนาดไฟล์</h3>
                      <p className="text-sm text-muted-foreground">
                        ขนาดไฟล์ไม่ควรเกิน 5MB และควรเป็นไฟล์รูปภาพประเภท JPG, PNG หรือ GIF
                      </p>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="api" className="mt-4 space-y-4">
                    <div className="space-y-2">
                      <h3 className="font-semibold">Endpoint</h3>
                      <div className="flex items-center bg-secondary-50 p-2 rounded-md">
                        <Code className="h-4 w-4 text-primary-700 mr-2" />
                        <code className="text-sm">POST /api/v1/verify</code>
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div className="space-y-2">
                      <h3 className="font-semibold">Headers ที่จำเป็น</h3>
                      <p className="text-sm text-muted-foreground">
                        <code className="bg-secondary-50 px-1 py-0.5 rounded">Content-Type: multipart/form-data</code>
                        <br />
                        <code className="bg-secondary-50 px-1 py-0.5 rounded">Authorization: Bearer YOUR_ACCESS_TOKEN</code>
                      </p>
                    </div>
                    
                    <Separator />
                    
                    <div className="space-y-2">
                      <h3 className="font-semibold">ตัวอย่างการใช้งาน (Node.js)</h3>
                      <pre className="bg-neutral-800 text-neutral-100 p-3 rounded-md text-xs overflow-x-auto">
{`const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

const verifySlip = async () => {
  const formData = new FormData();
  formData.append('file', fs.createReadStream('slip.jpg'));

  try {
    const response = await axios.post(
      'https://api.verifyslip.com/api/v1/verify',
      formData,
      {
        headers: {
          'Authorization': 'Bearer YOUR_TOKEN',
          ...formData.getHeaders()
        }
      }
    );

    console.log(response.data);
  } catch (error) {
    console.error('Error:', error.response?.data);
  }
};

verifySlip();`}
                      </pre>
                    </div>
                    
                    <div className="flex items-center justify-center">
                      <a href="/docs" className="inline-flex items-center text-primary-600 hover:text-primary-700 font-medium">
                        ดูเอกสาร API ฉบับเต็ม <ArrowRight className="ml-2 h-4 w-4" />
                      </a>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
