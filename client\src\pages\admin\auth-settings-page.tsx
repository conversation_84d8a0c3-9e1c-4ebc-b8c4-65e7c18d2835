import React, { useState, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { Admin } from "@/components/layouts/admin-layout";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Separator } from "@/components/ui/separator";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { queryClient } from "@/lib/queryClient";
import {
  RefreshCcw,
  MailCheck,
  Smartphone,
  Key,
  Save,
  Send as SendIcon,
  ExternalLink,
  AlertCircle,
  Info,
  ChevronsUpDown,
  LineChart,
  Facebook,
  Mail,
  UserCheck,
  UserPlus,
  LucideIcon,
  Loader2,
  Shield,
  HelpCircle,
} from "lucide-react";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { motion } from "framer-motion";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

// Schema สำหรับตั้งค่าระบบรักษาความปลอดภัย
const securitySettingsSchema = z.object({
  passwordMinLength: z.number().min(6).max(32),
  passwordRequireNumbers: z.boolean(),
  passwordRequireSpecialChars: z.boolean(),
  maxLoginAttempts: z.number().min(1).max(20),
  loginLockoutTime: z.number().min(1).max(1440), // เวลาในนาที
  sessionTimeout: z.number().min(10).max(10080), // เวลาในนาที (สูงสุด 1 สัปดาห์)
});

// Schema สำหรับตั้งค่า social login
const socialLoginSchema = z.object({
  enableLineLogin: z.boolean(),
  lineClientId: z.string().optional(),
  lineClientSecret: z.string().optional(),
  lineAuthCallback: z.string().optional(),

  enableFacebookLogin: z.boolean(),
  facebookAppId: z.string().optional(),
  facebookAppSecret: z.string().optional(),
  facebookAuthCallback: z.string().optional(),

  enableGoogleLogin: z.boolean(),
  googleClientId: z.string().optional(),
  googleClientSecret: z.string().optional(),
  googleAuthCallback: z.string().optional(),
});

// Schema สำหรับตั้งค่า SMS ส่งรหัส OTP
const otpSettingsSchema = z.object({
  otpLength: z.number().min(4).max(8),
  otpExpiry: z.number().min(1).max(60), // เวลาในนาที
  otpResendDelay: z.number().min(30).max(300), // เวลาในวินาที
  smsmktApiKey: z.string().optional(),
  smsmktSecret: z.string().optional(),
  smsSender: z.string().max(11).optional(), // Sender ID สำหรับการส่ง SMS (ไม่เกิน 11 ตัวอักษร)
  offlineSimulation: z.boolean(), // จำลองการส่ง OTP เมื่อไม่มีการเชื่อมต่อกับ API จริง
});

// Interface สำหรับแสดงข้อมูลวิธีการล็อกอิน
interface LoginMethodStats {
  username_password: number;
  line: number;
  facebook: number;
  google: number;
  phone: number;
}

// คอมโพเนนต์แสดงสถิติการล็อกอิน
const LoginStatsCard = () => {
  const { data: loginStats, isLoading } = useQuery({
    queryKey: ['/api/admin/auth-stats'],
    queryFn: async () => {
      const response = await fetch('/api/admin/auth-stats');
      if (!response.ok) {
        throw new Error('ไม่สามารถโหลดข้อมูลสถิติได้');
      }
      return response.json();
    }
  });

  return (
    <Card className="bg-indigo-950/50 border-amber-500/20">
      <CardHeader>
        <CardTitle className="bg-clip-text text-transparent bg-gradient-to-r from-amber-200 to-amber-400">
          สถิติการล็อกอิน
        </CardTitle>
        <CardDescription className="text-indigo-300">
          ข้อมูลการเข้าสู่ระบบในรอบ 30 วันที่ผ่านมา
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {isLoading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 text-amber-500 animate-spin" />
          </div>
        ) : (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-indigo-900/30 rounded-lg p-4 border border-indigo-600/30">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-indigo-300">การล็อกอินทั้งหมด</span>
                  <UserCheck className="h-5 w-5 text-amber-400" />
                </div>
                <p className="text-2xl font-bold text-white">{loginStats?.totalLogins || 0}</p>
              </div>

              <div className="bg-indigo-900/30 rounded-lg p-4 border border-indigo-600/30">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-indigo-300">อัตราความสำเร็จ</span>
                  <LineChart className="h-5 w-5 text-amber-400" />
                </div>
                <p className="text-2xl font-bold text-white">{loginStats?.successRate || 0}%</p>
              </div>

              <div className="bg-indigo-900/30 rounded-lg p-4 border border-indigo-600/30">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-indigo-300">ผู้สมัครใหม่</span>
                  <UserPlus className="h-5 w-5 text-amber-400" />
                </div>
                <p className="text-2xl font-bold text-white">{loginStats?.newRegistrations || 0}</p>
              </div>
            </div>

            <h3 className="text-lg font-medium text-indigo-200 mt-4 mb-2">วิธีการล็อกอินยอดนิยม</h3>

            <div className="space-y-3">
              {loginStats?.methods && (
                <>
                  <LoginMethodBar
                    icon={Key}
                    label="ชื่อผู้ใช้/รหัสผ่าน"
                    count={loginStats.methods.username_password || 0}
                    total={loginStats.totalLogins}
                    color="from-emerald-600 to-emerald-400"
                  />

                  <LoginMethodBar
                    icon={Smartphone}
                    label="เบอร์โทรศัพท์"
                    count={loginStats.methods.phone || 0}
                    total={loginStats.totalLogins}
                    color="from-blue-600 to-blue-400"
                  />

                  {loginStats.methods.line > 0 && (
                    <LoginMethodBar
                      icon={Info}
                      label="LINE"
                      count={loginStats.methods.line || 0}
                      total={loginStats.totalLogins}
                      color="from-green-600 to-green-400"
                    />
                  )}

                  {loginStats.methods.facebook > 0 && (
                    <LoginMethodBar
                      icon={Facebook}
                      label="Facebook"
                      count={loginStats.methods.facebook || 0}
                      total={loginStats.totalLogins}
                      color="from-blue-700 to-blue-500"
                    />
                  )}

                  {loginStats.methods.google > 0 && (
                    <LoginMethodBar
                      icon={Mail}
                      label="Google"
                      count={loginStats.methods.google || 0}
                      total={loginStats.totalLogins}
                      color="from-red-600 to-red-400"
                    />
                  )}
                </>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// คอมโพเนนต์แสดงแถบเมธอดล็อกอิน
interface LoginMethodBarProps {
  icon: LucideIcon;
  label: string;
  count: number;
  total: number;
  color: string;
}

const LoginMethodBar = ({ icon: Icon, label, count, total, color }: LoginMethodBarProps) => {
  const percentage = total > 0 ? Math.round((count / total) * 100) : 0;

  return (
    <div className="space-y-1">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Icon className="h-4 w-4 text-indigo-300" />
          <span className="text-sm text-indigo-200">{label}</span>
        </div>
        <span className="text-sm font-medium text-indigo-300">{count} ({percentage}%)</span>
      </div>
      <div className="h-2 w-full bg-indigo-950/50 rounded-full overflow-hidden">
        <motion.div
          className={`h-full rounded-full bg-gradient-to-r ${color}`}
          initial={{ width: 0 }}
          animate={{ width: `${percentage}%` }}
          transition={{ duration: 1, ease: "easeOut" }}
        />
      </div>
    </div>
  );
};

// คอมโพเนนต์ Tooltip ช่วยเหลือ
interface HelpTooltipProps {
  content: string;
}

const HelpTooltip = ({ content }: HelpTooltipProps) => (
  <TooltipProvider>
    <Tooltip>
      <TooltipTrigger asChild>
        <HelpCircle className="h-4 w-4 text-amber-500/70 hover:text-amber-500 transition-colors cursor-help ml-1" />
      </TooltipTrigger>
      <TooltipContent className="bg-indigo-900 border-amber-500/50 text-indigo-100 max-w-xs">
        {content}
      </TooltipContent>
    </Tooltip>
  </TooltipProvider>
);

// หน้าตั้งค่าระบบล็อกอินสำหรับแอดมิน
const AuthSettingsPage = () => {
  const { toast } = useToast();
  const [isTestingSMS, setIsTestingSMS] = useState(false);
  const [testPhone, setTestPhone] = useState("");

  // โหลดการตั้งค่าล็อกอิน
  const { data: authSettings, isLoading } = useQuery({
    queryKey: ['/api/admin/auth-settings'],
    queryFn: async () => {
      const response = await fetch('/api/admin/auth-settings');
      if (!response.ok) {
        throw new Error('ไม่สามารถโหลดการตั้งค่าได้');
      }
      return response.json();
    }
  });

  // ฟอร์มตั้งค่าความปลอดภัย
  const securityForm = useForm<z.infer<typeof securitySettingsSchema>>({
    resolver: zodResolver(securitySettingsSchema),
    defaultValues: {
      passwordMinLength: 8,
      passwordRequireNumbers: true,
      passwordRequireSpecialChars: false,
      maxLoginAttempts: 5,
      loginLockoutTime: 30,
      sessionTimeout: 1440, // 1 วัน
    },
  });

  // ฟอร์มตั้งค่า Social Login
  const socialLoginForm = useForm<z.infer<typeof socialLoginSchema>>({
    resolver: zodResolver(socialLoginSchema),
    defaultValues: {
      enableLineLogin: false,
      enableFacebookLogin: false,
      enableGoogleLogin: false,
    },
  });

  // ฟอร์มตั้งค่า OTP
  const otpForm = useForm<z.infer<typeof otpSettingsSchema>>({
    resolver: zodResolver(otpSettingsSchema),
    defaultValues: {
      otpLength: 6,
      otpExpiry: 5,
      otpResendDelay: 60,
      offlineSimulation: true,
    },
  });

  // อัปเดตค่าในฟอร์มเมื่อข้อมูลจาก API โหลดเสร็จ
  useEffect(() => {
    if (authSettings && !isLoading) {
      // อัปเดตฟอร์มตั้งค่าความปลอดภัย
      if (authSettings.security) {
        securityForm.reset(authSettings.security);
      }

      // อัปเดตฟอร์มตั้งค่า Social Login
      if (authSettings.socialLogin) {
        socialLoginForm.reset(authSettings.socialLogin);
      }

      // อัปเดตฟอร์มตั้งค่า OTP
      if (authSettings.otp) {
        otpForm.reset(authSettings.otp);
      }
    }
  }, [authSettings, isLoading]);

  // Mutation สำหรับบันทึกการตั้งค่าความปลอดภัย
  const saveSecuritySettingsMutation = useMutation({
    mutationFn: async (data: z.infer<typeof securitySettingsSchema>) => {
      const response = await fetch('/api/admin/auth-settings/security', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('ไม่สามารถบันทึกการตั้งค่าได้');
      }

      return await response.json();
    },
    onSuccess: () => {
      toast({
        title: "บันทึกการตั้งค่าเรียบร้อย",
        description: "การตั้งค่าระบบความปลอดภัยถูกอัปเดตแล้ว",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/auth-settings'] });
    },
    onError: (error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Mutation สำหรับบันทึกการตั้งค่า Social Login
  const saveSocialLoginSettingsMutation = useMutation({
    mutationFn: async (data: z.infer<typeof socialLoginSchema>) => {
      const response = await fetch('/api/admin/auth-settings/social', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('ไม่สามารถบันทึกการตั้งค่าได้');
      }

      return await response.json();
    },
    onSuccess: () => {
      toast({
        title: "บันทึกการตั้งค่าเรียบร้อย",
        description: "การตั้งค่า Social Login ถูกอัปเดตแล้ว",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/auth-settings'] });
    },
    onError: (error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Mutation สำหรับบันทึกการตั้งค่า OTP
  const saveOtpSettingsMutation = useMutation({
    mutationFn: async (data: z.infer<typeof otpSettingsSchema>) => {
      const response = await fetch('/api/admin/auth-settings/otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('ไม่สามารถบันทึกการตั้งค่าได้');
      }

      return await response.json();
    },
    onSuccess: () => {
      toast({
        title: "บันทึกการตั้งค่าเรียบร้อย",
        description: "การตั้งค่า OTP ถูกอัปเดตแล้ว",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/auth-settings'] });
    },
    onError: (error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Mutation สำหรับทดสอบการส่ง OTP
  const testSendOtpMutation = useMutation({
    mutationFn: async (phoneNumber: string) => {
      const response = await fetch('/api/admin/test-send-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phoneNumber }),
      });

      if (!response.ok) {
        throw new Error('ไม่สามารถส่ง OTP ทดสอบได้');
      }

      return await response.json();
    },
    onSuccess: (data) => {
      toast({
        title: "ส่ง OTP ทดสอบเรียบร้อย",
        description: `รหัส OTP ถูกส่งไปยัง ${testPhone} ${data.offlineMode ? "(โหมดจำลอง - ไม่มีการส่งจริง)" : ""}`,
      });
      setIsTestingSMS(false);
    },
    onError: (error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive",
      });
      setIsTestingSMS(false);
    },
  });

  const handleTestSendOtp = () => {
    if (!testPhone || testPhone.length < 10) {
      toast({
        title: "กรุณาระบุเบอร์โทรศัพท์ที่ถูกต้อง",
        variant: "destructive",
      });
      return;
    }

    setIsTestingSMS(true);
    testSendOtpMutation.mutate(testPhone);
  };

  // บันทึกการตั้งค่าความปลอดภัย
  const onSubmitSecuritySettings = (data: z.infer<typeof securitySettingsSchema>) => {
    saveSecuritySettingsMutation.mutate(data);
  };

  // บันทึกการตั้งค่า Social Login
  const onSubmitSocialLoginSettings = (data: z.infer<typeof socialLoginSchema>) => {
    saveSocialLoginSettingsMutation.mutate(data);
  };

  // บันทึกการตั้งค่า OTP
  const onSubmitOtpSettings = (data: z.infer<typeof otpSettingsSchema>) => {
    saveOtpSettingsMutation.mutate(data);
  };

  return (
    <Admin>
      <div className="container mx-auto py-6 space-y-8">
        <div className="flex flex-col space-y-2">
          <h1 className="text-3xl font-bold tracking-tight text-transparent bg-clip-text bg-gradient-to-r from-amber-200 via-amber-300 to-amber-200">
            ตั้งค่าระบบยืนยันตัวตน
          </h1>
          <p className="text-indigo-300">
            กำหนดค่าระบบการล็อกอิน, การสมัครสมาชิก และวิธีการยืนยันตัวตนทั้งหมด
          </p>
        </div>

        {/* สถิติการล็อกอิน */}
        <LoginStatsCard />

        {isLoading ? (
          <div className="flex justify-center py-12">
            <Loader2 className="h-8 w-8 text-amber-500 animate-spin" />
          </div>
        ) : (
          <Tabs defaultValue="security" className="w-full">
            <TabsList className="grid grid-cols-3 mb-8 bg-indigo-900/40 border border-indigo-800/50">
              <TabsTrigger value="security" className="data-[state=active]:bg-amber-500 data-[state=active]:text-indigo-950">
                <Shield className="h-4 w-4 mr-2" />
                ความปลอดภัย
              </TabsTrigger>
              <TabsTrigger value="social" className="data-[state=active]:bg-amber-500 data-[state=active]:text-indigo-950">
                <ExternalLink className="h-4 w-4 mr-2" />
                Social Login
              </TabsTrigger>
              <TabsTrigger value="otp" className="data-[state=active]:bg-amber-500 data-[state=active]:text-indigo-950">
                <Smartphone className="h-4 w-4 mr-2" />
                OTP และ SMS
              </TabsTrigger>
            </TabsList>

            {/* แท็บตั้งค่าความปลอดภัย */}
            <TabsContent value="security" className="space-y-4">
              <Card className="bg-indigo-950/50 border-amber-500/20">
                <CardHeader>
                  <div className="flex items-center">
                    <Shield className="h-5 w-5 text-amber-400 mr-2" />
                    <CardTitle className="bg-clip-text text-transparent bg-gradient-to-r from-amber-200 to-amber-400">
                      ตั้งค่าความปลอดภัย
                    </CardTitle>
                  </div>
                  <CardDescription className="text-indigo-300">
                    กำหนดนโยบายรหัสผ่านและการรักษาความปลอดภัยของเซสชัน
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Form {...securityForm}>
                    <form onSubmit={securityForm.handleSubmit(onSubmitSecuritySettings)} className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-4">
                          <div className="flex items-center">
                            <h3 className="text-lg font-medium text-amber-300">นโยบายรหัสผ่าน</h3>
                            <HelpTooltip content="กำหนดความซับซ้อนของรหัสผ่านเพื่อเพิ่มความปลอดภัยให้กับผู้ใช้งาน" />
                          </div>
                          <Separator className="bg-indigo-800/30 my-2" />

                          <div className="space-y-4">
                            <FormField
                              control={securityForm.control}
                              name="passwordMinLength"
                              render={({ field }) => (
                                <FormItem>
                                  <div className="flex items-center">
                                    <FormLabel className="text-indigo-200">ความยาวรหัสผ่านขั้นต่ำ</FormLabel>
                                    <HelpTooltip content="จำนวนตัวอักษรขั้นต่ำที่ต้องการในรหัสผ่าน ค่าที่แนะนำคือ 8 หรือมากกว่า" />
                                  </div>
                                  <FormControl>
                                    <Input
                                      type="number"
                                      min={6}
                                      max={32}
                                      className="bg-indigo-950/50 border-indigo-700 text-indigo-100"
                                      {...field}
                                      onChange={(e) => field.onChange(parseInt(e.target.value))}
                                    />
                                  </FormControl>
                                  <FormDescription className="text-indigo-400">
                                    จำนวนตัวอักษรขั้นต่ำที่ต้องการ (6-32)
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={securityForm.control}
                              name="passwordRequireNumbers"
                              render={({ field }) => (
                                <FormItem className="flex flex-row items-center justify-between border rounded-lg border-indigo-800/40 p-4 bg-indigo-900/20">
                                  <div className="space-y-0.5">
                                    <FormLabel className="text-indigo-200">ต้องมีตัวเลข</FormLabel>
                                    <FormDescription className="text-indigo-400">
                                      รหัสผ่านต้องมีตัวเลขอย่างน้อย 1 ตัว
                                    </FormDescription>
                                  </div>
                                  <FormControl>
                                    <Switch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={securityForm.control}
                              name="passwordRequireSpecialChars"
                              render={({ field }) => (
                                <FormItem className="flex flex-row items-center justify-between border rounded-lg border-indigo-800/40 p-4 bg-indigo-900/20">
                                  <div className="space-y-0.5">
                                    <FormLabel className="text-indigo-200">ต้องมีอักขระพิเศษ</FormLabel>
                                    <FormDescription className="text-indigo-400">
                                      รหัสผ่านต้องมีอักขระพิเศษอย่างน้อย 1 ตัว
                                    </FormDescription>
                                  </div>
                                  <FormControl>
                                    <Switch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>

                        <div className="space-y-4">
                          <div className="flex items-center">
                            <h3 className="text-lg font-medium text-amber-300">การล็อกอินและเซสชัน</h3>
                            <HelpTooltip content="กำหนดการป้องกันการโจมตีแบบ Brute Force และการจัดการเซสชัน" />
                          </div>
                          <Separator className="bg-indigo-800/30 my-2" />

                          <div className="space-y-4">
                            <FormField
                              control={securityForm.control}
                              name="maxLoginAttempts"
                              render={({ field }) => (
                                <FormItem>
                                  <div className="flex items-center">
                                    <FormLabel className="text-indigo-200">จำนวนครั้งสูงสุดที่ล็อกอินไม่สำเร็จ</FormLabel>
                                    <HelpTooltip content="จำนวนครั้งที่อนุญาตให้ล็อกอินผิดก่อนล็อคบัญชี เพื่อป้องกันการโจมตีแบบ Brute Force" />
                                  </div>
                                  <FormControl>
                                    <Input
                                      type="number"
                                      min={1}
                                      max={20}
                                      className="bg-indigo-950/50 border-indigo-700 text-indigo-100"
                                      {...field}
                                      onChange={(e) => field.onChange(parseInt(e.target.value))}
                                    />
                                  </FormControl>
                                  <FormDescription className="text-indigo-400">
                                    จำนวนครั้งที่อนุญาตให้ล็อกอินผิดพลาดก่อนล็อคบัญชี
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={securityForm.control}
                              name="loginLockoutTime"
                              render={({ field }) => (
                                <FormItem>
                                  <div className="flex items-center">
                                    <FormLabel className="text-indigo-200">ระยะเวลาล็อคบัญชี (นาที)</FormLabel>
                                    <HelpTooltip content="ระยะเวลาที่ล็อคบัญชีหลังจากล็อกอินผิดเกินจำนวนครั้งที่กำหนด" />
                                  </div>
                                  <FormControl>
                                    <Input
                                      type="number"
                                      min={1}
                                      max={1440}
                                      className="bg-indigo-950/50 border-indigo-700 text-indigo-100"
                                      {...field}
                                      onChange={(e) => field.onChange(parseInt(e.target.value))}
                                    />
                                  </FormControl>
                                  <FormDescription className="text-indigo-400">
                                    ระยะเวลาที่ล็อคบัญชีเมื่อล็อกอินผิดพลาดเกินกำหนด
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={securityForm.control}
                              name="sessionTimeout"
                              render={({ field }) => (
                                <FormItem>
                                  <div className="flex items-center">
                                    <FormLabel className="text-indigo-200">เวลาหมดอายุเซสชัน (นาที)</FormLabel>
                                    <HelpTooltip content="ระยะเวลาที่เซสชันจะหมดอายุหลังจากไม่มีการใช้งาน เพื่อป้องกันการเข้าถึงโดยไม่ได้รับอนุญาต" />
                                  </div>
                                  <FormControl>
                                    <Input
                                      type="number"
                                      min={10}
                                      max={10080}
                                      className="bg-indigo-950/50 border-indigo-700 text-indigo-100"
                                      {...field}
                                      onChange={(e) => field.onChange(parseInt(e.target.value))}
                                    />
                                  </FormControl>
                                  <FormDescription className="text-indigo-400">
                                    ระยะเวลาที่เซสชันจะหมดอายุหลังจากไม่มีกิจกรรม
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>
                      </div>

                      <div className="flex justify-end">
                        <Button
                          type="submit"
                          className="bg-gradient-to-r from-amber-500 to-amber-600 text-indigo-950 hover:from-amber-600 hover:to-amber-700"
                          disabled={saveSecuritySettingsMutation.isPending}
                        >
                          {saveSecuritySettingsMutation.isPending ? (
                            <><Loader2 className="h-4 w-4 mr-2 animate-spin" /> กำลังบันทึก...</>
                          ) : (
                            <><Save className="h-4 w-4 mr-2" /> บันทึกการตั้งค่า</>
                          )}
                        </Button>
                      </div>
                    </form>
                  </Form>
                </CardContent>
              </Card>
            </TabsContent>

            {/* แท็บ Social Login */}
            <TabsContent value="social" className="space-y-4">
              <Card className="bg-indigo-950/50 border-amber-500/20">
                <CardHeader>
                  <div className="flex items-center">
                    <ExternalLink className="h-5 w-5 text-amber-400 mr-2" />
                    <CardTitle className="bg-clip-text text-transparent bg-gradient-to-r from-amber-200 to-amber-400">
                      Social Login
                    </CardTitle>
                  </div>
                  <CardDescription className="text-indigo-300">
                    ตั้งค่าการล็อกอินผ่านบริการภายนอก เช่น LINE, Facebook, Google
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Form {...socialLoginForm}>
                    <form onSubmit={socialLoginForm.handleSubmit(onSubmitSocialLoginSettings)} className="space-y-6">
                      <Alert className="bg-indigo-900/30 border-amber-500/30">
                        <Info className="h-4 w-4 text-amber-400" />
                        <AlertTitle className="text-amber-300">คำแนะนำ</AlertTitle>
                        <AlertDescription className="text-indigo-200">
                          คุณต้องลงทะเบียนแอปพลิเคชันของคุณกับแต่ละแพลตฟอร์มเพื่อรับรหัส client ID และ client secret
                          สำหรับการเชื่อมต่อ OAuth. URL callback ควรชี้ไปที่เส้นทาง API ที่ถูกต้องบนเซิร์ฟเวอร์ของคุณ.
                        </AlertDescription>
                      </Alert>

                      <Accordion type="single" collapsible className="w-full space-y-4">
                        {/* LINE Login */}
                        <AccordionItem value="line" className="border-indigo-800/40 rounded-lg overflow-hidden">
                          <AccordionTrigger className="px-4 py-2 bg-indigo-900/20 hover:bg-indigo-900/40 rounded-lg text-indigo-100">
                            <div className="flex items-center">
                              <div className="h-8 w-8 rounded-full bg-green-600 flex items-center justify-center mr-3">
                                <Info className="h-4 w-4 text-white" />
                              </div>
                              LINE Login
                            </div>
                          </AccordionTrigger>
                          <AccordionContent className="px-4 py-3 bg-indigo-900/10 space-y-4">
                            <FormField
                              control={socialLoginForm.control}
                              name="enableLineLogin"
                              render={({ field }) => (
                                <FormItem className="flex flex-row items-center justify-between border rounded-lg border-indigo-800/40 p-4 bg-indigo-900/20">
                                  <div className="space-y-0.5">
                                    <FormLabel className="text-indigo-200">เปิดใช้งาน LINE Login</FormLabel>
                                    <FormDescription className="text-indigo-400">
                                      อนุญาตให้ผู้ใช้ล็อกอินด้วยบัญชี LINE
                                    </FormDescription>
                                  </div>
                                  <FormControl>
                                    <Switch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />

                            {socialLoginForm.watch("enableLineLogin") && (
                              <div className="space-y-4">
                                <FormField
                                  control={socialLoginForm.control}
                                  name="lineClientId"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel className="text-indigo-200">LINE Client ID</FormLabel>
                                      <FormControl>
                                        <Input
                                          className="bg-indigo-950/50 border-indigo-700 text-indigo-100"
                                          placeholder="ใส่ LINE Client ID"
                                          {...field}
                                        />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />

                                <FormField
                                  control={socialLoginForm.control}
                                  name="lineClientSecret"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel className="text-indigo-200">LINE Client Secret</FormLabel>
                                      <FormControl>
                                        <Input
                                          type="password"
                                          className="bg-indigo-950/50 border-indigo-700 text-indigo-100"
                                          placeholder="ใส่ LINE Client Secret"
                                          {...field}
                                        />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />

                                <FormField
                                  control={socialLoginForm.control}
                                  name="lineAuthCallback"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel className="text-indigo-200">LINE Callback URL</FormLabel>
                                      <FormControl>
                                        <Input
                                          className="bg-indigo-950/50 border-indigo-700 text-indigo-100"
                                          placeholder="https://yourdomain.com/api/auth/line/callback"
                                          {...field}
                                        />
                                      </FormControl>
                                      <FormDescription className="text-indigo-400">
                                        URL สำหรับรับคำขอหลังจากการตรวจสอบสิทธิ์ LINE สำเร็จ
                                      </FormDescription>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              </div>
                            )}
                          </AccordionContent>
                        </AccordionItem>

                        {/* Facebook Login */}
                        <AccordionItem value="facebook" className="border-indigo-800/40 rounded-lg overflow-hidden">
                          <AccordionTrigger className="px-4 py-2 bg-indigo-900/20 hover:bg-indigo-900/40 rounded-lg text-indigo-100">
                            <div className="flex items-center">
                              <div className="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center mr-3">
                                <Facebook className="h-4 w-4 text-white" />
                              </div>
                              Facebook Login
                            </div>
                          </AccordionTrigger>
                          <AccordionContent className="px-4 py-3 bg-indigo-900/10 space-y-4">
                            <FormField
                              control={socialLoginForm.control}
                              name="enableFacebookLogin"
                              render={({ field }) => (
                                <FormItem className="flex flex-row items-center justify-between border rounded-lg border-indigo-800/40 p-4 bg-indigo-900/20">
                                  <div className="space-y-0.5">
                                    <FormLabel className="text-indigo-200">เปิดใช้งาน Facebook Login</FormLabel>
                                    <FormDescription className="text-indigo-400">
                                      อนุญาตให้ผู้ใช้ล็อกอินด้วยบัญชี Facebook
                                    </FormDescription>
                                  </div>
                                  <FormControl>
                                    <Switch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />

                            {socialLoginForm.watch("enableFacebookLogin") && (
                              <div className="space-y-4">
                                <FormField
                                  control={socialLoginForm.control}
                                  name="facebookAppId"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel className="text-indigo-200">Facebook App ID</FormLabel>
                                      <FormControl>
                                        <Input
                                          className="bg-indigo-950/50 border-indigo-700 text-indigo-100"
                                          placeholder="ใส่ Facebook App ID"
                                          {...field}
                                        />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />

                                <FormField
                                  control={socialLoginForm.control}
                                  name="facebookAppSecret"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel className="text-indigo-200">Facebook App Secret</FormLabel>
                                      <FormControl>
                                        <Input
                                          type="password"
                                          className="bg-indigo-950/50 border-indigo-700 text-indigo-100"
                                          placeholder="ใส่ Facebook App Secret"
                                          {...field}
                                        />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />

                                <FormField
                                  control={socialLoginForm.control}
                                  name="facebookAuthCallback"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel className="text-indigo-200">Facebook Callback URL</FormLabel>
                                      <FormControl>
                                        <Input
                                          className="bg-indigo-950/50 border-indigo-700 text-indigo-100"
                                          placeholder="https://yourdomain.com/api/auth/facebook/callback"
                                          {...field}
                                        />
                                      </FormControl>
                                      <FormDescription className="text-indigo-400">
                                        URL สำหรับรับคำขอหลังจากการตรวจสอบสิทธิ์ Facebook สำเร็จ
                                      </FormDescription>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              </div>
                            )}
                          </AccordionContent>
                        </AccordionItem>

                        {/* Google Login */}
                        <AccordionItem value="google" className="border-indigo-800/40 rounded-lg overflow-hidden">
                          <AccordionTrigger className="px-4 py-2 bg-indigo-900/20 hover:bg-indigo-900/40 rounded-lg text-indigo-100">
                            <div className="flex items-center">
                              <div className="h-8 w-8 rounded-full bg-red-600 flex items-center justify-center mr-3">
                                <Mail className="h-4 w-4 text-white" />
                              </div>
                              Google Login
                            </div>
                          </AccordionTrigger>
                          <AccordionContent className="px-4 py-3 bg-indigo-900/10 space-y-4">
                            <FormField
                              control={socialLoginForm.control}
                              name="enableGoogleLogin"
                              render={({ field }) => (
                                <FormItem className="flex flex-row items-center justify-between border rounded-lg border-indigo-800/40 p-4 bg-indigo-900/20">
                                  <div className="space-y-0.5">
                                    <FormLabel className="text-indigo-200">เปิดใช้งาน Google Login</FormLabel>
                                    <FormDescription className="text-indigo-400">
                                      อนุญาตให้ผู้ใช้ล็อกอินด้วยบัญชี Google
                                    </FormDescription>
                                  </div>
                                  <FormControl>
                                    <Switch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />

                            {socialLoginForm.watch("enableGoogleLogin") && (
                              <div className="space-y-4">
                                <FormField
                                  control={socialLoginForm.control}
                                  name="googleClientId"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel className="text-indigo-200">Google Client ID</FormLabel>
                                      <FormControl>
                                        <Input
                                          className="bg-indigo-950/50 border-indigo-700 text-indigo-100"
                                          placeholder="ใส่ Google Client ID"
                                          {...field}
                                        />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />

                                <FormField
                                  control={socialLoginForm.control}
                                  name="googleClientSecret"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel className="text-indigo-200">Google Client Secret</FormLabel>
                                      <FormControl>
                                        <Input
                                          type="password"
                                          className="bg-indigo-950/50 border-indigo-700 text-indigo-100"
                                          placeholder="ใส่ Google Client Secret"
                                          {...field}
                                        />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />

                                <FormField
                                  control={socialLoginForm.control}
                                  name="googleAuthCallback"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel className="text-indigo-200">Google Callback URL</FormLabel>
                                      <FormControl>
                                        <Input
                                          className="bg-indigo-950/50 border-indigo-700 text-indigo-100"
                                          placeholder="https://yourdomain.com/api/auth/google/callback"
                                          {...field}
                                        />
                                      </FormControl>
                                      <FormDescription className="text-indigo-400">
                                        URL สำหรับรับคำขอหลังจากการตรวจสอบสิทธิ์ Google สำเร็จ
                                      </FormDescription>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              </div>
                            )}
                          </AccordionContent>
                        </AccordionItem>
                      </Accordion>

                      <Button
                        type="submit"
                        className="bg-gradient-to-r from-amber-500 to-amber-600 text-indigo-950 hover:from-amber-600 hover:to-amber-700"
                        disabled={saveSocialLoginSettingsMutation.isPending}
                      >
                        {saveSocialLoginSettingsMutation.isPending ? (
                          <><Loader2 className="h-4 w-4 mr-2 animate-spin" /> กำลังบันทึก...</>
                        ) : (
                          <><Save className="h-4 w-4 mr-2" /> บันทึกการตั้งค่า</>
                        )}
                      </Button>
                    </form>
                  </Form>
                </CardContent>
              </Card>
            </TabsContent>

            {/* แท็บ OTP */}
            <TabsContent value="otp" className="space-y-4">
              <Card className="bg-indigo-950/50 border-amber-500/20">
                <CardHeader>
                  <CardTitle className="text-indigo-100">การตั้งค่า OTP และ SMS</CardTitle>
                  <CardDescription className="text-indigo-300">
                    กำหนดค่าการส่ง OTP ผ่าน SMS สำหรับการยืนยันตัวตน
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Form {...otpForm}>
                    <form onSubmit={otpForm.handleSubmit(onSubmitOtpSettings)} className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-4">
                          <div className="space-y-1">
                            <h3 className="text-lg font-medium text-indigo-100">การตั้งค่า OTP</h3>
                            <p className="text-sm text-indigo-400">กำหนดพารามิเตอร์สำหรับการส่งและตรวจสอบ OTP</p>
                          </div>

                          <FormField
                            control={otpForm.control}
                            name="otpLength"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-indigo-200">ความยาวรหัส OTP</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    min={4}
                                    max={8}
                                    className="bg-indigo-950/50 border-indigo-700 text-indigo-100"
                                    {...field}
                                    onChange={(e) => field.onChange(parseInt(e.target.value))}
                                  />
                                </FormControl>
                                <FormDescription className="text-indigo-400">
                                  จำนวนตัวเลขในรหัส OTP (4-8 หลัก)
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={otpForm.control}
                            name="otpExpiry"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-indigo-200">เวลาหมดอายุ OTP (นาที)</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    min={1}
                                    max={60}
                                    className="bg-indigo-950/50 border-indigo-700 text-indigo-100"
                                    {...field}
                                    onChange={(e) => field.onChange(parseInt(e.target.value))}
                                  />
                                </FormControl>
                                <FormDescription className="text-indigo-400">
                                  ระยะเวลาที่รหัส OTP มีผลใช้งาน (1-60 นาที)
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={otpForm.control}
                            name="otpResendDelay"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-indigo-200">เวลารอส่ง OTP ใหม่ (วินาที)</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    min={30}
                                    max={300}
                                    className="bg-indigo-950/50 border-indigo-700 text-indigo-100"
                                    {...field}
                                    onChange={(e) => field.onChange(parseInt(e.target.value))}
                                  />
                                </FormControl>
                                <FormDescription className="text-indigo-400">
                                  ระยะเวลาขั้นต่ำระหว่างการส่ง OTP แต่ละครั้ง (30-300 วินาที)
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <div className="space-y-4">
                          <div className="space-y-1">
                            <h3 className="text-lg font-medium text-indigo-100">การตั้งค่า SMS Provider</h3>
                            <p className="text-sm text-indigo-400">กำหนดค่าสำหรับการเชื่อมต่อกับผู้ให้บริการส่ง SMS</p>
                          </div>

                          <FormField
                            control={otpForm.control}
                            name="smsmktApiKey"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-indigo-200">DEESMSX API Key</FormLabel>
                                <FormControl>
                                  <Input
                                    className="bg-indigo-950/50 border-indigo-700 text-indigo-100"
                                    placeholder="ใส่ DEESMSX API Key"
                                    {...field}
                                    value={field.value || ""}
                                  />
                                </FormControl>
                                <FormDescription className="text-indigo-400">
                                  API Key สำหรับเชื่อมต่อกับบริการ DEESMSX
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={otpForm.control}
                            name="smsmktSecret"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-indigo-200">DEESMSX API Secret</FormLabel>
                                <FormControl>
                                  <Input
                                    className="bg-indigo-950/50 border-indigo-700 text-indigo-100"
                                    placeholder="ใส่ DEESMSX API Secret"
                                    type="password"
                                    {...field}
                                    value={field.value || ""}
                                  />
                                </FormControl>
                                <FormDescription className="text-indigo-400">
                                  API Secret สำหรับยืนยันตัวตนกับบริการ DEESMSX
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={otpForm.control}
                            name="smsSender"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-indigo-200">SMS Sender ID</FormLabel>
                                <FormControl>
                                  <Input
                                    className="bg-indigo-950/50 border-indigo-700 text-indigo-100"
                                    placeholder="ใส่ Sender ID (ไม่เกิน 11 ตัวอักษร)"
                                    maxLength={11}
                                    {...field}
                                    value={field.value || ""}
                                  />
                                </FormControl>
                                <FormDescription className="text-indigo-400">
                                  ชื่อผู้ส่งที่จะแสดงบนข้อความ SMS (เช่น SLIPKUY, COMPANY)
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />


                          <FormField
                            control={otpForm.control}
                            name="offlineSimulation"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between border rounded-lg border-indigo-800/40 p-4 bg-indigo-900/20">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-indigo-200">โหมดจำลอง (Offline)</FormLabel>
                                  <FormDescription className="text-indigo-400">
                                    จำลองการส่ง SMS โดยไม่เชื่อมต่อกับ API จริง ช่วยในการทดสอบโดยไม่สิ้นเปลืองเครดิต
                                  </FormDescription>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          <div className="pt-2">
                            <div className="text-indigo-200 text-sm font-medium mb-2">ทดสอบการส่ง OTP</div>
                            <div className="flex space-x-2">
                              <Input
                                className="bg-indigo-950/50 border-indigo-700 text-indigo-100"
                                placeholder="ใส่เบอร์โทรศัพท์ทดสอบ"
                                value={testPhone}
                                onChange={(e) => setTestPhone(e.target.value)}
                              />
                              <Button
                                type="button"
                                onClick={handleTestSendOtp}
                                disabled={isTestingSMS}
                                className="bg-amber-500 text-indigo-950 hover:bg-amber-600"
                              >
                                {isTestingSMS ? (
                                  <><Loader2 className="h-4 w-4 mr-2 animate-spin" /> กำลังส่ง...</>
                                ) : (
                                  <><SendIcon className="h-4 w-4 mr-2" /> ทดสอบส่ง</>
                                )}
                              </Button>
                            </div>
                            <p className="text-xs text-indigo-400 mt-1">ส่ง OTP ทดสอบไปยังเบอร์โทรศัพท์ที่ระบุ</p>
                          </div>
                        </div>
                      </div>

                      <Button
                        type="submit"
                        className="bg-gradient-to-r from-amber-500 to-amber-600 text-indigo-950 hover:from-amber-600 hover:to-amber-700"
                        disabled={saveOtpSettingsMutation.isPending}
                      >
                        {saveOtpSettingsMutation.isPending ? (
                          <><Loader2 className="h-4 w-4 mr-2 animate-spin" /> กำลังบันทึก...</>
                        ) : (
                          <><Save className="h-4 w-4 mr-2" /> บันทึกการตั้งค่า</>
                        )}
                      </Button>
                    </form>
                  </Form>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        )}
      </div>
    </Admin>
  );
};

export default AuthSettingsPage;