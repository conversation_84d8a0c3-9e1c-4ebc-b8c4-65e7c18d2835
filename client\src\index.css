@tailwind base;
@tailwind components;
@tailwind utilities;

/* PRM System Styles for Advanced Search */
.text-gradient-blue-to-emerald {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-emerald-500;
}

.text-gradient-purple-to-pink {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-pink-500;
}

.text-gradient-amber-to-orange {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-amber-500 to-orange-500;
}

.text-gradient-green-to-teal {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-green-500 to-teal-500;
}

.text-gradient-indigo-to-blue {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-indigo-500 to-blue-500;
}

.text-gradient-rose-to-pink {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-rose-500 to-pink-500;
}

.text-gradient-cyan-to-blue {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-cyan-500 to-blue-500;
}

.text-gradient-violet-to-purple {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-violet-500 to-purple-500;
}

.text-gradient-lime-to-green {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-lime-500 to-green-500;
}

.text-gradient-indigo-to-violet {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-indigo-500 to-violet-500;
}

/* Enhanced PRM Tables */
.table-enhanced {
  @apply w-full border-collapse rounded-lg overflow-hidden;
}

.table-enhanced th {
  @apply bg-gradient-to-r from-slate-100 to-slate-50 dark:from-slate-800 dark:to-slate-700 text-left px-4 py-3 text-sm font-semibold;
}

.table-enhanced td {
  @apply px-4 py-3 border-t border-slate-200 dark:border-slate-700;
}

.table-enhanced tr:hover td {
  @apply bg-slate-50 dark:bg-slate-800/50;
}

/* Chart Enhancements */
.recharts-wrapper {
  @apply !font-sans;
}

.recharts-tooltip-wrapper {
  @apply !shadow-lg !rounded-md !border !border-slate-200 dark:!border-slate-700;
}

.recharts-default-tooltip {
  @apply !bg-white/90 dark:!bg-slate-900/90 !text-slate-900 dark:!text-slate-100 !rounded-md !px-4 !py-2 !shadow-none !border-none;
}

.recharts-tooltip-label {
  @apply !font-medium !mb-1;
}

.recharts-tooltip-item-list {
  @apply !mt-1;
}

.recharts-tooltip-item {
  @apply !py-0.5;
}

.recharts-legend-item-text {
  @apply !text-slate-700 dark:!text-slate-300;
}

/* Card Enhancements */
.card-prm {
  @apply rounded-xl shadow-md transition-all border border-slate-200/50 dark:border-slate-700/50 hover:shadow-lg;
}

.card-prm-header {
  @apply p-5 border-b border-slate-200/75 dark:border-slate-700/75;
}

.card-prm-title {
  @apply text-xl font-semibold;
}

.card-prm-content {
  @apply p-5;
}

@keyframes twinkle-slow {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.8; }
}

@keyframes twinkle-medium {
  0%, 100% { opacity: 0.4; }
  50% { opacity: 0.9; }
}

@keyframes twinkle-fast {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

.animate-twinkle-slow {
  animation: twinkle-slow 4s ease-in-out infinite;
}

.animate-twinkle-medium {
  animation: twinkle-medium 3s ease-in-out infinite;
}

.animate-twinkle-fast {
  animation: twinkle-fast 2s ease-in-out infinite;
}

/* Define gold colors for luxury effect */
.from-gold-400 {
  --tw-gradient-from: #daa520;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.via-gold-200 {
  --tw-gradient-via: #ffd700;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-via), var(--tw-gradient-to);
}
.to-gold-400 {
  --tw-gradient-to: #daa520;
}

/* Custom animation for divine elements */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

.animate-pulse-slow {
  animation: pulse-slow 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@property --angle {
  syntax: '<angle>';
  initial-value: 0deg;
  inherits: false;
}

@layer base {
  :root {
    --primary: 258 90% 60%;
    --primary-foreground: 0 0% 100%;
    
    --secondary: 280 84% 54%;
    --secondary-foreground: 0 0% 100%;
    
    --accent: 335 78% 58%;
    --accent-foreground: 0 0% 100%;
    
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    
    --success: 142 76% 36%;
    --success-foreground: 0 0% 100%;
    
    --ring: 258 90% 60%;
    
    --radius: 0.75rem;
  }

  * {
    @apply border-border box-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Kanit', system-ui, sans-serif;
    overflow-x: hidden;
  }
}

/* Custom Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

@keyframes float {
  0% { transform: translateY(0px) translateX(0px); }
  25% { transform: translateY(-15px) translateX(10px); }
  50% { transform: translateY(0px) translateX(15px); }
  75% { transform: translateY(10px) translateX(5px); }
  100% { transform: translateY(0px) translateX(0px); }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pop {
  0% { transform: scale(0.8); opacity: 0; }
  50% { transform: scale(1.2); opacity: 1; }
  70% { transform: scale(0.95); opacity: 1; }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes confetti {
  0% { transform: translateY(0) rotate(0); opacity: 1; }
  100% { transform: translateY(400px) rotate(360deg); opacity: 0; }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

@keyframes spin3d {
  0% { transform: perspective(800px) rotateY(0); }
  100% { transform: perspective(800px) rotateY(360deg); }
}

@keyframes wobble {
  0%, 100% { transform: translateX(0); }
  15% { transform: translateX(-25px) rotate(-5deg); }
  30% { transform: translateX(20px) rotate(3deg); }
  45% { transform: translateX(-15px) rotate(-3deg); }
  60% { transform: translateX(10px) rotate(2deg); }
  75% { transform: translateX(-5px) rotate(-1deg); }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slideUp {
  animation: slideUp 0.5s ease-in-out;
}

.animate-pulse-custom {
  animation: pulse 2s infinite;
}

.animate-float {
  animation: float 15s ease-in-out infinite;
}

/* Divine celestial animations */
@keyframes pulsate {
  0% {
    opacity: 0.1;
    transform: rotate(25deg) scale(1.8);
  }
  50% {
    opacity: 0.5;
    transform: rotate(25deg) scale(2.2);
  }
  100% {
    opacity: 0.1;
    transform: rotate(25deg) scale(1.8);
  }
}

@keyframes pulse-fast {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.95);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 0.85;
    transform: scale(1.05);
  }
}

@keyframes bg-pan-slow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-bg-pan-slow {
  animation: bg-pan-slow 30s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse-slow 8s ease-in-out infinite;
}

.animate-pulse-fast {
  animation: pulse-fast 3s ease-in-out infinite;
}

@keyframes rotate-hue {
  0% {
    --angle: 0deg;
  }
  100% {
    --angle: 360deg;
  }
}

.animate-shimmer {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.8) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 3s infinite;
}

.animate-pop {
  animation: pop 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

.animate-wobble {
  animation: wobble 0.8s ease-in-out;
}

.animate-spin3d {
  animation: spin3d 1.2s ease-in-out;
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

@keyframes move-confetti {
  0% { transform: translateY(0) rotate(0); opacity: 1; }
  100% { transform: translateY(400px) rotate(360deg); opacity: 0; }
}

.confetti {
  position: absolute;
  width: 10px;
  height: 10px;
  opacity: 0;
  animation: move-confetti 3s ease-in-out 0s forwards;
  z-index: 1000;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--primary) / 0.3);
  border-radius: 100vh;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 0.5);
}

/* Custom Typography */
.gradient-text {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary;
}

/* Divine Themed Elements */
.divine-text {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 via-purple-500 to-amber-300;
  text-shadow: 0 0 10px rgba(138, 43, 226, 0.3);
}

.gold-text {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-yellow-400 via-amber-300 to-yellow-500;
  background-size: 200% auto;
  animation: goldShimmer 2s infinite;
  text-shadow: 0 0 5px rgba(255, 215, 0, 0.5), 0 0 10px rgba(255, 215, 0, 0.3);
}

.celestial-bg {
  background: radial-gradient(circle at center, #1e1c3c 0%, #0f0c29 100%);
  background-size: 200% 200%;
  animation: celestialMove 10s infinite alternate;
}

.divine-glow {
  box-shadow: 0 0 10px rgba(255, 215, 0, 0.5), 0 0 20px rgba(255, 215, 0, 0.3);
}

.subtle-shadow {
  box-shadow: 0 4px 20px -5px rgba(0, 0, 0, 0.1);
}

@keyframes goldShimmer {
  0% { background-position: 0%; }
  50% { background-position: 100%; }
  100% { background-position: 0%; }
}

@keyframes celestialMove {
  0% { background-position: 0% 0%; }
  100% { background-position: 100% 100%; }
}

/* Animations for active package */
@keyframes pulse-gentle {
  0% {
    box-shadow: 0 0 10px 2px rgba(245, 158, 11, 0.4);
  }
  50% {
    box-shadow: 0 0 20px 5px rgba(245, 158, 11, 0.6);
  }
  100% {
    box-shadow: 0 0 10px 2px rgba(245, 158, 11, 0.4);
  }
}

.animate-pulse-gentle {
  animation: pulse-gentle 2s infinite ease-in-out;
}

.glow-gold {
  box-shadow: 0 0 15px 3px rgba(245, 158, 11, 0.5);
  animation: pulse-gold 3s infinite ease-in-out;
}

@keyframes pulse-gold {
  0% {
    box-shadow: 0 0 8px 2px rgba(245, 158, 11, 0.5);
    border-color: rgba(245, 158, 11, 0.7);
  }
  50% {
    box-shadow: 0 0 20px 8px rgba(245, 158, 11, 0.7);
    border-color: rgba(255, 208, 61, 0.9);
  }
  100% {
    box-shadow: 0 0 8px 2px rgba(245, 158, 11, 0.5);
    border-color: rgba(245, 158, 11, 0.7);
  }
}

/* Animated lightning bar */
.lightning-bar {
  height: 2px;
  background: linear-gradient(to right, #ffcc00, #ffeb3b, #ffcc00);
  background-size: 200% 100%;
  animation: bgPanFast 3s infinite linear;
  position: relative;
  overflow: hidden;
}

.lightning-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(
    to right,
    transparent,
    rgba(255, 255, 255, 0.8),
    transparent
  );
  transform: skewX(-25deg);
  animation: shine 3s infinite;
}

@keyframes bgPanFast {
  from { background-position: 0% center; }
  to { background-position: 200% center; }
}

.animate-bg-pan-fast {
  animation: bgPanFast 3s infinite linear;
}

.glass-effect {
  @apply backdrop-blur-md bg-white/70 border border-white/20;
}

/* Custom Card Style */
.fancy-card {
  @apply rounded-xl bg-white border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-lg;
}

/* Custom Button Style */
.button-glow {
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 15px hsl(var(--primary) / 0.5);
}

.button-glow:after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to bottom right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  animation: shine 3s infinite;
}

@keyframes shine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  50% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* Celestial header effects */
.celestial-text {
  position: relative;
  @apply text-white;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5), 0 0 20px rgba(255, 255, 255, 0.3);
}

.celestial-text::after {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  opacity: 0;
  z-index: -1;
  animation: celestialPulse 3s infinite ease-in-out;
}

@keyframes celestialPulse {
  0% { opacity: 0; transform: scale(0.8); }
  50% { opacity: 0.5; transform: scale(1.1); }
  100% { opacity: 0; transform: scale(0.8); }
}

@keyframes pulseCustom {
  0% { opacity: 0.5; transform: scale(0.98); }
  50% { opacity: 0.8; transform: scale(1.05); }
  100% { opacity: 0.5; transform: scale(0.98); }
}

.animate-pulse-custom {
  animation: pulseCustom 2s ease-in-out infinite;
}

/* Card hover effects */
.fancy-card {
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
}

.fancy-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to bottom right,
    rgba(var(--primary), 0.1),
    rgba(var(--secondary), 0.1)
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.fancy-card:hover::before {
  opacity: 1;
}

/* Special Divine Effects for Bank Slip */
@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin-slow 12s linear infinite;
}

/* Ethereal Glow Effect */
.ethereal-glow {
  box-shadow: 
    0 0 10px rgba(139, 92, 246, 0.5),
    0 0 30px rgba(139, 92, 246, 0.3),
    0 0 60px rgba(139, 92, 246, 0.1);
}

/* Divine Shimmer Effect */
@keyframes divine-shimmer {
  0% {
    background-position: -300% 0;
    opacity: 0.2;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    background-position: 300% 0;
    opacity: 0.2;
  }
}

.divine-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    rgba(255, 215, 0, 0.4),
    rgba(255, 255, 255, 0.3),
    transparent
  );
  background-size: 300% 100%;
  animation: divine-shimmer 6s linear infinite;
}

/* Cosmic Star Twinkle */
@keyframes twinkle {
  0%, 100% {
    opacity: 0.1;
    transform: scale(0.8);
    box-shadow: 0 0 0px rgba(255, 215, 0, 0.1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.8);
  }
}

.cosmic-star {
  position: absolute;
  width: 2px;
  height: 2px;
  background-color: white;
  border-radius: 50%;
  z-index: 1;
}

.cosmic-star.twinkle {
  animation: twinkle calc(1s + (var(--star-speed, 0) * 4s)) ease-in-out infinite;
  animation-delay: calc(var(--star-delay, 0) * 5s);
}