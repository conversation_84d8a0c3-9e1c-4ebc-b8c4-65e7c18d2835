import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Checkbox } from "@/components/ui/checkbox";
import { apiRequest, queryClient } from "@/lib/queryClient";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  X,
  CheckCircle2,
  XCircle,
  AlertTriangle,
  Link2,
  Unlink,
  RefreshCw,
  Key,
  Send,
  History,
} from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";

// อาจจะต้องเพิ่มฟังก์ชัน formatRelativeTime
const formatRelativeTime = (date: Date): string => {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  
  // แปลงเป็นหน่วยเวลา
  const diffSecs = Math.floor(diffMs / 1000);
  const diffMins = Math.floor(diffSecs / 60);
  const diffHours = Math.floor(diffMins / 60);
  const diffDays = Math.floor(diffHours / 24);
  
  if (diffDays > 30) {
    return date.toLocaleDateString('th-TH');
  } else if (diffDays > 0) {
    return `${diffDays} วันที่แล้ว`;
  } else if (diffHours > 0) {
    return `${diffHours} ชั่วโมงที่แล้ว`;
  } else if (diffMins > 0) {
    return `${diffMins} นาทีที่แล้ว`;
  } else {
    return 'เมื่อครู่นี้';
  }
};

// สคีมาสำหรับข้อมูล webhook
const webhookSchema = z.object({
  name: z.string().min(3, {
    message: "ชื่อ webhook ต้องมีความยาวอย่างน้อย 3 ตัวอักษร",
  }),
  url: z.string().url({
    message: "URL ไม่ถูกต้อง ต้องเริ่มต้นด้วย http:// หรือ https://",
  }),
  secret: z.string().optional(),
  eventTypes: z.array(z.string()).min(1, {
    message: "ต้องเลือกประเภทของ event อย่างน้อย 1 รายการ",
  }),
  isActive: z.boolean().default(true),
  headers: z.string().optional(),
  conditions: z.object({
    minAmount: z.number().optional(),
    maxAmount: z.number().optional(),
    bankCodes: z.array(z.string()).optional(),
    timeRange: z.object({
      start: z.string().optional(),
      end: z.string().optional()
    }).optional()
  }).optional()
});

type WebhookFormData = z.infer<typeof webhookSchema>;

const eventTypes = [
  { value: "slip_verification", label: "ตรวจสอบสลิป" },
  { value: "quota_low", label: "โควต้าเหลือน้อย" },
  { value: "credit_low", label: "เครดิตเหลือน้อย" },
  { value: "api_key_expire", label: "API key ใกล้หมดอายุ" },
  { value: "package_expire", label: "แพ็คเกจใกล้หมดอายุ" },
  { value: "fraud_detected", label: "ตรวจพบการทุจริต" },
  { value: "system_update", label: "อัพเดทระบบ" },
];

export default function WebhookSettingsDialog() {
  const [open, setOpen] = useState(false);
  const [webhooks, setWebhooks] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedWebhook, setSelectedWebhook] = useState<any | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isTestMode, setIsTestMode] = useState(false);
  const [testResult, setTestResult] = useState<any | null>(null);
  const [webhookLogs, setWebhookLogs] = useState<any[]>([]);
  const [logsLoading, setLogsLoading] = useState(false);
  const { toast } = useToast();

  const form = useForm<WebhookFormData>({
    resolver: zodResolver(webhookSchema),
    defaultValues: {
      name: "",
      url: "",
      secret: "",
      eventTypes: [],
      isActive: true,
      headers: "",
      conditions: {
        minAmount: undefined,
        maxAmount: undefined,
        bankCodes: [],
        timeRange: {
          start: undefined,
          end: undefined
        }
      }
    },
  });

  // โหลดข้อมูล webhooks
  const fetchWebhooks = async () => {
    try {
      setLoading(true);
      const response = await apiRequest("GET", "/api/webhooks");
      const data = await response.json();
      
      if (data && data.data) {
        setWebhooks(data.data);
      }
    } catch (error) {
      console.error("Error fetching webhooks:", error);
      toast({
        title: "เกิดข้อผิดพลาด",
        description: "ไม่สามารถโหลดข้อมูล webhooks ได้",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // โหลดข้อมูล webhook logs
  const fetchWebhookLogs = async (webhookId: number) => {
    try {
      setLogsLoading(true);
      const response = await apiRequest("GET", `/api/webhooks/${webhookId}/logs`);
      const data = await response.json();
      
      if (data && data.data) {
        setWebhookLogs(data.data);
      }
    } catch (error) {
      console.error("Error fetching webhook logs:", error);
      toast({
        title: "เกิดข้อผิดพลาด",
        description: "ไม่สามารถโหลดข้อมูลประวัติ webhook ได้",
        variant: "destructive",
      });
    } finally {
      setLogsLoading(false);
    }
  };

  // เรียกใช้ fetchWebhooks เมื่อเปิด dialog
  useEffect(() => {
    if (open) {
      fetchWebhooks();
    }
  }, [open]);

  // กำหนดค่าเริ่มต้นของฟอร์มเมื่อแก้ไข webhook
  useEffect(() => {
    if (selectedWebhook && isEditMode) {
      // แปลง headers ที่เป็น JSON เป็น string
      let headersStr = "";
      if (selectedWebhook.headers) {
        try {
          headersStr = JSON.stringify(selectedWebhook.headers, null, 2);
        } catch (e) {
          console.error("Error parsing headers:", e);
        }
      }

      form.reset({
        name: selectedWebhook.name || "",
        url: selectedWebhook.url || "",
        secret: selectedWebhook.secret || "",
        eventTypes: selectedWebhook.eventTypes || [],
        isActive: selectedWebhook.isActive !== undefined ? selectedWebhook.isActive : true,
        headers: headersStr,
        conditions: selectedWebhook.conditions || {
          minAmount: undefined,
          maxAmount: undefined,
          bankCodes: [],
          timeRange: {
            start: undefined,
            end: undefined
          }
        }
      });
    } else if (!isEditMode) {
      // รีเซ็ตฟอร์มเมื่อสร้าง webhook ใหม่
      form.reset({
        name: "",
        url: "",
        secret: "",
        eventTypes: [],
        isActive: true,
        headers: "",
        conditions: {
          minAmount: undefined,
          maxAmount: undefined,
          bankCodes: [],
          timeRange: {
            start: undefined,
            end: undefined
          }
        }
      });
    }
  }, [selectedWebhook, isEditMode, form]);

  // ทดสอบส่ง webhook
  const testWebhook = async (webhookId: number) => {
    try {
      setIsTestMode(true);
      setTestResult(null);
      
      const response = await apiRequest("POST", `/api/webhooks/${webhookId}/test`);
      const data = await response.json();
      
      setTestResult(data);
      
      if (data.code === "200000") {
        toast({
          title: "ทดสอบสำเร็จ",
          description: "ส่ง webhook สำเร็จแล้ว",
        });
      } else {
        toast({
          title: "ทดสอบไม่สำเร็จ",
          description: data.message || "ไม่สามารถส่ง webhook ได้",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error testing webhook:", error);
      toast({
        title: "เกิดข้อผิดพลาด",
        description: "ไม่สามารถทดสอบส่ง webhook ได้",
        variant: "destructive",
      });
    } finally {
      setIsTestMode(false);
    }
  };

  // ลองส่ง webhook ที่ล้มเหลวใหม่
  const retryWebhook = async (logId: number) => {
    try {
      const response = await apiRequest("POST", `/api/webhooks/logs/${logId}/retry`);
      const data = await response.json();
      
      if (data.code === "200000") {
        toast({
          title: "ส่งใหม่สำเร็จ",
          description: "ลองส่ง webhook ใหม่สำเร็จแล้ว",
        });
        // โหลดประวัติใหม่
        if (selectedWebhook) {
          fetchWebhookLogs(selectedWebhook.id);
        }
      } else {
        toast({
          title: "ส่งใหม่ไม่สำเร็จ",
          description: data.message || "ไม่สามารถลองส่ง webhook ใหม่ได้",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error retrying webhook:", error);
      toast({
        title: "เกิดข้อผิดพลาด",
        description: "ไม่สามารถลองส่ง webhook ใหม่ได้",
        variant: "destructive",
      });
    }
  };

  // ลบ webhook
  const deleteWebhook = async (webhookId: number) => {
    try {
      const response = await apiRequest("DELETE", `/api/webhooks/${webhookId}`);
      const data = await response.json();
      
      if (data.code === "200000") {
        toast({
          title: "ลบสำเร็จ",
          description: "ลบ webhook สำเร็จแล้ว",
        });
        fetchWebhooks();
        setSelectedWebhook(null);
      } else {
        toast({
          title: "ลบไม่สำเร็จ",
          description: data.message || "ไม่สามารถลบ webhook ได้",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error deleting webhook:", error);
      toast({
        title: "เกิดข้อผิดพลาด",
        description: "ไม่สามารถลบ webhook ได้",
        variant: "destructive",
      });
    }
  };

  // ส่งฟอร์ม
  const onSubmit = async (data: WebhookFormData) => {
    try {
      let headersObj = {};
      if (data.headers) {
        try {
          headersObj = JSON.parse(data.headers);
        } catch (e) {
          toast({
            title: "รูปแบบ Headers ไม่ถูกต้อง",
            description: "กรุณาตรวจสอบรูปแบบ JSON ของ Headers",
            variant: "destructive",
          });
          return;
        }
      }

      const webhookData = {
        ...data,
        headers: Object.keys(headersObj).length > 0 ? headersObj : undefined
      };
      
      let response;
      
      if (isEditMode && selectedWebhook) {
        // แก้ไข webhook
        response = await apiRequest("PUT", `/api/webhooks/${selectedWebhook.id}`, webhookData);
      } else {
        // สร้าง webhook ใหม่
        response = await apiRequest("POST", "/api/webhooks", webhookData);
      }
      
      const result = await response.json();
      
      if (response.ok) {
        toast({
          title: isEditMode ? "แก้ไขสำเร็จ" : "สร้างสำเร็จ",
          description: isEditMode ? "แก้ไข webhook สำเร็จแล้ว" : "สร้าง webhook สำเร็จแล้ว",
        });
        fetchWebhooks();
        setIsEditMode(false);
        setSelectedWebhook(null);
      } else {
        toast({
          title: isEditMode ? "แก้ไขไม่สำเร็จ" : "สร้างไม่สำเร็จ",
          description: result.message || (isEditMode ? "ไม่สามารถแก้ไข webhook ได้" : "ไม่สามารถสร้าง webhook ได้"),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error submitting webhook:", error);
      toast({
        title: "เกิดข้อผิดพลาด",
        description: isEditMode ? "ไม่สามารถแก้ไข webhook ได้" : "ไม่สามารถสร้าง webhook ได้",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="default">
          <Link2 className="mr-2 h-4 w-4" />
          ตั้งค่า Webhooks
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-5xl">
        <DialogHeader>
          <DialogTitle>ตั้งค่า Webhooks</DialogTitle>
          <DialogDescription>
            กำหนดค่า webhooks เพื่อรับการแจ้งเตือนหรือข้อมูลจากระบบ SLIPKUY
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="list" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="list">รายการ Webhooks</TabsTrigger>
            <TabsTrigger value="create" onClick={() => {
              setIsEditMode(false);
              setSelectedWebhook(null);
            }}>
              {isEditMode ? "แก้ไข Webhook" : "สร้าง Webhook ใหม่"}
            </TabsTrigger>
            {selectedWebhook && (
              <TabsTrigger value="logs" onClick={() => {
                fetchWebhookLogs(selectedWebhook.id);
              }}>ประวัติการส่ง</TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="list" className="mt-4">
            {loading ? (
              <div className="flex justify-center items-center h-40">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
              </div>
            ) : webhooks.length === 0 ? (
              <div className="text-center py-10">
                <h3 className="text-lg font-medium">ยังไม่มี webhook</h3>
                <p className="text-sm text-muted-foreground mt-2">
                  คลิกที่แท็บ "สร้าง Webhook ใหม่" เพื่อเริ่มต้นสร้าง webhook
                </p>
              </div>
            ) : (
              <div className="grid gap-4">
                {webhooks.map((webhook) => (
                  <Card key={webhook.id} className="overflow-hidden">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="flex items-center">
                            {webhook.name}
                            <Badge variant={webhook.isActive ? "default" : "secondary"} className="ml-2">
                              {webhook.isActive ? "ใช้งาน" : "ปิดใช้งาน"}
                            </Badge>
                          </CardTitle>
                          <CardDescription className="mt-1 break-all">
                            {webhook.url}
                          </CardDescription>
                        </div>
                        <div className="flex gap-2">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => {
                              setSelectedWebhook(webhook);
                              setIsEditMode(true);
                              form.reset({
                                name: webhook.name,
                                url: webhook.url,
                                secret: webhook.secret || "",
                                eventTypes: webhook.eventTypes || [],
                                isActive: webhook.isActive,
                                headers: webhook.headers ? JSON.stringify(webhook.headers, null, 2) : ""
                              });
                              document.querySelector('[data-value="create"]')?.dispatchEvent(
                                new MouseEvent('click', { bubbles: true })
                              );
                            }}
                          >
                            แก้ไข
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => {
                              setSelectedWebhook(webhook);
                              document.querySelector('[data-value="logs"]')?.dispatchEvent(
                                new MouseEvent('click', { bubbles: true })
                              );
                            }}
                          >
                            ประวัติ
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => testWebhook(webhook.id)}
                            disabled={isTestMode}
                          >
                            {isTestMode ? 
                              <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" /> :
                              "ทดสอบ"
                            }
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="outline" size="sm" className="bg-red-50 hover:bg-red-100 text-red-600 hover:text-red-700">
                                ลบ
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>ยืนยันการลบ webhook</AlertDialogTitle>
                                <AlertDialogDescription>
                                  คุณต้องการลบ webhook "{webhook.name}" ใช่หรือไม่? การกระทำนี้ไม่สามารถยกเลิกได้
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>ยกเลิก</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => deleteWebhook(webhook.id)}
                                  className="bg-red-600 text-white hover:bg-red-700"
                                >
                                  ลบ
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                        <div className="flex flex-col">
                          <span className="text-muted-foreground">ประเภทเหตุการณ์:</span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {webhook.eventTypes && webhook.eventTypes.map((type: string) => (
                              <Badge key={type} variant="outline">
                                {eventTypes.find(et => et.value === type)?.label || type}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        <div className="flex flex-col gap-1">
                          <div className="flex items-center">
                            {webhook.lastTriggeredAt ? (
                              <span className="text-sm">
                                ส่งล่าสุด: {formatRelativeTime(new Date(webhook.lastTriggeredAt))}
                              </span>
                            ) : (
                              <span className="text-sm text-muted-foreground">ยังไม่เคยส่ง</span>
                            )}
                          </div>
                          <div className="flex items-center">
                            <span className="text-sm">
                              สร้างเมื่อ: {formatRelativeTime(new Date(webhook.createdAt))}
                            </span>
                          </div>
                        </div>
                      </div>
                      {webhook.conditions && (
                        <div className="mt-2">
                          <Separator className="my-2" />
                          <span className="text-sm text-muted-foreground">เงื่อนไข:</span>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-1 text-sm">
                            {webhook.conditions.minAmount && (
                              <div className="flex items-center gap-1">
                                <span>ยอดเงินขั้นต่ำ:</span>
                                <Badge variant="outline">{webhook.conditions.minAmount.toLocaleString()} บาท</Badge>
                              </div>
                            )}
                            {webhook.conditions.maxAmount && (
                              <div className="flex items-center gap-1">
                                <span>ยอดเงินสูงสุด:</span>
                                <Badge variant="outline">{webhook.conditions.maxAmount.toLocaleString()} บาท</Badge>
                              </div>
                            )}
                            {webhook.conditions.bankCodes && webhook.conditions.bankCodes.length > 0 && (
                              <div className="flex flex-col col-span-2">
                                <span>เฉพาะธนาคาร:</span>
                                <div className="flex flex-wrap gap-1 mt-1">
                                  {webhook.conditions.bankCodes.map((code: string) => (
                                    <Badge key={code} variant="outline">
                                      {code === "002" && "ธนาคารกรุงเทพ"}
                                      {code === "004" && "ธนาคารกสิกรไทย"}
                                      {code === "006" && "ธนาคารกรุงไทย"}
                                      {code === "011" && "ธนาคารทหารไทยธนชาต"}
                                      {code === "014" && "ธนาคารไทยพาณิชย์"}
                                      {code === "025" && "ธนาคารกรุงศรีอยุธยา"}
                                      {code === "069" && "ธนาคารเกียรตินาคินภัทร"}
                                      {code === "022" && "ธนาคารซีไอเอ็มบีไทย"}
                                      {code === "067" && "ธนาคารทิสโก้"}
                                      {code === "071" && "ธนาคารไทยเครดิต"}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="create" className="mt-4">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>ชื่อ Webhook</FormLabel>
                        <FormControl>
                          <Input placeholder="ระบุชื่อ webhook" {...field} />
                        </FormControl>
                        <FormDescription>
                          ชื่อสำหรับอ้างอิงภายในระบบเท่านั้น
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="url"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>URL Endpoint</FormLabel>
                        <FormControl>
                          <Input placeholder="https://yourdomain.com/webhook" {...field} />
                        </FormControl>
                        <FormDescription>
                          URL ที่ระบบจะส่งข้อมูลไปยัง (ต้องเป็น HTTPS สำหรับการใช้งานจริง)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="secret"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Secret Key (ไม่บังคับ)</FormLabel>
                        <FormControl>
                          <Input placeholder="ระบุ secret key" {...field} value={field.value || ""} />
                        </FormControl>
                        <FormDescription>
                          Secret key สำหรับตรวจสอบความถูกต้องของการส่งข้อมูล (จะส่งเป็น Header: X-Webhook-Secret)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="isActive"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>เปิดใช้งาน Webhook</FormLabel>
                          <FormDescription>
                            ระบบจะส่งข้อมูลไปยัง webhook นี้เมื่อเกิดเหตุการณ์ที่กำหนด
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />
                </div>
                <FormField
                  control={form.control}
                  name="eventTypes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>ประเภทเหตุการณ์</FormLabel>
                      <FormDescription>
                        เลือกเหตุการณ์ที่ต้องการได้รับการแจ้งเตือน
                      </FormDescription>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2">
                        {eventTypes.map((item) => (
                          <FormField
                            key={item.value}
                            control={form.control}
                            name="eventTypes"
                            render={({ field }) => {
                              return (
                                <FormItem
                                  key={item.value}
                                  className="flex flex-row items-start space-x-3 space-y-0"
                                >
                                  <FormControl>
                                    <Checkbox
                                      checked={field.value?.includes(item.value)}
                                      onCheckedChange={(checked) => {
                                        return checked
                                          ? field.onChange([...field.value, item.value])
                                          : field.onChange(
                                              field.value?.filter(
                                                (value) => value !== item.value
                                              )
                                            )
                                      }}
                                    />
                                  </FormControl>
                                  <FormLabel className="text-sm font-normal">
                                    {item.label}
                                  </FormLabel>
                                </FormItem>
                              )
                            }}
                          />
                        ))}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Accordion type="single" collapsible className="w-full my-4">
                  <AccordionItem value="conditions">
                    <AccordionTrigger className="text-lg font-medium">
                      เงื่อนไขการส่ง Webhook
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-4 p-2 bg-muted/30 rounded-md">
                        <div className="grid grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name="conditions.minAmount"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>ยอดเงินขั้นต่ำ (บาท)</FormLabel>
                                <FormControl>
                                  <Input 
                                    type="number" 
                                    placeholder="5000" 
                                    {...field} 
                                    value={field.value || ''} 
                                    onChange={e => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                                  />
                                </FormControl>
                                <FormDescription>
                                  ส่ง webhook เฉพาะเมื่อยอดเงินมากกว่าหรือเท่ากับค่านี้
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          
                          <FormField
                            control={form.control}
                            name="conditions.maxAmount"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>ยอดเงินสูงสุด (บาท)</FormLabel>
                                <FormControl>
                                  <Input 
                                    type="number"
                                    placeholder="10000" 
                                    {...field} 
                                    value={field.value || ''} 
                                    onChange={e => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                                  />
                                </FormControl>
                                <FormDescription>
                                  ส่ง webhook เฉพาะเมื่อยอดเงินน้อยกว่าหรือเท่ากับค่านี้
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        
                        <FormField
                          control={form.control}
                          name="conditions.bankCodes"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>รหัสธนาคาร</FormLabel>
                              <FormControl>
                                <Select 
                                  onValueChange={(value) => {
                                    // ถ้าเลือก "ทั้งหมด" ให้ใส่เป็น array ว่าง
                                    if (value === "all") {
                                      field.onChange([]);
                                    } else {
                                      const newValues = field.value ? [...field.value] : [];
                                      if (!newValues.includes(value)) {
                                        newValues.push(value);
                                        field.onChange(newValues);
                                      }
                                    }
                                  }}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder="เลือกธนาคาร" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="all">ทั้งหมด</SelectItem>
                                    <SelectItem value="002">ธนาคารกรุงเทพ</SelectItem>
                                    <SelectItem value="004">ธนาคารกสิกรไทย</SelectItem>
                                    <SelectItem value="006">ธนาคารกรุงไทย</SelectItem>
                                    <SelectItem value="011">ธนาคารทหารไทยธนชาต</SelectItem>
                                    <SelectItem value="014">ธนาคารไทยพาณิชย์</SelectItem>
                                    <SelectItem value="025">ธนาคารกรุงศรีอยุธยา</SelectItem>
                                    <SelectItem value="069">ธนาคารเกียรตินาคินภัทร</SelectItem>
                                    <SelectItem value="022">ธนาคารซีไอเอ็มบีไทย</SelectItem>
                                    <SelectItem value="067">ธนาคารทิสโก้</SelectItem>
                                    <SelectItem value="071">ธนาคารไทยเครดิต</SelectItem>
                                  </SelectContent>
                                </Select>
                              </FormControl>
                              <div className="flex flex-wrap gap-2 mt-2">
                                {field.value && field.value.length > 0 ? (
                                  field.value.map((bankCode) => (
                                    <Badge key={bankCode} variant="secondary" className="flex items-center gap-1">
                                      {bankCode === "002" && "ธนาคารกรุงเทพ"}
                                      {bankCode === "004" && "ธนาคารกสิกรไทย"}
                                      {bankCode === "006" && "ธนาคารกรุงไทย"}
                                      {bankCode === "011" && "ธนาคารทหารไทยธนชาต"}
                                      {bankCode === "014" && "ธนาคารไทยพาณิชย์"}
                                      {bankCode === "025" && "ธนาคารกรุงศรีอยุธยา"}
                                      {bankCode === "069" && "ธนาคารเกียรตินาคินภัทร"}
                                      {bankCode === "022" && "ธนาคารซีไอเอ็มบีไทย"}
                                      {bankCode === "067" && "ธนาคารทิสโก้"}
                                      {bankCode === "071" && "ธนาคารไทยเครดิต"}
                                      <X 
                                        className="h-3 w-3 cursor-pointer" 
                                        onClick={() => {
                                          field.onChange(field.value?.filter(code => code !== bankCode));
                                        }}
                                      />
                                    </Badge>
                                  ))
                                ) : (
                                  <span className="text-sm text-muted-foreground">ทุกธนาคาร</span>
                                )}
                              </div>
                              <FormDescription>
                                ส่ง webhook เฉพาะธุรกรรมจากธนาคารที่เลือก (ถ้าไม่เลือกหมายถึงทุกธนาคาร)
                              </FormDescription>
                            </FormItem>
                          )}
                        />
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
                
                <FormField
                  control={form.control}
                  name="headers"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Custom Headers (JSON)</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder={`{\n  "Custom-Header": "ค่า",\n  "Authorization": "Bearer token"\n}`}
                          className="font-mono"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        ระบุ HTTP headers เพิ่มเติมในรูปแบบ JSON (ไม่บังคับ)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="flex justify-end space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setIsEditMode(false);
                      setSelectedWebhook(null);
                      form.reset({
                        name: "",
                        url: "",
                        secret: "",
                        eventTypes: [],
                        isActive: true,
                        headers: "",
                        conditions: {
                          minAmount: undefined,
                          maxAmount: undefined,
                          bankCodes: [],
                          timeRange: {
                            start: undefined,
                            end: undefined
                          }
                        }
                      });
                      document.querySelector('[data-value="list"]')?.dispatchEvent(
                        new MouseEvent('click', { bubbles: true })
                      );
                    }}
                  >
                    ยกเลิก
                  </Button>
                  <Button type="submit">
                    {isEditMode ? "อัพเดท Webhook" : "สร้าง Webhook"}
                  </Button>
                </div>
              </form>
            </Form>
          </TabsContent>

          <TabsContent value="logs" className="mt-4">
            {!selectedWebhook ? (
              <div className="text-center py-10">
                <h3 className="text-lg font-medium">กรุณาเลือก webhook</h3>
                <p className="text-sm text-muted-foreground mt-2">
                  กรุณาเลือก webhook จากรายการเพื่อดูประวัติการส่ง
                </p>
              </div>
            ) : logsLoading ? (
              <div className="flex justify-center items-center h-40">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
              </div>
            ) : (
              <>
                <div className="mb-4">
                  <h3 className="text-lg font-medium">{selectedWebhook.name}</h3>
                  <p className="text-sm text-muted-foreground mt-1 break-all">{selectedWebhook.url}</p>
                </div>
                
                {webhookLogs.length === 0 ? (
                  <div className="text-center py-10 border rounded-md">
                    <h3 className="text-lg font-medium">ยังไม่มีประวัติการส่ง</h3>
                    <p className="text-sm text-muted-foreground mt-2">
                      webhook นี้ยังไม่เคยถูกเรียกใช้งาน
                    </p>
                    <Button 
                      variant="outline" 
                      className="mt-4"
                      onClick={() => testWebhook(selectedWebhook.id)}
                      disabled={isTestMode}
                    >
                      {isTestMode ? (
                        <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full mr-2" />
                      ) : (
                        <Send className="h-4 w-4 mr-2" />
                      )}
                      ทดสอบส่ง Webhook
                    </Button>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>เวลา</TableHead>
                          <TableHead>ประเภท</TableHead>
                          <TableHead>สถานะ</TableHead>
                          <TableHead>รหัสการตอบกลับ</TableHead>
                          <TableHead>การดำเนินการ</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {webhookLogs.map((log) => (
                          <TableRow key={log.id}>
                            <TableCell>
                              {new Date(log.createdAt).toLocaleString('th-TH')}
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline">
                                {eventTypes.find(et => et.value === log.eventType)?.label || log.eventType}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              {log.status === 'success' ? (
                                <Badge variant="success" className="flex items-center gap-1">
                                  <CheckCircle2 className="h-3 w-3" />
                                  <span>สำเร็จ</span>
                                </Badge>
                              ) : (
                                <Badge variant="destructive" className="flex items-center gap-1">
                                  <XCircle className="h-3 w-3" />
                                  <span>ล้มเหลว</span>
                                </Badge>
                              )}
                            </TableCell>
                            <TableCell>
                              {log.responseCode || 'N/A'}
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Button 
                                  variant="outline" 
                                  size="sm" 
                                  onClick={() => {
                                    // แสดงรายละเอียด
                                  }}
                                >
                                  รายละเอียด
                                </Button>
                                {log.status !== 'success' && (
                                  <Button 
                                    variant="outline" 
                                    size="sm"
                                    onClick={() => retryWebhook(log.id)}
                                  >
                                    <RefreshCw className="h-3 w-3 mr-1" />
                                    ลองใหม่
                                  </Button>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </>
            )}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}