import React, { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { DateRange } from "react-day-picker";
import { format, parseISO } from "date-fns";
import { th } from "date-fns/locale"
import { motion, AnimatePresence } from "framer-motion";
import { DashboardLayout } from "@/components/layouts/dashboard-layout";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { Loader2, Search, FileDown, BarChart3, FileCheck, AlertTriangle, CheckCircle, PieChart, LineChart, LayoutDashboard } from "lucide-react";
import {
  BarChart,
  Bar,
  PieChart as RPieChart,
  Pie,
  LineChart as RLineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell
} from "recharts";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { formatThaiCurrency, formatThaiDateTime } from "@/lib/utils";
import { apiRequest } from "@/lib/queryClient";

// ประเภทข้อมูลสำหรับรายการตรวจสอบสลิป
interface SlipVerification {
  id: number;
  userId: number;
  transactionRef: string | null;
  bankName: string | null;
  amount: number | null;
  sender: string | null;
  receiver: string | null;
  transactionDate: string | null;
  status: string;
  responseData: string | null;
  creditUsed: number | null;
  usedCredit: boolean;
  verificationSource: string;
  apiKeyId: number | null;
  createdAt: string;
}

// ประเภทข้อมูลสำหรับสถิติการตรวจสอบ
interface VerificationStats {
  total: number;
  totalAmount: number;
  averageAmount: number;
  successCount: number;
  errorCount: number;
  pendingCount: number;
  successRate: number;
  bankGroups: Record<string, number>;
  dateGroups: Record<string, number>;
  hourlyGroups: Record<string, number>; // ช่วงเวลาที่มีการใช้งานสูงสุด
  topBank: { name: string; count: number } | null; // ธนาคารที่มีการโอนมากที่สุด
  bankAmounts: Record<string, number>; // มูลค่ารวมแยกตามธนาคาร
  mostActiveHours: { hour: string; count: number }[]; // ช่วงเวลาที่มีการใช้งานมากที่สุด 3 อันดับแรก
}

// Divine Analytics and Search Page - เทพแห่งการวิเคราะห์
export default function AdvancedSearchPage() {
  const { toast } = useToast();
  const { user } = useAuth();
  
  // ข้อมูลการค้นหา
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [bankFilter, setBankFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const [minAmount, setMinAmount] = useState<string>("");
  const [maxAmount, setMaxAmount] = useState<string>("");
  const [sourceType, setSourceType] = useState<string>("all");
  
  // การแสดงผล
  const [viewMode, setViewMode] = useState<"search" | "insights">("search");
  
  // สร้างพารามิเตอร์การค้นหา
  const buildSearchParams = () => {
    const params: Record<string, string> = {};
    
    // เพิ่ม timestamp เพื่อให้ React Query สร้าง request ใหม่ทุกครั้งที่กดค้นหา
    params._t = Date.now().toString();
    
    if (searchTerm) params.search = searchTerm;
    if (bankFilter !== "all") params.bank = bankFilter;
    if (statusFilter !== "all") params.status = statusFilter;
    if (sourceType !== "all") params.source = sourceType;
    
    if (dateRange?.from) {
      params.startDate = format(dateRange.from, 'yyyy-MM-dd');
    }
    
    if (dateRange?.to) {
      params.endDate = format(dateRange.to, 'yyyy-MM-dd');
    }
    
    if (minAmount) params.minAmount = minAmount;
    if (maxAmount) params.maxAmount = maxAmount;
    
    // แสดงข้อมูลการค้นหาใน console เพื่อการดีบัก
    console.log('Search parameters:', params);
    
    return params;
  };
  
  // ดึงข้อมูลรายการตรวจสอบ
  // สร้าง memoized ของพารามิเตอร์การค้นหาเพื่อป้องกันการสร้างพารามิเตอร์ใหม่ทุกครั้งที่ render
  const searchParams = React.useMemo(() => {
    return buildSearchParams();
  }, [
    searchTerm, bankFilter, statusFilter, 
    dateRange, minAmount, maxAmount, 
    sourceType, viewMode
  ]);
  
  // สร้าง timestamp ที่แน่นอนสำหรับการค้นหา (เปลี่ยนเฉพาะเมื่อกดปุ่มค้นหา)
  const [searchTimestamp, setSearchTimestamp] = React.useState(Date.now());
  
  // ฟังก์ชันการค้นหาที่จะเรียก refetch
  const handleSearch = React.useCallback(() => {
    setSearchTimestamp(Date.now()); // สร้าง timestamp ใหม่เพื่อทำให้ queryKey เปลี่ยน
  }, []);

  // ดึงข้อมูลรายการตรวจสอบ
  const {
    data: verifications,
    isLoading,
    error,
    refetch
  } = useQuery<SlipVerification[]>({
    queryKey: ['/api/verifications', searchParams, searchTimestamp],
    enabled: viewMode === "search" && !!user,
    staleTime: 0,
    refetchOnWindowFocus: false,
    retry: 1,
    onSuccess: (data) => {
      console.log('ได้รับข้อมูลรายการตรวจสอบเรียบร้อย:', data?.length || 0, 'รายการ');
    },
    onError: (err) => {
      console.error('เกิดข้อผิดพลาดในการดึงข้อมูลรายการตรวจสอบ:', err);
    }
  });
  
  // ดึงข้อมูลสถิติ
  const {
    data: stats,
    isLoading: isStatsLoading,
    error: statsError
  } = useQuery<VerificationStats>({
    queryKey: ['/api/verifications/stats', searchParams, searchTimestamp],
    enabled: !!user,
    staleTime: 0,
    refetchOnWindowFocus: false,
    retry: 1,
    onSuccess: () => {
      console.log('ได้รับข้อมูลสถิติเรียบร้อย');
    },
    onError: (err) => {
      console.error('เกิดข้อผิดพลาดในการดึงข้อมูลสถิติ:', err);
    }
  });
  
  // ส่งออกไฟล์ CSV
  const exportToCSV = async () => {
    try {
      const params = new URLSearchParams(buildSearchParams());
      const response = await fetch(`/api/verifications/export?${params.toString()}`);
      const blob = await response.blob();
      
      // สร้าง URL สำหรับดาวน์โหลดไฟล์
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `slip-verifications-${format(new Date(), 'yyyy-MM-dd')}.csv`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      
      toast({
        title: "ส่งออกข้อมูลสำเร็จ",
        description: "ไฟล์ CSV ถูกดาวน์โหลดแล้ว",
      });
    } catch (error) {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: "ไม่สามารถส่งออกข้อมูล CSV ได้",
        variant: "destructive",
      });
    }
  };
  
  // แสดงสถานะของการตรวจสอบเป็นสี
  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'success':
        return <Badge variant="default" className="bg-green-500">สำเร็จ</Badge>;
      case 'error':
      case 'failed':
        return <Badge variant="destructive">ล้มเหลว</Badge>;
      case 'pending':
        return <Badge variant="outline" className="border-amber-500 text-amber-500">รอดำเนินการ</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };
  
  // แสดงไอคอนของแหล่งที่มาของการตรวจสอบ
  const getSourceIcon = (source: string) => {
    switch (source) {
      case 'api':
        return <FileCheck className="h-4 w-4 text-blue-500" />;
      case 'web':
        return <LayoutDashboard className="h-4 w-4 text-indigo-500" />;
      default:
        return null;
    }
  };
  
  // ลองดึงข้อมูลทันทีถ้ายังไม่ได้รับข้อมูลและมีการใช้ query parameters
  React.useEffect(() => {
    if (viewMode === "search" && !verifications && !isLoading) {
      console.log('ทำการรีเฟรชข้อมูลอัตโนมัติเนื่องจากยังไม่มีข้อมูล');
      refetch();
    }
  }, [viewMode, verifications, isLoading]);
  
  // การแสดงสถานะข้อมูลเพื่อการดีบัก
  console.log('สถานะข้อมูล:', { 
    viewMode, 
    isLoading, 
    hasVerifications: !!verifications, 
    verificationCount: verifications?.length || 0,
    hasStats: !!stats
  });
  
  // ไม่แสดงหน้า Loading อีกต่อไป เพื่อป้องกันการติดที่หน้า loading
  // แทนที่ด้วยการแสดง loading state ในหน้าหลัก
  
  // แสดงหน้า Error
  if (error && viewMode === "search") {
    return (
      <DashboardLayout>
        <Card className="border-destructive">
          <CardHeader className="bg-destructive/10 text-destructive">
            <CardTitle>เกิดข้อผิดพลาด</CardTitle>
          </CardHeader>
          <CardContent className="pt-6">
            <p className="text-red-700">{(error as Error).message}</p>
          </CardContent>
          <CardFooter>
            <Button onClick={() => window.location.reload()}>โหลดหน้าใหม่</Button>
          </CardFooter>
        </Card>
      </DashboardLayout>
    );
  }
  
  return (
    <DashboardLayout>
      <div className="space-y-6 relative z-10">
        {/* Hero Header แบบธีมเทพเจ้า - ปรับให้สอดคล้องกับ GodlyNavbar */}
        <div className="relative overflow-hidden rounded-xl bg-gradient-to-r from-indigo-900/80 via-purple-900/80 to-indigo-900/80 p-8 shadow-xl backdrop-blur-md border border-indigo-500/30">
          {/* ลายพื้นหลังจักรวาลและดวงดาว */}
          <div className="absolute inset-0 opacity-20">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000" className="h-full w-full">
              <circle cx="500" cy="500" r="400" fill="none" stroke="white" strokeWidth="2" strokeDasharray="3,10" />
              <circle cx="500" cy="500" r="300" fill="none" stroke="white" strokeWidth="1" strokeDasharray="2,8" />
              <circle cx="500" cy="500" r="200" fill="none" stroke="white" strokeWidth="1" strokeDasharray="1,6" />
              {Array.from({ length: 50 }).map((_, i) => (
                <circle 
                  key={i} 
                  cx={Math.random() * 1000} 
                  cy={Math.random() * 1000} 
                  r={Math.random() * 2 + 1} 
                  fill="white" 
                />
              ))}
            </svg>
          </div>
          
          <div className="relative z-10 flex justify-between items-center">
            <div>
              <div className="flex items-center gap-4 mb-2">
                <div className="bg-purple-300/20 p-3 rounded-lg">
                  <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 4L15.5 10L22 11.5L17 16.5L18.5 23L12 20L5.5 23L7 16.5L2 11.5L8.5 10L12 4Z" fill="white" stroke="white" strokeWidth="0.5" />
                  </svg>
                </div>
                <h1 className="text-4xl font-bold tracking-tight text-white">
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 to-purple-500">คลัง</span>
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-amber-400 to-yellow-300">แห่ง</span>
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 to-purple-500">ความ</span>
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-amber-400 to-yellow-300">รู้</span>
                </h1>
              </div>
              <p className="text-indigo-200 max-w-2xl text-lg">
                เปิดประตูสู่พลังแห่งการค้นหาและวิเคราะห์ข้อมูลเชิงลึก ค้นพบความจริงที่ซ่อนอยู่ในข้อมูลของคุณ
              </p>
            </div>
            <div className="bg-indigo-800/50 p-1 backdrop-blur-sm rounded-xl shadow-inner border border-indigo-700/50">
              <div className="flex items-center gap-1">
                <Button 
                  variant={viewMode === "search" ? "default" : "outline"}
                  size="lg"
                  onClick={() => setViewMode("search")}
                  className={viewMode === "search" 
                    ? "bg-white text-indigo-900 hover:bg-indigo-100 hover:text-indigo-900 border-none shadow-lg" 
                    : "bg-transparent text-white hover:bg-indigo-800/50 border-indigo-700/50"
                  }
                >
                  <Search className="h-5 w-5 mr-2" />
                  <span>ค้นหาขั้นเทพ</span>
                </Button>
                <Button 
                  variant={viewMode === "insights" ? "default" : "outline"}
                  size="lg"
                  onClick={() => setViewMode("insights")}
                  className={viewMode === "insights" 
                    ? "bg-white text-indigo-900 hover:bg-indigo-100 hover:text-indigo-900 border-none shadow-lg" 
                    : "bg-transparent text-white hover:bg-indigo-800/50 border-indigo-700/50"
                  }
                >
                  <PieChart className="h-5 w-5 mr-2" />
                  <span>เห็นเทวทัศน์</span>
                </Button>
              </div>
            </div>
          </div>
        </div>
        
        {/* แผงสถิติภาพรวมแบบเทพเจ้า */}
        {!isStatsLoading && stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-gradient-to-br from-indigo-900 to-purple-900 rounded-xl shadow-lg overflow-hidden border border-indigo-700 group hover:shadow-xl transition-all duration-500 relative">
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-400 via-indigo-400 to-purple-400"></div>
              <div className="absolute -right-16 -top-16 w-32 h-32 bg-indigo-600/20 rounded-full group-hover:scale-110 transition-all duration-700"></div>
              <div className="absolute -left-8 -bottom-8 w-16 h-16 bg-indigo-600/20 rounded-full group-hover:scale-110 transition-all duration-700"></div>
              <div className="p-6 relative z-10">
                <div className="flex items-center gap-3 mb-3">
                  <div className="p-2 bg-indigo-800/50 rounded-lg">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-indigo-200">
                      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                      <circle cx="9" cy="7" r="4"></circle>
                      <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                      <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold text-indigo-100">รายการทั้งหมด</h3>
                </div>
                <div className="text-4xl font-bold text-white mb-1">{stats.total.toLocaleString()}</div>
                <p className="text-indigo-200 text-sm flex items-center">
                  <span className="inline-block mr-1">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <circle cx="12" cy="12" r="10"></circle>
                      <polyline points="12 6 12 12 16 14"></polyline>
                    </svg>
                  </span>
                  มูลค่ารวม: {formatThaiCurrency(stats.totalAmount)}
                </p>
              </div>
            </div>
            
            <div className="bg-gradient-to-br from-emerald-900 to-green-800 rounded-xl shadow-lg overflow-hidden border border-emerald-700 group hover:shadow-xl transition-all duration-500 relative">
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-400 via-emerald-400 to-teal-400"></div>
              <div className="absolute -right-16 -top-16 w-32 h-32 bg-emerald-600/20 rounded-full group-hover:scale-110 transition-all duration-700"></div>
              <div className="absolute -left-8 -bottom-8 w-16 h-16 bg-emerald-600/20 rounded-full group-hover:scale-110 transition-all duration-700"></div>
              <div className="p-6 relative z-10">
                <div className="flex items-center gap-3 mb-3">
                  <div className="p-2 bg-emerald-800/50 rounded-lg">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-emerald-200">
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold text-emerald-100">รายการสำเร็จ</h3>
                </div>
                <div className="text-4xl font-bold text-white mb-1">{stats.successCount.toLocaleString()}</div>
                <p className="text-emerald-200 text-sm flex items-center">
                  <span className="inline-block mr-1">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
                      <polyline points="17 6 23 6 23 12"></polyline>
                    </svg>
                  </span>
                  อัตราความสำเร็จ: {stats.successRate.toFixed(1)}%
                </p>
              </div>
            </div>
            
            <div className="bg-gradient-to-br from-amber-900 to-yellow-800 rounded-xl shadow-lg overflow-hidden border border-amber-700 group hover:shadow-xl transition-all duration-500 relative">
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-yellow-400 via-amber-400 to-orange-400"></div>
              <div className="absolute -right-16 -top-16 w-32 h-32 bg-amber-600/20 rounded-full group-hover:scale-110 transition-all duration-700"></div>
              <div className="absolute -left-8 -bottom-8 w-16 h-16 bg-amber-600/20 rounded-full group-hover:scale-110 transition-all duration-700"></div>
              <div className="p-6 relative z-10">
                <div className="flex items-center gap-3 mb-3">
                  <div className="p-2 bg-amber-800/50 rounded-lg">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-amber-200">
                      <line x1="12" y1="1" x2="12" y2="23"></line>
                      <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold text-amber-100">ยอดเฉลี่ยต่อรายการ</h3>
                </div>
                <div className="text-4xl font-bold text-white mb-1">
                  {formatThaiCurrency(stats.averageAmount)}
                </div>
                <p className="text-amber-200 text-sm flex items-center">
                  <span className="inline-block mr-1">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                      <line x1="12" y1="8" x2="12" y2="16"></line>
                      <line x1="8" y1="12" x2="16" y2="12"></line>
                    </svg>
                  </span>
                  คำนวณจาก {stats.total} รายการ
                </p>
              </div>
            </div>
            
            <div className="bg-gradient-to-br from-rose-900 to-red-800 rounded-xl shadow-lg overflow-hidden border border-rose-700 group hover:shadow-xl transition-all duration-500 relative">
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-red-400 via-rose-400 to-pink-400"></div>
              <div className="absolute -right-16 -top-16 w-32 h-32 bg-rose-600/20 rounded-full group-hover:scale-110 transition-all duration-700"></div>
              <div className="absolute -left-8 -bottom-8 w-16 h-16 bg-rose-600/20 rounded-full group-hover:scale-110 transition-all duration-700"></div>
              <div className="p-6 relative z-10">
                <div className="flex items-center gap-3 mb-3">
                  <div className="p-2 bg-rose-800/50 rounded-lg">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-rose-200">
                      <circle cx="12" cy="12" r="10"></circle>
                      <line x1="12" y1="8" x2="12" y2="12"></line>
                      <line x1="12" y1="16" x2="12.01" y2="16"></line>
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold text-rose-100">รายการที่มีปัญหา</h3>
                </div>
                <div className="text-4xl font-bold text-white mb-1">{stats.errorCount.toLocaleString()}</div>
                <p className="text-rose-200 text-sm flex items-center">
                  <span className="inline-block mr-1">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>
                    </svg>
                  </span>
                  อัตราความล้มเหลว: {(100 - stats.successRate).toFixed(1)}%
                </p>
              </div>
            </div>
          </div>
        )}
        
        {/* แสดงส่วนการค้นหาขั้นสูง */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* ส่วนเงื่อนไขการค้นหา - แผงควบคุมซ้าย */}
          <Card className="bg-indigo-950/80 border-indigo-700/40 backdrop-blur-sm shadow-xl order-2 lg:order-1">
            <CardHeader className="border-b border-indigo-800/30 bg-gradient-to-r from-indigo-900/50 to-purple-900/60">
              <CardTitle className="text-white">
                <span className="flex items-center gap-2">
                  <Search className="h-5 w-5 text-indigo-300" />
                  <span>ตั้งค่าการค้นหา</span>
                </span>
              </CardTitle>
              <CardDescription className="text-indigo-300">
                ปรับแต่งการค้นหาขั้นสูงเพื่อกรองข้อมูลตามต้องการ
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4 pt-6">
              <div className="space-y-2">
                <Label htmlFor="search" className="text-indigo-300">คำค้นหา</Label>
                <Input
                  id="search"
                  placeholder="ค้นหาทุกอย่าง..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="bg-indigo-900/40 border-indigo-700/50 placeholder:text-indigo-400/50 text-white"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="bank" className="text-indigo-300">ธนาคาร</Label>
                <Select value={bankFilter} onValueChange={setBankFilter}>
                  <SelectTrigger
                    id="bank"
                    className="bg-indigo-900/40 border-indigo-700/50 text-white"
                  >
                    <SelectValue placeholder="เลือกธนาคาร" />
                  </SelectTrigger>
                  <SelectContent className="bg-indigo-900 border-indigo-700 text-white">
                    <SelectItem value="all">ทุกธนาคาร</SelectItem>
                    <SelectItem value="kbank">กสิกรไทย (KBANK)</SelectItem>
                    <SelectItem value="scb">ไทยพาณิชย์ (SCB)</SelectItem>
                    <SelectItem value="bbl">กรุงเทพ (BBL)</SelectItem>
                    <SelectItem value="ktb">กรุงไทย (KTB)</SelectItem>
                    <SelectItem value="bay">กรุงศรี (BAY)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="status" className="text-indigo-300">สถานะ</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger
                    id="status"
                    className="bg-indigo-900/40 border-indigo-700/50 text-white"
                  >
                    <SelectValue placeholder="เลือกสถานะ" />
                  </SelectTrigger>
                  <SelectContent className="bg-indigo-900 border-indigo-700 text-white">
                    <SelectItem value="all">ทุกสถานะ</SelectItem>
                    <SelectItem value="success">สำเร็จ</SelectItem>
                    <SelectItem value="failed">ล้มเหลว</SelectItem>
                    <SelectItem value="pending">รอดำเนินการ</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="source" className="text-indigo-300">แหล่งที่มา</Label>
                <Select value={sourceType} onValueChange={setSourceType}>
                  <SelectTrigger
                    id="source"
                    className="bg-indigo-900/40 border-indigo-700/50 text-white"
                  >
                    <SelectValue placeholder="เลือกแหล่งที่มา" />
                  </SelectTrigger>
                  <SelectContent className="bg-indigo-900 border-indigo-700 text-white">
                    <SelectItem value="all">ทั้งหมด</SelectItem>
                    <SelectItem value="web">เว็บไซต์</SelectItem>
                    <SelectItem value="api">API</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label className="text-indigo-300">ช่วงวันที่</Label>
                <div className="bg-indigo-900/40 border border-indigo-700/50 rounded-md">
                  <DateRangePicker 
                    date={dateRange}
                    onDateChange={setDateRange}
                    placeholder="เลือกช่วงวันที่"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="min-amount" className="text-indigo-300">จำนวนเงินต่ำสุด</Label>
                  <Input
                    id="min-amount"
                    placeholder="0"
                    value={minAmount}
                    onChange={(e) => setMinAmount(e.target.value)}
                    type="number"
                    className="bg-indigo-900/40 border-indigo-700/50 placeholder:text-indigo-400/50 text-white"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="max-amount" className="text-indigo-300">จำนวนเงินสูงสุด</Label>
                  <Input
                    id="max-amount"
                    placeholder="ไม่จำกัด"
                    value={maxAmount}
                    onChange={(e) => setMaxAmount(e.target.value)}
                    type="number"
                    className="bg-indigo-900/40 border-indigo-700/50 placeholder:text-indigo-400/50 text-white"
                  />
                </div>
              </div>
              
              <Button 
                onClick={handleSearch}
                className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 border-none"
              >
                <Search className="h-4 w-4 mr-2" />
                ค้นหา
              </Button>
              
              <Button 
                variant="outline" 
                onClick={exportToCSV}
                className="w-full mt-2 border-indigo-700/50 hover:bg-indigo-800/40 text-indigo-100"
              >
                <FileDown className="h-4 w-4 mr-2" />
                ส่งออกเป็น CSV
              </Button>
            </CardContent>
          </Card>
          
          {/* ส่วนแสดงผลการค้นหา หรือ ข้อมูลเชิงลึก */}
          <div className="lg:col-span-3 space-y-6 order-1 lg:order-2">
            {/* ส่วนแสดงผลการค้นหา */}
            {viewMode === "search" && (
              <>
                {/* แสดงผลลัพธ์การค้นหา */}
                <Card className="bg-indigo-950/80 border-indigo-700/40 backdrop-blur-sm shadow-xl overflow-hidden">
                  <CardHeader className="border-b border-indigo-800/30 bg-gradient-to-r from-indigo-900/50 to-purple-900/60">
                    <CardTitle className="text-white">
                      {verifications && (
                        <span className="flex items-center gap-2">
                          <Search className="h-5 w-5 text-indigo-300" />
                          <span>ผลการค้นหา ({verifications.length} รายการ)</span>
                        </span>
                      )}
                    </CardTitle>
                    <CardDescription className="text-indigo-300">
                      รายการตรวจสอบสลิปที่ตรงกับเงื่อนไขการค้นหา
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="p-0">
                    {isLoading ? (
                      <div className="flex flex-col items-center justify-center py-16">
                        <Loader2 className="h-8 w-8 animate-spin text-primary mb-3" />
                        <h3 className="text-xl font-medium text-indigo-100 mb-2">กำลังโหลดข้อมูล...</h3>
                        <p className="text-indigo-300 max-w-md text-center">
                          ระบบกำลังทำการค้นหาตามเงื่อนไขที่กำหนด
                        </p>
                      </div>
                    ) : verifications && verifications.length > 0 ? (
                      <ScrollArea className="h-[600px]">
                        <Table>
                          <TableHeader className="bg-indigo-900/40 sticky top-0 z-10">
                            <TableRow className="border-b-indigo-800 hover:bg-indigo-900/70">
                              <TableHead className="text-indigo-300">รหัสอ้างอิง</TableHead>
                              <TableHead className="text-indigo-300">ธนาคาร</TableHead>
                              <TableHead className="text-indigo-300 text-right">จำนวนเงิน</TableHead>
                              <TableHead className="text-indigo-300">ผู้ส่ง</TableHead>
                              <TableHead className="text-indigo-300">ผู้รับ</TableHead>
                              <TableHead className="text-indigo-300">วันที่ทำรายการ</TableHead>
                              <TableHead className="text-indigo-300">สถานะ</TableHead>
                              <TableHead className="text-indigo-300">ที่มา</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {verifications.map((verification) => (
                              <TableRow 
                                key={verification.id} 
                                className="border-b border-indigo-800/30 hover:bg-indigo-900/30"
                              >
                                <TableCell className="font-medium text-indigo-200">
                                  {verification.transactionRef || "-"}
                                </TableCell>
                                <TableCell className="text-indigo-200">
                                  {verification.bankName || "-"}
                                </TableCell>
                                <TableCell className="text-indigo-200 text-right">
                                  {verification.amount ? formatThaiCurrency(verification.amount) : "-"}
                                </TableCell>
                                <TableCell className="text-indigo-200">
                                  {verification.sender || "-"}
                                </TableCell>
                                <TableCell className="text-indigo-200">
                                  {verification.receiver || "-"}
                                </TableCell>
                                <TableCell className="text-indigo-200">
                                  {verification.transactionDate 
                                    ? formatThaiDateTime(verification.transactionDate) 
                                    : "-"}
                                </TableCell>
                                <TableCell>
                                  {getStatusBadge(verification.status)}
                                </TableCell>
                                <TableCell>
                                  <div className="flex">
                                    {getSourceIcon(verification.verificationSource)}
                                    <span className="ml-1 text-xs text-indigo-400">{verification.verificationSource}</span>
                                  </div>
                                  {verification.apiKeyId && (
                                    <div className="text-xs text-indigo-500">API Key #{verification.apiKeyId}</div>
                                  )}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </ScrollArea>
                    ) : (
                      <div className="flex flex-col items-center justify-center py-16">
                        <div className="bg-indigo-900/50 p-4 rounded-full mb-4">
                          <AlertTriangle className="h-8 w-8 text-amber-400" />
                        </div>
                        <h3 className="text-xl font-medium text-indigo-100 mb-2">ไม่พบข้อมูลตามเงื่อนไขการค้นหา</h3>
                        <p className="text-indigo-300 max-w-md text-center">
                          ลองเปลี่ยนเงื่อนไขการค้นหาเพื่อดูผลลัพธ์เพิ่มเติม
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </>
            )}
            
            {/* ส่วนแสดงผลข้อมูลเชิงลึก - รายงานวิเคราะห์ */}
            {viewMode === "insights" && (
              <>
                {isStatsLoading ? (
                  <div className="flex items-center justify-center min-h-[400px]">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    <span className="ml-2 text-lg">กำลังวิเคราะห์ข้อมูล...</span>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {statsError ? (
                      <Card className="border-destructive">
                        <CardHeader className="bg-destructive/10 text-destructive">
                          <CardTitle>เกิดข้อผิดพลาดในการวิเคราะห์</CardTitle>
                        </CardHeader>
                        <CardContent className="pt-6">
                          <p className="text-red-700">{(statsError as Error).message}</p>
                        </CardContent>
                        <CardFooter>
                          <Button onClick={() => window.location.reload()}>โหลดหน้าใหม่</Button>
                        </CardFooter>
                      </Card>
                    ) : stats ? (
                      <>
                        {/* แสดงแผนภูมิวงกลมแสดงสัดส่วนธนาคาร */}
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                          <Card className="bg-indigo-950/80 border-indigo-700/40 backdrop-blur-sm shadow-xl overflow-hidden">
                            <CardHeader className="border-b border-indigo-800/30 bg-gradient-to-r from-indigo-900/50 to-purple-900/60">
                              <CardTitle className="text-white">
                                <span className="flex items-center gap-2">
                                  <PieChart className="h-5 w-5 text-indigo-300" />
                                  <span>สัดส่วนธนาคาร</span>
                                </span>
                              </CardTitle>
                              <CardDescription className="text-indigo-300">
                                การกระจายตัวของธนาคารในการทำรายการ
                              </CardDescription>
                            </CardHeader>
                            <CardContent className="pt-6">
                              {stats.total > 0 ? (
                                <div className="h-[400px]">
                                  <ResponsiveContainer width="100%" height="100%">
                                    <RPieChart>
                                      <Pie
                                        data={Object.entries(stats.bankGroups || {}).map(([name, value]) => ({
                                          name,
                                          value
                                        }))}
                                        cx="50%"
                                        cy="50%"
                                        labelLine={false}
                                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                                        outerRadius={140}
                                        fill="#8884d8"
                                        dataKey="value"
                                      >
                                        {Object.entries(stats.bankGroups || {}).map(([_, value], index) => (
                                          <Cell key={`cell-${index}`} fill={[
                                            "#4f46e5", "#7c3aed", "#0891b2", "#059669", "#ca8a04", "#d97706", "#dc2626"
                                          ][index % 7]} />
                                        ))}
                                      </Pie>
                                      <Tooltip formatter={(value) => [`${value} รายการ`, 'จำนวน']} />
                                      <Legend />
                                    </RPieChart>
                                  </ResponsiveContainer>
                                </div>
                              ) : (
                                <div className="flex flex-col items-center justify-center py-16">
                                  <div className="bg-indigo-900/50 p-4 rounded-full mb-4">
                                    <AlertTriangle className="h-8 w-8 text-amber-400" />
                                  </div>
                                  <h3 className="text-xl font-medium text-indigo-100 mb-2">ไม่พบข้อมูลสำหรับการแสดงแผนภูมิ</h3>
                                </div>
                              )}
                            </CardContent>
                          </Card>
                          
                          <Card className="bg-indigo-950/80 border-indigo-700/40 backdrop-blur-sm shadow-xl overflow-hidden">
                            <CardHeader className="border-b border-indigo-800/30 bg-gradient-to-r from-indigo-900/50 to-purple-900/60">
                              <CardTitle className="text-white">
                                <span className="flex items-center gap-2">
                                  <PieChart className="h-5 w-5 text-indigo-300" />
                                  <span>สถานะการตรวจสอบ</span>
                                </span>
                              </CardTitle>
                              <CardDescription className="text-indigo-300">
                                สัดส่วนสถานะความสำเร็จของการตรวจสอบ
                              </CardDescription>
                            </CardHeader>
                            <CardContent className="pt-6">
                              {stats.total > 0 ? (
                                <div className="h-[400px]">
                                  <ResponsiveContainer width="100%" height="100%">
                                    <RPieChart>
                                      <Pie
                                        data={[
                                          { name: 'สำเร็จ', value: stats.successCount },
                                          { name: 'ล้มเหลว', value: stats.errorCount },
                                          { name: 'รอดำเนินการ', value: stats.pendingCount },
                                        ]}
                                        cx="50%"
                                        cy="50%"
                                        labelLine={false}
                                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                                        outerRadius={140}
                                        fill="#8884d8"
                                        dataKey="value"
                                      >
                                        <Cell fill="#10b981" />
                                        <Cell fill="#ef4444" />
                                        <Cell fill="#f59e0b" />
                                      </Pie>
                                      <Tooltip formatter={(value) => [`${value} รายการ`, 'จำนวน']} />
                                      <Legend />
                                    </RPieChart>
                                  </ResponsiveContainer>
                                </div>
                              ) : (
                                <div className="flex flex-col items-center justify-center py-16">
                                  <div className="bg-indigo-900/50 p-4 rounded-full mb-4">
                                    <AlertTriangle className="h-8 w-8 text-amber-400" />
                                  </div>
                                  <h3 className="text-xl font-medium text-indigo-100 mb-2">ไม่พบข้อมูลสำหรับการแสดงแผนภูมิ</h3>
                                </div>
                              )}
                            </CardContent>
                          </Card>
                        </div>
                        
                        {/* แสดงแผนภูมิแท่งรายชั่วโมง */}
                        <Card className="bg-indigo-950/80 border-indigo-700/40 backdrop-blur-sm shadow-xl overflow-hidden">
                          <CardHeader className="border-b border-indigo-800/30 bg-gradient-to-r from-indigo-900/50 to-purple-900/60">
                            <CardTitle className="text-white">
                              <span className="flex items-center gap-2">
                                <BarChart3 className="h-5 w-5 text-indigo-300" />
                                <span>การใช้งานตามช่วงเวลา</span>
                              </span>
                            </CardTitle>
                            <CardDescription className="text-indigo-300">
                              เวลาที่มีการตรวจสอบมากที่สุดในแต่ละช่วง
                            </CardDescription>
                          </CardHeader>
                          <CardContent className="pt-6">
                            {stats.total > 0 && Object.keys(stats.hourlyGroups || {}).length > 0 ? (
                              <div className="h-[400px]">
                                <ResponsiveContainer width="100%" height="100%">
                                  <BarChart
                                    data={Object.entries(stats.hourlyGroups || {})
                                      .map(([hour, count]) => ({
                                        hour: `${hour}:00`,
                                        count
                                      }))
                                      .sort((a, b) => {
                                        const hourA = parseInt(a.hour);
                                        const hourB = parseInt(b.hour);
                                        return hourA - hourB;
                                      })
                                    }
                                    margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
                                  >
                                    <CartesianGrid strokeDasharray="3 3" stroke="#2d3748" />
                                    <XAxis 
                                      dataKey="hour" 
                                      angle={-45} 
                                      textAnchor="end" 
                                      tick={{ fill: '#a5b4fc' }}
                                      height={60} 
                                    />
                                    <YAxis tick={{ fill: '#a5b4fc' }} />
                                    <Tooltip 
                                      formatter={(value) => [`${value} รายการ`, 'จำนวน']}
                                      contentStyle={{ 
                                        backgroundColor: '#1e1b4b', 
                                        borderColor: '#4338ca',
                                        color: '#e0e7ff' 
                                      }}
                                    />
                                    <Legend />
                                    <Bar 
                                      dataKey="count" 
                                      name="จำนวนรายการ" 
                                      fill="#6366f1"
                                      radius={[4, 4, 0, 0]}
                                    />
                                  </BarChart>
                                </ResponsiveContainer>
                              </div>
                            ) : (
                              <div className="flex flex-col items-center justify-center py-16">
                                <div className="bg-indigo-900/50 p-4 rounded-full mb-4">
                                  <AlertTriangle className="h-8 w-8 text-amber-400" />
                                </div>
                                <h3 className="text-xl font-medium text-indigo-100 mb-2">ไม่พบข้อมูลสำหรับการแสดงแผนภูมิ</h3>
                              </div>
                            )}
                          </CardContent>
                        </Card>
                        
                        {/* แสดงแผนภูมิแท่งแนวนอนสำหรับมูลค่ารวมของแต่ละธนาคาร */}
                        <Card className="bg-indigo-950/80 border-indigo-700/40 backdrop-blur-sm shadow-xl overflow-hidden">
                          <CardHeader className="border-b border-indigo-800/30 bg-gradient-to-r from-indigo-900/50 to-purple-900/60">
                            <CardTitle className="text-white">
                              <span className="flex items-center gap-2">
                                <BarChart3 className="h-5 w-5 text-indigo-300" />
                                <span>มูลค่ารวมของแต่ละธนาคาร</span>
                              </span>
                            </CardTitle>
                            <CardDescription className="text-indigo-300">
                              เปรียบเทียบมูลค่ารวมของการทำรายการแยกตามธนาคาร
                            </CardDescription>
                          </CardHeader>
                          <CardContent className="pt-6">
                            {stats.total > 0 && Object.keys(stats.bankAmounts || {}).length > 0 ? (
                              <div className="h-[400px]">
                                <ResponsiveContainer width="100%" height="100%">
                                  <BarChart
                                    data={Object.entries(stats.bankAmounts || {})
                                      .map(([bank, amount]) => ({
                                        bank,
                                        amount
                                      }))
                                      .sort((a, b) => b.amount - a.amount)
                                    }
                                    layout="vertical"
                                    margin={{ top: 20, right: 30, left: 60, bottom: 60 }}
                                  >
                                    <CartesianGrid strokeDasharray="3 3" stroke="#2d3748" />
                                    <XAxis 
                                      type="number" 
                                      tick={{ fill: '#a5b4fc' }}
                                      tickFormatter={(value) => {
                                        if (value >= 1000000) return `${(value / 1000000).toFixed(1)}M`;
                                        if (value >= 1000) return `${(value / 1000).toFixed(1)}K`;
                                        return value;
                                      }}
                                    />
                                    <YAxis 
                                      dataKey="bank" 
                                      type="category" 
                                      tick={{ fill: '#a5b4fc' }}
                                      width={60}
                                    />
                                    <Tooltip 
                                      formatter={(value) => [`${formatThaiCurrency(value as number)}`, 'มูลค่ารวม']}
                                      contentStyle={{ 
                                        backgroundColor: '#1e1b4b', 
                                        borderColor: '#4338ca',
                                        color: '#e0e7ff' 
                                      }}
                                    />
                                    <Legend />
                                    <Bar 
                                      dataKey="amount" 
                                      name="มูลค่ารวม (บาท)" 
                                      fill="#6366f1"
                                      radius={[0, 4, 4, 0]}
                                    />
                                  </BarChart>
                                </ResponsiveContainer>
                              </div>
                            ) : (
                              <div className="flex flex-col items-center justify-center py-16">
                                <div className="bg-indigo-900/50 p-4 rounded-full mb-4">
                                  <AlertTriangle className="h-8 w-8 text-amber-400" />
                                </div>
                                <h3 className="text-xl font-medium text-indigo-100 mb-2">ไม่พบข้อมูลสำหรับการแสดงแผนภูมิ</h3>
                              </div>
                            )}
                          </CardContent>
                        </Card>
                      </>
                    ) : (
                      <Card className="bg-indigo-950/80 border-indigo-700/40 backdrop-blur-sm shadow-xl overflow-hidden">
                        <CardContent className="py-12">
                          <div className="flex flex-col items-center justify-center">
                            <div className="bg-indigo-900/50 p-4 rounded-full mb-4">
                              <AlertTriangle className="h-8 w-8 text-amber-400" />
                            </div>
                            <h3 className="text-xl font-medium text-indigo-100 mb-2">ยังไม่มีข้อมูลสำหรับการแสดงรายงาน</h3>
                            <p className="text-indigo-300 max-w-md text-center">
                              รายงานวิเคราะห์เชิงลึก
                            </p>
                          </div>
                        </CardContent>
                      </Card>
                    )}
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}