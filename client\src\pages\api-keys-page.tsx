import { useState, useEffect } from "react";
import { <PERSON> } from "wouter";
import { DashboardLayout } from "@/components/layouts/dashboard-layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { useQuery, useMutation } from "@tanstack/react-query";
import { queryClient, apiRequest } from "@/lib/queryClient";
import { Loader2, RefreshCw, ClipboardCopy, Shield, Key, Clock, AlertTriangle, CheckCircle2, Copy } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";
import { format, parseISO, subDays } from "date-fns";
import { th } from "date-fns/locale";

// ประเภทข้อมูลของ API Key
interface ApiKey {
  id: number;
  name: string;
  status: "active" | "inactive" | "revoked";
  apiKey?: string;
  createdAt: string;
  lastUsed: string | null;
  requestCount: number;
  description: string | null;
  expiresAt: string | null;
  limitEnabled?: boolean; // เปิดใช้งานการจำกัดการใช้งานหรือไม่
  usageLimit?: number; // จำนวนการใช้งานสูงสุดที่อนุญาต
}

// ประเภทข้อมูลของ API Log
interface ApiLog {
  id: number;
  apiKeyId: number;
  requestType: string;
  responseStatus: string;
  ipAddress: string;
  processingTime: number;
  createdAt: string;
  errorMessage: string | null;
}

// ส่วนแสดงสถานะของ API Key
const ApiKeyStatusBadge = ({ status }: { status: string }) => {
  switch (status) {
    case "active":
      return <Badge className="bg-green-600">{status}</Badge>;
    case "inactive":
      return <Badge variant="outline" className="text-muted-foreground">{status}</Badge>;
    case "revoked":
      return <Badge variant="destructive">{status}</Badge>;
    default:
      return <Badge variant="secondary">{status}</Badge>;
  }
};

// ส่วนแสดงหน้าจัดการ API Keys
export default function ApiKeysPage() {
  const [selectedApiKey, setSelectedApiKey] = useState<ApiKey | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newApiKeyName, setNewApiKeyName] = useState("");
  const [newApiKeyDescription, setNewApiKeyDescription] = useState("");
  const [newIpWhitelist, setNewIpWhitelist] = useState("");
  const [newExpiration, setNewExpiration] = useState<number | null>(null);
  const [activeTab, setActiveTab] = useState("keys");
  const [copiedKey, setCopiedKey] = useState<string | null>(null);
  const [showFullKey, setShowFullKey] = useState(false);
  
  const { user } = useAuth();

  // ตัวแปรสำหรับข้อมูล API Keys และ Logs
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [apiLogs, setApiLogs] = useState<ApiLog[]>([]);
  const [isLoadingKeys, setIsLoadingKeys] = useState(true);
  const [isLoadingLogs, setIsLoadingLogs] = useState(false);
  
  // ดึงข้อมูล API Keys ผ่าน Query
  const { data: apiKeysData } = useQuery({
    queryKey: ["/api/user/api-keys"],
    enabled: !!user
  });

  // ดึงข้อมูลประวัติการใช้งาน API สำหรับ Key ที่เลือก
  const { data: apiLogsData } = useQuery({
    queryKey: [`/api/user/api-keys/${selectedApiKey?.id}/logs`],
    enabled: !!selectedApiKey
  });
  
  // อัพเดทข้อมูล API Keys เมื่อโหลดสำเร็จ
  useEffect(() => {
    if (apiKeysData) {
      setApiKeys(apiKeysData as ApiKey[]);
      setIsLoadingKeys(false);
    }
  }, [apiKeysData]);
  
  // อัพเดทข้อมูล API Logs เมื่อโหลดสำเร็จ
  useEffect(() => {
    if (apiLogsData) {
      setApiLogs(apiLogsData as ApiLog[]);
      setIsLoadingLogs(false);
    }
  }, [apiLogsData]);
  
  // ใช้ Server-Sent Events (SSE) เพื่ออัพเดทข้อมูลแบบเรียลไทม์
  useEffect(() => {
    if (!user) return;
    
    // เชื่อมต่อกับ SSE endpoint
    const sseUrl = selectedApiKey 
      ? `/api/sse/api-keys-data?selectedApiKeyId=${selectedApiKey.id}`
      : '/api/sse/api-keys-data';
      
    const eventSource = new EventSource(sseUrl);
    
    // รับข้อมูล API Keys ที่อัพเดท
    eventSource.addEventListener('apiKeys', (event) => {
      try {
        const data = JSON.parse(event.data);
        setApiKeys(data || []);
        setIsLoadingKeys(false);
      } catch (error) {
        console.error('Error parsing API keys SSE data:', error);
      }
    });
    
    // รับข้อมูล API Logs ที่อัพเดท (ถ้ามีการเลือก API Key)
    eventSource.addEventListener('apiLogs', (event) => {
      try {
        const data = JSON.parse(event.data);
        setApiLogs(data || []);
        setIsLoadingLogs(false);
      } catch (error) {
        console.error('Error parsing API logs SSE data:', error);
      }
    });
    
    // รับข้อมูลข้อผิดพลาด
    eventSource.addEventListener('error', (event) => {
      console.error('SSE Error:', event);
    });
    
    // ทำความสะอาดเมื่อ unmount
    return () => {
      eventSource.close();
    };
  }, [user, selectedApiKey]);

  // สร้าง API Key ใหม่
  const createApiKeyMutation = useMutation({
    mutationFn: async (data: { name: string; description?: string; ipWhitelist?: string[]; expiresAt?: string; status?: string }) => {
      // กำหนดค่า status เริ่มต้นเป็น active
      const payload = {
        ...data,
        status: data.status || 'active',
      };

      const res = await apiRequest("POST", "/api/user/api-keys", payload);
      const responseData = await res.json();
      console.log("API Key creation response:", responseData);
      
      if (!res.ok) {
        throw new Error(responseData.error || "ไม่สามารถสร้าง API Key ใหม่ได้");
      }
      
      return responseData;
    },
    onSuccess: (data) => {
      console.log("API Key created successfully:", data);
      queryClient.invalidateQueries({ queryKey: ["/api/user/api-keys"] });
      setShowCreateDialog(false);
      
      // ตรวจสอบว่าได้รับข้อมูล API key มาครบถ้วนหรือไม่
      if (data && data.apiKey) {
        setSelectedApiKey(data);
        toast({
          title: "สร้าง API Key สำเร็จ",
          description: "API Key ใหม่ถูกสร้างขึ้นเรียบร้อยแล้ว",
        });
      } else {
        console.error("API Key created but response is missing apiKey:", data);
        toast({
          title: "เกิดข้อผิดพลาด",
          description: "สร้าง API Key สำเร็จแต่ไม่ได้รับข้อมูล Key กรุณารีเฟรชหน้าและลองใหม่",
          variant: "destructive",
        });
      }
    },
    onError: (error: Error) => {
      console.error("API Key creation error:", error);
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message || "ไม่สามารถสร้าง API Key ใหม่ได้ กรุณาลองอีกครั้ง",
        variant: "destructive",
      });
    },
  });

  // อัพเดทสถานะ API Key
  const updateApiKeyMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: { status?: string; limitEnabled?: boolean; usageLimit?: number; } }) => {
      const res = await apiRequest("PATCH", `/api/user/api-keys/${id}`, data);
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/user/api-keys"] });
      toast({
        title: "อัพเดท API Key สำเร็จ",
        description: "API Key ถูกอัพเดทเรียบร้อยแล้ว",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // สร้าง API Key ใหม่
  const handleCreateApiKey = () => {
    if (!newApiKeyName) {
      toast({
        title: "กรุณากรอกชื่อ API Key",
        variant: "destructive",
      });
      return;
    }

    const data: any = {
      name: newApiKeyName,
    };

    if (newApiKeyDescription) {
      data.description = newApiKeyDescription;
    }

    if (newIpWhitelist) {
      data.ipWhitelist = newIpWhitelist.split(",").map(ip => ip.trim());
    }

    if (newExpiration) {
      const expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() + newExpiration);
      data.expiresAt = expirationDate.toISOString();
    }

    // ใช้ API endpoint ใหม่สำหรับการสร้าง API key
    fetch('/api/direct/api-keys', {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
      credentials: 'include'
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }
      return response.json();
    })
    .then(data => {
      console.log("API Key created successfully:", data);
      queryClient.invalidateQueries({ queryKey: ["/api/user/api-keys"] });
      setShowCreateDialog(false);
      
      if (data && data.apiKey) {
        setSelectedApiKey(data);
        toast({
          title: "สร้าง API Key สำเร็จ",
          description: "API Key ใหม่ถูกสร้างขึ้นเรียบร้อยแล้ว",
        });
      } else {
        console.error("API Key created but response is missing apiKey:", data);
        toast({
          title: "เกิดข้อผิดพลาด",
          description: "สร้าง API Key สำเร็จแต่ไม่ได้รับข้อมูล Key กรุณารีเฟรชหน้าและลองใหม่",
          variant: "destructive",
        });
      }
    })
    .catch(error => {
      console.error("Error creating API key:", error);
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message || "ไม่สามารถสร้าง API Key ใหม่ได้ กรุณาลองอีกครั้ง",
        variant: "destructive",
      });
    });
  };

  // คัดลอก API Key ไปยังคลิปบอร์ด
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(
      function () {
        setCopiedKey(text);
        setTimeout(() => setCopiedKey(null), 3000);
        toast({
          title: "คัดลอกสำเร็จ",
          description: "API Key ถูกคัดลอกไปยังคลิปบอร์ดแล้ว",
        });
      },
      function (err) {
        toast({
          title: "คัดลอกไม่สำเร็จ",
          description: "ไม่สามารถคัดลอก API Key ได้",
          variant: "destructive",
        });
      }
    );
  };

  // เปลี่ยนสถานะของ API Key
  const toggleApiKeyStatus = (apiKey: ApiKey) => {
    const newStatus = apiKey.status === "active" ? "inactive" : "active";
    updateApiKeyMutation.mutate({ 
      id: apiKey.id, 
      data: { status: newStatus } 
    });
  };
  
  // เปลี่ยนสถานะการจำกัดการใช้งาน
  const toggleLimitEnabled = (apiKey: ApiKey) => {
    updateApiKeyMutation.mutate({ 
      id: apiKey.id, 
      data: { 
        limitEnabled: !apiKey.limitEnabled 
      } 
    });
  };
  
  // อัพเดทจำนวนใช้งานสูงสุด
  const updateUsageLimit = (apiKey: ApiKey, limit: number) => {
    updateApiKeyMutation.mutate({ 
      id: apiKey.id, 
      data: { 
        usageLimit: limit 
      } 
    });
  };

  // สร้างกราฟการใช้งาน
  const getUsageChartData = () => {
    if (!apiLogs || !Array.isArray(apiLogs)) return [];

    const today = new Date();
    const data = [];

    // สร้างข้อมูลย้อนหลัง 30 วัน
    for (let i = 30; i >= 0; i--) {
      const date = subDays(today, i);
      const dateStr = format(date, "yyyy-MM-dd");
      
      // นับจำนวนการใช้งานในวันนั้น
      const logsOnDate = Array.isArray(apiLogs) ? apiLogs.filter((log: ApiLog) => {
        const logDate = new Date(log.createdAt);
        return format(logDate, "yyyy-MM-dd") === dateStr;
      }) : [];

      const successCount = logsOnDate.filter((log: ApiLog) => log.responseStatus === "success").length;
      const errorCount = logsOnDate.length - successCount;

      data.push({
        date: format(date, "d MMM", { locale: th }),
        รวม: logsOnDate.length,
        สำเร็จ: successCount,
        ล้มเหลว: errorCount,
      });
    }

    return data;
  };

  // รวมจำนวนการใช้งานตามประเภท
  const getUsageByType = () => {
    if (!apiLogs || !Array.isArray(apiLogs)) return {};
    
    const usageByType: Record<string, number> = {};
    
    apiLogs.forEach((log: ApiLog) => {
      const type = log.requestType;
      usageByType[type] = (usageByType[type] || 0) + 1;
    });
    
    return usageByType;
  };

  // คำนวณอัตราความสำเร็จ
  const getSuccessRate = () => {
    if (!apiLogs || !Array.isArray(apiLogs) || apiLogs.length === 0) return 0;
    
    const successCount = apiLogs.filter((log: ApiLog) => log.responseStatus === "success").length;
    return Math.round((successCount / apiLogs.length) * 100);
  };

  // สร้าง API Key ใหม่โดยสุ่ม
  const regenerateApiKey = async (id: number) => {
    try {
      const res = await apiRequest("POST", `/api/user/api-keys/${id}/regenerate`, {});
      const data = await res.json();
      
      console.log("Regenerated API key response:", data);
      
      if (!res.ok) {
        throw new Error(data.error || "ไม่สามารถสร้าง API Key ใหม่ได้");
      }
      
      queryClient.invalidateQueries({ queryKey: ["/api/user/api-keys"] });
      
      // ตรวจสอบว่าได้รับข้อมูล API key มาครบถ้วนหรือไม่
      if (data && data.apiKey) {
        setSelectedApiKey(data);
        toast({
          title: "สร้าง API Key ใหม่สำเร็จ",
          description: "API Key ใหม่ถูกสร้างขึ้นเรียบร้อยแล้ว",
        });
      } else {
        console.error("API Key regenerated but response is missing apiKey:", data);
        toast({
          title: "เกิดข้อผิดพลาด",
          description: "สร้าง API Key ใหม่สำเร็จแต่ไม่ได้รับข้อมูล Key กรุณารีเฟรชหน้าและลองใหม่",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      console.error("Error regenerating API key:", error);
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message || "ไม่สามารถสร้าง API Key ใหม่ได้",
        variant: "destructive",
      });
    }
  };

  // ซ่อนบางส่วนของ API Key
  const maskApiKey = (key: string) => {
    if (showFullKey) return key;
    return key.substring(0, 8) + "..." + key.substring(key.length - 8);
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="space-y-1">
            <h2 className="text-3xl font-bold tracking-tight">API Keys</h2>
            <p className="text-indigo-300">จัดการ API Keys และดูประวัติการใช้งาน API</p>
          </div>
          <Button onClick={() => setShowCreateDialog(true)} className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700">
            <Key className="mr-2 h-4 w-4" />
            สร้าง API Key
          </Button>
        </div>

        <Tabs defaultValue="keys" value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="gap-4">
            <TabsTrigger value="keys" className="data-[state=active]:bg-amber-500 data-[state=active]:text-white">
              <Key className="mr-2 h-4 w-4" />
              API Keys
            </TabsTrigger>
            {selectedApiKey && (
              <TabsTrigger value="usage" className="data-[state=active]:bg-amber-500 data-[state=active]:text-white">
                <Clock className="mr-2 h-4 w-4" />
                ประวัติการใช้งาน
              </TabsTrigger>
            )}
          </TabsList>
          
          <TabsContent value="keys" className="space-y-4">
            <Card className="bg-gradient-to-br from-indigo-950/80 to-purple-950/90 border-indigo-800/40">
              <CardHeader>
                <CardTitle className="text-white">API Keys ของคุณ</CardTitle>
                <CardDescription className="text-indigo-300">
                  สร้างและจัดการ API Keys สำหรับเชื่อมต่อกับ API ของเรา
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoadingKeys ? (
                  <div className="flex justify-center py-10">
                    <Loader2 className="h-10 w-10 animate-spin text-indigo-400" />
                  </div>
                ) : apiKeys && Array.isArray(apiKeys) && apiKeys.length > 0 ? (
                  <Table>
                    <TableHeader>
                      <TableRow className="border-indigo-800/40 hover:bg-indigo-900/20">
                        <TableHead className="text-indigo-300">ชื่อ</TableHead>
                        <TableHead className="text-indigo-300">API Key</TableHead>
                        <TableHead className="text-indigo-300">สถานะ</TableHead>
                        <TableHead className="text-indigo-300">การใช้งาน</TableHead>
                        <TableHead className="text-indigo-300">วันที่สร้าง</TableHead>
                        <TableHead className="text-indigo-300">การจัดการ</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {apiKeys.map((apiKey: ApiKey) => (
                        <TableRow key={apiKey.id} className="border-indigo-800/40 hover:bg-indigo-900/20">
                          <TableCell className="text-white font-medium">
                            <div className="flex flex-col">
                              <span>{apiKey.name}</span>
                              {apiKey.description && (
                                <span className="text-xs text-indigo-400">{apiKey.description}</span>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="font-mono text-amber-300">
                            <div className="flex items-center">
                              {apiKey.apiKey ? maskApiKey(apiKey.apiKey) : "ไม่แสดง API Key"}
                              {apiKey.apiKey && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => copyToClipboard(apiKey.apiKey!)}
                                  className="ml-2 h-7 w-7 p-0 text-indigo-300 hover:text-amber-300 hover:bg-transparent"
                                >
                                  {copiedKey === apiKey.apiKey ? (
                                    <CheckCircle2 className="h-4 w-4" />
                                  ) : (
                                    <Copy className="h-4 w-4" />
                                  )}
                                </Button>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <ApiKeyStatusBadge status={apiKey.status} />
                              {apiKey.status !== "revoked" && (
                                <Switch
                                  checked={apiKey.status === "active"}
                                  onCheckedChange={() => toggleApiKeyStatus(apiKey)}
                                  className="data-[state=checked]:bg-green-600"
                                />
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <span className="text-indigo-100">{apiKey.requestCount} ครั้ง</span>
                              {apiKey.lastUsed && (
                                <div className="text-xs text-indigo-400">
                                  ล่าสุด: {new Date(apiKey.lastUsed).toLocaleString("th-TH")}
                                </div>
                              )}
                              
                              {/* แสดงข้อมูลการจำกัดการใช้งาน (ถ้ามี) */}
                              <div className="mt-2 pt-2 border-t border-indigo-800/40">
                                {apiKey.limitEnabled && (
                                  <div className="text-xs text-indigo-300">
                                    <span className="text-amber-400">จำกัดการใช้งาน:</span> {apiKey.usageLimit || 0} ครั้ง
                                  </div>
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="text-indigo-200">
                            {new Date(apiKey.createdAt).toLocaleDateString("th-TH")}
                          </TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 px-2 text-indigo-300 hover:text-amber-300 hover:bg-transparent"
                                onClick={() => {
                                  setSelectedApiKey(apiKey);
                                  setActiveTab("usage");
                                }}
                              >
                                ประวัติ
                              </Button>
                              <Link href={`/api-key-settings/${apiKey.id}`}>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 px-2 text-indigo-300 hover:text-amber-300 hover:bg-transparent"
                                >
                                  ตั้งค่า
                                </Button>
                              </Link>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 px-2 text-indigo-300 hover:text-amber-300 hover:bg-transparent"
                                onClick={() => regenerateApiKey(apiKey.id)}
                              >
                                <RefreshCw className="h-4 w-4 mr-1" />
                                สร้างใหม่
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <div className="text-center py-10">
                    <Shield className="mx-auto h-12 w-12 text-indigo-400 mb-4" />
                    <h3 className="text-lg font-medium text-white">ไม่มี API Keys</h3>
                    <p className="text-indigo-300 mt-2">
                      คุณยังไม่มี API Keys กดปุ่ม "สร้าง API Key" เพื่อเริ่มใช้งาน API
                    </p>
                  </div>
                )}
              </CardContent>
              <CardFooter className="border-t border-indigo-800/40 pt-6 text-indigo-300">
                <div className="flex items-center">
                  <AlertTriangle className="h-4 w-4 mr-2 text-amber-400" />
                  <span className="text-sm">
                    เก็บรักษา API Key ให้ปลอดภัย อย่าเปิดเผยกับบุคคลอื่น
                  </span>
                </div>
              </CardFooter>
            </Card>
          </TabsContent>

          {selectedApiKey && (
            <TabsContent value="usage" className="space-y-4">
              <Card className="bg-gradient-to-br from-indigo-950/80 to-purple-950/90 border-indigo-800/40">
                <CardHeader>
                  <CardTitle className="text-white">ประวัติการใช้งาน: {selectedApiKey.name}</CardTitle>
                  <CardDescription className="text-indigo-300">
                    ข้อมูลการใช้งาน API Key และสถิติต่างๆ
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* สรุปการใช้งาน */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card className="bg-indigo-900/40 border-indigo-800/40">
                      <CardHeader className="py-4">
                        <CardTitle className="text-lg text-indigo-100">การใช้งานทั้งหมด</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-4xl font-bold text-amber-300">{selectedApiKey.requestCount}</div>
                        <p className="text-indigo-300 text-sm mt-1">ครั้ง</p>
                      </CardContent>
                    </Card>
                    
                    <Card className="bg-indigo-900/40 border-indigo-800/40">
                      <CardHeader className="py-4">
                        <CardTitle className="text-lg text-indigo-100">อัตราความสำเร็จ</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-4xl font-bold text-amber-300">{getSuccessRate()}%</div>
                        <p className="text-indigo-300 text-sm mt-1">ของการร้องขอทั้งหมด</p>
                      </CardContent>
                    </Card>
                    
                    <Card className="bg-indigo-900/40 border-indigo-800/40">
                      <CardHeader className="py-4">
                        <CardTitle className="text-lg text-indigo-100">การใช้งานล่าสุด</CardTitle>
                      </CardHeader>
                      <CardContent>
                        {selectedApiKey.lastUsed ? (
                          <>
                            <div className="text-xl font-bold text-amber-300">
                              {new Date(selectedApiKey.lastUsed).toLocaleDateString("th-TH")}
                            </div>
                            <p className="text-indigo-300 text-sm mt-1">
                              {new Date(selectedApiKey.lastUsed).toLocaleTimeString("th-TH")}
                            </p>
                          </>
                        ) : (
                          <div className="text-xl font-bold text-indigo-300">ยังไม่เคยใช้</div>
                        )}
                      </CardContent>
                    </Card>
                  </div>

                  {/* กราฟการใช้งาน */}
                  <Card className="bg-indigo-900/40 border-indigo-800/40">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg text-indigo-100">กราฟการใช้งานย้อนหลัง 30 วัน</CardTitle>
                    </CardHeader>
                    <CardContent>
                      {isLoadingLogs ? (
                        <div className="flex justify-center py-10">
                          <Loader2 className="h-10 w-10 animate-spin text-indigo-400" />
                        </div>
                      ) : apiLogs && Array.isArray(apiLogs) && apiLogs.length > 0 ? (
                        <div className="h-72">
                          <ResponsiveContainer width="100%" height="100%">
                            <AreaChart
                              data={getUsageChartData()}
                              margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                            >
                              <defs>
                                <linearGradient id="colorTotal" x1="0" y1="0" x2="0" y2="1">
                                  <stop offset="5%" stopColor="#312e81" stopOpacity={0.8} />
                                  <stop offset="95%" stopColor="#312e81" stopOpacity={0.1} />
                                </linearGradient>
                                <linearGradient id="colorSuccess" x1="0" y1="0" x2="0" y2="1">
                                  <stop offset="5%" stopColor="#16a34a" stopOpacity={0.8} />
                                  <stop offset="95%" stopColor="#16a34a" stopOpacity={0.1} />
                                </linearGradient>
                                <linearGradient id="colorFailed" x1="0" y1="0" x2="0" y2="1">
                                  <stop offset="5%" stopColor="#dc2626" stopOpacity={0.8} />
                                  <stop offset="95%" stopColor="#dc2626" stopOpacity={0.1} />
                                </linearGradient>
                              </defs>
                              <XAxis 
                                dataKey="date" 
                                tick={{ fill: '#94a3b8' }} 
                                tickLine={{ stroke: '#94a3b8' }}
                              />
                              <YAxis 
                                tick={{ fill: '#94a3b8' }} 
                                tickLine={{ stroke: '#94a3b8' }}
                              />
                              <CartesianGrid strokeDasharray="3 3" stroke="#1e293b" />
                              <Tooltip 
                                contentStyle={{ 
                                  backgroundColor: '#1e1b4b', 
                                  borderColor: '#4338ca',
                                  borderRadius: '8px',
                                  color: '#e2e8f0' 
                                }}
                              />
                              <Area 
                                type="monotone" 
                                dataKey="รวม" 
                                stroke="#312e81" 
                                fillOpacity={1} 
                                fill="url(#colorTotal)" 
                              />
                              <Area 
                                type="monotone" 
                                dataKey="สำเร็จ" 
                                stroke="#16a34a" 
                                fillOpacity={1} 
                                fill="url(#colorSuccess)" 
                              />
                              <Area 
                                type="monotone" 
                                dataKey="ล้มเหลว" 
                                stroke="#dc2626" 
                                fillOpacity={1} 
                                fill="url(#colorFailed)" 
                              />
                            </AreaChart>
                          </ResponsiveContainer>
                        </div>
                      ) : (
                        <div className="text-center py-10">
                          <Clock className="mx-auto h-12 w-12 text-indigo-400 mb-4" />
                          <h3 className="text-lg font-medium text-white">ไม่มีประวัติการใช้งาน</h3>
                          <p className="text-indigo-300 mt-2">
                            ยังไม่มีข้อมูลการใช้งาน API สำหรับ Key นี้
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  {/* ประวัติการใช้งานล่าสุด */}
                  <Card className="bg-indigo-900/40 border-indigo-800/40">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg text-indigo-100">ประวัติการใช้งานล่าสุด</CardTitle>
                    </CardHeader>
                    <CardContent>
                      {isLoadingLogs ? (
                        <div className="flex justify-center py-10">
                          <Loader2 className="h-10 w-10 animate-spin text-indigo-400" />
                        </div>
                      ) : apiLogs && Array.isArray(apiLogs) && apiLogs.length > 0 ? (
                        <Table>
                          <TableHeader>
                            <TableRow className="border-indigo-800/40 hover:bg-indigo-900/20">
                              <TableHead className="text-indigo-300">วันเวลา</TableHead>
                              <TableHead className="text-indigo-300">ประเภทการร้องขอ</TableHead>
                              <TableHead className="text-indigo-300">สถานะ</TableHead>
                              <TableHead className="text-indigo-300">IP Address</TableHead>
                              <TableHead className="text-indigo-300">เวลาประมวลผล</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {Array.isArray(apiLogs) && apiLogs.slice(0, 10).map((log: ApiLog) => (
                              <TableRow key={log.id} className="border-indigo-800/40 hover:bg-indigo-900/20">
                                <TableCell className="text-indigo-200">
                                  {new Date(log.createdAt).toLocaleString("th-TH")}
                                </TableCell>
                                <TableCell className="text-white">{log.requestType}</TableCell>
                                <TableCell>
                                  {log.responseStatus === "success" ? (
                                    <Badge className="bg-green-600">สำเร็จ</Badge>
                                  ) : (
                                    <Badge variant="destructive">ล้มเหลว</Badge>
                                  )}
                                </TableCell>
                                <TableCell className="text-indigo-200">{log.ipAddress}</TableCell>
                                <TableCell className="text-indigo-200">{log.processingTime} ms</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      ) : (
                        <div className="text-center py-10">
                          <Clock className="mx-auto h-12 w-12 text-indigo-400 mb-4" />
                          <h3 className="text-lg font-medium text-white">ไม่มีประวัติการใช้งาน</h3>
                          <p className="text-indigo-300 mt-2">
                            ยังไม่มีข้อมูลการใช้งาน API สำหรับ Key นี้
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </CardContent>
              </Card>
            </TabsContent>
          )}
        </Tabs>

        {/* Dialog สร้าง API Key ใหม่ */}
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogContent className="bg-gradient-to-br from-indigo-950 to-purple-950 border-indigo-800/40 text-white">
            <DialogHeader>
              <DialogTitle className="text-white">สร้าง API Key ใหม่</DialogTitle>
              <DialogDescription className="text-indigo-300">
                กรอกข้อมูลเพื่อสร้าง API Key สำหรับเชื่อมต่อกับ API ของเรา
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 pt-4">
              <div className="space-y-2">
                <Label htmlFor="name" className="text-indigo-100">ชื่อ API Key</Label>
                <Input
                  id="name"
                  placeholder="เช่น Production API, Test API"
                  value={newApiKeyName}
                  onChange={(e) => setNewApiKeyName(e.target.value)}
                  className="bg-indigo-900/40 border-indigo-700/60 text-white placeholder:text-indigo-400"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description" className="text-indigo-100">คำอธิบาย (ไม่บังคับ)</Label>
                <Input
                  id="description"
                  placeholder="รายละเอียดเพิ่มเติมเกี่ยวกับ API Key นี้"
                  value={newApiKeyDescription}
                  onChange={(e) => setNewApiKeyDescription(e.target.value)}
                  className="bg-indigo-900/40 border-indigo-700/60 text-white placeholder:text-indigo-400"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="ipWhitelist" className="text-indigo-100">IP Whitelist (ไม่บังคับ)</Label>
                <Input
                  id="ipWhitelist"
                  placeholder="เช่น ***********, ***********"
                  value={newIpWhitelist}
                  onChange={(e) => setNewIpWhitelist(e.target.value)}
                  className="bg-indigo-900/40 border-indigo-700/60 text-white placeholder:text-indigo-400"
                />
                <p className="text-xs text-indigo-400">กรอก IP Address หลายตัวโดยคั่นด้วยเครื่องหมายจุลภาค (,)</p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="expiration" className="text-indigo-100">วันหมดอายุ (ไม่บังคับ)</Label>
                <select
                  id="expiration"
                  value={newExpiration || ""}
                  onChange={(e) => setNewExpiration(e.target.value ? parseInt(e.target.value) : null)}
                  className="w-full bg-indigo-900/40 border border-indigo-700/60 text-white rounded-md h-10 px-3"
                >
                  <option value="">ไม่มีวันหมดอายุ</option>
                  <option value="30">30 วัน</option>
                  <option value="90">90 วัน (3 เดือน)</option>
                  <option value="180">180 วัน (6 เดือน)</option>
                  <option value="365">365 วัน (1 ปี)</option>
                </select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowCreateDialog(false)} className="border-indigo-700/60 text-indigo-300 hover:text-white">
                ยกเลิก
              </Button>
              <Button 
                onClick={handleCreateApiKey} 
                className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700"
                disabled={createApiKeyMutation.isPending}
              >
                {createApiKeyMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                สร้าง API Key
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Dialog แสดง API Key ที่สร้างใหม่ */}
        {selectedApiKey && selectedApiKey.apiKey && (
          <Dialog open={!!selectedApiKey.apiKey && activeTab !== "usage"} onOpenChange={() => {
            // ถ้าปิด Dialog แต่ต้องการดูประวัติ ไม่ต้อง reset selectedApiKey
            if (activeTab !== "usage") {
              setSelectedApiKey(null);
            }
          }}>
            <DialogContent className="bg-gradient-to-br from-indigo-950 to-purple-950 border-indigo-800/40 text-white">
              <DialogHeader>
                <DialogTitle className="text-white">API Key ของคุณ</DialogTitle>
                <DialogDescription className="text-indigo-300">
                  คัดลอก API Key นี้ เนื่องจากคุณจะไม่สามารถดูมันอีกครั้งหลังจากปิดหน้าต่างนี้
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 pt-4">
                <div className="p-4 rounded-md bg-indigo-900/60 border border-indigo-700/60">
                  <div className="flex">
                    <code className="flex-1 font-mono text-amber-300 break-all">
                      {selectedApiKey.apiKey}
                    </code>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(selectedApiKey.apiKey!)}
                      className="ml-2 h-7 w-7 p-0 text-indigo-300 hover:text-amber-300 hover:bg-transparent"
                    >
                      {copiedKey === selectedApiKey.apiKey ? (
                        <CheckCircle2 className="h-4 w-4" />
                      ) : (
                        <ClipboardCopy className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>
                <div className="flex items-center text-amber-400 bg-amber-900/20 p-3 rounded-md">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  <p className="text-sm">กรุณาเก็บ API Key นี้ให้ปลอดภัย นี่จะเป็นครั้งเดียวที่เราแสดง Key เต็ม</p>
                </div>
              </div>
              <DialogFooter>
                <Button 
                  onClick={() => {
                    setSelectedApiKey(null);
                    setActiveTab("keys");
                  }} 
                  className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700"
                >
                  เสร็จสิ้น
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}
      </div>
    </DashboardLayout>
  );
}