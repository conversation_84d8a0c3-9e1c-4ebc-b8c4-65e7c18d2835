import express, { Request, Response, NextFunction } from 'express';
import { storage } from '../storage';
import multer from 'multer';
import fs from 'fs';
import path from 'path';
import { scrypt, randomBytes, timingSafeEqual } from "crypto";
import { promisify } from "util";
import sharp from 'sharp';

const router = express.Router();
const scryptAsync = promisify(scrypt);

// ตั้งค่าที่เก็บไฟล์อัพโหลด
const uploadDir = path.join(process.cwd(), 'public', 'uploads');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// นำเข้ารูปอวตารเริ่มต้น
// สร้างรายการรูปอวตารเริ่มต้น
const defaultAvatars: { id: number; name: string; path: string }[] = [
  {
    id: 1,
    name: 'เทพเจ้าแห่งแสง',
    path: '/uploads/default-avatars/avatar-1.webp'
  },
  {
    id: 2,
    name: 'เทพเจ้าแห่งความมืด',
    path: '/uploads/default-avatars/avatar-2.webp'
  },
  {
    id: 3,
    name: 'เทพเจ้าแห่งสายฟ้า',
    path: '/uploads/default-avatars/avatar-3.webp'
  },
  {
    id: 4,
    name: 'เทพเจ้าแห่งทะเล',
    path: '/uploads/default-avatars/avatar-4.webp'
  },
  {
    id: 5,
    name: 'เทพเจ้าแห่งไฟ',
    path: '/uploads/default-avatars/avatar-5.webp'
  },
  {
    id: 6,
    name: 'เทพเจ้าแห่งพายุ',
    path: '/uploads/default-avatars/avatar-6.webp'
  },
  {
    id: 7,
    name: 'เทพเจ้าแห่งดาวเคราะห์',
    path: '/uploads/default-avatars/avatar-7.webp'
  },
  {
    id: 8,
    name: 'เทพเจ้าแห่งโชคลาภ',
    path: '/uploads/default-avatars/avatar-8.webp'
  },
  {
    id: 9,
    name: 'เทพเจ้าแห่งปัญญา',
    path: '/uploads/default-avatars/avatar-9.webp'
  },
  {
    id: 10,
    name: 'เทพเจ้าแห่งเวลา',
    path: '/uploads/default-avatars/avatar-10.webp'
  }
];

// ตั้งค่า multer สำหรับอัพโหลดรูปโปรไฟล์
const profileUpload = multer({
  storage: multer.diskStorage({
    destination: (req, file, cb) => {
      cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
      const ext = path.extname(file.originalname);
      cb(null, 'profile-' + uniqueSuffix + ext);
    }
  }),
  limits: {
    fileSize: 2 * 1024 * 1024, // 2MB
  },
  fileFilter: (req, file, cb) => {
    // อนุญาตเฉพาะไฟล์รูปภาพ
    if (!file.mimetype.startsWith('image/')) {
      return cb(new Error('อนุญาตเฉพาะไฟล์รูปภาพเท่านั้น'));
    }
    cb(null, true);
  }
});

const mapFormValuesToDatabaseFields = (formData: any) => {
  // แปลง company จากฟอร์มให้เป็น companyName ในฐานข้อมูล
  if (formData.company !== undefined) {
    formData.companyName = formData.company;
    delete formData.company;
  }
  
  return formData;
};

async function hashPassword(password: string) {
  const salt = randomBytes(16).toString("hex");
  const buf = (await scryptAsync(password, salt, 64)) as Buffer;
  return `${buf.toString("hex")}.${salt}`;
}

async function comparePasswords(supplied: string, stored: string) {
  const [hashed, salt] = stored.split(".");
  const hashedBuf = Buffer.from(hashed, "hex");
  const suppliedBuf = (await scryptAsync(supplied, salt, 64)) as Buffer;
  return timingSafeEqual(hashedBuf, suppliedBuf);
}

// อัปเดตข้อมูลโปรไฟล์
router.patch('/profile', async (req: Request, res: Response) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ error: 'กรุณาเข้าสู่ระบบ' });
  }

  try {
    const userId = req.user!.id;
    const formattedData = mapFormValuesToDatabaseFields(req.body);
    const updatedUser = await storage.updateUser(userId, formattedData);
    
    if (!updatedUser) {
      return res.status(404).json({ error: 'ไม่พบผู้ใช้' });
    }
    
    // ไม่ส่งคืนข้อมูลรหัสผ่าน
    const { password, ...userWithoutPassword } = updatedUser;
    
    return res.json(userWithoutPassword);
  } catch (error: any) {
    console.error('Error updating profile:', error);
    return res.status(500).json({ error: 'เกิดข้อผิดพลาดในการอัปเดตโปรไฟล์' });
  }
});

// อัปโหลดรูปโปรไฟล์
router.post('/profile-image', profileUpload.single('profileImage'), async (req: Request, res: Response) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ error: 'กรุณาเข้าสู่ระบบ' });
  }

  try {
    if (!req.file) {
      return res.status(400).json({ error: 'ไม่พบไฟล์รูปภาพ' });
    }
    
    const userId = req.user!.id;
    
    // ขนาดที่ต้องการสำหรับรูปโปรไฟล์
    const size = 200; // แก้ไขขนาดตามที่ต้องการ
    
    // ชื่อไฟล์ใหม่สำหรับรูปที่ย่อขนาดแล้ว
    const filename = `profile-${userId}-${Date.now()}.webp`;
    const outputPath = path.join(uploadDir, filename);
    
    // ย่อขนาดรูปภาพและแปลงเป็น WebP เพื่อลดขนาดไฟล์
    await sharp(req.file.path)
      .resize(size, size, { 
        fit: 'cover', 
        position: 'centre' 
      })
      .webp({ quality: 80 })
      .toFile(outputPath);
    
    // ลบไฟล์ต้นฉบับที่อัปโหลดมา
    fs.unlink(req.file.path, (err) => {
      if (err) console.error('Error deleting original file:', err);
    });
    
    // สร้าง URL สำหรับเข้าถึงรูปภาพ
    const imageUrl = `/uploads/${filename}`;
    
    // อัปเดตข้อมูลผู้ใช้ด้วย URL ของรูปภาพ
    const updatedUser = await storage.updateUser(userId, { profileImage: imageUrl });
    
    if (!updatedUser) {
      return res.status(404).json({ error: 'ไม่พบผู้ใช้' });
    }
    
    return res.json({ imageUrl });
  } catch (error: any) {
    console.error('Error uploading profile image:', error);
    return res.status(500).json({ error: 'เกิดข้อผิดพลาดในการอัปโหลดรูปโปรไฟล์' });
  }
});

// เปลี่ยนรหัสผ่าน
router.post('/change-password', async (req: Request, res: Response) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ error: 'กรุณาเข้าสู่ระบบ' });
  }

  try {
    const { currentPassword, newPassword } = req.body;
    
    if (!currentPassword || !newPassword) {
      return res.status(400).json({ error: 'กรุณากรอกข้อมูลให้ครบถ้วน' });
    }
    
    const userId = req.user!.id;
    const user = await storage.getUser(userId);
    
    if (!user) {
      return res.status(404).json({ error: 'ไม่พบผู้ใช้' });
    }
    
    // ตรวจสอบรหัสผ่านปัจจุบัน
    const isPasswordValid = await comparePasswords(currentPassword, user.password);
    
    if (!isPasswordValid) {
      return res.status(400).json({ error: 'รหัสผ่านปัจจุบันไม่ถูกต้อง' });
    }
    
    // เข้ารหัสรหัสผ่านใหม่
    const hashedPassword = await hashPassword(newPassword);
    
    // อัปเดตรหัสผ่าน
    await storage.updateUser(userId, { password: hashedPassword });
    
    return res.json({ success: true, message: 'เปลี่ยนรหัสผ่านสำเร็จ' });
  } catch (error: any) {
    console.error('Error changing password:', error);
    return res.status(500).json({ error: 'เกิดข้อผิดพลาดในการเปลี่ยนรหัสผ่าน' });
  }
});

// รับรายการรูปโปรไฟล์เริ่มต้น
router.get('/default-avatars', (req: Request, res: Response) => {
  try {
    return res.json(defaultAvatars);
  } catch (error: any) {
    console.error('Error getting default avatars:', error);
    return res.status(500).json({ error: 'เกิดข้อผิดพลาดในการดึงข้อมูลรูปโปรไฟล์เริ่มต้น' });
  }
});

// เลือกรูปโปรไฟล์เริ่มต้น
router.post('/select-default-avatar', async (req: Request, res: Response) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ error: 'กรุณาเข้าสู่ระบบ' });
  }

  try {
    const { avatarId } = req.body;
    
    if (!avatarId) {
      return res.status(400).json({ error: 'กรุณาเลือกรูปโปรไฟล์' });
    }
    
    const userId = req.user!.id;
    
    // หารูปโปรไฟล์จาก ID
    const selectedAvatar = defaultAvatars.find((avatar: {id: number, name: string, path: string}) => avatar.id === parseInt(avatarId));
    
    if (!selectedAvatar) {
      return res.status(404).json({ error: 'ไม่พบรูปโปรไฟล์ที่เลือก' });
    }
    
    // อัปเดตข้อมูลผู้ใช้ด้วย URL ของรูปภาพ
    const updatedUser = await storage.updateUser(userId, { profileImage: selectedAvatar.path });
    
    if (!updatedUser) {
      return res.status(404).json({ error: 'ไม่พบผู้ใช้' });
    }
    
    return res.json({ 
      imageUrl: selectedAvatar.path,
      name: selectedAvatar.name
    });
  } catch (error: any) {
    console.error('Error selecting default avatar:', error);
    return res.status(500).json({ error: 'เกิดข้อผิดพลาดในการเลือกรูปโปรไฟล์' });
  }
});

export default router;