import { Express, Request, Response, NextFunction } from "express";
import { storage } from "../storage";
import { addMonths } from "date-fns";

// ตรวจสอบว่าผู้ใช้งานได้เข้าสู่ระบบหรือไม่
function isAuthenticated(req: Request, res: Response, next: NextFunction) {
  if (req.isAuthenticated()) {
    return next();
  }
  return res.status(401).json({ message: "กรุณาเข้าสู่ระบบก่อนใช้งาน" });
}

// ตรวจสอบว่าผู้ใช้งานเป็นแอดมินหรือไม่
function isAdmin(req: Request, res: Response, next: NextFunction) {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ message: "กรุณาเข้าสู่ระบบก่อนใช้งาน" });
  }
  
  if (req.user?.role !== 'admin') {
    return res.status(403).json({ message: "ไม่มีสิทธิ์เข้าถึง เฉพาะผู้ดูแลระบบเท่านั้น" });
  }
  
  next();
}

// ฟังก์ชันคำนวณค่าใช้จ่ายเครดิตต่อการตรวจสอบสลิป
async function calculateCreditPerVerification(packageId: number) {
  try {
    // ดึงข้อมูลแพ็กเกจ
    const packageData = await storage.getPackage(packageId);
    if (!packageData) {
      throw new Error('ไม่พบแพ็กเกจที่ระบุ');
    }
    
    // กรณีที่มีการตั้งค่า creditPerVerification ไว้แล้ว ให้ใช้ค่านั้น
    if (packageData.creditPerVerification !== null && packageData.creditPerVerification !== undefined) {
      return packageData.creditPerVerification;
    }
    
    // คำนวณค่าใช้จ่ายเครดิตต่อการตรวจสอบ = ราคาแพคเกจ / จำนวนโควต้า
    // หากราคาหรือจำนวนโควต้าเป็น 0 ให้ใช้ค่าเริ่มต้นเป็น 0
    const price = packageData.price || 0;
    const requestsLimit = packageData.requestsLimit || 1; // ป้องกันการหารด้วย 0
    
    // คำนวณค่าเครดิตต่อการตรวจสอบสลิป 1 ครั้ง
    const creditPerVerification = price / requestsLimit;
    
    // อัพเดตค่าในฐานข้อมูล
    await storage.updatePackage(packageId, {
      creditPerVerification,
      updatedAt: new Date()
    });
    
    return creditPerVerification;
  } catch (error) {
    console.error('Error calculating credit per verification:', error);
    throw error;
  }
}

export function setupPackageRoutes(app: Express) {
  // === Admin Routes ===
  
  // เพิ่มแพ็คเกจให้ผู้ใช้งาน (สำหรับผู้ดูแลระบบ)
  app.post("/api/admin/users/:userId/packages", isAdmin, async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      
      if (isNaN(userId)) {
        return res.status(400).json({ message: "รหัสผู้ใช้ไม่ถูกต้อง" });
      }
      
      // ตรวจสอบว่าผู้ใช้มีอยู่จริงหรือไม่
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "ไม่พบผู้ใช้ที่ระบุ" });
      }
      
      const { packageId, durationMonths = 1, isActive = true } = req.body;
      
      if (!packageId) {
        return res.status(400).json({ message: "กรุณาระบุรหัสแพ็คเกจ" });
      }
      
      // ตรวจสอบว่าแพ็คเกจมีอยู่จริงหรือไม่
      const packageData = await storage.getPackage(packageId);
      if (!packageData) {
        return res.status(404).json({ message: "ไม่พบแพ็คเกจที่ระบุ" });
      }
      
      // คำนวณวันหมดอายุ
      const now = new Date();
      const expiryDate = new Date(now);
      expiryDate.setMonth(expiryDate.getMonth() + durationMonths);
      
      // สร้างแพ็คเกจสำหรับผู้ใช้
      const userPackage = await storage.createUserPackage({
        userId,
        packageId,
        isActive,
        startDate: now,
        endDate: expiryDate,
        durationMonths
      });
      
      res.status(201).json(userPackage);
    } catch (error) {
      console.error('Error creating user package:', error);
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการเพิ่มแพ็คเกจให้ผู้ใช้" });
    }
  });
  
  // ระบบจัดการแพ็คเกจ: ดึงข้อมูลแพ็คเกจทั้งหมด (สำหรับผู้ดูแลระบบ)
  app.get("/api/admin/packages", isAdmin, async (req, res) => {
    try {
      const packages = await storage.listPackages();
      res.status(200).json(packages);
    } catch (error) {
      console.error('Error fetching packages for admin:', error);
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการดึงข้อมูลแพ็คเกจ" });
    }
  });
  
  // เพิ่มแพ็คเกจใหม่ (สำหรับผู้ดูแลระบบ)
  app.post("/api/admin/packages", isAdmin, async (req, res) => {
    try {
      const packageData = req.body;
      // ตรวจสอบข้อมูลพื้นฐานที่จำเป็น
      if (!packageData.name || packageData.price === undefined || !packageData.requestsLimit) {
        return res.status(400).json({ message: "กรุณาระบุข้อมูลแพ็คเกจให้ครบถ้วน (ชื่อ, ราคา, จำนวนครั้งที่ใช้งานได้)" });
      }
      
      // คำนวณค่าเครดิตต่อการตรวจสอบสลิป 1 ครั้ง (ราคาหารจำนวนครั้ง)
      const requestsLimit = packageData.requestsLimit || 1; // ป้องกันการหารด้วย 0
      const creditPerVerification = packageData.price / requestsLimit;
      
      // เพิ่มข้อมูลแพ็คเกจ
      const newPackage = await storage.createPackage({
        ...packageData,
        creditPerVerification,
        isActive: packageData.isActive !== undefined ? packageData.isActive : true,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      res.status(201).json(newPackage);
    } catch (error) {
      console.error('Error creating package:', error);
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการสร้างแพ็คเกจใหม่" });
    }
  });
  
  // แก้ไขแพ็คเกจ (สำหรับผู้ดูแลระบบ)
  app.patch("/api/admin/packages/:id", isAdmin, async (req, res) => {
    try {
      const packageId = parseInt(req.params.id);
      if (isNaN(packageId)) {
        return res.status(400).json({ message: "รหัสแพ็คเกจไม่ถูกต้อง" });
      }
      
      // ตรวจสอบว่าแพ็คเกจมีอยู่จริงหรือไม่
      const packageData = await storage.getPackage(packageId);
      if (!packageData) {
        return res.status(404).json({ message: "ไม่พบแพ็คเกจที่ระบุ" });
      }
      
      // คำนวณค่าเครดิตต่อการตรวจสอบสลิป 1 ครั้ง ถ้ามีการเปลี่ยนราคาหรือจำนวนครั้ง
      let creditPerVerification = packageData.creditPerVerification;
      if (req.body.price !== undefined || req.body.requestsLimit !== undefined) {
        const price = req.body.price !== undefined ? req.body.price : packageData.price;
        const requestsLimit = req.body.requestsLimit !== undefined ? req.body.requestsLimit : packageData.requestsLimit;
        if (requestsLimit > 0) {
          creditPerVerification = price / requestsLimit;
        }
      }
      
      // อัพเดทข้อมูลแพ็คเกจ
      const updatedPackage = await storage.updatePackage(packageId, {
        ...req.body,
        creditPerVerification,
        updatedAt: new Date()
      });
      
      if (!updatedPackage) {
        return res.status(500).json({ message: "เกิดข้อผิดพลาดในการอัพเดทแพ็คเกจ" });
      }
      
      res.status(200).json(updatedPackage);
    } catch (error) {
      console.error('Error updating package:', error);
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการอัพเดทแพ็คเกจ" });
    }
  });
  
  // ลบแพ็คเกจ (สำหรับผู้ดูแลระบบ)
  app.delete("/api/admin/packages/:id", isAdmin, async (req, res) => {
    try {
      const packageId = parseInt(req.params.id);
      if (isNaN(packageId)) {
        return res.status(400).json({ message: "รหัสแพ็คเกจไม่ถูกต้อง" });
      }
      
      // ตรวจสอบว่าแพ็คเกจมีอยู่จริงหรือไม่
      const packageData = await storage.getPackage(packageId);
      if (!packageData) {
        return res.status(404).json({ message: "ไม่พบแพ็คเกจที่ระบุ" });
      }
      
      // เนื่องจากไม่มีฟังก์ชัน getUsersWithPackage ในระบบ
      // จึงข้ามการตรวจสอบว่ามีผู้ใช้งานแพ็คเกจนี้อยู่หรือไม่ไปก่อน
      // หมายเหตุ: ในระบบจริงควรมีการตรวจสอบว่ามีผู้ใช้งานแพ็คเกจนี้อยู่หรือไม่ก่อนลบ
      
      // ลบแพ็คเกจ
      // เนื่องจากไม่มีฟังก์ชัน deletePackage ในระบบ
      // จึงใช้ updatePackage โดยตั้งค่า isActive เป็น false แทน
      await storage.updatePackage(packageId, {
        isActive: false,
        updatedAt: new Date()
      });
      
      res.status(200).json({ message: "ลบแพ็คเกจเรียบร้อยแล้ว" });
    } catch (error) {
      console.error('Error deleting package:', error);
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการลบแพ็คเกจ" });
    }
  });
  
  // เรียงลำดับแพ็คเกจใหม่ (สำหรับผู้ดูแลระบบ)
  app.post("/api/admin/packages/reorder", isAdmin, async (req, res) => {
    try {
      const { items } = req.body;
      
      if (!items || !Array.isArray(items)) {
        return res.status(400).json({ message: "ข้อมูลไม่ถูกต้อง กรุณาระบุรายการแพ็คเกจที่ต้องการเรียงลำดับ" });
      }
      
      // ตรวจสอบว่ารูปแบบข้อมูลถูกต้องหรือไม่
      const isValidFormat = items.every(item => 
        typeof item === 'object' && 
        item !== null && 
        'id' in item && 
        'sortOrder' in item &&
        !isNaN(item.id) &&
        !isNaN(item.sortOrder)
      );
      
      if (!isValidFormat) {
        return res.status(400).json({ 
          message: "รูปแบบข้อมูลไม่ถูกต้อง กรุณาระบุ id และ sortOrder สำหรับแต่ละรายการ" 
        });
      }
      
      // อัพเดตลำดับของแพ็คเกจแต่ละรายการ
      for (const item of items) {
        await storage.updatePackage(item.id, {
          sortOrder: item.sortOrder,
          updatedAt: new Date()
        });
      }
      
      res.status(200).json({ 
        message: "เรียงลำดับแพ็คเกจเรียบร้อยแล้ว",
        updatedItems: items
      });
    } catch (error) {
      console.error('Error reordering packages:', error);
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการเรียงลำดับแพ็คเกจ" });
    }
  });
  
  // รีเซ็ทเครดิตในแพ็คเกจผู้ใช้ (สำหรับผู้ดูแลระบบ)
  app.post("/api/admin/users/:userId/packages/:id/reset-quota", isAdmin, async (req, res) => {
    console.log(`API รีเซ็ตโควต้าแพ็คเกจถูกเรียกใช้: userId=${req.params.userId}, packageId=${req.params.id}`);
    try {
      const userId = parseInt(req.params.userId);
      const packageId = parseInt(req.params.id);
      
      if (isNaN(userId) || isNaN(packageId)) {
        console.log(`รหัสผู้ใช้หรือรหัสแพ็คเกจไม่ถูกต้อง: userId=${req.params.userId}, packageId=${req.params.id}`);
        return res.status(400).json({ message: "รหัสผู้ใช้หรือรหัสแพ็คเกจไม่ถูกต้อง" });
      }
      
      // ตรวจสอบว่าผู้ใช้มีอยู่จริงหรือไม่
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "ไม่พบผู้ใช้ที่ระบุ" });
      }
      
      // ตรวจสอบว่ามีแพ็คเกจนี้อยู่จริงหรือไม่
      const userPackage = await storage.getUserPackage(packageId);
      if (!userPackage) {
        return res.status(404).json({ message: "ไม่พบแพ็คเกจของผู้ใช้ที่ระบุ" });
      }
      
      // ตรวจสอบว่าแพ็คเกจนี้เป็นของผู้ใช้ที่ระบุหรือไม่
      if (userPackage.userId !== userId) {
        return res.status(403).json({ message: "แพ็คเกจนี้ไม่ใช่ของผู้ใช้ที่ระบุ" });
      }
      
      // รีเซ็ทจำนวนที่ใช้งานในแพ็คเกจ
      const resetPackage = await storage.resetPackageQuota(packageId);
      
      if (!resetPackage) {
        return res.status(500).json({ message: "เกิดข้อผิดพลาดในการรีเซ็ทโควต้าแพ็คเกจ" });
      }
      
      res.status(200).json({
        message: "รีเซ็ทโควต้าแพ็คเกจสำเร็จ",
        package: resetPackage
      });
    } catch (error) {
      console.error('Error resetting package quota:', error);
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการรีเซ็ทโควต้าแพ็คเกจ" });
    }
  });
  
  // แก้ไขแพ็คเกจของผู้ใช้ (สำหรับผู้ดูแลระบบ)
  app.patch("/api/admin/users/:userId/packages/:id", isAdmin, async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const packageId = parseInt(req.params.id);
      
      if (isNaN(userId) || isNaN(packageId)) {
        return res.status(400).json({ message: "รหัสผู้ใช้หรือรหัสแพ็คเกจไม่ถูกต้อง" });
      }
      
      // ตรวจสอบว่าผู้ใช้มีอยู่จริงหรือไม่
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "ไม่พบผู้ใช้ที่ระบุ" });
      }
      
      // ตรวจสอบว่ามีแพ็คเกจนี้อยู่จริงหรือไม่
      const userPackage = await storage.getUserPackage(packageId);
      if (!userPackage) {
        return res.status(404).json({ message: "ไม่พบแพ็คเกจของผู้ใช้ที่ระบุ" });
      }
      
      // ตรวจสอบว่าแพ็คเกจนี้เป็นของผู้ใช้ที่ระบุหรือไม่
      if (userPackage.userId !== userId) {
        return res.status(403).json({ message: "แพ็คเกจนี้ไม่ใช่ของผู้ใช้ที่ระบุ" });
      }
      
      // ทำงานตาม request ที่ได้รับ
      // กรณีพิเศษ: ถ้ามีพารามิเตอร์ reset = true ให้รีเซ็ตโควต้าแพ็คเกจ (รองรับการเรียกผ่าน client เดิม)
      if (req.body.reset === true) {
        console.log(`รีเซ็ตโควต้าแพ็คเกจผ่าน PATCH API: userId=${userId}, packageId=${packageId}`);
        const resetPackage = await storage.resetPackageQuota(packageId);
        
        if (!resetPackage) {
          return res.status(500).json({ message: "เกิดข้อผิดพลาดในการรีเซ็ทโควต้าแพ็คเกจ" });
        }
        
        return res.status(200).json({
          message: "รีเซ็ทโควต้าแพ็คเกจสำเร็จ",
          package: resetPackage
        });
      }
      
      // การอัพเดทปกติ
      const updatedPackage = await storage.updateUserPackage(packageId, {
        ...req.body,
        updatedAt: new Date()
      });
      
      if (!updatedPackage) {
        return res.status(500).json({ message: "เกิดข้อผิดพลาดในการอัพเดทแพ็คเกจ" });
      }
      
      res.status(200).json(updatedPackage);
    } catch (error) {
      console.error('Error updating user package:', error);
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการอัพเดทแพ็คเกจ" });
    }
  });
  
  // ลบหรือยกเลิกแพ็คเกจของผู้ใช้ (สำหรับผู้ดูแลระบบ)
  app.delete("/api/admin/users/:userId/packages/:id", isAdmin, async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const packageId = parseInt(req.params.id);
      
      if (isNaN(userId) || isNaN(packageId)) {
        return res.status(400).json({ message: "รหัสผู้ใช้หรือรหัสแพ็คเกจไม่ถูกต้อง" });
      }
      
      // ตรวจสอบว่าผู้ใช้มีอยู่จริงหรือไม่
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "ไม่พบผู้ใช้ที่ระบุ" });
      }
      
      // ตรวจสอบว่ามีแพ็คเกจนี้อยู่จริงหรือไม่
      const userPackage = await storage.getUserPackage(packageId);
      if (!userPackage) {
        return res.status(404).json({ message: "ไม่พบแพ็คเกจของผู้ใช้ที่ระบุ" });
      }
      
      // ตรวจสอบว่าแพ็คเกจนี้เป็นของผู้ใช้ที่ระบุหรือไม่
      if (userPackage.userId !== userId) {
        return res.status(403).json({ message: "แพ็คเกจนี้ไม่ใช่ของผู้ใช้ที่ระบุ" });
      }
      
      // ยกเลิกแพ็คเกจ (เปลี่ยนสถานะเป็น inactive ไม่ลบออกจากฐานข้อมูล)
      const updatedPackage = await storage.updateUserPackage(packageId, {
        isActive: false,
        updatedAt: new Date()
      });
      
      res.status(200).json({ message: "ยกเลิกแพ็คเกจสำเร็จ" });
    } catch (error) {
      console.error('Error canceling user package:', error);
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการยกเลิกแพ็คเกจ" });
    }
  });
  
  // === User Routes ===
  // ดึงข้อมูลแพ็กเกจที่ใช้งานได้ทั้งหมด
  app.get("/api/packages", async (req, res) => {
    try {
      const packages = await storage.listPackages();
      res.status(200).json(packages);
    } catch (error) {
      console.error('Error fetching packages:', error);
      res.status(500).json({ message: 'เกิดข้อผิดพลาดในการดึงข้อมูลแพ็กเกจ' });
    }
  });
  
  // ดึงข้อมูลแพ็กเกจที่ผู้ใช้งานสมัคร
  app.get("/api/user/packages", isAuthenticated, async (req, res) => {
    try {
      const userPackages = await storage.listUserPackages(req.user!.id);
      res.status(200).json(userPackages);
    } catch (error) {
      console.error('Error fetching user packages:', error);
      res.status(500).json({ message: 'เกิดข้อผิดพลาดในการดึงข้อมูลแพ็กเกจของผู้ใช้งาน' });
    }
  });
  
  // ดึงข้อมูลแพ็กเกจที่ผู้ใช้งานกำลังใช้งานอยู่
  app.get("/api/user/active-package", isAuthenticated, async (req, res) => {
    try {
      const activePackage = await storage.getUserActivePackage(req.user!.id);
      
      if (!activePackage) {
        return res.status(404).json({ message: 'ไม่พบแพ็กเกจที่ใช้งานอยู่' });
      }
      
      res.status(200).json(activePackage);
    } catch (error) {
      console.error('Error fetching active package:', error);
      res.status(500).json({ message: 'เกิดข้อผิดพลาดในการดึงข้อมูลแพ็กเกจที่ใช้งานอยู่' });
    }
  });
  
  // สมัครแพ็กเกจใหม่
  app.post("/api/user/subscribe", isAuthenticated, async (req, res) => {
    try {
      const { packageId, durationMonths = 1, couponCode } = req.body;
      
      if (!packageId) {
        return res.status(400).json({ message: 'กรุณาระบุ packageId' });
      }
      
      // ตรวจสอบว่าแพ็กเกจมีอยู่จริงหรือไม่
      const packageData = await storage.getPackage(packageId);
      if (!packageData) {
        return res.status(404).json({ message: 'ไม่พบแพ็กเกจที่ระบุ' });
      }
      
      // ตรวจสอบว่าแพ็กเกจกำลังใช้งานอยู่หรือไม่
      if (!packageData.isActive) {
        return res.status(400).json({ message: 'แพ็กเกจนี้ไม่สามารถใช้งานได้ในขณะนี้' });
      }
      
      // ตรวจสอบว่าผู้ใช้มีสิทธิ์ใช้แพ็กเกจนี้หรือไม่
      const userAllowedPackages = await storage.getUserAllowedPackages(req.user!.id);
      if (userAllowedPackages && userAllowedPackages.length > 0) {
        const isAllowed = userAllowedPackages.includes(packageId);
        if (!isAllowed) {
          return res.status(403).json({ 
            message: 'คุณไม่มีสิทธิ์สมัครแพ็กเกจนี้ กรุณาติดต่อผู้ดูแลระบบ',
            allowedPackages: userAllowedPackages 
          });
        }
      }
      
      // ตรวจสอบเพิ่มเติมสำหรับแพ็กเกจฟรี
      if (packageData.price === 0) {
        // ตรวจสอบว่าผู้ใช้เคยใช้แพ็กเกจฟรีนี้มาก่อนหรือไม่
        const previousFreePackages = await storage.getUserPreviousPackagesByType(req.user!.id, packageId);
        
        if (previousFreePackages.length > 0) {
          return res.status(400).json({ 
            message: 'คุณเคยใช้แพ็กเกจฟรีนี้ไปแล้ว ไม่สามารถสมัครซ้ำได้',
            previousUsage: previousFreePackages
          });
        }
      }
      
      // คำนวณราคาตามระยะเวลา
      let packagePrice = packageData.price;
      let totalPrice = packagePrice * durationMonths;
      let discountAmount = 0;
      let discountDetails = null;
      
      // คำนวณส่วนลดตามระยะเวลา
      if (durationMonths === 3 && packageData.discount3Months > 0) {
        discountAmount = Math.round(totalPrice * (packageData.discount3Months / 100));
        discountDetails = {
          type: 'duration',
          percent: packageData.discount3Months,
          amount: discountAmount
        };
      } else if (durationMonths === 6 && packageData.discount6Months > 0) {
        discountAmount = Math.round(totalPrice * (packageData.discount6Months / 100));
        discountDetails = {
          type: 'duration',
          percent: packageData.discount6Months,
          amount: discountAmount
        };
      } else if (durationMonths === 12 && packageData.discount12Months > 0) {
        discountAmount = Math.round(totalPrice * (packageData.discount12Months / 100));
        discountDetails = {
          type: 'duration',
          percent: packageData.discount12Months,
          amount: discountAmount
        };
      }
      
      // ตรวจสอบคูปองส่วนลด (ถ้ามี)
      let coupon = null;
      if (couponCode) {
        coupon = await storage.getCouponByCode(couponCode);
        
        if (coupon) {
          // ตรวจสอบเงื่อนไขคูปอง
          const now = new Date();
          if (!coupon.isActive) {
            return res.status(400).json({ message: 'คูปองนี้ไม่สามารถใช้งานได้' });
          }
          
          if (now < coupon.startDate) {
            return res.status(400).json({ message: 'คูปองนี้ยังไม่ถึงวันที่สามารถใช้งานได้' });
          }
          
          if (now > coupon.endDate) {
            return res.status(400).json({ message: 'คูปองนี้หมดอายุแล้ว' });
          }
          
          if (coupon.usageCount >= coupon.maxUsage) {
            return res.status(400).json({ message: 'คูปองนี้ถูกใช้งานเต็มจำนวนแล้ว' });
          }
          
          // คำนวณส่วนลดจากคูปอง
          if (coupon.discountPercent > 0) {
            const couponDiscountAmount = Math.round((totalPrice - discountAmount) * (coupon.discountPercent / 100));
            discountAmount += couponDiscountAmount;
            discountDetails = {
              ...discountDetails,
              coupon: {
                code: coupon.code,
                type: 'percent',
                percent: coupon.discountPercent,
                amount: couponDiscountAmount
              }
            };
          } else if (coupon.discountAmount > 0) {
            discountAmount += coupon.discountAmount;
            discountDetails = {
              ...discountDetails,
              coupon: {
                code: coupon.code,
                type: 'amount',
                amount: coupon.discountAmount
              }
            };
          }
        }
      }
      
      // คำนวณราคาสุทธิ
      const finalPrice = totalPrice - discountAmount;
      
      if (finalPrice < 0) {
        return res.status(400).json({ message: 'เกิดข้อผิดพลาดในการคำนวณราคา' });
      }
      
      // ตรวจสอบเครดิตผู้ใช้งาน
      const userCredit = await storage.getUserCredit(req.user!.id);
      
      // ตรวจสอบว่าเป็นแพคเกจฟรีหรือไม่
      const isFreePackage = finalPrice === 0;
      
      // ถ้าไม่ใช่แพคเกจฟรี ตรวจสอบว่ามีเครดิตเพียงพอหรือไม่
      if (!isFreePackage && userCredit < finalPrice) {
        return res.status(400).json({ 
          message: 'เครดิตไม่เพียงพอสำหรับการสมัครแพ็กเกจนี้',
          credit: userCredit,
          required: finalPrice,
          shortfall: finalPrice - userCredit
        });
      }
      
      // คำนวณวันเริ่มต้นและวันสิ้นสุด
      const startDate = new Date();
      // คำนวณวันสิ้นสุดตามระยะเวลาที่เลือก
      const endDate = addMonths(startDate, durationMonths);
      
      // หักเครดิต (เฉพาะกรณีที่ไม่ใช่แพคเกจฟรี)
      if (!isFreePackage) {
        await storage.addUserCredit(req.user!.id, -finalPrice);
      }
      
      // เพิ่มการใช้งานคูปอง (ถ้ามี)
      if (coupon) {
        await storage.incrementCouponUsage(coupon.id);
      }
      
      // สร้างข้อมูลการสมัครแพ็กเกจ
      // ตรวจสอบว่ามี durationDays ใน package หรือไม่
      let durationDays = 30 * durationMonths; // ค่าเริ่มต้น
      
      // ถ้าเป็นแพ็กเกจฟรี และมีการกำหนด durationDays ไว้ ให้ใช้ค่าจาก durationDays แทน
      if (packageData.durationDays && isFreePackage) {
        durationDays = packageData.durationDays;
        // คำนวณวันสิ้นสุดใหม่
        const endDateFromDays = new Date(startDate);
        endDateFromDays.setDate(endDateFromDays.getDate() + durationDays);
        // ใช้ค่านี้แทนค่าที่คำนวณจาก durationMonths
        endDate.setTime(endDateFromDays.getTime());
      }
      
      // ตรวจสอบและยกเลิกแพ็กเกจที่ใช้งานอยู่ปัจจุบัน (ถ้ามี)
      const activePackage = await storage.getUserActivePackage(req.user!.id);
      if (activePackage) {
        console.log(`ยกเลิกแพ็กเกจเก่า ID: ${activePackage.id} ของผู้ใช้ ID: ${req.user!.id}`);
        await storage.updateUserPackage(activePackage.id, {
          isActive: false,
          updatedAt: new Date()
        });
      }
      
      // คำนวณและอัพเดตค่าใช้จ่ายเครดิตต่อการตรวจสอบสลิป
      const creditPerVerification = await calculateCreditPerVerification(packageId);
      
      // สร้างแพ็กเกจใหม่
      const userPackage = await storage.createUserPackage({
        userId: req.user!.id,
        packageId,
        startDate,
        endDate,
        isActive: true,
        durationMonths: durationMonths
      });
      
      res.status(201).json({
        userPackage,
        pricing: {
          basePrice: packagePrice,
          duration: durationMonths,
          totalBasePrice: totalPrice,
          discount: discountAmount,
          discountDetails,
          finalPrice,
          remainingCredit: userCredit - (isFreePackage ? 0 : finalPrice),
          creditPerVerification: creditPerVerification
        }
      });
    } catch (error) {
      console.error('Error subscribing to package:', error);
      res.status(500).json({ message: 'เกิดข้อผิดพลาดในการสมัครแพ็กเกจ' });
    }
  });

  // ต่ออายุหรือยกเลิกแพ็กเกจ
  app.patch("/api/user/packages/:id", isAuthenticated, async (req, res) => {
    try {
      const packageId = parseInt(req.params.id);
      const { action } = req.body;
      
      // ตรวจสอบว่าแพ็กเกจนี้เป็นของผู้ใช้งานหรือไม่
      const userPackage = await storage.getUserPackage(packageId);
      
      if (!userPackage) {
        return res.status(404).json({ message: 'ไม่พบแพ็กเกจที่ระบุ' });
      }
      
      if (userPackage.userId !== req.user!.id) {
        return res.status(403).json({ message: 'คุณไม่มีสิทธิ์ในการแก้ไขแพ็กเกจนี้' });
      }
      
      let updatedPackage;
      
      if (action === 'renew') {
        // ต่ออายุแพ็กเกจ
        const endDate = addMonths(userPackage.endDate, 1);
        updatedPackage = await storage.updateUserPackage(packageId, {
          endDate,
          isActive: true
        });
      } else if (action === 'cancel') {
        // ยกเลิกแพ็กเกจ
        updatedPackage = await storage.updateUserPackage(packageId, {
          isActive: false
        });
      } else {
        return res.status(400).json({ message: 'ระบุ action ไม่ถูกต้อง (renew หรือ cancel)' });
      }
      
      res.status(200).json(updatedPackage);
    } catch (error) {
      console.error('Error updating user package:', error);
      res.status(500).json({ message: 'เกิดข้อผิดพลาดในการอัปเดตแพ็กเกจ' });
    }
  });
}
