import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { io } from 'socket.io-client';
import { AlertCircle, CheckCircle2 } from 'lucide-react';

export default function SocketDebugger() {
  const [isConnected, setIsConnected] = useState(false);
  const [messages, setMessages] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [socketInstance, setSocketInstance] = useState<any | null>(null);
  const [testCount, setTestCount] = useState(0);

  useEffect(() => {
    return () => {
      if (socketInstance) {
        socketInstance.disconnect();
      }
    };
  }, [socketInstance]);

  const connectSocket = () => {
    try {
      // ล้างข้อความเก่า
      setMessages([]);
      setError(null);

      // สร้าง URL ของ Socket.IO server
      const protocol = window.location.protocol === 'https:' ? 'https://' : 'http://';
      const host = window.location.host;
      const url = `${protocol}${host}`;
      
      // แสดงข้อความการเชื่อมต่อ
      addMessage(`กำลังเชื่อมต่อกับ Socket.IO ที่ ${url}...`);
      
      // สร้าง Socket.IO client
      const socket = io(url, {
        transports: ['polling'],
        reconnectionAttempts: 3,
        timeout: 10000
      });
      
      setSocketInstance(socket);
      
      // จัดการกับเหตุการณ์ต่าง ๆ
      socket.on('connect', () => {
        setIsConnected(true);
        addMessage(`✅ เชื่อมต่อสำเร็จ! Socket ID: ${socket.id}`);
      });
      
      socket.on('connect_error', (err) => {
        const errorMsg = `❌ การเชื่อมต่อล้มเหลว: ${err.message || 'ไม่ทราบสาเหตุ'}`;
        addMessage(errorMsg);
        setError(errorMsg);
      });
      
      socket.on('disconnect', (reason) => {
        setIsConnected(false);
        addMessage(`🔌 การเชื่อมต่อถูกตัด: ${reason}`);
      });
      
      // รับข้อความทดสอบ
      socket.on('connected', (data) => {
        addMessage(`📣 ได้รับข้อความ 'connected': ${JSON.stringify(data)}`);
      });
      
      socket.on('connection_test', (data) => {
        addMessage(`📣 ได้รับข้อความ 'connection_test': ${JSON.stringify(data)}`);
      });
    } catch (err: any) {
      const errorMsg = `❌ เกิดข้อผิดพลาดในการสร้างการเชื่อมต่อ: ${err.message}`;
      addMessage(errorMsg);
      setError(errorMsg);
    }
  };
  
  const disconnectSocket = () => {
    if (socketInstance) {
      socketInstance.disconnect();
      addMessage('🔌 ตัดการเชื่อมต่อ Socket.IO แล้ว');
      setSocketInstance(null);
    }
  };
  
  const testConnection = () => {
    if (socketInstance && isConnected) {
      const testPayload = { 
        message: 'ทดสอบการส่งข้อความ',
        timestamp: new Date().toISOString(),
        count: testCount + 1
      };
      
      try {
        socketInstance.emit('test_message', testPayload);
        addMessage(`📤 ส่งข้อความทดสอบ: ${JSON.stringify(testPayload)}`);
        setTestCount(prev => prev + 1);
      } catch (err: any) {
        const errorMsg = `❌ ไม่สามารถส่งข้อความได้: ${err.message}`;
        addMessage(errorMsg);
        setError(errorMsg);
      }
    } else {
      addMessage('❌ ไม่สามารถส่งข้อความได้: ไม่ได้เชื่อมต่อกับ Socket.IO');
    }
  };

  const subscribeToChannel = () => {
    if (socketInstance && isConnected) {
      try {
        socketInstance.emit('subscribe', 'dashboard');
        addMessage('📝 สมัครสมาชิกห้อง dashboard แล้ว');
      } catch (err: any) {
        const errorMsg = `❌ ไม่สามารถสมัครสมาชิกห้องได้: ${err.message}`;
        addMessage(errorMsg);
        setError(errorMsg);
      }
    } else {
      addMessage('❌ ไม่สามารถสมัครสมาชิกห้องได้: ไม่ได้เชื่อมต่อกับ Socket.IO');
    }
  };
  
  const addMessage = (message: string) => {
    setMessages(prev => [...prev, {
      id: Date.now(),
      text: message,
      time: new Date().toLocaleTimeString()
    }]);
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Socket.IO Debugger</CardTitle>
          <CardDescription>
            เครื่องมือตรวจสอบการเชื่อมต่อ Socket.IO สำหรับแก้ไขปัญหา
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center mb-4">
            <Badge variant={isConnected ? "default" : "destructive"} className={`mr-2 ${isConnected ? 'bg-green-500 hover:bg-green-600 text-white' : ''}`}>
              {isConnected ? 'เชื่อมต่ออยู่' : 'ไม่ได้เชื่อมต่อ'}
            </Badge>
            <span className="text-sm text-gray-500">
              {socketInstance ? `Socket ID: ${socketInstance.id || '-'}` : 'ไม่มีการเชื่อมต่อ'}
            </span>
          </div>
          
          <div className="flex flex-wrap gap-2 mb-4">
            <Button 
              onClick={connectSocket} 
              disabled={!!socketInstance}
              variant="default"
              className="flex-1"
            >
              เชื่อมต่อ Socket.IO
            </Button>
            
            <Button 
              onClick={disconnectSocket} 
              disabled={!socketInstance}
              variant="outline"
              className="flex-1"
            >
              ตัดการเชื่อมต่อ
            </Button>
            
            <Button 
              onClick={testConnection} 
              disabled={!isConnected}
              variant="secondary"
              className="flex-1"
            >
              ส่งข้อความทดสอบ
            </Button>
            
            <Button 
              onClick={subscribeToChannel} 
              disabled={!isConnected}
              variant="secondary"
              className="flex-1"
            >
              เข้าร่วมห้อง Dashboard
            </Button>
          </div>
          
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>เกิดข้อผิดพลาด</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          {isConnected && (
            <Alert className="mb-4">
              <CheckCircle2 className="h-4 w-4" />
              <AlertTitle>เชื่อมต่อสำเร็จ</AlertTitle>
              <AlertDescription>คุณเชื่อมต่อกับ Socket.IO server แล้ว</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>บันทึกการทำงาน</CardTitle>
          <CardDescription>
            ข้อความทั้งหมดที่ส่งและรับผ่าน Socket.IO
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="max-h-[400px] overflow-y-auto border rounded-md p-4 bg-muted/20">
            {messages.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                ยังไม่มีข้อความ กรุณาเชื่อมต่อและทดสอบการส่งข้อมูล
              </div>
            ) : (
              <div className="space-y-2">
                {messages.map((msg) => (
                  <div key={msg.id} className="text-sm">
                    <span className="text-gray-500 text-xs">{msg.time}</span>
                    <p className="mt-1">{msg.text}</p>
                    <Separator className="my-2" />
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}