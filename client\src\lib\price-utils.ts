/**
 * คำนวณราคาพร้อมส่วนลดตามระยะเวลา
 * @param pkg แพ็กเกจที่ต้องการคำนวณราคา (มีรูปแบบ {price, discount3Months, discount6Months, discount12Months})
 * @param durationMonths ระยะเวลาที่สมัคร (1, 3, 6, หรือ 12 เดือน)
 * @returns ราคาที่คำนวณแล้ว
 */
export function calculatePriceWithDiscount(pkg: any, durationMonths: number): number {
  // ตรวจสอบว่า pkg เป็น object หรือไม่
  if (!pkg || typeof pkg !== 'object') return 0;
  
  // ดึงค่า price จาก pkg ไม่ว่าจะเป็น object หรือมีค่า price โดยตรง
  const price = typeof pkg.price === 'number' ? pkg.price : 0;
  if (price === 0) return 0;
  
  let discountPercent = 0;
  
  // ตรวจสอบระยะเวลาและกำหนดเปอร์เซ็นต์ส่วนลด
  if (durationMonths === 3 && pkg.discount3Months) {
    discountPercent = parseFloat(pkg.discount3Months) || 0;
  } else if (durationMonths === 6 && pkg.discount6Months) {
    discountPercent = parseFloat(pkg.discount6Months) || 0;
  } else if (durationMonths === 12 && pkg.discount12Months) {
    discountPercent = parseFloat(pkg.discount12Months) || 0;
  }
  
  // คำนวณราคาทั้งหมดพร้อมส่วนลด
  const totalPrice = price * durationMonths;
  const discount = totalPrice * (discountPercent / 100);
  
  return Math.round(totalPrice - discount);
}