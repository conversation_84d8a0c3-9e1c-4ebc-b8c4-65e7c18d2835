import { Link } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { GodlyNavbar } from "@/components/layouts/godly-navbar";
import { Footer } from "@/components/layouts/footer";
import {
  <PERSON>Hal<PERSON>,
  Shuffle,
  <PERSON>,
  Sparkles,
  Star,
  Zap,
  Clock
} from "lucide-react";
import React from "react";

// Simple Bank Slip Component
function SimpleBankSlipPreview() {
  return (
    <div className="rounded-xl shadow-lg relative max-w-md mx-auto">
      {/* Simple border */}
      <div className="absolute inset-0 rounded-xl border border-purple-500/30"></div>

      {/* Main slip container */}
      <div className="relative rounded-xl overflow-hidden">
        {/* Simple background */}
        <div className="absolute inset-0 bg-gradient-to-b from-indigo-900 to-purple-900"></div>

        {/* Content container */}
        <div className="relative z-10 p-6">
          {/* Header */}
          <div className="flex justify-between items-start mb-4">
            <div className="flex-1">
              <h1 className="text-xl font-bold text-white mb-1 flex items-center">
                รายการโอนเงินสำเร็จ
                <Sparkles className="h-4 w-4 ml-2 text-yellow-300" />
              </h1>

              <div className="text-white/80 text-sm">
                <span>19 เม.ย. 2568</span>
                <span className="mx-1">•</span>
                <span>15:30:45</span>
              </div>
            </div>

            {/* Bank Logo */}
            <div className="flex items-center">
              <div className="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-lg">K</span>
              </div>
            </div>
          </div>

          {/* Reference number */}
          <div className="text-sm text-white/70 mb-4">
            หมายเลขอ้างอิง: <span className="font-medium text-white">2568041915305689512</span>
          </div>

          {/* Amount box */}
          <div className="bg-indigo-800/60 rounded-xl p-5 mb-5">
            <div className="text-white/80 text-base mb-1 text-center">จำนวนเงิน</div>
            <div className="text-center">
              <span className="text-3xl font-bold text-white">
                ฿37,865.50
              </span>
            </div>
          </div>

          {/* Transaction Details */}
          <div className="space-y-3">
            {/* From Account */}
            <div className="flex justify-between items-center py-2 border-b border-white/10">
              <div className="text-white/70">จาก</div>
              <div className="text-right">
                <div className="text-base font-bold text-white">
                  นาย เจริญ ค
                </div>
                <div className="text-sm text-white/50">xxx-x-x5678-x</div>
              </div>
            </div>

            {/* To Account */}
            <div className="flex justify-between items-center py-2 border-b border-white/10">
              <div className="text-white/70">ไปยัง</div>
              <div className="text-right">
                <div className="text-base font-bold text-white flex items-center justify-end">
                  <span>Slipkuy</span>
                  <Star className="h-3.5 w-3.5 ml-1 text-yellow-300" />
                </div>
                <div className="text-sm text-white/50">xxx-x-x3456-x</div>
              </div>
            </div>

            {/* Fee */}
            <div className="flex justify-between items-center py-2">
              <div className="text-white/70">ค่าธรรมเนียม</div>
              <div className="text-base font-bold text-white">฿0.00</div>
            </div>
          </div>

          {/* Button */}
          <button
            className="mt-5 bg-gradient-to-r from-violet-600 to-indigo-600 hover:from-violet-500 hover:to-indigo-500 rounded-xl py-3 px-4 text-center text-white w-full"
          >
            ตรวจสอบสลิป
          </button>
        </div>
      </div>
    </div>
  );
}

// Features Section Component
function FeaturesSection() {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-12">
      <div className="bg-white/10 backdrop-blur-sm p-5 rounded-xl border border-white/20 shadow-md">
        <div className="text-yellow-300 mb-3"><Zap className="h-7 w-7" /></div>
        <h3 className="text-xl font-medium mb-2">รวดเร็ว</h3>
        <p className="text-white/80">ตรวจสอบสลิปได้ภายในเวลาไม่กี่วินาที</p>
      </div>

      <div className="bg-white/10 backdrop-blur-sm p-5 rounded-xl border border-white/20 shadow-md">
        <div className="text-yellow-300 mb-3"><ShieldHalf className="h-7 w-7" /></div>
        <h3 className="text-xl font-medium mb-2">ปลอดภัย</h3>
        <p className="text-white/80">มาตรฐานความปลอดภัยสูง ข้อมูลเป็นความลับ</p>
      </div>

      <div className="bg-white/10 backdrop-blur-sm p-5 rounded-xl border border-white/20 shadow-md">
        <div className="text-yellow-300 mb-3"><Shuffle className="h-7 w-7" /></div>
        <h3 className="text-xl font-medium mb-2">ยืดหยุ่น</h3>
        <p className="text-white/80">ปรับแต่งการใช้งานตามความต้องการของธุรกิจ</p>
      </div>

      <div className="bg-white/10 backdrop-blur-sm p-5 rounded-xl border border-white/20 shadow-md">
        <div className="text-yellow-300 mb-3"><Code className="h-7 w-7" /></div>
        <h3 className="text-xl font-medium mb-2">API สำหรับนักพัฒนา</h3>
        <p className="text-white/80">เชื่อมต่อได้ง่ายกับระบบของคุณ</p>
      </div>
    </div>
  );
}

// API Feature Highlight Component
function ApiFeatureHighlight() {
  return (
    <div className="bg-white/5 rounded-xl p-6 mt-12 border border-white/10">
      <h2 className="text-2xl font-bold mb-4 flex items-center">
        <Code className="h-6 w-6 mr-2 text-yellow-300" />
        API ที่ใช้งานง่าย
      </h2>

      <div className="bg-black/40 rounded-md p-4 font-mono text-sm text-white/80 overflow-x-auto">
        <pre>{`// ตรวจสอบสลิปด้วย API
POST /api/v1/verify-slip
{
  "image": "base64_encoded_slip_image",
  "options": {
    "validateAmount": true,
    "validateDate": true
  }
}`}</pre>
      </div>

      <div className="mt-6 flex justify-end">
        <Button variant="outline" className="text-white border-white/20 hover:bg-white/10">
          <Link href="/api-docs">ดูเอกสาร API</Link>
        </Button>
      </div>
    </div>
  );
}

// Main Home Page Component
export default function HomePage() {
  return (
    <div className="min-h-screen flex flex-col">
      <GodlyNavbar />

      {/* Hero Section - Simplified Divine Theme */}
      <section id="home" className="bg-gradient-to-b from-indigo-800 to-purple-900 text-white py-16 overflow-hidden relative">
        {/* Simple stars background */}
        <div className="absolute inset-0 opacity-30"
          style={{
            backgroundImage: `radial-gradient(white, rgba(255,255,255,.2) 2px, transparent 3px)`,
            backgroundSize: '100px 100px'
          }}
        ></div>

        <div className="container mx-auto px-4 md:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            <div>
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">
                <span className="text-white">ตรวจสอบสลิปธนาคารด้วย</span>
                <div className="mt-2 mb-3 px-4 py-2 bg-white inline-block rounded-lg shadow-lg">
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-purple-600 font-extrabold">API ระดับมืออาชีพ</span>
                  <Sparkles className="inline-block ml-2 h-6 w-6 text-yellow-500" />
                </div>
              </h1>

              <p className="text-lg mb-8 text-white/90">
                บริการตรวจสอบข้อมูลสลิปธนาคารที่รวดเร็ว แม่นยำ ปลอดภัย และน่าเชื่อถือ เหมาะสำหรับธุรกิจทุกขนาด
              </p>

              <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
                <Button asChild variant="default" size="lg" className="bg-white hover:bg-white/90 text-primary hover:text-primary/90 shadow-lg">
                  <Link href="/auth">
                    <Sparkles className="h-5 w-5 mr-2 text-yellow-500" /> ทดลองใช้ฟรี
                  </Link>
                </Button>
                <Button asChild variant="secondary" size="lg" className="bg-secondary hover:bg-secondary/90 text-white shadow-lg">
                  <Link href="/packages">
                    <Star className="h-5 w-5 mr-2" /> ดูแพ็กเกจทั้งหมด
                  </Link>
                </Button>
              </div>
            </div>

            <div className="hidden md:block">
              <SimpleBankSlipPreview />
            </div>
          </div>

          <FeaturesSection />

          <div className="mt-16">
            <h2 className="text-2xl md:text-3xl font-bold mb-6 text-center">ทำไมต้องเลือกใช้ SLIPKUY?</h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white/5 p-6 rounded-xl border border-white/10">
                <h3 className="text-xl font-semibold mb-3 flex items-center">
                  <Clock className="h-5 w-5 mr-2 text-yellow-300" />
                  ประหยัดเวลา
                </h3>
                <p>ลดเวลาในการตรวจสอบสลิปด้วยตาเปล่า ลดความผิดพลาดในการทำงาน</p>
              </div>

              <div className="bg-white/5 p-6 rounded-xl border border-white/10">
                <h3 className="text-xl font-semibold mb-3 flex items-center">
                  <Sparkles className="h-5 w-5 mr-2 text-yellow-300" />
                  ความแม่นยำสูง
                </h3>
                <p>ระบบตรวจสอบอัตโนมัติที่แม่นยำ ทำงานได้ตลอด 24 ชั่วโมง</p>
              </div>

              <div className="bg-white/5 p-6 rounded-xl border border-white/10">
                <h3 className="text-xl font-semibold mb-3 flex items-center">
                  <Zap className="h-5 w-5 mr-2 text-yellow-300" />
                  บูรณาการกับระบบของคุณ
                </h3>
                <p>เชื่อมต่อกับระบบภายในองค์กรของคุณได้ง่ายผ่าน REST API</p>
              </div>
            </div>
          </div>

          <ApiFeatureHighlight />
        </div>
      </section>

      {/* Footer */}
      <Footer />
    </div>
  );
}