import { Express, Request, Response, Router } from "express";
import { storage } from "../storage";
import { isAdmin } from "../auth";
import fs from 'fs';
import path from 'path';

const router = Router();

// API endpoint สำหรับดึงรายการตรวจสอบสลิปทั้งหมด (สำหรับแอดมิน) หรือเฉพาะของผู้ใช้ (สำหรับผู้ใช้ทั่วไป)
// รองรับการกรองข้อมูลด้วย query parameters
router.get('/verifications', async (req: Request, res: Response) => {
  try {
    // ตรวจสอบสถานะการล็อกอิน
    if (!req.isAuthenticated()) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const isUserAdmin = isAdmin(req);
    
    // ถ้าเป็นแอดมินจะดึงข้อมูลทั้งหมด ถ้าเป็นผู้ใช้ทั่วไปจะดึงเฉพาะข้อมูลของตัวเอง
    let verifications;
    if (isUserAdmin) {
      verifications = await storage.listAllSlipVerifications();
    } else {
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({ error: 'Cannot identify user' });
      }
      verifications = await storage.listUserSlipVerifications(userId);
    }
    
    // ตรวจสอบว่ามีพารามิเตอร์การค้นหาหรือไม่
    const {
      search,
      bank,
      status,
      startDate,
      endDate,
      minAmount,
      maxAmount,
      source
    } = req.query;
    
    // กรองข้อมูลตามพารามิเตอร์การค้นหา
    let results = verifications;
    
    // กรองตามคำค้นหา
    if (search && typeof search === 'string') {
      const searchLower = search.toLowerCase();
      results = results.filter(v =>
        (v.transactionRef && v.transactionRef.toLowerCase().includes(searchLower)) ||
        (v.sender && v.sender.toLowerCase().includes(searchLower)) ||
        (v.receiver && v.receiver.toLowerCase().includes(searchLower))
      );
    }
    
    // กรองตามธนาคาร
    if (bank && typeof bank === 'string' && bank !== 'all') {
      results = results.filter(v => v.bankName && v.bankName.toLowerCase().includes(bank.toLowerCase()));
    }
    
    // กรองตามสถานะ
    if (status && typeof status === 'string' && status !== 'all') {
      // สถานะใน URL parameter อาจเป็น 'error' แต่ในฐานข้อมูลเป็น 'failed'
      const statusMap: Record<string, string[]> = {
        'success': ['success'],
        'failed': ['failed', 'error'],
        'error': ['failed', 'error'],
        'pending': ['pending']
      };
      
      const statusValues = statusMap[status.toLowerCase()] || [status.toLowerCase()];
      console.log('กรองตามสถานะ:', status, 'ค่าที่ยอมรับ:', statusValues);
      
      results = results.filter(v => {
        if (!v.status) return false;
        return statusValues.includes(v.status.toLowerCase());
      });
    }
    
    // กรองตามวันที่
    if (startDate && typeof startDate === 'string') {
      const from = new Date(startDate);
      results = results.filter(v => {
        if (!v.createdAt) return false;
        const date = new Date(v.createdAt);
        return date >= from;
      });
    }
    
    if (endDate && typeof endDate === 'string') {
      const to = new Date(endDate);
      // เพิ่ม 1 วันเพื่อให้รวมถึงวันสุดท้ายด้วย
      to.setDate(to.getDate() + 1);
      results = results.filter(v => {
        if (!v.createdAt) return false;
        const date = new Date(v.createdAt);
        return date < to;
      });
    }
    
    // กรองตามจำนวนเงิน
    if (minAmount !== undefined && minAmount !== null) {
      const min = parseFloat(minAmount as string);
      if (!isNaN(min)) {
        results = results.filter(v => (v.amount || 0) >= min);
      }
    }
    
    if (maxAmount !== undefined && maxAmount !== null) {
      const max = parseFloat(maxAmount as string);
      if (!isNaN(max)) {
        results = results.filter(v => (v.amount || 0) <= max);
      }
    }
    
    // กรองตามแหล่งที่มา
    if (source && typeof source === 'string' && source !== 'all') {
      results = results.filter(v => {
        return v.verificationSource && v.verificationSource.toLowerCase() === source.toLowerCase();
      });
    }
    
    // แสดงข้อมูลการกรองเพื่อการดีบัก
    console.log('Search filters:', { search, bank, status, startDate, endDate, minAmount, maxAmount, source });
    console.log('Results count:', results.length);
    
    // ตรวจสอบและแก้ไข URL ของรูปภาพสลิป
    results = results.map(verification => {
      // ถ้ามี imagePath แต่ไม่ได้เริ่มต้นด้วย http หรือ https
      if (verification.imagePath && !verification.imagePath.startsWith('http')) {
        // ตรวจสอบว่าไฟล์มีอยู่จริงหรือไม่
        const fullImagePath = path.join('public', 'uploads', 'slips', verification.imagePath);
        try {
          if (fs.existsSync(fullImagePath)) {
            // กรณีไฟล์มีอยู่จริง กำหนด path ที่ถูกต้อง
            verification.imagePath = `/uploads/slips/${verification.imagePath}`;
          } else {
            // กรณีไฟล์ไม่มีอยู่ ให้ใช้ path สำรอง
            verification.imagePath = null;
          }
        } catch (err) {
          console.error('Error checking image path:', err);
          verification.imagePath = null;
        }
      }
      return verification;
    });
    
    res.json(results);
  } catch (error) {
    console.error('Error fetching verifications:', error);
    res.status(500).json({ error: 'Failed to fetch verifications' });
  }
});

// API endpoint สำหรับค้นหาขั้นสูง
router.post('/verifications/search', async (req: Request, res: Response) => {
  try {
    // ตรวจสอบสถานะการล็อกอิน
    if (!req.isAuthenticated()) {
      return res.status(401).json({ error: 'Unauthorized' });
    }
    
    const isUserAdmin = isAdmin(req);
    
    // ดึงข้อมูลทั้งหมดก่อน (ในกรณีที่เป็นแอดมิน) หรือข้อมูลเฉพาะผู้ใช้ (ในกรณีที่เป็นผู้ใช้ทั่วไป)
    let verifications;
    if (isUserAdmin) {
      verifications = await storage.listAllSlipVerifications();
    } else {
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({ error: 'Cannot identify user' });
      }
      verifications = await storage.listUserSlipVerifications(userId);
    }
    
    // ดึงพารามิเตอร์การค้นหา
    const {
      search,
      bankFilter,
      statusFilter,
      fromDate,
      toDate,
      amountMin,
      amountMax,
      sourceFilter
    } = req.body;
    
    // กรองข้อมูลตามพารามิเตอร์การค้นหา
    let results = verifications;
    
    // กรองตามคำค้นหา
    if (search) {
      const searchLower = search.toLowerCase();
      results = results.filter(v =>
        (v.transactionRef && v.transactionRef.toLowerCase().includes(searchLower)) ||
        (v.sender && v.sender.toLowerCase().includes(searchLower)) ||
        (v.receiver && v.receiver.toLowerCase().includes(searchLower))
      );
    }
    
    // กรองตามธนาคาร
    if (bankFilter && bankFilter !== 'all') {
      results = results.filter(v => v.bankName === bankFilter);
    }
    
    // กรองตามสถานะ
    if (statusFilter && statusFilter !== 'all') {
      // สถานะใน parameter อาจเป็น 'error' แต่ในฐานข้อมูลเป็น 'failed'
      const statusMap: Record<string, string[]> = {
        'success': ['success'],
        'failed': ['failed', 'error'],
        'error': ['failed', 'error'],
        'pending': ['pending']
      };
      
      const statusValues = statusMap[statusFilter.toLowerCase()] || [statusFilter.toLowerCase()];
      console.log('กรองตามสถานะ (POST):', statusFilter, 'ค่าที่ยอมรับ:', statusValues);
      
      results = results.filter(v => {
        if (!v.status) return false;
        return statusValues.includes(v.status.toLowerCase());
      });
    }
    
    // กรองตามวันที่
    if (fromDate) {
      const from = new Date(fromDate);
      results = results.filter(v => {
        if (!v.createdAt) return false;
        const date = new Date(v.createdAt);
        return date >= from;
      });
    }
    
    if (toDate) {
      const to = new Date(toDate);
      // เพิ่ม 1 วันเพื่อให้รวมถึงวันสุดท้ายด้วย
      to.setDate(to.getDate() + 1);
      results = results.filter(v => {
        if (!v.createdAt) return false;
        const date = new Date(v.createdAt);
        return date < to;
      });
    }
    
    // กรองตามจำนวนเงิน
    if (amountMin !== undefined && amountMin !== null) {
      const min = parseFloat(amountMin);
      results = results.filter(v => (v.amount || 0) >= min);
    }
    
    if (amountMax !== undefined && amountMax !== null) {
      const max = parseFloat(amountMax);
      results = results.filter(v => (v.amount || 0) <= max);
    }
    
    // กรองตามแหล่งที่มา
    if (sourceFilter && sourceFilter !== 'all') {
      results = results.filter(v => {
        // ตรวจสอบ null หรือ undefined
        return v.verificationSource && v.verificationSource.toLowerCase() === sourceFilter.toLowerCase();
      });
    }
    
    res.json(results);
  } catch (error) {
    console.error('Error searching verifications:', error);
    res.status(500).json({ error: 'Failed to search verifications' });
  }
});

// API endpoint สำหรับสถิติ
router.get('/verifications/stats', async (req: Request, res: Response) => {
  try {
    // ตรวจสอบสถานะการล็อกอิน
    if (!req.isAuthenticated()) {
      return res.status(401).json({ error: 'Unauthorized' });
    }
    
    const isUserAdmin = isAdmin(req);
    
    // ดึงข้อมูลทั้งหมดก่อน (ในกรณีที่เป็นแอดมิน) หรือข้อมูลเฉพาะผู้ใช้ (ในกรณีที่เป็นผู้ใช้ทั่วไป)
    let verifications;
    if (isUserAdmin) {
      verifications = await storage.listAllSlipVerifications();
    } else {
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({ error: 'Cannot identify user' });
      }
      verifications = await storage.listUserSlipVerifications(userId);
    }
    
    // คำนวณสถิติ
    const total = verifications.length;
    
    // คำนวณยอดเงินรวม
    const totalAmount = verifications.reduce((sum, v) => sum + (v.amount || 0), 0);
    
    // คำนวณยอดเงินเฉลี่ย
    const averageAmount = total > 0 ? totalAmount / total : 0;
    
    // จำนวนรายการตามสถานะ
    const successCount = verifications.filter(v => v.status?.toLowerCase() === 'success').length;
    const errorCount = verifications.filter(v => ['error', 'failed'].includes(v.status?.toLowerCase() || '')).length;
    const pendingCount = verifications.filter(v => v.status?.toLowerCase() === 'pending').length;
    
    // คำนวณอัตราความสำเร็จ
    const successRate = total > 0 ? (successCount / total) * 100 : 0;
    
    // จัดกลุ่มตามธนาคาร
    const bankGroups: Record<string, number> = {};
    verifications.forEach(v => {
      const bank = v.bankName || 'unknown';
      bankGroups[bank] = (bankGroups[bank] || 0) + 1;
    });
    
    // จัดกลุ่มตามวันที่
    const dateGroups: Record<string, number> = {};
    verifications.forEach(v => {
      if (!v.createdAt) return;
      const date = new Date(v.createdAt);
      const dateStr = date.toISOString().split('T')[0]; // แปลงเป็น YYYY-MM-DD
      dateGroups[dateStr] = (dateGroups[dateStr] || 0) + 1;
    });
    
    // จัดกลุ่มตามช่วงเวลาของวัน
    const hourlyGroups: Record<string, number> = {};
    verifications.forEach(v => {
      if (!v.createdAt) return;
      const date = new Date(v.createdAt);
      const hour = date.getHours(); // 0-23
      const hourStr = `${hour < 10 ? '0' : ''}${hour}:00`;
      hourlyGroups[hourStr] = (hourlyGroups[hourStr] || 0) + 1;
    });
    
    // หาธนาคารที่มีการโอนมากที่สุด
    let topBank: { name: string; count: number } | null = null;
    Object.entries(bankGroups).forEach(([name, count]) => {
      if (!topBank || count > topBank.count) {
        topBank = { name, count };
      }
    });
    
    // คำนวณมูลค่ารวมแยกตามธนาคาร
    const bankAmounts: Record<string, number> = {};
    verifications.forEach(v => {
      if (!v.bankName || !v.amount) return;
      const bank = v.bankName;
      bankAmounts[bank] = (bankAmounts[bank] || 0) + v.amount;
    });
    
    // หา 3 ช่วงเวลาที่มีการใช้งานมากที่สุด
    const mostActiveHours = Object.entries(hourlyGroups)
      .map(([hour, count]) => ({ hour, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 3);
    
    // ส่งข้อมูลสถิติกลับไป
    res.json({
      total,
      totalAmount,
      averageAmount,
      successCount,
      errorCount,
      pendingCount,
      successRate,
      bankGroups,
      dateGroups,
      hourlyGroups,
      topBank,
      bankAmounts,
      mostActiveHours
    });
  } catch (error) {
    console.error('Error calculating verification stats:', error);
    res.status(500).json({ error: 'Failed to calculate verification stats' });
  }
});

// API endpoint สำหรับดาวน์โหลดข้อมูลเป็น CSV
router.get('/verifications/download', async (req: Request, res: Response) => {
  try {
    // ตรวจสอบสถานะการล็อกอิน
    if (!req.isAuthenticated()) {
      return res.status(401).json({ error: 'Unauthorized' });
    }
    
    const isUserAdmin = isAdmin(req);
    
    // ดึงข้อมูลทั้งหมดก่อน (ในกรณีที่เป็นแอดมิน) หรือข้อมูลเฉพาะผู้ใช้ (ในกรณีที่เป็นผู้ใช้ทั่วไป)
    let verifications;
    if (isUserAdmin) {
      verifications = await storage.listAllSlipVerifications();
    } else {
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({ error: 'Cannot identify user' });
      }
      verifications = await storage.listUserSlipVerifications(userId);
    }
    
    // สร้างหัวตาราง CSV
    const headers = [
      "ID",
      "อ้างอิง",
      "ธนาคาร",
      "จำนวนเงิน",
      "ผู้ส่ง",
      "ผู้รับ",
      "วันที่ทำธุรกรรม",
      "สถานะ",
      "แหล่งที่มา",
      "วันที่ตรวจสอบ",
    ].join(",");
    
    // สร้างข้อมูลแต่ละแถว
    const rows = verifications.map((item) => {
      return [
        item.id,
        item.transactionRef || "",
        item.bankName || "",
        item.amount || 0,
        item.sender || "",
        item.receiver || "",
        item.transactionDate || "",
        item.status,
        item.verificationSource,
        item.createdAt,
      ]
        .map((value) => {
          // ทำให้แน่ใจว่าค่าเป็นสตริงและหากมีเครื่องหมาย , ให้ครอบด้วย "
          const strValue = String(value);
          return strValue.includes(",") ? `"${strValue}"` : strValue;
        })
        .join(",");
    });
    
    // รวมหัวตารางและข้อมูลทั้งหมด
    const csvContent = [headers, ...rows].join("\n");
    
    // ตั้งค่า header สำหรับการดาวน์โหลด
    res.setHeader('Content-Type', 'text/csv; charset=utf-8');
    res.setHeader('Content-Disposition', 'attachment; filename=verifications.csv');
    
    // ส่งข้อมูล CSV กลับไป
    res.send(csvContent);
  } catch (error) {
    console.error('Error generating CSV:', error);
    res.status(500).json({ error: 'Failed to generate CSV file' });
  }
});

// API endpoint สำหรับตรวจสอบสถานะการล็อกอิน
router.get('/auth-status', (req: Request, res: Response) => {
  res.json({
    isAuthenticated: req.isAuthenticated(),
    isAdmin: req.isAuthenticated() ? isAdmin(req) : false,
    user: req.user ? {
      id: req.user.id,
      username: req.user.username,
      email: req.user.email,
    } : null,
  });
});

// ฟังก์ชันสำหรับเพิ่ม routes เข้าไปใน Express app
export function setupAdvancedSearchRoutes(app: Express) {
  app.use('/api', router);
}

export default setupAdvancedSearchRoutes;