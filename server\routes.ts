import type { Express, Request, Response } from "express";
import { createServer, type Server } from "http";
import { setupAuth } from "./auth";
import { setupSlipRoutes } from "./api/slip";
import { setupAdminRoutes } from "./api/admin";
import { setupAdminUserDetailsRoutes } from "./api/admin-user-details";
import { setupPackageRoutes } from "./api/packages";
import { setupCouponRoutes } from "./api/coupon";
import { setupTopUpRoutes } from "./api/topup";
import { setupPaymentRoutes } from "./api/payment";
import { setupApiKeyRoutes } from "./api/api-key";
import { setupDirectApiKeyRoutes } from "./api/direct-api-key";
import { setupSlipApiRoutes } from "./api/slip-api";
import { setupResponseTemplatesRoutes } from "./api/response-templates";
import setupAdvancedSearchRoutes from "./api/advanced-search";
import setupAdvancedSearchFixRoutes from "./api/advanced-search-fix";
import userProfileRoutes from "./api/user-profile";
import notificationsRoutes from "./api/notifications-routes";
import emailSettingsRoutes from "./api/email-settings-routes";
import emailTemplatesRoutes from "./api/email-templates";
import fraudDetectionRoutes from "./api/fraud-detection-routes";
import reportsRoutes from "./api/reports-routes";
import webhookSettingsRoutes from "./api/webhook-settings";
import apiDocsRoutes from "./api/docs";
import { socialAuthApi } from "./api/social-auth-api";
import { authSettingsApi } from "./api/auth-settings-api";
import { analyzeUserData } from "./api/simple-analytics";
import { clearExpiredSessions, clearAllSessions, clearUserSession } from "./api/session-cleanup";
import { registerSessionMonitorRoutes } from "./api/session-monitor";
import { registerAuthAPI } from "./api/auth-api";
import { registerVerificationAPI } from "./api/verification-api";
import userSettingsRoutes from "./api/user-settings";
import { passwordResetRoutes } from "./api/password-reset";
import registerAchievementRoutes from "./api/achievements";
// นำเข้า middleware ตรวจสอบการยืนยันตัวตน
import { requireVerified, requireAdmin } from "./middleware/verification-middleware";
import multer from "multer";
import fs from "fs";
import cors from "cors";
// ใช้ Server-Sent Events แทน WebSocket และ Socket.IO

// กำหนดพาธสำหรับเก็บไฟล์อัปโหลด
import path from "path";

// สร้างโฟลเดอร์ถ้ายังไม่มี
const permanentUploadDir = path.join('public', 'uploads', 'slips');
if (!fs.existsSync(permanentUploadDir)) {
  fs.mkdirSync(permanentUploadDir, { recursive: true });
}

// สร้าง Storage สำหรับอัปโหลดไฟล์
const storage = multer.memoryStorage(); // ยังใช้ memory storage สำหรับการประมวลผลเบื้องต้น
export const upload = multer({
  storage,
  limits: { fileSize: 50 * 1024 * 1024 } // เพิ่มขนาดไฟล์เป็น 50MB เพื่อรองรับรูป QR code ขนาดใหญ่
});

export async function registerRoutes(app: Express): Promise<Server> {

  // ตั้งค่าเส้นทาง API ต่างๆ
  setupAuth(app);
  setupSlipRoutes(app);
  setupAdminRoutes(app);
  setupAdminUserDetailsRoutes(app);
  setupPackageRoutes(app);
  setupCouponRoutes(app);
  setupTopUpRoutes(app);
  setupPaymentRoutes(app);
  setupApiKeyRoutes(app);
  setupDirectApiKeyRoutes(app);
  setupSlipApiRoutes(app);
  setupResponseTemplatesRoutes(app);
  setupAdvancedSearchRoutes(app);
  setupAdvancedSearchFixRoutes(app);

  // ลงทะเบียนระบบติดตามเซสชันแบบเรียลไทม์
  registerSessionMonitorRoutes(app);

  // ลงทะเบียนระบบ authentication หลายช่องทาง
  registerAuthAPI(app);

  // ลงทะเบียน API สำหรับการยืนยันตัวตนด้วยอีเมล และ OTP
  registerVerificationAPI(app);

  // ลงทะเบียน API สำหรับการล็อกอินผ่านโซเชียลมีเดีย
  app.use('/api/social', socialAuthApi);

  // ลงทะเบียน API สำหรับการตั้งค่าการยืนยันตัวตนและความปลอดภัย
  app.use('/api/admin', authSettingsApi);

  // ลงทะเบียน API สำหรับการตั้งค่า Social Login
  app.use('/api/auth', (await import('./api/auth-settings-social-api')).authSettingsSocialApi);

  // ลงทะเบียน API สำหรับการตั้งค่าผู้ใช้
  app.use('/api/user-settings', (await import('./api/user-settings')).default);

  // ลงทะเบียนฟังก์ชันค้นหาขั้นสูงสำหรับวิเคราะห์ข้อมูลของผู้ใช้เท่านั้น
  const { setupAdvancedUserAnalyticsRoutes } = await import('./api/advanced-user-analytics');
  setupAdvancedUserAnalyticsRoutes(app);

  // API สำหรับการค้นหาขั้นสูงแบบง่าย
  app.post('/api/advanced-search/analyze-simple', async (req, res) => {
    console.log("API ได้รับคำขอค้นหาขั้นสูง:", req.body);

    // ตรวจสอบว่าผู้ใช้เข้าสู่ระบบแล้ว
    if (!req.isAuthenticated()) {
      return res.status(401).json({
        success: false,
        message: "กรุณาเข้าสู่ระบบ",
        code: "NOT_AUTHENTICATED"
      });
    }

    return analyzeUserData(req, res);
  });

  // ลงทะเบียนเส้นทางโปรไฟล์ผู้ใช้
  app.use('/api/user', userProfileRoutes);

  // ลงทะเบียนเส้นทางการตั้งค่าผู้ใช้ (เวอร์ชันเก่า)
  app.use('/api/user-settings-old', userSettingsRoutes);

  // ลงทะเบียนเส้นทางใหม่
  app.use('/api/notifications', notificationsRoutes);
  app.use('/api/email', emailSettingsRoutes);
  app.use('/api/admin/email', (await import('./api/admin/email-settings-routes')).default);
  app.use('/api', emailTemplatesRoutes);
  app.use('/api/fraud-detection', fraudDetectionRoutes);
  app.use('/api/reports', reportsRoutes);
  app.use('/api/webhooks', webhookSettingsRoutes);

  // ลงทะเบียนเส้นทางสำหรับการรีเซ็ตรหัสผ่าน
  app.use('/api/password-reset', passwordResetRoutes);

  // ลงทะเบียนเส้นทางสำหรับระบบ Gamified Package Selection
  registerAchievementRoutes(app);

  // ลงทะเบียนเส้นทาง API สำหรับ Analytics
  app.use('/api/analytics', (await import('./api/analytics-routes')).default);

  // ลงทะเบียนเส้นทาง API สำหรับการสำรองข้อมูล
  app.use('/api/backup', (await import('./api/backup-routes')).default);

  // ลงทะเบียนเส้นทาง API เอกสาร
  app.use('/api/docs', apiDocsRoutes);

  // ตั้งค่า cron job สำหรับรีเซ็ตโควต้าแพ็กเกจทุกวัน
  setInterval(async () => {
    try {
      const { storage } = await import('./storage');
      await storage.checkAndResetPackageQuotas();
      console.log('Checked package quotas for reset at', new Date().toISOString());
    } catch (error) {
      console.error('Error checking package quotas:', error);
    }
  }, 24 * 60 * 60 * 1000); // ตรวจสอบทุก 24 ชั่วโมง

  // ตั้งค่า cron job สำหรับลบ session ที่หมดอายุทุกชั่วโมง
  setInterval(async () => {
    try {
      const result = await clearExpiredSessions();
      console.log('Cleared expired sessions at', new Date().toISOString(), 'Result:', result);
    } catch (error) {
      console.error('Error clearing expired sessions:', error);
    }
  }, 60 * 60 * 1000); // ตรวจสอบทุก 1 ชั่วโมง

  // ตั้งค่า cron job สำหรับตรวจสอบและสร้างไฟล์สำรองข้อมูลอัตโนมัติทุก 5 นาที
  setInterval(async () => {
    try {
      const { backupService } = await import('./backup-service');
      await backupService.checkAndCreateBackup();
    } catch (error) {
      console.error('Error checking and creating backup:', error);
    }
  }, 5 * 60 * 1000); // ตรวจสอบทุก 5 นาที

  // เส้นทางสำหรับตรวจสอบว่า API เซิร์ฟเวอร์ทำงานอยู่หรือไม่
  app.get("/api/health", (req, res) => {
    res.status(200).json({ status: "ok" });
  });

  // เส้นทาง API สำหรับจัดการ Session
  app.get("/api/admin/sessions/clear-expired", async (req, res) => {
    if (!req.isAuthenticated() || !req.user?.isAdmin) {
      return res.status(403).json({ message: "สิทธิ์ไม่ถูกต้อง กรุณาล็อกอินด้วยบัญชีแอดมิน" });
    }

    try {
      const result = await clearExpiredSessions();
      res.status(200).json({ message: "ลบเซสชันที่หมดอายุแล้วเรียบร้อย", result });
    } catch (error) {
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการลบเซสชัน", error: String(error) });
    }
  });

  app.post("/api/admin/sessions/clear-all", async (req, res) => {
    if (!req.isAuthenticated() || !req.user?.isAdmin) {
      return res.status(403).json({ message: "สิทธิ์ไม่ถูกต้อง กรุณาล็อกอินด้วยบัญชีแอดมิน" });
    }

    try {
      const result = await clearAllSessions();
      // ลบเซสชันของผู้ใช้ปัจจุบันด้วย เนื่องจากเป็นแอดมิน
      req.session.destroy((err) => {
        if (err) {
          console.error("เกิดข้อผิดพลาดในการลบเซสชันของแอดมิน:", err);
        }
        res.clearCookie('slipkuy.sid', { path: '/' });
        res.status(200).json({ message: "ลบเซสชันทั้งหมดเรียบร้อย กรุณาล็อกอินใหม่", result });
      });
    } catch (error) {
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการลบเซสชัน", error: String(error) });
    }
  });

  app.post("/api/admin/sessions/clear-user/:userId", async (req, res) => {
    if (!req.isAuthenticated() || !req.user?.isAdmin) {
      return res.status(403).json({ message: "สิทธิ์ไม่ถูกต้อง กรุณาล็อกอินด้วยบัญชีแอดมิน" });
    }

    const userId = parseInt(req.params.userId);
    if (isNaN(userId)) {
      return res.status(400).json({ message: "รหัสผู้ใช้ไม่ถูกต้อง" });
    }

    try {
      const result = await clearUserSession(userId);
      res.status(200).json({ message: `ลบเซสชันของผู้ใช้ ID ${userId} เรียบร้อย`, result });
    } catch (error) {
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการลบเซสชัน", error: String(error) });
    }
  });

  // API สำหรับระบบภายนอกเรียกใช้ผ่าน API Key (เส้นทางเก่า - แนะนำให้ใช้ /api/v1/verify-slip แทน)
  app.post('/api/verify/slip', upload.single('slip_image'), async (req: Request, res: Response) => {
    // แสดงคำเตือนในบันทึก
    console.log('⚠️ มีการใช้งาน API เส้นทางเก่า /api/verify/slip แนะนำให้ใช้ /api/v1/verify-slip แทน');
    try {
      // ตรวจสอบว่ามีรูปภาพถูกอัปโหลดหรือไม่
      if (!req.file) {
        return res.status(400).json({
          code: '400001',
          message: 'กรุณาอัปโหลดรูปภาพสลิปธนาคาร',
          data: null
        });
      }

      // ดึง API key จากส่วนหัวของคำขอ
      const apiKey = req.header('X-API-Key');
      if (!apiKey) {
        return res.status(400).json({
          code: '400002',
          message: 'ไม่พบ API Key ในส่วนหัวของคำขอ',
          data: null
        });
      }

      // ค้นหา API key ในฐานข้อมูล
      const { storage } = await import('./storage');
      const foundApiKey = await storage.getApiKeyByKey(apiKey);
      if (!foundApiKey) {
        return res.status(401).json({
          code: '401002',
          message: 'API Key ไม่ถูกต้อง',
          data: null
        });
      }

      // ตรวจสอบแพ็กเกจของผู้ใช้และการหักเครดิต
      const userId = foundApiKey.userId;
      const userPackage = await storage.getUserActivePackage(userId);
      if (!userPackage) {
        return res.status(403).json({
          code: '403001',
          message: 'ไม่พบแพ็กเกจที่ใช้งานได้ กรุณาสมัครแพ็กเกจก่อนใช้งาน',
          data: null
        });
      }

      // ตรวจสอบว่าเกินโควต้าหรือไม่ และหักเครดิตถ้าจำเป็น
      let overQuota = false;
      let pricePerRequest = 0;

      if (userPackage.requestsUsed >= userPackage.package.requestsLimit) {
        overQuota = true;

        // ใช้ค่า creditPerVerification จากฐานข้อมูล หากไม่มีให้คำนวณใหม่
        if (userPackage.package.creditPerVerification !== null &&
            userPackage.package.creditPerVerification !== undefined) {
          pricePerRequest = userPackage.package.creditPerVerification;
        } else {
          // คำนวณราคาต่อครั้ง = ราคาแพ็กเกจ / จำนวนครั้งที่ใช้งานได้
          pricePerRequest = userPackage.package.price / userPackage.package.requestsLimit;

          // บันทึกค่านี้กลับเข้าฐานข้อมูล
          await storage.updatePackage(userPackage.package.id, {
            creditPerVerification: pricePerRequest,
            updatedAt: new Date()
          });
        }

        // ดึงข้อมูลเครดิตของผู้ใช้
        const userCredit = await storage.getUserCredit(userId);

        // ถ้าเครดิตไม่พอ
        if (userCredit < pricePerRequest) {
          return res.status(403).json({
            code: '403002',
            message: `คุณได้ใช้งานครบตามจำนวนที่กำหนดในแพ็กเกจแล้ว และมีเครดิตไม่เพียงพอสำหรับการตรวจสอบเพิ่มเติม (${pricePerRequest.toFixed(2)} บาท/ครั้ง) กรุณาเติมเครดิตหรืออัปเกรดแพ็กเกจ`,
            data: null
          });
        }
      }

      // ใช้ buffer จาก multer ซึ่งเก็บไฟล์ไว้ในหน่วยความจำ
      const fileBuffer = req.file.buffer;

      // ตัวแปรสำหรับเก็บข้อมูล QR code
      let qrData: string | null = null;

      // ตรวจสอบว่าเป็นรูปสลิปธนาคารหรือไม่
      const { analyzeBankSlip } = await import('./analyzeQRCode');
      const validationResult = await analyzeBankSlip(fileBuffer);

      if (!validationResult.isValid) {
        return res.status(400).json({
          code: '400004',
          message: `ไม่ใช่รูปสลิปธนาคาร: ${validationResult.reason}`,
          data: null
        });
      }

      // ตรวจสอบว่ามี QR Code ในรูปภาพหรือไม่
      const { detectQRCode } = await import('./qr-detector-new');
      console.log('🔍 ตรวจสอบ QR code ในรูปภาพก่อนส่งไปยัง API');
      const qrResult = await detectQRCode(fileBuffer);

      if (qrResult.hasQRCode) {
        // เก็บข้อมูล QR code
        qrData = qrResult.qrData || null;
        console.log(`✅ พบ QR code ในรูปภาพ: ${qrData ? qrData.substring(0, 30) + '...' : 'พบแต่อ่านข้อมูลไม่ได้'}`);
      } else {
        console.log('❌ ไม่พบ QR code ในรูปภาพ');
      }

      // บันทึกรูปภาพไว้ถาวร
      let imagePath = null;
      try {
        // สร้างชื่อไฟล์แบบไม่ซ้ำกัน
        const timestamp = Date.now();
        const filename = `slip_image-${userId}-${timestamp}.jpg`;
        const filepath = path.join(permanentUploadDir, filename);

        // บันทึกไฟล์ในโฟลเดอร์ถาวร
        fs.writeFileSync(filepath, fileBuffer);

        // กำหนดพาธสัมพัทธ์สำหรับบันทึกในฐานข้อมูล
        imagePath = `/uploads/slips/${filename}`;

        console.log('Slip image saved permanently at:', imagePath);
        console.log('Absolute path:', filepath);
      } catch (error) {
        console.error('Error saving slip image from web API:', error);
      }

      // ตรวจสอบสลิป
      const { slipService } = await import('./slip-service');
      const result = await slipService.verifySlip(foundApiKey.userId, fileBuffer, req.file.originalname);

      // ผลการตรวจสอบสำเร็จ
      if (result.status === 200 && result.data) {
        // อัปเดตข้อมูลการใช้งานและหักเครดิต
        if (overQuota) {
          // ถ้าเกินโควต้า ให้หักเครดิต
          await storage.addUserCredit(userId, -pricePerRequest);
          console.log(`หักเครดิตผู้ใช้ ${userId} จำนวน ${pricePerRequest} บาท เนื่องจากเกินโควต้า`);
        } else {
          // ถ้ายังไม่เกินโควต้า เพิ่มจำนวนการใช้งาน
          await storage.incrementRequestsUsed(userPackage.id);
          console.log(`เพิ่มจำนวนการใช้งานแพ็กเกจ ${userPackage.id} ของผู้ใช้ ${userId}`);
        }

        // เพิ่มจำนวนการใช้งาน API Key และบันทึกประวัติการใช้งาน
        try {
          await storage.updateApiKeyUsage(foundApiKey.id);

          // บันทึก API Key Log
          const startTime = Date.now(); // จับเวลาการประมวลผล
          await storage.createApiLog({
            apiKeyId: foundApiKey.id,
            requestType: 'verify_slip',
            requestData: {
              method: 'POST',
              fileSize: req.file?.size || 0,
              filename: req.file?.originalname || '',
              userAgent: req.get('user-agent') || ''
            },
            responseStatus: 'success',
            responseData: {
              status: result.status,
              transRef: result.data?.transRef || '',
              amount: result.data?.amount?.amount || 0,
              success: true
            },
            processingTime: Date.now() - startTime, // เวลาที่ใช้ในการประมวลผล (มิลลิวินาที)
            ipAddress: req.ip || req.socket.remoteAddress || '',
          });

          console.log(`บันทึกประวัติการใช้งาน API Key ${foundApiKey.id}`);
        } catch (error) {
          console.error('Error logging API key usage:', error);
        }

        // สร้างบันทึกการตรวจสอบในฐานข้อมูล
        try {
          const verificationRecord = await storage.createSlipVerification({
            userId: userId,
            status: 'success',
            verificationSource: 'api', // ระบุแหล่งที่มาเป็น API
            responseData: JSON.stringify(result),
            transactionRef: result.data?.transRef || '',
            bankName: result.data?.sender?.bank?.name || '',
            amount: result.data?.amount?.amount || 0,
            sender: result.data?.sender?.account?.name?.th || result.data?.sender?.account?.name?.en || '',
            receiver: result.data?.receiver?.account?.name?.th || result.data?.receiver?.account?.name?.en || '',
            transactionDate: result.data?.date ? new Date(result.data.date) : new Date(),
            apiKeyId: foundApiKey.id, // เพิ่ม API Key ID เพื่อให้สามารถติดตามได้ว่าใช้ API Key ใด
            imagePath: imagePath, // เพิ่มพาธของรูปภาพ
            qrData: qrData // เพิ่มข้อมูล QR code
          });

          console.log(`สร้างบันทึกการตรวจสอบ ID: ${verificationRecord.id}`);
        } catch (error) {
          console.error('Error creating verification record:', error);
        }

        // ส่งผลลัพธ์กลับไปยังผู้ใช้
        return res.status(200).json({
          code: '200000',
          message: 'ตรวจสอบสลิปสำเร็จ',
          data: {
            referenceId: String(Date.now()),
            decode: result.data.payload || '',
            transRef: result.data.transRef || '',
            dateTime: result.data.date || new Date().toISOString(),
            amount: result.data.amount?.amount || 0,
            ref1: result.data.ref1 || null,
            ref2: result.data.ref2 || null,
            ref3: result.data.ref3 || null,
            note: "แนะนำให้ใช้ API เวอร์ชันใหม่ที่ /api/v1/verify-slip แทน เส้นทางนี้อาจถูกยกเลิกในอนาคต",
            receiver: {
              account: {
                name: result.data.receiver?.account?.name?.th || result.data.receiver?.account?.name?.en || '',
                number: result.data.receiver?.account?.bank?.account || '',
                bank: {
                  id: result.data.receiver?.bank?.id || '',
                  name: result.data.receiver?.bank?.name || '',
                }
              }
            },
            sender: {
              account: {
                name: result.data.sender?.account?.name?.th || result.data.sender?.account?.name?.en || '',
                number: result.data.sender?.account?.bank?.account || '',
                bank: {
                  id: result.data.sender?.bank?.id || '',
                  name: result.data.sender?.bank?.name || '',
                }
              }
            }
          }
        });
      } else {
        return res.status(result.status).json({
          code: String(result.status) + '000',
          message: result.message || 'เกิดข้อผิดพลาดในการตรวจสอบสลิป',
          data: null
        });
      }
    } catch (error) {
      console.error('Error in /api/verify/slip:', error);
      return res.status(500).json({
        code: '500000',
        message: 'เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์',
        data: null
      });
    }
    // ไม่ต้องลบไฟล์ชั่วคราวเพราะเราใช้ memoryStorage ซึ่งเก็บไฟล์ในหน่วยความจำ
  });

  // ลบ API สำหรับทดสอบภายในระบบเพื่อไม่ให้ทดสอบโดยไม่หักเครดิต
  // จากนี้ไป ทุกการทดสอบจะต้องใช้ API จริงซึ่งจะหักเครดิตตามจริง

  // เพิ่ม Server-Sent Events (SSE) endpoint สำหรับ real-time dashboard
  app.get('/api/sse/dashboard-data', (req: Request, res: Response) => {
    // ตั้งค่า headers ที่จำเป็นสำหรับ SSE
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.flushHeaders(); // ส่ง headers ทันที

    // ดึงการตั้งค่า polling interval จาก DB
    let pollingInterval = 3000; // default 3 วินาที
    (async () => {
      try {
        const { storage } = await import('./storage');
        const pollingIntervalSetting = await storage.getSystemSetting('polling_interval');
        if (pollingIntervalSetting && pollingIntervalSetting.value) {
          pollingInterval = parseInt(pollingIntervalSetting.value, 10);
        }
      } catch (error) {
        console.error('Error getting polling interval setting:', error);
      }
    })();

    // ฟังก์ชันสำหรับส่งข้อมูลไปยัง client
    const sendData = async () => {
      try {
        if (req.isAuthenticated()) {
          const { storage } = await import('./storage');
          const userId = req.user?.id;

          if (!userId) {
            return;
          }

          // ดึงข้อมูลที่จำเป็นสำหรับ dashboard
          const stats = await storage.getUserStats(userId);
          const credit = await storage.getUserCredit(userId);
          const recentVerifications = await storage.getRecentVerifications(userId, 5);

          // ดึงข้อมูลแพ็คเกจที่ใช้งานอยู่
          const activePackage = await storage.getUserActivePackage(userId);

          // สร้างข้อมูลสถิติพร้อมข้อมูลแพ็คเกจ
          const statsWithPackage = {
            ...stats,
            activePackage: activePackage || null,
            requestsRemaining: activePackage ?
              activePackage.package.requestsLimit - activePackage.requestsUsed : 0
          };

          // ส่งข้อมูลแยกแต่ละประเภท
          res.write(`event: stats\ndata: ${JSON.stringify(statsWithPackage)}\n\n`);
          res.write(`event: credit\ndata: ${JSON.stringify({ credit: credit || 0 })}\n\n`);
          res.write(`event: verifications\ndata: ${JSON.stringify(recentVerifications)}\n\n`);

          // ส่งข้อมูลแพ็คเกจแยกออกมาด้วย
          if (activePackage) {
            res.write(`event: activePackage\ndata: ${JSON.stringify(activePackage)}\n\n`);
          }
        }
      } catch (error) {
        console.error('Error sending SSE data:', error);
        res.write(`event: error\ndata: ${JSON.stringify({ error: 'Error fetching data' })}\n\n`);
      }
    };

    // เริ่มส่งข้อมูลทันทีเมื่อเชื่อมต่อ
    sendData();

    // ส่งข้อมูลตามรอบเวลาที่กำหนด
    const interval = setInterval(sendData, pollingInterval);

    // เมื่อ client ตัดการเชื่อมต่อ
    req.on('close', () => {
      clearInterval(interval);
      res.end();
    });
  });

  // SSE endpoint สำหรับ API Keys และ API Logs แบบเรียลไทม์
  app.get('/api/sse/api-keys-data', (req: Request, res: Response) => {
    // ตั้งค่า headers สำหรับ SSE
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.flushHeaders();

    // ใช้ polling interval เดียวกับ dashboard
    let pollingInterval = 3000;
    (async () => {
      try {
        const { storage } = await import('./storage');
        const setting = await storage.getSystemSetting('polling_interval');
        if (setting && setting.value) {
          pollingInterval = parseInt(setting.value, 10);
        }
      } catch (error) {
        console.error('Error getting polling interval for API keys:', error);
      }
    })();

    // ฟังก์ชันสำหรับส่งข้อมูล API Keys และประวัติการใช้งาน
    const sendApiKeysData = async () => {
      try {
        if (req.isAuthenticated()) {
          const { storage } = await import('./storage');
          const userId = req.user?.id;

          if (!userId) {
            return;
          }

          // ดึงข้อมูล API Keys ของผู้ใช้
          const userApiKeys = await storage.listUserApiKeys(userId);

          // ส่งข้อมูล API Keys
          res.write(`event: apiKeys\ndata: ${JSON.stringify(userApiKeys)}\n\n`);

          // ถ้ามีการเลือก API Key ให้ส่งประวัติการใช้งานด้วย
          const selectedApiKeyId = req.query.selectedApiKeyId;
          if (selectedApiKeyId && !isNaN(Number(selectedApiKeyId))) {
            const apiKeyId = Number(selectedApiKeyId);
            // ตรวจสอบว่า API Key นี้เป็นของผู้ใช้หรือไม่
            const isUserApiKey = userApiKeys.some((key: any) => key.id === apiKeyId);

            if (isUserApiKey) {
              const apiLogs = await storage.getApiKeyLogs(apiKeyId);
              res.write(`event: apiLogs\ndata: ${JSON.stringify(apiLogs)}\n\n`);
            }
          }
        }
      } catch (error) {
        console.error('Error sending API keys SSE data:', error);
        res.write(`event: error\ndata: ${JSON.stringify({ error: 'Error fetching API keys data' })}\n\n`);
      }
    };

    // เริ่มส่งข้อมูลทันที
    sendApiKeysData();

    // ส่งข้อมูลตามรอบเวลา
    const interval = setInterval(sendApiKeysData, pollingInterval);

    // เมื่อ client ตัดการเชื่อมต่อ
    req.on('close', () => {
      clearInterval(interval);
      res.end();
    });
  });

  // สร้าง HTTP server และคืนค่า
  const httpServer = createServer(app);

  console.log('HTTP server created with Server-Sent Events support')

  return httpServer;
}
