import { useState, useEffect } from "react";
import { EmailSettingsDialog } from "@/components/admin/email-settings-dialog";
import WebhookSettingsDialog from "@/components/admin/webhook-settings-dialog";
import { DatabaseBackupDialog } from "@/components/admin/database-backup-dialog";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Admin } from "@/components/layouts/admin-layout";
import { useAuth } from "@/hooks/use-auth";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, Settings, Check, AlertCircle, CreditCard, Trash2, Plus, Mail, Bell, Shield, ExternalLink, Link2, Database } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFieldArray } from "react-hook-form";
import { z } from "zod";

interface SystemSetting {
  id: number;
  key: string;
  value: string | null;
  valueJson: any | null;
  description: string | null;
  createdAt: string;
  updatedAt: string;
}

// สร้าง Schema สำหรับ payment account form
const bankAccountSchema = z.object({
  bankCode: z.string().min(3, { message: "กรุณาระบุรหัสธนาคาร" }),
  accountNumber: z.string().min(5, { message: "กรุณาระบุเลขบัญชี" }),
  accountName: z.string().min(2, { message: "กรุณาระบุชื่อบัญชี (ภาษาไทย)" }),
  accountNameEn: z.string().min(2, { message: "กรุณาระบุชื่อบัญชี (ภาษาอังกฤษ)" }),
  qrPaymentImage: z.string().optional(),
});

const promptpaySchema = z.string().min(10, { message: "กรุณาระบุเบอร์โทรศัพท์หรือเลขประจำตัวประชาชน" });

const paymentAccountFormSchema = z.object({
  bankAccounts: z.array(bankAccountSchema).min(1, {
    message: "กรุณาเพิ่มบัญชีธนาคารอย่างน้อย 1 บัญชี",
  }),
  promptpays: z.array(promptpaySchema).optional(),
});

type PaymentAccountFormValues = z.infer<typeof paymentAccountFormSchema>;

// ข้อมูลธนาคารไทย
const THAI_BANKS = [
  { code: "002", name: "ธนาคารกรุงเทพ" },
  { code: "004", name: "ธนาคารกสิกรไทย" },
  { code: "006", name: "ธนาคารกรุงไทย" },
  { code: "011", name: "ธนาคารทหารไทยธนชาต" },
  { code: "014", name: "ธนาคารไทยพาณิชย์" },
  { code: "025", name: "ธนาคารกรุงศรีอยุธยา" },
  { code: "030", name: "ธนาคารออมสิน" },
  { code: "034", name: "ธนาคารเกียรตินาคินภัทร" },
  { code: "067", name: "ธนาคารทิสโก้" },
  { code: "069", name: "ธนาคารซีไอเอ็มบี ไทย" },
  { code: "073", name: "ธนาคารยูโอบี" },
];

export default function SystemSettings() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("api");
  const [apiProvider, setApiProvider] = useState<string>("slip2go");
  const [loading, setLoading] = useState(false);
  const [emailSettingsOpen, setEmailSettingsOpen] = useState(false);
  const [testResult, setTestResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);

  // สร้าง state สำหรับข้อมูลบัญชีธนาคาร
  const [paymentAccounts, setPaymentAccounts] = useState<PaymentAccountFormValues>({
    bankAccounts: [{ bankCode: "", accountNumber: "", accountName: "", accountNameEn: "", qrPaymentImage: "" }],
    promptpays: [""],
  });

  // ตรวจสอบว่าผู้ใช้เป็นแอดมินหรือไม่
  useEffect(() => {
    if (user && user.role !== 'admin') {
      window.location.href = '/dashboard';
    }
  }, [user]);

  // ดึงการตั้งค่าจากฐานข้อมูล
  const { data: settings, isLoading, isError, refetch } = useQuery<SystemSetting[]>({
    queryKey: ['/api/admin/settings']
  });

  // เซตค่า API provider และบัญชีธนาคารจากการตั้งค่าที่ได้รับ
  useEffect(() => {
    if (settings) {
      // ตั้งค่า API provider
      const apiProviderSetting = settings.find(setting => setting.key === 'verification_api_provider');
      if (apiProviderSetting && apiProviderSetting.value) {
        setApiProvider(apiProviderSetting.value);
      }

      // ตั้งค่าบัญชีธนาคาร
      const paymentAccountsSetting = settings.find(setting => setting.key === 'payment_accounts');
      if (paymentAccountsSetting && paymentAccountsSetting.value) {
        try {
          const accounts = JSON.parse(paymentAccountsSetting.value);
          console.log("โหลดข้อมูลบัญชีธนาคารจากฐานข้อมูล:", accounts);

          // ตรวจสอบโครงสร้างข้อมูลที่ถูกต้อง
          if (accounts.bankAccounts && Array.isArray(accounts.bankAccounts)) {
            const updatedAccounts = {
              bankAccounts: accounts.bankAccounts,
              promptpays: Array.isArray(accounts.promptpays) ? accounts.promptpays : [""],
            };
            console.log("กำลังอัพเดตข้อมูลบัญชีธนาคาร:", updatedAccounts);
            setPaymentAccounts(updatedAccounts);
          }
        } catch (e) {
          console.error("Error parsing payment accounts:", e);
        }
      } else {
        console.log("ไม่พบข้อมูลบัญชีธนาคารในฐานข้อมูล หรือข้อมูลว่างเปล่า");
      }
    }
  }, [settings]);

  // Mutation สำหรับการอัพเดตการตั้งค่า
  const updateSettingMutation = useMutation({
    mutationFn: async ({ key, value }: { key: string, value: string }) => {
      const res = await apiRequest('POST', '/api/admin/settings', { key, value });
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/settings'] });
      toast({
        title: "บันทึกการตั้งค่าเรียบร้อย",
        description: "การตั้งค่าได้รับการอัพเดตแล้ว",
        variant: "default",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message || "ไม่สามารถบันทึกการตั้งค่าได้",
        variant: "destructive",
      });
    }
  });

  // Mutation สำหรับการทดสอบการเชื่อมต่อ API
  const testApiMutation = useMutation({
    mutationFn: async (provider: string) => {
      setLoading(true);
      try {
        // API นี้ควรจะสร้างในฝั่งเซิร์ฟเวอร์เพื่อทดสอบการเชื่อมต่อ
        const res = await apiRequest('POST', '/api/admin/test-api-connection', { provider });
        setLoading(false);
        return await res.json();
      } catch (error) {
        setLoading(false);
        throw error;
      }
    },
    onSuccess: (data) => {
      setTestResult({
        success: true,
        message: "เชื่อมต่อ API สำเร็จ! ระบบพร้อมให้บริการ"
      });
    },
    onError: (error: Error) => {
      setTestResult({
        success: false,
        message: `การเชื่อมต่อล้มเหลว: ${error.message}`
      });
    }
  });

  // บันทึกการตั้งค่า API provider
  const handleSaveApiProvider = () => {
    updateSettingMutation.mutate({ key: 'verification_api_provider', value: apiProvider });
  };

  // ทดสอบการเชื่อมต่อ API
  const handleTestApiConnection = () => {
    testApiMutation.mutate(apiProvider);
  };

  // form สำหรับบัญชีธนาคาร
  const form = useForm<PaymentAccountFormValues>({
    resolver: zodResolver(paymentAccountFormSchema),
    defaultValues: paymentAccounts,
  });

  // อัพเดตค่าเริ่มต้นของฟอร์มเมื่อได้รับข้อมูลจากฐานข้อมูล
  useEffect(() => {
    if (paymentAccounts.bankAccounts.length > 0 && paymentAccounts.bankAccounts[0].bankCode) {
      form.reset(paymentAccounts);
    }
  }, [paymentAccounts, form]);

  // บันทึกข้อมูลบัญชี
  const handleSavePaymentAccounts = (data: PaymentAccountFormValues) => {
    updateSettingMutation.mutate({
      key: 'payment_accounts',
      value: JSON.stringify(data)
    });
  };

  // สร้าง field array สำหรับบัญชีธนาคาร
  const bankAccountsField = useFieldArray({
    name: "bankAccounts",
    control: form.control,
  });

  // สร้าง field array สำหรับพร้อมเพย์
  const promptpaysField = useFieldArray({
    name: "promptpays" as any, // Type fix
    control: form.control,
  });

  // หากกำลังโหลดข้อมูล
  if (isLoading) {
    return (
      <Admin>
        <div className="flex flex-col space-y-8">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">การตั้งค่าระบบ</h1>
            <p className="text-muted-foreground">
              จัดการการตั้งค่าระบบทั้งหมด
            </p>
          </div>
          <div className="flex items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </div>
      </Admin>
    );
  }

  return (
    <Admin>
      <EmailSettingsDialog
        open={emailSettingsOpen}
        setOpen={setEmailSettingsOpen}
        onSave={() => {
          toast({
            title: "ตั้งค่าอีเมลเรียบร้อย",
            description: "การตั้งค่าอีเมลได้รับการอัพเดตแล้ว",
            variant: "default",
          });
        }}
      />
      <div className="flex flex-col space-y-8">
        <div>
          <h1 className="text-2xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-indigo-600">การตั้งค่าระบบ</h1>
          <div className="lightning-bar w-24 my-1"></div>
          <p className="text-purple-600/70">
            จัดการการตั้งค่าระบบทั้งหมด
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3 md:grid-cols-8 lg:w-[1000px] bg-purple-50 border border-purple-200">
            <TabsTrigger
              value="api"
              className="flex items-center data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-600 data-[state=active]:to-purple-700 data-[state=active]:text-white"
            >
              <Settings className="mr-2 h-4 w-4" />
              API Provider
            </TabsTrigger>
            <TabsTrigger
              value="payment"
              className="flex items-center data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-600 data-[state=active]:to-purple-700 data-[state=active]:text-white"
            >
              <CreditCard className="mr-2 h-4 w-4" />
              บัญชีธนาคาร
            </TabsTrigger>
            <TabsTrigger
              value="email"
              className="flex items-center data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-600 data-[state=active]:to-purple-700 data-[state=active]:text-white"
            >
              <Mail className="mr-2 h-4 w-4" />
              ตั้งค่าอีเมล
            </TabsTrigger>
            <TabsTrigger
              value="webhooks"
              className="flex items-center data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-600 data-[state=active]:to-purple-700 data-[state=active]:text-white"
            >
              <Link2 className="mr-2 h-4 w-4" />
              Webhooks
            </TabsTrigger>
            <TabsTrigger
              value="notifications"
              className="flex items-center data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-600 data-[state=active]:to-purple-700 data-[state=active]:text-white"
            >
              <Bell className="mr-2 h-4 w-4" />
              การแจ้งเตือน
            </TabsTrigger>
            <TabsTrigger
              value="database"
              className="flex items-center data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-600 data-[state=active]:to-purple-700 data-[state=active]:text-white"
            >
              <Database className="mr-2 h-4 w-4" />
              ฐานข้อมูล
            </TabsTrigger>
            <TabsTrigger
              value="system"
              className="flex items-center data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-600 data-[state=active]:to-purple-700 data-[state=active]:text-white"
            >
              <Settings className="mr-2 h-4 w-4" />
              ตั้งค่าระบบ
            </TabsTrigger>
            <TabsTrigger
              value="fraud"
              className="flex items-center data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-600 data-[state=active]:to-purple-700 data-[state=active]:text-white"
            >
              <Shield className="mr-2 h-4 w-4" />
              ตรวจจับการฉ้อโกง
            </TabsTrigger>
          </TabsList>

          <TabsContent value="api" className="mt-6">
            <Card className="border border-purple-200 shadow-md">
              <CardHeader className="border-b border-purple-100 bg-gradient-to-r from-purple-50 to-purple-100/50">
                <CardTitle className="text-purple-700 flex items-center">
                  <div className="p-1.5 rounded-full bg-gradient-to-r from-purple-100 to-purple-200 mr-2 flex items-center justify-center">
                    <Settings className="h-5 w-5 text-purple-600" />
                  </div>
                  ตั้งค่า API Provider
                </CardTitle>
                <div className="lightning-bar w-20 my-1"></div>
                <CardDescription className="text-purple-600/70">
                  เลือก API ที่ต้องการใช้ในการตรวจสอบสลิป
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="api-provider">เลือก API Provider</Label>
                    <Select
                      value={apiProvider}
                      onValueChange={setApiProvider}
                    >
                      <SelectTrigger id="api-provider">
                        <SelectValue placeholder="เลือก API Provider" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="easyslip">EasySlip</SelectItem>
                        <SelectItem value="slip2go">Slip2Go</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-sm text-muted-foreground mt-1">
                      API ที่เลือกจะถูกใช้สำหรับการตรวจสอบสลิปทั้งหมดในระบบ
                    </p>
                  </div>

                  {testResult && (
                    <Alert variant={testResult.success ? "default" : "destructive"}>
                      {testResult.success ? (
                        <Check className="h-4 w-4" />
                      ) : (
                        <AlertCircle className="h-4 w-4" />
                      )}
                      <AlertTitle>
                        {testResult.success ? "การเชื่อมต่อสำเร็จ" : "การเชื่อมต่อล้มเหลว"}
                      </AlertTitle>
                      <AlertDescription>
                        {testResult.message}
                      </AlertDescription>
                    </Alert>
                  )}

                  <div className="flex flex-col sm:flex-row gap-2 pt-4">
                    <Button
                      onClick={handleSaveApiProvider}
                      disabled={updateSettingMutation.isPending}
                      className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white"
                    >
                      {updateSettingMutation.isPending ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" /> กำลังบันทึก...
                        </>
                      ) : (
                        "บันทึกการตั้งค่า"
                      )}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={handleTestApiConnection}
                      disabled={loading}
                      className="border-purple-300 text-purple-700 hover:bg-purple-50"
                    >
                      {loading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" /> กำลังทดสอบ...
                        </>
                      ) : (
                        "ทดสอบการเชื่อมต่อ"
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="email" className="mt-6">
            <Card className="border border-purple-200 shadow-md">
              <CardHeader className="border-b border-purple-100 bg-gradient-to-r from-purple-50 to-purple-100/50">
                <CardTitle className="text-purple-700 flex items-center">
                  <div className="p-1.5 rounded-full bg-gradient-to-r from-purple-100 to-purple-200 mr-2 flex items-center justify-center">
                    <Mail className="h-5 w-5 text-purple-600" />
                  </div>
                  ตั้งค่าอีเมล
                </CardTitle>
                <div className="lightning-bar w-20 my-1"></div>
                <CardDescription className="text-purple-600/70">
                  กำหนดการตั้งค่าอีเมลและการส่งการแจ้งเตือน
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid gap-4">
                  <div className="p-4 border border-blue-200 rounded-lg bg-blue-50/50">
                    <h3 className="text-lg font-medium text-blue-700 mb-3 flex items-center">
                      <Mail className="mr-2 h-5 w-5 text-blue-600" />
                      การตั้งค่าอีเมล
                    </h3>
                    <p className="text-blue-700 mb-4">
                      ตั้งค่ารูปแบบการส่งอีเมล เทมเพลต และทดสอบการส่งอีเมล
                    </p>
                    <Button
                      onClick={() => setEmailSettingsOpen(true)}
                      className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white"
                    >
                      <Mail className="mr-2 h-4 w-4" />
                      เปิดตั้งค่าอีเมล
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="webhooks" className="mt-6">
            <Card className="border border-purple-200 shadow-md">
              <CardHeader className="border-b border-purple-100 bg-gradient-to-r from-purple-50 to-purple-100/50">
                <CardTitle className="text-purple-700 flex items-center">
                  <div className="p-1.5 rounded-full bg-gradient-to-r from-purple-100 to-purple-200 mr-2 flex items-center justify-center">
                    <Link2 className="h-5 w-5 text-purple-600" />
                  </div>
                  ตั้งค่า Webhooks
                </CardTitle>
                <div className="lightning-bar w-20 my-1"></div>
                <CardDescription className="text-purple-600/70">
                  กำหนดค่า webhooks เพื่อรับการแจ้งเตือนหรือข้อมูลจากระบบ SLIPKUY
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid gap-4">
                  <div className="p-4 border border-indigo-200 rounded-lg bg-indigo-50/50">
                    <h3 className="text-lg font-medium text-indigo-700 mb-3 flex items-center">
                      <Link2 className="mr-2 h-5 w-5 text-indigo-600" />
                      ระบบ Webhooks
                    </h3>
                    <p className="text-indigo-700 mb-4">
                      สร้างและจัดการ webhooks เพื่อรับข้อมูลจากระบบ SLIPKUY แบบเรียลไทม์
                    </p>
                    <WebhookSettingsDialog />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="notifications" className="mt-6">
            <Card className="border border-purple-200 shadow-md">
              <CardHeader className="border-b border-purple-100 bg-gradient-to-r from-purple-50 to-purple-100/50">
                <CardTitle className="text-purple-700 flex items-center">
                  <div className="p-1.5 rounded-full bg-gradient-to-r from-purple-100 to-purple-200 mr-2 flex items-center justify-center">
                    <Bell className="h-5 w-5 text-purple-600" />
                  </div>
                  การแจ้งเตือน
                </CardTitle>
                <div className="lightning-bar w-20 my-1"></div>
                <CardDescription className="text-purple-600/70">
                  กำหนดการแจ้งเตือนและรายงานอัตโนมัติ
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid gap-4">
                  <div className="p-4 border border-amber-200 rounded-lg bg-amber-50/50">
                    <h3 className="text-lg font-medium text-amber-700 mb-3 flex items-center">
                      <Bell className="mr-2 h-5 w-5 text-amber-600" />
                      ระบบการแจ้งเตือน
                    </h3>
                    <p className="text-amber-700 mb-4">
                      ตั้งค่าการแจ้งเตือนสำหรับธุรกรรมผิดปกติและรายงานต่างๆ
                    </p>
                    <Button
                      onClick={() => window.location.href = '/admin/notifications'}
                      className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white"
                    >
                      <ExternalLink className="mr-2 h-4 w-4" />
                      ไปยังการตั้งค่าการแจ้งเตือน
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="system" className="mt-6">
            <Card className="border border-purple-200 shadow-md">
              <CardHeader className="border-b border-purple-100 bg-gradient-to-r from-purple-50 to-purple-100/50">
                <CardTitle className="text-purple-700 flex items-center">
                  <div className="p-1.5 rounded-full bg-gradient-to-r from-purple-100 to-purple-200 mr-2 flex items-center justify-center">
                    <Settings className="h-5 w-5 text-purple-600" />
                  </div>
                  ตั้งค่าระบบโพลลิ่ง
                </CardTitle>
                <div className="lightning-bar w-20 my-1"></div>
                <CardDescription className="text-purple-600/70">
                  กำหนดเวลาในการอัพเดทข้อมูลอัตโนมัติ
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="polling-interval">ระยะเวลาในการโพลลิ่ง (มิลลิวินาที)</Label>
                    <Input
                      id="polling-interval"
                      type="number"
                      min="1000"
                      max="60000"
                      step="1000"
                      placeholder="3000"
                      defaultValue={
                        settings?.find(setting => setting.key === 'polling_interval')?.value || "3000"
                      }
                      onChange={(e) => {
                        // ควบคุมให้ค่าอยู่ระหว่าง 1000 - 60000
                        const value = parseInt(e.target.value);
                        if (value < 1000) e.target.value = "1000";
                        if (value > 60000) e.target.value = "60000";
                      }}
                    />
                    <p className="text-sm text-muted-foreground mt-1">
                      ระบุเวลาในการอัพเดทข้อมูลอัตโนมัติ (1000 = 1 วินาที)
                    </p>
                    <p className="text-sm text-amber-600 mt-1">
                      * แนะนำให้ตั้งค่าอย่างน้อย 3000 มิลลิวินาที (3 วินาที) เพื่อลดการใช้ทรัพยากรเซิร์ฟเวอร์
                    </p>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-2 pt-4">
                    <Button
                      onClick={() => {
                        const inputElement = document.getElementById('polling-interval') as HTMLInputElement;
                        const pollingInterval = inputElement.value;
                        // ตรวจสอบว่าค่าถูกต้องหรือไม่
                        if (!pollingInterval || parseInt(pollingInterval) < 1000 || parseInt(pollingInterval) > 60000) {
                          toast({
                            title: "ค่าไม่ถูกต้อง",
                            description: "กรุณาระบุค่าระหว่าง 1000 - 60000",
                            variant: "destructive",
                          });
                          return;
                        }
                        updateSettingMutation.mutate({ key: 'polling_interval', value: pollingInterval });
                      }}
                      disabled={updateSettingMutation.isPending}
                      className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white"
                    >
                      {updateSettingMutation.isPending ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" /> กำลังบันทึก...
                        </>
                      ) : (
                        "บันทึกการตั้งค่า"
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="database" className="mt-6">
            <Card className="border border-purple-200 shadow-md">
              <CardHeader className="border-b border-purple-100 bg-gradient-to-r from-purple-50 to-purple-100/50">
                <CardTitle className="text-purple-700 flex items-center">
                  <div className="p-1.5 rounded-full bg-gradient-to-r from-purple-100 to-purple-200 mr-2 flex items-center justify-center">
                    <Database className="h-5 w-5 text-purple-600" />
                  </div>
                  จัดการฐานข้อมูล
                </CardTitle>
                <div className="lightning-bar w-20 my-1"></div>
                <CardDescription className="text-purple-600/70">
                  สำรองข้อมูลและจัดการฐานข้อมูลของระบบ
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid gap-4">
                  <div className="p-4 border border-purple-200 rounded-lg bg-purple-50/50">
                    <h3 className="text-lg font-medium text-purple-700 mb-3 flex items-center">
                      <Database className="mr-2 h-5 w-5 text-purple-600" />
                      การสำรองข้อมูล
                    </h3>
                    <p className="text-purple-700 mb-4">
                      สร้างไฟล์สำรองข้อมูลฐานข้อมูลและตั้งค่าการสำรองข้อมูลอัตโนมัติ
                    </p>
                    <div className="flex flex-wrap gap-2">
                      <DatabaseBackupDialog />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="fraud" className="mt-6">
            <Card className="border border-purple-200 shadow-md">
              <CardHeader className="border-b border-purple-100 bg-gradient-to-r from-purple-50 to-purple-100/50">
                <CardTitle className="text-purple-700 flex items-center">
                  <div className="p-1.5 rounded-full bg-gradient-to-r from-purple-100 to-purple-200 mr-2 flex items-center justify-center">
                    <Shield className="h-5 w-5 text-purple-600" />
                  </div>
                  ตรวจจับการฉ้อโกง
                </CardTitle>
                <div className="lightning-bar w-20 my-1"></div>
                <CardDescription className="text-purple-600/70">
                  กำหนดการตั้งค่าระบบตรวจจับการฉ้อโกง
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid gap-4">
                  <div className="p-4 border border-red-200 rounded-lg bg-red-50/50">
                    <h3 className="text-lg font-medium text-red-700 mb-3 flex items-center">
                      <Shield className="mr-2 h-5 w-5 text-red-600" />
                      ระบบตรวจจับการฉ้อโกง
                    </h3>
                    <p className="text-red-700 mb-4">
                      กำหนดกฎและเงื่อนไขในการตรวจจับธุรกรรมที่น่าสงสัย
                    </p>
                    <Button
                      onClick={() => window.location.href = '/admin/fraud-detection'}
                      className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white"
                    >
                      <ExternalLink className="mr-2 h-4 w-4" />
                      ไปยังระบบตรวจจับการฉ้อโกง
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="payment" className="mt-6">
            <Card className="border border-purple-200 shadow-md">
              <CardHeader className="border-b border-purple-100 bg-gradient-to-r from-purple-50 to-purple-100/50">
                <CardTitle className="text-purple-700 flex items-center">
                  <div className="p-1.5 rounded-full bg-gradient-to-r from-purple-100 to-purple-200 mr-2 flex items-center justify-center">
                    <CreditCard className="h-5 w-5 text-purple-600" />
                  </div>
                  ตั้งค่าบัญชีธนาคาร
                </CardTitle>
                <div className="lightning-bar w-20 my-1"></div>
                <CardDescription className="text-purple-600/70">
                  กำหนดบัญชีธนาคารที่ใช้รับเงินในระบบ
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <form onSubmit={form.handleSubmit(handleSavePaymentAccounts)} className="space-y-6">
                  <div className="space-y-4">
                    <div>
                      <div className="flex items-center justify-between mb-3">
                        <Label className="text-base font-medium text-purple-700">บัญชีธนาคาร</Label>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => bankAccountsField.append({ bankCode: "", accountNumber: "", accountName: "", accountNameEn: "", qrPaymentImage: "" })}
                          className="h-8 border-purple-300 text-purple-700 hover:bg-purple-50"
                        >
                          <Plus className="mr-1 h-4 w-4" /> เพิ่มบัญชี
                        </Button>
                      </div>

                      {bankAccountsField.fields.length > 0 ? (
                        <div className="rounded-md border overflow-hidden">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead className="w-1/6">ธนาคาร</TableHead>
                                <TableHead className="w-1/6">เลขบัญชี</TableHead>
                                <TableHead className="w-3/5">ชื่อบัญชี</TableHead>
                                <TableHead className="w-[50px]"></TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {bankAccountsField.fields.map((field, index) => (
                                <TableRow key={field.id} className="border-b hover:bg-purple-50/50">
                                  <TableCell>
                                    <Select
                                      value={form.watch(`bankAccounts.${index}.bankCode`)}
                                      onValueChange={(value) => form.setValue(`bankAccounts.${index}.bankCode`, value)}
                                    >
                                      <SelectTrigger id={`bank-${index}`} className="border-purple-200 focus:ring-purple-500">
                                        <SelectValue placeholder="เลือกธนาคาร" />
                                      </SelectTrigger>
                                      <SelectContent>
                                        {THAI_BANKS.map((bank) => (
                                          <SelectItem key={bank.code} value={bank.code}>
                                            {bank.name}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                    {form.formState.errors.bankAccounts?.[index]?.bankCode && (
                                      <p className="text-xs text-red-500 mt-1">
                                        {form.formState.errors.bankAccounts[index]?.bankCode?.message}
                                      </p>
                                    )}
                                  </TableCell>
                                  <TableCell>
                                    <Input
                                      placeholder="เลขบัญชี"
                                      {...form.register(`bankAccounts.${index}.accountNumber`)}
                                      className="border-purple-200 focus:ring-purple-500"
                                    />
                                    {form.formState.errors.bankAccounts?.[index]?.accountNumber && (
                                      <p className="text-xs text-red-500 mt-1">
                                        {form.formState.errors.bankAccounts[index]?.accountNumber?.message}
                                      </p>
                                    )}
                                  </TableCell>
                                  <TableCell colSpan={2}>
                                    <div className="space-y-2">
                                      <Input
                                        placeholder="ชื่อบัญชี (ภาษาไทย)"
                                        {...form.register(`bankAccounts.${index}.accountName`)}
                                        className="border-purple-200 focus:ring-purple-500"
                                      />
                                      {form.formState.errors.bankAccounts?.[index]?.accountName && (
                                        <p className="text-xs text-red-500 mt-1">
                                          {form.formState.errors.bankAccounts[index]?.accountName?.message}
                                        </p>
                                      )}

                                      <Input
                                        placeholder="ชื่อบัญชี (ภาษาอังกฤษ)"
                                        {...form.register(`bankAccounts.${index}.accountNameEn`)}
                                        className="border-purple-200 focus:ring-purple-500"
                                      />
                                      {form.formState.errors.bankAccounts?.[index]?.accountNameEn && (
                                        <p className="text-xs text-red-500 mt-1">
                                          {form.formState.errors.bankAccounts[index]?.accountNameEn?.message}
                                        </p>
                                      )}

                                      <div className="mt-2">
                                        <Label htmlFor={`qr-upload-${index}`} className="block text-sm text-gray-600 mb-1">
                                          QR Payment (ไม่บังคับ)
                                        </Label>
                                        <div className="flex items-center gap-2">
                                          <Input
                                            id={`qr-upload-${index}`}
                                            type="file"
                                            accept="image/*"
                                            className="hidden"
                                            onChange={(e) => {
                                              const file = e.target.files?.[0];
                                              if (file) {
                                                const reader = new FileReader();
                                                reader.onload = (event) => {
                                                  const base64String = event.target?.result as string;
                                                  form.setValue(`bankAccounts.${index}.qrPaymentImage`, base64String);
                                                };
                                                reader.readAsDataURL(file);
                                              }
                                            }}
                                          />
                                          <Button
                                            type="button"
                                            variant="outline"
                                            size="sm"
                                            onClick={() => document.getElementById(`qr-upload-${index}`)?.click()}
                                            className="border-purple-300 text-purple-700 hover:bg-purple-50"
                                          >
                                            อัพโหลด QR Payment
                                          </Button>
                                          {form.watch(`bankAccounts.${index}.qrPaymentImage`) && (
                                            <div className="relative">
                                              <img
                                                src={form.watch(`bankAccounts.${index}.qrPaymentImage`)}
                                                alt="QR Payment"
                                                className="w-12 h-12 object-cover rounded"
                                              />
                                              <Button
                                                type="button"
                                                variant="destructive"
                                                size="sm"
                                                className="absolute -top-2 -right-2 h-5 w-5 p-0 rounded-full"
                                                onClick={() => form.setValue(`bankAccounts.${index}.qrPaymentImage`, "")}
                                              >
                                                ×
                                              </Button>
                                            </div>
                                          )}
                                        </div>
                                      </div>
                                    </div>
                                  </TableCell>
                                  <TableCell>
                                    <Button
                                      type="button"
                                      variant="ghost"
                                      size="icon"
                                      onClick={() => bankAccountsField.remove(index)}
                                      disabled={bankAccountsField.fields.length <= 1}
                                      className={bankAccountsField.fields.length <= 1 ? "opacity-50 cursor-not-allowed" : ""}
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </div>
                      ) : (
                        <div className="flex items-center justify-center rounded-md border border-dashed p-8">
                          <div className="text-center">
                            <p className="text-sm text-muted-foreground">ยังไม่มีบัญชีธนาคาร</p>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => bankAccountsField.append({ bankCode: "", accountNumber: "", accountName: "", accountNameEn: "", qrPaymentImage: "" })}
                              className="mt-2"
                            >
                              <Plus className="mr-1 h-4 w-4" /> เพิ่มบัญชี
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>

                    <div>
                      <div className="flex items-center justify-between mb-3">
                        <Label className="text-base font-medium text-purple-700">พร้อมเพย์</Label>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => promptpaysField.append("" as any)}
                          className="h-8 border-purple-300 text-purple-700 hover:bg-purple-50"
                        >
                          <Plus className="mr-1 h-4 w-4" /> เพิ่มพร้อมเพย์
                        </Button>
                      </div>

                      {promptpaysField.fields.length > 0 ? (
                        <div className="space-y-4">
                          {promptpaysField.fields.map((field, index) => (
                            <div key={field.id} className="flex items-center gap-2">
                              <div className="flex-1">
                                <Input
                                  placeholder="เบอร์มือถือหรือเลขประจำตัวประชาชน"
                                  {...form.register(`promptpays.${index}`)}
                                />
                                {form.formState.errors.promptpays?.[index] && (
                                  <p className="text-xs text-red-500 mt-1">
                                    {form.formState.errors.promptpays[index]?.message}
                                  </p>
                                )}
                              </div>
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                onClick={() => promptpaysField.remove(index)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="flex items-center justify-center rounded-md border border-dashed p-8">
                          <div className="text-center">
                            <p className="text-sm text-muted-foreground">ยังไม่มีเบอร์พร้อมเพย์</p>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => promptpaysField.append("" as any)}
                              className="mt-2"
                            >
                              <Plus className="mr-1 h-4 w-4" /> เพิ่มพร้อมเพย์
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  <Button
                    type="submit"
                    disabled={updateSettingMutation.isPending}
                    className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white"
                  >
                    {updateSettingMutation.isPending ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" /> กำลังบันทึก...
                      </>
                    ) : (
                      "บันทึกการตั้งค่า"
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* ส่วนของ Dialog สำหรับตั้งค่าอีเมล */}
      <EmailSettingsDialog
        open={emailSettingsOpen}
        setOpen={setEmailSettingsOpen}
        onSave={() => {
          toast({
            title: "บันทึกการตั้งค่าอีเมลเรียบร้อย",
            description: "การตั้งค่าอีเมลได้รับการอัพเดตแล้ว",
            variant: "default",
          });
        }}
      />
    </Admin>
  );
}