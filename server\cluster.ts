import cluster from 'cluster';
import os from 'os';
import { logger } from './logger';

// จำนวน CPU cores ที่มีในระบบ
const numCPUs = os.cpus().length;

/**
 * เริ่มต้นเซิร์ฟเวอร์ในโหมด cluster แบบง่าย
 * @param workerFunction ฟังก์ชันที่จะถูกเรียกในแต่ละ worker
 */
export function startCluster(workerFunction: () => void): void {
  if (cluster.isPrimary) {
    logger.info(`🚀 Master process ${process.pid} is running`);
    logger.info(`🖥️ ระบบมี ${numCPUs} CPU cores`);

    // จำกัดจำนวน workers ไม่ให้มากเกินไป (ใช้ 75% ของ CPU cores)
    const workerCount = Math.max(1, Math.floor(numCPUs * 0.75));
    logger.info(`🧑‍🔧 เริ่มต้น ${workerCount} workers`);

    // สร้าง worker ตามจำนวนที่กำหนด
    for (let i = 0; i < workerCount; i++) {
      cluster.fork();
    }

    // เมื่อ worker หยุดทำงาน ให้สร้าง worker ใหม่
    cluster.on('exit', (worker, code, signal) => {
      logger.warn(`🚨 Worker ${worker.process.pid} died (${signal || code}). Restarting...`);
      cluster.fork();
    });
  } else {
    // Worker processes
    logger.info(`👷 Worker ${process.pid} started`);

    // เรียกฟังก์ชันที่จะทำงานใน worker
    workerFunction();
  }
}
