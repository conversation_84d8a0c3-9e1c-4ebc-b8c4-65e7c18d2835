/**
 * API สำหรับจัดการการตั้งค่าระบบล็อกอินและยืนยันตัวตน
 */
import { Router, Request, Response } from 'express';
import { body, validationResult } from 'express-validator';
import { logger } from '../logger';
import { isAdmin } from '../auth';
import { storage } from '../storage';
import { smsService } from '../sms-service';

const router = Router();

// API สำหรับดึงการตั้งค่าทั้งหมดของระบบล็อกอินและยืนยันตัวตน
router.get('/auth-settings', async (req: Request, res: Response) => {
  try {
    if (!isAdmin(req)) {
      return res.status(403).json({ error: 'ไม่มีสิทธิ์เข้าถึงข้อมูลส่วนนี้' });
    }

    const securitySettings = await storage.getSystemSettings('security_settings');
    const socialLoginSettings = await storage.getSystemSettings('social_login_settings');
    const otpSettings = await storage.getSystemSettings('otp_settings');

    res.status(200).json({
      security: securitySettings ? JSON.parse(securitySettings.value) : {
        passwordMinLength: 8,
        passwordRequireNumbers: true,
        passwordRequireSpecialChars: false,
        maxLoginAttempts: 5,
        loginLockoutTime: 30,
        sessionTimeout: 1440, // 1 วัน
      },
      socialLogin: socialLoginSettings ? JSON.parse(socialLoginSettings.value) : {
        enableLineLogin: false,
        enableFacebookLogin: false,
        enableGoogleLogin: false,
      },
      otp: otpSettings ? JSON.parse(otpSettings.value) : {
        otpLength: 6,
        otpExpiry: 5,
        otpResendDelay: 60,
        smsmktApiKey: '',
        smsmktSecret: '',
        smsmktProjectKey: '',
        smsSender: 'SLIPKUY',
        offlineSimulation: true,
      },
    });
  } catch (error) {
    logger.error('เกิดข้อผิดพลาดในการดึงการตั้งค่าระบบล็อกอิน', error);
    res.status(500).json({ error: 'เกิดข้อผิดพลาดในการดึงการตั้งค่า' });
  }
});

// API สำหรับอัปเดตการตั้งค่าความปลอดภัย
router.post('/auth-settings/security', [
  body('passwordMinLength').isInt({ min: 6, max: 32 }),
  body('passwordRequireNumbers').isBoolean(),
  body('passwordRequireSpecialChars').isBoolean(),
  body('maxLoginAttempts').isInt({ min: 1, max: 20 }),
  body('loginLockoutTime').isInt({ min: 1, max: 1440 }),
  body('sessionTimeout').isInt({ min: 10, max: 10080 }),
], async (req: Request, res: Response) => {
  try {
    if (!isAdmin(req)) {
      return res.status(403).json({ error: 'ไม่มีสิทธิ์ดำเนินการส่วนนี้' });
    }

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const settings = req.body;
    await storage.setSystemSettings('security_settings', JSON.stringify(settings));

    logger.info('อัปเดตการตั้งค่าความปลอดภัยสำเร็จ', { userId: req.user?.id });
    res.status(200).json({ success: true, message: 'อัปเดตการตั้งค่าความปลอดภัยสำเร็จ' });
  } catch (error) {
    logger.error('เกิดข้อผิดพลาดในการอัปเดตการตั้งค่าความปลอดภัย', error);
    res.status(500).json({ error: 'เกิดข้อผิดพลาดในการอัปเดตการตั้งค่า' });
  }
});

// API สำหรับอัปเดตการตั้งค่า Social Login
router.post('/auth-settings/social', [
  body('enableLineLogin').isBoolean(),
  body('enableFacebookLogin').isBoolean(),
  body('enableGoogleLogin').isBoolean(),
], async (req: Request, res: Response) => {
  try {
    if (!isAdmin(req)) {
      return res.status(403).json({ error: 'ไม่มีสิทธิ์ดำเนินการส่วนนี้' });
    }

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const settings = req.body;
    await storage.setSystemSettings('social_login_settings', JSON.stringify(settings));

    logger.info('อัปเดตการตั้งค่า Social Login สำเร็จ', { userId: req.user?.id });
    res.status(200).json({ success: true, message: 'อัปเดตการตั้งค่า Social Login สำเร็จ' });
  } catch (error) {
    logger.error('เกิดข้อผิดพลาดในการอัปเดตการตั้งค่า Social Login', error);
    res.status(500).json({ error: 'เกิดข้อผิดพลาดในการอัปเดตการตั้งค่า' });
  }
});

// API สำหรับอัปเดตการตั้งค่า OTP
router.post('/auth-settings/otp', [
  body('otpLength').isInt({ min: 4, max: 8 }),
  body('otpExpiry').isInt({ min: 1, max: 60 }),
  body('otpResendDelay').isInt({ min: 30, max: 300 }),
  body('offlineSimulation').isBoolean(),
  body('smsmktApiKey').optional(),
  body('smsmktSecret').optional(),
  body('smsmktProjectKey').optional(),
  body('smsSender').optional().isLength({ max: 11 }),
], async (req: Request, res: Response) => {
  try {
    if (!isAdmin(req)) {
      return res.status(403).json({ error: 'ไม่มีสิทธิ์ดำเนินการส่วนนี้' });
    }

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const settings = req.body;
    await storage.setSystemSettings('otp_settings', JSON.stringify(settings));

    // อัปเดตการตั้งค่า SMS Service ถ้ามี
    if (settings.smsmktApiKey) {
      await smsService.updateConfig({
        apiKey: settings.smsmktApiKey,
        secret: settings.smsmktSecret,
        sender: settings.smsSender,
        offlineMode: settings.offlineSimulation,
      });
    }

    logger.info('อัปเดตการตั้งค่า OTP สำเร็จ', { userId: req.user?.id });
    res.status(200).json({ success: true, message: 'อัปเดตการตั้งค่า OTP สำเร็จ' });
  } catch (error) {
    logger.error('เกิดข้อผิดพลาดในการอัปเดตการตั้งค่า OTP', error);
    res.status(500).json({ error: 'เกิดข้อผิดพลาดในการอัปเดตการตั้งค่า' });
  }
});

// API สำหรับทดสอบการส่ง OTP
router.post('/test-send-otp', [
  body('phoneNumber').isMobilePhone('th-TH'),
], async (req: Request, res: Response) => {
  try {
    if (!isAdmin(req)) {
      return res.status(403).json({ error: 'ไม่มีสิทธิ์ดำเนินการส่วนนี้' });
    }

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { phoneNumber } = req.body;

    // ส่ง OTP ไปยังเบอร์โทรศัพท์ผ่าน SMSMKT OTP API
    const result = await smsService.sendOTP(phoneNumber);

    logger.info('ทดสอบส่ง OTP สำเร็จ', {
      userId: req.user?.id,
      phoneNumber,
      offlineMode: result.offlineMode,
      token: result.token,
      otpRef: result.otpRef
    });

    res.status(200).json({
      success: true,
      message: 'ส่ง OTP ทดสอบสำเร็จ',
      otpCode: result.otpCode,
      token: result.token,
      otpRef: result.otpRef,
      offlineMode: result.offlineMode
    });
  } catch (error) {
    logger.error('เกิดข้อผิดพลาดในการทดสอบส่ง OTP', error);
    res.status(500).json({ error: 'เกิดข้อผิดพลาดในการส่ง OTP' });
  }
});

// API สำหรับดึงสถิติการล็อกอิน
router.get('/auth-stats', async (req: Request, res: Response) => {
  try {
    if (!isAdmin(req)) {
      return res.status(403).json({ error: 'ไม่มีสิทธิ์เข้าถึงข้อมูลส่วนนี้' });
    }

    // ดึงสถิติการล็อกอินจากฐานข้อมูล
    const loginStats = await storage.getLoginStats();

    res.status(200).json(loginStats);
  } catch (error) {
    logger.error('เกิดข้อผิดพลาดในการดึงสถิติการล็อกอิน', error);
    res.status(500).json({ error: 'เกิดข้อผิดพลาดในการดึงสถิติ' });
  }
});

export const authSettingsApi = router;