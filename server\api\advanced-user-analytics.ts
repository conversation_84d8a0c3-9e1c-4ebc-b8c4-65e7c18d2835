import { Express, Request, Response, Router } from "express";
import { storage } from "../storage";
import { db } from "../db";
import { eq, gte, lte, desc, sql, sum, count, avg } from "drizzle-orm";
import { slipVerifications, apiKeys, users, apiLogs } from "@shared/schema";
import path from "path";

const router = Router();

/**
 * API endpoint สำหรับการวิเคราะห์ขั้นสูงข้อมูลของผู้ใช้เท่านั้น
 * สามารถกรองข้อมูลตามช่วงเวลา และกำหนดเงื่อนไขในการวิเคราะห์
 */
router.post('/advanced-search/analyze', async (req: Request, res: Response) => {
  try {
    // ตรวจสอบสถานะการล็อกอิน
    if (!req.isAuthenticated()) {
      return res.status(401).json({
        success: false,
        message: 'กรุณาเข้าสู่ระบบก่อนใช้งาน',
      });
    }
    
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'ไม่พบข้อมูลผู้ใช้ กรุณาเข้าสู่ระบบใหม่',
      });
    }
    
    console.log(`ผู้ใช้ล็อกอินแล้ว: ${userId}`);
    
    // ดึงข้อมูลจาก request body
    const {
      timeframe,
      startDate,
      endDate,
      analysisType,
      groupBy,
      includeDetails,
      status,
      bankName,
      amount,
      receiver
    } = req.body;
    
    // กำหนดช่วงเวลาในการวิเคราะห์
    let fromDate: Date;
    let toDate = new Date(); // ปัจจุบัน
    
    // กำหนดช่วงเวลาตามที่เลือก
    if (startDate && endDate) {
      // ใช้ช่วงวันที่ที่กำหนดมาโดยตรง
      fromDate = new Date(startDate);
      toDate = new Date(endDate);
      toDate.setHours(23, 59, 59, 999); // ตั้งเวลาเป็นสิ้นสุดของวัน
    } else if (timeframe) {
      // ใช้ช่วงเวลาที่กำหนดไว้ล่วงหน้า
      switch (timeframe) {
        case '7days':
          fromDate = new Date();
          fromDate.setDate(fromDate.getDate() - 7);
          break;
        case '30days':
          fromDate = new Date();
          fromDate.setDate(fromDate.getDate() - 30);
          break;
        case '90days':
          fromDate = new Date();
          fromDate.setDate(fromDate.getDate() - 90);
          break;
        case '1year':
          fromDate = new Date();
          fromDate.setFullYear(fromDate.getFullYear() - 1);
          break;
        case 'all':
          fromDate = new Date(0); // เริ่มตั้งแต่ 1 มกราคม 1970
          break;
        default:
          fromDate = new Date();
          fromDate.setDate(fromDate.getDate() - 30); // ค่าเริ่มต้น 30 วัน
      }
    } else {
      // ค่าเริ่มต้นหากไม่ระบุ - 30 วันล่าสุด
      fromDate = new Date();
      fromDate.setDate(fromDate.getDate() - 30);
    }
    
    console.log(`ทำการวิเคราะห์ข้อมูลตั้งแต่ ${fromDate.toISOString()} ถึง ${toDate.toISOString()}`);
    
    // ทำการวิเคราะห์ข้อมูลตามประเภทที่เลือก
    let results: any = {};
    let details: any[] = [];
    
    // สร้าง conditions สำหรับการ query ข้อมูล
    let conditions = sql`${slipVerifications.userId} = ${userId} AND
        ${slipVerifications.createdAt} >= ${fromDate} AND
        ${slipVerifications.createdAt} <= ${toDate}`;
    
    // เพิ่มเงื่อนไขการกรองตามพารามิเตอร์ที่ส่งมา
    if (status) {
      conditions = sql`${conditions} AND ${slipVerifications.status} = ${status}`;
    }
    
    if (bankName) {
      conditions = sql`${conditions} AND ${slipVerifications.bankName} = ${bankName}`;
    }
    
    if (amount && amount > 0) {
      conditions = sql`${conditions} AND ${slipVerifications.amount} = ${amount}`;
    }
    
    if (receiver) {
      conditions = sql`${conditions} AND ${slipVerifications.receiver} LIKE ${`%${receiver}%`}`;
    }
    
    // ดึงข้อมูลรายการตรวจสอบสลิปในช่วงเวลาที่กำหนด
    console.log('กำลังค้นหาข้อมูลในฐานข้อมูลด้วย SQL condition:', conditions);
    
    const verifications = await db
      .select()
      .from(slipVerifications)
      .leftJoin(apiKeys, eq(slipVerifications.apiKeyId, apiKeys.id))
      .where(conditions)
      .orderBy(desc(slipVerifications.createdAt));
      
    console.log(`พบข้อมูลการตรวจสอบสลิปทั้งหมด ${verifications.length} รายการ`);
    if (verifications.length > 0) {
      console.log('ตัวอย่างข้อมูลรายการแรก:', JSON.stringify(verifications[0], null, 2));
    } else {
      console.log('ไม่พบข้อมูลการตรวจสอบสลิปตามเงื่อนไข');
    }
      
    // เก็บข้อมูลรายละเอียดหากต้องการ
    if (includeDetails) {
      details = verifications.map(v => ({
        ...v.slip_verifications,
        apiKey: v.api_keys ? {
          id: v.api_keys.id,
          name: v.api_keys.name,
          status: v.api_keys.status,
        } : null
      }));
    }
    
    // ใช้ข้อมูลที่ได้มาวิเคราะห์ตาม analysisType ที่เลือก
    switch (analysisType) {
      case 'usage_summary': {
        // สรุปการใช้งานทั่วไป
        // วิเคราะห์สรุปข้อมูลการใช้งานแบ่งตามสถานะ
        const statusCounts: Record<string, number> = {};
        verifications.forEach(v => {
          const status = v.slip_verifications.status || 'unknown';
          statusCounts[status] = (statusCounts[status] || 0) + 1;
        });
        
        // คำนวณยอดเงินรวมและเฉลี่ยต่อรายการ
        const totalAmount = verifications.reduce((sum, v) => 
          sum + (v.slip_verifications.amount || 0), 0);
        const avgAmount = verifications.length > 0 ? 
          totalAmount / verifications.length : 0;
          
        // หายอดเงินสูงสุดและต่ำสุด
        let maxAmount = 0;
        let minAmount = Number.MAX_SAFE_INTEGER;
        let maxTransaction = null;
        let minTransaction = null;
        
        verifications.forEach(v => {
          const amount = v.slip_verifications.amount || 0;
          if (amount > maxAmount) {
            maxAmount = amount;
            maxTransaction = v.slip_verifications;
          }
          if (amount > 0 && amount < minAmount) {
            minAmount = amount;
            minTransaction = v.slip_verifications;
          }
        });
        
        // สรุปข้อมูลส่วนการใช้ API ต่างๆ
        const sourceCount: Record<string, number> = {};
        verifications.forEach(v => {
          const source = v.slip_verifications.verificationSource || 'unknown';
          sourceCount[source] = (sourceCount[source] || 0) + 1;
        });
        
        results = {
          totalVerifications: verifications.length,
          statusCounts,
          totalAmount,
          averageAmount: avgAmount,
          highestAmount: {
            amount: maxAmount,
            transaction: maxTransaction ? {
              id: maxTransaction.id,
              date: maxTransaction.createdAt,
              bank: maxTransaction.bankName,
              reference: maxTransaction.transactionRef
            } : null
          },
          lowestAmount: minAmount === Number.MAX_SAFE_INTEGER ? 0 : {
            amount: minAmount,
            transaction: minTransaction ? {
              id: minTransaction.id,
              date: minTransaction.createdAt,
              bank: minTransaction.bankName,
              reference: minTransaction.transactionRef
            } : null
          },
          sourceCounts: sourceCount
        };
        break;
      }
      
      case 'bank_analysis': {
        // วิเคราะห์แยกตามธนาคาร
        const bankData: Record<string, any> = {};
        
        verifications.forEach(v => {
          const bank = v.slip_verifications.bankName || 'ไม่ระบุธนาคาร';
          const amount = v.slip_verifications.amount || 0;
          
          if (!bankData[bank]) {
            bankData[bank] = {
              count: 0,
              totalAmount: 0,
              successCount: 0,
              failCount: 0
            };
          }
          
          bankData[bank].count++;
          bankData[bank].totalAmount += amount;
          
          if (v.slip_verifications.status === 'success') {
            bankData[bank].successCount++;
          } else if (v.slip_verifications.status === 'failed' || 
                     v.slip_verifications.status === 'error') {
            bankData[bank].failCount++;
          }
        });
        
        // คำนวณค่าเฉลี่ยและอัตราความสำเร็จสำหรับแต่ละธนาคาร
        Object.keys(bankData).forEach(bank => {
          const data = bankData[bank];
          data.averageAmount = data.count > 0 ? data.totalAmount / data.count : 0;
          data.successRate = data.count > 0 ? (data.successCount / data.count) * 100 : 0;
        });
        
        results = {
          bankAnalysis: bankData,
          totalBanks: Object.keys(bankData).length
        };
        break;
      }
      
      case 'time_analysis': {
        // วิเคราะห์ตามเวลา
        
        // จัดกลุ่มตามวันในสัปดาห์
        const weekdayCounts: Record<string, number> = {
          'วันอาทิตย์': 0, 'วันจันทร์': 0, 'วันอังคาร': 0, 'วันพุธ': 0, 
          'วันพฤหัสบดี': 0, 'วันศุกร์': 0, 'วันเสาร์': 0
        };
        const weekdayNames = ['วันอาทิตย์', 'วันจันทร์', 'วันอังคาร', 'วันพุธ', 'วันพฤหัสบดี', 'วันศุกร์', 'วันเสาร์'];
        
        // จัดกลุ่มตามช่วงเวลาของวัน
        const hourCounts: Record<string, number> = {};
        for (let i = 0; i < 24; i++) {
          hourCounts[`${i.toString().padStart(2, '0')}:00`] = 0;
        }
        
        // จัดกลุ่มตามเดือน
        const monthCounts: Record<string, number> = {};
        const thaiMonths = [
          'มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน',
          'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'
        ];
        
        verifications.forEach(v => {
          const date = new Date(v.slip_verifications.createdAt || new Date());
          
          // นับตามวันในสัปดาห์
          const weekday = date.getDay(); // 0 = วันอาทิตย์, 1 = วันจันทร์, ...
          weekdayCounts[weekdayNames[weekday]]++;
          
          // นับตามช่วงเวลาของวัน
          const hour = date.getHours();
          const hourStr = `${hour.toString().padStart(2, '0')}:00`;
          hourCounts[hourStr]++;
          
          // นับตามเดือน
          const month = date.getMonth(); // 0-11
          const monthStr = thaiMonths[month];
          monthCounts[monthStr] = (monthCounts[monthStr] || 0) + 1;
        });
        
        // หาช่วงเวลาที่มีการใช้งานมากที่สุด
        const mostActiveHour = Object.entries(hourCounts)
          .sort((a, b) => b[1] - a[1])[0];
        
        // หาวันที่มีการใช้งานมากที่สุด
        const mostActiveDay = Object.entries(weekdayCounts)
          .sort((a, b) => b[1] - a[1])[0];
        
        results = {
          weekdayDistribution: weekdayCounts,
          hourlyDistribution: hourCounts,
          monthlyDistribution: monthCounts,
          mostActiveTime: {
            hour: mostActiveHour[0],
            count: mostActiveHour[1]
          },
          mostActiveDay: {
            day: mostActiveDay[0],
            count: mostActiveDay[1]
          },
          // เพิ่มสถิติอัตราการใช้งานตามช่วงเวลา (เช้า/กลางวัน/เย็น/กลางคืน)
          timeOfDayDistribution: {
            morning: Object.entries(hourCounts)
              .filter(([h]) => parseInt(h) >= 6 && parseInt(h) < 12)
              .reduce((sum, [_, count]) => sum + count, 0),
            afternoon: Object.entries(hourCounts)
              .filter(([h]) => parseInt(h) >= 12 && parseInt(h) < 17)
              .reduce((sum, [_, count]) => sum + count, 0),
            evening: Object.entries(hourCounts)
              .filter(([h]) => parseInt(h) >= 17 && parseInt(h) < 22)
              .reduce((sum, [_, count]) => sum + count, 0),
            night: Object.entries(hourCounts)
              .filter(([h]) => parseInt(h) >= 22 || parseInt(h) < 6)
              .reduce((sum, [_, count]) => sum + count, 0)
          }
        };
        break;
      }
      
      case 'transaction_patterns': {
        // วิเคราะห์รูปแบบธุรกรรม
        
        // กลุ่มจำนวนเงิน
        const amountRanges: Record<string, number> = {
          '< 100': 0,
          '100-500': 0,
          '501-1,000': 0,
          '1,001-5,000': 0,
          '5,001-10,000': 0,
          '10,001-50,000': 0,
          '50,001-100,000': 0,
          '> 100,000': 0
        };
        
        // วิเคราะห์รูปแบบผู้รับและผู้ส่ง
        const receivers: Record<string, {count: number, total: number}> = {};
        const senders: Record<string, {count: number, total: number}> = {};
        
        verifications.forEach(v => {
          const amount = v.slip_verifications.amount || 0;
          
          // จัดกลุ่มตามช่วงจำนวนเงิน
          if (amount < 100) amountRanges['< 100']++;
          else if (amount <= 500) amountRanges['100-500']++;
          else if (amount <= 1000) amountRanges['501-1,000']++;
          else if (amount <= 5000) amountRanges['1,001-5,000']++;
          else if (amount <= 10000) amountRanges['5,001-10,000']++;
          else if (amount <= 50000) amountRanges['10,001-50,000']++;
          else if (amount <= 100000) amountRanges['50,001-100,000']++;
          else amountRanges['> 100,000']++;
          
          // วิเคราะห์ผู้รับ
          const receiver = v.slip_verifications.receiver || 'ไม่ระบุ';
          if (!receivers[receiver]) {
            receivers[receiver] = {count: 0, total: 0};
          }
          receivers[receiver].count++;
          receivers[receiver].total += amount;
          
          // วิเคราะห์ผู้ส่ง
          const sender = v.slip_verifications.sender || 'ไม่ระบุ';
          if (!senders[sender]) {
            senders[sender] = {count: 0, total: 0};
          }
          senders[sender].count++;
          senders[sender].total += amount;
        });
        
        // จัดเรียงข้อมูลผู้รับและผู้ส่งตามความถี่
        const topReceivers = Object.entries(receivers)
          .map(([name, data]) => ({name, ...data}))
          .sort((a, b) => b.count - a.count)
          .slice(0, 5);
          
        const topSenders = Object.entries(senders)
          .map(([name, data]) => ({name, ...data}))
          .sort((a, b) => b.count - a.count)
          .slice(0, 5);
        
        results = {
          amountDistribution: amountRanges,
          topReceivers,
          topSenders,
          mostCommonAmount: Object.entries(amountRanges)
            .sort((a, b) => b[1] - a[1])[0]
        };
        break;
      }
      
      case 'api_usage': {
        // วิเคราะห์การใช้งาน API
        
        // ดึงข้อมูล API Logs
        const apiUsageLogs = await db
          .select()
          .from(apiLogs)
          .leftJoin(apiKeys, eq(apiLogs.apiKeyId, apiKeys.id))
          .where(
            sql`${apiKeys.userId} = ${userId} AND
                ${apiLogs.createdAt} >= ${fromDate} AND
                ${apiLogs.createdAt} <= ${toDate}`
          )
          .orderBy(desc(apiLogs.createdAt));
        
        // จัดกลุ่มตาม API Key
        const apiKeyUsage: Record<string, any> = {};
        
        apiUsageLogs.forEach(log => {
          if (!log.api_keys) return;
          
          const keyId = log.api_keys.id;
          const keyName = log.api_keys.name || `API Key ${keyId}`;
          
          if (!apiKeyUsage[keyName]) {
            apiKeyUsage[keyName] = {
              id: keyId,
              count: 0,
              successCount: 0,
              errorCount: 0,
              avgResponseTime: 0,
              totalResponseTime: 0
            };
          }
          
          apiKeyUsage[keyName].count++;
          
          if (log.api_logs.responseStatus === 'success') {
            apiKeyUsage[keyName].successCount++;
          } else {
            apiKeyUsage[keyName].errorCount++;
          }
          
          // เก็บข้อมูลเวลาตอบสนอง
          const responseTime = log.api_logs.processingTime || 0;
          apiKeyUsage[keyName].totalResponseTime += responseTime;
        });
        
        // คำนวณเวลาตอบสนองเฉลี่ย
        Object.keys(apiKeyUsage).forEach(key => {
          const usage = apiKeyUsage[key];
          usage.avgResponseTime = usage.count > 0 ? 
            usage.totalResponseTime / usage.count : 0;
          usage.successRate = usage.count > 0 ? 
            (usage.successCount / usage.count) * 100 : 0;
        });
        
        // วิเคราะห์ปริมาณการใช้งานตาม IP address
        const ipAddressUsage: Record<string, number> = {};
        apiUsageLogs.forEach(log => {
          const ip = log.api_logs.ipAddress || 'unknown';
          ipAddressUsage[ip] = (ipAddressUsage[ip] || 0) + 1;
        });
        
        // จัดเรียง IP Address ตามความถี่
        const topIpAddresses = Object.entries(ipAddressUsage)
          .map(([ip, count]) => ({ip, count}))
          .sort((a, b) => b.count - a.count)
          .slice(0, 5);
        
        results = {
          totalApiCalls: apiUsageLogs.length,
          apiKeyUsage,
          topIpAddresses,
          mostUsedApiKey: Object.entries(apiKeyUsage)
            .sort((a, b) => b[1].count - a[1].count)[0]?.[0] || 'ไม่มี'
        };
        break;
      }
      
      default: {
        // ถ้าไม่ได้ระบุประเภทการวิเคราะห์ ให้ส่งข้อมูลสรุปรวม
        
        // จำนวนรายการทั้งหมด
        const totalCount = verifications.length;
        
        // จำนวนรายการที่สำเร็จ/ล้มเหลว
        const successCount = verifications.filter(v => 
          v.slip_verifications.status === 'success').length;
        const failCount = verifications.filter(v => 
          v.slip_verifications.status === 'failed' || 
          v.slip_verifications.status === 'error').length;
        
        // ยอดเงินรวม
        const totalAmount = verifications.reduce((sum, v) => 
          sum + (v.slip_verifications.amount || 0), 0);
        
        // แยกตามธนาคาร
        const bankCounts: Record<string, number> = {};
        verifications.forEach(v => {
          const bank = v.slip_verifications.bankName || 'ไม่ระบุธนาคาร';
          bankCounts[bank] = (bankCounts[bank] || 0) + 1;
        });
        
        results = {
          totalTransactions: totalCount,
          successCount,
          failCount,
          successRate: totalCount > 0 ? (successCount / totalCount) * 100 : 0,
          totalAmount,
          averageAmount: totalCount > 0 ? totalAmount / totalCount : 0,
          bankDistribution: bankCounts
        };
      }
    }
    
    // ส่งผลลัพธ์การวิเคราะห์กลับไป
    res.json({
      success: true,
      timeframe: {
        start: fromDate,
        end: toDate,
        label: timeframe || 'custom',
      },
      analysisType: analysisType || 'summary',
      results,
      details: includeDetails ? details : undefined,
    });
    
  } catch (error) {
    console.error('Error in advanced user analysis:', error);
    res.status(500).json({
      success: false,
      message: 'เกิดข้อผิดพลาดในการวิเคราะห์ข้อมูล',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * API endpoint สำหรับการหาความสัมพันธ์ระหว่างรายการตรวจสอบสลิปของผู้ใช้
 * วิเคราะห์รูปแบบและความเชื่อมโยงของการทำธุรกรรม
 */
router.post('/advanced-search/correlations', async (req: Request, res: Response) => {
  try {
    // ตรวจสอบสถานะการล็อกอิน
    if (!req.isAuthenticated()) {
      return res.status(401).json({
        success: false,
        message: 'กรุณาเข้าสู่ระบบก่อนใช้งาน',
      });
    }
    
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'ไม่สามารถระบุตัวตนผู้ใช้งานได้',
      });
    }
    
    // ดึงข้อมูลจาก request body
    const {
      timeframe,
      startDate,
      endDate,
      correlationType
    } = req.body;
    
    // กำหนดช่วงเวลาในการวิเคราะห์ (ใช้โค้ดเดียวกับ endpoint analyze)
    let fromDate: Date;
    let toDate = new Date(); // ปัจจุบัน
    
    // กำหนดช่วงเวลาตามที่เลือก
    if (startDate && endDate) {
      // ใช้ช่วงวันที่ที่กำหนดมาโดยตรง
      fromDate = new Date(startDate);
      toDate = new Date(endDate);
      toDate.setHours(23, 59, 59, 999); // ตั้งเวลาเป็นสิ้นสุดของวัน
    } else if (timeframe) {
      // ใช้ช่วงเวลาที่กำหนดไว้ล่วงหน้า
      switch (timeframe) {
        case '7days':
          fromDate = new Date();
          fromDate.setDate(fromDate.getDate() - 7);
          break;
        case '30days':
          fromDate = new Date();
          fromDate.setDate(fromDate.getDate() - 30);
          break;
        case '90days':
          fromDate = new Date();
          fromDate.setDate(fromDate.getDate() - 90);
          break;
        case '1year':
          fromDate = new Date();
          fromDate.setFullYear(fromDate.getFullYear() - 1);
          break;
        case 'all':
          fromDate = new Date(0); // เริ่มตั้งแต่ 1 มกราคม 1970
          break;
        default:
          fromDate = new Date();
          fromDate.setDate(fromDate.getDate() - 30); // ค่าเริ่มต้น 30 วัน
      }
    } else {
      // ค่าเริ่มต้นหากไม่ระบุ - 30 วันล่าสุด
      fromDate = new Date();
      fromDate.setDate(fromDate.getDate() - 30);
    }
    
    // ดึงข้อมูลรายการตรวจสอบสลิปในช่วงเวลาที่กำหนด
    const verifications = await db
      .select()
      .from(slipVerifications)
      .where(
        sql`${slipVerifications.userId} = ${userId} AND
            ${slipVerifications.createdAt} >= ${fromDate} AND
            ${slipVerifications.createdAt} <= ${toDate}`
      )
      .orderBy(desc(slipVerifications.createdAt));
    
    // ตรวจสอบประเภทของความสัมพันธ์ที่ต้องการวิเคราะห์
    let correlationResults: any = {};
    
    switch (correlationType) {
      case 'transaction_patterns': {
        // หารูปแบบธุรกรรมที่เกิดขึ้นบ่อย

        // วิเคราะห์ความสัมพันธ์ระหว่างจำนวนเงินและธนาคาร
        const bankAmountPatterns: Record<string, number[]> = {};
        verifications.forEach(v => {
          const bank = v.bankName || 'ไม่ระบุธนาคาร';
          const amount = v.amount || 0;
          
          if (!bankAmountPatterns[bank]) {
            bankAmountPatterns[bank] = [];
          }
          bankAmountPatterns[bank].push(amount);
        });
        
        // คำนวณค่าเฉลี่ยและค่าที่พบบ่อยสำหรับแต่ละธนาคาร
        const bankAmountAnalysis: Record<string, any> = {};
        Object.entries(bankAmountPatterns).forEach(([bank, amounts]) => {
          // หาค่าเฉลี่ย
          const avgAmount = amounts.reduce((sum, amt) => sum + amt, 0) / amounts.length;
          
          // หาจำนวนเงินที่ใช้บ่อยที่สุด (mode)
          const amountFrequency: Record<number, number> = {};
          amounts.forEach(amt => {
            amountFrequency[amt] = (amountFrequency[amt] || 0) + 1;
          });
          
          const mostCommonAmount = Object.entries(amountFrequency)
            .sort((a, b) => b[1] - a[1])[0];
          
          bankAmountAnalysis[bank] = {
            totalTransactions: amounts.length,
            averageAmount: avgAmount,
            mostCommonAmount: mostCommonAmount ? {
              amount: parseFloat(mostCommonAmount[0]),
              frequency: mostCommonAmount[1],
              percentage: (mostCommonAmount[1] / amounts.length) * 100
            } : null
          };
        });
        
        // หาธนาคารที่ใช้บ่อยในแต่ละช่วงเวลา
        const timeOfDayBankPreference: Record<string, Record<string, number>> = {
          'เช้า (6:00-11:59)': {},
          'กลางวัน (12:00-16:59)': {},
          'เย็น (17:00-21:59)': {},
          'กลางคืน (22:00-5:59)': {}
        };
        
        verifications.forEach(v => {
          if (!v.createdAt) return;
          
          const date = new Date(v.createdAt);
          const hour = date.getHours();
          const bank = v.bankName || 'ไม่ระบุธนาคาร';
          
          let timeCategory;
          if (hour >= 6 && hour < 12) {
            timeCategory = 'เช้า (6:00-11:59)';
          } else if (hour >= 12 && hour < 17) {
            timeCategory = 'กลางวัน (12:00-16:59)';
          } else if (hour >= 17 && hour < 22) {
            timeCategory = 'เย็น (17:00-21:59)';
          } else {
            timeCategory = 'กลางคืน (22:00-5:59)';
          }
          
          timeOfDayBankPreference[timeCategory][bank] = 
            (timeOfDayBankPreference[timeCategory][bank] || 0) + 1;
        });
        
        // หาธนาคารที่ใช้บ่อยที่สุดในแต่ละช่วงเวลา
        const preferredBankByTimeOfDay: Record<string, any> = {};
        Object.entries(timeOfDayBankPreference).forEach(([timeCategory, banks]) => {
          const sortedBanks = Object.entries(banks)
            .sort((a, b) => b[1] - a[1]);
            
          preferredBankByTimeOfDay[timeCategory] = sortedBanks.length > 0 ? {
            bank: sortedBanks[0][0],
            count: sortedBanks[0][1],
            percentage: sortedBanks[0][1] / sortedBanks.reduce((sum, [_, count]) => sum + count, 0) * 100
          } : null;
        });

        correlationResults = {
          bankAmountPatterns: bankAmountAnalysis,
          timeOfDayBankPreference: preferredBankByTimeOfDay
        };
        break;
      }
      
      case 'recurring_transactions': {
        // หารายการที่เกิดขึ้นซ้ำๆ
        
        // หาผู้รับที่มีรายการซ้ำๆ
        const recurringReceivers: Record<string, any[]> = {};
        
        verifications.forEach(v => {
          const receiver = v.receiver || 'ไม่ระบุ';
          if (!recurringReceivers[receiver]) {
            recurringReceivers[receiver] = [];
          }
          
          recurringReceivers[receiver].push({
            id: v.id,
            date: v.createdAt,
            amount: v.amount,
            bank: v.bankName,
            transactionRef: v.transactionRef
          });
        });
        
        // กรองเฉพาะผู้รับที่มีรายการมากกว่า 1 รายการ
        const filteredRecurringReceivers: Record<string, any> = {};
        Object.entries(recurringReceivers).forEach(([receiver, transactions]) => {
          if (transactions.length > 1) {
            // คำนวณช่วงห่างระหว่างรายการ (ในวัน)
            const dates = transactions.map(t => new Date(t.date || new Date()).getTime());
            dates.sort((a, b) => a - b);
            
            const intervals: number[] = [];
            for (let i = 1; i < dates.length; i++) {
              const diffDays = (dates[i] - dates[i-1]) / (1000 * 60 * 60 * 24);
              intervals.push(diffDays);
            }
            
            // หาช่วงห่างเฉลี่ย
            const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
            
            // ตรวจสอบว่าเป็นรายการที่ทำเป็นประจำหรือไม่
            // (ช่วงห่างคงที่ภายใน 2 วัน)
            const isConsistent = intervals.length > 1 && 
              Math.max(...intervals) - Math.min(...intervals) <= 2;
              
            // คำนวณจำนวนเงินเฉลี่ย
            const amounts = transactions.map(t => t.amount || 0);
            const avgAmount = amounts.reduce((sum, amt) => sum + amt, 0) / amounts.length;
            
            // คำนวณความเบี่ยงเบนมาตรฐานของจำนวนเงิน
            const amountVariance = amounts.reduce((sum, amt) => sum + Math.pow(amt - avgAmount, 2), 0) / amounts.length;
            const amountStdDev = Math.sqrt(amountVariance);
            
            // เพิ่มข้อมูลการวิเคราะห์
            filteredRecurringReceivers[receiver] = {
              transactionCount: transactions.length,
              transactions,
              averageAmount: avgAmount,
              amountStdDev,
              averageIntervalDays: avgInterval,
              patternType: isConsistent ? 'ประจำ' : 'ไม่สม่ำเสมอ',
              // ทำนายวันที่น่าจะเกิดรายการถัดไป
              predictedNextTransaction: isConsistent ? 
                new Date(dates[dates.length - 1] + (avgInterval * 24 * 60 * 60 * 1000)) : null
            };
          }
        });
        
        correlationResults = {
          recurringTransactions: filteredRecurringReceivers,
          totalPatterns: Object.keys(filteredRecurringReceivers).length
        };
        break;
      }
      
      case 'anomaly_detection': {
        // ตรวจจับรายการผิดปกติ
        
        // คำนวณค่าเฉลี่ยและความเบี่ยงเบนมาตรฐานของจำนวนเงิน
        const amounts = verifications.map(v => v.amount || 0).filter(amt => amt > 0);
        const avgAmount = amounts.reduce((sum, amt) => sum + amt, 0) / amounts.length;
        const amountVariance = amounts.reduce((sum, amt) => sum + Math.pow(amt - avgAmount, 2), 0) / amounts.length;
        const amountStdDev = Math.sqrt(amountVariance);
        
        // กำหนดขอบเขตปกติ (ค่าเฉลี่ย ± 2 ความเบี่ยงเบนมาตรฐาน)
        const upperBound = avgAmount + (2 * amountStdDev);
        const lowerBound = Math.max(0, avgAmount - (2 * amountStdDev));
        
        // หารายการที่ผิดปกติด้านจำนวนเงิน
        const amountAnomalies = verifications
          .filter(v => {
            const amount = v.amount || 0;
            return amount > 0 && (amount > upperBound || amount < lowerBound);
          })
          .map(v => ({
            id: v.id,
            date: v.createdAt,
            amount: v.amount,
            bank: v.bankName,
            receiver: v.receiver,
            transactionRef: v.transactionRef
          }));
        
        // หารายการที่ผิดปกติด้านเวลา (เวลาที่ไม่เคยทำรายการมาก่อน)
        const hourlyDistribution: Record<number, number> = {};
        verifications.forEach(v => {
          if (!v.createdAt) return;
          const date = new Date(v.createdAt);
          const hour = date.getHours();
          hourlyDistribution[hour] = (hourlyDistribution[hour] || 0) + 1;
        });
        
        const unusualHours = Object.entries(hourlyDistribution)
          .filter(([_, count]) => count === 1) // เกิดขึ้นเพียงครั้งเดียว
          .map(([hour]) => parseInt(hour));
          
        const timeAnomalies = verifications
          .filter(v => {
            if (!v.createdAt) return false;
            const date = new Date(v.createdAt);
            return unusualHours.includes(date.getHours());
          })
          .map(v => ({
            id: v.id,
            date: v.createdAt,
            hour: new Date(v.createdAt || new Date()).getHours(),
            amount: v.amount,
            bank: v.bankName,
            receiver: v.receiver
          }));
        
        correlationResults = {
          amountAnomalies,
          timeAnomalies,
          amountStatistics: {
            average: avgAmount,
            stdDev: amountStdDev,
            normalRange: {
              min: lowerBound,
              max: upperBound
            }
          },
          totalAnomalies: amountAnomalies.length + timeAnomalies.length
        };
        break;
      }
      
      default: {
        // ถ้าไม่ได้ระบุประเภทความสัมพันธ์ ให้สรุปทั่วไป
        const bankCorrelation: Record<string, {
          count: number,
          avgAmount: number,
          topReceiver?: { name: string, count: number }
        }> = {};
        
        // จัดกลุ่มข้อมูลธนาคารและผู้รับ
        verifications.forEach(v => {
          const bank = v.bankName || 'ไม่ระบุธนาคาร';
          const amount = v.amount || 0;
          const receiver = v.receiver || 'ไม่ระบุ';
          
          if (!bankCorrelation[bank]) {
            bankCorrelation[bank] = {
              count: 0,
              avgAmount: 0,
              receivers: {} as Record<string, number>
            };
          }
          
          bankCorrelation[bank].count++;
          bankCorrelation[bank].avgAmount += amount;
          
          // เก็บข้อมูลผู้รับ
          bankCorrelation[bank].receivers = bankCorrelation[bank].receivers || {};
          bankCorrelation[bank].receivers[receiver] = 
            (bankCorrelation[bank].receivers[receiver] || 0) + 1;
        });
        
        // คำนวณค่าเฉลี่ยและหาผู้รับที่พบบ่อยที่สุดสำหรับแต่ละธนาคาร
        Object.keys(bankCorrelation).forEach(bank => {
          const data = bankCorrelation[bank];
          data.avgAmount = data.count > 0 ? data.avgAmount / data.count : 0;
          
          // หาผู้รับที่พบบ่อยที่สุด
          if (data.receivers) {
            const sortedReceivers = Object.entries(data.receivers)
              .sort((a, b) => b[1] - a[1]);
              
            if (sortedReceivers.length > 0) {
              data.topReceiver = {
                name: sortedReceivers[0][0],
                count: sortedReceivers[0][1]
              };
            }
            
            // ลบข้อมูลตัวแปร receivers เพราะไม่ต้องการส่งข้อมูลทั้งหมด
            delete data.receivers;
          }
        });
        
        correlationResults = {
          bankReceiverCorrelation: bankCorrelation
        };
      }
    }
    
    // ส่งผลลัพธ์กลับไป
    res.json({
      success: true,
      timeframe: {
        start: fromDate,
        end: toDate
      },
      correlationType: correlationType || 'general',
      results: correlationResults,
      totalTransactions: verifications.length
    });
    
  } catch (error) {
    console.error('Error in transaction correlation analysis:', error);
    res.status(500).json({
      success: false,
      message: 'เกิดข้อผิดพลาดในการวิเคราะห์ความสัมพันธ์ของข้อมูล',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * API endpoint สำหรับการคาดการณ์การใช้งานในอนาคต (predictive analytics)
 */
router.post('/advanced-search/predictions', async (req: Request, res: Response) => {
  try {
    // ตรวจสอบสถานะการล็อกอิน
    if (!req.isAuthenticated()) {
      return res.status(401).json({
        success: false,
        message: 'กรุณาเข้าสู่ระบบก่อนใช้งาน',
      });
    }
    
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'ไม่สามารถระบุตัวตนผู้ใช้งานได้',
      });
    }
    
    // ดึงข้อมูลจาก request body
    const {
      predictionType,
      timeframe,
      dataPoints
    } = req.body;
    
    // ดึงช่วงเวลาสำหรับการวิเคราะห์ (ขึ้นอยู่กับประเภทของการคาดการณ์)
    const daysToAnalyze = parseInt(timeframe) || 90; // ค่าเริ่มต้น 90 วัน
    
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - daysToAnalyze);
    
    // ดึงข้อมูลรายการตรวจสอบสลิปในช่วงเวลาที่กำหนด
    const verifications = await db
      .select()
      .from(slipVerifications)
      .where(
        sql`${slipVerifications.userId} = ${userId} AND
            ${slipVerifications.createdAt} >= ${startDate} AND
            ${slipVerifications.createdAt} <= ${endDate}`
      )
      .orderBy(desc(slipVerifications.createdAt));
    
    // ตรวจสอบประเภทของการคาดการณ์
    let predictions: any = {};
    
    switch (predictionType) {
      case 'usage_forecast': {
        // พยากรณ์การใช้งานในอนาคต

        // จัดกลุ่มข้อมูลตามวันที่
        const dailyUsage: Record<string, number> = {};
        verifications.forEach(v => {
          if (!v.createdAt) return;
          
          const date = new Date(v.createdAt);
          const dateStr = date.toISOString().split('T')[0]; // แปลงเป็น YYYY-MM-DD
          
          dailyUsage[dateStr] = (dailyUsage[dateStr] || 0) + 1;
        });
        
        // คำนวณค่าเฉลี่ยการใช้งานต่อวัน
        const usageDays = Object.keys(dailyUsage).length;
        const totalUsage = verifications.length;
        const avgDailyUsage = usageDays > 0 ? totalUsage / usageDays : 0;
        
        // สร้างการคาดการณ์สำหรับ 7, 30 และ 90 วันข้างหน้า
        const predict7Days = Math.round(avgDailyUsage * 7);
        const predict30Days = Math.round(avgDailyUsage * 30);
        const predict90Days = Math.round(avgDailyUsage * 90);
        
        // หาแนวโน้มการใช้งาน (เพิ่มขึ้น/ลดลง)
        // แบ่งช่วงเวลาวิเคราะห์เป็น 2 ส่วนเท่าๆ กัน แล้วเปรียบเทียบ
        const halfDays = Math.floor(daysToAnalyze / 2);
        const firstHalfStart = new Date();
        firstHalfStart.setDate(firstHalfStart.getDate() - daysToAnalyze);
        
        const firstHalfEnd = new Date();
        firstHalfEnd.setDate(firstHalfEnd.getDate() - halfDays);
        
        const secondHalfStart = new Date(firstHalfEnd);
        const secondHalfEnd = new Date();
        
        // นับจำนวนรายการในแต่ละช่วง
        const firstHalfCount = verifications.filter(v => {
          if (!v.createdAt) return false;
          const date = new Date(v.createdAt);
          return date >= firstHalfStart && date < firstHalfEnd;
        }).length;
        
        const secondHalfCount = verifications.filter(v => {
          if (!v.createdAt) return false;
          const date = new Date(v.createdAt);
          return date >= secondHalfStart && date <= secondHalfEnd;
        }).length;
        
        // คำนวณการเปลี่ยนแปลง
        const usageChangePercent = firstHalfCount > 0 ? 
          ((secondHalfCount - firstHalfCount) / firstHalfCount) * 100 : 0;
        
        const trend = usageChangePercent > 5 ? 'เพิ่มขึ้น' :
                     usageChangePercent < -5 ? 'ลดลง' : 'คงที่';
        
        predictions = {
          historicalData: {
            daysAnalyzed: daysToAnalyze,
            totalTransactions: totalUsage,
            averageDailyUsage: avgDailyUsage,
            usageTrend: {
              trend,
              changePercent: usageChangePercent
            }
          },
          forecasts: {
            next7Days: predict7Days,
            next30Days: predict30Days,
            next90Days: predict90Days
          }
        };
        break;
      }
      
      case 'amount_prediction': {
        // พยากรณ์จำนวนเงินเฉลี่ยในอนาคต
        
        // จัดกลุ่มตามเดือน
        const monthlyAmounts: Record<string, number[]> = {};
        verifications.forEach(v => {
          if (!v.createdAt || !v.amount) return;
          
          const date = new Date(v.createdAt);
          const monthStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
          
          if (!monthlyAmounts[monthStr]) {
            monthlyAmounts[monthStr] = [];
          }
          monthlyAmounts[monthStr].push(v.amount);
        });
        
        // คำนวณค่าเฉลี่ยของแต่ละเดือน
        const monthlyAverages = Object.entries(monthlyAmounts).map(([month, amounts]) => {
          const total = amounts.reduce((sum, amt) => sum + amt, 0);
          return {
            month,
            averageAmount: total / amounts.length,
            transactionCount: amounts.length
          };
        }).sort((a, b) => a.month.localeCompare(b.month)); // เรียงตามเดือน
        
        // หาแนวโน้มของจำนวนเงินเฉลี่ย
        let amountTrend = 'คงที่';
        let trendPercent = 0;
        
        if (monthlyAverages.length >= 2) {
          const firstMonthAvg = monthlyAverages[0].averageAmount;
          const lastMonthAvg = monthlyAverages[monthlyAverages.length - 1].averageAmount;
          
          trendPercent = firstMonthAvg > 0 ? 
            ((lastMonthAvg - firstMonthAvg) / firstMonthAvg) * 100 : 0;
            
          amountTrend = trendPercent > 5 ? 'เพิ่มขึ้น' :
                        trendPercent < -5 ? 'ลดลง' : 'คงที่';
        }
        
        // พยากรณ์จำนวนเงินเฉลี่ยในอนาคต
        // ใช้ค่าเฉลี่ยเคลื่อนที่ (moving average) ของ 3 เดือนล่าสุด
        let predictedAmount;
        
        if (monthlyAverages.length >= 3) {
          const last3Months = monthlyAverages.slice(-3);
          const last3MonthsAvg = last3Months.reduce((sum, month) => 
            sum + month.averageAmount, 0) / 3;
            
          // ปรับด้วยแนวโน้ม
          const adjustmentFactor = 1 + (trendPercent / 100);
          predictedAmount = last3MonthsAvg * adjustmentFactor;
        } else if (monthlyAverages.length > 0) {
          // ถ้ามีข้อมูลน้อยกว่า 3 เดือน ใช้ข้อมูลที่มีอยู่
          const lastMonth = monthlyAverages[monthlyAverages.length - 1];
          predictedAmount = lastMonth.averageAmount;
        } else {
          predictedAmount = 0;
        }
        
        predictions = {
          historicalData: {
            monthlyAverages,
            amountTrend: {
              trend: amountTrend,
              changePercent: trendPercent
            }
          },
          predictedAmount: {
            nextMonth: predictedAmount,
            nextQuarter: predictedAmount * 3 // ประมาณการใช้จ่ายใน 3 เดือนข้างหน้า
          }
        };
        break;
      }
      
      case 'recurring_prediction': {
        // พยากรณ์รายการที่จะเกิดขึ้นซ้ำ
        
        // จัดกลุ่มตามผู้รับและจำนวนเงิน
        interface RecurringPattern {
          receivers: Record<string, {
            transactions: any[],
            amounts: Record<number, number> // จำนวนเงิน -> จำนวนครั้ง
          }>
        }
        
        const patterns: RecurringPattern = {
          receivers: {}
        };
        
        verifications.forEach(v => {
          if (!v.receiver || !v.amount) return;
          
          const receiver = v.receiver;
          const amount = v.amount;
          
          if (!patterns.receivers[receiver]) {
            patterns.receivers[receiver] = {
              transactions: [],
              amounts: {}
            };
          }
          
          patterns.receivers[receiver].transactions.push({
            id: v.id,
            date: v.createdAt,
            amount,
            bank: v.bankName
          });
          
          patterns.receivers[receiver].amounts[amount] = 
            (patterns.receivers[receiver].amounts[amount] || 0) + 1;
        });
        
        // หารูปแบบการทำรายการที่ซ้ำกัน
        const recurringPredictions: any[] = [];
        
        Object.entries(patterns.receivers).forEach(([receiver, data]) => {
          // ตรวจสอบว่ามีรายการมากกว่า 1 รายการ
          if (data.transactions.length > 1) {
            // ดูว่ามีจำนวนเงินที่ทำซ้ำๆ หรือไม่
            const commonAmounts = Object.entries(data.amounts)
              .filter(([_, count]) => count > 1)
              .map(([amount, count]) => ({
                amount: parseFloat(amount),
                count
              }))
              .sort((a, b) => b.count - a.count);
            
            if (commonAmounts.length > 0) {
              // มีรูปแบบการทำรายการซ้ำๆ
              const mostCommonAmount = commonAmounts[0];
              
              // กรองเฉพาะรายการที่มีจำนวนเงินเท่ากับ mostCommonAmount
              const matchingTransactions = data.transactions
                .filter(t => t.amount === mostCommonAmount.amount)
                .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
              
              if (matchingTransactions.length > 1) {
                // คำนวณช่วงเวลาระหว่างรายการ
                const intervals: number[] = [];
                for (let i = 1; i < matchingTransactions.length; i++) {
                  const current = new Date(matchingTransactions[i].date).getTime();
                  const previous = new Date(matchingTransactions[i-1].date).getTime();
                  const diffDays = (current - previous) / (1000 * 60 * 60 * 24);
                  intervals.push(diffDays);
                }
                
                // คำนวณช่วงเวลาเฉลี่ย
                const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
                
                // ตรวจสอบว่าเป็นรูปแบบที่สม่ำเสมอหรือไม่ (ช่วงห่างไม่เกิน 20%)
                const isRegular = intervals.length > 0 && 
                  intervals.every(interval => Math.abs(interval - avgInterval) <= (avgInterval * 0.2));
                
                if (isRegular) {
                  // คาดการณ์วันที่ของรายการถัดไป
                  const lastTransactionDate = new Date(matchingTransactions[matchingTransactions.length - 1].date);
                  const nextPredictedDate = new Date(lastTransactionDate);
                  nextPredictedDate.setDate(nextPredictedDate.getDate() + Math.round(avgInterval));
                  
                  // เพิ่มการคาดการณ์
                  recurringPredictions.push({
                    receiver,
                    amount: mostCommonAmount.amount,
                    frequency: `ทุก ${Math.round(avgInterval)} วัน`,
                    confidence: (mostCommonAmount.count / data.transactions.length) * 100,
                    nextPredictedDate,
                    averageIntervalDays: avgInterval,
                    historicalTransactions: matchingTransactions.slice(-3) // แสดงเฉพาะ 3 รายการล่าสุด
                  });
                }
              }
            }
          }
        });
        
        predictions = {
          recurringPredictions,
          predictionCount: recurringPredictions.length
        };
        break;
      }
      
      default:
        // ถ้าไม่ระบุประเภทการคาดการณ์ ให้ส่งข้อความแจ้งว่าไม่สนับสนุน
        return res.status(400).json({
          success: false,
          message: 'ไม่สนับสนุนประเภทการคาดการณ์ที่ระบุ',
          supportedTypes: ['usage_forecast', 'amount_prediction', 'recurring_prediction']
        });
    }
    
    console.log('ส่งผลลัพธ์การวิเคราะห์: พยากรณ์ข้อมูล');
    
    // ส่งข้อมูลในรูปแบบที่มีข้อมูลที่ UI ต้องการ
    res.json({
      success: true,
      timeframe: {
        start: startDate,
        end: endDate,
        daysAnalyzed: daysToAnalyze
      },
      predictionType,
      predictions,
      transactionCount: verifications.length,
      // เพิ่มข้อมูลที่ UI ต้องการ
      totalAmount: totalAmount || 0,
      avgAmount: avgAmount || 0,
      totalTransactions: verifications.length,
      bankDistribution: bankDistribution || [],
      timeDistribution: timeOfDayDistribution || [],
      topReceivers: topReceivers || [],
      monthlyData: monthlyData || [],
      transactions: includeDetails ? details : []
    });
    
  } catch (error) {
    console.error('Error in predictive analytics:', error);
    res.status(500).json({
      success: false,
      message: 'เกิดข้อผิดพลาดในการวิเคราะห์และคาดการณ์ข้อมูล',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// ฟังก์ชันสำหรับเพิ่ม routes เข้าไปใน Express app
export function setupAdvancedUserAnalyticsRoutes(app: Express) {
  app.use('/api', router);
}

export default setupAdvancedUserAnalyticsRoutes;