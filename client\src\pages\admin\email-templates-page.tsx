import React, { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Helmet } from "react-helmet";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { 
  Card, CardContent, CardDescription, 
  CardFooter, CardHeader, CardTitle 
} from "@/components/ui/card";
import {
  Table, TableBody, TableCaption, TableCell,
  TableHead, TableHeader, TableRow
} from "@/components/ui/table";
import { 
  Dialog, DialogClose, DialogContent, DialogDescription, 
  DialogFooter, DialogHeader, DialogTitle, DialogTrigger 
} from "@/components/ui/dialog";
import {
  Tabs, TabsContent, TabsList, TabsTrigger
} from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { <PERSON><PERSON><PERSON><PERSON>cle, CheckCircle, Edit, Plus, Send, Trash2, RefreshCw } from "lucide-react";
import { Toast } from "@/components/ui/toast";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient } from "@/lib/queryClient";

import AdminLayout from "@/components/layouts/admin-layout";

// ประเภทข้อมูล EmailTemplate
interface EmailTemplate {
  id: number;
  name: string;
  subject: string;
  htmlContent: string;
  textContent: string;
  variables: string[];
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

// ฟอร์มสำหรับสร้าง/แก้ไขเทมเพลตอีเมล
interface EmailTemplateForm {
  name: string;
  subject: string;
  htmlContent: string;
  textContent: string;
  variables: string[];
  isDefault: boolean;
}

// ฟอร์มสำหรับการทดสอบส่งอีเมล
interface TestEmailForm {
  to: string;
  templateId: number;
  variables: Record<string, string>;
}

const EmailTemplatesPage: React.FC = () => {
  const { toast } = useToast();
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplate | null>(null);
  const [templateForm, setTemplateForm] = useState<EmailTemplateForm>({
    name: "",
    subject: "",
    htmlContent: "",
    textContent: "",
    variables: [],
    isDefault: false
  });
  const [testEmailForm, setTestEmailForm] = useState<TestEmailForm>({
    to: "",
    templateId: 0,
    variables: {}
  });
  const [formTab, setFormTab] = useState<"html" | "text">("html");
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showPreviewDialog, setShowPreviewDialog] = useState(false);

  // ดึงข้อมูลเทมเพลตอีเมลทั้งหมด
  const { data: templates, isLoading, refetch } = useQuery({
    queryKey: ["/api/admin/email-templates"],
    onError: (error: any) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: `ไม่สามารถดึงข้อมูลเทมเพลตอีเมลได้: ${error.message}`,
        variant: "destructive",
      });
    }
  });

  // สร้างเทมเพลตอีเมลใหม่
  const createTemplateMutation = useMutation({
    mutationFn: async (templateData: EmailTemplateForm) => {
      const res = await apiRequest("POST", "/api/admin/email-templates", templateData);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "สร้างเทมเพลตอีเมลสำเร็จ",
        description: "เทมเพลตอีเมลใหม่ถูกสร้างเรียบร้อยแล้ว",
        variant: "default",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/admin/email-templates"] });
      resetForm();
    },
    onError: (error: any) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: `ไม่สามารถสร้างเทมเพลตอีเมลได้: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  // อัปเดตเทมเพลตอีเมล
  const updateTemplateMutation = useMutation({
    mutationFn: async ({ id, templateData }: { id: number; templateData: EmailTemplateForm }) => {
      const res = await apiRequest("PUT", `/api/admin/email-templates/${id}`, templateData);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "อัปเดตเทมเพลตอีเมลสำเร็จ",
        description: "เทมเพลตอีเมลถูกอัปเดตเรียบร้อยแล้ว",
        variant: "default",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/admin/email-templates"] });
      resetForm();
    },
    onError: (error: any) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: `ไม่สามารถอัปเดตเทมเพลตอีเมลได้: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  // ลบเทมเพลตอีเมล
  const deleteTemplateMutation = useMutation({
    mutationFn: async (id: number) => {
      const res = await apiRequest("DELETE", `/api/admin/email-templates/${id}`);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "ลบเทมเพลตอีเมลสำเร็จ",
        description: "เทมเพลตอีเมลถูกลบเรียบร้อยแล้ว",
        variant: "default",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/admin/email-templates"] });
      setShowDeleteDialog(false);
      resetForm();
    },
    onError: (error: any) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: `ไม่สามารถลบเทมเพลตอีเมลได้: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  // ส่งอีเมลทดสอบ
  const sendTestEmailMutation = useMutation({
    mutationFn: async (testData: TestEmailForm) => {
      const res = await apiRequest("POST", `/api/admin/send-test-email`, testData);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "ส่งอีเมลทดสอบสำเร็จ",
        description: "อีเมลทดสอบถูกส่งเรียบร้อยแล้ว โปรดตรวจสอบกล่องข้อความของคุณ",
        variant: "default",
      });
    },
    onError: (error: any) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: `ไม่สามารถส่งอีเมลทดสอบได้: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  // รีเซ็ตฟอร์ม
  const resetForm = () => {
    setIsEditMode(false);
    setSelectedTemplate(null);
    setTemplateForm({
      name: "",
      subject: "",
      htmlContent: "",
      textContent: "",
      variables: [],
      isDefault: false
    });
    setFormTab("html");
  };

  // เปิดฟอร์มแก้ไข
  const handleEdit = (template: EmailTemplate) => {
    setIsEditMode(true);
    setSelectedTemplate(template);
    setTemplateForm({
      name: template.name,
      subject: template.subject,
      htmlContent: template.htmlContent,
      textContent: template.textContent,
      variables: template.variables,
      isDefault: template.isDefault
    });

    // เตรียมข้อมูลสำหรับทดสอบส่งอีเมล
    const initialVars: Record<string, string> = {};
    template.variables.forEach(v => {
      initialVars[v] = `ตัวอย่าง ${v}`;
    });

    setTestEmailForm({
      to: "",
      templateId: template.id,
      variables: initialVars
    });
  };

  // ส่งฟอร์ม
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!templateForm.name || !templateForm.subject || !templateForm.htmlContent) {
      toast({
        title: "กรุณากรอกข้อมูลให้ครบถ้วน",
        description: "ชื่อเทมเพลต หัวข้อ และเนื้อหา HTML เป็นข้อมูลที่จำเป็น",
        variant: "destructive",
      });
      return;
    }

    if (isEditMode && selectedTemplate) {
      updateTemplateMutation.mutate({
        id: selectedTemplate.id,
        templateData: templateForm
      });
    } else {
      createTemplateMutation.mutate(templateForm);
    }
  };

  // ส่งอีเมลทดสอบ
  const handleSendTestEmail = (e: React.FormEvent) => {
    e.preventDefault();

    if (!testEmailForm.to || !testEmailForm.templateId) {
      toast({
        title: "กรุณากรอกข้อมูลให้ครบถ้วน",
        description: "อีเมลผู้รับและเทมเพลตเป็นข้อมูลที่จำเป็น",
        variant: "destructive",
      });
      return;
    }

    sendTestEmailMutation.mutate(testEmailForm);
  };

  // แปลงข้อมูลตัวแปรให้อยู่ในรูปแบบที่เหมาะสม
  const handleVariablesChange = (inputValue: string) => {
    // แยกด้วย comma และลบช่องว่าง
    const variablesArray = inputValue.split(',').map(v => v.trim()).filter(v => v);
    setTemplateForm({
      ...templateForm,
      variables: variablesArray
    });
  };
  
  // เตรียมข้อมูลตัวแปรสำหรับแสดงในฟอร์ม
  const displayVariables = templateForm.variables.join(', ');

  return (
    <AdminLayout>
      <Helmet>
        <title>จัดการเทมเพลตอีเมล | SLIPKUY - ระบบตรวจสอบสลิป</title>
      </Helmet>
      
      <div className="container mx-auto p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-600 bg-clip-text text-transparent">
              จัดการเทมเพลตอีเมล
            </h1>
            <p className="text-gray-400 mt-1">
              สร้างและจัดการเทมเพลตสำหรับการส่งอีเมลอัตโนมัติ
            </p>
          </div>
          <Button 
            onClick={resetForm}
            className="bg-gradient-to-r from-indigo-500 to-purple-600"
          >
            <Plus className="mr-2 h-4 w-4" />
            สร้างเทมเพลตใหม่
          </Button>
        </div>

        <Separator className="my-6" />
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* รายการเทมเพลตอีเมล */}
          <div className="lg:col-span-2">
            <Card className="bg-gray-950 border-gray-800 shadow-lg">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle className="text-xl font-semibold text-gray-100">รายการเทมเพลตอีเมล</CardTitle>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => refetch()}
                    className="text-gray-300 border-gray-700"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    รีเฟรช
                  </Button>
                </div>
                <CardDescription className="text-gray-400">
                  เทมเพลตทั้งหมดที่ใช้ในระบบการส่งอีเมลอัตโนมัติ
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="py-8 text-center text-gray-400">
                    <RefreshCw className="h-8 w-8 mx-auto animate-spin mb-2" />
                    กำลังโหลดข้อมูล...
                  </div>
                ) : templates && templates.length > 0 ? (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow className="border-gray-800 hover:bg-gray-900">
                          <TableHead className="text-gray-300">ชื่อเทมเพลต</TableHead>
                          <TableHead className="text-gray-300">หัวข้อ</TableHead>
                          <TableHead className="text-gray-300 text-center">ค่าเริ่มต้น</TableHead>
                          <TableHead className="text-gray-300 text-right">การดำเนินการ</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {templates.map((template: EmailTemplate) => (
                          <TableRow key={template.id} className="border-gray-800 hover:bg-gray-900">
                            <TableCell className="font-medium text-gray-200">{template.name}</TableCell>
                            <TableCell className="text-gray-300">{template.subject}</TableCell>
                            <TableCell className="text-center">
                              {template.isDefault ? (
                                <CheckCircle className="h-5 w-5 text-green-500 mx-auto" />
                              ) : (
                                <span className="text-gray-500">-</span>
                              )}
                            </TableCell>
                            <TableCell className="text-right space-x-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEdit(template)}
                                className="text-blue-400 hover:text-blue-300 hover:bg-blue-950"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  setSelectedTemplate(template);
                                  setShowDeleteDialog(true);
                                }}
                                className="text-red-400 hover:text-red-300 hover:bg-red-950"
                                disabled={template.isDefault}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ) : (
                  <div className="py-8 text-center text-gray-400 border border-dashed border-gray-800 rounded-lg">
                    <AlertCircle className="h-8 w-8 mx-auto mb-2" />
                    <p>ไม่พบเทมเพลตอีเมล กรุณาสร้างเทมเพลตใหม่</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* ฟอร์มสร้าง/แก้ไขเทมเพลต */}
          <div className="lg:col-span-1">
            <Card className="bg-gray-950 border-gray-800 shadow-lg">
              <CardHeader>
                <CardTitle className="text-xl font-semibold text-gray-100">
                  {isEditMode ? "แก้ไขเทมเพลตอีเมล" : "สร้างเทมเพลตอีเมล"}
                </CardTitle>
                <CardDescription className="text-gray-400">
                  {isEditMode 
                    ? "แก้ไขและอัปเดตเทมเพลตอีเมลที่มีอยู่" 
                    : "สร้างเทมเพลตอีเมลใหม่สำหรับระบบ"}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name" className="text-gray-300">ชื่อเทมเพลต</Label>
                    <Input 
                      id="name"
                      value={templateForm.name}
                      onChange={(e) => setTemplateForm({...templateForm, name: e.target.value})}
                      placeholder="เช่น welcome_email, verification_success"
                      className="bg-gray-900 border-gray-700 text-gray-200"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="subject" className="text-gray-300">หัวข้ออีเมล</Label>
                    <Input 
                      id="subject"
                      value={templateForm.subject}
                      onChange={(e) => setTemplateForm({...templateForm, subject: e.target.value})}
                      placeholder="เช่น ยินดีต้อนรับสู่ SLIPKUY"
                      className="bg-gray-900 border-gray-700 text-gray-200"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="variables" className="text-gray-300">ตัวแปรที่ใช้ในเทมเพลต (คั่นด้วยเครื่องหมายจุลภาค)</Label>
                    <Input 
                      id="variables"
                      value={displayVariables}
                      onChange={(e) => handleVariablesChange(e.target.value)}
                      placeholder="เช่น userName, amount, date"
                      className="bg-gray-900 border-gray-700 text-gray-200"
                    />
                    <p className="text-xs text-gray-500">ตัวอย่าง: userName, transactionRef, amount</p>
                  </div>
                  
                  <div className="space-y-2">
                    <Tabs defaultValue="html" value={formTab} onValueChange={(value) => setFormTab(value as "html" | "text")}>
                      <div className="flex justify-between items-center mb-2">
                        <Label className="text-gray-300">เนื้อหาอีเมล</Label>
                        <TabsList className="bg-gray-900">
                          <TabsTrigger 
                            value="html" 
                            className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white"
                          >
                            HTML
                          </TabsTrigger>
                          <TabsTrigger 
                            value="text" 
                            className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white"
                          >
                            Text
                          </TabsTrigger>
                        </TabsList>
                      </div>
                      
                      <TabsContent value="html">
                        <Textarea 
                          value={templateForm.htmlContent}
                          onChange={(e) => setTemplateForm({...templateForm, htmlContent: e.target.value})}
                          placeholder="<div>เนื้อหา HTML ของอีเมล</div>"
                          className="min-h-[200px] bg-gray-900 border-gray-700 text-gray-200"
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          ใช้ {{ชื่อตัวแปร}} เพื่อแทนที่ด้วยข้อมูลจริง เช่น {{userName}}
                        </p>
                      </TabsContent>
                      
                      <TabsContent value="text">
                        <Textarea 
                          value={templateForm.textContent}
                          onChange={(e) => setTemplateForm({...templateForm, textContent: e.target.value})}
                          placeholder="เนื้อหาข้อความธรรมดาของอีเมล"
                          className="min-h-[200px] bg-gray-900 border-gray-700 text-gray-200"
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          ใช้ {{ชื่อตัวแปร}} เพื่อแทนที่ด้วยข้อมูลจริง เช่น {{userName}}
                        </p>
                      </TabsContent>
                    </Tabs>
                  </div>
                  
                  <div className="flex items-center space-x-2 pt-2">
                    <Switch
                      id="isDefault"
                      checked={templateForm.isDefault}
                      onCheckedChange={(checked) => setTemplateForm({...templateForm, isDefault: checked})}
                    />
                    <Label htmlFor="isDefault" className="text-gray-300">กำหนดเป็นเทมเพลตเริ่มต้น</Label>
                  </div>
                  
                  <div className="flex justify-between pt-4">
                    <Button 
                      type="button" 
                      variant="outline" 
                      onClick={resetForm}
                      className="border-gray-700 text-gray-300"
                    >
                      ยกเลิก
                    </Button>
                    <Button 
                      type="submit"
                      className="bg-gradient-to-r from-indigo-500 to-purple-600"
                    >
                      {isEditMode ? "อัปเดตเทมเพลต" : "สร้างเทมเพลต"}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
            
            {/* ส่วนทดสอบส่งอีเมล (แสดงเฉพาะเมื่ออยู่ในโหมดแก้ไข) */}
            {isEditMode && selectedTemplate && (
              <Card className="bg-gray-950 border-gray-800 shadow-lg mt-6">
                <CardHeader>
                  <CardTitle className="text-xl font-semibold text-gray-100">
                    ทดสอบส่งอีเมล
                  </CardTitle>
                  <CardDescription className="text-gray-400">
                    ทดสอบส่งอีเมลโดยใช้เทมเพลตที่เลือก
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSendTestEmail} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="testEmail" className="text-gray-300">อีเมลผู้รับ</Label>
                      <Input 
                        id="testEmail"
                        type="email"
                        value={testEmailForm.to}
                        onChange={(e) => setTestEmailForm({...testEmailForm, to: e.target.value})}
                        placeholder="อีเมลสำหรับทดสอบ"
                        className="bg-gray-900 border-gray-700 text-gray-200"
                      />
                    </div>
                    
                    {/* แสดงฟอร์มสำหรับใส่ค่าตัวแปร */}
                    {selectedTemplate.variables.length > 0 && (
                      <div className="space-y-3">
                        <Label className="text-gray-300">ตัวแปรที่ใช้ในเทมเพลต</Label>
                        {selectedTemplate.variables.map((variable) => (
                          <div key={variable} className="space-y-1">
                            <Label htmlFor={`var-${variable}`} className="text-sm text-gray-400">
                              {variable}
                            </Label>
                            <Input 
                              id={`var-${variable}`}
                              value={testEmailForm.variables[variable] || ''}
                              onChange={(e) => setTestEmailForm({
                                ...testEmailForm, 
                                variables: {
                                  ...testEmailForm.variables,
                                  [variable]: e.target.value
                                }
                              })}
                              placeholder={`ค่าสำหรับ ${variable}`}
                              className="bg-gray-900 border-gray-700 text-gray-200"
                            />
                          </div>
                        ))}
                      </div>
                    )}
                    
                    <div className="pt-2">
                      <Button 
                        type="submit"
                        className="w-full bg-gradient-to-r from-blue-500 to-indigo-600"
                      >
                        <Send className="mr-2 h-4 w-4" />
                        ส่งอีเมลทดสอบ
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
      
      {/* Modal ยืนยันการลบ */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="bg-gray-950 border-gray-800 text-gray-200">
          <DialogHeader>
            <DialogTitle className="text-xl text-gray-100">ยืนยันการลบเทมเพลต</DialogTitle>
            <DialogDescription className="text-gray-400">
              คุณแน่ใจหรือไม่ว่าต้องการลบเทมเพลต "{selectedTemplate?.name}"?
              การดำเนินการนี้ไม่สามารถเรียกคืนได้
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="pt-4">
            <DialogClose asChild>
              <Button variant="outline" className="border-gray-700 text-gray-300">
                ยกเลิก
              </Button>
            </DialogClose>
            <Button 
              variant="destructive" 
              onClick={() => selectedTemplate && deleteTemplateMutation.mutate(selectedTemplate.id)}
              className="bg-gradient-to-r from-red-500 to-red-700"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              ยืนยันการลบ
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
};

export default EmailTemplatesPage;