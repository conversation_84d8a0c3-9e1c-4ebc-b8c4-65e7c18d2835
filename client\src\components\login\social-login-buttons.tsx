import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Facebook, Mail, AlertTriangle, Loader2 } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Card } from "@/components/ui/card";
import axios from 'axios';

type SocialSettings = {
  enableLineLogin?: boolean;
  enableFacebookLogin?: boolean; 
  enableGoogleLogin?: boolean;
};

export function SocialLoginButtons() {
  const [settings, setSettings] = useState<SocialSettings>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setLoading(true);
        const response = await axios.get('/api/auth/settings/social');
        setSettings(response.data || {});
        setError(null);
      } catch (err) {
        console.error('ไม่สามารถโหลดการตั้งค่า Social Login:', err);
        setError('ไม่สามารถโหลดการตั้งค่า Social Login');
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center py-4">
        <Loader2 className="h-6 w-6 text-amber-500 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className="my-4 bg-red-950/30 border-red-800 border">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  // ถ้าไม่มี social login ที่เปิดใช้งาน
  if (!settings.enableLineLogin && !settings.enableFacebookLogin && !settings.enableGoogleLogin) {
    return null;
  }

  return (
    <Card className="p-4 bg-indigo-950/50 border-amber-500/20 space-y-3 my-6">
      <div className="text-center mb-2">
        <p className="text-indigo-300">หรือ เข้าสู่ระบบด้วย</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
        {settings.enableLineLogin && (
          <Button 
            variant="outline" 
            className="bg-green-600 hover:bg-green-700 text-white border-none"
            onClick={() => window.location.href = '/api/social/line/auth'}
          >
            <div className="h-5 w-5 mr-2 flex items-center justify-center text-lg">L</div>
            LINE
          </Button>
        )}
        
        {settings.enableFacebookLogin && (
          <Button 
            variant="outline" 
            className="bg-blue-600 hover:bg-blue-700 text-white border-none"
            onClick={() => window.location.href = '/api/social/facebook/auth'}
          >
            <Facebook className="h-5 w-5 mr-2" />
            Facebook
          </Button>
        )}
        
        {settings.enableGoogleLogin && (
          <Button 
            variant="outline" 
            className="bg-red-600 hover:bg-red-700 text-white border-none"
            onClick={() => window.location.href = '/api/social/google/auth'}
          >
            <Mail className="h-5 w-5 mr-2" />
            Google
          </Button>
        )}
      </div>
    </Card>
  );
}