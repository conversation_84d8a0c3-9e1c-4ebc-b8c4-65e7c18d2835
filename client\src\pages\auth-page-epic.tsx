import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/hooks/use-auth";
import { insertUserSchema } from "@shared/schema";
import { Sword, Sparkles, Skull, Zap, Shield, Crown, BoltIcon, Laugh, FlameKindling } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

// กำหนด Schema สำหรับการเข้าสู่ระบบ
const loginSchema = z.object({
  username: z.string().min(1, "ชื่อผู้ใช้หายไปไหน? เทพเจ้ารอคอยอยู่!"),
  password: z.string().min(1, "รหัสผ่านล่ะ? ฉันไม่สามารถอ่านใจเทพคุณได้นะ!"),
});

// กำหนด Schema สำหรับการลงทะเบียน (โดยใช้ Schema ที่มีอยู่แล้ว)
const registerSchema = insertUserSchema.extend({
  password: z.string().min(8, "รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร (เพื่อป้องกันมนุษย์ธรรมดา)"),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "รหัสผ่านไม่ตรงกัน! คุณหลงทางในวังวนแห่งอักขระหรือไร?",
  path: ["confirmPassword"],
});

type LoginFormValues = z.infer<typeof loginSchema>;
type RegisterFormValues = z.infer<typeof registerSchema>;

// คำคมกวนๆ สำหรับแสดงสุ่ม
const epicQuotes = [
  "หากท่านล็อกอินสำเร็จ จะได้รับพลังเทพเจ้า... แต่ถ้าลืมรหัสผ่าน ก็จำไว้ว่าท่านไม่ใช่เทพ!",
  "เข้าสู่ระบบวันนี้ รับฟรี! ความอลังการของระบบ (ไม่มีค่าส่ง)",
  "แม้แต่เทพเจ้ายังต้องล็อกอินก่อนเปิดสวรรค์",
  "รหัสผ่านของท่านปลอดภัยกับเรา... เทพเจ้าสาบานว่าไม่แอบดู (จริงๆนะ)",
  "ลงทะเบียนวันนี้ ได้สิทธิ์ลงชื่อบนกำแพงแห่งเทพเจ้า (เป็นไม้หมายเหตุชั้นล่างสุด)",
  "อย่ากรอกรหัสผิด 3 ครั้ง เดี๋ยวเทพเจ้าจะหัวเราะเยาะท่าน (ในที่ลับหลัง)",
  "กรอกข้อมูลให้ถูกต้อง หรือจะถูกส่งไปพบยมทูต (แปลว่าหน้า login อีกรอบนั่นแหละ)",
  "ท่านคือผู้ถูกเลือก... ให้กรอกฟอร์มนี้",
  "ระบบนี้สร้างโดยโปรแกรมเมอร์ที่อดนอน เคารพเขาด้วยการกรอกฟอร์มให้ถูกต้อง",
];

export default function AuthPage() {
  const [activeTab, setActiveTab] = useState<"login" | "register">("login");
  const [location, setLocation] = useLocation();
  const { user, loginMutation, registerMutation } = useAuth();
  const [quote, setQuote] = useState<string>(epicQuotes[Math.floor(Math.random() * epicQuotes.length)]);
  const [lightningEffect, setLightningEffect] = useState(false);

  // สร้าง Form สำหรับการเข้าสู่ระบบ
  const loginForm = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: "",
      password: "",
    },
  });

  // สร้าง Form สำหรับการลงทะเบียน
  const registerForm = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      username: "",
      email: "",
      firstName: "",
      lastName: "",
      companyName: "",
      password: "",
      confirmPassword: "",
    },
  });

  // เปลี่ยนคำคมเมื่อเปลี่ยนแท็บ
  useEffect(() => {
    setQuote(epicQuotes[Math.floor(Math.random() * epicQuotes.length)]);
    
    // สร้างเอฟเฟกต์ฟ้าแลบเมื่อเปลี่ยนแท็บ
    setLightningEffect(true);
    const timer = setTimeout(() => setLightningEffect(false), 500);
    return () => clearTimeout(timer);
  }, [activeTab]);

  // ส่งคำขอเข้าสู่ระบบ
  const onLoginSubmit = (values: LoginFormValues) => {
    loginMutation.mutate(values);
  };

  // ส่งคำขอลงทะเบียน
  const onRegisterSubmit = (values: RegisterFormValues) => {
    // ลบ confirmPassword ออกเพราะไม่ต้องส่งไปยัง API
    const { confirmPassword, ...registerData } = values;
    registerMutation.mutate(registerData);
  };

  // เมื่อมีผู้ใช้งานเข้าสู่ระบบแล้ว ให้ไปยังหน้าแดชบอร์ด
  useEffect(() => {
    if (user) {
      setLocation("/dashboard");
    }
  }, [user, setLocation]);

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-purple-950 via-indigo-900 to-purple-950 relative">
      
      {/* เอฟเฟกต์พื้นหลังอลังการ */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* ดาวระยิบระยับในจักรวาล */}
        <div className="absolute inset-0" style={{ 
          backgroundImage: 'radial-gradient(white, rgba(255, 255, 255, 0.2) 2px, transparent 2px)',
          backgroundSize: '50px 50px'
        }}></div>
        
        {/* วงแหวนพลังเทพเจ้า */}
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[150vh] h-[150vh]">
          <div className="absolute inset-0 rounded-full border-8 border-amber-400/10 animate-[spin_120s_linear_infinite]"></div>
          <div className="absolute inset-10 rounded-full border-4 border-pink-500/10 animate-[spin_100s_linear_infinite_reverse]"></div>
          <div className="absolute inset-20 rounded-full border-2 border-blue-400/10 animate-[spin_80s_linear_infinite]"></div>
        </div>
        
        {/* คลื่นพลังงานเทพเจ้า */}
        <div className="absolute bottom-0 inset-x-0 h-48 bg-gradient-to-t from-pink-500/20 to-transparent"></div>
        
        {/* ฟ้าแลบที่มุมจอ */}
        <div className="absolute top-1/4 right-10 w-20 h-40">
          <motion.div 
            className="absolute inset-0 bg-yellow-300" 
            initial={{ opacity: 0 }}
            animate={{ 
              opacity: [0, 0.8, 0],
              rotate: [0, 5, 0],
              pathLength: [0, 1, 0]
            }}
            transition={{ 
              duration: 1.5, 
              repeat: Infinity, 
              repeatDelay: 7
            }}
            style={{ clipPath: "polygon(50% 0%, 15% 50%, 50% 50%, 0% 100%, 50% 70%, 50% 50%, 85% 100%)" }}
          />
        </div>
        
        {/* เฮลิกซ์พลังงานลึกลับ */}
        <div className="absolute top-[10%] left-[5%] w-20 h-80 origin-center">
          <motion.div
            className="absolute top-0 left-0 right-0 h-6 rounded-full bg-purple-500/30 blur-sm"
            animate={{ y: [0, 80, 0], opacity: [0.2, 0.8, 0.2] }}
            transition={{ duration: 5, repeat: Infinity, ease: "easeInOut" }}
          />
          <motion.div
            className="absolute top-20 left-0 right-0 h-6 rounded-full bg-indigo-500/30 blur-sm"
            animate={{ y: [80, 0, 80], opacity: [0.2, 0.8, 0.2] }}
            transition={{ duration: 5, repeat: Infinity, ease: "easeInOut", delay: 1 }}
          />
        </div>
        
        {/* ฟ้าแลบเมื่อเปลี่ยนแท็บ */}
        <AnimatePresence>
          {lightningEffect && (
            <motion.div 
              initial={{ opacity: 0 }}
              animate={{ opacity: 0.7 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="absolute inset-0 bg-white pointer-events-none"
            />
          )}
        </AnimatePresence>
      </div>

      <main className="relative z-10 flex-1 flex flex-col items-center justify-center p-4 mt-14">
        {/* ส่วนหัว */}
        <motion.div 
          className="text-center mb-8 relative"
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ type: "spring", stiffness: 200, damping: 20 }}
        >
          <div className="relative inline-block">
            <motion.div 
              className="absolute -inset-4 rounded-full bg-gradient-to-r from-amber-600/30 via-pink-500/30 to-purple-600/30 blur-2xl"
              animate={{ 
                scale: [1, 1.1, 1],
                rotate: [0, 5, 0]
              }}
              transition={{ 
                duration: 5, 
                repeat: Infinity,
                repeatType: "reverse"
              }}
            />
            <h1 className="text-6xl font-bold bg-gradient-to-r from-amber-300 via-yellow-200 to-amber-400 bg-clip-text text-transparent font-serif relative">
              SLIPKUY
              <motion.div 
                className="absolute -right-8 -top-8 text-amber-300"
                animate={{ rotate: 360 }}
                transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
              >
                <Crown className="h-10 w-10" />
              </motion.div>
            </h1>
          </div>
          <p className="mt-2 text-xl text-fuchsia-200 font-semibold">ระบบตรวจสอบสลิปแห่งเทพเจ้า</p>
          
          {/* คำคมกวนๆ */}
          <motion.div 
            className="mt-4 max-w-xl mx-auto bg-indigo-950/50 p-3 rounded-lg border border-indigo-800/50 relative overflow-hidden group"
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 via-fuchsia-600/5 to-amber-600/10 group-hover:opacity-80 transition-opacity" />
            <Laugh className="h-5 w-5 text-amber-300 inline-block mr-2 mb-1" />
            <span className="italic text-indigo-200">{quote}</span>
            <motion.div
              className="absolute bottom-0 left-0 h-[2px] bg-gradient-to-r from-amber-400 via-fuchsia-500 to-purple-500"
              initial={{ width: "0%" }}
              animate={{ width: "100%" }}
              transition={{ duration: 3 }}
            />
          </motion.div>
        </motion.div>
        
        {/* กล่องล็อกอิน */}
        <motion.div 
          className="w-full max-w-md"
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ 
            type: "spring", 
            stiffness: 200, 
            damping: 20,
            delay: 0.2
          }}
        >
          <div className="relative group">
            {/* เอฟเฟกต์เรืองแสงรอบกล่อง */}
            <div className="absolute -inset-1 bg-gradient-to-r from-pink-600 via-purple-600 to-indigo-600 rounded-xl blur-md opacity-70 group-hover:opacity-100 transition duration-300"></div>
            
            <div className="relative bg-gray-950/90 backdrop-blur-md rounded-lg border border-gray-800 overflow-hidden shadow-[0_0_30px_5px_rgba(168,85,247,0.2)]">
              {/* แท็บเปลี่ยนหน้า */}
              <div className="flex border-b border-gray-800 bg-gray-900/80">
                <button 
                  onClick={() => setActiveTab("login")}
                  className={`flex-1 py-4 text-center font-bold transition flex items-center justify-center gap-2 ${
                    activeTab === "login"
                      ? "bg-gradient-to-b from-amber-500/20 to-gray-900/20 text-amber-300"
                      : "text-gray-400 hover:text-gray-300"
                  }`}
                >
                  {activeTab === "login" ? (
                    <motion.div
                      initial={{ rotate: -10, scale: 0.9 }}
                      animate={{ rotate: 0, scale: 1 }}
                      transition={{ type: "spring", stiffness: 400, damping: 10 }}
                    >
                      <BoltIcon className="h-5 w-5 text-amber-300" />
                    </motion.div>
                  ) : (
                    <Shield className="h-5 w-5" />
                  )}
                  <span className="relative">
                    เข้าสู่ระบบเทพเจ้า
                    {activeTab === "login" && (
                      <motion.span 
                        className="absolute -bottom-2 left-0 w-full h-0.5 bg-gradient-to-r from-amber-500 to-pink-500"
                        layoutId="tab-indicator"
                      />
                    )}
                  </span>
                </button>
                <button 
                  onClick={() => setActiveTab("register")}
                  className={`flex-1 py-4 text-center font-bold transition flex items-center justify-center gap-2 ${
                    activeTab === "register"
                      ? "bg-gradient-to-b from-fuchsia-500/20 to-gray-900/20 text-fuchsia-300"
                      : "text-gray-400 hover:text-gray-300"
                  }`}
                >
                  {activeTab === "register" ? (
                    <motion.div
                      initial={{ rotate: 10, scale: 0.9 }}
                      animate={{ rotate: 0, scale: 1 }}
                      transition={{ type: "spring", stiffness: 400, damping: 10 }}
                    >
                      <Crown className="h-5 w-5 text-fuchsia-300" />
                    </motion.div>
                  ) : (
                    <Sparkles className="h-5 w-5" />
                  )}
                  <span className="relative">
                    สร้างบัญชีเทพเจ้า
                    {activeTab === "register" && (
                      <motion.span 
                        className="absolute -bottom-2 left-0 w-full h-0.5 bg-gradient-to-r from-fuchsia-500 to-pink-500"
                        layoutId="tab-indicator"
                      />
                    )}
                  </span>
                </button>
              </div>

              {/* เนื้อหาแบบฟอร์ม */}
              <div className="p-6">
                <AnimatePresence mode="wait">
                  <motion.div
                    key={activeTab}
                    initial={{ opacity: 0, x: activeTab === "login" ? -20 : 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: activeTab === "login" ? 20 : -20 }}
                    transition={{ duration: 0.2 }}
                  >
                    {activeTab === "login" ? (
                      <Form {...loginForm}>
                        <form onSubmit={loginForm.handleSubmit(onLoginSubmit)} className="space-y-5">
                          <FormField
                            control={loginForm.control}
                            name="username"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-indigo-200 font-semibold flex items-center gap-1.5">
                                  <Sparkles className="h-4 w-4 text-indigo-300" />
                                  ชื่อผู้ใช้แห่งเทพเจ้า
                                </FormLabel>
                                <FormControl>
                                  <div className="relative">
                                    <Input
                                      placeholder="ผู้กล้าป้อนชื่อเถิด"
                                      className="bg-gray-900/50 border-indigo-950 text-white pl-10 focus-visible:ring-1 focus-visible:ring-amber-500 focus-visible:border-amber-500"
                                      value={field.value || ""}
                                      onChange={field.onChange}
                                      onBlur={field.onBlur}
                                      name={field.name}
                                      ref={field.ref}
                                    />
                                    <div className="absolute left-3 top-2.5 text-indigo-400">
                                      <Crown className="h-5 w-5" />
                                    </div>
                                    <motion.div 
                                      className="absolute right-3 top-2.5 text-amber-400 opacity-0"
                                      animate={{ opacity: field.value ? 1 : 0 }}
                                    >
                                      <Zap className="h-5 w-5" />
                                    </motion.div>
                                  </div>
                                </FormControl>
                                <FormMessage className="text-amber-400 font-medium" />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={loginForm.control}
                            name="password"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-indigo-200 font-semibold flex items-center gap-1.5">
                                  <Sword className="h-4 w-4 text-indigo-300" />
                                  รหัสผ่านศักดิ์สิทธิ์
                                </FormLabel>
                                <FormControl>
                                  <div className="relative">
                                    <Input
                                      type="password"
                                      placeholder="ถ้าลืมรหัส ก็จงอธิษฐาน"
                                      className="bg-gray-900/50 border-indigo-950 text-white pl-10 focus-visible:ring-1 focus-visible:ring-amber-500 focus-visible:border-amber-500"
                                      value={field.value || ""}
                                      onChange={field.onChange}
                                      onBlur={field.onBlur}
                                      name={field.name}
                                      ref={field.ref}
                                    />
                                    <div className="absolute left-3 top-2.5 text-indigo-400">
                                      <Shield className="h-5 w-5" />
                                    </div>
                                    <motion.div 
                                      className="absolute right-3 top-2.5 text-amber-400 opacity-0"
                                      animate={{ opacity: field.value ? 1 : 0 }}
                                    >
                                      <Sword className="h-5 w-5" />
                                    </motion.div>
                                  </div>
                                </FormControl>
                                <FormMessage className="text-amber-400 font-medium" />
                              </FormItem>
                            )}
                          />

                          <motion.div
                            whileHover={{ scale: 1.03 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <Button
                              type="submit"
                              className="w-full py-6 bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white font-bold text-base flex items-center justify-center gap-2 relative overflow-hidden group"
                              disabled={loginMutation.isPending}
                            >
                              <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-amber-600 to-amber-700 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                              <span className="absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMTI4MCAxNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZmZmZmZmMTAiIHN0cm9rZS13aWR0aD0iMSI+PHBhdGggZD0iTTAgODUuNzgyYzIyLjkzMiAyMS4zMjcgNDIuNDc0IDE1LjExMiA2NCAxNS4wNzNsNjQgMS40NTRjMjEuNTI2LS4zOTkgNDEuMDY4IDEzLjM1MSA2NCAyMS4zOThsNjQgMS40NTRjMjEuNTI2LS4zOTkgNDMuMTMgOC40OCA2NCAyMS4zOTggMjIuNyA1LjIzMiA0Mi42OCAyMS4zOTkgNjQgMjEuMzk5IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz48L2c+PC9zdmc+')]"></span>
                              <BoltIcon className="h-5 w-5 relative" />
                              <span className="relative">{loginMutation.isPending ? "กำลังขุมพลังเทพเจ้า..." : "เข้าสู่ดินแดนศักดิ์สิทธิ์"}</span>
                            </Button>
                          </motion.div>
                        </form>
                      </Form>
                    ) : (
                      <Form {...registerForm}>
                        <form onSubmit={registerForm.handleSubmit(onRegisterSubmit)} className="space-y-4">
                          <FormField
                            control={registerForm.control}
                            name="username"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-fuchsia-200 font-semibold flex items-center gap-1.5">
                                  <Crown className="h-4 w-4 text-fuchsia-300" />
                                  ชื่อผู้ใช้แห่งเทพเจ้า
                                </FormLabel>
                                <FormControl>
                                  <div className="relative">
                                    <Input
                                      placeholder="ป้อนชื่อตำนานของท่าน"
                                      className="bg-gray-900/50 border-indigo-950 text-white pl-10 focus-visible:ring-1 focus-visible:ring-fuchsia-500 focus-visible:border-fuchsia-500"
                                      value={field.value || ""}
                                      onChange={field.onChange}
                                      onBlur={field.onBlur}
                                      name={field.name}
                                      ref={field.ref}
                                    />
                                    <div className="absolute left-3 top-2.5 text-fuchsia-400">
                                      <Crown className="h-5 w-5" />
                                    </div>
                                  </div>
                                </FormControl>
                                <FormMessage className="text-fuchsia-400 font-medium" />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={registerForm.control}
                            name="email"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-fuchsia-200 font-semibold flex items-center gap-1.5">
                                  <Sparkles className="h-4 w-4 text-fuchsia-300" />
                                  อีเมลติดต่อเทวดา
                                </FormLabel>
                                <FormControl>
                                  <div className="relative">
                                    <Input
                                      type="email"
                                      placeholder="เทพเจ้าจะส่งจดหมายบอกความลับ"
                                      className="bg-gray-900/50 border-indigo-950 text-white pl-10 focus-visible:ring-1 focus-visible:ring-fuchsia-500 focus-visible:border-fuchsia-500"
                                      value={field.value || ""}
                                      onChange={field.onChange}
                                      onBlur={field.onBlur}
                                      name={field.name}
                                      ref={field.ref}
                                    />
                                    <div className="absolute left-3 top-2.5 text-fuchsia-400">
                                      <Zap className="h-5 w-5" />
                                    </div>
                                  </div>
                                </FormControl>
                                <FormMessage className="text-fuchsia-400 font-medium" />
                              </FormItem>
                            )}
                          />

                          <div className="grid grid-cols-2 gap-4">
                            <FormField
                              control={registerForm.control}
                              name="firstName"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-fuchsia-200 font-semibold">ชื่อจริง</FormLabel>
                                  <FormControl>
                                    <Input
                                      placeholder="ชื่อโลกมนุษย์"
                                      className="bg-gray-900/50 border-indigo-950 text-white focus-visible:ring-1 focus-visible:ring-fuchsia-500 focus-visible:border-fuchsia-500"
                                      value={field.value || ""}
                                      onChange={field.onChange}
                                      onBlur={field.onBlur}
                                      name={field.name}
                                      ref={field.ref}
                                    />
                                  </FormControl>
                                  <FormMessage className="text-fuchsia-400 font-medium" />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={registerForm.control}
                              name="lastName"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-fuchsia-200 font-semibold">นามสกุล</FormLabel>
                                  <FormControl>
                                    <Input
                                      placeholder="ตระกูลของท่าน"
                                      className="bg-gray-900/50 border-indigo-950 text-white focus-visible:ring-1 focus-visible:ring-fuchsia-500 focus-visible:border-fuchsia-500"
                                      value={field.value || ""}
                                      onChange={field.onChange}
                                      onBlur={field.onBlur}
                                      name={field.name}
                                      ref={field.ref}
                                    />
                                  </FormControl>
                                  <FormMessage className="text-fuchsia-400 font-medium" />
                                </FormItem>
                              )}
                            />
                          </div>

                          <FormField
                            control={registerForm.control}
                            name="companyName"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-fuchsia-200 font-semibold flex items-center gap-1.5">
                                  <FlameKindling className="h-4 w-4 text-fuchsia-300" />
                                  อาณาจักรของท่าน (ไม่บังคับ)
                                </FormLabel>
                                <FormControl>
                                  <div className="relative">
                                    <Input
                                      placeholder="ชื่อบริษัท หรือ อาณาจักรของท่าน"
                                      className="bg-gray-900/50 border-indigo-950 text-white pl-10 focus-visible:ring-1 focus-visible:ring-fuchsia-500 focus-visible:border-fuchsia-500"
                                      value={field.value || ""}
                                      onChange={field.onChange}
                                      onBlur={field.onBlur}
                                      name={field.name}
                                      ref={field.ref}
                                    />
                                    <div className="absolute left-3 top-2.5 text-fuchsia-400">
                                      <FlameKindling className="h-5 w-5" />
                                    </div>
                                  </div>
                                </FormControl>
                                <FormMessage className="text-fuchsia-400 font-medium" />
                              </FormItem>
                            )}
                          />

                          <div className="grid grid-cols-2 gap-4">
                            <FormField
                              control={registerForm.control}
                              name="password"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-fuchsia-200 font-semibold flex items-center gap-1.5">
                                    <Shield className="h-4 w-4 text-fuchsia-300" />
                                    รหัสผ่านเวทมนตร์
                                  </FormLabel>
                                  <FormControl>
                                    <div className="relative">
                                      <Input
                                        type="password"
                                        placeholder="อย่าให้มารรู้"
                                        className="bg-gray-900/50 border-indigo-950 text-white pl-10 focus-visible:ring-1 focus-visible:ring-fuchsia-500 focus-visible:border-fuchsia-500"
                                        value={field.value || ""}
                                        onChange={field.onChange}
                                        onBlur={field.onBlur}
                                        name={field.name}
                                        ref={field.ref}
                                      />
                                      <div className="absolute left-3 top-2.5 text-fuchsia-400">
                                        <Sword className="h-5 w-5" />
                                      </div>
                                    </div>
                                  </FormControl>
                                  <FormMessage className="text-fuchsia-400 font-medium" />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={registerForm.control}
                              name="confirmPassword"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-fuchsia-200 font-semibold">ยืนยันรหัสเวทมนตร์</FormLabel>
                                  <FormControl>
                                    <div className="relative">
                                      <Input
                                        type="password"
                                        placeholder="ป้อนอีกครั้ง"
                                        className="bg-gray-900/50 border-indigo-950 text-white pl-10 focus-visible:ring-1 focus-visible:ring-fuchsia-500 focus-visible:border-fuchsia-500"
                                        value={field.value || ""}
                                        onChange={field.onChange}
                                        onBlur={field.onBlur}
                                        name={field.name}
                                        ref={field.ref}
                                      />
                                      <div className="absolute left-3 top-2.5 text-fuchsia-400">
                                        <Shield className="h-5 w-5" />
                                      </div>
                                    </div>
                                  </FormControl>
                                  <FormMessage className="text-fuchsia-400 font-medium" />
                                </FormItem>
                              )}
                            />
                          </div>

                          <div className="flex items-start mt-2">
                            <div className="flex items-center h-5">
                              <input
                                id="terms"
                                type="checkbox"
                                className="h-4 w-4 rounded focus:ring-fuchsia-500 text-fuchsia-600 border-gray-700 bg-gray-900"
                                required
                              />
                            </div>
                            <div className="ml-3 text-sm">
                              <label htmlFor="terms" className="font-medium text-fuchsia-200">
                                ข้าพเจ้ายอมรับข้อกำหนดและสาบานต่อเทพเจ้า
                              </label>
                            </div>
                          </div>

                          <motion.div
                            whileHover={{ scale: 1.03 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <Button
                              type="submit"
                              className="w-full py-6 bg-gradient-to-r from-fuchsia-600 to-purple-700 hover:from-fuchsia-700 hover:to-purple-800 text-white font-bold text-base flex items-center justify-center gap-2 relative overflow-hidden group"
                              disabled={registerMutation.isPending}
                            >
                              <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-fuchsia-600 to-purple-700 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                              <span className="absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMTI4MCAxNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZmZmZmZmMTAiIHN0cm9rZS13aWR0aD0iMSI+PHBhdGggZD0iTTAgODUuNzgyYzIyLjkzMiAyMS4zMjcgNDIuNDc0IDE1LjExMiA2NCAxNS4wNzNsNjQgMS40NTRjMjEuNTI2LS4zOTkgNDEuMDY4IDEzLjM1MSA2NCAyMS4zOThsNjQgMS40NTRjMjEuNTI2LS4zOTkgNDMuMTMgOC40OCA2NCAyMS4zOTggMjIuNyA1LjIzMiA0Mi42OCAyMS4zOTkgNjQgMjEuMzk5IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz48L2c+PC9zdmc+')]"></span>
                              <Crown className="h-5 w-5 relative" />
                              <span className="relative">
                                {registerMutation.isPending ? "กำลังสร้างตำนาน..." : "สร้างตำนานเทพเจ้า"}
                              </span>
                            </Button>
                          </motion.div>
                        </form>
                      </Form>
                    )}
                  </motion.div>
                </AnimatePresence>
              </div>
            </div>
          </div>
        </motion.div>
        
        {/* ส่วนท้าย */}
        <motion.div 
          className="mt-8 text-center text-indigo-400 text-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
        >
          <p>&copy; {new Date().getFullYear()} SLIPKUY - ทุกจักรวาลต้องก้มกราบ!</p>
          <p className="mt-2 text-xs text-indigo-500">ไม่มีเทพเจ้าตนใดถูกทำร้ายระหว่างการสร้างหน้านี้</p>
        </motion.div>
      </main>
    </div>
  );
}