import { useQuery } from "@tanstack/react-query";
import { <PERSON> } from "wouter";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { PackageCard } from "@/components/packages/package-card";
import { Skeleton } from "@/components/ui/skeleton";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Crown, Sparkles, Zap, Star, Shield } from "lucide-react";
import { motion } from "framer-motion";
import { useAuth } from "@/hooks/use-auth";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";

// Interface สำหรับข้อมูลแพ็กเกจ (เหมือนกับในหน้า packages-page)
interface Package {
  id: number;
  name: string;
  description: string;
  price: number;
  requestsLimit: number;
  isActive: boolean;
  features: string[];
  tag?: string;
  duration_days?: number;
}

export function DashboardPackagesSection() {
  const { user } = useAuth();
  const { toast } = useToast();

  // ดึงข้อมูลแพ็กเกจ
  const { data: packages, isLoading } = useQuery<Package[]>({
    queryKey: ["/api/packages"],
  });

  // ดึงข้อมูล active package ของผู้ใช้
  const { data: activePackage } = useQuery<any>({
    queryKey: ["/api/user/active-package"],
    enabled: !!user,
  });

  // นำเอาข้อความคุณสมบัติมาเป็นออบเจ็กต์
  const parseFeatures = (
    features: string[] | undefined,
  ): { name: string; included: boolean }[] => {
    if (!features) return [];

    return features.map((feature) => {
      const included = !feature.startsWith("!");
      const name = included ? feature : feature.substring(1);
      return { name, included };
    });
  };

  // ไปยังหน้า Dashboard เมื่อสมัครสำเร็จ
  const handleSubscriptionSuccess = () => {
    toast({
      title: "สมัครแพ็กเกจสำเร็จ",
      description: "แพ็กเกจของคุณได้ถูกอัปเดตแล้ว",
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }}
      className="pt-24 pb-12 mt-16"
    >
      {/* ข้อความต้อนรับเทพเจ้า */}
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.7, delay: 0.3 }}
        className="text-center mb-16"
      >
        <div className="relative mx-auto w-max">
          <div className="absolute inset-0 bg-gradient-to-r from-amber-500/40 via-purple-500/20 to-amber-500/40 blur-md rounded-lg -z-10"></div>
          <h1 className="text-3xl font-bold text-indigo-100 py-3 px-8 border border-indigo-700/50 rounded-lg bg-indigo-900/50 backdrop-blur-sm shadow-lg flex items-center justify-center">
            <span className="inline-block mr-3 text-amber-400">
              <Crown className="h-7 w-7" />
            </span>
            ยินดีต้อนรับสู่สรวงสวรรค์แห่งการตรวจสอบ!
          </h1>
        </div>
      </motion.div>

      <div className="text-center mb-8">
        <div className="inline-block mb-3">
          <motion.div
            className="bg-indigo-600/10 rounded-full p-2 inline-block"
            whileHover={{ rotate: 15 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <Crown className="h-6 w-6 text-amber-400" />
          </motion.div>
        </div>
        <h2 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-purple-600 mb-2">
          อัพเกรดพลังสวรรค์
        </h2>
        <p className="text-indigo-200 max-w-2xl mx-auto">
          เพิ่มศักยภาพในการตรวจสอบสลิปของคุณด้วยแพ็กเกจที่เหมาะกับธุรกิจ
        </p>
      </div>

      <div className="flex justify-center mb-6 space-x-4">
        <motion.div
          whileHover={{ y: -2 }}
          className="flex items-center text-xs font-medium text-indigo-100 bg-indigo-800/30 rounded-full py-1 px-3 border border-indigo-700/40"
        >
          <Shield className="h-3 w-3 text-green-400 mr-1.5" />
          <span>การันตีความแม่นยำ</span>
        </motion.div>
        <motion.div
          whileHover={{ y: -2 }}
          className="flex items-center text-xs font-medium text-indigo-100 bg-indigo-800/30 rounded-full py-1 px-3 border border-indigo-700/40"
        >
          <Zap className="h-3 w-3 text-amber-400 mr-1.5" />
          <span>ใช้งานได้ทันที</span>
        </motion.div>
        <motion.div
          whileHover={{ y: -2 }}
          className="flex items-center text-xs font-medium text-indigo-100 bg-indigo-800/30 rounded-full py-1 px-3 border border-indigo-700/40"
        >
          <Star className="h-3 w-3 text-purple-400 mr-1.5" />
          <span>เปลี่ยนแพ็กเกจได้ตลอด</span>
        </motion.div>
      </div>

      <Tabs defaultValue="monthly" className="mb-4">
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <TabsList className="grid w-[400px] max-w-full grid-cols-3 mx-auto bg-indigo-950/60 backdrop-blur-sm border border-indigo-800/30 p-1 rounded-full">
            <TabsTrigger
              value="monthly"
              className="rounded-full data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-600 data-[state=active]:to-purple-600 data-[state=active]:text-white"
            >
              1 เดือน
            </TabsTrigger>
            <TabsTrigger
              value="3months"
              className="rounded-full data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-600 data-[state=active]:to-purple-600 data-[state=active]:text-white"
            >
              3 เดือน
            </TabsTrigger>
            <TabsTrigger
              value="6months"
              className="rounded-full data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-600 data-[state=active]:to-purple-600 data-[state=active]:text-white"
            >
              6 เดือน
            </TabsTrigger>
          </TabsList>
        </motion.div>

        <TabsContent value="monthly" className="mt-6">
          {isLoading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(3)].map((_, index) => (
                <div
                  key={index}
                  className="bg-gradient-to-br from-indigo-900/40 to-purple-900/30 border border-indigo-800/30 rounded-xl p-5"
                >
                  <Skeleton className="h-7 w-1/2 mb-1 bg-indigo-700/20" />
                  <Skeleton className="h-4 w-3/4 mb-4 bg-indigo-700/20" />
                  <Skeleton className="h-10 w-1/2 mb-6 bg-indigo-700/20" />
                  <div className="space-y-3 mb-6">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="flex items-start">
                        <Skeleton className="h-4 w-4 mr-2 bg-indigo-700/20" />
                        <Skeleton className="h-4 w-full bg-indigo-700/20" />
                      </div>
                    ))}
                  </div>
                  <Skeleton className="h-9 w-full bg-indigo-700/20" />
                </div>
              ))}
            </div>
          ) : packages ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {packages.map((pkg) => {
                const isCurrentPackage =
                  activePackage && activePackage.packageId === pkg.id;
                return (
                  <motion.div
                    key={pkg.id}
                    whileHover={{ scale: 1.02 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                    className={`relative overflow-hidden rounded-xl ${isCurrentPackage ? "ring-2 ring-amber-400" : ""}`}
                  >
                    <PackageCard
                      id={pkg.id}
                      name={pkg.name}
                      description={pkg.description}
                      price={pkg.price}
                      requestsLimit={pkg.requestsLimit}
                      isPopular={
                        pkg.name === "มืออาชีพ" || pkg.name === "ธรรดา"
                      }
                      features={parseFeatures(pkg.features)}
                      tag={isCurrentPackage ? "แพ็กเกจปัจจุบัน" : pkg.tag}
                      onSubscribe={handleSubscriptionSuccess}
                    />
                    {isCurrentPackage && (
                      <div className="absolute -top-1 -right-1 transform rotate-45 bg-amber-400 text-xs font-bold px-5 py-1 text-amber-900">
                        แพ็กเกจปัจจุบัน
                      </div>
                    )}
                  </motion.div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-12 bg-indigo-900/20 rounded-lg border border-indigo-800/30">
              <Sparkles className="h-8 w-8 mx-auto mb-3 text-amber-400/70" />
              <p className="text-indigo-200">ไม่พบข้อมูลแพ็กเกจในขณะนี้</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="3months" className="mt-6">
          {isLoading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(3)].map((_, index) => (
                <div
                  key={index}
                  className="bg-gradient-to-br from-indigo-900/40 to-purple-900/30 border border-indigo-800/30 rounded-xl p-5"
                >
                  <Skeleton className="h-7 w-1/2 mb-1 bg-indigo-700/20" />
                  <Skeleton className="h-4 w-3/4 mb-4 bg-indigo-700/20" />
                  <Skeleton className="h-10 w-1/2 mb-6 bg-indigo-700/20" />
                  <div className="space-y-3 mb-6">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="flex items-start">
                        <Skeleton className="h-4 w-4 mr-2 bg-indigo-700/20" />
                        <Skeleton className="h-4 w-full bg-indigo-700/20" />
                      </div>
                    ))}
                  </div>
                  <Skeleton className="h-9 w-full bg-indigo-700/20" />
                </div>
              ))}
            </div>
          ) : packages ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {packages.map((pkg) => (
                <PackageCard
                  key={pkg.id}
                  id={pkg.id}
                  name={pkg.name}
                  description={pkg.description}
                  price={Math.round(pkg.price * 3 * 0.95)} // คำนวณราคา 3 เดือนลด 5%
                  requestsLimit={pkg.requestsLimit * 3} // คำนวณจำนวนใช้งาน 3 เดือน
                  isPopular={pkg.name === "มืออาชีพ" || pkg.name === "ธรรดา"}
                  features={parseFeatures(pkg.features)}
                  tag={pkg.tag} // ส่งค่า tag ไปแสดงผล
                  onSubscribe={handleSubscriptionSuccess}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12 bg-indigo-900/20 rounded-lg border border-indigo-800/30">
              <Sparkles className="h-8 w-8 mx-auto mb-3 text-amber-400/70" />
              <p className="text-indigo-200">ไม่พบข้อมูลแพ็กเกจในขณะนี้</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="6months" className="mt-6">
          {isLoading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(3)].map((_, index) => (
                <div
                  key={index}
                  className="bg-gradient-to-br from-indigo-900/40 to-purple-900/30 border border-indigo-800/30 rounded-xl p-5"
                >
                  <Skeleton className="h-7 w-1/2 mb-1 bg-indigo-700/20" />
                  <Skeleton className="h-4 w-3/4 mb-4 bg-indigo-700/20" />
                  <Skeleton className="h-10 w-1/2 mb-6 bg-indigo-700/20" />
                  <div className="space-y-3 mb-6">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="flex items-start">
                        <Skeleton className="h-4 w-4 mr-2 bg-indigo-700/20" />
                        <Skeleton className="h-4 w-full bg-indigo-700/20" />
                      </div>
                    ))}
                  </div>
                  <Skeleton className="h-9 w-full bg-indigo-700/20" />
                </div>
              ))}
            </div>
          ) : packages ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {packages.map((pkg) => (
                <PackageCard
                  key={pkg.id}
                  id={pkg.id}
                  name={pkg.name}
                  description={pkg.description}
                  price={Math.round(pkg.price * 6 * 0.9)} // คำนวณราคา 6 เดือนลด 10%
                  requestsLimit={pkg.requestsLimit * 6} // คำนวณจำนวนใช้งาน 6 เดือน
                  isPopular={pkg.name === "มืออาชีพ" || pkg.name === "ธรรดา"}
                  features={parseFeatures(pkg.features)}
                  tag={pkg.tag}
                  onSubscribe={handleSubscriptionSuccess}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12 bg-indigo-900/20 rounded-lg border border-indigo-800/30">
              <Sparkles className="h-8 w-8 mx-auto mb-3 text-amber-400/70" />
              <p className="text-indigo-200">ไม่พบข้อมูลแพ็กเกจในขณะนี้</p>
            </div>
          )}
        </TabsContent>
      </Tabs>

      <div className="flex justify-center mt-8">
        <Link href="/packages">
          <Button
            variant="outline"
            className="bg-indigo-900/30 text-indigo-200 border-indigo-700/40 hover:bg-indigo-800/40 hover:text-indigo-100"
          >
            ดูแพ็กเกจทั้งหมด
          </Button>
        </Link>
      </div>
    </motion.div>
  );
}
