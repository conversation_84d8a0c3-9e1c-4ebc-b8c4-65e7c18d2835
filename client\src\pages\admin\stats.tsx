import { useState, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { getQueryFn, apiRequest, queryClient } from "@/lib/queryClient";
import { Admin } from "@/components/layouts/admin-layout";
import {
  ResponsiveContainer,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  Legend,
} from "recharts";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Tabs,
  Ta<PERSON>Content,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { AlertTriangle, Users, Activity, Package, ArrowUp, ArrowDown, BarChart3, Globe, Clock, LogOut } from "lucide-react";
import { SessionMonitor } from "@/components/ui/session-monitor";

// สีสำหรับการตกแต่งกราฟ
const COLORS = ["#8884d8", "#82ca9d", "#ffc658", "#ff8042", "#0088fe"];

// คอมโพเนนต์กราฟแสดงข้อมูลสถิติรายชั่วโมง
function LiveSessionChart() {
  const { data: hourlyData, isLoading, error } = useQuery<{
    success: boolean;
    hourlyStats: { hour: number; activeUsers: number; newSessions: number }[];
  }>({
    queryKey: ["/api/admin/hourly-stats"],
    queryFn: getQueryFn({ on401: "throw" }),
    refetchInterval: 30000, // รีเฟรชทุก 30 วินาที
  });

  // เรียงลำดับชั่วโมงจากอดีตถึงปัจจุบัน (จากซ้ายไปขวา)
  const chartData = hourlyData?.hourlyStats
    ? [...hourlyData.hourlyStats]
        .sort((a, b) => a.hour - b.hour)
        .map((item) => ({
          ...item,
          hour: `${item.hour}:00`,
        }))
    : [];

  if (isLoading) {
    return (
      <div className="flex justify-center p-6">
        <div className="animate-pulse h-60 w-full bg-zinc-200 dark:bg-zinc-800 rounded-lg"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="border border-red-300 bg-red-50 dark:bg-red-950/30 rounded-lg p-4 text-red-600 dark:text-red-400">
        <p className="text-sm">เกิดข้อผิดพลาดในการโหลดข้อมูล กรุณาลองใหม่อีกครั้ง</p>
      </div>
    );
  }

  return (
    <div className="h-80">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={chartData}
          margin={{ top: 20, right: 30, left: 0, bottom: 10 }}
        >
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis dataKey="hour" />
          <YAxis />
          <Tooltip
            formatter={(value, name) => {
              if (name === "activeUsers") return [`${value} คน`, "ผู้ใช้ออนไลน์"];
              if (name === "newSessions") return [`${value} เซสชัน`, "เซสชันใหม่"];
              return [value, name];
            }}
          />
          <Legend
            formatter={(value) => {
              if (value === "activeUsers") return "ผู้ใช้ออนไลน์";
              if (value === "newSessions") return "เซสชันใหม่";
              return value;
            }}
          />
          <Bar
            dataKey="activeUsers"
            fill="url(#colorUsers)"
            radius={[4, 4, 0, 0]}
            animationDuration={300}
          />
          <Bar
            dataKey="newSessions"
            fill="url(#colorSessions)"
            radius={[4, 4, 0, 0]}
            animationDuration={300}
          />
          <defs>
            <linearGradient id="colorUsers" x1="0" y1="0" x2="0" y2="1">
              <stop offset="0%" stopColor="#8884d8" stopOpacity={0.8} />
              <stop offset="100%" stopColor="#8884d8" stopOpacity={0.2} />
            </linearGradient>
            <linearGradient id="colorSessions" x1="0" y1="0" x2="0" y2="1">
              <stop offset="0%" stopColor="#82ca9d" stopOpacity={0.8} />
              <stop offset="100%" stopColor="#82ca9d" stopOpacity={0.2} />
            </linearGradient>
          </defs>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}

// คอมโพเนนต์ตารางจัดอันดับผู้ใช้งาน
function UserRankingTable() {
  const { data: rankingData, isLoading, error } = useQuery<{
    success: boolean;
    userRanking: { userId: number; username: string; sessionCount: number; lastActive: string }[];
  }>({
    queryKey: ["/api/admin/user-ranking"],
    queryFn: getQueryFn({ on401: "throw" }),
    refetchInterval: 30000, // รีเฟรชทุก 30 วินาที
  });

  if (isLoading) {
    return (
      <div className="flex flex-col space-y-2">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="animate-pulse h-10 bg-zinc-200 dark:bg-zinc-800 rounded-lg"></div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="border border-red-300 bg-red-50 dark:bg-red-950/30 rounded-lg p-4 text-red-600 dark:text-red-400">
        <p className="text-sm">เกิดข้อผิดพลาดในการโหลดข้อมูล กรุณาลองใหม่อีกครั้ง</p>
      </div>
    );
  }

  // จัดอันดับผู้ใช้งานที่มีเซสชันมากสุด
  const sortedRanking = rankingData?.userRanking
    ? [...rankingData.userRanking].sort((a, b) => b.sessionCount - a.sessionCount).slice(0, 10)
    : [];

  if (sortedRanking.length === 0) {
    return (
      <div className="border border-zinc-300 dark:border-zinc-700 rounded-lg p-4 text-center">
        <p className="text-sm text-muted-foreground">ไม่มีข้อมูลการเข้าใช้งานในขณะนี้</p>
      </div>
    );
  }

  const formatTime = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleTimeString('th-TH', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className="overflow-hidden border rounded-lg dark:border-zinc-800">
      <Table>
        <TableHeader className="bg-muted/50">
          <TableRow>
            <TableHead className="w-12 text-center">#</TableHead>
            <TableHead>ชื่อผู้ใช้</TableHead>
            <TableHead className="text-right">เซสชัน</TableHead>
            <TableHead className="text-right">ใช้งานล่าสุด</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {sortedRanking.map((user, index) => (
            <TableRow key={user.userId} className={index < 3 ? "bg-amber-50/50 dark:bg-amber-950/10" : ""}>
              <TableCell className="text-center font-medium">
                {index < 3 ? (
                  <span className={`inline-block w-6 h-6 rounded-full text-white flex items-center justify-center text-xs
                    ${index === 0 ? "bg-gradient-to-br from-yellow-400 to-amber-600" :
                      index === 1 ? "bg-gradient-to-br from-zinc-300 to-zinc-500" :
                      "bg-gradient-to-br from-amber-700 to-amber-900"}`
                  }>
                    {index + 1}
                  </span>
                ) : (
                  index + 1
                )}
              </TableCell>
              <TableCell className="font-medium">
                {user.username}
              </TableCell>
              <TableCell className="text-right">
                <Badge variant="outline" className="bg-zinc-100 dark:bg-zinc-900">
                  {user.sessionCount}
                </Badge>
              </TableCell>
              <TableCell className="text-right text-muted-foreground text-xs">
                {formatTime(user.lastActive)}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}

// สร้างฟังก์ชันช่วยในการแปลงวันที่
function formatDate(dateString: string) {
  const date = new Date(dateString);
  return date.toLocaleDateString('th-TH', {
    day: '2-digit',
    month: 'short'
  });
}

interface UserStatus {
  total: number;
  active: number;
  inactive: number;
  suspended: number;
}

interface SlipStatus {
  total: number;
  success: number;
  failed: number;
}

interface DayStats {
  date: string;
  count: number;
}

interface TopUser {
  userId: number;
  username: string;
  email: string;
  count: number;
}

interface TopPackage {
  packageId: number;
  packageName: string;
  count: number;
}

interface SystemStats {
  users: UserStatus;
  slips: SlipStatus;
  signupsByDay: DayStats[];
  apiUsageByDay: DayStats[];
  topUsers: TopUser[];
  topPackages: TopPackage[];
}

interface OnlineUser {
  id: number;
  username: string;
  email: string;
  status: string;
  role: string;
  sessionId: string;
  lastActivity: string;
  ip: string;
  userAgent: string;
  location: string;
}

export default function Stats() {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("overview");

  // ดึงข้อมูลสถิติของระบบ
  const { data: stats, isLoading: statsLoading, error: statsError } = useQuery<SystemStats>({
    queryKey: ["/api/admin/stats"],
    queryFn: getQueryFn({ on401: "throw" }),
    refetchInterval: 60000, // รีเฟรชทุก 1 นาที
  });

  // ดึงข้อมูลผู้ใช้ที่ออนไลน์
  const { data: onlineUsers, isLoading: onlineLoading, error: onlineError } = useQuery<OnlineUser[]>({
    queryKey: ["/api/admin/online-users"],
    queryFn: getQueryFn({ on401: "throw" }),
    refetchInterval: 10000, // รีเฟรชทุก 10 วินาที
  });

  // Mutation สำหรับการเตะผู้ใช้ออกจากระบบ
  const logoutUserMutation = useMutation({
    mutationFn: async (sessionId: string) => {
      const res = await apiRequest("POST", "/api/admin/logout-user", { sessionId });
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "สำเร็จ",
        description: "ผู้ใช้ถูกออกจากระบบเรียบร้อยแล้ว",
        variant: "default",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/admin/online-users"] });
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // ฟังก์ชันสำหรับการเตะผู้ใช้ออกจากระบบ
  const handleLogoutUser = (sessionId: string, username: string) => {
    if (confirm(`คุณต้องการที่จะเตะผู้ใช้ "${username}" ออกจากระบบหรือไม่?`)) {
      logoutUserMutation.mutate(sessionId);
    }
  };

  // สร้างข้อมูลสำหรับ PieChart ของ User Status
  const userStatusData = stats
    ? [
        { name: "กำลังใช้งาน", value: stats.users.active },
        { name: "ไม่ได้ใช้งาน", value: stats.users.inactive },
        { name: "ถูกระงับ", value: stats.users.suspended },
      ]
    : [];

  // สร้างข้อมูลสำหรับ PieChart ของ Slip Verification
  const slipStatusData = stats
    ? [
        { name: "สำเร็จ", value: stats.slips.success },
        { name: "ล้มเหลว", value: stats.slips.failed },
      ]
    : [];

  // สร้างข้อมูลสำหรับ BarChart ของ Top Users
  const topUsersChartData = Array.isArray(stats?.topUsers)
    ? stats.topUsers.map((user) => ({
        name: user.username,
        value: user.count,
      }))
    : [];

  // สร้างข้อมูลสำหรับ BarChart ของ Top Packages
  const topPackagesChartData = Array.isArray(stats?.topPackages)
    ? stats.topPackages.map((pkg) => ({
        name: pkg.packageName,
        value: pkg.count,
      }))
    : [];

  // แปลง signupsByDay สำหรับ AreaChart
  const signupsChartData = Array.isArray(stats?.signupsByDay)
    ? stats.signupsByDay.map((item) => ({
        date: formatDate(item.date),
        count: item.count,
      }))
    : [];

  // แปลง apiUsageByDay สำหรับ AreaChart
  const apiUsageChartData = Array.isArray(stats?.apiUsageByDay)
    ? stats.apiUsageByDay.map((item) => ({
        date: formatDate(item.date),
        count: item.count,
      }))
    : [];

  // ฟังก์ชันช่วยในการรับประเภทของ Browser จาก User-Agent
  const getBrowserInfo = (userAgent: string) => {
    if (!userAgent) return "ไม่ทราบ";

    if (userAgent.includes("Chrome")) return "Chrome";
    if (userAgent.includes("Firefox")) return "Firefox";
    if (userAgent.includes("Safari") && !userAgent.includes("Chrome")) return "Safari";
    if (userAgent.includes("Edge")) return "Edge";
    if (userAgent.includes("Opera") || userAgent.includes("OPR")) return "Opera";
    if (userAgent.includes("MSIE") || userAgent.includes("Trident/")) return "Internet Explorer";

    return "อื่นๆ";
  };

  // ฟังก์ชันช่วยในการรับประเภทของอุปกรณ์จาก User-Agent
  const getDeviceInfo = (userAgent: string) => {
    if (!userAgent) return "ไม่ทราบ";

    if (userAgent.includes("iPhone") || userAgent.includes("iPad") || userAgent.includes("iPod")) return "iOS";
    if (userAgent.includes("Android")) return "Android";
    if (userAgent.includes("Windows Phone")) return "Windows Phone";
    if (userAgent.includes("Windows")) return "Windows";
    if (userAgent.includes("Macintosh") || userAgent.includes("Mac OS")) return "Mac";
    if (userAgent.includes("Linux") && !userAgent.includes("Android")) return "Linux";

    return "อื่นๆ";
  };

  return (
    <Admin>
      <div className="container mx-auto p-6">
        <h1 className="text-3xl font-bold mb-8 text-center bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent">
          สถิติและการวิเคราะห์
        </h1>
        <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-6 bg-gradient-to-r from-indigo-600 to-purple-600 p-1">
            <TabsTrigger value="overview" className="data-[state=active]:bg-white">
              <BarChart3 className="h-4 w-4 mr-2" />
              ภาพรวม
            </TabsTrigger>
            <TabsTrigger value="online-users" className="data-[state=active]:bg-white">
              <Users className="h-4 w-4 mr-2" />
              ผู้ใช้ออนไลน์
            </TabsTrigger>
          </TabsList>

          {/* แท็บภาพรวม */}
          <TabsContent value="overview">
            {/* เพิ่มเซคชันแดชบอร์ดแบบเรียลไทม์ */}
            <Card className="mb-6">
              <CardHeader className="border-b">
                <CardTitle className="text-lg font-medium flex items-center text-purple-600">
                  <Activity className="h-5 w-5 mr-2" />
                  การเข้าใช้งานระบบแบบเรียลไทม์
                </CardTitle>
                <CardDescription>
                  แสดงข้อมูลการเข้าใช้งานระบบในช่วง 24 ชั่วโมง และการจัดอันดับการเข้าใช้งาน
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                  {/* กราฟแสดงสถิติรายชั่วโมง */}
                  <div>
                    <h3 className="text-sm font-semibold mb-4 text-muted-foreground">การเข้าใช้งานตามช่วงเวลา (24 ชั่วโมง)</h3>
                    <LiveSessionChart />
                  </div>
                  {/* ตารางจัดอันดับผู้ใช้งาน */}
                  <div>
                    <h3 className="text-sm font-semibold mb-4 text-muted-foreground">อันดับการเข้าใช้งาน</h3>
                    <UserRankingTable />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* สถิติหลัก */}
            {statsLoading ? (
              <div className="flex justify-center p-12">
                <div className="animate-pulse flex flex-col items-center">
                  <div className="h-12 w-12 rounded-full bg-purple-200 mb-2"></div>
                  <div className="h-4 w-24 bg-purple-200 rounded"></div>
                </div>
              </div>
            ) : statsError ? (
              <Card className="mb-6">
                <CardHeader className="bg-red-50">
                  <CardTitle className="text-red-600 flex items-center">
                    <AlertTriangle className="h-5 w-5 mr-2" />
                    เกิดข้อผิดพลาด
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                  <p>ไม่สามารถโหลดข้อมูลสถิติได้ กรุณาลองใหม่อีกครั้ง</p>
                </CardContent>
              </Card>
            ) : stats ? (
              <>
                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                  <Card className="overflow-hidden">
                    <div className="h-2 bg-gradient-to-r from-purple-500 to-purple-700"></div>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm text-muted-foreground">ผู้ใช้ทั้งหมด</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center">
                        <Users className="h-8 w-8 mr-4 text-purple-500" />
                        <div>
                          <p className="text-3xl font-bold">{stats.users.total}</p>
                          <p className="text-xs text-muted-foreground">
                            <span className="text-green-500 font-medium flex items-center">
                              <ArrowUp className="h-3 w-3 mr-1" /> {stats.users.active} กำลังใช้งาน
                            </span>
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="overflow-hidden">
                    <div className="h-2 bg-gradient-to-r from-blue-500 to-blue-700"></div>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm text-muted-foreground">การตรวจสอบสลิปทั้งหมด</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center">
                        <Activity className="h-8 w-8 mr-4 text-blue-500" />
                        <div>
                          <p className="text-3xl font-bold">{stats.slips.total}</p>
                          <p className="text-xs text-muted-foreground">
                            <span className="flex items-center">
                              <span className="text-green-500 font-medium flex items-center mr-2">
                                <ArrowUp className="h-3 w-3 mr-1" /> {stats.slips.success} สำเร็จ
                              </span>
                              <span className="text-red-500 font-medium flex items-center">
                                <ArrowDown className="h-3 w-3 mr-1" /> {stats.slips.failed} ล้มเหลว
                              </span>
                            </span>
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="overflow-hidden">
                    <div className="h-2 bg-gradient-to-r from-amber-500 to-amber-700"></div>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm text-muted-foreground">ผู้ใช้ออนไลน์</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center">
                        <Globe className="h-8 w-8 mr-4 text-amber-500" />
                        <div>
                          <p className="text-3xl font-bold">{onlineUsers?.length || 0}</p>
                          <p className="text-xs text-muted-foreground">
                            ออนไลน์ในขณะนี้
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="overflow-hidden">
                    <div className="h-2 bg-gradient-to-r from-emerald-500 to-emerald-700"></div>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm text-muted-foreground">แพ็กเกจที่ใช้งานอยู่</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center">
                        <Package className="h-8 w-8 mr-4 text-emerald-500" />
                        <div>
                          <p className="text-3xl font-bold">
                            {Array.isArray(stats.topPackages)
                              ? stats.topPackages.reduce((sum, pkg) => sum + pkg.count, 0)
                              : 0}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            กำลังใช้งานอยู่
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Graphs Row 1 */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                  {/* Signups Chart */}
                  <Card>
                    <CardHeader>
                      <CardTitle>การสมัครสมาชิกในช่วง 30 วันที่ผ่านมา</CardTitle>
                      <CardDescription>จำนวนผู้ใช้ใหม่ที่สมัครในแต่ละวัน</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="h-80">
                        <ResponsiveContainer width="100%" height="100%">
                          <AreaChart
                            data={signupsChartData}
                            margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                          >
                            <defs>
                              <linearGradient id="colorSignups" x1="0" y1="0" x2="0" y2="1">
                                <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8} />
                                <stop offset="95%" stopColor="#8884d8" stopOpacity={0.1} />
                              </linearGradient>
                            </defs>
                            <XAxis dataKey="date" />
                            <YAxis />
                            <CartesianGrid strokeDasharray="3 3" vertical={false} />
                            <Tooltip />
                            <Area
                              type="monotone"
                              dataKey="count"
                              name="จำนวนผู้สมัคร"
                              stroke="#8884d8"
                              fillOpacity={1}
                              fill="url(#colorSignups)"
                            />
                          </AreaChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>

                  {/* API Usage Chart */}
                  <Card>
                    <CardHeader>
                      <CardTitle>การใช้งาน API ในช่วง 30 วันที่ผ่านมา</CardTitle>
                      <CardDescription>จำนวนการตรวจสอบสลิปในแต่ละวัน</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="h-80">
                        <ResponsiveContainer width="100%" height="100%">
                          <AreaChart
                            data={apiUsageChartData}
                            margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                          >
                            <defs>
                              <linearGradient id="colorAPIUsage" x1="0" y1="0" x2="0" y2="1">
                                <stop offset="5%" stopColor="#82ca9d" stopOpacity={0.8} />
                                <stop offset="95%" stopColor="#82ca9d" stopOpacity={0.1} />
                              </linearGradient>
                            </defs>
                            <XAxis dataKey="date" />
                            <YAxis />
                            <CartesianGrid strokeDasharray="3 3" vertical={false} />
                            <Tooltip />
                            <Area
                              type="monotone"
                              dataKey="count"
                              name="จำนวนการตรวจสอบ"
                              stroke="#82ca9d"
                              fillOpacity={1}
                              fill="url(#colorAPIUsage)"
                            />
                          </AreaChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Graphs Row 2 */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                  {/* User Status Pie Chart */}
                  <Card>
                    <CardHeader>
                      <CardTitle>สถานะผู้ใช้งาน</CardTitle>
                      <CardDescription>การกระจายของผู้ใช้ตามสถานะ</CardDescription>
                    </CardHeader>
                    <CardContent className="flex justify-center">
                      <div className="h-64 w-full">
                        <ResponsiveContainer width="100%" height="100%">
                          <PieChart>
                            <Pie
                              data={userStatusData}
                              cx="50%"
                              cy="50%"
                              innerRadius={60}
                              outerRadius={80}
                              fill="#8884d8"
                              paddingAngle={5}
                              dataKey="value"
                              nameKey="name"
                              label={(entry) => `${entry.name}: ${entry.value}`}
                            >
                              {userStatusData.map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                              ))}
                            </Pie>
                            <Tooltip formatter={(value) => [`${value} คน`, "จำนวน"]} />
                            <Legend />
                          </PieChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Slip Verification Status Pie Chart */}
                  <Card>
                    <CardHeader>
                      <CardTitle>ผลการตรวจสอบสลิป</CardTitle>
                      <CardDescription>อัตราส่วนของการตรวจสอบที่สำเร็จและล้มเหลว</CardDescription>
                    </CardHeader>
                    <CardContent className="flex justify-center">
                      <div className="h-64 w-full">
                        <ResponsiveContainer width="100%" height="100%">
                          <PieChart>
                            <Pie
                              data={slipStatusData}
                              cx="50%"
                              cy="50%"
                              innerRadius={60}
                              outerRadius={80}
                              fill="#8884d8"
                              paddingAngle={5}
                              dataKey="value"
                              nameKey="name"
                              label={(entry) => `${entry.name}: ${entry.value}`}
                            >
                              <Cell fill="#82ca9d" />
                              <Cell fill="#ff8042" />
                            </Pie>
                            <Tooltip formatter={(value) => [`${value} ครั้ง`, "จำนวน"]} />
                            <Legend />
                          </PieChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Graphs Row 3 */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                  {/* Top Users Bar Chart */}
                  <Card>
                    <CardHeader>
                      <CardTitle>ผู้ใช้ที่ใช้งานมากที่สุด</CardTitle>
                      <CardDescription>ผู้ใช้ที่มีการตรวจสอบสลิปมากที่สุด 5 อันดับแรก</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="h-80">
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={topUsersChartData.slice(0, 5)}
                            layout="vertical"
                            margin={{ top: 20, right: 30, left: 40, bottom: 10 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" horizontal={false} />
                            <XAxis type="number" />
                            <YAxis dataKey="name" type="category" width={100} />
                            <Tooltip formatter={(value) => [`${value} ครั้ง`, "จำนวนการตรวจสอบ"]} />
                            <Bar dataKey="value" name="จำนวนการตรวจสอบ" fill="#8884d8" radius={[0, 4, 4, 0]}>
                              {topUsersChartData.slice(0, 5).map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                              ))}
                            </Bar>
                          </BarChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Top Packages Bar Chart */}
                  <Card>
                    <CardHeader>
                      <CardTitle>แพ็กเกจที่ใช้งานมากที่สุด</CardTitle>
                      <CardDescription>แพ็กเกจที่มีการใช้งานมากที่สุด 5 อันดับแรก</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="h-80">
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={topPackagesChartData.slice(0, 5)}
                            layout="vertical"
                            margin={{ top: 20, right: 30, left: 40, bottom: 10 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" horizontal={false} />
                            <XAxis type="number" />
                            <YAxis dataKey="name" type="category" width={100} />
                            <Tooltip formatter={(value) => [`${value} ครั้ง`, "จำนวนการใช้งาน"]} />
                            <Bar dataKey="value" name="จำนวนการใช้งาน" fill="#82ca9d" radius={[0, 4, 4, 0]}>
                              {topPackagesChartData.slice(0, 5).map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={COLORS[(index + 2) % COLORS.length]} />
                              ))}
                            </Bar>
                          </BarChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </>
            ) : null}
          </TabsContent>

          {/* แท็บผู้ใช้ออนไลน์ */}
          <TabsContent value="online-users">
            <SessionMonitor
              onlineUsers={onlineUsers || []}
              isLoading={onlineLoading || false}
              error={onlineError ? "เกิดข้อผิดพลาดในการโหลดข้อมูลผู้ใช้ออนไลน์" : null}
              onLogout={handleLogoutUser}
              getDeviceInfo={getDeviceInfo}
              getBrowserInfo={getBrowserInfo}
            />
          </TabsContent>
        </Tabs>
      </div>
    </Admin>
  );
}