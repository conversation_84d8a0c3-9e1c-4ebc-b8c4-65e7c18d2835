import { pgTable, text, serial, integer, boolean, timestamp, jsonb } from "drizzle-orm/pg-core";
import { apiLogs, apiRequestTypeEnum, apiResponseStatusEnum } from "./schema";
import { sql } from "drizzle-orm";

// อัพเดทคอลัมน์ใน apiLogs
export const apiLogsUpdated = {
  id: apiLogs.id,
  apiKeyId: apiLogs.apiKeyId,
  userId: integer("user_id").references(() => "users.id"),
  requestType: apiLogs.requestType,
  requestData: apiLogs.requestData,
  responseStatus: apiLogs.responseStatus,
  responseData: apiLogs.responseData,
  slipVerificationId: apiLogs.slipVerificationId,
  ipAddress: apiLogs.ipAddress,
  userAgent: apiLogs.userAgent,
  processingTime: apiLogs.processingTime,
  errorMessage: apiLogs.errorMessage,
  error: jsonb("error"),
  location: text("location"),
  createdAt: apiLogs.createdAt,
};

// คำสั่ง SQL เพื่ออัพเดทตาราง
export const updateApiLogsTableSQL = sql`
  ALTER TABLE api_logs ADD COLUMN IF NOT EXISTS location text;
  ALTER TABLE api_logs ADD COLUMN IF NOT EXISTS error jsonb;
  ALTER TABLE api_logs ADD COLUMN IF NOT EXISTS user_id integer REFERENCES users(id);
  
  CREATE INDEX IF NOT EXISTS api_logs_user_id_idx ON api_logs (user_id);
  CREATE INDEX IF NOT EXISTS api_logs_created_at_idx ON api_logs (created_at);
  CREATE INDEX IF NOT EXISTS api_logs_response_status_idx ON api_logs (response_status);
  
  UPDATE api_logs 
  SET user_id = api_keys.user_id 
  FROM api_keys
  WHERE api_logs.api_key_id = api_keys.id AND api_logs.user_id IS NULL;
`;