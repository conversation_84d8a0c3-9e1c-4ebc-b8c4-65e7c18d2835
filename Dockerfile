# --- Stage 1: Builder ---
FROM node:20-alpine AS builder
WORKDIR /app

# ติดตั้ง dependencies ที่จำเป็นสำหรับ native modules
RUN apk add --no-cache python3 make g++ cairo-dev jpeg-dev pango-dev giflib-dev

# คัดลอกไฟล์ package.json และ package-lock.json
COPY package*.json ./

# ติดตั้ง dependencies ทั้งหมดสำหรับ build
RUN npm ci

# คัดลอกไฟล์ทั้งหมด
COPY . .

# Build แอปพลิเคชัน
RUN npm run build

# --- Stage 2: Production ---
FROM node:20-alpine
WORKDIR /app

# ติดตั้ง dependencies ที่จำเป็นสำหรับ runtime
RUN apk add --no-cache python3 make g++ cairo-dev jpeg-dev pango-dev giflib-dev

# คัดลอกไฟล์ package.json และ package-lock.json
COPY package*.json ./

# ติดตั้ง dependencies ทั้งหมด (รวม vite สำหรับ runtime)
RUN npm ci && npm cache clean --force

# คัดลอก dist folder จาก builder stage
COPY --from=builder /app/dist ./dist

# คัดลอก theme.json จาก builder stage ไปยังโฟลเดอร์ dist/
COPY --from=builder /app/theme.json ./dist/theme.json

# คัดลอกไฟล์ที่จำเป็นอื่นๆ
COPY --from=builder /app/public ./public
COPY --from=builder /app/migrations ./migrations
COPY --from=builder /app/shared ./shared

# สร้างโฟลเดอร์ที่จำเป็น
RUN mkdir -p public/uploads/slips logs tmp backups

# เปิดพอร์ต 4000
EXPOSE 4000

# ตั้งค่า environment variables เริ่มต้น
ENV NODE_ENV=production
ENV PORT=4000

# รันแอปพลิเคชันด้วย npm start
CMD ["npm", "start"]
