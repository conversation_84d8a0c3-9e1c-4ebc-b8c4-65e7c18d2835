import { Request, Response } from 'express';
import { db } from '../db';
import { eq, desc, and, gte, lte, sql } from 'drizzle-orm';
import { isAdmin } from '../auth';
import { reports, slipVerifications, insertReportSchema } from '@shared/schema';
import { emailService } from '../email-service';

// สร้างรายงานแบบ adhoc (สำหรับผู้ใช้ทั่วไป)
export async function createUserReport(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'กรุณาเข้าสู่ระบบ' });
    }

    const userId = req.user.id;
    const { startDate, endDate, type, format } = req.body;

    if (!startDate || !endDate) {
      return res.status(400).json({ message: 'กรุณาระบุช่วงเวลาของรายงาน' });
    }

    // สร้างข้อมูลรายงาน
    const reportData = await generateReportData(userId, new Date(startDate), new Date(endDate));

    // บันทึกรายงาน
    const report = insertReportSchema.parse({
      userId,
      name: `รายงาน ${type} - ${new Date().toLocaleDateString('th-TH')}`,
      type: type || 'daily',
      format: format || 'html',
      data: reportData,
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      status: 'generated'
    });

    const result = await db.insert(reports).values(report).returning();

    // ส่งอีเมลรายงาน (ถ้าผู้ใช้ต้องการ)
    if (req.body.sendEmail) {
      const user = req.user;
      await emailService.sendPeriodicReport(userId, type || 'daily', {
        ...reportData,
        totalTransactions: reportData.transactions.length,
        totalAmount: reportData.totalAmount.toLocaleString('th-TH'),
        avgAmount: reportData.avgAmount.toLocaleString('th-TH'),
        maxAmount: reportData.maxAmount.toLocaleString('th-TH'),
        recentTransactions: reportData.transactions
          .slice(0, 5)
          .map(t => `${new Date(t.createdAt).toLocaleDateString('th-TH')} - ${t.amount.toLocaleString('th-TH')} บาท (${t.receiver})`)
          .join('<br>')
      });
    }

    res.status(201).json(result[0]);
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการสร้างรายงาน:', error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการสร้างรายงาน' });
  }
}

// ดึงรายงานของผู้ใช้
export async function getUserReports(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'กรุณาเข้าสู่ระบบ' });
    }

    const userId = req.user.id;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
    const page = req.query.page ? parseInt(req.query.page as string) : 1;
    const offset = (page - 1) * limit;

    const userReports = await db.select().from(reports)
      .where(eq(reports.userId, userId))
      .orderBy(desc(reports.createdAt))
      .limit(limit)
      .offset(offset);

    const totalCount = await db.select({ count: db.fn.count() }).from(reports)
      .where(eq(reports.userId, userId));

    res.json({
      reports: userReports,
      pagination: {
        total: parseInt(totalCount[0].count as string),
        page,
        limit,
        totalPages: Math.ceil(parseInt(totalCount[0].count as string) / limit)
      }
    });
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการดึงรายงานของผู้ใช้:', error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการดึงรายงานของผู้ใช้' });
  }
}

// ดึงรายงานโดย ID
export async function getReportById(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'กรุณาเข้าสู่ระบบ' });
    }

    const userId = req.user.id;
    const reportId = parseInt(req.params.id);

    // ตรวจสอบว่ารายงานนี้เป็นของผู้ใช้หรือเป็นผู้ดูแลระบบ
    let report;
    if (isAdmin(req)) {
      report = await db.select().from(reports)
        .where(eq(reports.id, reportId));
    } else {
      report = await db.select().from(reports)
        .where(and(
          eq(reports.id, reportId),
          eq(reports.userId, userId)
        ));
    }

    if (report.length === 0) {
      return res.status(404).json({ message: 'ไม่พบรายงาน' });
    }

    res.json(report[0]);
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการดึงรายงาน:', error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการดึงรายงาน' });
  }
}

// ลบรายงาน
export async function deleteReport(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'กรุณาเข้าสู่ระบบ' });
    }

    const userId = req.user.id;
    const reportId = parseInt(req.params.id);

    // ตรวจสอบว่ารายงานนี้เป็นของผู้ใช้หรือเป็นผู้ดูแลระบบ
    let report;
    if (isAdmin(req)) {
      report = await db.select().from(reports)
        .where(eq(reports.id, reportId));
    } else {
      report = await db.select().from(reports)
        .where(and(
          eq(reports.id, reportId),
          eq(reports.userId, userId)
        ));
    }

    if (report.length === 0) {
      return res.status(404).json({ message: 'ไม่พบรายงาน' });
    }

    // ลบรายงาน
    await db.delete(reports)
      .where(eq(reports.id, reportId));

    res.json({ message: 'ลบรายงานเรียบร้อย' });
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการลบรายงาน:', error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการลบรายงาน' });
  }
}

// ส่งรายงานทางอีเมล
export async function sendReportEmail(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'กรุณาเข้าสู่ระบบ' });
    }

    const userId = req.user.id;
    const reportId = parseInt(req.params.id);

    // ตรวจสอบว่ารายงานนี้เป็นของผู้ใช้หรือเป็นผู้ดูแลระบบ
    let report;
    if (isAdmin(req)) {
      report = await db.select().from(reports)
        .where(eq(reports.id, reportId));
    } else {
      report = await db.select().from(reports)
        .where(and(
          eq(reports.id, reportId),
          eq(reports.userId, userId)
        ));
    }

    if (report.length === 0) {
      return res.status(404).json({ message: 'ไม่พบรายงาน' });
    }

    // ส่งอีเมลรายงาน
    const reportData = report[0].data;
    const success = await emailService.sendPeriodicReport(
      report[0].userId,
      report[0].type,
      {
        ...reportData,
        totalTransactions: reportData.transactions.length,
        totalAmount: reportData.totalAmount.toLocaleString('th-TH'),
        avgAmount: reportData.avgAmount.toLocaleString('th-TH'),
        maxAmount: reportData.maxAmount.toLocaleString('th-TH'),
        recentTransactions: reportData.transactions
          .slice(0, 5)
          .map(t => `${new Date(t.createdAt).toLocaleDateString('th-TH')} - ${t.amount.toLocaleString('th-TH')} บาท (${t.receiver})`)
          .join('<br>')
      }
    );

    if (!success) {
      return res.status(500).json({ message: 'ไม่สามารถส่งอีเมลรายงานได้' });
    }

    res.json({ message: 'ส่งอีเมลรายงานเรียบร้อย' });
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการส่งอีเมลรายงาน:', error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการส่งอีเมลรายงาน' });
  }
}

// ฟังก์ชันสร้างข้อมูลรายงาน
async function generateReportData(userId: number, startDate: Date, endDate: Date) {
  // ดึงธุรกรรมในช่วงเวลาที่กำหนด
  const transactions = await db.select().from(slipVerifications)
    .where(
      and(
        eq(slipVerifications.userId, userId),
        gte(slipVerifications.transactionDate || slipVerifications.createdAt, startDate),
        lte(slipVerifications.transactionDate || slipVerifications.createdAt, endDate)
      )
    )
    .orderBy(desc(slipVerifications.transactionDate || slipVerifications.createdAt));

  // คำนวณข้อมูลสำหรับรายงาน
  const amounts = transactions
    .map(t => t.amount || 0)
    .filter(amount => amount > 0);

  const totalAmount = amounts.reduce((sum, amount) => sum + amount, 0);
  const avgAmount = amounts.length > 0 ? totalAmount / amounts.length : 0;
  const maxAmount = amounts.length > 0 ? Math.max(...amounts) : 0;

  // จัดกลุ่มตามธนาคาร
  const bankGroups: Record<string, number> = {};
  transactions.forEach(t => {
    const bank = t.bankName || 'อื่นๆ';
    bankGroups[bank] = (bankGroups[bank] || 0) + 1;
  });

  // จัดกลุ่มตามช่วงเวลา
  const hourlyGroups: Record<string, number> = {};
  transactions.forEach(t => {
    const date = t.transactionDate || t.createdAt;
    const hour = new Date(date).getHours();
    hourlyGroups[hour] = (hourlyGroups[hour] || 0) + 1;
  });

  // สร้างข้อมูลกราฟสำหรับการแสดงผล
  const bankChartData = Object.entries(bankGroups).map(([bank, count]) => ({
    name: bank,
    value: count
  }));

  const hourlyChartData = Array.from({ length: 24 }, (_, i) => ({
    hour: i,
    count: hourlyGroups[i] || 0
  }));

  return {
    transactions,
    totalAmount,
    avgAmount,
    maxAmount,
    bankGroups,
    hourlyGroups,
    bankChartData,
    hourlyChartData,
    transactionCount: transactions.length,
    startDate: startDate.toISOString(),
    endDate: endDate.toISOString(),
    generatedAt: new Date().toISOString()
  };
}

// ดึงสถิติรายงานทั้งหมด (สำหรับผู้ดูแลระบบ)
export async function getReportStats(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'กรุณาเข้าสู่ระบบ' });
    }

    if (!isAdmin(req)) {
      return res.status(403).json({ message: 'คุณไม่มีสิทธิ์ในการเข้าถึง' });
    }

    // จำนวนรายงานทั้งหมด
    const totalReports = await db.select({ count: db.fn.count() }).from(reports);

    // จำนวนรายงานแยกตามประเภท
    const reportsByType = await db.select({
      type: reports.type,
      count: db.fn.count(),
    })
    .from(reports)
    .groupBy(reports.type);

    // จำนวนรายงานที่สร้างในเดือนนี้
    const thisMonth = new Date();
    thisMonth.setDate(1);
    thisMonth.setHours(0, 0, 0, 0);

    const reportsThisMonth = await db.select({ count: db.fn.count() })
      .from(reports)
      .where(gte(reports.createdAt, thisMonth));

    // รายการผู้ใช้ที่สร้างรายงานมากที่สุด
    const topUsers = await db.select({
      userId: reports.userId,
      count: db.fn.count(),
    })
    .from(reports)
    .groupBy(reports.userId)
    .orderBy(desc(sql`count`))
    .limit(5);

    // แปลงข้อมูลให้อยู่ในรูปแบบที่เหมาะสม
    const stats = {
      totalReports: parseInt(totalReports[0].count as string),
      reportsByType: reportsByType.map(item => ({
        type: item.type,
        count: parseInt(item.count as string)
      })),
      reportsThisMonth: parseInt(reportsThisMonth[0].count as string),
      topUsers: topUsers.map(item => ({
        userId: item.userId,
        count: parseInt(item.count as string)
      }))
    };

    res.json(stats);
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการดึงสถิติรายงาน:', error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการดึงสถิติรายงาน' });
  }
}