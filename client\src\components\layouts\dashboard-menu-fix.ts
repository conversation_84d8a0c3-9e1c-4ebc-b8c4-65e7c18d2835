/**
 * ไฟล์ชั่วคราวสำหรับแก้ไขเมนูในหน้า dashboard
 * ให้เรียกใช้งานสคริปต์นี้เพื่อเพิ่มเมนูการตั้งค่าส่วนตัวในแดชบอร์ด
 * โดยการรันคำสั่ง: NODE_ENV=development tsx client/src/components/layouts/dashboard-menu-fix.ts
 */

import * as fs from 'fs';
import * as path from 'path';

// ไฟล์ที่ต้องการแก้ไข
const filePath = path.resolve('./client/src/components/layouts/dashboard-responsive-new.tsx');

try {
  // อ่านเนื้อหาไฟล์
  const fileContent = fs.readFileSync(filePath, 'utf8');

  // ค้นหาและแทนที่ส่วนรายการเมนูตั้งค่าบัญชี
  const searchStr = `      title: "ตั้งค่าบัญชี",
      items: [
        { href: "/profile", label: "โปรไฟล์", icon: Sun, description: "แก้ไขข้อมูลส่วนตัวและรูปโปรไฟล์" },
        { href: "/change-password", label: "เปลี่ยนรหัสผ่าน", icon: Shield, description: "เปลี่ยนรหัสผ่านเข้าสู่ระบบ" },
        { href: "/notifications", label: "การแจ้งเตือน", icon: CloudLightning, description: "จัดการการแจ้งเตือนต่างๆ" },
      ]`;
  
  const replaceStr = `      title: "ตั้งค่าบัญชี",
      items: [
        { href: "/profile", label: "โปรไฟล์", icon: Sun, description: "แก้ไขข้อมูลส่วนตัวและรูปโปรไฟล์" },
        { href: "/change-password", label: "เปลี่ยนรหัสผ่าน", icon: Shield, description: "เปลี่ยนรหัสผ่านเข้าสู่ระบบ" },
        { href: "/notifications", label: "การแจ้งเตือน", icon: CloudLightning, description: "จัดการการแจ้งเตือนต่างๆ" },
        { href: "/user-settings", label: "การตั้งค่าส่วนตัว", icon: Settings, description: "ปรับแต่งการทำงานของระบบ" },
      ]`;

  if (fileContent.includes(searchStr)) {
    // แทนที่เนื้อหาทั้งหมดที่ตรงกัน
    const updatedContent = fileContent.replace(new RegExp(searchStr.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), replaceStr);
    
    // บันทึกไฟล์ที่แก้ไขแล้ว
    fs.writeFileSync(filePath, updatedContent, 'utf8');
    
    console.log('✅ เพิ่มเมนูการตั้งค่าส่วนตัวเรียบร้อยแล้ว');
  } else {
    console.error('❌ ไม่พบข้อความที่ต้องการแก้ไข');
  }
} catch (error) {
  console.error('เกิดข้อผิดพลาดในการแก้ไขไฟล์:', error);
}