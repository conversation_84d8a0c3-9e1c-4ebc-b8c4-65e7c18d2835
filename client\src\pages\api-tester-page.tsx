import { useState } from "react";
import { DashboardLayout } from "@/components/layouts/dashboard-layout";
import { Card, CardContent, CardDescription, CardHeader, CardT<PERSON>le, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Code, FileCheck, AlertTriangle, Check, Info, Copy, Upload, Sparkles, Loader2 } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { useQuery } from "@tanstack/react-query";
import { motion } from "framer-motion";
import { Separator } from "@/components/ui/separator";

export default function ApiTesterPage() {
  const [apiKey, setApiKey] = useState("");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [responseData, setResponseData] = useState<any>(null);
  const [responseError, setResponseError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("verify-slip");
  
  // ดึงข้อมูล API Keys ของผู้ใช้
  const { data: apiKeys } = useQuery({
    queryKey: ['/api/user/api-keys'],
  });
  
  // คัดลอก API Key ไปยังคลิปบอร์ด
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(
      function () {
        toast({
          title: "คัดลอกสำเร็จ",
          description: "API Key ถูกคัดลอกไปยังคลิปบอร์ดแล้ว",
        });
      },
      function (err) {
        toast({
          title: "คัดลอกไม่สำเร็จ",
          description: "ไม่สามารถคัดลอก API Key ได้",
          variant: "destructive",
        });
      }
    );
  };
  
  // เมื่อผู้ใช้เลือกไฟล์
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0]);
    }
  };
  
  // ทดสอบ API
  const handleTestApi = async () => {
    if (!apiKey) {
      toast({
        title: "กรุณาระบุ API Key",
        description: "ต้องใช้ API Key ในการทดสอบ API",
        variant: "destructive",
      });
      return;
    }
    
    if (activeTab === "verify-slip" && !selectedFile) {
      toast({
        title: "กรุณาเลือกไฟล์สลิป",
        description: "ต้องมีไฟล์สลิปในการทดสอบ API",
        variant: "destructive",
      });
      return;
    }
    
    setIsLoading(true);
    // ล้างผลลัพธ์เดิม
    setResponseData(null);
    setResponseError(null);
    
    try {
      console.log('เริ่มทดสอบ API การตรวจสอบสลิป');
      
      // สร้าง form data สำหรับส่งไฟล์
      const formData = new FormData();
      if (selectedFile) {
        formData.append("slip_image", selectedFile);
        console.log('เพิ่มไฟล์ลงใน FormData:', selectedFile.name, selectedFile.type, selectedFile.size);
      }
      
      // เรียกใช้ API ผ่าน endpoint ปกติ
      console.log('กำลังเรียกใช้ API ด้วย key:', apiKey);
      
      // ใช้ fetch ที่มีการ timeout เพื่อให้รู้ว่ามีปัญหาการเชื่อมต่อ
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 วินาที timeout
      
      console.log('เริ่มเรียก API แบบมี timeout - ใช้ API จริงเพื่อหักเครดิต');
      const response = await fetch("/api/verify/slip", {
        method: "POST",
        headers: {
          "X-API-Key": apiKey,
          "Accept": "application/json"
        },
        body: formData,
        signal: controller.signal,
        credentials: 'include' // ใช้ credentials เพื่อส่ง session cookies
      });
      
      // ยกเลิก timeout หลังจากได้รับการตอบกลับ
      clearTimeout(timeoutId);
      console.log('ได้รับการตอบสนองจาก API:', response.status);
      const data = await response.json();
      
      if (response.ok) {
        setResponseData(data);
      } else {
        setResponseError(data.message || "เกิดข้อผิดพลาดในการเรียกใช้ API");
      }
    } catch (error) {
      console.error("API test error:", error);
      console.log('รายละเอียดข้อผิดพลาด:', error instanceof Error ? error.message : error);
      setResponseError("เกิดข้อผิดพลาดในการเชื่อมต่อกับ API: " + (error instanceof Error ? error.message : 'กรุณาตรวจสอบ console logs'));
    } finally {
      console.log('เสร็จสิ้นการทดสอบ API');
      setIsLoading(false);
    }
  };
  
  // แสดงการรูปแบบ JSON ในรูปแบบที่อ่านง่าย
  const formatJson = (json: any) => {
    return JSON.stringify(json, null, 2);
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center">
          <div className="mr-3 relative">
            <div className="absolute -inset-1 rounded-full bg-purple-600/30 animate-pulse blur-md"></div>
            <div className="relative bg-gradient-to-br from-purple-700 to-purple-900 h-10 w-10 rounded-full flex items-center justify-center">
              <Code className="h-5 w-5 text-purple-300" />
            </div>
          </div>
          <div>
            <h1 className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-100 to-white">
              ทดสอบ API
            </h1>
            <p className="text-indigo-300 text-sm">
              ทดสอบการใช้งาน API ที่เชื่อมต่อกับระบบ (มีการหักเครดิต)
            </p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card className="bg-gradient-to-br from-indigo-950/80 to-purple-950/90 border-indigo-800/40">
              <CardHeader>
                <CardTitle className="text-white">เลือก API Key</CardTitle>
                <CardDescription className="text-indigo-300">
                  เลือก API Key ที่ต้องการใช้ในการทดสอบ หรือป้อน API Key ของคุณ
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {apiKeys && Array.isArray(apiKeys) && apiKeys.length > 0 ? (
                  <div className="space-y-4">
                    <div className="bg-indigo-900/40 p-3 rounded-md border border-indigo-800/40 space-y-2">
                      <Label htmlFor="apiKey" className="text-indigo-300">API Keys ของคุณ</Label>
                      <div className="flex flex-wrap gap-2">
                        {apiKeys.map((key: any) => (
                          <Button
                            key={key.id}
                            variant="outline"
                            className={`border border-indigo-700/40 hover:bg-indigo-800/30 hover:text-amber-400 hover:border-amber-800/40 ${
                              apiKey === key.apiKey ? "bg-indigo-800/50 text-amber-400 border-amber-800/40" : "bg-indigo-900/30 text-indigo-300"
                            }`}
                            onClick={() => setApiKey(key.apiKey || "")}
                          >
                            <span className="font-mono text-xs mr-2">{key.name}</span>
                            <Check className={`h-3.5 w-3.5 ${apiKey === key.apiKey ? "opacity-100" : "opacity-0"}`} />
                          </Button>
                        ))}
                      </div>
                    </div>
                  </div>
                ) : (
                  <Alert className="bg-amber-900/30 border-amber-800/40 text-amber-100">
                    <Info className="h-4 w-4 text-amber-400" />
                    <AlertTitle className="text-amber-200">ไม่พบ API Key</AlertTitle>
                    <AlertDescription className="text-amber-300">
                      คุณยังไม่มี API Key สร้าง API Key ใหม่ในหน้า <a href="/api-keys" className="text-amber-400 hover:underline">API Keys</a>
                    </AlertDescription>
                  </Alert>
                )}
                
                <div className="pt-2">
                  <Label htmlFor="custom-api-key" className="text-indigo-200">หรือป้อน API Key</Label>
                  <div className="flex mt-1.5">
                    <Input
                      id="custom-api-key"
                      value={apiKey}
                      onChange={(e) => setApiKey(e.target.value)}
                      placeholder="ป้อน API Key ของคุณ"
                      className="flex-1 bg-indigo-900/30 border-indigo-700/40 text-indigo-100 placeholder:text-indigo-400/50"
                    />
                    {apiKey && (
                      <Button 
                        variant="ghost"
                        className="ml-2 text-indigo-300 hover:text-amber-400 hover:bg-transparent"
                        onClick={() => copyToClipboard(apiKey)}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <Card className="bg-gradient-to-br from-indigo-950/80 to-purple-950/90 border-indigo-800/40">
              <CardHeader>
                <CardTitle className="text-white">ทดสอบ API</CardTitle>
                <CardDescription className="text-indigo-300">
                  เลือกประเภท API ที่ต้องการทดสอบ (การทดสอบจะใช้ API จริงและหักเครดิต)
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="verify-slip" value={activeTab} onValueChange={setActiveTab} className="w-full">
                  <TabsList className="bg-indigo-950/60 border border-indigo-800/40 p-1 mb-4">
                    <TabsTrigger 
                      value="verify-slip" 
                      className="data-[state=active]:bg-indigo-700/40 data-[state=active]:text-indigo-100"
                    >
                      <FileCheck className="h-4 w-4 mr-2" />
                      ตรวจสอบสลิป
                    </TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="verify-slip" className="space-y-4">
                    <div className="bg-indigo-900/30 p-4 rounded-md border border-indigo-800/40">
                      <Label htmlFor="slip-image" className="text-indigo-200 mb-2 block">อัปโหลดรูปภาพสลิป</Label>
                      <div className={`border-2 border-dashed rounded-md flex flex-col items-center justify-center py-6 px-4 ${
                        selectedFile ? 'border-amber-600/60 bg-amber-900/10' : 'border-indigo-700/40 bg-indigo-900/20'
                      }`}>
                        {selectedFile ? (
                          <div className="space-y-2 text-center">
                            <div className="h-12 w-12 bg-amber-900/40 rounded-full flex items-center justify-center mx-auto">
                              <FileCheck className="h-6 w-6 text-amber-400" />
                            </div>
                            <div>
                              <p className="text-sm font-medium text-amber-300">{selectedFile.name}</p>
                              <p className="text-xs text-indigo-300">
                                {(selectedFile.size / 1024).toFixed(2)} KB
                              </p>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-red-400 hover:text-red-300 hover:bg-red-900/20"
                              onClick={() => setSelectedFile(null)}
                            >
                              เปลี่ยนไฟล์
                            </Button>
                          </div>
                        ) : (
                          <div className="space-y-2 text-center">
                            <div className="h-12 w-12 bg-indigo-900/40 rounded-full flex items-center justify-center mx-auto">
                              <Upload className="h-6 w-6 text-indigo-400" />
                            </div>
                            <div className="text-indigo-300">
                              <p className="text-indigo-200">ลากและวางไฟล์ หรือ</p>
                              <Label 
                                htmlFor="file-upload" 
                                className="relative cursor-pointer rounded-md font-medium text-amber-400 hover:text-amber-300 focus-within:outline-none"
                              >
                                <span>อัปโหลดรูปภาพ</span>
                                <input
                                  id="file-upload"
                                  name="file-upload"
                                  type="file"
                                  accept="image/*"
                                  className="sr-only"
                                  onChange={handleFileChange}
                                />
                              </Label>
                            </div>
                            <p className="text-xs text-indigo-400">
                              PNG, JPG, GIF สูงสุด 10MB
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex justify-center">
                      <Button 
                        onClick={handleTestApi}
                        disabled={isLoading}
                        className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white shadow-lg shadow-amber-500/20 w-full md:w-auto px-8"
                      >
                        {isLoading ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            กำลังทดสอบ
                          </>
                        ) : (
                          <>
                            <Sparkles className="h-4 w-4 mr-2" />
                            ทดสอบ API
                          </>
                        )}
                      </Button>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </motion.div>
          
          {(responseData || responseError) && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Card className="bg-gradient-to-br from-indigo-950/80 to-purple-950/90 border-indigo-800/40">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    ผลลัพธ์การทดสอบ
                    {responseData && <Check className="h-5 w-5 text-green-400 ml-2" />}
                    {responseError && <AlertTriangle className="h-5 w-5 text-red-400 ml-2" />}
                  </CardTitle>
                  <CardDescription className="text-indigo-300">
                    {responseData ? "การทดสอบสำเร็จ" : "การทดสอบล้มเหลว"}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {responseError && (
                    <Alert className="bg-red-900/20 border-red-800/40 text-red-200 mb-4">
                      <AlertTriangle className="h-4 w-4 text-red-400" />
                      <AlertTitle className="text-red-200">เกิดข้อผิดพลาด</AlertTitle>
                      <AlertDescription className="text-red-300">
                        {responseError}
                      </AlertDescription>
                    </Alert>
                  )}
                  
                  <div className="bg-indigo-950 border border-indigo-800/40 rounded-md overflow-hidden">
                    <div className="bg-indigo-900/40 px-4 py-2 flex justify-between items-center">
                      <div className="font-mono text-sm text-indigo-300">Response</div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 px-2 text-indigo-300 hover:text-amber-300 hover:bg-transparent"
                        onClick={() => copyToClipboard(JSON.stringify(responseData || { error: responseError }, null, 2))}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                    <pre className="p-4 overflow-auto text-sm font-mono text-indigo-200 whitespace-pre-wrap max-h-96">
                      {formatJson(responseData || { error: responseError })}
                    </pre>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}