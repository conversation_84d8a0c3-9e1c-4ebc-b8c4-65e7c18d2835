import { useState } from "react";
import { DashboardLayout } from "@/components/layouts/dashboard-responsive-new";
import { SlipUploader } from "@/components/slip/slip-uploader";
import { SlipResult } from "@/components/slip/slip-result";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import {
  AlertTriangle,
  FileImage,
  Code,
  Info,
  ArrowRight,
  FileCheck,
  Sparkles,
  Diamond,
  Star,
  BadgeCheck,
  Shield,
  Zap,
  CloudLightning
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { motion, AnimatePresence } from "framer-motion";

interface VerificationResult {
  status: number;
  data?: any;
  message?: string;
}

interface UserPackage {
  isActive: boolean;
  requestsUsed: number;
  package: {
    requestsLimit: number;
    name: string;
    creditPerVerification?: number;
  }
}

export default function SlipVerifyPage() {
  const [verificationResult, setVerificationResult] = useState<VerificationResult | null>(null);

  // ดึงข้อมูลแพ็กเกจที่ใช้งานอยู่
  const { data: activePackage } = useQuery<UserPackage>({
    queryKey: ['/api/user/active-package'],
  });

  // ดึงข้อมูลเครดิตของผู้ใช้
  const { data: userCredit } = useQuery<{ credit: number }>({
    queryKey: ['/api/user/credit'],
  });

  // ตรวจสอบว่ามีแพ็กเกจใช้งานอยู่หรือไม่
  const hasActivePackage = activePackage && activePackage.isActive;

  // ตรวจสอบว่าครบจำนวนการใช้งานหรือไม่
  const hasReachedLimit = hasActivePackage &&
    activePackage.requestsUsed >= activePackage.package.requestsLimit;

  // ตรวจสอบว่ามีเครดิตพอสำหรับการตรวจสอบเพิ่มเติมหรือไม่
  const creditPerVerification = activePackage?.package.creditPerVerification || 0;
  const hasEnoughCredit = (userCredit?.credit || 0) >= creditPerVerification;

  // คำนวณจำนวนการใช้งานที่เหลือ
  const remainingRequests = hasActivePackage
    ? activePackage.package.requestsLimit - activePackage.requestsUsed
    : 0;

  // จัดการผลลัพธ์การตรวจสอบ
  const handleVerificationComplete = (result: VerificationResult) => {
    setVerificationResult(result);
  };

  return (
    <DashboardLayout>
      <div className="relative z-10">
        {/* Decorative Background Elements */}
        <div className="absolute -top-12 left-1/4 w-20 h-20 rounded-full bg-purple-600/10 filter blur-2xl"></div>
        <div className="absolute top-60 right-10 w-32 h-32 rounded-full bg-indigo-600/20 filter blur-3xl"></div>
        <div className="absolute bottom-40 left-20 w-24 h-24 rounded-full bg-amber-600/10 filter blur-2xl"></div>

        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="relative z-10 container mx-auto py-6 max-w-6xl"
        >
          <div className="flex items-center mb-6">
            <div className="mr-3 relative">
              <div className="absolute -inset-1 rounded-full bg-purple-600/30 animate-pulse blur-md"></div>
              <div className="relative bg-gradient-to-br from-purple-700 to-purple-900 h-10 w-10 rounded-full flex items-center justify-center">
                <FileCheck className="h-5 w-5 text-purple-300" />
              </div>
            </div>
            <div>
              <h1 className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-100 to-white">
                ตรวจสอบสลิปด้วยพลังเทพเจ้า
              </h1>
              <p className="text-indigo-300 text-sm">
                อัปโหลดรูปภาพสลิปเพื่อตรวจสอบความถูกต้องด้วยพลังอันศักดิ์สิทธิ์
              </p>
            </div>
          </div>

          {/* แสดงการแจ้งเตือนกรณีไม่มีแพ็กเกจหรือใช้งานเกินโควต้า */}
          <AnimatePresence>
            {!hasActivePackage && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
              >
                <Alert className="bg-red-900/40 border-red-800/50 text-red-100 mb-6">
                  <AlertTriangle className="h-4 w-4 text-red-400" />
                  <AlertTitle className="text-red-200">ไม่มีพลังเทพเจ้าที่ใช้งานได้</AlertTitle>
                  <AlertDescription className="text-red-300">
                    คุณยังไม่ได้รับพรจากเทพเจ้าหรือพรของคุณหมดอายุแล้ว กรุณาร่ายมนตร์ใหม่เพื่อขอพรเทพเจ้าในการตรวจสอบสลิป
                  </AlertDescription>
                </Alert>
              </motion.div>
            )}

            {hasReachedLimit && hasEnoughCredit && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
              >
                <Alert className="bg-indigo-900/40 border-indigo-800/50 text-indigo-100 mb-6">
                  <div className="relative mr-1">
                    <div className="absolute -inset-1 rounded-full bg-amber-600/20 animate-pulse blur-sm"></div>
                    <Diamond className="h-4 w-4 text-amber-400 relative" />
                  </div>
                  <AlertTitle className="text-indigo-100">
                    พร้อมใช้พลังเทพเจ้าเพิ่มเติมด้วยเครดิต
                  </AlertTitle>
                  <AlertDescription className="text-indigo-300">
                    <p>คุณได้ใช้พลังเทพเจ้าในแพ็กเกจ <span className="text-amber-400 font-semibold">{activePackage.package.name}</span> ครบตามจำนวนแล้ว</p>
                    <p className="mt-1">ระบบจะใช้พลังเครดิตในการตรวจสอบเพิ่มเติม (<span className="text-amber-400 font-semibold">{activePackage.package.creditPerVerification?.toFixed(2) || 0}</span> บาท/ครั้ง)</p>
                    <p className="mt-1">เครดิตคงเหลือ: <span className="text-amber-400 font-semibold">{userCredit?.credit.toFixed(2) || 0}</span> บาท</p>
                  </AlertDescription>
                </Alert>
              </motion.div>
            )}

            {hasReachedLimit && !hasEnoughCredit && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
              >
                <Alert className="bg-red-900/40 border-red-800/50 text-red-100 mb-6">
                  <AlertTriangle className="h-4 w-4 text-red-400" />
                  <AlertTitle className="text-red-200">เครดิตไม่เพียงพอสำหรับการตรวจสอบเพิ่มเติม</AlertTitle>
                  <AlertDescription className="text-red-300">
                    <p>คุณได้ใช้พลังเทพเจ้าในแพ็กเกจ <span className="text-amber-400 font-semibold">{activePackage.package.name}</span> ครบตามจำนวนแล้ว และมีเครดิตไม่เพียงพอ</p>
                    <p className="mt-1">ต้องการเครดิต: <span className="text-amber-400 font-semibold">{activePackage.package.creditPerVerification?.toFixed(2) || 0}</span> บาท/ครั้ง</p>
                    <p className="mt-1">เครดิตคงเหลือ: <span className="text-amber-400 font-semibold">{userCredit?.credit.toFixed(2) || 0}</span> บาท</p>
                    <p className="mt-2">กรุณาเติมเครดิตเพิ่มเติมเพื่อใช้พลังเทพเจ้าต่อ</p>
                  </AlertDescription>
                </Alert>
              </motion.div>
            )}

            {hasActivePackage && !hasReachedLimit && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
              >
                <Alert className="bg-indigo-900/40 border-indigo-800/50 text-indigo-100 mb-6">
                  <div className="relative mr-1">
                    <div className="absolute -inset-1 rounded-full bg-amber-600/20 animate-pulse blur-sm"></div>
                    <Sparkles className="h-4 w-4 text-amber-400 relative" />
                  </div>
                  <AlertTitle className="text-indigo-100">
                    พรเทพเจ้าปัจจุบัน: <span className="text-transparent bg-clip-text bg-gradient-to-r from-amber-300 to-amber-500">{activePackage.package.name}</span>
                  </AlertTitle>
                  <AlertDescription className="text-indigo-300">
                    คุณมีพลังเทพเจ้าคงเหลือ <span className="text-amber-400 font-semibold">{remainingRequests}</span> ครั้ง
                    จากทั้งหมด <span className="text-amber-400 font-semibold">{activePackage.package.requestsLimit}</span> ครั้ง
                  </AlertDescription>
                </Alert>
              </motion.div>
            )}
          </AnimatePresence>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
            {/* ส่วนอัปโหลดและผลลัพธ์ */}
            <div className="space-y-8">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
              >
                <Card className="backdrop-blur-md overflow-hidden bg-gradient-to-br from-indigo-950/80 to-purple-900/80 border-indigo-800/30 shadow-lg">
                  <CardHeader className="relative">
                    <div className="absolute inset-0 bg-gradient-to-br from-indigo-800/30 to-indigo-900/10"></div>
                    <div className="relative z-10 flex items-center">
                      <div className="mr-3 h-8 w-8 rounded-full bg-gradient-to-br from-indigo-600 to-indigo-800 flex items-center justify-center">
                        <FileImage className="h-4 w-4 text-indigo-100" />
                      </div>
                      <div>
                        <CardTitle className="text-indigo-100">อัปโหลดสลิปเพื่อรับพลังเทพเจ้า</CardTitle>
                        <CardDescription className="text-indigo-300">
                          อัปโหลดภาพสลิปธนาคารที่ต้องการตรวจสอบด้วยพลังอันศักดิ์สิทธิ์ รองรับไฟล์ JPG, PNG, GIF
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <SlipUploader
                      onVerificationComplete={handleVerificationComplete}
                    />
                  </CardContent>
                </Card>
              </motion.div>

              {/* แสดงผลลัพธ์การตรวจสอบ */}
              <AnimatePresence>
                {verificationResult && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5 }}
                  >
                    <SlipResult result={verificationResult} />
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* ส่วนคำแนะนำการใช้งาน */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Card className="backdrop-blur-md overflow-hidden bg-gradient-to-br from-indigo-950/80 to-purple-900/80 border-indigo-800/30 shadow-lg">
                <CardHeader className="relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-indigo-800/30 to-indigo-900/10"></div>
                  <div className="relative z-10 flex items-center">
                    <div className="mr-3 h-8 w-8 rounded-full bg-gradient-to-br from-indigo-600 to-indigo-800 flex items-center justify-center">
                      <Star className="h-4 w-4 text-indigo-100" />
                    </div>
                    <div>
                      <CardTitle className="text-indigo-100">คำแนะนำจากเทพเจ้า</CardTitle>
                      <CardDescription className="text-indigo-300">
                        เคล็ดวิชาในการใช้พลังเทพเจ้าอย่างมีประสิทธิภาพและแม่นยำ
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <Tabs defaultValue="image" className="w-full">
                    <TabsList className="grid w-full grid-cols-2 bg-indigo-950/50">
                      <TabsTrigger
                        value="image"
                        className="data-[state=active]:bg-indigo-700/40 data-[state=active]:text-indigo-100"
                      >
                        <BadgeCheck className="h-4 w-4 mr-2" />
                        คำแนะนำรูปภาพ
                      </TabsTrigger>
                      <TabsTrigger
                        value="api"
                        className="data-[state=active]:bg-indigo-700/40 data-[state=active]:text-indigo-100"
                      >
                        <Code className="h-4 w-4 mr-2" />
                        การใช้งาน API
                      </TabsTrigger>
                    </TabsList>

                    <TabsContent value="image" className="mt-4 space-y-4">
                      <div className="space-y-2 bg-indigo-900/30 rounded-md p-3 border border-indigo-700/30">
                        <div className="flex items-center">
                          <div className="h-8 w-8 rounded-full bg-indigo-900/70 flex items-center justify-center mr-2">
                            <Sparkles className="h-4 w-4 text-amber-400" />
                          </div>
                          <h3 className="font-semibold text-indigo-100">คุณภาพของรูปภาพ</h3>
                        </div>
                        <p className="text-sm text-indigo-300 ml-10">
                          ตรวจสอบให้แน่ใจว่ารูปภาพมีความชัดเจน ไม่มืด ไม่เบลอ และไม่มีแสงสะท้อน เพื่อให้เทพเจ้ามองเห็นข้อมูลได้อย่างชัดเจน
                        </p>
                      </div>

                      <Separator className="bg-indigo-800/30" />

                      <div className="space-y-2 bg-indigo-900/30 rounded-md p-3 border border-indigo-700/30">
                        <div className="flex items-center">
                          <div className="h-8 w-8 rounded-full bg-indigo-900/70 flex items-center justify-center mr-2">
                            <Shield className="h-4 w-4 text-amber-400" />
                          </div>
                          <h3 className="font-semibold text-indigo-100">ข้อมูลครบถ้วน</h3>
                        </div>
                        <p className="text-sm text-indigo-300 ml-10">
                          ภาพสลิปต้องแสดงข้อมูลสำคัญทั้งหมด เช่น ชื่อธนาคาร วันที่ เวลา จำนวนเงิน
                          ชื่อผู้ส่ง ชื่อผู้รับ และเลขอ้างอิงธุรกรรม เพื่อการอ่านที่แม่นยำ
                        </p>
                      </div>

                      <Separator className="bg-indigo-800/30" />

                      <div className="space-y-2 bg-indigo-900/30 rounded-md p-3 border border-indigo-700/30">
                        <div className="flex items-center">
                          <div className="h-8 w-8 rounded-full bg-indigo-900/70 flex items-center justify-center mr-2">
                            <CloudLightning className="h-4 w-4 text-amber-400" />
                          </div>
                          <h3 className="font-semibold text-indigo-100">ธนาคารที่รองรับ</h3>
                        </div>
                        <p className="text-sm text-indigo-300 ml-10">
                          พลังเทพเจ้ารองรับการตรวจสอบสลิปจากธนาคารทั้งหมดในประเทศไทย รวมถึงพร้อมเพย์และแอปพลิเคชันธนาคารต่างๆ
                        </p>
                      </div>

                      <Separator className="bg-indigo-800/30" />

                      <div className="space-y-2 bg-indigo-900/30 rounded-md p-3 border border-indigo-700/30">
                        <div className="flex items-center">
                          <div className="h-8 w-8 rounded-full bg-indigo-900/70 flex items-center justify-center mr-2">
                            <Diamond className="h-4 w-4 text-amber-400" />
                          </div>
                          <h3 className="font-semibold text-indigo-100">ขนาดไฟล์</h3>
                        </div>
                        <p className="text-sm text-indigo-300 ml-10">
                          ขนาดไฟล์ไม่ควรเกิน 5MB และควรเป็นไฟล์รูปภาพประเภท JPG, PNG หรือ GIF เพื่อการประมวลผลที่รวดเร็ว
                        </p>
                      </div>
                    </TabsContent>

                    <TabsContent value="api" className="mt-4 space-y-4">
                      <div className="space-y-2 bg-indigo-900/30 rounded-md p-3 border border-indigo-700/30">
                        <div className="flex items-center">
                          <div className="h-8 w-8 rounded-full bg-indigo-900/70 flex items-center justify-center mr-2">
                            <Zap className="h-4 w-4 text-amber-400" />
                          </div>
                          <h3 className="font-semibold text-indigo-100">Endpoint</h3>
                        </div>
                        <div className="flex items-center bg-indigo-950/80 p-2 rounded-md ml-10 border border-indigo-800/50">
                          <Code className="h-4 w-4 text-amber-400 mr-2" />
                          <code className="text-sm text-indigo-100">POST /api/v1/verify</code>
                        </div>
                      </div>

                      <Separator className="bg-indigo-800/30" />

                      <div className="space-y-2 bg-indigo-900/30 rounded-md p-3 border border-indigo-700/30">
                        <div className="flex items-center">
                          <div className="h-8 w-8 rounded-full bg-indigo-900/70 flex items-center justify-center mr-2">
                            <Shield className="h-4 w-4 text-amber-400" />
                          </div>
                          <h3 className="font-semibold text-indigo-100">Headers ที่จำเป็น</h3>
                        </div>
                        <div className="ml-10">
                          <code className="bg-indigo-950/80 text-indigo-100 px-2 py-1 rounded-md border border-indigo-800/50 block mb-2">
                            Content-Type: multipart/form-data
                          </code>
                          <code className="bg-indigo-950/80 text-indigo-100 px-2 py-1 rounded-md border border-indigo-800/50 block">
                            Authorization: Bearer YOUR_ACCESS_TOKEN
                          </code>
                        </div>
                      </div>

                      <Separator className="bg-indigo-800/30" />

                      <div className="space-y-2">
                        <div className="flex items-center">
                          <div className="h-8 w-8 rounded-full bg-indigo-900/70 flex items-center justify-center mr-2">
                            <Code className="h-4 w-4 text-amber-400" />
                          </div>
                          <h3 className="font-semibold text-indigo-100">ตัวอย่างการใช้งาน (Node.js)</h3>
                        </div>
                        <pre className="bg-neutral-950 text-neutral-300 p-3 rounded-md text-xs overflow-x-auto border border-indigo-800/50">
{`const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

const verifySlip = async () => {
  const formData = new FormData();
  formData.append('file', fs.createReadStream('slip.jpg'));

  try {
    const response = await axios.post(
      'https://api.slipkuy.com/api/v1/verify',
      formData,
      {
        headers: {
          'Authorization': 'Bearer YOUR_TOKEN',
          ...formData.getHeaders()
        }
      }
    );

    console.log(response.data);
  } catch (error) {
    console.error('Error:', error.response?.data);
  }
};

verifySlip();`}
                        </pre>
                      </div>

                      <div className="flex items-center justify-center gap-4">
                        <a href="/docs" className="inline-flex items-center text-amber-400 hover:text-amber-300 font-medium">
                          ดูเอกสาร API ฉบับเต็ม <ArrowRight className="ml-2 h-4 w-4" />
                        </a>

                        <a href="/api-tester" className="inline-flex items-center text-green-400 hover:text-green-300 font-medium">
                          ทดสอบ API <Code className="ml-2 h-4 w-4" />
                        </a>
                      </div>
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* Floating particles ประกอบฉาก */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            {Array.from({ length: 30 }).map((_, i) => (
              <motion.div
                key={i}
                className="absolute rounded-full bg-amber-300"
                initial={{
                  opacity: Math.random() * 0.5 + 0.3,
                  x: Math.random() * 100,
                  y: Math.random() * 100,
                }}
                animate={{
                  opacity: [Math.random() * 0.2 + 0.1, Math.random() * 0.5 + 0.3, Math.random() * 0.2 + 0.1],
                  y: [Math.random() * 100, Math.random() * 100 - 20, Math.random() * 100]
                }}
                transition={{
                  duration: Math.random() * 8 + 5,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
                style={{
                  width: `${Math.random() * 4 + 1}px`,
                  height: `${Math.random() * 4 + 1}px`,
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  boxShadow: `0 0 ${Math.random() * 6 + 3}px ${Math.random() * 3 + 1}px rgba(251, 191, 36, 0.3)`,
                }}
              />
            ))}
          </div>
        </motion.div>
      </div>

      <style dangerouslySetInnerHTML={{
        __html: `
        @keyframes floatParticle {
          0% { transform: translateY(0) rotate(0deg); }
          50% { transform: translateY(-10px) rotate(5deg); }
          100% { transform: translateY(0) rotate(0deg); }
        }

        @keyframes glowPulse {
          0% { opacity: 0.2; }
          100% { opacity: 0.6; }
        }
        `
      }} />
    </DashboardLayout>
  );
}