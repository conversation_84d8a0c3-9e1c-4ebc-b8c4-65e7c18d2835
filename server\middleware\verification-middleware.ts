import { Request, Response, NextFunction } from "express";

/**
 * มิดเดิลแวร์สำหรับตรวจสอบว่าผู้ใช้ได้ยืนยันตัวตนแล้วหรือไม่
 * จะอนุญาตให้เข้าถึงเส้นทางที่ต้องการการยืนยันตัวตนเมื่อผู้ใช้ได้ยืนยันอีเมลหรือเบอร์โทรศัพท์แล้วเท่านั้น
 */
export function requireVerified(req: Request, res: Response, next: NextFunction) {
  // ตรวจสอบว่าผู้ใช้เข้าสู่ระบบแล้ว
  if (!req.isAuthenticated()) {
    return res.status(401).json({
      success: false,
      message: "กรุณาเข้าสู่ระบบ",
      code: "NOT_AUTHENTICATED"
    });
  }

  // ตรวจสอบว่าผู้ใช้มีข้อมูลหรือไม่
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: "ไม่พบข้อมูลผู้ใช้",
      code: "USER_NOT_FOUND"
    });
  }

  // ตรวจสอบว่าผู้ใช้ได้ยืนยันอีเมลหรือเบอร์โทรศัพท์แล้ว
  const isVerified = req.user.email_verified || req.user.phone_verified;

  if (!isVerified) {
    return res.status(403).json({
      success: false,
      message: "กรุณายืนยันตัวตนก่อนเข้าใช้งาน",
      code: "VERIFICATION_REQUIRED",
      needVerification: true,
      email: req.user.email,
      phoneNumber: req.user.phoneNumber
    });
  }

  // ถ้าผู้ใช้ยืนยันตัวตนแล้ว ให้ดำเนินการต่อไป
  next();
}

/**
 * มิดเดิลแวร์สำหรับตรวจสอบว่าผู้ใช้เป็นแอดมินหรือไม่
 */
export function requireAdmin(req: Request, res: Response, next: NextFunction) {
  // ตรวจสอบว่าผู้ใช้เข้าสู่ระบบแล้ว
  if (!req.isAuthenticated()) {
    return res.status(401).json({
      success: false,
      message: "กรุณาเข้าสู่ระบบ",
      code: "NOT_AUTHENTICATED"
    });
  }

  // ตรวจสอบว่าผู้ใช้เป็นแอดมินหรือไม่
  if (req.user?.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: "คุณไม่มีสิทธิ์เข้าถึงส่วนนี้",
      code: "NOT_ADMIN"
    });
  }

  // ถ้าผู้ใช้เป็นแอดมิน ให้ดำเนินการต่อไป
  next();
}