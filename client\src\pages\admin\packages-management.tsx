import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";
import {
  Search,
  RefreshCw,
  Edit,
  Trash,
  Plus,
  Package,
  ToggleRight,
  Tag,
  ListChecks,
  GripVertical,
  BarChart4,
  Percent
} from "lucide-react";
import { Admin } from "@/components/layouts/admin-layout";
import { useAuth } from "@/hooks/use-auth";
import { apiRequest } from "@/lib/queryClient";
import { queryClient } from "@/lib/queryClient";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { formatCurrency } from "@/lib/utils";
import { z } from "zod";
import { Package as PackageType } from "@shared/schema";

// components จาก dnd-kit
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

// สร้าง Schema สำหรับแพ็กเกจ
const packageSchema = z.object({
  name: z.string().min(1, "กรุณากรอกชื่อแพ็กเกจ"),
  description: z.string().min(1, "กรุณากรอกรายละเอียดแพ็กเกจ"),
  price: z.coerce.number().min(0, "ราคาต้องไม่ต่ำกว่า 0"),
  discount3Months: z.coerce.number().min(0, "ส่วนลดต้องไม่ต่ำกว่า 0").max(100, "ส่วนลดต้องไม่เกิน 100%"),
  discount6Months: z.coerce.number().min(0, "ส่วนลดต้องไม่ต่ำกว่า 0").max(100, "ส่วนลดต้องไม่เกิน 100%"),
  discount12Months: z.coerce.number().min(0, "ส่วนลดต้องไม่ต่ำกว่า 0").max(100, "ส่วนลดต้องไม่เกิน 100%"),
  durationDays: z.coerce.number().min(1, "ระยะเวลาต้องไม่ต่ำกว่า 1 วัน"),
  requestsLimit: z.coerce.number().min(1, "จำนวนครั้งต้องไม่ต่ำกว่า 1"),
  isActive: z.boolean().default(true),
  features: z.string().min(1, "กรุณากรอกคุณสมบัติของแพ็กเกจ")
});

type PackageFormValues = z.infer<typeof packageSchema>;

// คอมโพเนนต์แถวแพ็กเกจที่ลากได้
function SortablePackageRow({
  package: pkg,
  onEdit,
  onDelete
}: {
  package: PackageType;
  onEdit: (pkg: PackageType) => void;
  onDelete: (pkg: PackageType) => void;
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id: pkg.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 1 : 0,
  };

  return (
    <TableRow
      ref={setNodeRef}
      style={style}
      className={`transition-colors hover:bg-indigo-50/50 ${isDragging ? 'border border-indigo-300 bg-indigo-50' : ''}`}
    >
      <TableCell className="w-10">
        <div
          className="cursor-move flex items-center justify-center p-1.5 rounded-md hover:bg-indigo-100"
          {...attributes}
          {...listeners}
        >
          <GripVertical className="h-4 w-4 text-indigo-400" />
        </div>
      </TableCell>
      <TableCell>
        <div className="font-medium text-indigo-800">{pkg.name}</div>
        <div className="text-sm text-indigo-600/70 line-clamp-1">
          {pkg.description}
        </div>
        {(pkg.discount3Months > 0 || pkg.discount6Months > 0 || pkg.discount12Months > 0) && (
          <div className="flex items-center gap-1 mt-1">
            <Percent className="h-3 w-3 text-purple-500" />
            <span className="text-xs text-purple-600 font-medium">
              ส่วนลด: {pkg.discount3Months}%, {pkg.discount6Months}%, {pkg.discount12Months}%
            </span>
          </div>
        )}
      </TableCell>
      <TableCell className="font-medium text-indigo-800">
        {formatCurrency(pkg.price)}
      </TableCell>
      <TableCell className="font-medium text-indigo-800">
        {pkg.requestsLimit.toLocaleString()}
      </TableCell>
      <TableCell>
        {pkg.isActive ? (
          <Badge className="bg-gradient-to-r from-green-50 to-green-100 text-green-700 border-green-200">
            ใช้งาน
          </Badge>
        ) : (
          <Badge className="bg-gradient-to-r from-red-50 to-red-100 text-red-700 border-red-200">
            ไม่ได้ใช้งาน
          </Badge>
        )}
      </TableCell>
      <TableCell>
        <div className="text-sm line-clamp-1 font-medium text-indigo-600">
          {Array.isArray(pkg.features) ? pkg.features.length : 0} รายการ
        </div>
      </TableCell>
      <TableCell>
        <div className="flex justify-end gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onEdit(pkg)}
            className="text-indigo-600 hover:text-indigo-700 hover:bg-indigo-100"
          >
            <Edit className="h-4 w-4" />
            <span className="sr-only">แก้ไข</span>
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onDelete(pkg)}
            className="text-red-600 hover:text-red-700 hover:bg-red-100"
          >
            <Trash className="h-4 w-4" />
            <span className="sr-only">ลบ</span>
          </Button>
        </div>
      </TableCell>
    </TableRow>
  );
}

export default function PackagesManagement() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [search, setSearch] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [selectedPackage, setSelectedPackage] = useState<PackageType | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Sensors สำหรับ DnD Kit
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // ฟอร์มสำหรับเพิ่ม/แก้ไขแพ็กเกจ
  const packageForm = useForm<PackageFormValues>({
    resolver: zodResolver(packageSchema),
    defaultValues: {
      name: "",
      description: "",
      price: 0,
      discount3Months: 10,
      discount6Months: 15,
      discount12Months: 20,
      durationDays: 30,
      requestsLimit: 100,
      isActive: true,
      features: ""
    }
  });

  // ดึงข้อมูลแพ็กเกจทั้งหมด
  const { data: packages, isLoading, refetch } = useQuery<PackageType[]>({
    queryKey: ['/api/packages'],
  });

  // Mutation สำหรับเพิ่มแพ็กเกจใหม่
  const addPackageMutation = useMutation({
    mutationFn: async (packageData: Omit<PackageFormValues, "features"> & { features: string[] }) => {
      const res = await apiRequest("POST", "/api/admin/packages", packageData);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "เพิ่มแพ็กเกจสำเร็จ",
        description: "แพ็กเกจใหม่ถูกเพิ่มเข้าสู่ระบบเรียบร้อยแล้ว",
      });
      setIsAddDialogOpen(false);
      packageForm.reset();
      queryClient.invalidateQueries({ queryKey: ['/api/packages'] });
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับอัปเดตแพ็กเกจ
  const updatePackageMutation = useMutation({
    mutationFn: async (packageData: Omit<PackageFormValues, "features"> & { features: string[], id: number }) => {
      const { id, ...data } = packageData;
      const res = await apiRequest("PATCH", `/api/admin/packages/${id}`, data);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "อัปเดตแพ็กเกจสำเร็จ",
        description: "ข้อมูลแพ็กเกจถูกอัปเดตเรียบร้อยแล้ว",
      });
      setIsEditDialogOpen(false);
      queryClient.invalidateQueries({ queryKey: ['/api/packages'] });
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับลบแพ็กเกจ
  const deletePackageMutation = useMutation({
    mutationFn: async (id: number) => {
      const res = await apiRequest("DELETE", `/api/admin/packages/${id}`, {});
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "ลบแพ็กเกจสำเร็จ",
        description: "แพ็กเกจถูกลบออกจากระบบเรียบร้อยแล้ว",
      });
      setIsDeleteDialogOpen(false);
      queryClient.invalidateQueries({ queryKey: ['/api/packages'] });
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับจัดเรียงลำดับแพ็กเกจ
  const reorderPackagesMutation = useMutation({
    mutationFn: async (items: { id: number, sortOrder: number }[]) => {
      const res = await apiRequest("POST", "/api/admin/packages/reorder", { items });
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "จัดเรียงแพ็กเกจสำเร็จ",
        description: "ลำดับแพ็กเกจถูกอัปเดตเรียบร้อยแล้ว",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/packages'] });
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // กรองข้อมูลแพ็กเกจตามเงื่อนไขค้นหาและสถานะ
  const filteredPackages = packages?.filter((pkg) => {
    // กรองตามข้อความค้นหา
    const searchMatch =
      search === "" ||
      pkg.name.toLowerCase().includes(search.toLowerCase()) ||
      pkg.description.toLowerCase().includes(search.toLowerCase());

    // กรองตามสถานะ
    const statusMatch = statusFilter === "all" ||
      (statusFilter === "active" && pkg.isActive) ||
      (statusFilter === "inactive" && !pkg.isActive);

    return searchMatch && statusMatch;
  });

  // เปิดไดอะล็อกเพิ่มแพ็กเกจใหม่
  const handleOpenAddDialog = () => {
    packageForm.reset({
      name: "",
      description: "",
      price: 0,
      discount3Months: 10,
      discount6Months: 15,
      discount12Months: 20,
      durationDays: 30,
      requestsLimit: 100,
      isActive: true,
      features: ""
    });
    setIsAddDialogOpen(true);
  };

  // เปิดไดอะล็อกแก้ไขแพ็กเกจ
  const handleOpenEditDialog = (pkg: PackageType) => {
    setSelectedPackage(pkg);
    packageForm.reset({
      name: pkg.name,
      description: pkg.description,
      price: pkg.price,
      discount3Months: pkg.discount3Months || 10,
      discount6Months: pkg.discount6Months || 15,
      discount12Months: pkg.discount12Months || 20,
      durationDays: pkg.durationDays || 30,
      requestsLimit: pkg.requestsLimit,
      isActive: pkg.isActive,
      features: Array.isArray(pkg.features) ? pkg.features.join("\n") : ""
    });
    setIsEditDialogOpen(true);
  };

  // เปิดไดอะล็อกลบแพ็กเกจ
  const handleOpenDeleteDialog = (pkg: PackageType) => {
    setSelectedPackage(pkg);
    setIsDeleteDialogOpen(true);
  };

  // บันทึกแพ็กเกจใหม่
  const onSubmitAddForm = (values: PackageFormValues) => {
    // แปลงข้อความคุณสมบัติเป็นอาร์เรย์
    const features = values.features.split("\n").filter(f => f.trim() !== "");

    addPackageMutation.mutate({
      ...values,
      features
    });
  };

  // บันทึกแพ็กเกจที่แก้ไข
  const onSubmitEditForm = (values: PackageFormValues) => {
    if (!selectedPackage) return;

    // แปลงข้อความคุณสมบัติเป็นอาร์เรย์
    const features = values.features.split("\n").filter(f => f.trim() !== "");

    updatePackageMutation.mutate({
      ...values,
      features,
      id: selectedPackage.id
    });
  };

  // จัดการเมื่อมีการลากวางแพ็กเกจเพื่อเรียงลำดับใหม่
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    // ถ้าไม่มีการเปลี่ยนตำแหน่ง หรือไม่มีข้อมูลแพ็กเกจหรือ over เป็น null
    if (!over || active.id === over.id || !filteredPackages) {
      return;
    }

    // หา index ของรายการที่ลากและรายการที่ปล่อย
    const oldIndex = filteredPackages.findIndex(p => p.id === active.id);
    const newIndex = filteredPackages.findIndex(p => p.id === over.id);

    // สร้างอาร์เรย์ใหม่โดยเรียงลำดับแพ็กเกจใหม่
    const reorderedPackages = arrayMove(filteredPackages, oldIndex, newIndex);

    // อัปเดตค่า sortOrder ตามลำดับใหม่
    const updatedItems = reorderedPackages.map((pkg, index) => ({
      id: pkg.id,
      sortOrder: index
    }));

    // ส่งข้อมูลไปอัปเดตบนเซิร์ฟเวอร์
    reorderPackagesMutation.mutate(updatedItems);
  };

  return (
    <Admin>
      <div className="space-y-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-purple-600">จัดการแพ็กเกจ</h1>
            <div className="lightning-bar w-24 my-1"></div>
            <p className="text-indigo-600/70">
              ดูและจัดการแพ็กเกจทั้งหมดในระบบ
            </p>
          </div>
          <Button
            onClick={handleOpenAddDialog}
            className="bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white"
          >
            <Plus className="h-4 w-4 mr-2" />
            เพิ่มแพ็กเกจใหม่
          </Button>
        </div>

        <Card className="border border-indigo-200 shadow-md">
          <CardHeader className="border-b border-indigo-100 bg-gradient-to-r from-indigo-50 to-indigo-100/50">
            <CardTitle className="text-indigo-700 flex items-center">
              <div className="p-1.5 rounded-full bg-gradient-to-r from-indigo-100 to-indigo-200 mr-2 flex items-center justify-center">
                <Package className="h-5 w-5 text-indigo-600" />
              </div>
              รายการแพ็กเกจ
            </CardTitle>
            <div className="lightning-bar w-20 my-1"></div>
            <CardDescription className="text-indigo-600/70">
              จัดการรายการแพ็กเกจทั้งหมดในระบบ แก้ไข เปลี่ยนสถานะ หรือลบแพ็กเกจ
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* ส่วนกรองและค้นหา */}
              <div className="flex flex-col gap-4 md:flex-row">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="ค้นหาแพ็กเกจ..."
                    className="pl-10"
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                  />
                </div>
                <Select
                  value={statusFilter}
                  onValueChange={setStatusFilter}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="สถานะ" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">ทั้งหมด</SelectItem>
                    <SelectItem value="active">ใช้งาน</SelectItem>
                    <SelectItem value="inactive">ไม่ได้ใช้งาน</SelectItem>
                  </SelectContent>
                </Select>
                <Button variant="outline" className="h-10 px-4 py-2" onClick={() => refetch()}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  รีเฟรช
                </Button>
              </div>

              {/* ตารางแสดงข้อมูล */}
              {isLoading ? (
                <div className="space-y-4">
                  {[...Array(3)].map((_, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <Skeleton className="h-6 w-1/4" />
                      <Skeleton className="h-6 w-1/5" />
                      <Skeleton className="h-6 w-1/6" />
                      <Skeleton className="h-6 w-1/6" />
                    </div>
                  ))}
                </div>
              ) : filteredPackages && filteredPackages.length > 0 ? (
                <div className="rounded-md border border-indigo-200">
                  <DndContext
                    sensors={sensors}
                    collisionDetection={closestCenter}
                    onDragEnd={handleDragEnd}
                  >
                    <Table>
                      <TableHeader className="bg-gradient-to-r from-indigo-50 to-indigo-100/50">
                        <TableRow>
                          <TableHead className="w-10"></TableHead>
                          <TableHead className="font-bold text-indigo-700">แพ็กเกจ</TableHead>
                          <TableHead className="font-bold text-indigo-700">ราคา</TableHead>
                          <TableHead className="font-bold text-indigo-700">จำนวนครั้ง</TableHead>
                          <TableHead className="font-bold text-indigo-700">สถานะ</TableHead>
                          <TableHead className="font-bold text-indigo-700">คุณสมบัติ</TableHead>
                          <TableHead className="font-bold text-indigo-700 text-right">การจัดการ</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        <SortableContext
                          items={filteredPackages.map(p => p.id)}
                          strategy={verticalListSortingStrategy}
                        >
                          {filteredPackages.map((pkg) => (
                            <SortablePackageRow
                              key={pkg.id}
                              package={pkg}
                              onEdit={handleOpenEditDialog}
                              onDelete={handleOpenDeleteDialog}
                            />
                          ))}
                        </SortableContext>
                      </TableBody>
                    </Table>
                  </DndContext>
                </div>
              ) : (
                <div className="py-8 text-center">
                  <p className="text-muted-foreground">ไม่พบข้อมูลแพ็กเกจ</p>
                  <Button className="mt-4" onClick={handleOpenAddDialog}>
                    <Plus className="h-4 w-4 mr-2" />
                    เพิ่มแพ็กเกจแรก
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* ไดอะล็อกเพิ่มแพ็กเกจใหม่ */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[550px]">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Package className="mr-2 h-5 w-5" />
              เพิ่มแพ็กเกจใหม่
            </DialogTitle>
            <DialogDescription>
              กรอกข้อมูลเพื่อสร้างแพ็กเกจใหม่ในระบบ
            </DialogDescription>
          </DialogHeader>
          <Form {...packageForm}>
            <form onSubmit={packageForm.handleSubmit(onSubmitAddForm)} className="space-y-4">
              <FormField
                control={packageForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>ชื่อแพ็กเกจ</FormLabel>
                    <FormControl>
                      <Input placeholder="ชื่อแพ็กเกจ" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={packageForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>รายละเอียด</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="รายละเอียดแพ็กเกจ"
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={packageForm.control}
                  name="price"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>ราคา (บาท)</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Tag className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                          <Input
                            type="number"
                            min="0"
                            placeholder="ราคา"
                            className="pl-10"
                            {...field}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={packageForm.control}
                  name="requestsLimit"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>จำนวนครั้งที่ใช้ได้</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <ListChecks className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                          <Input
                            type="number"
                            min="1"
                            placeholder="จำนวนครั้ง"
                            className="pl-10"
                            {...field}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="p-4 rounded-lg border border-indigo-200 bg-gradient-to-r from-indigo-50/50 to-purple-50/50 shadow-sm">
                <h3 className="text-base font-medium text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600 mb-3 flex items-center">
                  <BarChart4 className="h-4 w-4 mr-2 text-indigo-600" />
                  ส่วนลดสำหรับการสมัครระยะยาว
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={packageForm.control}
                    name="discount3Months"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-indigo-700">ส่วนลด 3 เดือน (%)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0"
                            max="100"
                            placeholder="เช่น 10"
                            className="border-indigo-200 focus-visible:ring-indigo-400"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription className="text-xs text-indigo-600/70">
                          ส่วนลดเมื่อลูกค้าสมัคร 3 เดือน
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={packageForm.control}
                    name="discount6Months"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-indigo-700">ส่วนลด 6 เดือน (%)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0"
                            max="100"
                            placeholder="เช่น 15"
                            className="border-indigo-200 focus-visible:ring-indigo-400"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription className="text-xs text-indigo-600/70">
                          ส่วนลดเมื่อลูกค้าสมัคร 6 เดือน
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={packageForm.control}
                    name="discount12Months"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-indigo-700">ส่วนลด 12 เดือน (%)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0"
                            max="100"
                            placeholder="เช่น 20"
                            className="border-indigo-200 focus-visible:ring-indigo-400"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription className="text-xs text-indigo-600/70">
                          ส่วนลดเมื่อลูกค้าสมัคร 12 เดือน
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <FormField
                control={packageForm.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">สถานะการใช้งาน</FormLabel>
                      <FormDescription>
                        กำหนดว่าจะเปิดใช้งานแพ็กเกจนี้ให้ลูกค้าสามารถสมัครได้หรือไม่
                      </FormDescription>
                    </div>
                    <FormControl>
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                        <span className={field.value ? "text-green-600" : "text-red-600"}>
                          {field.value ? "เปิดใช้งาน" : "ปิดใช้งาน"}
                        </span>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={packageForm.control}
                name="features"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>คุณสมบัติของแพ็กเกจ</FormLabel>
                    <FormDescription>
                      กรอกคุณสมบัติแต่ละรายการในบรรทัดใหม่ เพิ่ม ! ข้างหน้าสำหรับคุณสมบัติที่ไม่รวมในแพ็กเกจ
                    </FormDescription>
                    <FormControl>
                      <Textarea
                        placeholder="คุณสมบัติของแพ็กเกจ (แต่ละบรรทัดคือ 1 คุณสมบัติ)"
                        className="min-h-[150px] font-mono"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsAddDialogOpen(false)}
                >
                  ยกเลิก
                </Button>
                <Button
                  type="submit"
                  disabled={addPackageMutation.isPending}
                >
                  {addPackageMutation.isPending ? "กำลังบันทึก..." : "บันทึก"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* ไดอะล็อกแก้ไขแพ็กเกจ */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[550px]">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Edit className="mr-2 h-5 w-5" />
              แก้ไขแพ็กเกจ
            </DialogTitle>
            <DialogDescription>
              แก้ไขข้อมูลของแพ็กเกจ {selectedPackage?.name}
            </DialogDescription>
          </DialogHeader>
          <Form {...packageForm}>
            <form onSubmit={packageForm.handleSubmit(onSubmitEditForm)} className="space-y-4">
              <FormField
                control={packageForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>ชื่อแพ็กเกจ</FormLabel>
                    <FormControl>
                      <Input placeholder="ชื่อแพ็กเกจ" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={packageForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>รายละเอียด</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="รายละเอียดแพ็กเกจ"
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={packageForm.control}
                  name="price"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>ราคา (บาท)</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Tag className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                          <Input
                            type="number"
                            min="0"
                            placeholder="ราคา"
                            className="pl-10"
                            {...field}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={packageForm.control}
                  name="requestsLimit"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>จำนวนครั้งที่ใช้ได้</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <ListChecks className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                          <Input
                            type="number"
                            min="1"
                            placeholder="จำนวนครั้ง"
                            className="pl-10"
                            {...field}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="p-4 rounded-lg border border-indigo-200 bg-gradient-to-r from-indigo-50/50 to-purple-50/50 shadow-sm">
                <h3 className="text-base font-medium text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600 mb-3 flex items-center">
                  <BarChart4 className="h-4 w-4 mr-2 text-indigo-600" />
                  ส่วนลดสำหรับการสมัครระยะยาว
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={packageForm.control}
                    name="discount3Months"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-indigo-700">ส่วนลด 3 เดือน (%)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0"
                            max="100"
                            placeholder="เช่น 10"
                            className="border-indigo-200 focus-visible:ring-indigo-400"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription className="text-xs text-indigo-600/70">
                          ส่วนลดเมื่อลูกค้าสมัคร 3 เดือน
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={packageForm.control}
                    name="discount6Months"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-indigo-700">ส่วนลด 6 เดือน (%)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0"
                            max="100"
                            placeholder="เช่น 15"
                            className="border-indigo-200 focus-visible:ring-indigo-400"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription className="text-xs text-indigo-600/70">
                          ส่วนลดเมื่อลูกค้าสมัคร 6 เดือน
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={packageForm.control}
                    name="discount12Months"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-indigo-700">ส่วนลด 12 เดือน (%)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0"
                            max="100"
                            placeholder="เช่น 20"
                            className="border-indigo-200 focus-visible:ring-indigo-400"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription className="text-xs text-indigo-600/70">
                          ส่วนลดเมื่อลูกค้าสมัคร 12 เดือน
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <FormField
                control={packageForm.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">สถานะการใช้งาน</FormLabel>
                      <FormDescription>
                        กำหนดว่าจะเปิดใช้งานแพ็กเกจนี้ให้ลูกค้าสามารถสมัครได้หรือไม่
                      </FormDescription>
                    </div>
                    <FormControl>
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                        <span className={field.value ? "text-green-600" : "text-red-600"}>
                          {field.value ? "เปิดใช้งาน" : "ปิดใช้งาน"}
                        </span>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={packageForm.control}
                name="features"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>คุณสมบัติของแพ็กเกจ</FormLabel>
                    <FormDescription>
                      กรอกคุณสมบัติแต่ละรายการในบรรทัดใหม่ เพิ่ม ! ข้างหน้าสำหรับคุณสมบัติที่ไม่รวมในแพ็กเกจ
                    </FormDescription>
                    <FormControl>
                      <Textarea
                        placeholder="คุณสมบัติของแพ็กเกจ (แต่ละบรรทัดคือ 1 คุณสมบัติ)"
                        className="min-h-[150px] font-mono"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsEditDialogOpen(false)}
                >
                  ยกเลิก
                </Button>
                <Button
                  type="submit"
                  disabled={updatePackageMutation.isPending}
                >
                  {updatePackageMutation.isPending ? "กำลังบันทึก..." : "บันทึก"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* ไดอะล็อกลบแพ็กเกจ */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[450px]">
          <DialogHeader>
            <DialogTitle className="flex items-center text-red-600">
              <Trash className="mr-2 h-5 w-5" />
              ยืนยันการลบแพ็กเกจ
            </DialogTitle>
            <DialogDescription>
              คุณต้องการลบแพ็กเกจ "{selectedPackage?.name}" ใช่หรือไม่? การกระทำนี้ไม่สามารถย้อนกลับได้
            </DialogDescription>
          </DialogHeader>
          <div className="p-4 mt-2 rounded-lg bg-red-50 border border-red-200 text-red-800 text-sm">
            <p className="font-medium">คำเตือน:</p>
            <p className="mt-1">
              การลบแพ็กเกจจะส่งผลให้ลูกค้าไม่สามารถสมัครใช้งานแพ็กเกจนี้ได้อีกต่อไป อย่างไรก็ตาม
              ลูกค้าที่ใช้งานแพ็กเกจนี้อยู่แล้วจะยังคงใช้งานได้จนกว่าจะสิ้นสุดระยะเวลาการใช้งาน
            </p>
          </div>
          <DialogFooter className="mt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              ยกเลิก
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={() => selectedPackage && deletePackageMutation.mutate(selectedPackage.id)}
              disabled={deletePackageMutation.isPending}
            >
              {deletePackageMutation.isPending ? "กำลังลบ..." : "ลบแพ็กเกจ"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Admin>
  );
}