import { useState, useRef } from "react";
import { useToast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { UploadCloud, X, Image } from "lucide-react";
import { apiRequest } from "@/lib/queryClient";

interface SlipUploaderProps {
  onVerificationComplete: (result: any) => void;
}

export function SlipUploader({ onVerificationComplete }: SlipUploaderProps) {
  const { toast } = useToast();
  const [file, setFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const queryClient = useQueryClient();
  
  const verifyMutation = useMutation({
    mutationFn: async (formData: FormData) => {
      const xhr = new XMLHttpRequest();
      
      // ติดตามความคืบหน้าการอัปโหลด
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          setUploadProgress(Math.round((event.loaded / event.total) * 100));
        }
      });
      
      return new Promise((resolve, reject) => {
        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            resolve(JSON.parse(xhr.responseText));
          } else {
            try {
              reject(JSON.parse(xhr.responseText));
            } catch (e) {
              reject({ message: "การตรวจสอบล้มเหลว" });
            }
          }
        };
        xhr.onerror = () => {
          reject({ message: "การเชื่อมต่อล้มเหลว กรุณาลองใหม่อีกครั้ง" });
        };
        
        xhr.open('POST', '/api/v1/verify');
        xhr.withCredentials = true;
        xhr.send(formData);
      });
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/verifications'] });
      queryClient.invalidateQueries({ queryKey: ['/api/stats'] });
      onVerificationComplete(data);
      setUploadProgress(0);
      toast({
        title: "ตรวจสอบสำเร็จ",
        description: "ระบบได้ตรวจสอบรายละเอียดสลิปเรียบร้อยแล้ว",
      });
    },
    onError: (error: any) => {
      setUploadProgress(0);
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message || "ไม่สามารถตรวจสอบสลิปได้ กรุณาลองใหม่อีกครั้ง",
        variant: "destructive",
      });
    }
  });
  
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const selectedFile = event.target.files[0];
      
      // ตรวจสอบว่าเป็นไฟล์รูปภาพหรือไม่
      if (!selectedFile.type.match('image.*')) {
        toast({
          title: "ไฟล์ไม่ถูกต้อง",
          description: "กรุณาอัปโหลดไฟล์รูปภาพเท่านั้น",
          variant: "destructive",
        });
        return;
      }
      
      // ตรวจสอบขนาดไฟล์
      if (selectedFile.size > 5 * 1024 * 1024) { // 5MB
        toast({
          title: "ไฟล์มีขนาดใหญ่เกินไป",
          description: "กรุณาอัปโหลดไฟล์ขนาดไม่เกิน 5MB",
          variant: "destructive",
        });
        return;
      }
      
      setFile(selectedFile);
      
      // สร้าง URL สำหรับแสดงตัวอย่างรูปภาพ
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result as string);
      };
      reader.readAsDataURL(selectedFile);
    }
  };
  
  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    
    if (event.dataTransfer.files && event.dataTransfer.files[0]) {
      const droppedFile = event.dataTransfer.files[0];
      
      if (!droppedFile.type.match('image.*')) {
        toast({
          title: "ไฟล์ไม่ถูกต้อง",
          description: "กรุณาอัปโหลดไฟล์รูปภาพเท่านั้น",
          variant: "destructive",
        });
        return;
      }
      
      if (droppedFile.size > 5 * 1024 * 1024) {
        toast({
          title: "ไฟล์มีขนาดใหญ่เกินไป",
          description: "กรุณาอัปโหลดไฟล์ขนาดไม่เกิน 5MB",
          variant: "destructive",
        });
        return;
      }
      
      setFile(droppedFile);
      
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result as string);
      };
      reader.readAsDataURL(droppedFile);
    }
  };
  
  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
  };
  
  const removeFile = () => {
    setFile(null);
    setPreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!file) {
      toast({
        title: "ไม่พบไฟล์",
        description: "กรุณาอัปโหลดรูปภาพสลิปก่อนดำเนินการ",
        variant: "destructive",
      });
      return;
    }
    
    const formData = new FormData();
    formData.append('file', file);
    
    verifyMutation.mutate(formData);
  };
  
  return (
    <Card className="w-full">
      <CardContent className="pt-6">
        <form onSubmit={handleSubmit}>
          <div
            className={`border-2 border-dashed rounded-lg p-6 text-center ${
              preview ? 'border-primary-300 bg-primary-50' : 'border-gray-300 hover:border-primary-300'
            }`}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
          >
            {preview ? (
              <div className="relative">
                <img
                  src={preview}
                  alt="ตัวอย่างสลิป"
                  className="mx-auto max-h-64 rounded"
                />
                <button
                  type="button"
                  onClick={removeFile}
                  className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                >
                  <X className="h-4 w-4" />
                </button>
                <div className="mt-4">
                  <p className="text-sm text-gray-600 mb-1">{file?.name}</p>
                  <p className="text-xs text-gray-500">{(file?.size && (file.size / 1024 / 1024).toFixed(2)) || 0} MB</p>
                </div>
              </div>
            ) : (
              <div className="py-8">
                <input
                  type="file"
                  id="slip-image"
                  ref={fileInputRef}
                  onChange={handleFileChange}
                  className="hidden"
                  accept="image/*"
                />
                <UploadCloud className="mx-auto h-12 w-12 text-gray-400" />
                <p className="mt-2 text-sm font-medium text-gray-900">ลากและวางรูปภาพ หรือ <label htmlFor="slip-image" className="text-primary-600 hover:text-primary-700 cursor-pointer">คลิกเพื่อเลือกไฟล์</label></p>
                <p className="mt-1 text-xs text-gray-500">รองรับรูปภาพ JPG, PNG, GIF ขนาดไม่เกิน 5MB</p>
              </div>
            )}
          </div>
          
          {verifyMutation.isPending && (
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-700 mb-2">กำลังอัปโหลดและตรวจสอบ...</p>
              <Progress value={uploadProgress} className="h-2" />
            </div>
          )}
          
          <div className="mt-6 flex justify-center">
            <Button
              type="submit"
              disabled={!file || verifyMutation.isPending}
              className="w-full sm:w-auto"
              size="lg"
            >
              {verifyMutation.isPending ? "กำลังตรวจสอบ..." : "ตรวจสอบสลิป"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
