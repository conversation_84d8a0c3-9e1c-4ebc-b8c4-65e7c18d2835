# pm2-shell.nix
{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = with pkgs; [
    nodejs_20
    nodePackages.pm2
    postgresql_15
    redis
    libuuid
    openssl
    postgresql_15.lib
  ];

  shellHook = ''
    echo "🚀 SLIPKUY PM2 Production Environment"
    echo "📦 Node.js: $(node --version)"
    echo "📦 PM2: $(pm2 --version)"
    echo "📦 PostgreSQL: $(psql --version)"
    echo "📦 Redis: $(redis-server --version)"

    # ตั้งค่าตัวแปรสภาพแวดล้อมสำหรับฐานข้อมูลในเครื่อง
    export PGHOST=localhost
    export PGUSER=slipkuy_user
    export PGPASSWORD=slipkuy_password
    export PGDATABASE=slipkuy_db
    export PGPORT=5432
    export PGSSLMODE=disable

    # ตั้งค่า DATABASE_URL สำหรับแอพพลิเคชัน
    export DATABASE_URL="postgresql://slipkuy_user:slipkuy_password@localhost:5432/slipkuy_db"

    # ตั้งค่าตัวแปรสภาพแวดล้อมสำหรับ Redis
    export REDIS_HOST=localhost
    export REDIS_PORT=6379
    export REDIS_PASSWORD=""
    export REDIS_DB=0

    # ตั้งค่าตัวแปรสภาพแวดล้อมสำหรับ pg-native (ถ้าใช้)
    export LD_LIBRARY_PATH=${pkgs.postgresql_15.lib}/lib:$LD_LIBRARY_PATH
    export PGCLIENTENCODING=UTF8

    # สร้างโฟลเดอร์ที่จำเป็น
    mkdir -p logs
    mkdir -p public/uploads/slips

    echo "✅ สภาพแวดล้อมพร้อมใช้งานแล้ว!"
    echo ""
    echo "🚀 คำสั่งที่ใช้บ่อย:"
    echo "   - ./start-with-nix.sh : เริ่มเซิร์ฟเวอร์ด้วย PM2"
    echo "   - pm2 logs slipkuy    : ดูบันทึกการทำงาน"
    echo "   - pm2 stop slipkuy    : หยุดการทำงาน"
    echo "   - pm2 restart slipkuy : รีสตาร์ทแอพพลิเคชัน"
    echo "   - pm2 delete slipkuy  : ลบแอพพลิเคชันออกจาก PM2"
  '';
}
