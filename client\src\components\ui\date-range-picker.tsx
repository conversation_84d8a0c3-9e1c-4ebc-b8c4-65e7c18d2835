import * as React from "react"
import { format } from "date-fns"
import { th } from "date-fns/locale"
import { Calendar as CalendarIcon } from "lucide-react"
import { DateRange } from "react-day-picker"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

interface DatePickerWithRangeProps {
  className?: string
  date: DateRange | undefined
  setDate: (date: DateRange | undefined) => void
}

export function DatePickerWithRange({
  className,
  date,
  setDate,
}: DatePickerWithRangeProps) {
  return (
    <div className={cn("grid gap-2", className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"outline"}
            className={cn(
              "w-full justify-start text-left font-normal border-indigo-500/30 bg-gray-900/80 backdrop-blur-sm focus:ring-1 focus:ring-indigo-500 focus:ring-offset-0",
              !date && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4 text-indigo-400" />
            {date?.from ? (
              date.to ? (
                <>
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-cyan-500 to-indigo-500">
                    {format(date.from, "dd LLL yyyy", { locale: th })} -{" "}
                    {format(date.to, "dd LLL yyyy", { locale: th })}
                  </span>
                </>
              ) : (
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-cyan-500 to-indigo-500">
                  {format(date.from, "dd LLL yyyy", { locale: th })}
                </span>
              )
            ) : (
              <span className="text-white font-medium">เลือกวันที่</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0 bg-gray-900/90 backdrop-blur-md border-indigo-500/50" align="start">
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={date?.from}
            selected={date}
            onSelect={setDate}
            numberOfMonths={2}
            locale={th}
            className="bg-transparent text-white"
          />
        </PopoverContent>
      </Popover>
    </div>
  )
}