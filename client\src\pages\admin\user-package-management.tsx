import React, { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Admin } from "@/components/layouts/admin-layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Shield,
  ShieldAlert,
  PackageOpen,
  Star,
  User,
  Users,
  Shield as ShieldIcon,
  CheckCircle2,
  XCircle,
  Loader2
} from "lucide-react";
import { apiRequest, queryClient } from "@/lib/queryClient";

interface User {
  id: number;
  username: string;
  firstName?: string;
  lastName?: string;
  email: string;
  role: 'user' | 'admin';
  status: 'active' | 'inactive' | 'suspended';
  tier: 'standard' | 'premium' | 'vip' | 'enterprise';
  allowedPackages: number[] | null;
}

interface Package {
  id: number;
  name: string;
  description: string;
  price: number;
  tag?: string;
}

export default function UserPackageManagement() {
  const { toast } = useToast();
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedPackages, setSelectedPackages] = useState<number[]>([]);

  // ดึงข้อมูลผู้ใช้ทั้งหมด
  const { data: users, isLoading: isLoadingUsers, error: usersError, refetch: refetchUsers } = useQuery<User[]>({
    queryKey: ['/api/admin/users'],
    retry: 1,
    refetchOnWindowFocus: true
  });

  // ดึงข้อมูลแพ็กเกจทั้งหมด
  const { data: packages, isLoading: isLoadingPackages } = useQuery<Package[]>({
    queryKey: ['/api/packages'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/packages');
      return await res.json();
    }
  });

  // mutation สำหรับอัพเดทสิทธิ์การใช้แพ็กเกจของผู้ใช้
  const updateUserPackagesMutation = useMutation({
    mutationFn: async ({ userId, packageIds }: { userId: number, packageIds: number[] | null }) => {
      const res = await apiRequest('PATCH', `/api/admin/users/${userId}/allowed-packages`, {
        allowedPackages: packageIds
      });
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "อัพเดทสิทธิ์สำเร็จ",
        description: "อัพเดทสิทธิ์การใช้แพ็กเกจของผู้ใช้เรียบร้อยแล้ว",
        variant: "default",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users'] });
      setIsEditDialogOpen(false);
    },
    onError: (error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: `ไม่สามารถอัพเดทสิทธิ์การใช้แพ็กเกจได้: ${error.message}`,
        variant: "destructive",
      });
    }
  });

  // เปิด Dialog และเตรียมข้อมูลสำหรับการแก้ไข
  const handleEditUserPackages = (user: User) => {
    setSelectedUser(user);
    setSelectedPackages(user.allowedPackages || []);
    setIsEditDialogOpen(true);
  };

  // บันทึกการเปลี่ยนแปลง
  const handleSaveUserPackages = () => {
    if (!selectedUser) return;

    // ถ้าไม่มีแพ็กเกจที่เลือก (ทุกคนเข้าถึงได้) ให้ส่งค่า null
    const packagesToSave = selectedPackages.length > 0 ? selectedPackages : null;

    updateUserPackagesMutation.mutate({
      userId: selectedUser.id,
      packageIds: packagesToSave
    });
  };

  // สลับการเลือกแพ็กเกจ
  const togglePackageSelection = (packageId: number) => {
    setSelectedPackages(prev => {
      // ถ้ามีการเลือกแพ็กเกจนี้ไว้แล้ว ให้ลบออก
      if (prev.includes(packageId)) {
        const newSelection = prev.filter(id => id !== packageId);
        // ถ้าลบจนไม่มีแพ็กเกจที่เลือกแล้ว ให้ตั้งค่าเป็นอาร์เรย์ว่าง (อนุญาตทุกแพ็กเกจ)
        return newSelection.length > 0 ? newSelection : [];
      } else {
        // ถ้ายังไม่ได้เลือกแพ็กเกจนี้ ให้เพิ่มเข้าไป
        // ถ้าเป็นการเลือกแพ็กเกจตัวแรก (จากที่ไม่มีการเลือกเลย = อนุญาตทุกแพ็กเกจ)
        // ให้เปลี่ยนจากการอนุญาตทั้งหมดเป็นอนุญาตเฉพาะแพ็กเกจที่เลือก
        return [...prev, packageId];
      }
    });
  };

  // สถานะการเข้าถึงแพ็กเกจของผู้ใช้
  const getUserPackageAccess = (user: User) => {
    if (!user.allowedPackages) {
      return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-300">ไม่จำกัดสิทธิ์</Badge>;
    }
    if (user.allowedPackages.length === 0) {
      return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-300">ไม่มีสิทธิ์</Badge>;
    }
    return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-300">จำกัดสิทธิ์</Badge>;
  };

  const getPackageTag = (tag?: string) => {
    if (!tag) return null;

    switch (tag.toLowerCase()) {
      case 'ฟรี':
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-300">ฟรี</Badge>;
      case 'แนะนำ':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-300">แนะนำ</Badge>;
      case 'พิเศษ':
        return <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-300">พิเศษ</Badge>;
      case 'พื้นฐาน':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-300">พื้นฐาน</Badge>;
      case 'สำหรับร้านค้า':
        return <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-300">สำหรับร้านค้า</Badge>;
      default:
        return <Badge variant="outline">{tag}</Badge>;
    }
  };

  const getUserRoleBadge = (role: string) => {
    switch (role) {
      case 'admin':
        return <Badge variant="default" className="bg-red-500">ผู้ดูแลระบบ</Badge>;
      case 'user':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-300">ผู้ใช้งาน</Badge>;
      default:
        return <Badge variant="outline">{role}</Badge>;
    }
  };

  return (
    <Admin>
      <div className="flex flex-col gap-4 p-4">
        <div className="flex flex-row justify-between items-center">
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Shield className="h-8 w-8 text-primary" />
            <span>จัดการสิทธิ์การใช้แพ็กเกจ</span>
          </h1>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-primary" />
              <span>ผู้ใช้ทั้งหมด</span>
            </CardTitle>
            <CardDescription>
              กำหนดสิทธิ์การเข้าถึงแพ็กเกจสำหรับผู้ใช้งานแต่ละคน
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoadingUsers ? (
              <div className="py-8 flex justify-center">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow className="bg-muted/30">
                    <TableHead>ชื่อผู้ใช้</TableHead>
                    <TableHead>อีเมล</TableHead>
                    <TableHead>บทบาท</TableHead>
                    <TableHead>สิทธิ์การเข้าถึงแพ็กเกจ</TableHead>
                    <TableHead className="text-right">จัดการ</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users && users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-gray-500" />
                          {user.username}
                        </div>
                      </TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>{getUserRoleBadge(user.role)}</TableCell>
                      <TableCell>{getUserPackageAccess(user)}</TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditUserPackages(user)}
                          disabled={user.role === 'admin'} // ผู้ดูแลระบบมีสิทธิ์เข้าถึงทุกอย่างอยู่แล้ว
                        >
                          <ShieldIcon className="h-4 w-4 mr-1" />
                          แก้ไขสิทธิ์
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Modal สำหรับแก้ไขสิทธิ์ */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <ShieldAlert className="h-5 w-5 text-primary" />
              <span>กำหนดสิทธิ์การใช้แพ็กเกจ</span>
            </DialogTitle>
            <DialogDescription>
              สำหรับ <span className="font-bold">{selectedUser?.username}</span> ({selectedUser?.email})
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="all-packages-access"
                checked={selectedPackages.length === 0}
                onCheckedChange={(checked) => {
                  if (checked) {
                    setSelectedPackages([]);
                  } else {
                    // ถ้ายกเลิกการเลือก "อนุญาตให้เข้าถึงแพ็กเกจทั้งหมด" ให้เลือกแพ็กเกจแรกเป็นค่าเริ่มต้น
                    if (packages && packages.length > 0) {
                      setSelectedPackages([packages[0].id]);
                    }
                  }
                }}
              />
              <label
                htmlFor="all-packages-access"
                className="text-sm font-medium leading-none cursor-pointer flex items-center gap-1"
              >
                <Star className="h-4 w-4 text-amber-500" />
                อนุญาตให้เข้าถึงแพ็กเกจทั้งหมด (ค่าเริ่มต้น)
              </label>
            </div>

            <div className="border rounded-md p-4">
              <h3 className="text-sm font-semibold mb-3 flex items-center gap-1">
                <PackageOpen className="h-4 w-4 text-primary" />
                แพ็กเกจที่อนุญาตให้ใช้งาน
              </h3>

              {isLoadingPackages ? (
                <div className="py-4 flex justify-center">
                  <Loader2 className="h-6 w-6 animate-spin text-primary" />
                </div>
              ) : (
                <div className="space-y-2">
                  {packages && packages.map((pkg) => (
                    <div key={pkg.id} className="flex justify-between items-center p-2 border rounded-md">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id={`pkg-${pkg.id}`}
                          checked={selectedPackages.includes(pkg.id)}
                          onCheckedChange={() => togglePackageSelection(pkg.id)}
                        />
                        <label
                          htmlFor={`pkg-${pkg.id}`}
                          className="cursor-pointer flex flex-col text-sm"
                        >
                          <span className="font-medium flex items-center gap-1">
                            {pkg.name} {getPackageTag(pkg.tag)}
                          </span>
                          <span className="text-muted-foreground text-xs">฿{pkg.price} บาท</span>
                        </label>
                      </div>

                      <div>
                        {selectedPackages.includes(pkg.id) ? (
                          <CheckCircle2 className="h-5 w-5 text-green-500" />
                        ) : (
                          selectedPackages.length > 0 && (
                            <XCircle className="h-5 w-5 text-gray-300" />
                          )
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
            >
              ยกเลิก
            </Button>
            <Button
              onClick={handleSaveUserPackages}
              disabled={updateUserPackagesMutation.isPending}
            >
              {updateUserPackagesMutation.isPending && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              บันทึก
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Admin>
  );
}