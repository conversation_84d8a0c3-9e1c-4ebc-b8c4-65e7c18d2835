import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { DashboardLayout } from "@/components/layouts/dashboard-responsive-new";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { motion, AnimatePresence } from "framer-motion";
import {
  Search,
  CheckCircle2,
  XCircle,
  Clock,
  Info,
  ArrowRight,
  ChevronRight,
  Copy,
  FileCheck,
  Sparkles,
  Star,
  History,
  Sun,
  Coins,
  Gem,
  CloudLightning,
  CalendarDays,
  ReceiptText,
  Globe,
  Terminal,
  MonitorSmartphone,
  Database,
  Code
} from "lucide-react";
import { formatDate, formatNumber, formatCurrency } from "@/lib/utils";

// ประกาศ Interface สำหรับข้อมูลการตรวจสอบสลิป
interface SlipVerification {
  id: number;
  userId: number;
  transactionRef: string | null;
  bankName: string | null;
  amount: number | null;
  sender: string | null;
  receiver: string | null;
  transactionDate: string | null;
  status: string;
  responseData: string | null;
  verificationSource?: string; // ที่มาของการตรวจสอบ (เว็บ/API)
  apiKeyId?: number; // ID ของ API Key ที่ใช้ (ถ้ามี)
  apiKey?: {
    id: number;
    name: string;
    apiKey: string;
  };
  imagePath?: string | null; // พาธของรูปภาพสลิป
  createdAt: string;
}

// Interface สำหรับ Response Data ที่เก็บใน JSON
interface SlipResponseData {
  status: number;
  data?: {
    payload: string;
    transRef: string;
    date: string;
    countryCode: string;
    amount: {
      amount: number;
      local: {
        amount?: number;
        currency?: string;
      }
    };
    fee?: number;
    ref1?: string;
    ref2?: string;
    ref3?: string;
    sender: {
      bank: {
        id: string;
        name?: string;
        short?: string;
      };
      account: {
        name: {
          th?: string;
          en?: string;
        };
        bank?: {
          type: 'BANKAC' | 'TOKEN' | 'DUMMY';
          account: string;
        };
        proxy?: {
          type: 'NATID' | 'MSISDN' | 'EWALLETID' | 'EMAIL' | 'BILLERID';
          account: string;
        };
      };
    };
    receiver: {
      bank: {
        id: string;
        name?: string;
        short?: string;
      };
      account: {
        name: {
          th?: string;
          en?: string;
        };
        bank?: {
          type: 'BANKAC' | 'TOKEN' | 'DUMMY';
          account: string;
        };
        proxy?: {
          type: 'NATID' | 'MSISDN' | 'EWALLETID' | 'EMAIL' | 'BILLERID';
          account: string;
        };
      };
    };
  };
  message?: string;
}

// คอมโพเนนต์สำหรับแสดงรายละเอียดสลิปเต็มรูปแบบเหมือนสลิปธนาคารจริงๆ
// Modal สำหรับแสดงรูปภาพสลิป
function SlipImageModal({
  verification,
  open,
  onOpenChange
}: {
  verification: SlipVerification | null,
  open: boolean,
  onOpenChange: (open: boolean) => void
}) {
  const { toast } = useToast();
  const [imagePath, setImagePath] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // ดึงข้อมูลรูปภาพสลิปเมื่อโมดัลเปิด
  useEffect(() => {
    if (open && verification && verification.id) {
      setLoading(true);
      setError(null);

      fetch(`/api/slip-image/${verification.id}`)
        .then(res => {
          if (!res.ok) {
            throw new Error(`HTTP error! status: ${res.status}`);
          }
          return res.json();
        })
        .then(data => {
          // เก็บข้อมูล imagePath ถ้ามีการระบุว่าเป็น URL ภายนอกให้ใช้โดยไม่ต้องปรับแต่ง path
          if (data.isExternalUrl) {
            setImagePath(data.imagePath); // ใช้ URL เต็มรูปแบบโดยไม่ต้องแก้ไข
          } else {
            setImagePath(data.imagePath); // URL ภายในระบบ จะมีการปรับแต่งในส่วนแสดงผล
          }

          console.log("Slip image data:", data);
          setLoading(false);
        })
        .catch(err => {
          console.error("Error fetching slip image:", err);
          setError("ไม่สามารถโหลดรูปภาพสลิปได้ กรุณาลองใหม่อีกครั้ง");
          setLoading(false);
        });
    }
  }, [open, verification]);

  if (!verification || !open) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md bg-gradient-to-br from-indigo-950 to-purple-950 border-indigo-800/30 text-indigo-100">
        <DialogHeader>
          <DialogTitle className="text-center text-xl bg-clip-text text-transparent bg-gradient-to-r from-amber-300 to-amber-500">
            รูปภาพสลิปธนาคาร
          </DialogTitle>
          <DialogDescription className="text-center text-indigo-300">
            ภาพต้นฉบับที่ใช้ในการตรวจสอบการโอนเงิน
          </DialogDescription>
        </DialogHeader>

        <div className="py-4 px-2 relative rounded-lg overflow-hidden">
          {/* เอฟเฟกต์แสงด้านหลัง */}
          <div className="absolute inset-0 bg-gradient-radial from-indigo-900/80 via-indigo-950 to-purple-950"></div>

          {/* เอฟเฟกต์ประกายดาวเล็กๆ */}
          <div className="absolute inset-0 overflow-hidden">
            {Array.from({ length: 10 }).map((_, i) => (
              <motion.div
                key={i}
                className="absolute h-1 w-1 rounded-full bg-amber-300"
                initial={{ opacity: Math.random() * 0.5 + 0.3 }}
                animate={{
                  opacity: [Math.random() * 0.3 + 0.2, Math.random() * 0.7 + 0.3, Math.random() * 0.3 + 0.2],
                  scale: [1, 1.5, 1]
                }}
                transition={{
                  duration: Math.random() * 3 + 2,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
                style={{
                  top: `${Math.random() * 100}%`,
                  left: `${Math.random() * 100}%`,
                  boxShadow: `0 0 ${Math.random() * 5 + 2}px ${Math.random() * 3 + 1}px rgba(251, 191, 36, 0.3)`
                }}
              />
            ))}
          </div>

          <div className="relative z-10">
            {loading && (
              <div className="flex flex-col items-center justify-center py-12">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                >
                  <Globe className="h-12 w-12 text-amber-400 opacity-60" />
                </motion.div>
                <p className="mt-4 text-indigo-300">กำลังโหลดรูปภาพสลิป...</p>
              </div>
            )}

            {error && (
              <div className="flex flex-col items-center justify-center py-12">
                <XCircle className="h-12 w-12 text-red-400" />
                <p className="mt-4 text-indigo-300">{error}</p>
              </div>
            )}

            {!loading && !error && imagePath && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
              >
                <div className="relative rounded-lg overflow-hidden border-2 border-amber-500/30">
                  {/* กรอบเรืองแสงรอบรูปภาพ */}
                  <div className="absolute -inset-0.5 bg-gradient-to-r from-amber-500/20 to-amber-300/20 animate-pulse blur"></div>

                  <img
                    src={imagePath.startsWith('http') || imagePath.startsWith('https') ?
                          imagePath :
                          (imagePath.startsWith('/') ? imagePath : `/${imagePath}`)}
                    alt="รูปภาพสลิปธนาคาร"
                    className="w-full object-contain relative z-10"
                  />
                </div>
                <p className="text-xs text-center mt-2 text-indigo-400">
                  รูปภาพสลิปที่ใช้ตรวจสอบเมื่อ {formatDate(new Date(verification.createdAt), "PPp")}
                </p>
              </motion.div>
            )}

            {!loading && !error && !imagePath && (
              <div className="flex flex-col items-center justify-center py-12">
                <FileCheck className="h-12 w-12 text-amber-400 opacity-60" />
                <p className="mt-4 text-indigo-300">ไม่พบรูปภาพสลิปสำหรับรายการนี้</p>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

function SlipDetailModal({
  verification,
  open,
  onOpenChange
}: {
  verification: SlipVerification | null,
  open: boolean,
  onOpenChange: (open: boolean) => void
}) {
  const { toast } = useToast();

  // ถ้าไม่มีข้อมูลหรือไม่ได้เปิด dialog ให้ return null
  if (!verification || !open) return null;

  // แปลง responseData จาก JSON string เป็น object
  let slipData: SlipResponseData | null = null;
  try {
    if (verification.responseData) {
      slipData = JSON.parse(verification.responseData) as SlipResponseData;
    }
  } catch (error) {
    console.error("Error parsing slip response data:", error);
  }

  // ฟังก์ชัน copy ข้อความไปยัง clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
      .then(() => {
        toast({
          title: "คัดลอกข้อความสำเร็จ",
          description: "คัดลอกข้อความลงในคลิปบอร์ดเรียบร้อยแล้ว",
        });
      })
      .catch(err => {
        console.error("ไม่สามารถคัดลอกข้อมูลได้:", err);
      });
  };

  // วันที่ทำรายการ
  const transactionDate = verification.transactionDate
    ? new Date(verification.transactionDate)
    : new Date(verification.createdAt);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md bg-gradient-to-br from-indigo-950 to-purple-950 border-indigo-800/30 text-indigo-100">
        <DialogHeader>
          <DialogTitle className="text-center text-xl bg-clip-text text-transparent bg-gradient-to-r from-amber-300 to-amber-500">
            {verification.status === "success" ? "พลังแห่งการยืนยัน" : "บันทึกแห่งพลังทิพย์"}
          </DialogTitle>
          <DialogDescription className="text-center text-indigo-300">
            {verification.status === "success" ? "การโอนเงินได้รับการยืนยันจากเทพเจ้า" : "รายละเอียดการตรวจสอบจากพลังเหนือธรรมชาติ"}
          </DialogDescription>
        </DialogHeader>

        {/* หน้าตาสลิปแบบมีเอฟเฟกต์ศักดิ์สิทธิ์ */}
        <div className="py-4 px-2 relative rounded-lg overflow-hidden">
          {/* เอฟเฟกต์แสงด้านหลัง */}
          <div className="absolute inset-0 bg-gradient-radial from-indigo-900/80 via-indigo-950 to-purple-950"></div>

          {/* เอฟเฟกต์ประกายดาวเล็กๆ */}
          <div className="absolute inset-0 overflow-hidden">
            {Array.from({ length: 20 }).map((_, i) => (
              <motion.div
                key={i}
                className="absolute h-1 w-1 rounded-full bg-amber-300"
                initial={{ opacity: Math.random() * 0.5 + 0.3 }}
                animate={{
                  opacity: [Math.random() * 0.3 + 0.2, Math.random() * 0.7 + 0.3, Math.random() * 0.3 + 0.2],
                  scale: [1, 1.5, 1]
                }}
                transition={{
                  duration: Math.random() * 3 + 2,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
                style={{
                  top: `${Math.random() * 100}%`,
                  left: `${Math.random() * 100}%`,
                  boxShadow: `0 0 ${Math.random() * 5 + 2}px ${Math.random() * 3 + 1}px rgba(251, 191, 36, 0.3)`
                }}
              />
            ))}
          </div>

          <div className="relative z-10">
            {/* โลโก้ */}
            <div className="flex justify-center mb-6">
              <motion.div
                className="relative"
                initial={{ scale: 0.5, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5, type: "spring" }}
              >
                <div className="absolute -inset-1 rounded-full bg-amber-600/30 animate-pulse blur-md"></div>
                <div className="relative bg-gradient-to-tr from-amber-600 to-orange-800 rounded-full p-4">
                  {verification.status === "success" ? (
                    <CheckCircle2 className="h-12 w-12 text-white" />
                  ) : verification.status === "failed" ? (
                    <XCircle className="h-12 w-12 text-white" />
                  ) : (
                    <Clock className="h-12 w-12 text-white" />
                  )}
                  <div className="absolute inset-0 rounded-full bg-gradient-to-tr from-amber-300/20 to-amber-600/10"></div>
                </div>
                <motion.div
                  className="absolute -top-1 -right-1"
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.3, duration: 0.4 }}
                >
                  <Sparkles className="h-5 w-5 text-amber-300" />
                </motion.div>
              </motion.div>
            </div>

            {/* ชื่อธนาคาร */}
            <motion.div
              className="text-center mb-6 relative"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.4 }}
            >
              <h3 className="text-lg font-bold text-transparent bg-clip-text bg-gradient-to-r from-amber-200 to-amber-400">
                {verification.bankName || "ธนาคารไม่ระบุ"}
              </h3>
              <p className="text-sm text-indigo-300">
                {verification.status === "success" ? "ธุรกรรมได้รับการยืนยัน" :
                 verification.status === "failed" ? "ธุรกรรมไม่สำเร็จ" : "กำลังดำเนินการ"}
              </p>
            </motion.div>

            {/* จำนวนเงิน */}
            <motion.div
              className="text-center mb-6 relative"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.4, duration: 0.6 }}
            >
              {verification.amount ? (
                <>
                  <div className="absolute -inset-2 rounded-full bg-amber-600/20 animate-pulse blur-md"></div>
                  <motion.h2
                    className="text-2xl font-bold bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent relative"
                    initial={{ scale: 0.8 }}
                    animate={{ scale: 1 }}
                    transition={{
                      type: "spring",
                      bounce: 0.5,
                      duration: 0.8,
                      delay: 0.6
                    }}
                  >
                    {formatCurrency(verification.amount)}
                    <motion.div
                      className="absolute -right-4 -top-2"
                      initial={{ opacity: 0, rotate: -45 }}
                      animate={{ opacity: 1, rotate: 0 }}
                      transition={{ delay: 1 }}
                    >
                      <Sparkles className="h-5 w-5 text-amber-400" />
                    </motion.div>
                  </motion.h2>
                </>
              ) : (
                <h2 className="text-2xl font-bold text-indigo-400">ไม่ระบุจำนวนเงิน</h2>
              )}
            </motion.div>

            {/* รายละเอียดการโอน */}
            <div className="space-y-4 text-sm bg-indigo-900/30 rounded-lg p-4 border border-indigo-700/30">
              {/* วัน/เวลาที่ทำรายการ */}
              <div className="flex justify-between items-center border-b border-indigo-800/30 pb-2">
                <div className="flex items-center text-indigo-400">
                  <CalendarDays className="h-3.5 w-3.5 mr-2" />
                  <span>วัน/เวลา</span>
                </div>
                <span className="font-medium text-indigo-100">{formatDate(transactionDate, "PPp")}</span>
              </div>

              {/* หมายเลขอ้างอิง */}
              {verification.transactionRef && (
                <div className="flex justify-between items-center border-b border-indigo-800/30 pb-2">
                  <div className="flex items-center text-indigo-400">
                    <ReceiptText className="h-3.5 w-3.5 mr-2" />
                    <span>หมายเลขอ้างอิง</span>
                  </div>
                  <div className="flex items-center">
                    <span className="font-medium text-indigo-100 mr-2">{verification.transactionRef}</span>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 hover:bg-indigo-800/40 text-indigo-300 hover:text-amber-300"
                      onClick={() => copyToClipboard(verification.transactionRef!)}
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              )}

              {/* แหล่งที่มาของการตรวจสอบ */}
              <div className="flex justify-between items-center border-b border-indigo-800/30 pb-2">
                <div className="flex items-center text-indigo-400">
                  {verification.verificationSource === "api" ? (
                    <Terminal className="h-3.5 w-3.5 mr-2" />
                  ) : verification.verificationSource === "Credit+" ? (
                    <Coins className="h-3.5 w-3.5 mr-2" />
                  ) : (
                    <Globe className="h-3.5 w-3.5 mr-2" />
                  )}
                  <span>แหล่งที่มา</span>
                </div>
                <div className="flex items-center">
                  {verification.verificationSource === "api" ? (
                    <>
                      <div className="bg-blue-900/30 px-2 py-0.5 rounded-full text-blue-300 text-xs mr-2 flex items-center">
                        <Terminal className="h-3 w-3 mr-1" />
                        <span>API</span>
                      </div>
                      {verification.apiKey && (
                        <span className="text-indigo-300 text-xs">{verification.apiKey.name}</span>
                      )}
                    </>
                  ) : verification.verificationSource === "Credit+" ? (
                    <div className="bg-amber-900/30 px-2 py-0.5 rounded-full text-amber-300 text-xs flex items-center">
                      <Coins className="h-3 w-3 mr-1" />
                      <span>เติมเงิน</span>
                    </div>
                  ) : (
                    <div className="bg-teal-900/30 px-2 py-0.5 rounded-full text-teal-300 text-xs flex items-center">
                      <MonitorSmartphone className="h-3 w-3 mr-1" />
                      <span>หน้าเว็บ</span>
                    </div>
                  )}
                </div>
              </div>

              {/* จากบัญชี */}
              <div className="flex justify-between items-start border-b border-indigo-800/30 pb-2">
                <div className="flex items-center text-indigo-400">
                  <ArrowRight className="h-3.5 w-3.5 mr-2" />
                  <span>จาก</span>
                </div>
                <div className="text-right">
                  <div className="font-medium text-indigo-100">{verification.sender || "ไม่ระบุชื่อผู้ส่ง"}</div>
                  {slipData?.data?.sender?.bank?.name && (
                    <div className="text-xs text-indigo-400">
                      {slipData.data.sender.bank.name}
                    </div>
                  )}
                  {slipData?.data?.sender?.account?.bank?.account && (
                    <div className="text-xs text-indigo-400">
                      {slipData.data.sender.account.bank.account}
                    </div>
                  )}
                </div>
              </div>

              {/* ไปยังบัญชี */}
              <div className="flex justify-between items-start border-b border-indigo-800/30 pb-2">
                <div className="flex items-center text-indigo-400">
                  <ArrowRight className="h-3.5 w-3.5 mr-2" />
                  <span>ไปยัง</span>
                </div>
                <div className="text-right">
                  <div className="font-medium text-indigo-100">{verification.receiver || "ไม่ระบุชื่อผู้รับ"}</div>
                  {slipData?.data?.receiver?.bank?.name && (
                    <div className="text-xs text-indigo-400">
                      {slipData.data.receiver.bank.name}
                    </div>
                  )}
                  {slipData?.data?.receiver?.account?.bank?.account && (
                    <div className="text-xs text-indigo-400">
                      {slipData.data.receiver.account.bank.account}
                    </div>
                  )}
                </div>
              </div>

              {/* ค่าธรรมเนียม */}
              {slipData?.data?.fee !== undefined && (
                <div className="flex justify-between items-center border-b border-indigo-800/30 pb-2">
                  <div className="flex items-center text-indigo-400">
                    <Coins className="h-3.5 w-3.5 mr-2" />
                    <span>ค่าธรรมเนียม</span>
                  </div>
                  <span className="font-medium text-indigo-100">{formatCurrency(slipData.data.fee)}</span>
                </div>
              )}

              {/* รายละเอียดเพิ่มเติม */}
              {(slipData?.data?.ref1 || slipData?.data?.ref2 || slipData?.data?.ref3) && (
                <div className="pt-2">
                  <h4 className="font-medium mb-2 text-indigo-200 flex items-center">
                    <Info className="h-3.5 w-3.5 mr-2" />
                    รายละเอียดเพิ่มเติม
                  </h4>
                  {slipData?.data?.ref1 && (
                    <div className="flex justify-between items-center text-xs text-indigo-300">
                      <span>Ref 1:</span>
                      <span>{slipData.data.ref1}</span>
                    </div>
                  )}
                  {slipData?.data?.ref2 && (
                    <div className="flex justify-between items-center text-xs text-indigo-300">
                      <span>Ref 2:</span>
                      <span>{slipData.data.ref2}</span>
                    </div>
                  )}
                  {slipData?.data?.ref3 && (
                    <div className="flex justify-between items-center text-xs text-indigo-300">
                      <span>Ref 3:</span>
                      <span>{slipData.data.ref3}</span>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* ส่วนท้าย */}
            <div className="mt-6 text-center">
              <div className="flex items-center justify-center text-xs text-indigo-400 mb-2">
                <div className="relative mr-1">
                  <div className="absolute -inset-1 rounded-full bg-amber-600/20 animate-pulse blur-sm"></div>
                  <Gem className="h-4 w-4 text-amber-400 relative" />
                </div>
                ตรวจสอบโดย SLIPKUY
              </div>
              <div className="text-xs text-indigo-500">{formatDate(new Date(), "Pp")}</div>
            </div>
          </div>
        </div>

        <motion.div
          className="flex justify-center mt-4"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
        >
          <Button
            onClick={() => onOpenChange(false)}
            className="px-8 bg-gradient-to-r from-amber-600 to-amber-800 hover:from-amber-700 hover:to-amber-900"
          >
            <Sparkles className="h-4 w-4 mr-2" />
            ปิด
          </Button>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}

export default function HistoryPage() {
  const [search, setSearch] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedVerification, setSelectedVerification] = useState<SlipVerification | null>(null);
  const [slipModalOpen, setSlipModalOpen] = useState(false);
  const [slipImageModalOpen, setSlipImageModalOpen] = useState(false);
  const { toast } = useToast();
  const itemsPerPage = 10;

  // ดึงข้อมูลประวัติการตรวจสอบ
  const { data: verifications, isLoading } = useQuery<SlipVerification[]>({
    queryKey: ['/api/verifications'],
  });

  // กรองข้อมูลตามเงื่อนไขค้นหาและสถานะ
  const filteredVerifications = verifications?.filter((verification) => {
    // กรองตามข้อความค้นหา
    const searchMatch =
      search === "" ||
      (verification.transactionRef?.toLowerCase().includes(search.toLowerCase()) ||
       verification.bankName?.toLowerCase().includes(search.toLowerCase()) ||
       verification.sender?.toLowerCase().includes(search.toLowerCase()) ||
       verification.receiver?.toLowerCase().includes(search.toLowerCase()));

    // กรองตามสถานะ
    const statusMatch = statusFilter === "all" || verification.status === statusFilter;

    return searchMatch && statusMatch;
  });

  // คำนวณจำนวนหน้าทั้งหมด
  const totalPages = filteredVerifications
    ? Math.ceil(filteredVerifications.length / itemsPerPage)
    : 0;

  // คำนวณดัชนีเริ่มต้นและสิ้นสุดของรายการที่จะแสดงในหน้าปัจจุบัน
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;

  // รายการที่จะแสดงในหน้าปัจจุบัน
  const currentVerifications = filteredVerifications?.slice(startIndex, endIndex);

  // ฟังก์ชันแสดงสถานะด้วยสีและไอคอน
  const renderStatus = (status: string) => {
    switch (status) {
      case "success":
        return (
          <Badge className="bg-green-800/70 text-green-100 hover:bg-green-800/80 flex items-center gap-1 border-none">
            <CheckCircle2 className="h-3 w-3" />
            สำเร็จ
          </Badge>
        );
      case "failed":
        return (
          <Badge className="bg-red-800/70 text-red-100 hover:bg-red-800/80 flex items-center gap-1 border-none">
            <XCircle className="h-3 w-3" />
            ล้มเหลว
          </Badge>
        );
      case "pending":
        return (
          <Badge className="bg-amber-800/70 text-amber-100 hover:bg-amber-800/80 flex items-center gap-1 border-none">
            <Clock className="h-3 w-3" />
            กำลังดำเนินการ
          </Badge>
        );
      default:
        return (
          <Badge className="bg-neutral-800/70 text-neutral-100 hover:bg-neutral-800/80 flex items-center gap-1 border-none">
            {status}
          </Badge>
        );
    }
  };

  // แสดงข้อมูลทั้งหมดในรูปแบบตาราง
  return (
    <DashboardLayout>
      <div className="relative z-10">
        {/* Decorative Background Elements */}
        <div className="absolute -top-12 left-1/4 w-20 h-20 rounded-full bg-indigo-600/10 filter blur-2xl"></div>
        <div className="absolute top-40 right-20 w-32 h-32 rounded-full bg-purple-600/20 filter blur-3xl"></div>
        <div className="absolute bottom-40 left-10 w-24 h-24 rounded-full bg-amber-600/10 filter blur-2xl"></div>

        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="relative z-10 container mx-auto py-6 max-w-6xl"
        >
          <div className="flex items-center mb-6">
            <div className="mr-3 relative">
              <div className="absolute -inset-1 rounded-full bg-indigo-600/30 animate-pulse blur-md"></div>
              <div className="relative bg-gradient-to-br from-indigo-700 to-indigo-900 h-10 w-10 rounded-full flex items-center justify-center">
                <FileCheck className="h-5 w-5 text-indigo-300" />
              </div>
            </div>
            <div>
              <h1 className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-100 to-white">บันทึกการตรวจสอบ</h1>
              <p className="text-indigo-300 text-sm">ประวัติการตรวจสอบสลิปจากพลังแห่งเทพเจ้า</p>
            </div>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mb-6"
          >
            <Card className="overflow-hidden backdrop-blur-md bg-gradient-to-br from-indigo-950/80 to-purple-900/80 border-indigo-800/30 shadow-lg">
              <CardHeader className="relative">
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-800/30 to-indigo-900/10"></div>
                <div className="relative z-10 flex items-center">
                  <div className="mr-3 h-8 w-8 rounded-full bg-gradient-to-br from-indigo-600 to-indigo-800 flex items-center justify-center">
                    <History className="h-4 w-4 text-indigo-100" />
                  </div>
                  <div>
                    <CardTitle className="text-indigo-100">ประวัติการตรวจสอบสลิป</CardTitle>
                    <CardDescription className="text-indigo-300">
                      บันทึกการใช้พลังเทพเจ้าในการตรวจสอบสลิป
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* ส่วนกรองและค้นหา */}
                  <div className="flex flex-col gap-4 md:flex-row">
                    <div className="relative flex-1">
                      <Search className="absolute left-3 top-3 h-4 w-4 text-indigo-400" />
                      <Input
                        placeholder="ค้นหาจากรายละเอียดสลิป..."
                        className="pl-10 bg-indigo-950/50 border-indigo-700/30 text-indigo-100 placeholder:text-indigo-400/70 focus:border-amber-500/50 focus:ring-amber-500/30"
                        value={search}
                        onChange={(e) => setSearch(e.target.value)}
                      />
                    </div>
                    <Select
                      value={statusFilter}
                      onValueChange={setStatusFilter}
                    >
                      <SelectTrigger className="w-[180px] bg-indigo-950/50 border-indigo-700/30 text-indigo-100 focus:border-amber-500/50 focus:ring-amber-500/30">
                        <SelectValue placeholder="สถานะ" />
                      </SelectTrigger>
                      <SelectContent className="bg-indigo-950 border-indigo-800/50 text-indigo-100">
                        <SelectItem value="all" className="focus:bg-indigo-900/80 focus:text-indigo-100">ทั้งหมด</SelectItem>
                        <SelectItem value="success" className="focus:bg-indigo-900/80 focus:text-indigo-100">สำเร็จ</SelectItem>
                        <SelectItem value="failed" className="focus:bg-indigo-900/80 focus:text-indigo-100">ล้มเหลว</SelectItem>
                        <SelectItem value="pending" className="focus:bg-indigo-900/80 focus:text-indigo-100">กำลังดำเนินการ</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* ตารางแสดงข้อมูล */}
                  {isLoading ? (
                    <div className="space-y-4">
                      {[...Array(5)].map((_, index) => (
                        <div key={index} className="flex justify-between items-center">
                          <Skeleton className="h-6 w-1/4 bg-indigo-800/30" />
                          <Skeleton className="h-6 w-1/5 bg-indigo-800/30" />
                          <Skeleton className="h-6 w-1/6 bg-indigo-800/30" />
                          <Skeleton className="h-6 w-1/6 bg-indigo-800/30" />
                        </div>
                      ))}
                    </div>
                  ) : currentVerifications && currentVerifications.length > 0 ? (
                    <>
                      <div className="rounded-md border border-indigo-800/30 overflow-hidden">
                        <Table>
                          <TableHeader className="bg-indigo-900/40">
                            <TableRow className="hover:bg-indigo-900/40 border-indigo-800/30">
                              <TableHead className="text-indigo-300">วันที่</TableHead>
                              <TableHead className="text-indigo-300">ธนาคาร / รายละเอียด</TableHead>
                              <TableHead className="text-indigo-300">จำนวนเงิน</TableHead>
                              <TableHead className="text-indigo-300">ผู้ส่ง / ผู้รับ</TableHead>
                              <TableHead className="text-indigo-300">แหล่งที่มา</TableHead>
                              <TableHead className="text-indigo-300">สถานะ</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            <AnimatePresence>
                              {currentVerifications.map((verification) => (
                                <motion.tr
                                  key={verification.id}
                                  className="hover:bg-indigo-900/40 border-indigo-800/30"
                                  initial={{ opacity: 0, y: 10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  exit={{ opacity: 0 }}
                                  transition={{ duration: 0.3 }}
                                  whileHover={{
                                    backgroundColor: "rgba(79, 70, 229, 0.2)",
                                    transition: { duration: 0.1 }
                                  }}
                                >
                                  <TableCell className="font-medium text-indigo-200">
                                    {formatDate(new Date(verification.createdAt), "dd MMM yy")}
                                    <div className="text-xs text-indigo-400">
                                      {formatDate(new Date(verification.createdAt), "HH:mm:ss")}
                                    </div>
                                  </TableCell>
                                  <TableCell className="text-indigo-200">
                                    <div className="flex items-center">
                                      <div className="h-8 w-8 rounded-full bg-indigo-900/70 flex items-center justify-center mr-2">
                                        <CloudLightning className="h-4 w-4 text-amber-400" />
                                      </div>
                                      <div>
                                        <div className="text-indigo-100">{verification.bankName || "-"}</div>
                                        <div className="text-xs text-indigo-400">
                                          Ref: {verification.transactionRef || "-"}
                                        </div>
                                      </div>
                                    </div>
                                  </TableCell>
                                  <TableCell className="text-amber-400 font-semibold">
                                    {verification.amount ? formatCurrency(verification.amount) : "-"}
                                  </TableCell>
                                  <TableCell className="text-indigo-200">
                                    <div className="truncate max-w-[180px]">
                                      จาก: <span className="text-indigo-100">{verification.sender || "-"}</span>
                                    </div>
                                    <div className="truncate max-w-[180px] text-sm text-indigo-400">
                                      ถึง: <span className="text-indigo-300">{verification.receiver || "-"}</span>
                                    </div>
                                  </TableCell>
                                  <TableCell>
                                    {verification.verificationSource === "Credit+" ? (
                                      <div className="bg-amber-900/30 px-2 py-0.5 rounded-full text-amber-300 text-xs flex items-center w-fit">
                                        <Coins className="h-3 w-3 mr-1" />
                                        <span>เติมเงิน</span>
                                      </div>
                                    ) : verification.verificationSource === "api" ? (
                                      <div className="flex items-center">
                                        <div className="bg-blue-900/30 px-2 py-0.5 rounded-full text-blue-300 text-xs flex items-center">
                                          <Terminal className="h-3 w-3 mr-1" />
                                          <span>API</span>
                                        </div>
                                        {verification.apiKey && (
                                          <div className="ml-1 text-xs text-indigo-400 truncate max-w-[80px]">
                                            {verification.apiKey.name}
                                          </div>
                                        )}
                                      </div>
                                    ) : (
                                      <div className="bg-teal-900/30 px-2 py-0.5 rounded-full text-teal-300 text-xs flex items-center w-fit">
                                        <MonitorSmartphone className="h-3 w-3 mr-1" />
                                        <span>เว็บ</span>
                                      </div>
                                    )}
                                  </TableCell>
                                  <TableCell>
                                    {renderStatus(verification.status)}
                                  </TableCell>
                                  <TableCell>
                                    <div className="flex space-x-2">
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="p-0 h-8 w-8 bg-indigo-900/40 border-indigo-700/30 hover:bg-indigo-800/50 hover:text-amber-400"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          setSelectedVerification(verification);
                                          setSlipModalOpen(true);
                                        }}
                                      >
                                        <Info className="h-4 w-4" />
                                      </Button>

                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="p-0 h-8 w-8 bg-indigo-900/40 border-indigo-700/30 hover:bg-indigo-800/50 hover:text-amber-400"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          setSelectedVerification(verification);
                                          setSlipImageModalOpen(true);
                                        }}
                                      >
                                        <FileCheck className="h-4 w-4" />
                                      </Button>
                                    </div>
                                  </TableCell>
                                </motion.tr>
                              ))}
                            </AnimatePresence>
                          </TableBody>
                        </Table>
                      </div>

                      {/* Pagination */}
                      {totalPages > 1 && (
                        <Pagination className="mt-4">
                          <PaginationContent>
                            <PaginationItem>
                              <PaginationPrevious
                                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                                className={`${currentPage === 1 ? 'pointer-events-none opacity-50' : ''}
                                  bg-indigo-900/30 border-indigo-700/30 text-indigo-300 hover:bg-indigo-800/40 hover:text-indigo-100`}
                              />
                            </PaginationItem>
                            {[...Array(totalPages)].map((_, i) => (
                              <PaginationItem key={i}>
                                <PaginationLink
                                  onClick={() => setCurrentPage(i + 1)}
                                  isActive={currentPage === i + 1}
                                  className={`${currentPage === i + 1
                                    ? 'bg-amber-600 text-white hover:bg-amber-700 border-amber-700/50'
                                    : 'bg-indigo-900/30 border-indigo-700/30 text-indigo-300 hover:bg-indigo-800/40 hover:text-indigo-100'}`}
                                >
                                  {i + 1}
                                </PaginationLink>
                              </PaginationItem>
                            ))}
                            <PaginationItem>
                              <PaginationNext
                                onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                                className={`${currentPage === totalPages ? 'pointer-events-none opacity-50' : ''}
                                  bg-indigo-900/30 border-indigo-700/30 text-indigo-300 hover:bg-indigo-800/40 hover:text-indigo-100`}
                              />
                            </PaginationItem>
                          </PaginationContent>
                        </Pagination>
                      )}
                    </>
                  ) : (
                    <div className="p-8 text-center">
                      <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-indigo-900/50 mb-4">
                        <FileCheck className="h-8 w-8 text-indigo-400/70" />
                      </div>
                      <p className="text-indigo-300">ยังไม่มีประวัติการตรวจสอบสลิป</p>
                      {(search || statusFilter !== "all") && (
                        <Button
                          variant="link"
                          className="mt-2 text-amber-400 hover:text-amber-300"
                          onClick={() => {
                            setSearch("");
                            setStatusFilter("all");
                          }}
                        >
                          ล้างตัวกรอง
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* ส่วนแสดงรายละเอียดสลิป */}
          <SlipDetailModal
            verification={selectedVerification}
            open={slipModalOpen}
            onOpenChange={setSlipModalOpen}
          />

          {/* ส่วนแสดงรูปภาพสลิป */}
          <SlipImageModal
            verification={selectedVerification}
            open={slipImageModalOpen}
            onOpenChange={setSlipImageModalOpen}
          />
        </motion.div>
      </div>

    </DashboardLayout>
  );
}