import { Express, Request, Response } from "express";
import { storage } from "../storage";
import multer from "multer";
import { slipService } from "../slip-service";
import { randomBytes } from "crypto";
import fs from "fs";
import path from "path";

// ฟังก์ชันคำนวณความเหมือนของข้อความ (Levenshtein Distance)
function levenshteinDistance(a: string, b: string): number {
  const matrix: number[][] = [];

  // เริ่มต้นสร้าง matrix
  for (let i = 0; i <= b.length; i++) {
    matrix[i] = [i];
  }
  for (let j = 0; j <= a.length; j++) {
    matrix[0][j] = j;
  }

  // คำนวณระยะห่างแบบ Levenshtein
  for (let i = 1; i <= b.length; i++) {
    for (let j = 1; j <= a.length; j++) {
      const cost = a[j - 1] === b[i - 1] ? 0 : 1;
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,        // ลบ
        matrix[i][j - 1] + 1,        // เพิ่ม
        matrix[i - 1][j - 1] + cost  // แทนที่
      );
    }
  }

  return matrix[b.length][a.length];
}

// สร้างโฟลเดอร์เก็บไฟล์สลิป
const uploadsDir = path.join(process.cwd(), "tmp", "uploads");
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// กำหนด multer storage settings
const storage_engine = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    const ext = path.extname(file.originalname);
    cb(null, uniqueSuffix + ext);
  },
});

const upload = multer({
  storage: storage_engine,
  limits: {
    fileSize: 5 * 1024 * 1024, // ขนาดไฟล์สูงสุด 5MB
  },
  fileFilter: function (req, file, cb) {
    // รับเฉพาะไฟล์รูปภาพ
    if (file.mimetype.startsWith("image/")) {
      cb(null, true);
    } else {
      cb(new Error("ไฟล์ที่อัปโหลดต้องเป็นรูปภาพเท่านั้น") as any);
    }
  },
});

function isAuthenticated(req: Request, res: Response, next: any) {
  if (req.isAuthenticated()) {
    return next();
  }
  return res.status(401).json({ message: "กรุณาเข้าสู่ระบบก่อนใช้งาน" });
}

export function setupTopUpRoutes(app: Express) {
  // ดึงข้อมูลเครดิตของผู้ใช้
  app.get("/api/user/credit", isAuthenticated, async (req, res) => {
    try {
      const credit = await storage.getUserCredit(req.user!.id);
      res.status(200).json({ credit });
    } catch (error) {
      console.error("Error fetching user credit:", error);
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการดึงข้อมูลเครดิต" });
    }
  });

  // สร้างรายการเติมเงิน
  app.post("/api/topup/create", isAuthenticated, async (req, res) => {
    try {
      const { amount } = req.body;

      if (!amount || amount < 100) {
        return res.status(400).json({ message: "จำนวนเงินต้องมากกว่าหรือเท่ากับ 100 บาท" });
      }

      // สร้างรหัสอ้างอิง
      const referenceCode = generateReferenceCode();

      // สร้างรายการเติมเงิน
      const topUpTransaction = await storage.createTopUpTransaction({
        userId: req.user!.id,
        amount,
        status: "pending",
        referenceCode
      });

      res.status(201).json(topUpTransaction);
    } catch (error) {
      console.error("Error creating top-up transaction:", error);
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการสร้างรายการเติมเงิน" });
    }
  });

  // ดึงรายการเติมเงินทั้งหมดของผู้ใช้
  app.get("/api/topup", isAuthenticated, async (req, res) => {
    try {
      const transactions = await storage.listUserTopUpTransactions(req.user!.id);
      res.status(200).json(transactions);
    } catch (error) {
      console.error("Error fetching top-up transactions:", error);
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการดึงข้อมูลรายการเติมเงิน" });
    }
  });

  // ดึงรายการเติมเงินเฉพาะรายการ
  app.get("/api/topup/:id", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const transaction = await storage.getTopUpTransaction(id);

      if (!transaction) {
        return res.status(404).json({ message: "ไม่พบรายการเติมเงิน" });
      }

      // ตรวจสอบว่าเป็นรายการของผู้ใช้หรือไม่
      if (transaction.userId !== req.user!.id) {
        return res.status(403).json({ message: "คุณไม่มีสิทธิ์เข้าถึงรายการนี้" });
      }

      res.status(200).json(transaction);
    } catch (error) {
      console.error("Error fetching top-up transaction:", error);
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการดึงข้อมูลรายการเติมเงิน" });
    }
  });

  // อัปโหลดสลิปและยืนยันการชำระเงิน
  app.post("/api/topup/verify/:id", isAuthenticated, upload.single("slip"), async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      
      // ตรวจสอบว่ามีการอัปโหลดไฟล์หรือไม่
      if (!req.file) {
        return res.status(400).json({ error: "กรุณาอัปโหลดไฟล์สลิป" });
      }

      // ดึงข้อมูลรายการเติมเงิน
      const transaction = await storage.getTopUpTransaction(id);

      if (!transaction) {
        // ลบไฟล์ที่อัปโหลดเมื่อไม่พบรายการ
        fs.unlinkSync(req.file.path);
        return res.status(404).json({ error: "ไม่พบรายการเติมเงิน" });
      }

      // ตรวจสอบว่าเป็นรายการของผู้ใช้หรือไม่
      if (transaction.userId !== req.user!.id) {
        // ลบไฟล์ที่อัปโหลดเมื่อไม่มีสิทธิ์
        fs.unlinkSync(req.file.path);
        return res.status(403).json({ error: "คุณไม่มีสิทธิ์เข้าถึงรายการนี้" });
      }

      // ตรวจสอบว่ารายการอยู่ในสถานะ pending หรือไม่
      if (transaction.status !== "pending") {
        // ลบไฟล์ที่อัปโหลดเมื่อรายการไม่ได้อยู่ในสถานะ pending
        fs.unlinkSync(req.file.path);
        return res.status(400).json({ error: "รายการนี้ไม่สามารถยืนยันได้" });
      }

      // อ่านไฟล์ที่อัปโหลด
      const fileBuffer = fs.readFileSync(req.file.path);

      try {
        // ตรวจสอบสลิปด้วย API เพื่อดึงข้อมูลสลิป
        const verifyResult = await slipService.verifySlip(req.user!.id, fileBuffer, req.file.originalname);
        
        // ดึงข้อมูลวันที่จากสลิป
        const slipDate = verifyResult.data?.date ? new Date(verifyResult.data.date) : null;
        if (!slipDate) {
          throw new Error("ไม่พบข้อมูลวันที่ในสลิป กรุณาตรวจสอบความถูกต้องของสลิป");
        }
        
        console.log("วันที่ในสลิป:", slipDate.toISOString());
        console.log("วันที่สร้างรายการ:", transaction.createdAt.toISOString());
        
        // สร้าง Date object สำหรับวันที่สร้างรายการ ลบไป 1 วัน
        const oneDayBefore = new Date(transaction.createdAt);
        oneDayBefore.setDate(oneDayBefore.getDate() - 1);
        console.log("วันที่สร้างรายการ - 1 วัน:", oneDayBefore.toISOString());
        
        // ตรวจสอบวันที่ในสลิป ต้องไม่เก่ากว่าวันที่สร้างรายการเกิน 1 วัน
        if (slipDate < oneDayBefore) {
          const diffInDays = Math.ceil((oneDayBefore.getTime() - slipDate.getTime()) / (1000 * 60 * 60 * 24));
          throw new Error(`วันที่ในสลิปเก่ากว่าวันที่สร้างรายการ ${diffInDays} วัน กรุณาใช้สลิปที่มีวันที่ไม่เก่ากว่า 1 วันก่อนสร้างรายการ`);
        }
        
        // คำนวณระยะเวลาที่ผ่านไป (เป็นวัน)
        const daysDifference = Math.floor((slipDate.getTime() - transaction.createdAt.getTime()) / (1000 * 60 * 60 * 24));
        console.log("จำนวนวันที่ต่างกัน:", daysDifference);
        
        // ถ้าวันที่ในสลิปมากกว่าวันที่สร้างรายการเกิน 7 วัน
        if (daysDifference > 7) {
          throw new Error(`วันที่ในสลิปห่างจากวันที่สร้างรายการเกินไป (${daysDifference} วัน) กรุณาใช้สลิปที่มีวันที่ไม่เกิน 7 วันหลังจากวันที่สร้างรายการ`);
        }
        
        // ถ้าได้รับ transaction reference จาก API ให้ตรวจสอบว่าเคยมีการใช้สลิปนี้ไปแล้วหรือไม่
        const transRef = verifyResult.data?.transRef;
        if (transRef) {
          // ค้นหารายการตรวจสอบสลิปทั้งหมดในระบบเพื่อตรวจสอบการใช้สลิปซ้ำ
          const allVerifications = await storage.listUserSlipVerifications(0); // 0 = ดึงทั้งหมด
          
          const existingVerification = allVerifications.find(v => 
            v.transactionRef === transRef && v.status === "success"
          );
          
          if (existingVerification) {
            throw new Error(`สลิปนี้ถูกใช้ไปแล้วในระบบ กรุณาอย่าส่งสลิปซ้ำ`);
          }
        }
        
        // ตรวจสอบจำนวนเงินในสลิปว่าตรงกับที่ลูกค้าต้องการเติมหรือไม่
        const slipAmount = Number(verifyResult.data?.amount?.amount) || 0;
        console.log(`ตรวจสอบจำนวนเงิน: จำนวนเงินในสลิป = ${slipAmount} บาท, จำนวนเงินที่ต้องการเติม = ${transaction.amount} บาท`);
        
        // ตรวจสอบว่าเป็นจำนวนเงินที่ถูกต้อง
        if (isNaN(slipAmount)) {
          throw new Error(`ไม่สามารถอ่านจำนวนเงินในสลิปได้`);
        }
        
        // ต้องมีจำนวนเงินในสลิปที่ไม่น้อยกว่าจำนวนขั้นต่ำที่ยอมรับได้
        if (slipAmount < 1) {
          throw new Error(`จำนวนเงินในสลิป (${slipAmount.toFixed(2)} บาท) น้อยเกินไป กรุณาตรวจสอบสลิปอีกครั้ง`);
        }
        
        // บันทึกจำนวนเงินต้นทาง (ที่ผู้ใช้กรอก) เพื่อใช้ในการอ้างอิง
        const requestedAmount = transaction.amount;
        
        // ถ้าจำนวนเงินในสลิปต่างจากที่ผู้ใช้กรอก ให้อัพเดตจำนวนเงินเป็นตามสลิป
        if (slipAmount !== transaction.amount) {
          console.log(`จำนวนเงินในสลิป (${slipAmount} บาท) ${slipAmount > transaction.amount ? 'มากกว่า' : 'น้อยกว่า'}ที่ระบุไว้ (${transaction.amount} บาท) จะใช้จำนวนเงินในสลิปแทน`);
          
          // อัพเดตจำนวนเงินในรายการเติมเงิน
          try {
            await storage.updateTopUpTransaction(id, {
              amount: slipAmount,
              notes: `ปรับจำนวนเงินจาก ${transaction.amount} บาท เป็น ${slipAmount} บาท ตามที่พบในสลิป`
            });
            console.log(`อัพเดตจำนวนเงินในรายการเติมเงิน ID ${id} สำเร็จ: ${transaction.amount} -> ${slipAmount} บาท`);
            
            // อัพเดตข้อมูลรายการปัจจุบัน
            transaction.amount = slipAmount;
          } catch (error) {
            console.error('เกิดข้อผิดพลาดในการอัพเดตจำนวนเงิน:', error);
            // ถ้าไม่สามารถอัพเดตได้ ให้ใช้จำนวนเงินในสลิปแทน แต่ไม่อัพเดตฐานข้อมูล
            console.log('จะใช้จำนวนเงินในสลิป', slipAmount, 'บาท แทนจำนวนเงินที่ผู้ใช้กรอก', transaction.amount, 'บาท');
            transaction.amount = slipAmount;
          }
        }
        
        // ตรวจสอบบัญชีผู้รับเงินว่าตรงกับบัญชีในระบบหรือไม่
        // ดึงการตั้งค่าบัญชีจากฐานข้อมูล (ถ้ามี) หรือใช้ค่าเริ่มต้น
        const systemSettings = await storage.getSetting('payment_accounts');
        let accountSettings;
        try {
          accountSettings = systemSettings ? JSON.parse(systemSettings.value as string) : null;
        } catch (e) {
          accountSettings = null;
        }
        
        // ข้อมูลบัญชีของระบบ (ตัวอย่าง หรือจากการตั้งค่า)
        const validBankAccounts = accountSettings?.bankAccounts || [
          { 
            bankCode: "004", 
            accountNumber: "057-8-74800-8",     // ตัวอย่างบัญชีกสิกรไทย
            accountName: "นาย จิรายุ สัตย์ซื่อ",
            accountNameEn: "Mr. Jirayu Satsue"
          }, 
          { bankCode: "002", accountNumber: "111-2-33333-4" }, // กรุงเทพ
          { bankCode: "006", accountNumber: "444-5-66666-7" }  // กรุงไทย
        ];
        
        const validPromptpays = accountSettings?.promptpays || [
          "**********",
          "*************",
          "**********" // เพิ่มเลขพร้อมเพย์ของแอดมิน
        ];
        
        // ตรวจสอบบัญชีผู้รับจากสลิป
        const receiverBankId = verifyResult.data?.receiver?.bank?.id || "";
        const receiverAccount = verifyResult.data?.receiver?.account?.bank?.account || 
                              verifyResult.data?.receiver?.account?.proxy?.account || "";
        const receiverName = verifyResult.data?.receiver?.account?.name?.th || 
                           verifyResult.data?.receiver?.account?.name?.en || "";
        
        // พิมพ์ข้อมูลจาก API เพื่อการแก้ไขปัญหา
        console.log('ข้อมูลจาก API:');
        console.log('- ธนาคารผู้รับ:', receiverBankId, verifyResult.data?.receiver?.bank?.name);
        console.log('- เลขบัญชีผู้รับ:', receiverAccount);
        console.log('- ชื่อบัญชีผู้รับ:', receiverName);
        console.log('- ข้อมูลเต็ม receiver:', JSON.stringify(verifyResult.data?.receiver));
        
        let isValidReceiver = false;
        
        // ตรวจสอบบัญชีธนาคาร - ใช้การตรวจสอบแบบยืดหยุ่นมากขึ้น
        if (receiverBankId) {
          // ลบเครื่องหมาย - และขีด
          const sanitizedReceiverAccount = receiverAccount ? receiverAccount.replace(/[-\s]/g, "") : "";
          
          isValidReceiver = validBankAccounts.some(acc => {
            // ตรวจสอบรหัสธนาคารว่าตรงกันหรือไม่ - ใช้การตรวจสอบแบบเข้มงวดมากขึ้น
            if (acc.bankCode !== receiverBankId) return false;
            
            // ลบเครื่องหมาย - และขีดจากเลขบัญชีที่ตั้งค่าไว้
            const sanitizedAccountNumber = acc.accountNumber.replace(/[-\s]/g, "");
            
            // ตรวจสอบชื่อบัญชี (ถ้ามี) - ใช้การตรวจสอบแบบยืดหยุ่น
            // ลบช่องว่างและแปลงเป็นตัวพิมพ์เล็กเพื่อเปรียบเทียบ
            const validNameTH = acc.accountName ? acc.accountName.replace(/\s+/g, "").toLowerCase() : "";
            const validNameEN = acc.accountNameEn ? acc.accountNameEn.replace(/\s+/g, "").toLowerCase() : "";
            const sanitizedReceiverName = receiverName ? receiverName.replace(/\s+/g, "").toLowerCase() : "";
            
            // ตรวจสอบเลขบัญชีแบบยืดหยุ่นมากขึ้น - รองรับการปิดบังบางส่วนเป็น x
            // แยกส่วนที่เป็นตัวเลขจากเลขบัญชี (ไม่รวม x)
            const validDigits = sanitizedAccountNumber.replace(/[^0-9]/g, "");
            const receiverDigits = sanitizedReceiverAccount.replace(/[^0-9]/g, "");
            
            console.log('เลขบัญชีที่ถูกต้อง (ตัวเลขอย่างเดียว):', validDigits);
            console.log('เลขบัญชีจากสลิป (ตัวเลขอย่างเดียว):', receiverDigits);
            
            // ตรวจสอบเลขบัญชีแบบยืดหยุ่น - ต้องมีเลขหลักสำคัญตรงกัน
            let accountNumberMatches = false;
            
            // 1. ถ้ามีเลขท้าย 4 ตัวตรงกัน ถือว่าผ่าน
            const lastFourValid = validDigits.slice(-4);
            const lastFourReceiver = receiverDigits.slice(-4);
            
            if (lastFourValid.length >= 2 && lastFourReceiver.length >= 2 && 
                lastFourValid === lastFourReceiver) {
              accountNumberMatches = true;
              console.log('เลขท้ายตรงกัน:', lastFourValid, '=', lastFourReceiver);
            }
            
            // 2. ถ้ามีเลขบัญชีบางส่วนตรงกัน
            if (!accountNumberMatches) {
              // ตรวจสอบว่ามีตัวเลขที่ตรงกันหรือไม่ (อย่างน้อย 2 ตัว)
              if (validDigits.length >= 2 && receiverDigits.length >= 2) {
                // ตรวจสอบว่ามีตัวเลขชุดเดียวกันอยู่ใน receiver หรือไม่
                accountNumberMatches = 
                  validDigits.includes(receiverDigits) || 
                  receiverDigits.includes(validDigits);
                
                if (accountNumberMatches) {
                  console.log('เลขบัญชีมีส่วนที่ตรงกัน:', 
                              validDigits.includes(receiverDigits) ? `${validDigits} มี ${receiverDigits}` : 
                                                                    `${receiverDigits} มี ${validDigits}`);
                }
              }
            }
            
            // 3. เปรียบเทียบเฉพาะส่วนที่ไม่ได้ถูกปิดบัง (ไม่ใช่ x)
            if (!accountNumberMatches) {
              const nonMaskedValid = [];
              const nonMaskedReceiver = [];
              
              // แยกตำแหน่งของตัวเลขที่ไม่ได้ถูกปิดบัง
              for (let i = 0; i < sanitizedAccountNumber.length; i++) {
                if (/\d/.test(sanitizedAccountNumber[i])) {
                  nonMaskedValid.push({pos: i, digit: sanitizedAccountNumber[i]});
                }
              }
              
              for (let i = 0; i < sanitizedReceiverAccount.length; i++) {
                if (/\d/.test(sanitizedReceiverAccount[i])) {
                  nonMaskedReceiver.push({pos: i, digit: sanitizedReceiverAccount[i]});
                }
              }
              
              // ตรวจสอบว่ามีตัวเลขในตำแหน่งเดียวกันที่ตรงกันหรือไม่
              let matchCount = 0;
              const totalDigits = Math.min(nonMaskedValid.length, nonMaskedReceiver.length);
              
              for (const validDigit of nonMaskedValid) {
                for (const recDigit of nonMaskedReceiver) {
                  // ต้องมีตำแหน่งใกล้เคียงกัน (±1) และตัวเลขตรงกัน
                  if (Math.abs(validDigit.pos - recDigit.pos) <= 1 && validDigit.digit === recDigit.digit) {
                    matchCount++;
                    break;
                  }
                }
              }
              
              // ถ้ามีตัวเลขที่ตรงกันมากกว่า 50% ถือว่าผ่าน
              if (totalDigits > 0 && matchCount >= totalDigits * 0.5) {
                accountNumberMatches = true;
                console.log('ตัวเลขที่ไม่ถูกปิดบังตรงกัน:', matchCount, 'จาก', totalDigits);
              }
            }
            
            console.log('ตรวจสอบเลขบัญชี:', {
              expectedAcc: acc.accountNumber,
              sanitizedExpected: sanitizedAccountNumber,
              receivedAcc: receiverAccount,
              sanitizedReceived: sanitizedReceiverAccount,
              lastFourValid,
              lastFourReceiver,
              isMatch: accountNumberMatches
            });
            
            // ตรวจสอบชื่อบัญชี (ถ้ามีชื่อบัญชี) - ปรับให้ยืดหยุ่นมากขึ้น
            let nameMatches = false;
            
            if (sanitizedReceiverName) {
              console.log('กำลังตรวจสอบชื่อบัญชี:');
              console.log('- ชื่อในสลิป:', receiverName, '/', sanitizedReceiverName);
              console.log('- ชื่อในระบบ (TH):', acc.accountName, '/', validNameTH);
              console.log('- ชื่อในระบบ (EN):', acc.accountNameEn, '/', validNameEN);
              
              // 1. ตรวจสอบแบบตรงกันสมบูรณ์
              if (validNameTH === sanitizedReceiverName || validNameEN === sanitizedReceiverName) {
                console.log('ชื่อตรงกันสมบูรณ์');
                nameMatches = true;
              }
              
              // 2. ตรวจสอบโดยใช้ Levenshtein Distance (ยอมให้ผิดได้ 15%)
              if (!nameMatches && validNameTH.length > 0) {
                const distance = levenshteinDistance(validNameTH, sanitizedReceiverName);
                const similarity = 1 - (distance / Math.max(validNameTH.length, sanitizedReceiverName.length));
                console.log(`ความคล้ายคลึงของชื่อไทย: ${similarity.toFixed(2)} (${distance} / ${Math.max(validNameTH.length, sanitizedReceiverName.length)})`);
                
                if (similarity >= 0.85) { // ยอมให้ผิดได้ 15%
                  console.log('ชื่อไทยคล้ายกันมากพอ');
                  nameMatches = true;
                }
              }
              
              if (!nameMatches && validNameEN.length > 0) {
                const distance = levenshteinDistance(validNameEN, sanitizedReceiverName);
                const similarity = 1 - (distance / Math.max(validNameEN.length, sanitizedReceiverName.length));
                console.log(`ความคล้ายคลึงของชื่ออังกฤษ: ${similarity.toFixed(2)} (${distance} / ${Math.max(validNameEN.length, sanitizedReceiverName.length)})`);
                
                if (similarity >= 0.85) { // ยอมให้ผิดได้ 15%
                  console.log('ชื่ออังกฤษคล้ายกันมากพอ');
                  nameMatches = true;
                }
              }
              
              // 3. ตรวจสอบว่าเป็นชื่อย่อของกันและกันหรือไม่
              if (!nameMatches) {
                // ตัดสระและวรรณยุกต์เพื่อเปรียบเทียบเฉพาะพยัญชนะ
                const consonantOnlyTH = validNameTH.replace(/[่้๊๋็์ะาิีึืุูเแโใไๅำั]/g, "");
                const consonantOnlyReceiver = sanitizedReceiverName.replace(/[่้๊๋็์ะาิีึืุูเแโใไๅำั]/g, "");
                
                console.log('ชื่อหลังตัดสระ (TH):', consonantOnlyTH);
                console.log('ชื่อหลังตัดสระ (receiver):', consonantOnlyReceiver);
                
                // ตรวจสอบว่าเป็นส่วนหนึ่งของกันและกันหรือไม่
                if (consonantOnlyTH.includes(consonantOnlyReceiver) || 
                    consonantOnlyReceiver.includes(consonantOnlyTH)) {
                  console.log('พยัญชนะเป็นส่วนหนึ่งของกันและกัน');
                  nameMatches = true;
                }
              }
            } else {
              // ถ้าไม่มีชื่อผู้รับ ให้ผ่านได้
              nameMatches = true;
            }
            
            console.log('ตรวจสอบชื่อบัญชี:', {
              validNameTH,
              validNameEN,
              receiverName: sanitizedReceiverName,
              isMatch: nameMatches
            });
            
            // ต้องผ่านทั้งเลขบัญชีและชื่อบัญชี ถึงจะให้ผ่าน
            const bothMatch = accountNumberMatches && (nameMatches || !sanitizedReceiverName);
            
            // ถ้าไม่มีข้อมูลชื่อผู้รับ ให้ผ่านเฉพาะเลขบัญชี
            return bothMatch;
          });
        }
        
        // ตรวจสอบพร้อมเพย์ - ใช้การตรวจสอบแบบยืดหยุ่นมากขึ้น
        // กรณีที่เป็นพร้อมเพย์สามารถตรวจสอบได้จากหลายวิธี:
        // 1. ตรวจสอบจาก responsePayload ถ้ามีคำว่า promptpay หรือ พร้อมเพย์
        const responsePayload = JSON.stringify(verifyResult.data || {}).toLowerCase();
        const isPromptpayTransaction = 
          responsePayload.includes('promptpay') || 
          responsePayload.includes('พร้อมเพย์');
        
        if (!isValidReceiver && receiverAccount) {
          const sanitizedReceiverAccount = receiverAccount.replace(/[-\s]/g, "");
          
          // แสดงข้อมูลสำหรับการแก้ไขปัญหา
          console.log('ตรวจสอบพร้อมเพย์:', {
            receivedPromptpay: receiverAccount,
            sanitizedReceived: sanitizedReceiverAccount,
            validPromptpays
          });
          
          // ตรวจสอบหมายเลขพร้อมเพย์แบบยืดหยุ่นมากขึ้น
          isValidReceiver = validPromptpays.some(number => {
            const sanitizedPromptpay = number.replace(/[-\s]/g, "");
            
            // ตรวจสอบแบบยืดหยุ่นโดยมีหลายเงื่อนไข
            const lastSixValid = sanitizedPromptpay.slice(-6);
            const lastSixReceiver = sanitizedReceiverAccount.slice(-6);
            const lastFourValid = sanitizedPromptpay.slice(-4);
            const lastFourReceiver = sanitizedReceiverAccount.slice(-4);
            
            // เพิ่มเงื่อนไขให้ยืดหยุ่นมากขึ้น
            const promptpayMatches = 
              // เงื่อนไข 1: ทั้งหมดต้องตรงกัน
              (sanitizedPromptpay === sanitizedReceiverAccount) ||
              // เงื่อนไข 2: มีเลข 6 ตัวท้ายตรงกัน
              (lastSixValid === lastSixReceiver && lastSixValid.length >= 4) ||
              // เงื่อนไข 3: มีเลข 4 ตัวท้ายตรงกัน
              (lastFourValid === lastFourReceiver && lastFourValid.length >= 4) ||
              // เงื่อนไข 4: หมายเลขรวมอยู่ในกันและกัน
              sanitizedPromptpay.includes(sanitizedReceiverAccount) || 
              sanitizedReceiverAccount.includes(sanitizedPromptpay);
              
            console.log('เปรียบเทียบพร้อมเพย์ (ปรับปรุง):', {
              expected: number,
              sanitizedExpected: sanitizedPromptpay,
              lastSixValid,
              lastSixReceiver,
              lastFourValid,
              lastFourReceiver,
              isMatch: promptpayMatches
            });
              
            return promptpayMatches;
          });
        }
        
        // กรณีการโอนพร้อมเพย์: หลายครั้งไม่มีเลขบัญชี มีแค่ชื่อผู้รับเงิน
        // มีหลายกรณีที่ควรอนุญาตให้ทำรายการ:
        // 1. ไม่มีข้อมูลบัญชีธนาคารหรือพร้อมเพย์จาก API
        // 2. เป็นการโอนพร้อมเพย์ที่มีชื่อแต่ไม่มีเลขบัญชี
        if (!receiverBankId && !receiverAccount) {
          console.log('ไม่พบข้อมูลบัญชีธนาคารหรือพร้อมเพย์ในผลการตรวจสอบ จะอนุญาตให้ทำรายการ');
          isValidReceiver = true;
        }
        
        // ตรวจสอบว่าเป็นการโอนเงินแบบพร้อมเพย์หรือไม่
        // โดยตรวจหาเงื่อนไขหลัก: มีชื่อผู้รับเงิน แต่ไม่มีเลขบัญชี
        const isPromptpayByStructure = receiverName && !receiverAccount;
        
        if (!isValidReceiver && (isPromptpayTransaction || isPromptpayByStructure)) {
          console.log('ตรวจพบว่าเป็นรายการโอนพร้อมเพย์ (จากโครงสร้างข้อมูลหรือ transaction type)');
          
          // สำหรับพร้อมเพย์ ไม่จำเป็นต้องตรวจสอบเลขบัญชี ตรวจชื่อเท่านั้น
          // แต่ปัจจุบันยังไม่มีการตรวจสอบชื่อเนื่องจากความหลากหลายของชื่อบัญชี
          // จึงอนุมัติรายการที่เป็นการโอนพร้อมเพย์ โดยไม่ตรวจสอบเพิ่มเติม
          console.log('รายการโอนพร้อมเพย์ยืนยันโดยโครงสร้างข้อมูล (มีชื่อแต่ไม่มีเลขบัญชี) จะยอมรับรายการนี้');
          isValidReceiver = true;
        }
        
        if (!isValidReceiver) {
          console.log('Detected bank ID:', receiverBankId);
          console.log('Detected account number:', receiverAccount);
          console.log('Detected account name:', receiverName);
          console.log('Valid bank accounts:', validBankAccounts);
          console.log('Valid promptpays:', validPromptpays);
          throw new Error("บัญชีผู้รับเงินไม่ตรงกับบัญชีในระบบ กรุณาตรวจสอบว่าโอนเงินถูกบัญชีหรือไม่");
        }

        // เก็บรูปภาพสลิปถาวรในโฟลเดอร์สาธารณะ
        let permanentSlipPath = '';
        try {
          const slipDirectory = path.join('public', 'uploads', 'slips');
          // สร้างโฟลเดอร์ถ้ายังไม่มี
          if (!fs.existsSync(slipDirectory)) {
            fs.mkdirSync(slipDirectory, { recursive: true });
          }
          
          // สร้างชื่อไฟล์ใหม่สำหรับบันทึกถาวร
          const fileExt = path.extname(req.file.originalname);
          const fileName = `slip_image-${req.user!.id}-${Date.now()}${fileExt}`;
          permanentSlipPath = path.join('uploads', 'slips', fileName);
          
          // คัดลอกไฟล์ไปเก็บถาวร
          const sourcePath = req.file.path;
          const destinationPath = path.join('public', permanentSlipPath);
          
          fs.copyFileSync(sourcePath, destinationPath);
          console.log(`Slip image saved permanently at: ${permanentSlipPath}`);
          console.log(`Absolute path: ${destinationPath}`);
        } catch (error) {
          console.error("Error saving permanent slip image:", error);
        }
        
        // บันทึกผลการตรวจสอบสลิป
        const slipVerification = await storage.createSlipVerification({
          userId: req.user!.id,
          status: "success",
          verificationSource: "Credit+", // เพิ่มแหล่งที่มาของการตรวจสอบ
          imagePath: permanentSlipPath, // บันทึกเป็นพาธถาวร
          responseData: JSON.stringify(verifyResult.data || {}),
          transactionRef: verifyResult.data?.transRef || "",
          amount: slipAmount,
          transactionDate: verifyResult.data?.date ? new Date(verifyResult.data.date) : new Date(),
          // ข้อมูลธนาคารและชื่อผู้โอน
          bankName: verifyResult.data?.sender?.bank?.name || "",
          sender: verifyResult.data?.sender?.account?.name?.th || "",
          // ข้อมูลผู้รับเงิน
          receiver: receiverName || ""
        });

        // อัปเดตรายการเติมเงิน
        const updatedTransaction = await storage.updateTopUpTransaction(id, {
          status: "completed",
          verificationId: slipVerification.id
        });

        // เพิ่มเครดิตให้ผู้ใช้ (ใช้จำนวนเงินตามที่พบในสลิป)
        await storage.addUserCredit(req.user!.id, slipAmount);
        
        console.log(`เติมเครดิต ${slipAmount} บาท สำเร็จ (จำนวนเงินที่ผู้ใช้ระบุไว้แต่แรก: ${requestedAmount} บาท)`);
        
        // เพิ่มบันทึกว่ามีการใช้ยอดเงินจากสลิปจริง
        if (slipAmount !== requestedAmount) {
          console.log(`มีการปรับยอดเงินตามที่พบในสลิป: ${requestedAmount} -> ${slipAmount} บาท`);
        }

        // ส่งผลลัพธ์
        res.status(200).json({
          success: true,
          transaction: updatedTransaction,
          verification: slipVerification
        });
      } catch (error: any) {
        console.error("Error verifying slip:", error);

        // เก็บรูปภาพสลิปถาวรในโฟลเดอร์สาธารณะ (แม้จะล้มเหลว)
        let permanentSlipPath = '';
        try {
          const slipDirectory = path.join('public', 'uploads', 'slips');
          // สร้างโฟลเดอร์ถ้ายังไม่มี
          if (!fs.existsSync(slipDirectory)) {
            fs.mkdirSync(slipDirectory, { recursive: true });
          }
          
          // สร้างชื่อไฟล์ใหม่สำหรับบันทึกถาวร
          const fileExt = path.extname(req.file.originalname);
          const fileName = `slip_failed-${req.user!.id}-${Date.now()}${fileExt}`;
          permanentSlipPath = path.join('uploads', 'slips', fileName);
          
          // คัดลอกไฟล์ไปเก็บถาวร
          const sourcePath = req.file.path;
          const destinationPath = path.join('public', permanentSlipPath);
          
          fs.copyFileSync(sourcePath, destinationPath);
          console.log(`Failed slip image saved permanently at: ${permanentSlipPath}`);
        } catch (error) {
          console.error("Error saving permanent slip image:", error);
        }
        
        // บันทึกผลการตรวจสอบสลิปที่ล้มเหลว
        const slipVerification = await storage.createSlipVerification({
          userId: req.user!.id,
          status: "failed",
          verificationSource: "Credit+", // เพิ่มแหล่งที่มาของการตรวจสอบ
          imagePath: permanentSlipPath || req.file.path,
          responseData: JSON.stringify({ error: error.message || "ไม่สามารถตรวจสอบสลิปได้" }),
        });

        // อัปเดตรายการเติมเงิน
        await storage.updateTopUpTransaction(id, {
          status: "failed",
          verificationId: slipVerification.id
        });

        res.status(400).json({
          success: false,
          error: error.message || "ไม่สามารถตรวจสอบสลิปได้"
        });
      }
    } catch (error) {
      console.error("Error verifying top-up transaction:", error);
      
      // ลบไฟล์ที่อัปโหลดเมื่อเกิดข้อผิดพลาด
      if (req.file) {
        try {
          fs.unlinkSync(req.file.path);
        } catch (unlinkError) {
          console.error("Error deleting file:", unlinkError);
        }
      }
      
      res.status(500).json({ error: "เกิดข้อผิดพลาดในการยืนยันการชำระเงิน" });
    }
  });
}

// สร้างรหัสอ้างอิง
function generateReferenceCode(): string {
  const timestamp = Date.now().toString().slice(-6);
  const random = randomBytes(3).toString('hex');
  return `SLP${timestamp}${random.toUpperCase()}`;
}