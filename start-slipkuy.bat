@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    🚀 เริ่มต้น SLIPKUY Docker System
echo ========================================
echo.

echo 📋 ตรวจสอบสถานะ Docker...
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker ไม่ได้ติดตั้งหรือไม่ทำงาน
    echo กรุณาติดตั้ง Docker Desktop และเริ่มต้นใหม่
    pause
    exit /b 1
)

echo ✅ Docker พร้อมใช้งาน
echo.

echo 🔧 เริ่มต้นระบบ SLIPKUY...
docker-compose up -d

if errorlevel 1 (
    echo ❌ เกิดข้อผิดพลาดในการเริ่มต้นระบบ
    pause
    exit /b 1
)

echo.
echo ⏳ รอระบบเริ่มต้น...
timeout /t 10 /nobreak >nul

echo.
echo 🔍 ตรวจสอบสถานะ containers...
docker-compose ps

echo.
echo ✅ ระบบ SLIPKUY เริ่มต้นเรียบร้อยแล้ว!
echo.
echo 🌐 เข้าใช้งานได้ที่: http://localhost:4000
echo 👤 Admin Login: tmognot
echo 👤 User Login: test
echo.
echo 📊 ตรวจสอบ logs: logs-slipkuy.bat
echo 🛑 หยุดระบบ: stop-slipkuy.bat
echo.
pause
