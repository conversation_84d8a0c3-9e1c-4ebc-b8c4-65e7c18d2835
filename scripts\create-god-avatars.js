/**
 * สคริปต์สร้างอวตารเทพเจ้า 10 รูป
 * รันด้วยคำสั่ง: node scripts/create-god-avatars.js
 */

import fs from 'fs';
import path from 'path';
import sharp from 'sharp';
import { fileURLToPath } from 'url';

// แก้ไขให้ใช้งานได้กับ ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// รายชื่อเทพเจ้า 10 อย่าง
const godAvatars = [
  { id: 1, name: 'เทพเจ้าแห่งแสง', color: '#ffd700' },     // สีทอง
  { id: 2, name: 'เทพเจ้าแห่งความมืด', color: '#4b0082' }, // สีม่วงเข้ม
  { id: 3, name: 'เทพเจ้าแห่งสายฟ้า', color: '#00bfff' },  // สีฟ้าสว่าง
  { id: 4, name: 'เทพเจ้าแห่งทะเล', color: '#1e90ff' },    // สีฟ้าน้ำทะเล
  { id: 5, name: 'เทพเจ้าแห่งไฟ', color: '#ff4500' },      // สีส้มแดง
  { id: 6, name: 'เทพเจ้าแห่งพายุ', color: '#708090' },    // สีเทา
  { id: 7, name: 'เทพเจ้าแห่งดาวเคราะห์', color: '#9370db' }, // สีม่วงอ่อน
  { id: 8, name: 'เทพเจ้าแห่งโชคลาภ', color: '#32cd32' },  // สีเขียวสด
  { id: 9, name: 'เทพเจ้าแห่งปัญญา', color: '#ffa500' },   // สีส้ม
  { id: 10, name: 'เทพเจ้าแห่งเวลา', color: '#a0522d' }    // สีน้ำตาล
];

// สร้างโฟลเดอร์เก็บอวตาร
const defaultAvatarsDir = path.join(process.cwd(), 'public', 'uploads', 'default-avatars');
if (!fs.existsSync(defaultAvatarsDir)) {
  fs.mkdirSync(defaultAvatarsDir, { recursive: true });
}

// บันทึกข้อมูลอวตารลงไฟล์ indices.js
const saveAvatarIndices = () => {
  const avatarIndices = godAvatars.map(avatar => ({
    id: avatar.id,
    name: avatar.name,
    path: `/uploads/default-avatars/avatar-${avatar.id}.webp`
  }));

  const indexFile = path.join(defaultAvatarsDir, 'index.js');
  const indexContent = `// กำหนดรูปโปรไฟล์เริ่มต้น 10 รูป
const defaultAvatars = ${JSON.stringify(avatarIndices, null, 2)};

export default defaultAvatars;
// สำหรับการใช้งานกับ require
if (typeof module !== 'undefined') {
  module.exports = defaultAvatars;
}`;

  fs.writeFileSync(indexFile, indexContent);
  console.log(`✓ บันทึกข้อมูลอวตารลงใน ${indexFile}`);
};

// สร้างรูปอวตาร
async function createGodAvatar(avatar) {
  try {
    const size = 200;
    const outputPath = path.join(defaultAvatarsDir, `avatar-${avatar.id}.webp`);
    
    // สร้างรูปวงกลมพื้นหลังสีพร้อมตัวอักษรอยู่ตรงกลาง
    const svgImage = `
      <svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stop-color="${avatar.color}" stop-opacity="1" />
            <stop offset="100%" stop-color="${darkenColor(avatar.color, 30)}" stop-opacity="1" />
          </linearGradient>
          <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
            <feGaussianBlur in="SourceAlpha" stdDeviation="3" />
            <feOffset dx="0" dy="0" result="offsetblur" />
            <feComponentTransfer>
              <feFuncA type="linear" slope="0.7" />
            </feComponentTransfer>
            <feMerge>
              <feMergeNode />
              <feMergeNode in="SourceGraphic" />
            </feMerge>
          </filter>
        </defs>
        <circle cx="${size/2}" cy="${size/2}" r="${size/2 - 5}" fill="url(#grad)" />
        <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="90" font-weight="bold" text-anchor="middle" dominant-baseline="middle" fill="white" filter="url(#shadow)">
          ${avatar.name.charAt(0)}
        </text>
        <circle cx="${size/2}" cy="${size/2}" r="${size/2 - 5}" fill="none" 
          stroke="white" stroke-width="2" stroke-opacity="0.3" />
        ${addPatternForGod(avatar, size)}
      </svg>
    `;

    // แปลง SVG เป็นรูปภาพ WebP
    await sharp(Buffer.from(svgImage))
      .webp({ quality: 90 })
      .toFile(outputPath);

    console.log(`✓ สร้างอวตาร ${avatar.id} (${avatar.name}) เรียบร้อยแล้ว`);
  } catch (error) {
    console.error(`✗ สร้างอวตาร ${avatar.id} ไม่สำเร็จ:`, error);
  }
}

// ทำให้สีเข้มขึ้น
function darkenColor(color, percent) {
  const num = parseInt(color.replace("#", ""), 16);
  const amt = Math.round(2.55 * percent);
  const R = Math.max(0, (num >> 16) - amt);
  const G = Math.max(0, (num >> 8 & 0x00FF) - amt);
  const B = Math.max(0, (num & 0x0000FF) - amt);
  return `#${(0x1000000 + (R << 16) + (G << 8) + B).toString(16).slice(1)}`;
}

// เพิ่มลวดลายตามชนิดของเทพเจ้า
function addPatternForGod(avatar, size) {
  const centerX = size / 2;
  const centerY = size / 2;
  const radius = size / 2 - 10;
  
  switch (avatar.id) {
    case 1: // เทพเจ้าแห่งแสง - รังสีของแสง
      return `
        <g opacity="0.6">
          ${Array.from({ length: 12 }).map((_, i) => {
            const angle = (i * 30) * (Math.PI / 180);
            const x1 = centerX + Math.cos(angle) * (radius * 0.5);
            const y1 = centerY + Math.sin(angle) * (radius * 0.5);
            const x2 = centerX + Math.cos(angle) * radius;
            const y2 = centerY + Math.sin(angle) * radius;
            return `<line x1="${x1}" y1="${y1}" x2="${x2}" y2="${y2}" stroke="white" stroke-width="2" />`;
          }).join('')}
        </g>
      `;
      
    case 2: // เทพเจ้าแห่งความมืด - ดวงดาวในความมืด
      return `
        <g>
          ${Array.from({ length: 20 }).map(() => {
            const x = Math.random() * size;
            const y = Math.random() * size;
            const r = Math.random() * 2 + 1;
            return `<circle cx="${x}" cy="${y}" r="${r}" fill="white" opacity="${Math.random() * 0.7 + 0.3}" />`;
          }).join('')}
        </g>
      `;
      
    case 3: // เทพเจ้าแห่งสายฟ้า - ซิกแซกของสายฟ้า
      return `
        <path d="M ${centerX-40} ${centerY-40} L ${centerX-10} ${centerY} L ${centerX-30} ${centerY+10} L ${centerX+20} ${centerY-20}" 
          stroke="white" stroke-width="3" fill="none" opacity="0.7" />
      `;
      
    case 4: // เทพเจ้าแห่งทะเล - คลื่น
      return `
        <path d="M ${centerX-40} ${centerY+30} Q ${centerX-20} ${centerY+10}, ${centerX} ${centerY+30} T ${centerX+40} ${centerY+30}" 
          stroke="white" stroke-width="2" fill="none" opacity="0.7" />
        <path d="M ${centerX-40} ${centerY+40} Q ${centerX-20} ${centerY+20}, ${centerX} ${centerY+40} T ${centerX+40} ${centerY+40}" 
          stroke="white" stroke-width="2" fill="none" opacity="0.7" />
      `;
      
    case 5: // เทพเจ้าแห่งไฟ - เปลวไฟ
      return `
        <path d="M ${centerX-30} ${centerY+40} Q ${centerX-20} ${centerY}, ${centerX} ${centerY-20} T ${centerX+30} ${centerY+40}" 
          stroke="white" stroke-width="2" fill="none" opacity="0.7" />
      `;
      
    case 6: // เทพเจ้าแห่งพายุ - เมฆและลม
      return `
        <path d="M ${centerX-40} ${centerY-20} Q ${centerX-20} ${centerY-40}, ${centerX} ${centerY-20} T ${centerX+40} ${centerY-20}" 
          stroke="white" stroke-width="2" fill="none" opacity="0.7" />
        <path d="M ${centerX-30} ${centerY+20} L ${centerX+30} ${centerY+20}" 
          stroke="white" stroke-width="2" opacity="0.7" />
        <path d="M ${centerX-20} ${centerY+30} L ${centerX+20} ${centerY+30}" 
          stroke="white" stroke-width="2" opacity="0.7" />
      `;
      
    case 7: // เทพเจ้าแห่งดาวเคราะห์ - วงโคจร
      return `
        <ellipse cx="${centerX}" cy="${centerY}" rx="${radius * 0.8}" ry="${radius * 0.4}" 
          stroke="white" stroke-width="1" fill="none" opacity="0.5" transform="rotate(30 ${centerX} ${centerY})" />
        <circle cx="${centerX + radius * 0.6}" cy="${centerY}" r="5" fill="white" opacity="0.7" />
      `;
      
    case 8: // เทพเจ้าแห่งโชคลาภ - เหรียญ
      return `
        <circle cx="${centerX-20}" cy="${centerY+20}" r="15" stroke="white" stroke-width="2" fill="none" opacity="0.7" />
        <circle cx="${centerX+15}" cy="${centerY+25}" r="10" stroke="white" stroke-width="2" fill="none" opacity="0.7" />
      `;
      
    case 9: // เทพเจ้าแห่งปัญญา - หนังสือ
      return `
        <rect x="${centerX-25}" y="${centerY+15}" width="50" height="30" stroke="white" stroke-width="2" fill="none" opacity="0.7" />
        <line x1="${centerX}" y1="${centerY+15}" x2="${centerX}" y2="${centerY+45}" 
          stroke="white" stroke-width="1" opacity="0.7" />
      `;
      
    case 10: // เทพเจ้าแห่งเวลา - นาฬิกา
      return `
        <circle cx="${centerX}" cy="${centerY}" r="${radius * 0.6}" stroke="white" stroke-width="1" fill="none" opacity="0.7" />
        <line x1="${centerX}" y1="${centerY}" x2="${centerX}" y2="${centerY - radius * 0.4}" 
          stroke="white" stroke-width="2" opacity="0.7" />
        <line x1="${centerX}" y1="${centerY}" x2="${centerX + radius * 0.3}" y2="${centerY}" 
          stroke="white" stroke-width="2" opacity="0.7" />
      `;
      
    default:
      return '';
  }
}

// สร้างอวตารทั้งหมด
async function createAllAvatars() {
  try {
    for (const avatar of godAvatars) {
      await createGodAvatar(avatar);
    }
    saveAvatarIndices();
    console.log('✓ สร้างอวตารเทพเจ้าทั้ง 10 รูปเสร็จสมบูรณ์');
  } catch (error) {
    console.error('✗ เกิดข้อผิดพลาดในการสร้างอวตาร:', error);
  }
}

// เริ่มการทำงาน
createAllAvatars();