import { useState, useEffect, useMemo, useCallback } from "react";
import { useAuth } from "@/hooks/use-auth";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { Dialog, DialogContent, DialogTrigger, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { formatCurrency } from "@/lib/utils";
import { DashboardLayout } from "@/components/layouts/dashboard-responsive-new";
import { AlertCircle, Check, Copy, Loader2, UploadCloud, Sparkles, Wallet, CreditCard, Banknote, CoinsIcon, Gem, Star, Zap, ImageIcon, ZoomIn } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
// QRCode ถูกลบออกตามคำขอ เนื่องจากใช้รูปจากฐานข้อมูลแทน
import { motion } from "framer-motion";

// Define schema for top-up form
const topUpSchema = z.object({
  amount: z.coerce
    .number()
    .int()
    .min(100, { message: "จำนวนเงินขั้นต่ำคือ 100 บาท" })
    .max(100000, { message: "จำนวนเงินสูงสุดคือ 100,000 บาท" }),
});

// Define interface for top-up data
interface TopUpTransaction {
  id: number;
  amount: number;
  status: string;
  referenceCode: string;
  createdAt: string;
  updatedAt: string;
  verificationId?: number;
}

// Create a function to convert numbers to Thai baht text
function numberToThaiText(num: number): string {
  const units = ['', 'หนึ่ง', 'สอง', 'สาม', 'สี่', 'ห้า', 'หก', 'เจ็ด', 'แปด', 'เก้า', 'สิบ'];
  const positions = ['', 'สิบ', 'ร้อย', 'พัน', 'หมื่น', 'แสน', 'ล้าน'];

  if (num === 0) return 'ศูนย์บาทถ้วน';

  let text = '';
  const numStr = num.toString();

  for (let i = 0; i < numStr.length; i++) {
    const digit = parseInt(numStr[i]);
    const pos = numStr.length - i - 1;

    if (digit !== 0) {
      if (pos === 1 && digit === 1) {
        text += 'สิบ';
      } else if (pos === 1 && digit === 2) {
        text += 'ยี่สิบ';
      } else {
        text += units[digit] + positions[pos];
      }
    }
  }

  return text + 'บาทถ้วน';
}

export default function TopUpPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isVerifyDialogOpen, setIsVerifyDialogOpen] = useState(false);
  const [selectedTopUp, setSelectedTopUp] = useState<TopUpTransaction | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [zoomImageUrl, setZoomImageUrl] = useState<string | null>(null);

  // ฟังก์ชันเปิด/ปิด zoom QR code
  const handleZoomImage = (url: string | null) => {
    setZoomImageUrl(url);
  };

  // Create form
  const form = useForm<z.infer<typeof topUpSchema>>({
    resolver: zodResolver(topUpSchema),
    defaultValues: {
      amount: 1000,
    },
  });

  // Query for user credit
  const { data: creditData, isLoading: isLoadingCredit } = useQuery<{ credit: number }>({
    queryKey: ["/api/user/credit"],
    enabled: !!user,
  });

  // ดึงข้อมูลธนาคารจาก API
  const { data: paymentAccounts, isLoading: isLoadingPaymentAccounts } = useQuery<{
    bankAccounts: Array<{
      bankCode: string;
      accountNumber: string;
      accountName: string;
      accountNameEn: string;
      qrPaymentImage: string;
    }>;
    promptpays: Array<{
      number: string;
      qrPaymentImage: string;
    }>;
  }>({
    queryKey: ["/api/payment-accounts"],
    // เพิ่ม fallback data เพื่อป้องกันการแสดงหน้า loading ไม่สิ้นสุด
    placeholderData: {
      bankAccounts: [{
        bankCode: "SCB",
        accountNumber: "123-456-7890",
        accountName: "บริษัท สลิปคุย จำกัด",
        accountNameEn: "SLIPKUY CO., LTD.",
        qrPaymentImage: "/assets/qr-code.png"
      }],
      promptpays: [{
        number: "0-9999-99999-99",
        qrPaymentImage: "/assets/qr-promptpay.png"
      }]
    }
  });

  // Query for user's top-up transactions
  const { data: transactions, isLoading: isLoadingTransactions } = useQuery<TopUpTransaction[]>({
    queryKey: ["/api/topup"],
    enabled: !!user,
  });

  // Create topup mutation
  const createTopUpMutation = useMutation({
    mutationFn: async (amount: number) => {
      const res = await apiRequest("POST", "/api/topup/create", { amount });
      return await res.json();
    },
    onSuccess: (data: TopUpTransaction) => {
      toast({
        title: "สร้างรายการเติมเงินสำเร็จ",
        description: `รหัสอ้างอิง: ${data.referenceCode}`,
      });
      queryClient.invalidateQueries({ queryKey: ["/api/topup"] });
      setSelectedTopUp(data);
    },
    onError: (error: Error) => {
      toast({
        title: "ไม่สามารถสร้างรายการเติมเงินได้",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Verify topup mutation
  const verifyTopUpMutation = useMutation({
    mutationFn: async ({ id, formData }: { id: number; formData: FormData }) => {
      const res = await fetch(`/api/topup/verify/${id}`, {
        method: "POST",
        body: formData,
        credentials: "include",
      });

      if (!res.ok) {
        const error = await res.json();
        throw new Error(error.error || "เกิดข้อผิดพลาดในการตรวจสอบสลิป");
      }

      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "ตรวจสอบสลิปสำเร็จ",
        description: "เติมเงินเข้าบัญชีเรียบร้อยแล้ว",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/topup"] });
      queryClient.invalidateQueries({ queryKey: ["/api/user/credit"] });
      setIsVerifyDialogOpen(false);
      setSelectedTopUp(null);
      setSelectedFile(null);
      setUploadProgress(0);
    },
    onError: (error: Error) => {
      toast({
        title: "ไม่สามารถตรวจสอบสลิปได้",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Handle form submission
  const onSubmit = (values: z.infer<typeof topUpSchema>) => {
    createTopUpMutation.mutate(values.amount);
  };

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0]);
    }
  };

  // Handle file upload and verification
  const handleVerify = () => {
    if (!selectedFile || !selectedTopUp) return;

    const formData = new FormData();
    formData.append("slip", selectedFile);

    // Simulate upload progress
    let progress = 0;
    const interval = setInterval(() => {
      progress += 5;
      if (progress >= 100) {
        clearInterval(interval);
      }
      setUploadProgress(progress);
    }, 100);

    verifyTopUpMutation.mutate(
      { id: selectedTopUp.id, formData },
      {
        onSettled: () => {
          clearInterval(interval);
          setUploadProgress(100);
        },
      }
    );
  };

  // Open verify dialog
  const openVerifyDialog = (topUp: TopUpTransaction) => {
    setSelectedTopUp(topUp);
    setIsVerifyDialogOpen(true);
  };

  // Copy reference code to clipboard
  const copyReferenceCode = (code: string) => {
    navigator.clipboard.writeText(code);
    toast({
      title: "คัดลอกรหัสอ้างอิงแล้ว",
      description: code,
    });
  };

  // Get transaction status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-500">เสร็จสมบูรณ์</Badge>;
      case "pending":
        return <Badge className="bg-yellow-500">รอการยืนยัน</Badge>;
      case "failed":
        return <Badge className="bg-red-500">ล้มเหลว</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // ข้อมูลธนาคารไทย สำหรับแสดงชื่อธนาคาร
  const THAI_BANKS = [
    { code: "004", name: "ธนาคารกสิกรไทย" },
    { code: "002", name: "ธนาคารกรุงเทพ" },
    { code: "006", name: "ธนาคารกรุงไทย" },
    { code: "011", name: "ธนาคารทหารไทยธนชาต" },
    { code: "014", name: "ธนาคารไทยพาณิชย์" },
    { code: "025", name: "ธนาคารกรุงศรีอยุธยา" },
    { code: "030", name: "ธนาคารออมสิน" },
    { code: "034", name: "ธนาคารเกียรตินาคิน" },
    { code: "067", name: "ธนาคารทิสโก้" },
    { code: "065", name: "ธนาคารธนชาต" },
    { code: "069", name: "ธนาคารเกียรตินาคินภัทร" },
    { code: "073", name: "ธนาคารแลนด์ แอนด์ เฮ้าส์" },
    { code: "022", name: "ธนาคารซีไอเอ็มบี ไทย" },
    { code: "018", name: "ธนาคารยูโอบี" },
    { code: "033", name: "ธนาคารอาคารสงเคราะห์" },
    { code: "070", name: "ธนาคารไอซีบีซี (ไทย)" },
    { code: "031", name: "ธนาคารเพื่อการเกษตรและสหกรณ์การเกษตร" },
  ];

  // แปลงข้อมูลบัญชีธนาคารให้พร้อมใช้งาน
  const bankAccounts = useMemo(() => {
    if (isLoadingPaymentAccounts || !paymentAccounts || !paymentAccounts.bankAccounts || !paymentAccounts.bankAccounts.length) {
      return [{
        code: "",
        name: "กรุณารอสักครู่...",
        accountName: "กำลังโหลดข้อมูล",
        accountNameEn: "Loading...",
        accountNumber: "-",
        qrPaymentImage: "",
      }];
    }

    try {
      // แปลงข้อมูลทุกบัญชี
      return paymentAccounts.bankAccounts.map(bank => ({
        code: bank.bankCode,
        name: THAI_BANKS.find(b => b.code === bank.bankCode)?.name || bank.bankCode,
        accountName: bank.accountName,
        accountNameEn: bank.accountNameEn,
        accountNumber: bank.accountNumber,
        qrPaymentImage: bank.qrPaymentImage || "",
      }));
    } catch (error) {
      console.error("Error processing bank accounts data:", error);
      return [{
        code: "",
        name: "ไม่สามารถโหลดข้อมูลได้",
        accountName: "กรุณาลองใหม่อีกครั้ง",
        accountNameEn: "Please try again",
        accountNumber: "-",
        qrPaymentImage: "",
      }];
    }
  }, [paymentAccounts, isLoadingPaymentAccounts, THAI_BANKS]);

  // แปลงข้อมูล PromptPay
  const promptpayInfo = useMemo(() => {
    if (isLoadingPaymentAccounts || !paymentAccounts || !paymentAccounts.promptpays || !paymentAccounts.promptpays.length) {
      return {
        number: "-",
        qrImage: "",
      };
    }

    try {
      const firstPromptpay = paymentAccounts.promptpays[0];

      if (typeof firstPromptpay === 'string') {
        // รูปแบบเก่า: array ของ string
        return {
          number: firstPromptpay,
          qrImage: "",
        };
      } else if (typeof firstPromptpay === 'object' && firstPromptpay !== null) {
        // รูปแบบใหม่: array ของ object { number, qrPaymentImage }
        return {
          number: firstPromptpay.number || "-",
          qrImage: firstPromptpay.qrPaymentImage || "",
        };
      }

      return {
        number: "-",
        qrImage: "",
      };
    } catch (error) {
      console.error("Error processing promptpay data:", error);
      return {
        number: "-",
        qrImage: "",
      };
    }
  }, [paymentAccounts, isLoadingPaymentAccounts]);

  // สำหรับความเข้ากันได้กับโค้ดเดิม
  const bankInfo = useMemo(() => {
    if (bankAccounts.length === 0) {
      return {
        code: "",
        name: "กรุณารอสักครู่...",
        accountName: "กำลังโหลดข้อมูล",
        accountNameEn: "Loading...",
        accountNumber: "-",
        promptpayNumber: promptpayInfo.number,
        promptpayQrImage: promptpayInfo.qrImage,
        qrPaymentImage: "",
      };
    }

    return {
      ...bankAccounts[0],
      promptpayNumber: promptpayInfo.number,
      promptpayQrImage: promptpayInfo.qrImage,
    };
  }, [bankAccounts, promptpayInfo]);

  return (
    <DashboardLayout>
      <div className="relative z-10">
        {/* Decorative Background Elements */}
        <div className="absolute -top-12 left-1/4 w-20 h-20 rounded-full bg-amber-600/10 filter blur-2xl"></div>
        <div className="absolute top-40 right-20 w-32 h-32 rounded-full bg-indigo-600/20 filter blur-3xl"></div>
        <div className="absolute bottom-40 left-10 w-24 h-24 rounded-full bg-purple-600/10 filter blur-2xl"></div>

        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="relative z-10 container mx-auto py-6 max-w-6xl"
        >
          <div className="flex items-center mb-6">
            <div className="mr-3 relative">
              <div className="absolute -inset-1 rounded-full bg-amber-600/30 animate-pulse blur-md"></div>
              <div className="relative bg-gradient-to-br from-amber-700 to-amber-900 h-10 w-10 rounded-full flex items-center justify-center">
                <Banknote className="h-5 w-5 text-amber-300" />
              </div>
            </div>
            <div>
              <h1 className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-100 to-white">การเติมพลังทิพย์</h1>
              <p className="text-indigo-300 text-sm">เพิ่มกำลังพลในการใช้บริการตรวจสอบของเทพเจ้า</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Credit Card */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="md:col-span-1"
            >
              <Card className="overflow-hidden backdrop-blur-md bg-gradient-to-br from-indigo-950/80 to-purple-900/80 border-indigo-800/30 shadow-lg">
                <CardHeader className="relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-amber-800/30 to-amber-900/10"></div>
                  <div className="relative z-10 flex items-center">
                    <div className="mr-3 h-8 w-8 rounded-full bg-gradient-to-br from-amber-600 to-amber-800 flex items-center justify-center">
                      <Gem className="h-4 w-4 text-amber-100" />
                    </div>
                    <div>
                      <CardTitle className="text-indigo-100">คลังสมบัติของเทพเจ้า</CardTitle>
                      <CardDescription className="text-indigo-300">พลังทิพย์ที่ท่านสะสมไว้ในอาณาจักร</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-4">
                  <motion.div
                    className="text-center relative"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    {isLoadingCredit ? (
                      <div className="flex items-center justify-center py-6">
                        <div className="h-10 w-10 rounded-full border-4 border-amber-600 border-t-transparent animate-spin"></div>
                      </div>
                    ) : (
                      <div className="relative py-4">
                        <div className="absolute -inset-2 rounded-full bg-amber-600/20 animate-pulse blur-md"></div>
                        <motion.div
                          className="text-4xl font-bold bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent relative"
                          initial={{ scale: 0.8 }}
                          animate={{ scale: 1 }}
                          transition={{
                            type: "spring",
                            bounce: 0.5,
                            duration: 0.8
                          }}
                        >
                          {formatCurrency(creditData?.credit || 0)}
                          <motion.div
                            className="absolute -right-4 -top-2"
                            initial={{ opacity: 0, rotate: -45 }}
                            animate={{ opacity: 1, rotate: 0 }}
                            transition={{ delay: 0.5 }}
                          >
                            <Sparkles className="h-5 w-5 text-amber-400" />
                          </motion.div>
                        </motion.div>
                        <p className="text-xs text-indigo-400 mt-2">ใช้ในการอัปเกรดพลังทิพย์และความสามารถ</p>
                      </div>
                    )}
                  </motion.div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Top-up Form */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="md:col-span-2"
            >
              <Card className="backdrop-blur-md overflow-hidden bg-gradient-to-br from-indigo-950/80 to-purple-900/80 border-indigo-800/30 shadow-lg">
                <CardHeader className="relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-indigo-800/30 to-indigo-900/10"></div>
                  <div className="relative z-10 flex items-center">
                    <div className="mr-3 h-8 w-8 rounded-full bg-gradient-to-br from-indigo-600 to-indigo-800 flex items-center justify-center">
                      <Zap className="h-4 w-4 text-indigo-100" />
                    </div>
                    <div>
                      <CardTitle className="text-indigo-100">การเพิ่มพลังทิพย์</CardTitle>
                      <CardDescription className="text-indigo-300">ส่งพลังงานจากโลกมนุษย์สู่สรวงสวรรค์ของเทพเจ้า</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <Tabs defaultValue="promptpay" className="w-full">
                    <TabsList className="grid w-full grid-cols-2 mb-4 bg-indigo-950/60">
                      <TabsTrigger value="promptpay" className="data-[state=active]:bg-indigo-700/40 data-[state=active]:text-indigo-100">
                        <CoinsIcon className="h-4 w-4 mr-2" />
                        พร้อมเพย์
                      </TabsTrigger>
                      <TabsTrigger value="bank" className="data-[state=active]:bg-indigo-700/40 data-[state=active]:text-indigo-100">
                        <CreditCard className="h-4 w-4 mr-2" />
                        บัญชีธนาคาร
                      </TabsTrigger>
                    </TabsList>

                    <Form {...form}>
                      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <FormField
                          control={form.control}
                          name="amount"
                          render={({ field }) => (
                            <FormItem className="relative">
                              <FormLabel className="text-indigo-200">จำนวนพลังทิพย์ (บาท)</FormLabel>
                              <FormControl>
                                <div className="relative">
                                  <div className="absolute left-0 inset-y-0 w-10 flex items-center justify-center">
                                    <Sparkles className="h-4 w-4 text-amber-400" />
                                  </div>
                                  <Input
                                    type="number"
                                    placeholder="กรอกจำนวนเงิน"
                                    className="pl-10 bg-indigo-950/50 border-indigo-700/30 text-indigo-100 placeholder:text-indigo-400/70 focus:border-amber-500/50 focus:ring-amber-500/30"
                                    {...field}
                                  />
                                </div>
                              </FormControl>
                              <FormMessage className="text-red-400" />
                            </FormItem>
                          )}
                        />

                        {form.watch("amount") > 0 && (
                          <div className="text-sm text-indigo-300">
                            จำนวนเงิน: {numberToThaiText(form.watch("amount"))}
                          </div>
                        )}

                        {selectedTopUp ? (
                          <div className="space-y-4">
                            <Alert className="bg-indigo-950/40 border-amber-500/30">
                              <Sparkles className="h-4 w-4 text-amber-400" />
                              <AlertTitle className="text-indigo-100">สร้างคำขอพลังเรียบร้อย</AlertTitle>
                              <AlertDescription className="text-indigo-300">
                                รหัสอ้างอิง: <span className="text-amber-400 font-semibold">{selectedTopUp.referenceCode}</span>
                                <div className="mt-1">
                                  <Button variant="outline" size="sm" className="gap-1 bg-transparent border-indigo-700/50 hover:bg-indigo-900/30 text-indigo-300" onClick={() => copyReferenceCode(selectedTopUp.referenceCode)}>
                                    <Copy className="h-3.5 w-3.5" />
                                    คัดลอกรหัส
                                  </Button>
                                </div>
                              </AlertDescription>
                            </Alert>

                            <TabsContent value="promptpay">
                              <div className="p-6 bg-indigo-950/40 border border-indigo-800/30 rounded-md space-y-6">
                                <div className="space-y-4">
                                  {/* คำแนะนำ */}
                                  <div className="rounded-lg bg-gradient-to-r from-indigo-900/50 to-indigo-800/30 p-3 border border-indigo-700/40">
                                    <div className="flex items-start">
                                      <div className="mr-3 mt-1 h-6 w-6 rounded-full bg-gradient-to-br from-amber-600 to-amber-800 flex-shrink-0 flex items-center justify-center">
                                        <Zap className="h-3 w-3 text-amber-100" />
                                      </div>
                                      <div>
                                        <p className="text-indigo-200 text-sm mb-1">สแกน QR Code และโอนเงินพร้อมระบุรหัสอ้างอิงในช่องหมายเหตุการโอนเงิน</p>
                                        <p className="text-amber-400 font-medium flex items-center">
                                          รหัสอ้างอิง: {selectedTopUp.referenceCode}
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            className="h-8 w-8 p-0 ml-1 text-indigo-300 hover:text-amber-400 hover:bg-transparent"
                                            onClick={() => copyReferenceCode(selectedTopUp.referenceCode)}
                                          >
                                            <Copy className="h-3.5 w-3.5" />
                                          </Button>
                                        </p>
                                      </div>
                                    </div>
                                  </div>

                                  {/* PromptPay */}
                                  <div className="rounded-lg bg-gradient-to-br from-indigo-900/70 to-purple-900/60 border border-indigo-700/40 p-4 relative overflow-hidden">
                                    {/* เอฟเฟคพื้นหลัง */}
                                    <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-amber-600/10 to-amber-800/5 rounded-full filter blur-xl transform translate-x-10 -translate-y-10"></div>

                                    {/* หัวใบเสร็จ */}
                                    <div className="flex items-start mb-3">
                                      <div className="mr-3 h-8 w-8 rounded-md bg-gradient-to-br from-indigo-700 to-indigo-800 flex items-center justify-center flex-shrink-0">
                                        <CoinsIcon className="h-4 w-4 text-indigo-200" />
                                      </div>
                                      <div>
                                        <h4 className="font-medium text-indigo-100">พร้อมเพย์</h4>
                                        <p className="text-xs text-indigo-400">ชำระเงินจำนวน <span className="text-amber-400 font-medium">{formatCurrency(selectedTopUp.amount)}</span></p>
                                      </div>
                                    </div>

                                    {/* รายละเอียด */}
                                    <div className="mb-3 px-2">
                                      <div className="max-w-2xl mx-auto">
                                        <div className="mb-3 py-3 px-6 bg-gradient-to-br from-indigo-900/70 to-purple-900/50 border border-indigo-700/40 rounded-xl relative overflow-hidden">
                                          {/* เอฟเฟคพื้นหลัง */}
                                          <div className="absolute -top-5 -right-5 w-20 h-20 bg-amber-600/10 filter blur-2xl"></div>
                                          <div className="absolute bottom-0 left-10 w-16 h-16 bg-indigo-600/10 filter blur-xl"></div>

                                          <div className="space-y-5">
                                            <div className="flex items-center mb-2">
                                              <div className="h-10 w-10 rounded-full bg-gradient-to-br from-amber-600 to-amber-800 flex items-center justify-center mr-3">
                                                <CoinsIcon className="h-5 w-5 text-amber-100" />
                                              </div>
                                              <div>
                                                <h4 className="text-lg font-semibold text-indigo-100">พร้อมเพย์</h4>
                                                <p className="text-sm text-indigo-300">โอนเงินเข้าพร้อมเพย์เพื่อเติมพลังทิพย์</p>
                                              </div>
                                            </div>

                                            <div className="space-y-4">
                                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                                <div className="space-y-1 p-3 bg-indigo-900/40 rounded-lg border border-indigo-800/30">
                                                  <p className="text-xs text-indigo-400">หมายเลขพร้อมเพย์</p>
                                                  <div className="flex items-center">
                                                    <p className="font-medium text-amber-400 text-xl mr-2">{promptpayInfo.number || "-"}</p>
                                                    <Button
                                                      variant="ghost"
                                                      size="sm"
                                                      className="h-7 w-7 p-0 text-indigo-300 hover:text-amber-400 hover:bg-transparent"
                                                      onClick={() => copyReferenceCode(promptpayInfo.number)}
                                                      disabled={!promptpayInfo.number || promptpayInfo.number === "-"}
                                                    >
                                                      <Copy className="h-3.5 w-3.5" />
                                                    </Button>
                                                  </div>
                                                </div>

                                                <div className="space-y-1 p-3 bg-indigo-900/40 rounded-lg border border-indigo-800/30">
                                                  <p className="text-xs text-indigo-400">รหัสอ้างอิง (ใส่ในช่องหมายเหตุ)</p>
                                                  <div className="flex items-center">
                                                    <p className="font-medium text-amber-400 text-xl mr-2">{selectedTopUp.referenceCode}</p>
                                                    <Button
                                                      variant="ghost"
                                                      size="sm"
                                                      className="h-7 w-7 p-0 text-indigo-300 hover:text-amber-400 hover:bg-transparent"
                                                      onClick={() => copyReferenceCode(selectedTopUp.referenceCode)}
                                                    >
                                                      <Copy className="h-3.5 w-3.5" />
                                                    </Button>
                                                  </div>
                                                </div>
                                              </div>

                                              <div className="space-y-2">
                                                <div className="flex justify-between items-center">
                                                  <p className="text-sm text-indigo-300">ชื่อบัญชี</p>
                                                  <p className="font-medium text-indigo-100">{bankInfo.accountName || "-"}</p>
                                                </div>
                                                <div className="flex justify-between items-center">
                                                  <p className="text-sm text-indigo-300">ชื่อบัญชี (อังกฤษ)</p>
                                                  <p className="font-medium text-indigo-100 italic">{bankInfo.accountNameEn || "-"}</p>
                                                </div>
                                                <div className="flex justify-between items-center">
                                                  <p className="text-sm text-indigo-300">จำนวนเงิน</p>
                                                  <p className="font-medium text-amber-400">{formatCurrency(selectedTopUp.amount)}</p>
                                                </div>
                                                <div>
                                                  <p className="text-xs text-indigo-400 text-right">{numberToThaiText(selectedTopUp.amount)}</p>
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>

                                <Button
                                  type="button"
                                  onClick={() => openVerifyDialog(selectedTopUp)}
                                  className="w-full bg-gradient-to-r from-amber-600 to-amber-800 hover:from-amber-700 hover:to-amber-900"
                                >
                                  <Sparkles className="h-4 w-4 mr-2" />
                                  อัปโหลดสลิปและยืนยันการส่งพลัง
                                </Button>
                              </div>
                            </TabsContent>

                            <TabsContent value="bank">
                              <div className="p-6 bg-indigo-950/40 border border-indigo-800/30 rounded-md space-y-6">
                                <div className="space-y-4">
                                  {/* คำแนะนำ */}
                                  <div className="rounded-lg bg-gradient-to-r from-indigo-900/50 to-indigo-800/30 p-3 border border-indigo-700/40">
                                    <div className="flex items-start">
                                      <div className="mr-3 mt-1 h-6 w-6 rounded-full bg-gradient-to-br from-amber-600 to-amber-800 flex-shrink-0 flex items-center justify-center">
                                        <Zap className="h-3 w-3 text-amber-100" />
                                      </div>
                                      <div>
                                        <p className="text-indigo-200 text-sm mb-1">โอนเงินไปยังบัญชีธนาคารด้านล่างพร้อมระบุรหัสอ้างอิงในช่องหมายเหตุการโอนเงิน</p>
                                        <p className="text-amber-400 font-medium flex items-center">
                                          รหัสอ้างอิง: {selectedTopUp.referenceCode}
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            className="h-8 w-8 p-0 ml-1 text-indigo-300 hover:text-amber-400 hover:bg-transparent"
                                            onClick={() => copyReferenceCode(selectedTopUp.referenceCode)}
                                          >
                                            <Copy className="h-3.5 w-3.5" />
                                          </Button>
                                        </p>
                                      </div>
                                    </div>
                                  </div>

                                  {/* รายการบัญชี */}
                                  <div className="space-y-4">
                                    <h3 className="flex items-center text-indigo-100 font-medium">
                                      <CreditCard className="h-4 w-4 mr-2 text-indigo-400" />
                                      บัญชีธนาคารทั้งหมด ({bankAccounts.length})
                                    </h3>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                      {bankAccounts.map((bank, index) => (
                                        <motion.div
                                          key={index}
                                          initial={{ opacity: 0, y: 10 }}
                                          animate={{ opacity: 1, y: 0 }}
                                          transition={{ duration: 0.3, delay: index * 0.1 }}
                                          className="rounded-lg bg-gradient-to-br from-indigo-900/70 to-purple-900/60 border border-indigo-700/40 p-4 relative overflow-hidden"
                                        >
                                          {/* เอฟเฟคพื้นหลัง */}
                                          <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-amber-600/10 to-amber-800/5 rounded-full filter blur-xl transform translate-x-10 -translate-y-10"></div>

                                          {/* หัวใบเสร็จ */}
                                          <div className="flex items-start mb-3">
                                            <div className="mr-3 h-8 w-8 rounded-md bg-gradient-to-br from-indigo-700 to-indigo-800 flex items-center justify-center flex-shrink-0">
                                              <CreditCard className="h-4 w-4 text-indigo-200" />
                                            </div>
                                            <div>
                                              <h4 className="font-medium text-indigo-100">{bank.name}</h4>
                                              <p className="text-xs text-indigo-400">ชำระเงินจำนวน <span className="text-amber-400 font-medium">{formatCurrency(selectedTopUp.amount)}</span></p>
                                            </div>
                                          </div>

                                          {/* ข้อมูลบัญชี */}
                                          <div className="space-y-2 pl-11">
                                            <div>
                                              <p className="text-xs text-indigo-400">ชื่อบัญชี</p>
                                              <p className="font-medium text-indigo-200">{bank.accountName}</p>
                                              <p className="text-xs text-indigo-400 italic">{bank.accountNameEn}</p>
                                            </div>
                                            <div>
                                              <p className="text-xs text-indigo-400">หมายเลขบัญชี</p>
                                              <p className="font-medium text-indigo-200 flex items-center">
                                                {bank.accountNumber}
                                                <Button
                                                  variant="ghost"
                                                  size="sm"
                                                  className="h-7 w-7 p-0 ml-1 text-indigo-300 hover:text-amber-400 hover:bg-transparent"
                                                  onClick={() => copyReferenceCode(bank.accountNumber)}
                                                >
                                                  <Copy className="h-3 w-3" />
                                                </Button>
                                              </p>
                                            </div>

                                            {/* QR Code ถ้ามี */}
                                            {bank.qrPaymentImage && (
                                              <div className="mt-3 flex justify-center">
                                                <div
                                                  className="relative p-2 bg-white/10 rounded-lg overflow-hidden w-32 h-32 cursor-pointer group"
                                                  onClick={() => handleZoomImage(bank.qrPaymentImage)}
                                                >
                                                  <div className="absolute inset-0 flex items-center justify-center bg-indigo-900/60 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg">
                                                    <ZoomIn className="text-white w-6 h-6" />
                                                  </div>
                                                  <img
                                                    src={bank.qrPaymentImage}
                                                    alt="QR Payment"
                                                    className="w-full h-full object-contain"
                                                  />
                                                </div>
                                              </div>
                                            )}
                                          </div>
                                        </motion.div>
                                      ))}
                                    </div>
                                  </div>
                                </div>

                                <Button
                                  type="button"
                                  onClick={() => openVerifyDialog(selectedTopUp)}
                                  className="w-full bg-gradient-to-r from-amber-600 to-amber-800 hover:from-amber-700 hover:to-amber-900"
                                >
                                  <Sparkles className="h-4 w-4 mr-2" />
                                  อัปโหลดสลิปและยืนยันการส่งพลัง
                                </Button>
                              </div>
                            </TabsContent>
                          </div>
                        ) : (
                          <Button
                            type="submit"
                            disabled={createTopUpMutation.isPending || !form.formState.isValid}
                            className="w-full bg-gradient-to-r from-amber-600 to-amber-800 hover:from-amber-700 hover:to-amber-900"
                          >
                            {createTopUpMutation.isPending ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                กำลังสร้างรายการ...
                              </>
                            ) : (
                              <>
                                <Sparkles className="mr-2 h-4 w-4" />
                                สร้างรายการเติมพลัง
                              </>
                            )}
                          </Button>
                        )}
                      </form>
                    </Form>
                  </Tabs>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </motion.div>

        {/* Transaction History */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="mt-8 relative z-10 container mx-auto py-6 max-w-6xl"
        >
          <div className="flex items-center mb-4">
            <div className="mr-3 relative">
              <div className="absolute -inset-1 rounded-full bg-indigo-600/30 animate-pulse blur-md"></div>
              <div className="relative bg-gradient-to-br from-indigo-700 to-indigo-900 h-8 w-8 rounded-full flex items-center justify-center">
                <Star className="h-4 w-4 text-indigo-300" />
              </div>
            </div>
            <h2 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-100 to-white">บันทึกการส่งพลังทิพย์</h2>
          </div>

          <Card className="backdrop-blur-md overflow-hidden bg-gradient-to-br from-indigo-950/80 to-purple-900/80 border-indigo-800/30 shadow-lg">
            <CardContent className="p-0">
              {isLoadingTransactions ? (
                <div className="p-6 text-center">
                  <div className="h-10 w-10 rounded-full border-4 border-indigo-600 border-t-transparent animate-spin mx-auto mb-2"></div>
                  <p className="text-indigo-300">กำลังเชื่อมต่อกับแดนสวรรค์...</p>
                </div>
              ) : !transactions || transactions.length === 0 ? (
                <div className="p-6 text-center text-indigo-300">
                  <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-indigo-900/50 mb-4">
                    <Sparkles className="h-8 w-8 text-indigo-400/70" />
                  </div>
                  <p>ยังไม่มีประวัติการส่งพลังทิพย์</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-indigo-800/30 bg-indigo-900/40">
                        <th className="px-4 py-3 text-left text-sm font-medium text-indigo-300">วันที่</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-indigo-300">รหัสอ้างอิง</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-indigo-300">จำนวนพลัง</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-indigo-300">สถานะ</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-indigo-300">การกระทำ</th>
                      </tr>
                    </thead>
                    <tbody>
                      {transactions.map((transaction) => (
                        <tr key={transaction.id} className="border-b border-indigo-800/30 hover:bg-indigo-900/30 transition-colors">
                          <td className="px-4 py-3 text-sm text-indigo-200">
                            {new Date(transaction.createdAt).toLocaleString("th-TH")}
                          </td>
                          <td className="px-4 py-3 text-sm font-medium text-indigo-100">{transaction.referenceCode}</td>
                          <td className="px-4 py-3 text-sm text-amber-400/90">{formatCurrency(transaction.amount)}</td>
                          <td className="px-4 py-3 text-sm">
                            {transaction.status === "completed" ? (
                              <Badge className="bg-green-800/70 text-green-100 hover:bg-green-800/70">เสร็จสมบูรณ์</Badge>
                            ) : transaction.status === "pending" ? (
                              <Badge className="bg-amber-800/70 text-amber-100 hover:bg-amber-800/70">รอการยืนยัน</Badge>
                            ) : (
                              <Badge className="bg-red-800/70 text-red-100 hover:bg-red-800/70">ล้มเหลว</Badge>
                            )}
                          </td>
                          <td className="px-4 py-3 text-sm">
                            {transaction.status === "pending" && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => openVerifyDialog(transaction)}
                                className="border-indigo-600/40 bg-indigo-900/30 text-indigo-300 hover:bg-indigo-800/40 hover:text-indigo-100"
                              >
                                <Sparkles className="h-3.5 w-3.5 mr-1" />
                                ยืนยันการส่งพลัง
                              </Button>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Verify Dialog */}
      <Dialog open={isVerifyDialogOpen} onOpenChange={setIsVerifyDialogOpen}>
        <DialogContent className="bg-gradient-to-br from-indigo-950 to-purple-950 border-indigo-800/30 text-indigo-100">
          <DialogHeader>
            <DialogTitle className="text-center text-xl bg-clip-text text-transparent bg-gradient-to-r from-amber-300 to-amber-500">
              ยืนยันการส่งพลังทิพย์
            </DialogTitle>
            <DialogDescription className="text-center text-indigo-300">
              อัปโหลดหลักฐานการส่งพลังเพื่อยืนยันความสำเร็จ
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {selectedTopUp && (
              <div className="grid grid-cols-2 gap-2 text-sm bg-indigo-900/30 p-3 rounded-lg">
                <div className="text-indigo-400">รหัสอ้างอิง:</div>
                <div className="text-amber-300 font-semibold">{selectedTopUp.referenceCode}</div>
                <div className="text-indigo-400">จำนวนพลัง:</div>
                <div className="text-amber-300 font-semibold">{formatCurrency(selectedTopUp.amount)}</div>
              </div>
            )}

            <div className="border-2 border-dashed border-indigo-800/40 rounded-md p-6 text-center bg-indigo-900/20">
              <input
                type="file"
                id="slip-upload"
                className="hidden"
                accept="image/*"
                onChange={handleFileChange}
              />
              {!selectedFile ? (
                <label
                  htmlFor="slip-upload"
                  className="flex flex-col items-center cursor-pointer"
                >
                  <div className="h-16 w-16 rounded-full bg-indigo-900/50 flex items-center justify-center mb-3">
                    <UploadCloud className="h-8 w-8 text-indigo-300" />
                  </div>
                  <span className="text-sm font-medium text-indigo-200">คลิกเพื่ออัปโหลดหลักฐาน</span>
                  <span className="text-xs text-indigo-400 mt-1">PNG, JPG หรือ JPEG (สูงสุด 5MB)</span>
                </label>
              ) : (
                <div className="space-y-2">
                  <div className="flex items-center justify-center text-sm">
                    <div className="h-10 w-10 rounded-full bg-green-900/30 flex items-center justify-center mr-2">
                      <Check className="h-5 w-5 text-green-400" />
                    </div>
                    <span className="text-indigo-100">{selectedFile.name}</span>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedFile(null)}
                      className="border-indigo-700/40 text-indigo-300 hover:bg-indigo-800/30"
                    >
                      เปลี่ยนไฟล์
                    </Button>
                  </div>
                </div>
              )}
            </div>

            {verifyTopUpMutation.isPending && (
              <div className="space-y-2">
                <Progress
                  value={uploadProgress}
                  className="h-2 bg-indigo-900/50"
                />
                <p className="text-sm text-center text-indigo-300">กำลังตรวจสอบหลักฐาน...</p>
              </div>
            )}

            {verifyTopUpMutation.isError && (
              <Alert variant="destructive" className="bg-red-900/30 border-red-800 text-red-200">
                <AlertCircle className="h-4 w-4 text-red-400" />
                <AlertTitle>เกิดข้อผิดพลาด</AlertTitle>
                <AlertDescription className="text-red-300">
                  {verifyTopUpMutation.error?.message || "ไม่สามารถตรวจสอบหลักฐานได้ กรุณาลองใหม่อีกครั้ง"}
                </AlertDescription>
              </Alert>
            )}

            <div className="flex justify-end gap-2 pt-2">
              <Button
                variant="outline"
                onClick={() => setIsVerifyDialogOpen(false)}
                disabled={verifyTopUpMutation.isPending}
                className="border-indigo-700/40 text-indigo-300 hover:bg-indigo-800/30"
              >
                ยกเลิก
              </Button>
              <Button
                onClick={handleVerify}
                disabled={!selectedFile || verifyTopUpMutation.isPending}
                className="bg-gradient-to-r from-amber-600 to-amber-800 hover:from-amber-700 hover:to-amber-900"
              >
                {verifyTopUpMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    กำลังดำเนินการ...
                  </>
                ) : (
                  <>
                    <Sparkles className="mr-2 h-4 w-4" />
                    ยืนยันการชำระเงิน
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* QR Code Zoom Dialog */}
      <Dialog open={!!zoomImageUrl} onOpenChange={(open) => !open && setZoomImageUrl(null)}>
        <DialogContent className="bg-gradient-to-br from-indigo-950 to-purple-950 border-indigo-800/30 max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center text-xl bg-clip-text text-transparent bg-gradient-to-r from-amber-300 to-amber-500">
              QR Code ชำระเงิน
            </DialogTitle>
          </DialogHeader>

          <div className="flex justify-center">
            {zoomImageUrl && (
              <div className="p-4 bg-white rounded-lg w-full max-w-xs max-h-80 flex items-center justify-center">
                <img
                  src={zoomImageUrl}
                  alt="QR Payment"
                  className="max-w-full max-h-full object-contain"
                />
              </div>
            )}
          </div>

          <div className="flex justify-center mt-2">
            <Button onClick={() => setZoomImageUrl(null)} className="bg-indigo-800 hover:bg-indigo-700">
              ปิด
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </DashboardLayout>
  );
}