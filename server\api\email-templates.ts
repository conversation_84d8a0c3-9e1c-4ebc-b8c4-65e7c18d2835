/**
 * API endpoints สำหรับจัดการเทมเพลตอีเมล
 * เฉพาะ Admin เท่านั้นที่มีสิทธิ์เข้าถึง
 */
import { Router, Request, Response } from 'express';
import { db } from '../db';
import { isAdmin } from '../auth';
import { emailTemplates, insertEmailTemplateSchema } from '@shared/schema';
import { eq, and } from 'drizzle-orm';
import { emailService } from '../email-service';

const router = Router();

// =============== ดึงข้อมูลเทมเพลตอีเมลทั้งหมด ===============
router.get('/admin/email-templates', async (req: Request, res: Response) => {
  try {
    // ตรวจสอบสิทธิ์ Admin
    if (!isAdmin(req)) {
      return res.status(403).json({
        message: 'ไม่มีสิทธิ์เข้าถึงข้อมูลนี้ เฉพาะผู้ดูแลระบบเท่านั้นที่สามารถจัดการเทมเพลตอีเมลได้'
      });
    }

    // ดึงข้อมูลทั้งหมด
    const templates = await db.select().from(emailTemplates);
    
    return res.status(200).json(templates);
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการดึงข้อมูลเทมเพลตอีเมล:', error);
    return res.status(500).json({
      message: 'เกิดข้อผิดพลาดในการดึงข้อมูลเทมเพลตอีเมล'
    });
  }
});

// =============== ดึงข้อมูลเทมเพลตอีเมลตาม ID ===============
router.get('/admin/email-templates/:id', async (req: Request, res: Response) => {
  try {
    // ตรวจสอบสิทธิ์ Admin
    if (!isAdmin(req)) {
      return res.status(403).json({
        message: 'ไม่มีสิทธิ์เข้าถึงข้อมูลนี้ เฉพาะผู้ดูแลระบบเท่านั้นที่สามารถจัดการเทมเพลตอีเมลได้'
      });
    }

    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({
        message: 'รูปแบบ ID ไม่ถูกต้อง'
      });
    }

    // ดึงข้อมูลตาม ID
    const [template] = await db.select()
      .from(emailTemplates)
      .where(eq(emailTemplates.id, id));
    
    if (!template) {
      return res.status(404).json({
        message: 'ไม่พบเทมเพลตอีเมลที่ต้องการ'
      });
    }
    
    return res.status(200).json(template);
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการดึงข้อมูลเทมเพลตอีเมล:', error);
    return res.status(500).json({
      message: 'เกิดข้อผิดพลาดในการดึงข้อมูลเทมเพลตอีเมล'
    });
  }
});

// =============== สร้างเทมเพลตอีเมลใหม่ ===============
router.post('/admin/email-templates', async (req: Request, res: Response) => {
  try {
    // ตรวจสอบสิทธิ์ Admin
    if (!isAdmin(req)) {
      return res.status(403).json({
        message: 'ไม่มีสิทธิ์เข้าถึงข้อมูลนี้ เฉพาะผู้ดูแลระบบเท่านั้นที่สามารถจัดการเทมเพลตอีเมลได้'
      });
    }

    // ตรวจสอบข้อมูลที่ส่งมา
    const { name, subject, htmlContent, textContent, variables, isDefault } = req.body;

    if (!name || !subject || !htmlContent) {
      return res.status(400).json({
        message: 'กรุณากรอกข้อมูลให้ครบถ้วน (ชื่อเทมเพลต, หัวข้อ, และเนื้อหา HTML)'
      });
    }

    // ตรวจสอบว่าชื่อเทมเพลตซ้ำหรือไม่
    const [existingTemplate] = await db.select()
      .from(emailTemplates)
      .where(eq(emailTemplates.name, name));
    
    if (existingTemplate) {
      return res.status(400).json({
        message: 'ชื่อเทมเพลตนี้มีอยู่ในระบบแล้ว กรุณาใช้ชื่ออื่น'
      });
    }

    // ถ้าตั้งค่าเป็นเทมเพลตเริ่มต้น ต้องอัปเดตเทมเพลตอื่นให้ไม่เป็นค่าเริ่มต้น
    if (isDefault) {
      await db.update(emailTemplates)
        .set({ isDefault: false })
        .where(eq(emailTemplates.name, name));
    }

    // สร้างเทมเพลตใหม่
    const newTemplate = {
      name,
      subject,
      htmlContent,
      textContent: textContent || '',
      variables: Array.isArray(variables) ? variables : [],
      isDefault: isDefault || false,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const [createdTemplate] = await db.insert(emailTemplates)
      .values(newTemplate)
      .returning();
    
    return res.status(201).json(createdTemplate);
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการสร้างเทมเพลตอีเมล:', error);
    return res.status(500).json({
      message: 'เกิดข้อผิดพลาดในการสร้างเทมเพลตอีเมล'
    });
  }
});

// =============== อัปเดตเทมเพลตอีเมล ===============
router.put('/admin/email-templates/:id', async (req: Request, res: Response) => {
  try {
    // ตรวจสอบสิทธิ์ Admin
    if (!isAdmin(req)) {
      return res.status(403).json({
        message: 'ไม่มีสิทธิ์เข้าถึงข้อมูลนี้ เฉพาะผู้ดูแลระบบเท่านั้นที่สามารถจัดการเทมเพลตอีเมลได้'
      });
    }

    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({
        message: 'รูปแบบ ID ไม่ถูกต้อง'
      });
    }

    // ตรวจสอบว่าเทมเพลตที่ต้องการอัปเดตมีอยู่จริงหรือไม่
    const [existingTemplate] = await db.select()
      .from(emailTemplates)
      .where(eq(emailTemplates.id, id));
    
    if (!existingTemplate) {
      return res.status(404).json({
        message: 'ไม่พบเทมเพลตอีเมลที่ต้องการอัปเดต'
      });
    }

    // ตรวจสอบข้อมูลที่ส่งมา
    const { name, subject, htmlContent, textContent, variables, isDefault } = req.body;

    if (!name || !subject || !htmlContent) {
      return res.status(400).json({
        message: 'กรุณากรอกข้อมูลให้ครบถ้วน (ชื่อเทมเพลต, หัวข้อ, และเนื้อหา HTML)'
      });
    }

    // ตรวจสอบว่าชื่อเทมเพลตซ้ำหรือไม่ (ยกเว้นตัวเอง)
    const [duplicateTemplate] = await db.select()
      .from(emailTemplates)
      .where(and(
        eq(emailTemplates.name, name),
        eq(emailTemplates.id, id, true) // not equal
      ));
    
    if (duplicateTemplate) {
      return res.status(400).json({
        message: 'ชื่อเทมเพลตนี้มีอยู่ในระบบแล้ว กรุณาใช้ชื่ออื่น'
      });
    }

    // ถ้าตั้งค่าเป็นเทมเพลตเริ่มต้น ต้องอัปเดตเทมเพลตอื่นให้ไม่เป็นค่าเริ่มต้น
    if (isDefault) {
      await db.update(emailTemplates)
        .set({ isDefault: false });
    }

    // อัปเดตข้อมูล
    const [updatedTemplate] = await db.update(emailTemplates)
      .set({
        name,
        subject,
        htmlContent,
        textContent: textContent || '',
        variables: Array.isArray(variables) ? variables : [],
        isDefault: isDefault || false,
        updatedAt: new Date()
      })
      .where(eq(emailTemplates.id, id))
      .returning();
    
    return res.status(200).json(updatedTemplate);
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการอัปเดตเทมเพลตอีเมล:', error);
    return res.status(500).json({
      message: 'เกิดข้อผิดพลาดในการอัปเดตเทมเพลตอีเมล'
    });
  }
});

// =============== ลบเทมเพลตอีเมล ===============
router.delete('/admin/email-templates/:id', async (req: Request, res: Response) => {
  try {
    // ตรวจสอบสิทธิ์ Admin
    if (!isAdmin(req)) {
      return res.status(403).json({
        message: 'ไม่มีสิทธิ์เข้าถึงข้อมูลนี้ เฉพาะผู้ดูแลระบบเท่านั้นที่สามารถจัดการเทมเพลตอีเมลได้'
      });
    }

    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({
        message: 'รูปแบบ ID ไม่ถูกต้อง'
      });
    }

    // ตรวจสอบว่าเทมเพลตที่ต้องการลบมีอยู่จริงหรือไม่
    const [templateToDelete] = await db.select()
      .from(emailTemplates)
      .where(eq(emailTemplates.id, id));
    
    if (!templateToDelete) {
      return res.status(404).json({
        message: 'ไม่พบเทมเพลตอีเมลที่ต้องการลบ'
      });
    }

    // ไม่อนุญาตให้ลบเทมเพลตเริ่มต้น
    if (templateToDelete.isDefault) {
      return res.status(400).json({
        message: 'ไม่สามารถลบเทมเพลตเริ่มต้นได้ กรุณาตั้งค่าเทมเพลตอื่นเป็นค่าเริ่มต้นก่อนลบ'
      });
    }

    // ลบเทมเพลต
    await db.delete(emailTemplates)
      .where(eq(emailTemplates.id, id));
    
    return res.status(200).json({
      message: 'ลบเทมเพลตอีเมลเรียบร้อยแล้ว'
    });
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการลบเทมเพลตอีเมล:', error);
    return res.status(500).json({
      message: 'เกิดข้อผิดพลาดในการลบเทมเพลตอีเมล'
    });
  }
});

// =============== ส่งอีเมลทดสอบ ===============
router.post('/admin/send-test-email', async (req: Request, res: Response) => {
  try {
    // ตรวจสอบสิทธิ์ Admin
    if (!isAdmin(req)) {
      return res.status(403).json({
        message: 'ไม่มีสิทธิ์เข้าถึงข้อมูลนี้ เฉพาะผู้ดูแลระบบเท่านั้นที่สามารถส่งอีเมลทดสอบได้'
      });
    }

    const { to, templateId, variables } = req.body;

    if (!to || !templateId) {
      return res.status(400).json({
        message: 'กรุณาระบุอีเมลผู้รับและรหัสเทมเพลตที่ต้องการทดสอบ'
      });
    }

    // ตรวจสอบว่าเทมเพลตที่ต้องการทดสอบมีอยู่จริงหรือไม่
    const [template] = await db.select()
      .from(emailTemplates)
      .where(eq(emailTemplates.id, templateId));
    
    if (!template) {
      return res.status(404).json({
        message: 'ไม่พบเทมเพลตอีเมลที่ต้องการทดสอบ'
      });
    }

    // ส่งอีเมลทดสอบ
    const userId = req.user!.id;
    const success = await emailService.sendTemplateEmail(
      to,
      template.name,
      variables || {},
      userId
    );

    if (success) {
      return res.status(200).json({
        message: 'ส่งอีเมลทดสอบเรียบร้อยแล้ว'
      });
    } else {
      return res.status(500).json({
        message: 'ไม่สามารถส่งอีเมลทดสอบได้ โปรดตรวจสอบการตั้งค่าอีเมล'
      });
    }
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการส่งอีเมลทดสอบ:', error);
    return res.status(500).json({
      message: 'เกิดข้อผิดพลาดในการส่งอีเมลทดสอบ'
    });
  }
});

export default router;