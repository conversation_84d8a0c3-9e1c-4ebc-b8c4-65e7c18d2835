import { Router } from 'express';
import * as emailSettingsController from '../email-settings';
import { isAdmin } from '../../auth';

const router = Router();

// การตั้งค่าอีเมล (สำหรับผู้ดูแลระบบเท่านั้น)
router.get('/settings', isAdmin, emailSettingsController.getEmailSettings);
router.put('/settings', isAdmin, emailSettingsController.updateEmailSettings);
router.post('/settings/test', isAdmin, emailSettingsController.sendTestEmail);

// เทมเพลตอีเมล
router.get('/templates', isAdmin, emailSettingsController.getEmailTemplates);
router.get('/templates/:id', isAdmin, emailSettingsController.getEmailTemplateById);
router.put('/templates/:id', isAdmin, emailSettingsController.updateEmailTemplate);
router.post('/templates', isAdmin, emailSettingsController.createEmailTemplate);

// ประวัติการส่งอีเมล
router.get('/logs', isAdmin, emailSettingsController.getEmailLogs);

export default router;