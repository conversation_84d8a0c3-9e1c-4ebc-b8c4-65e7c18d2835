import { Request, Response, NextFunction } from "express";

// ตรวจสอบว่าผู้ใช้ล็อกอินหรือไม่
export function isAuthenticated(req: Request, res: Response, next: NextFunction) {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ error: "กรุณาล็อกอินเพื่อเข้าถึงข้อมูลนี้" });
  }
  next();
}

// ตรวจสอบว่าผู้ใช้เป็นแอดมินหรือไม่
export function isAdmin(req: Request, res: Response, next: NextFunction) {
  if (!req.user || req.user.role !== "admin") {
    return res.status(403).json({ error: "คุณไม่มีสิทธิ์เข้าถึงข้อมูลนี้ กรุณาล็อกอินด้วยบัญชีแอดมิน" });
  }
  next();
}

// ตรวจสอบว่าผู้ใช้เป็นเจ้าของข้อมูลหรือไม่
export function isOwner(propertyName: string) {
  return (req: Request, res: Response, next: NextFunction) => {
    const resourceId = parseInt(req.params[propertyName]);
    const userId = req.user?.id;
    
    if (req.user?.role === "admin") {
      // แอดมินสามารถเข้าถึงข้อมูลได้ทั้งหมด
      return next();
    }
    
    if (!userId || isNaN(resourceId)) {
      return res.status(400).json({ error: "ข้อมูลไม่ถูกต้อง" });
    }
    
    // ตรวจสอบว่าเป็นเจ้าของหรือไม่
    if (req.params[propertyName] && req.body.userId && req.body.userId !== userId) {
      return res.status(403).json({ error: "คุณไม่มีสิทธิ์เข้าถึงหรือแก้ไขข้อมูลนี้" });
    }
    
    next();
  };
}