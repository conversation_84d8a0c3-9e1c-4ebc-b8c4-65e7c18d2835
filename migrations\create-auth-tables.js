import { Pool } from '@neondatabase/serverless';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

dotenv.config();

const DATABASE_URL = process.env.DATABASE_URL;

if (!DATABASE_URL) {
  throw new Error('DATABASE_URL environment variable is not set');
}

const pool = new Pool({ connectionString: DATABASE_URL });

async function createAuthProviderEnum() {
  try {
    const result = await pool.query(`
      SELECT EXISTS (
        SELECT 1 FROM pg_type 
        WHERE typname = 'auth_provider'
      );
    `);

    if (!result.rows[0].exists) {
      console.log('Creating auth_provider enum...');
      await pool.query(`
        CREATE TYPE auth_provider AS ENUM (
          'line', 'facebook', 'google', 'apple', 'phone'
        );
      `);
      console.log('auth_provider enum created successfully');
    } else {
      console.log('auth_provider enum already exists');
    }
  } catch (error) {
    console.error('Error creating auth_provider enum:', error);
    throw error;
  }
}

async function createVerificationTypeEnum() {
  try {
    const result = await pool.query(`
      SELECT EXISTS (
        SELECT 1 FROM pg_type 
        WHERE typname = 'verification_type'
      );
    `);

    if (!result.rows[0].exists) {
      console.log('Creating verification_type enum...');
      await pool.query(`
        CREATE TYPE verification_type AS ENUM (
          'email', 'phone', 'password_reset', 'two_factor', 'account_deletion'
        );
      `);
      console.log('verification_type enum created successfully');
    } else {
      console.log('verification_type enum already exists');
    }
  } catch (error) {
    console.error('Error creating verification_type enum:', error);
    throw error;
  }
}

async function createExternalAuthTable() {
  try {
    const result = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'external_auth'
      );
    `);

    if (!result.rows[0].exists) {
      console.log('Creating external_auth table...');
      await pool.query(`
        CREATE TABLE external_auth (
          id SERIAL PRIMARY KEY,
          user_id INTEGER NOT NULL REFERENCES users(id),
          provider auth_provider NOT NULL,
          external_id TEXT NOT NULL,
          display_name TEXT,
          profile_image TEXT,
          email TEXT,
          access_token TEXT,
          refresh_token TEXT,
          token_expiry TIMESTAMP,
          metadata JSONB,
          last_login TIMESTAMP,
          created_at TIMESTAMP NOT NULL DEFAULT NOW(),
          updated_at TIMESTAMP NOT NULL DEFAULT NOW()
        );
      `);
      console.log('external_auth table created successfully');
    } else {
      console.log('external_auth table already exists');
    }
  } catch (error) {
    console.error('Error creating external_auth table:', error);
    throw error;
  }
}

async function createVerificationCodesTable() {
  try {
    const result = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'verification_codes'
      );
    `);

    if (!result.rows[0].exists) {
      console.log('Creating verification_codes table...');
      await pool.query(`
        CREATE TABLE verification_codes (
          id SERIAL PRIMARY KEY,
          user_id INTEGER REFERENCES users(id),
          type verification_type NOT NULL,
          code TEXT NOT NULL,
          identifier TEXT NOT NULL,
          is_used BOOLEAN NOT NULL DEFAULT FALSE,
          expires_at TIMESTAMP NOT NULL,
          attempts INTEGER NOT NULL DEFAULT 0,
          metadata JSONB,
          created_at TIMESTAMP NOT NULL DEFAULT NOW(),
          used_at TIMESTAMP
        );
      `);
      console.log('verification_codes table created successfully');
    } else {
      console.log('verification_codes table already exists');
    }
  } catch (error) {
    console.error('Error creating verification_codes table:', error);
    throw error;
  }
}

async function updateUsersTable() {
  try {
    console.log('Checking if phone_verified column exists in users table...');
    const result = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'phone_verified'
      );
    `);

    if (!result.rows[0].exists) {
      console.log('Adding phone_verified column to users table...');
      await pool.query(`
        ALTER TABLE users 
        ADD COLUMN phone_verified BOOLEAN DEFAULT FALSE,
        ADD COLUMN email_verified BOOLEAN DEFAULT FALSE,
        ADD COLUMN auth_providers TEXT[] DEFAULT ARRAY[]::TEXT[]
      `);
      console.log('User table updated successfully');
    } else {
      console.log('phone_verified column already exists in users table');
    }
  } catch (error) {
    console.error('Error updating users table:', error);
    throw error;
  }
}

async function main() {
  try {
    await createAuthProviderEnum();
    await createVerificationTypeEnum();
    await createExternalAuthTable();
    await createVerificationCodesTable();
    await updateUsersTable();
    
    console.log('All migrations completed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    await pool.end();
  }
}

main().catch(console.error);