import { Router } from 'express';
import * as notificationsController from './notifications';

const router = Router();

// การแจ้งเตือนของผู้ใช้
router.get('/', notificationsController.getUserNotifications);
router.put('/:id/read', notificationsController.markNotificationAsRead);
router.put('/read-all', notificationsController.markAllNotificationsAsRead);
router.delete('/:id', notificationsController.deleteNotification);

// การตั้งค่าการแจ้งเตือน
router.get('/settings', notificationsController.getAlertSettings);
router.put('/settings/:id', notificationsController.updateAlertSetting);

// สำหรับผู้ดูแลระบบ
router.post('/admin/create', notificationsController.createNotification);

export default router;