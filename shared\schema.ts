import { pgTable, text, serial, integer, boolean, timestamp, pgEnum, uniqueIndex, foreignKey, date, jsonb, doublePrecision, varchar, index } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";
import { relations } from "drizzle-orm";

// ================ ENUMS ================
// ตัวแปรกำหนดสถานะของผู้ใช้
export const userRoleEnum = pgEnum('user_role', ['user', 'admin']);
export const userStatusEnum = pgEnum('user_status', ['active', 'inactive', 'suspended']);

// สถานะของ API key
export const apiKeyStatusEnum = pgEnum('api_key_status', ['active', 'inactive', 'revoked']);

// ประเภทของ API request
export const apiRequestTypeEnum = pgEnum('api_request_type', ['verify_slip', 'get_usage', 'get_history']);

// สถานะของ API response
export const apiResponseStatusEnum = pgEnum('api_response_status', ['success', 'error', 'invalid_request', 'quota_exceeded', 'unauthorized']);

// ประเภทของการแจ้งเตือน
export const alertTypeEnum = pgEnum('alert_type', [
  'unusual_transaction',         // ธุรกรรมผิดปกติ
  'quota_low',                  // โควต้าเหลือน้อย
  'api_key_expiring',           // API Key ใกล้หมดอายุ
  'fraud_detection',            // ตรวจพบการฉ้อโกง
  'daily_report',               // รายงานประจำวัน
  'weekly_report',              // รายงานประจำสัปดาห์
  'monthly_report',             // รายงานประจำเดือน
  'system_update',              // อัพเดทระบบ
  'package_recommendation'      // คำแนะนำแพ็คเกจที่เหมาะสม
]);

// ระดับของลูกค้า
export const customerTierEnum = pgEnum('customer_tier', [
  'standard',                   // ลูกค้าทั่วไป
  'premium',                    // ลูกค้าระดับพรีเมียม
  'vip',                        // ลูกค้า VIP
  'enterprise'                  // ลูกค้าองค์กร
]);

// ช่องทางการแจ้งเตือน
export const alertChannelEnum = pgEnum('alert_channel', ['email', 'in_app', 'line', 'sms']);

// สถานะการแจ้งเตือน
export const alertStatusEnum = pgEnum('alert_status', ['pending', 'sent', 'failed', 'read']);

// ระดับความสำคัญของการแจ้งเตือน
export const alertPriorityEnum = pgEnum('alert_priority', ['low', 'medium', 'high', 'critical']);

// ประเภทของการเชื่อมโยงภายนอก (External Auth)
export const authProviderEnum = pgEnum('auth_provider', [
  'line',                       // LINE Login
  'facebook',                   // Facebook Login
  'google',                     // Google Login
  'apple',                      // Apple Login
  'phone',                      // เบอร์โทรศัพท์
]);

// ประเภทของรหัสยืนยัน
export const verificationTypeEnum = pgEnum('verification_type', [
  'email',                      // ยืนยันอีเมล
  'phone',                      // ยืนยันเบอร์โทรศัพท์ (OTP)
  'password_reset',             // รีเซ็ตรหัสผ่าน
  'two_factor',                 // การยืนยันตัวตนสองขั้นตอน (2FA)
  'account_deletion'            // ยืนยันการลบบัญชี
]);

// ช่วงเวลาของรายงาน
export const reportFrequencyEnum = pgEnum('report_frequency', ['daily', 'weekly', 'monthly']);

// ประเภทของ achievements
export const achievementTypeEnum = pgEnum('achievement_type', [
  'first_time',          // ทำครั้งแรก
  'milestone',           // ถึงเป้าหมายสำคัญ
  'streak',              // ทำต่อเนื่อง
  'usage',               // การใช้งาน
  'package',             // เกี่ยวกับแพ็กเกจ
  'special',             // กิจกรรมพิเศษ
  'progress'             // ความก้าวหน้า
]);

// ประเภทของ webhook event
export const webhookEventTypeEnum = pgEnum('webhook_event_type', [
  'slip_verification', // การตรวจสอบสลิป
  'quota_low',        // โควต้าเหลือน้อย
  'credit_low',       // เครดิตเหลือน้อย
  'api_key_expire',   // API key ใกล้หมดอายุ
  'package_expire',   // แพ็คเกจใกล้หมดอายุ
  'fraud_detected',   // ตรวจพบการทุจริต
  'system_update'     // การอัพเดทระบบ
]);

// ประเภทวิธีการล็อกอิน
export const authMethodEnum = pgEnum('auth_method', [
  'username_password', // ล็อกอินด้วยชื่อผู้ใช้และรหัสผ่าน
  'line',              // ล็อกอินด้วย LINE
  'facebook',          // ล็อกอินด้วย Facebook
  'google',            // ล็อกอินด้วย Google
  'phone'              // ล็อกอินด้วยเบอร์โทรศัพท์
]);

// ================ TABLES ================
// ตาราง users - ข้อมูลผู้ใช้งาน
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  email: text("email").notNull().unique(),
  firstName: text("first_name"),
  lastName: text("last_name"),
  companyName: text("company_name"),
  phoneNumber: text("phone_number"),
  address: text("address"),
  bio: text("bio"),
  profileImage: text("profile_image"),
  credit: doublePrecision("credit").default(0).notNull(),
  tier: customerTierEnum("tier").default('standard').notNull(), // ระดับของลูกค้า
  role: userRoleEnum("role").default('user').notNull(),
  status: userStatusEnum("status").default('active').notNull(),
  allowedPackages: integer("allowed_packages").array(), // แพ็กเกจที่ผู้ใช้ได้รับอนุญาตให้สมัคร (null = ไม่มีข้อจำกัด)
  email_verified: boolean("email_verified").default(false), // เพิ่มการยืนยันอีเมล
  phone_verified: boolean("phone_verified").default(false), // เพิ่มการยืนยันเบอร์โทรศัพท์
  auth_providers: text("auth_providers").array(), // เก็บรายการผู้ให้บริการที่ใช้
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// ตาราง packages - แพ็กเกจการใช้งาน
export const packages = pgTable("packages", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description").notNull(),
  price: integer("price").notNull(), // ราคาต่อเดือน
  discount3Months: integer("discount_3_months").default(0), // ส่วนลดเมื่อใช้ 3 เดือน (%)
  discount6Months: integer("discount_6_months").default(0), // ส่วนลดเมื่อใช้ 6 เดือน (%)
  discount12Months: integer("discount_12_months").default(0), // ส่วนลดเมื่อใช้ 12 เดือน (%)
  durationDays: integer("duration_days").default(30).notNull(), // ระยะเวลาใช้งาน (วัน) สำหรับแพคเกจ 1 เดือน
  requestsLimit: integer("requests_limit").notNull(),
  creditPerVerification: doublePrecision("credit_per_verification"), // ค่าเครดิตที่จะเสียต่อการตรวจสอบสลิป 1 ครั้ง (เมื่อโควต้าหมด)
  isActive: boolean("is_active").default(true).notNull(),
  features: text("features").array(),
  tag: text("tag"), // ป้ายกำกับแพคเกจ เช่น "ทดลองใช้", "แนะนำ", "ยอดนิยม"
  sortOrder: integer("sort_order").default(0), // ลำดับการเรียงแพ็กเกจ
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// ตาราง user_packages - แพ็กเกจที่ผู้ใช้งานสมัคร
export const userPackages = pgTable("user_packages", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  packageId: integer("package_id").references(() => packages.id).notNull(),
  startDate: date("start_date").notNull(),
  endDate: date("end_date").notNull(),
  isActive: boolean("is_active").default(true).notNull(),
  requestsUsed: integer("requests_used").default(0).notNull(),
  lastQuotaResetDate: date("last_quota_reset_date"), // วันที่ทำการรีเซ็ตโควต้าครั้งล่าสุด
  durationMonths: integer("duration_months").default(1).notNull(), // ระยะเวลาในการสมัครแพ็กเกจ (1, 3, 6, 12 เดือน)
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// ตาราง slip_verifications - ประวัติการตรวจสอบสลิป
export const slipVerifications = pgTable("slip_verifications", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  transactionRef: text("transaction_ref"),
  bankName: text("bank_name"),
  amount: doublePrecision("amount"),
  sender: text("sender"),
  receiver: text("receiver"),
  transactionDate: timestamp("transaction_date"),
  status: text("status").notNull().default("pending"),
  responseData: text("response_data"),
  creditUsed: doublePrecision("credit_used"), // จำนวนเครดิตที่ใช้ (ในกรณีที่ใช้เครดิตแทนโควต้า)
  usedCredit: boolean("used_credit").default(false), // เก็บข้อมูลว่าการตรวจสอบนี้ใช้เครดิตหรือโควต้า
  verificationSource: text("verification_source").default("web"), // ที่มาของการตรวจสอบ: "web", "api", หรืออื่นๆ
  apiKeyId: integer("api_key_id").references(() => apiKeys.id), // ID ของ API key ที่ใช้ (ถ้าเป็นการตรวจสอบผ่าน API)
  imagePath: text("image_path"), // พาธของไฟล์ภาพสลิป
  qrData: text("qr_data"), // ข้อมูลที่อ่านได้จาก QR Code (ใช้ตรวจสอบสลิปซ้ำ)
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// ตาราง system_settings - ตั้งค่าระบบ
export const systemSettings = pgTable("system_settings", {
  id: serial("id").primaryKey(),
  key: text("key").notNull().unique(),
  value: text("value"),
  valueJson: jsonb("value_json"),
  description: text("description"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// ตาราง coupons - คูปองส่วนลด
export const coupons = pgTable("coupons", {
  id: serial("id").primaryKey(),
  code: text("code").notNull().unique(),
  discountPercent: integer("discount_percent").default(0), // ส่วนลด %
  discountAmount: integer("discount_amount").default(0), // ส่วนลดเป็นจำนวนเงิน
  maxUsage: integer("max_usage").default(1).notNull(), // จำนวนครั้งที่ใช้ได้
  usageCount: integer("usage_count").default(0).notNull(), // จำนวนครั้งที่ถูกใช้ไปแล้ว
  startDate: timestamp("start_date").notNull(), // วันที่เริ่มใช้งานได้
  endDate: timestamp("end_date").notNull(), // วันที่หมดอายุ
  isActive: boolean("is_active").default(true).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// ตาราง top_up_transactions - ประวัติการเติมเงิน
export const topUpTransactions = pgTable("top_up_transactions", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  amount: doublePrecision("amount").notNull(),
  status: text("status").default("pending").notNull(),
  verificationId: integer("verification_id"),
  referenceCode: text("reference_code"),
  // หมายเหตุ: คอลัมน์ payment_method ไม่มีในฐานข้อมูลจริง จึงถูกนำออกจากโค้ด
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// ตาราง api_keys - API keys สำหรับลูกค้า
export const apiKeys = pgTable("api_keys", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  apiKey: varchar("api_key", { length: 64 }).notNull().unique(),
  name: text("name").notNull(), // ชื่อสำหรับการแสดงผล เช่น "Production API Key", "Test API Key"
  description: text("description"),
  status: apiKeyStatusEnum("status").default('active').notNull(),
  lastUsed: timestamp("last_used"),
  requestCount: integer("request_count").default(0).notNull(),
  ipWhitelist: text("ip_whitelist").array(), // รายการ IP ที่อนุญาตให้ใช้ API key นี้
  expiresAt: timestamp("expires_at"), // วันหมดอายุของ API key (ถ้ามี)
  limitEnabled: boolean("limit_enabled").default(false), // เปิดใช้งานการจำกัดการใช้งานหรือไม่
  usageLimit: integer("usage_limit"), // จำนวนการใช้งานสูงสุดที่อนุญาต (ถ้าเปิดใช้งานการจำกัด)
  duplicateSlipCheck: boolean("duplicate_slip_check").default(true).notNull(), // เปิดใช้การตรวจสอบสลิปซ้ำสำหรับ API key นี้
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// ตาราง user_settings - การตั้งค่าส่วนตัวของผู้ใช้
export const userSettings = pgTable("user_settings", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull().unique(),
  duplicateSlipCheck: boolean("duplicate_slip_check").default(true).notNull(), // เปิดใช้การตรวจสอบสลิปซ้ำ
  emailNotifications: boolean("email_notifications").default(true), // เปิดใช้การแจ้งเตือนทางอีเมล
  theme: text("theme").default("dark"), // ธีมของผู้ใช้ (dark, light)
  language: text("language").default("th"), // ภาษาที่ใช้ในระบบ
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// ความสัมพันธ์ระหว่างตาราง userSettings กับตาราง users
export const userSettingsRelations = relations(userSettings, ({ one }) => ({
  user: one(users, {
    fields: [userSettings.userId],
    references: [users.id],
  }),
}));

// สร้าง schema สำหรับการแทรกข้อมูลตั้งค่าผู้ใช้
export const insertUserSettingSchema = createInsertSchema(userSettings).omit({ id: true });

// สร้างประเภทข้อมูลจาก schema
export type InsertUserSetting = z.infer<typeof insertUserSettingSchema>;
export type UserSetting = typeof userSettings.$inferSelect;

// ตาราง api_logs - บันทึกการใช้งาน API
export const apiLogs = pgTable("api_logs", {
  id: serial("id").primaryKey(),
  apiKeyId: integer("api_key_id").references(() => apiKeys.id).notNull(),
  requestType: apiRequestTypeEnum("request_type").notNull(),
  requestData: jsonb("request_data"), // ข้อมูลที่ส่งมาในคำขอ
  responseStatus: apiResponseStatusEnum("response_status").notNull(),
  responseData: jsonb("response_data"), // ข้อมูลที่ส่งกลับไป
  slipVerificationId: integer("slip_verification_id").references(() => slipVerifications.id), // รหัสการตรวจสอบสลิป (ถ้ามี)
  ipAddress: text("ip_address").notNull(),
  userAgent: text("user_agent"),
  processingTime: integer("processing_time").notNull(), // เวลาที่ใช้ในการประมวลผล (มิลลิวินาที)
  errorMessage: text("error_message"), // ข้อความแสดงข้อผิดพลาด (ถ้ามี)
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// ตาราง api_response_templates - รูปแบบการตอบกลับ API
export const apiResponseTemplates = pgTable("api_response_templates", {
  id: serial("id").primaryKey(),
  statusCode: integer("status_code").notNull(),
  statusType: text("status_type").notNull(), // success, error, etc.
  message: text("message").notNull(),
  description: text("description"),
  template: jsonb("template").notNull(), // รูปแบบการตอบกลับในรูปแบบ JSON
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// ตาราง notifications - แจ้งเตือนของระบบ
export const notifications = pgTable("notifications", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  title: text("title").notNull(),                       // หัวข้อการแจ้งเตือน
  message: text("message").notNull(),                   // ข้อความ
  type: alertTypeEnum("type").notNull(),                // ประเภทการแจ้งเตือน
  priority: alertPriorityEnum("priority").default('medium').notNull(), // ระดับความสำคัญ
  status: alertStatusEnum("status").default('pending').notNull(), // สถานะ
  channel: alertChannelEnum("channel").notNull(),       // ช่องทางการแจ้งเตือน
  metadata: jsonb("metadata"),                          // ข้อมูลเพิ่มเติม
  isRead: boolean("is_read").default(false).notNull(),  // อ่านแล้วหรือยัง
  readAt: timestamp("read_at"),                         // อ่านเมื่อไร
  sentAt: timestamp("sent_at"),                         // ส่งเมื่อไร
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// ตาราง alert_settings - การตั้งค่าการแจ้งเตือน
export const alertSettings = pgTable("alert_settings", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  alertType: alertTypeEnum("alert_type").notNull(),     // ประเภทการแจ้งเตือน
  enabled: boolean("enabled").default(true).notNull(),  // เปิดใช้งานหรือไม่
  channels: alertChannelEnum("channels").array(),       // ช่องทางการแจ้งเตือน
  threshold: jsonb("threshold"),                        // ค่าสำหรับเกณฑ์การแจ้งเตือน (e.g. {'amount_threshold': 10000} สำหรับธุรกรรมผิดปกติ)
  frequency: reportFrequencyEnum("frequency"),          // ความถี่ของรายงาน (สำหรับการส่งรายงาน)
  timePreference: jsonb("time_preference"),             // เวลาที่ต้องการรับรายงาน (สำหรับการส่งรายงาน)
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// ตาราง email_templates - เทมเพลตอีเมล
export const emailTemplates = pgTable("email_templates", {
  id: serial("id").primaryKey(),
  name: text("name").notNull().unique(),                // ชื่อเทมเพลต (e.g. 'unusual_transaction', 'daily_report', etc.)
  subject: text("subject").notNull(),                   // หัวข้ออีเมล
  htmlContent: text("html_content").notNull(),          // เนื้อหา HTML
  textContent: text("text_content").notNull(),          // เนื้อหาข้อความธรรมดา
  variables: text("variables").array(),                 // ตัวแปรที่ใช้ในเทมเพลต (e.g. '{{username}}', '{{amount}}', etc.)
  isDefault: boolean("is_default").default(false).notNull(), // เป็นเทมเพลตเริ่มต้นหรือไม่
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// ตาราง email_logs - บันทึกการส่งอีเมล
export const emailLogs = pgTable("email_logs", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  to: text("to").notNull(),                             // อีเมลผู้รับ
  subject: text("subject").notNull(),                   // หัวข้ออีเมล
  content: text("content"),                             // เนื้อหาที่ส่ง
  templateId: integer("template_id").references(() => emailTemplates.id), // เทมเพลตที่ใช้
  status: text("status").default('pending').notNull(),  // สถานะการส่ง
  errorMessage: text("error_message"),                  // ข้อความแสดงข้อผิดพลาด (ถ้ามี)
  metadata: jsonb("metadata"),                          // ข้อมูลเพิ่มเติม
  sentAt: timestamp("sent_at"),                         // เวลาที่ส่ง
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// ตาราง fraud_rules - กฎการตรวจจับการฉ้อโกง
export const fraudRules = pgTable("fraud_rules", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),                         // ชื่อกฎ
  description: text("description"),                     // คำอธิบาย
  ruleType: text("rule_type").notNull(),                // ประเภทกฎ (e.g. 'duplicate_slip', 'unusual_amount', etc.)
  conditions: jsonb("conditions").notNull(),            // เงื่อนไขของกฎ
  action: text("action").notNull(),                     // การกระทำเมื่อตรวจพบการฉ้อโกง (e.g. 'block', 'flag', 'notify')
  severity: text("severity").default('medium').notNull(), // ความรุนแรงของการฉ้อโกง
  isActive: boolean("is_active").default(true).notNull(), // เปิดใช้งานหรือไม่
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// ตาราง fraud_detections - ประวัติการตรวจจับการฉ้อโกง
export const fraudDetections = pgTable("fraud_detections", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  ruleId: integer("rule_id").references(() => fraudRules.id).notNull(),
  slipVerificationId: integer("slip_verification_id").references(() => slipVerifications.id), // รหัสการตรวจสอบสลิป (ถ้ามี)
  status: text("status").default('pending').notNull(),  // สถานะการตรวจสอบ
  action: text("action").notNull(),                     // การกระทำที่ได้ดำเนินการ
  evidenceData: jsonb("evidence_data"),                 // ข้อมูลหลักฐาน
  notes: text("notes"),                                 // บันทึกเพิ่มเติม
  reviewedBy: integer("reviewed_by"),                   // ผู้ตรวจสอบ (admin user ID)
  reviewedAt: timestamp("reviewed_at"),                 // เวลาที่ตรวจสอบ
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// ตาราง reports - รายงานระบบ
export const reports = pgTable("reports", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  reportType: text("report_type").notNull(),            // ประเภทรายงาน
  frequency: reportFrequencyEnum("frequency").notNull(), // ความถี่ของรายงาน
  format: text("format").default('pdf').notNull(),      // รูปแบบรายงาน (pdf, csv, etc.)
  parameters: jsonb("parameters"),                      // พารามิเตอร์สำหรับการสร้างรายงาน
  lastSent: timestamp("last_sent"),                     // วันที่ส่งล่าสุด
  nextSchedule: timestamp("next_schedule"),             // กำหนดการส่งครั้งถัดไป
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// ตาราง webhooks - การตั้งค่า webhook
export const webhooks = pgTable("webhooks", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  url: text("url").notNull(),                           // URL ปลายทางสำหรับ webhook
  secret: text("secret").notNull(),                     // รหัสลับสำหรับการลงนาม webhook
  events: webhookEventTypeEnum("events").array(),       // เหตุการณ์ที่จะส่ง webhook
  isActive: boolean("is_active").default(true).notNull(), // เปิดใช้งานหรือไม่
  description: text("description"),                     // คำอธิบาย
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// ตาราง webhook_logs - บันทึกการส่ง webhook
export const webhookLogs = pgTable("webhook_logs", {
  id: serial("id").primaryKey(),
  webhookId: integer("webhook_id").references(() => webhooks.id).notNull(),
  event: webhookEventTypeEnum("event").notNull(),       // เหตุการณ์ที่ทำให้เกิดการส่ง webhook
  payload: jsonb("payload").notNull(),                  // ข้อมูลที่ส่ง
  statusCode: integer("status_code"),                   // รหัสสถานะการตอบกลับ
  responseBody: text("response_body"),                  // เนื้อหาการตอบกลับ
  error: text("error"),                                 // ข้อผิดพลาด (ถ้ามี)
  duration: integer("duration"),                        // ระยะเวลาในการส่ง (มิลลิวินาที)
  attempts: integer("attempts").default(1).notNull(),   // จำนวนครั้งที่พยายามส่ง
  success: boolean("success").default(false).notNull(), // สำเร็จหรือไม่
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// ตาราง external_auth - เก็บข้อมูลการเชื่อมโยงกับบริการภายนอก
export const externalAuth = pgTable("external_auth", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  provider: authProviderEnum("provider").notNull(),      // LINE, Facebook, Google, Apple, Phone
  externalId: text("external_id").notNull(),             // ID จากผู้ให้บริการภายนอก
  accessToken: text("access_token"),                     // Access token
  refreshToken: text("refresh_token"),                   // Refresh token (ถ้ามี)
  tokenExpiry: timestamp("token_expiry"),                // วันหมดอายุของ token
  profileData: jsonb("profile_data"),                    // ข้อมูลโปรไฟล์จากผู้ให้บริการ
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
}, (table) => {
  return {
    userProviderIdx: uniqueIndex("user_provider_idx").on(table.userId, table.provider),
    providerExternalIdIdx: uniqueIndex("provider_external_id_idx").on(table.provider, table.externalId)
  };
});

// ตาราง verification_codes - เก็บรหัสยืนยันสำหรับอีเมลและเบอร์โทรศัพท์
export const verificationCodes = pgTable("verification_codes", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id), // ทำให้ nullable เพื่อรองรับการส่งรหัสยืนยันโดยไม่ต้องล็อกอิน
  code: text("code").notNull(),                          // รหัสยืนยัน (6 หลักสำหรับอีเมล, 4-6 หลักสำหรับ OTP)
  type: verificationTypeEnum("type").notNull(),          // ประเภทการยืนยัน (email, phone, password_reset, etc.)
  identifier: text("identifier").notNull(),              // อีเมลหรือเบอร์โทรศัพท์ที่ส่งรหัสไป
  isUsed: boolean("is_used").default(false).notNull(),   // ใช้งานแล้วหรือยัง
  expiresAt: timestamp("expires_at").notNull(),          // เวลาหมดอายุ
  attempts: integer("attempts").default(0).notNull(),    // จำนวนครั้งที่พยายามใช้รหัส
  usedAt: timestamp("used_at"),                          // เวลาที่ใช้รหัส
  metadata: text("metadata"),                            // ข้อมูลเพิ่มเติม เช่น token, otpRef สำหรับ OTP
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// ตาราง customer_behavior - พฤติกรรมของลูกค้า
export const customerBehavior = pgTable("customer_behavior", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  metric: text("metric").notNull(), // ชื่อค่าที่วัด (e.g. 'login_frequency', 'average_transaction_amount', etc.)
  value: doublePrecision("value").notNull(), // ค่าที่วัด
  timeInterval: text("time_interval").default('day').notNull(), // ช่วงเวลาที่วัด (day, week, month, year)
  measurementDate: date("measurement_date").notNull(), // วันที่ทำการวัด
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// ตาราง achievements - ความสำเร็จและรางวัลในระบบ
export const achievements = pgTable("achievements", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),                         // ชื่อความสำเร็จ
  description: text("description").notNull(),           // คำอธิบาย
  type: achievementTypeEnum("type").notNull(),          // ประเภทของความสำเร็จ
  icon: text("icon").notNull(),                         // ไอคอน (Font Awesome หรือ Lucide icon name)
  requirement: integer("requirement").notNull(),        // จำนวนที่ต้องทำให้สำเร็จ
  points: integer("points").default(0).notNull(),       // คะแนนที่จะได้รับเมื่อสำเร็จ
  rewardType: text("reward_type"),                      // ประเภทของรางวัล (credit, badge, theme, etc.)
  rewardValue: integer("reward_value"),                 // มูลค่าของรางวัล
  color: text("color").default("#4F46E5"),              // สีของไอคอน
  level: integer("level").default(1).notNull(),         // ระดับของความสำเร็จ
  sortOrder: integer("sort_order").default(0),          // ลำดับการแสดงผล
  packageId: integer("package_id").references(() => packages.id), // เชื่อมโยงกับแพ็กเกจ (ถ้ามี)
  isActive: boolean("is_active").default(true).notNull(),// เปิดใช้งานหรือไม่
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// ตาราง user_achievements - ความสำเร็จที่ผู้ใช้ได้รับ
export const userAchievements = pgTable("user_achievements", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  achievementId: integer("achievement_id").references(() => achievements.id).notNull(),
  progress: integer("progress").default(0).notNull(),   // ความก้าวหน้าปัจจุบัน
  completed: boolean("completed").default(false).notNull(), // สำเร็จแล้วหรือไม่
  completedAt: timestamp("completed_at"),              // วันเวลาที่สำเร็จ
  rewardClaimed: boolean("reward_claimed").default(false).notNull(), // รับรางวัลแล้วหรือไม่
  lastUpdated: timestamp("last_updated").defaultNow().notNull(), // อัพเดทล่าสุด
  createdAt: timestamp("created_at").defaultNow().notNull(),
}, (table) => {
  return {
    userAchievementIdx: uniqueIndex("user_achievement_idx").on(table.userId, table.achievementId)
  };
});

// ================ RELATIONS ================
// ความสัมพันธ์ระหว่างตาราง users และตารางอื่นๆ
export const usersRelations = relations(users, ({ many }) => ({
  userPackages: many(userPackages),
  apiKeys: many(apiKeys),
  notifications: many(notifications),
  alertSettings: many(alertSettings),
  externalAuth: many(externalAuth),
  verificationCodes: many(verificationCodes),
  userAchievements: many(userAchievements)
}));

// ความสัมพันธ์ระหว่างตาราง packages และตารางอื่นๆ
export const packagesRelations = relations(packages, ({ many }) => ({
  userPackages: many(userPackages),
  achievements: many(achievements)
}));

// ความสัมพันธ์ระหว่างตาราง user_packages และตารางอื่นๆ
export const userPackagesRelations = relations(userPackages, ({ one }) => ({
  user: one(users, {
    fields: [userPackages.userId],
    references: [users.id]
  }),
  package: one(packages, {
    fields: [userPackages.packageId],
    references: [packages.id]
  })
}));

// ความสัมพันธ์ระหว่างตาราง slip_verifications และตารางอื่นๆ
export const slipVerificationsRelations = relations(slipVerifications, ({ one }) => ({
  user: one(users, {
    fields: [slipVerifications.userId],
    references: [users.id]
  }),
  apiKey: one(apiKeys, {
    fields: [slipVerifications.apiKeyId],
    references: [apiKeys.id]
  })
}));

// ความสัมพันธ์ระหว่างตาราง top_up_transactions และตารางอื่นๆ
export const topUpTransactionsRelations = relations(topUpTransactions, ({ one }) => ({
  user: one(users, {
    fields: [topUpTransactions.userId],
    references: [users.id]
  })
}));

// ความสัมพันธ์ระหว่างตาราง api_keys และตารางอื่นๆ
export const apiKeysRelations = relations(apiKeys, ({ one, many }) => ({
  user: one(users, {
    fields: [apiKeys.userId],
    references: [users.id]
  }),
  apiLogs: many(apiLogs)
}));

// ความสัมพันธ์ระหว่างตาราง api_logs และตารางอื่นๆ
export const apiLogsRelations = relations(apiLogs, ({ one }) => ({
  apiKey: one(apiKeys, {
    fields: [apiLogs.apiKeyId],
    references: [apiKeys.id]
  }),
  slipVerification: one(slipVerifications, {
    fields: [apiLogs.slipVerificationId],
    references: [slipVerifications.id]
  })
}));

// ความสัมพันธ์ระหว่างตาราง notifications และตารางอื่นๆ
export const notificationsRelations = relations(notifications, ({ one }) => ({
  user: one(users, {
    fields: [notifications.userId],
    references: [users.id]
  })
}));

// ความสัมพันธ์ระหว่างตาราง alert_settings และตารางอื่นๆ
export const alertSettingsRelations = relations(alertSettings, ({ one }) => ({
  user: one(users, {
    fields: [alertSettings.userId],
    references: [users.id]
  })
}));

// ความสัมพันธ์ระหว่างตาราง email_logs และตารางอื่นๆ
export const emailLogsRelations = relations(emailLogs, ({ one }) => ({
  user: one(users, {
    fields: [emailLogs.userId],
    references: [users.id]
  }),
  template: one(emailTemplates, {
    fields: [emailLogs.templateId],
    references: [emailTemplates.id]
  })
}));

// ความสัมพันธ์ระหว่างตาราง fraud_detections และตารางอื่นๆ
export const fraudDetectionsRelations = relations(fraudDetections, ({ one }) => ({
  user: one(users, {
    fields: [fraudDetections.userId],
    references: [users.id]
  }),
  rule: one(fraudRules, {
    fields: [fraudDetections.ruleId],
    references: [fraudRules.id]
  }),
  slipVerification: one(slipVerifications, {
    fields: [fraudDetections.slipVerificationId],
    references: [slipVerifications.id]
  })
}));

// ความสัมพันธ์ระหว่างตาราง reports และตารางอื่นๆ
export const reportsRelations = relations(reports, ({ one }) => ({
  user: one(users, {
    fields: [reports.userId],
    references: [users.id]
  })
}));

// ความสัมพันธ์ระหว่างตาราง webhooks และตารางอื่นๆ
export const webhooksRelations = relations(webhooks, ({ one, many }) => ({
  user: one(users, {
    fields: [webhooks.userId],
    references: [users.id]
  }),
  logs: many(webhookLogs)
}));

// ความสัมพันธ์ระหว่างตาราง webhook_logs และตารางอื่นๆ
export const webhookLogsRelations = relations(webhookLogs, ({ one }) => ({
  webhook: one(webhooks, {
    fields: [webhookLogs.webhookId],
    references: [webhooks.id]
  })
}));

// ความสัมพันธ์ระหว่างตาราง customer_behavior และตารางอื่นๆ
export const customerBehaviorRelations = relations(customerBehavior, ({ one }) => ({
  user: one(users, {
    fields: [customerBehavior.userId],
    references: [users.id]
  })
}));

// ความสัมพันธ์ระหว่างตาราง external_auth และตารางอื่นๆ
export const externalAuthRelations = relations(externalAuth, ({ one }) => ({
  user: one(users, {
    fields: [externalAuth.userId],
    references: [users.id]
  })
}));

// ความสัมพันธ์ระหว่างตาราง verification_codes และตารางอื่นๆ
export const verificationCodesRelations = relations(verificationCodes, ({ one }) => ({
  user: one(users, {
    fields: [verificationCodes.userId],
    references: [users.id]
  })
}));

// ความสัมพันธ์ระหว่างตาราง achievements และตารางอื่นๆ
export const achievementsRelations = relations(achievements, ({ one, many }) => ({
  package: one(packages, {
    fields: [achievements.packageId],
    references: [packages.id]
  }),
  userAchievements: many(userAchievements)
}));

// ความสัมพันธ์ระหว่างตาราง user_achievements และตารางอื่นๆ
export const userAchievementsRelations = relations(userAchievements, ({ one }) => ({
  user: one(users, {
    fields: [userAchievements.userId],
    references: [users.id]
  }),
  achievement: one(achievements, {
    fields: [userAchievements.achievementId],
    references: [achievements.id]
  })
}));

// ================ TYPES ================
// สร้าง Type จาก Schema
export type User = typeof users.$inferSelect;
export type InsertUser = typeof users.$inferInsert;

export type Package = typeof packages.$inferSelect;
export type InsertPackage = typeof packages.$inferInsert;

export type UserPackage = typeof userPackages.$inferSelect;
export type InsertUserPackage = typeof userPackages.$inferInsert;

export type SlipVerification = typeof slipVerifications.$inferSelect;
export type InsertSlipVerification = typeof slipVerifications.$inferInsert;

export type SystemSetting = typeof systemSettings.$inferSelect;
export type InsertSystemSetting = typeof systemSettings.$inferInsert;

export type Coupon = typeof coupons.$inferSelect;
export type InsertCoupon = typeof coupons.$inferInsert;

export type TopUpTransaction = typeof topUpTransactions.$inferSelect;
export type InsertTopUpTransaction = typeof topUpTransactions.$inferInsert;

export type ApiKey = typeof apiKeys.$inferSelect;
export type InsertApiKey = typeof apiKeys.$inferInsert;

export type ApiLog = typeof apiLogs.$inferSelect;
export type InsertApiLog = typeof apiLogs.$inferInsert;

export type ApiResponseTemplate = typeof apiResponseTemplates.$inferSelect;
export type InsertApiResponseTemplate = typeof apiResponseTemplates.$inferInsert;

export type Notification = typeof notifications.$inferSelect;
export type InsertNotification = typeof notifications.$inferInsert;

export type AlertSetting = typeof alertSettings.$inferSelect;
export type InsertAlertSetting = typeof alertSettings.$inferInsert;

export type EmailTemplate = typeof emailTemplates.$inferSelect;
export type InsertEmailTemplate = typeof emailTemplates.$inferInsert;

export type EmailLog = typeof emailLogs.$inferSelect;
export type InsertEmailLog = typeof emailLogs.$inferInsert;

export type FraudRule = typeof fraudRules.$inferSelect;
export type InsertFraudRule = typeof fraudRules.$inferInsert;

export type FraudDetection = typeof fraudDetections.$inferSelect;
export type InsertFraudDetection = typeof fraudDetections.$inferInsert;

export type Report = typeof reports.$inferSelect;
export type InsertReport = typeof reports.$inferInsert;

export type Webhook = typeof webhooks.$inferSelect;
export type InsertWebhook = typeof webhooks.$inferInsert;

export type WebhookLog = typeof webhookLogs.$inferSelect;
export type InsertWebhookLog = typeof webhookLogs.$inferInsert;

export type CustomerBehavior = typeof customerBehavior.$inferSelect;
export type InsertCustomerBehavior = typeof customerBehavior.$inferInsert;

export type ExternalAuth = typeof externalAuth.$inferSelect;
export type InsertExternalAuth = typeof externalAuth.$inferInsert;

export type VerificationCode = typeof verificationCodes.$inferSelect;
export type InsertVerificationCode = typeof verificationCodes.$inferInsert;

export type Achievement = typeof achievements.$inferSelect;
export type InsertAchievement = typeof achievements.$inferInsert;

export type UserAchievement = typeof userAchievements.$inferSelect;
export type InsertUserAchievement = typeof userAchievements.$inferInsert;

// ================ SCHEMAS ================
// สร้าง Schema จาก Table
export const insertUserSchema = createInsertSchema(users).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export const insertPackageSchema = createInsertSchema(packages).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export const insertUserPackageSchema = createInsertSchema(userPackages).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export const insertSlipVerificationSchema = createInsertSchema(slipVerifications).omit({
  id: true,
  createdAt: true
});

export const insertSystemSettingSchema = createInsertSchema(systemSettings).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export const insertCouponSchema = createInsertSchema(coupons).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export const insertTopUpTransactionSchema = createInsertSchema(topUpTransactions).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export const insertApiKeySchema = createInsertSchema(apiKeys).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  lastUsed: true,
  requestCount: true
});

export const insertApiLogSchema = createInsertSchema(apiLogs).omit({
  id: true,
  createdAt: true
});

export const insertApiResponseTemplateSchema = createInsertSchema(apiResponseTemplates).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export const insertNotificationSchema = createInsertSchema(notifications).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export const insertAlertSettingSchema = createInsertSchema(alertSettings).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export const insertEmailTemplateSchema = createInsertSchema(emailTemplates).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export const insertEmailLogSchema = createInsertSchema(emailLogs).omit({
  id: true,
  createdAt: true
});

export const insertFraudRuleSchema = createInsertSchema(fraudRules).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export const insertFraudDetectionSchema = createInsertSchema(fraudDetections).omit({
  id: true,
  createdAt: true
});

export const insertReportSchema = createInsertSchema(reports).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export const insertWebhookSchema = createInsertSchema(webhooks).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export const insertWebhookLogSchema = createInsertSchema(webhookLogs).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export const insertCustomerBehaviorSchema = createInsertSchema(customerBehavior).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

// ตาราง user_auth_logs - บันทึกประวัติการล็อกอิน
export const userAuthLogs = pgTable("user_auth_logs", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  ipAddress: text("ip_address").notNull(),
  userAgent: text("user_agent"),
  auth_method: authMethodEnum("auth_method").notNull(),
  success: boolean("success").default(false).notNull(),
  failReason: text("fail_reason"),
  created_at: timestamp("created_at").defaultNow().notNull(),
}, (table) => {
  return {
    userIdx: index("user_auth_logs_user_id_idx").on(table.userId),
    createdAtIdx: index("user_auth_logs_created_at_idx").on(table.created_at),
  }
});

export const insertExternalAuthSchema = createInsertSchema(externalAuth).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export const insertVerificationCodeSchema = createInsertSchema(verificationCodes).omit({
  id: true,
  createdAt: true
});

export const insertAchievementSchema = createInsertSchema(achievements).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export const insertUserAchievementSchema = createInsertSchema(userAchievements).omit({
  id: true,
  createdAt: true
});