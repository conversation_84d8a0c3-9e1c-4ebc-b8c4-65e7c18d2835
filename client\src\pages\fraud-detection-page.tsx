import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import { useToast } from '@/hooks/use-toast';
import { AlertTriangle, CheckCircle, Eye, ExternalLink, Filter, Info, RefreshCw, Search, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { formatThaiDateTime, formatThaiCurrency } from '@/lib/utils';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';

type FraudDetection = {
  id: number;
  status: string;
  slipVerification: {
    id: number;
    transactionRef: string;
    amount: number;
    transactionDate: string;
    sender: string;
    receiver: string;
    status: string;
  };
  rule: {
    name: string;
    description: string;
    ruleType: string;
    action: string;
    severity: string;
  };
  details: any;
  createdAt: string;
  updatedAt: string;
};

export default function FraudDetectionPage() {
  const { toast } = useToast();
  const [selectedTab, setSelectedTab] = useState('recent');

  // ดึงข้อมูลการตรวจจับการฉ้อโกง
  const { data, isLoading, isError, error, refetch } = useQuery({
    queryKey: ['/api/fraud-detection'],
    queryFn: async () => {
      const response = await fetch('/api/fraud-detection');
      if (!response.ok) {
        throw new Error('ไม่สามารถดึงข้อมูลการตรวจจับการฉ้อโกงได้');
      }
      return response.json();
    }
  });

  // ดูรายละเอียดธุรกรรม
  const handleViewTransaction = (id: number) => {
    window.open(`/verifications/${id}`, '_blank');
  };

  // คำอธิบายสถานะ
  const getStatusText = (status: string) => {
    switch (status) {
      case 'detected':
        return 'ตรวจพบ';
      case 'reviewed':
        return 'ตรวจสอบแล้ว';
      case 'confirmed':
        return 'ยืนยันแล้ว';
      case 'false_positive':
        return 'แจ้งเตือนผิด';
      default:
        return status;
    }
  };

  // สีของสถานะ
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'detected':
        return 'bg-yellow-500 text-black';
      case 'reviewed':
        return 'bg-blue-500 text-white';
      case 'confirmed':
        return 'bg-red-500 text-white';
      case 'false_positive':
        return 'bg-green-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  // ไอคอนสำหรับประเภทของการฉ้อโกง
  const getRuleTypeIcon = (ruleType: string) => {
    switch (ruleType) {
      case 'unusual_amount':
        return <AlertTriangle className="h-5 w-5 text-yellow-400" />;
      case 'duplicate_slip':
        return <Shield className="h-5 w-5 text-red-400" />;
      case 'unusual_usage':
        return <Info className="h-5 w-5 text-blue-400" />;
      default:
        return <AlertTriangle className="h-5 w-5 text-gray-400" />;
    }
  };

  // คำอธิบายประเภทของการฉ้อโกง
  const getRuleTypeText = (ruleType: string) => {
    switch (ruleType) {
      case 'unusual_amount':
        return 'มูลค่าสูงผิดปกติ';
      case 'duplicate_slip':
        return 'สลิปซ้ำ';
      case 'unusual_usage':
        return 'การใช้งานผิดปกติ';
      default:
        return ruleType;
    }
  };

  // คำอธิบายและสีของระดับความรุนแรง
  const getSeverityText = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'สูง';
      case 'medium':
        return 'ปานกลาง';
      case 'low':
        return 'ต่ำ';
      default:
        return severity;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'bg-red-500 text-white';
      case 'medium':
        return 'bg-yellow-500 text-black';
      case 'low':
        return 'bg-blue-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  // กรองเฉพาะข้อมูลที่ต้องการแสดง
  const getFilteredDetections = () => {
    if (!data) return [];
    
    switch (selectedTab) {
      case 'recent':
        return data.slice(0, 5);
      case 'unusual_amount':
        return data.filter(d => d.rule.ruleType === 'unusual_amount');
      case 'duplicate_slip':
        return data.filter(d => d.rule.ruleType === 'duplicate_slip');
      case 'confirmed':
        return data.filter(d => d.status === 'confirmed');
      default:
        return data;
    }
  };

  // รายละเอียดของการตรวจจับการฉ้อโกง
  const renderDetectionDetails = (detection: FraudDetection) => {
    const { rule, slipVerification, details } = detection;
    
    switch (rule.ruleType) {
      case 'unusual_amount':
        return (
          <div className="mt-4 p-3 bg-yellow-500/10 rounded-md border border-yellow-500/20">
            <p className="text-sm text-yellow-300 mb-2">
              <AlertTriangle className="h-4 w-4 inline mr-1" /> 
              ธุรกรรมนี้มีมูลค่า <span className="font-semibold">{formatThaiCurrency(slipVerification.amount)}</span> บาท
              {details?.thresholdAmount && (
                <> ซึ่งสูงกว่าเกณฑ์ที่กำหนดไว้ที่ <span className="font-semibold">{formatThaiCurrency(details.thresholdAmount)}</span> บาท</>
              )}
            </p>
            <p className="text-xs text-gray-400">
              เวลาที่ทำรายการ: {formatThaiDateTime(slipVerification.transactionDate || detection.createdAt)}
            </p>
          </div>
        );
      case 'duplicate_slip':
        return (
          <div className="mt-4 p-3 bg-red-500/10 rounded-md border border-red-500/20">
            <p className="text-sm text-red-300 mb-2">
              <Shield className="h-4 w-4 inline mr-1" />
              สลิปนี้ถูกใช้ซ้ำ {details?.duplicateCount || 2} ครั้งภายใน {details?.timeWindow ? `${details.timeWindow / 3600} ชั่วโมง` : '24 ชั่วโมง'}
            </p>
            <p className="text-xs text-gray-400">
              รหัสธุรกรรม: {slipVerification.transactionRef || 'ไม่ระบุ'}
            </p>
          </div>
        );
      default:
        return (
          <div className="mt-4 p-3 bg-gray-500/10 rounded-md border border-gray-500/20">
            <p className="text-sm text-gray-300">
              <Info className="h-4 w-4 inline mr-1" />
              รายละเอียดการตรวจจับ: {JSON.stringify(details || {})}
            </p>
          </div>
        );
    }
  };

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="container mx-auto py-6 px-4 sm:px-6"
    >
      {/* พื้นหลังสไตล์เทพเจ้า */}
      <div className="fixed inset-0 -z-10 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-b from-gray-900 via-purple-950/50 to-gray-900"></div>
        <div className="absolute inset-0 opacity-20 bg-[radial-gradient(#330a5e_1px,transparent_1px)] [background-size:32px_32px]"></div>
        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-purple-500/50 to-transparent"></div>
      </div>
      
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-pink-300 to-red-400">
              การตรวจจับการฉ้อโกง
            </h1>
            <p className="text-gray-400 mt-1">
              ระบบตรวจจับและแจ้งเตือนการฉ้อโกงอัตโนมัติ
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => refetch()}
              disabled={isLoading}
              className="text-purple-300 hover:text-purple-200 hover:bg-purple-950 border-purple-800"
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
              รีเฟรช
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              className="text-purple-300 hover:text-purple-200 hover:bg-purple-950 border-purple-800"
            >
              <Filter className="h-4 w-4 mr-1" />
              กรอง
            </Button>
          </div>
        </div>
        
        {/* แสดงการตรวจจับการฉ้อโกง */}
        <Card className="bg-gray-900/60 backdrop-blur-sm border-gray-800 shadow-xl overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-purple-900/40 to-gray-900/40 border-b border-gray-800 pb-4">
            <CardTitle className="flex items-center">
              <Shield className="h-5 w-5 mr-2 text-purple-400" />
              ระบบตรวจจับการฉ้อโกง
            </CardTitle>
          </CardHeader>
          
          <Tabs defaultValue="recent" value={selectedTab} onValueChange={setSelectedTab}>
            <div className="px-6 pt-4">
              <TabsList className="bg-gray-800/50 border border-gray-800">
                <TabsTrigger value="recent" className="data-[state=active]:bg-purple-900/40">
                  <AlertTriangle className="h-4 w-4 mr-1" />
                  ล่าสุด
                </TabsTrigger>
                <TabsTrigger value="unusual_amount" className="data-[state=active]:bg-purple-900/40">
                  <AlertTriangle className="h-4 w-4 mr-1" />
                  มูลค่าสูง
                </TabsTrigger>
                <TabsTrigger value="duplicate_slip" className="data-[state=active]:bg-purple-900/40">
                  <Shield className="h-4 w-4 mr-1" />
                  สลิปซ้ำ
                </TabsTrigger>
                <TabsTrigger value="confirmed" className="data-[state=active]:bg-purple-900/40">
                  <CheckCircle className="h-4 w-4 mr-1" />
                  ยืนยันแล้ว
                </TabsTrigger>
              </TabsList>
            </div>
            
            <CardContent className="p-4 sm:p-6">
              {isLoading ? (
                <div className="flex flex-col items-center justify-center p-8">
                  <div className="w-12 h-12 rounded-full border-4 border-t-purple-500 border-purple-800/30 animate-spin"></div>
                  <p className="mt-4 text-gray-400">กำลังโหลดข้อมูล...</p>
                </div>
              ) : isError ? (
                <div className="flex flex-col items-center justify-center p-8 text-red-400">
                  <p>เกิดข้อผิดพลาดในการโหลดข้อมูล</p>
                  <p className="text-sm text-gray-500">{error instanceof Error ? error.message : 'เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ'}</p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => refetch()}
                    className="mt-4 text-purple-300 hover:text-purple-200 hover:bg-purple-950 border-purple-800"
                  >
                    <RefreshCw className="h-4 w-4 mr-1" />
                    ลองอีกครั้ง
                  </Button>
                </div>
              ) : getFilteredDetections().length === 0 ? (
                <div className="flex flex-col items-center justify-center p-8 text-gray-500">
                  <Shield className="h-12 w-12 mb-2 text-gray-400 opacity-50" />
                  <p>ไม่พบการตรวจจับการฉ้อโกงในหมวดหมู่นี้</p>
                </div>
              ) : (
                <div className="space-y-6">
                  {getFilteredDetections().map((detection: FraudDetection) => (
                    <motion.div
                      key={detection.id}
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="rounded-lg bg-gray-800/60 border border-gray-700 overflow-hidden"
                    >
                      <div className="p-4">
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 mb-3">
                          <div className="flex items-center gap-2">
                            <Avatar className="h-8 w-8 bg-purple-900">
                              <AvatarFallback className="bg-purple-900 text-purple-300">
                                {getRuleTypeIcon(detection.rule.ruleType)}
                              </AvatarFallback>
                            </Avatar>
                            
                            <div>
                              <h3 className="font-medium text-white">
                                {detection.rule.name}
                              </h3>
                              <p className="text-xs text-gray-400">
                                {detection.rule.description}
                              </p>
                            </div>
                          </div>
                          
                          <div className="flex flex-wrap gap-2">
                            <Badge className={getStatusColor(detection.status)}>
                              {getStatusText(detection.status)}
                            </Badge>
                            <Badge className="bg-purple-700/80 text-white">
                              {getRuleTypeText(detection.rule.ruleType)}
                            </Badge>
                            <Badge className={getSeverityColor(detection.rule.severity)}>
                              ความเสี่ยง: {getSeverityText(detection.rule.severity)}
                            </Badge>
                          </div>
                        </div>
                        
                        <Separator className="my-3 bg-gray-700" />
                        
                        <div className="flex flex-col sm:flex-row justify-between gap-4">
                          <div className="space-y-1">
                            <p className="text-sm">
                              <span className="text-gray-400">รหัสธุรกรรม:</span>{' '}
                              <span className="text-white font-medium">{detection.slipVerification.transactionRef || 'ไม่ระบุ'}</span>
                            </p>
                            <p className="text-sm">
                              <span className="text-gray-400">จำนวนเงิน:</span>{' '}
                              <span className="text-white font-medium">{formatThaiCurrency(detection.slipVerification.amount)}</span>
                            </p>
                            <p className="text-sm">
                              <span className="text-gray-400">ผู้รับเงิน:</span>{' '}
                              <span className="text-white">{detection.slipVerification.receiver || 'ไม่ระบุ'}</span>
                            </p>
                          </div>
                          
                          <div className="flex flex-shrink-0 mt-2 sm:mt-0">
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-purple-300 hover:text-purple-200 hover:bg-purple-950 border-purple-800"
                              onClick={() => handleViewTransaction(detection.slipVerification.id)}
                            >
                              <Eye className="h-4 w-4 mr-1" />
                              ดูธุรกรรม
                            </Button>
                          </div>
                        </div>
                        
                        {/* รายละเอียดการตรวจจับ */}
                        {renderDetectionDetails(detection)}
                        
                        {/* เวลาที่ตรวจพบ */}
                        <p className="text-xs text-gray-500 mt-3">
                          ตรวจพบเมื่อ: {formatThaiDateTime(detection.createdAt)}
                        </p>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </CardContent>
          </Tabs>
        </Card>
        
        {/* คำอธิบายระบบ */}
        <Card className="bg-gray-900/60 backdrop-blur-sm border-gray-800 shadow-xl overflow-hidden">
          <CardContent className="p-4 sm:p-6">
            <h3 className="text-lg font-semibold text-purple-300 mb-3">
              <Shield className="h-5 w-5 inline mr-1 text-purple-400" />
              เกี่ยวกับระบบตรวจจับการฉ้อโกง
            </h3>
            
            <div className="space-y-3 text-sm text-gray-300">
              <p>
                ระบบตรวจจับการฉ้อโกงของ SLIPKUY ช่วยปกป้องคุณจากการทำธุรกรรมที่ผิดปกติหรือน่าสงสัย โดยใช้อัลกอริทึมขั้นสูงในการวิเคราะห์รูปแบบและพฤติกรรมการใช้งาน
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-4">
                <div className="p-3 bg-purple-950/30 rounded-lg border border-purple-800/40">
                  <h4 className="font-medium text-purple-300 mb-1">
                    <AlertTriangle className="h-4 w-4 inline mr-1 text-yellow-400" />
                    ตรวจจับมูลค่าสูงผิดปกติ
                  </h4>
                  <p className="text-gray-400 text-sm">
                    แจ้งเตือนเมื่อมีการทำธุรกรรมที่มีมูลค่าสูงเกินกว่าเกณฑ์ที่กำหนดไว้
                  </p>
                </div>
                
                <div className="p-3 bg-purple-950/30 rounded-lg border border-purple-800/40">
                  <h4 className="font-medium text-purple-300 mb-1">
                    <Shield className="h-4 w-4 inline mr-1 text-red-400" />
                    ตรวจจับสลิปซ้ำ
                  </h4>
                  <p className="text-gray-400 text-sm">
                    ป้องกันการใช้งานสลิปซ้ำโดยตรวจสอบรหัสธุรกรรมและข้อมูลสำคัญในสลิป
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </motion.div>
  );
}