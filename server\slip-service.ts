import axios from 'axios';
import FormData from 'form-data';
import { storage } from './storage';
import { Request, Response } from 'express';
import { hasQRCode, detectQRCode } from './qr-detector-new';
import { db } from './db';
import { userSettings, slipVerifications } from '@shared/schema';
import { eq, and, desc } from 'drizzle-orm';
import { logger } from './logger';

export interface VerifySlipResponse {
  status: number;
  apiProvider?: string;
  data?: {
    payload: string;
    transRef: string;
    date: string;
    countryCode: string;
    amount: {
      amount: number;
      local: {
        amount?: number;
        currency?: string;
      }
    };
    fee?: number;
    ref1?: string;
    ref2?: string;
    ref3?: string;
    sender: {
      bank: {
        id: string;
        name?: string;
        short?: string;
      };
      account: {
        name: {
          th?: string;
          en?: string;
        };
        bank?: {
          type: 'BANKAC' | 'TOKEN' | 'DUMMY';
          account: string;
        };
        proxy?: {
          type: 'NATID' | 'MSISDN' | 'EWALLETID' | 'EMAIL' | 'BILLERID';
          account: string;
        };
      };
    };
    receiver: {
      bank: {
        id: string;
        name?: string;
        short?: string;
      };
      account: {
        name: {
          th?: string;
          en?: string;
        };
        bank?: {
          type: 'BANKAC' | 'TOKEN' | 'DUMMY';
          account: string;
        };
        proxy?: {
          type: 'NATID' | 'MSISDN' | 'EWALLETID' | 'EMAIL' | 'BILLERID';
          account: string;
        };
      };
      merchantId?: string;
    };
  };
  message?: string;
}

// Generic error handler
export class SlipServiceError extends Error {
  status: number;

  constructor(message: string, status: number = 500) {
    super(message);
    this.name = 'SlipServiceError';
    this.status = status;
  }
}

// Service to handle slip verification API calls
export class SlipService {
  // EasySlip API
  private easySlipApiUrl = 'https://developer.easyslip.com/api/v1/verify';
  private easySlipApiKey = process.env.EASYSLIP_API_KEY || '';

  // SLIPKUY API
  private slip2GoApiUrl = 'https://connect.slip2go.com/api/verify-slip/qr-image/info';
  private slip2GoApiKey = process.env.SLIP2GO_API_KEY || '';

  // ตัวแปรเก็บค่า API provider ที่ต้องการใช้
  private apiProvider: string = 'slipkuy'; // ค่าเริ่มต้นคือ slipkuy

  // อ่านค่า API provider จากฐานข้อมูล
  private async getApiProvider(): Promise<string> {
    try {
      const setting = await storage.getSetting('verification_api_provider');
      return setting?.value || this.apiProvider;
    } catch (error) {
      console.error('Error getting API provider setting:', error);
      return this.apiProvider;
    }
  }

  // Verify with EasySlip
  async verifyWithEasySlip(fileBuffer: Buffer, fileName: string): Promise<VerifySlipResponse> {
    try {
      // ใช้ logger.debug แทนการแสดงข้อมูลแบบ console.log
      const { logger } = await import('./logger');
      logger.debug(`🔄 EasySlip API: ส่งข้อมูลขนาด ${fileBuffer.length} ไบต์ เพื่อตรวจสอบ`);

      const formData = new FormData();
      formData.append('file', fileBuffer, { filename: fileName });

      const response = await axios.post(this.easySlipApiUrl, formData, {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${this.easySlipApiKey}`
        },
        validateStatus: () => true // Allow any status code for custom error handling
      });

      // แสดงผลลัพธ์แบบย่อ
      const isSuccess = response.status === 200 && response.data && response.data.status === 200;
      logger.debug(`${isSuccess ? '✅' : '❌'} EasySlip API: ผลการตรวจสอบ ${isSuccess ? 'สำเร็จ' : 'ล้มเหลว'} (${response.status})`);

      // แสดงข้อมูลสำคัญเท่านั้น ไม่ต้องแสดงผลลัพธ์ทั้งหมด
      if (isSuccess && response.data && response.data.data) {
        const { transRef, amount } = response.data.data;
        logger.debug(`📝 ข้อมูลสลิป EasySlip: รายการ=${transRef || 'ไม่ระบุ'}, จำนวน=${amount?.amount || 0} บาท`);
      }

      // แปลงข้อมูลจาก EasySlip เป็นรูปแบบเดียวกับ Slip2Go
      if (isSuccess && response.data && response.data.status === 200) {
        // ถ้าตรวจสอบสำเร็จ
        return {
          status: 200,
          data: {
            payload: response.data.data?.payload || "",
            transRef: response.data.data?.transRef || "",
            date: response.data.data?.date || "",
            countryCode: response.data.data?.countryCode || "TH",
            amount: {
              amount: response.data.data?.amount?.amount || 0,
              local: {
                amount: response.data.data?.amount?.local?.amount || 0,
                currency: response.data.data?.amount?.local?.currency || "THB"
              }
            },
            sender: {
              bank: {
                id: response.data.data?.sender?.bank?.id || "",
                name: response.data.data?.sender?.bank?.name || "",
                short: response.data.data?.sender?.bank?.short || ""
              },
              account: {
                name: {
                  th: response.data.data?.sender?.account?.name?.th || "",
                  en: response.data.data?.sender?.account?.name?.en || ""
                },
                bank: {
                  type: response.data.data?.sender?.account?.bank?.type || "BANKAC",
                  account: response.data.data?.sender?.account?.bank?.account || ""
                }
              }
            },
            receiver: {
              bank: {
                id: response.data.data?.receiver?.bank?.id || "",
                name: response.data.data?.receiver?.bank?.name || "",
                short: response.data.data?.receiver?.bank?.short || ""
              },
              account: {
                name: {
                  th: response.data.data?.receiver?.account?.name?.th || "",
                  en: response.data.data?.receiver?.account?.name?.en || ""
                },
                bank: {
                  type: response.data.data?.receiver?.account?.bank?.type || "BANKAC",
                  account: response.data.data?.receiver?.account?.bank?.account || ""
                }
              }
            }
          }
        };
      } else {
        // กรณีข้อผิดพลาดอื่นๆ
        return {
          status: response.status || 500,
          message: response.data?.message || 'เกิดข้อผิดพลาดในการตรวจสอบสลิป'
        };
      }
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const status = error.response?.status || 500;
        const message = error.response?.data?.message || 'เกิดข้อผิดพลาดในการตรวจสอบสลิป';
        throw new SlipServiceError(message, status);
      }
      throw new SlipServiceError('ไม่สามารถเชื่อมต่อกับบริการตรวจสอบสลิปได้', 500);
    }
  }

  // Verify with SLIPKUY
  async verifyWithSlip2Go(fileBuffer: Buffer, fileName: string): Promise<VerifySlipResponse> {
    try {
      // ใช้ logger.debug แทนการแสดงข้อมูลแบบ console.log
      const { logger } = await import('./logger');
      logger.debug(`🔄 SLIPKUY API: ส่งข้อมูลขนาด ${fileBuffer.length} ไบต์ เพื่อตรวจสอบ`);

      const formData = new FormData();
      formData.append('file', fileBuffer, { filename: fileName });

      const response = await axios.post(this.slip2GoApiUrl, formData, {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${this.slip2GoApiKey}`
        },
        validateStatus: () => true // Allow any status code for custom error handling
      });

      // แสดงผลลัพธ์แบบย่อ
      const isSuccess = response.status === 200 || (response.data && response.data.code === "200000");
      logger.debug(`${isSuccess ? '✅' : '❌'} SLIPKUY API: ผลการตรวจสอบ ${isSuccess ? 'สำเร็จ' : 'ล้มเหลว'} (${response.status})`);

      // แสดงข้อมูลสำคัญเท่านั้น ไม่ต้องแสดงผลลัพธ์ทั้งหมด
      if (isSuccess && response.data && response.data.data) {
        const { transRef, amount, dateTime } = response.data.data;
        logger.debug(`📝 ข้อมูลสลิป: รายการ=${transRef || 'ไม่ระบุ'}, จำนวน=${amount || 0} บาท, วันที่=${dateTime || 'ไม่ระบุ'}`);
      }

      // แปลงข้อมูลจาก SLIPKUY เป็นรูปแบบเดียวกับ EasySlip
      if (response.data && response.data.code === "200000") {
        // ถ้าตรวจสอบสำเร็จ
        return {
          status: 200,
          data: {
            payload: response.data.data.decode || "",
            transRef: response.data.data.transRef || "",
            date: response.data.data.dateTime || "",
            countryCode: "TH",
            amount: {
              // กำหนดทศนิยม 2 ตำแหน่ง
              amount: parseFloat(parseFloat(response.data.data.amount).toFixed(2)) || 0,
              local: { amount: parseFloat(parseFloat(response.data.data.amount).toFixed(2)) || 0, currency: "THB" }
            },
            sender: {
              bank: {
                id: response.data.data.sender?.bank?.id || "",
                name: response.data.data.sender?.bank?.name || "",
                short: ""
              },
              account: {
                name: {
                  th: response.data.data.sender?.account?.name || "",
                  en: ""
                },
                bank: {
                  type: "BANKAC",
                  account: response.data.data.sender?.account?.bank?.account || ""
                }
              }
            },
            receiver: {
              bank: {
                id: response.data.data.receiver?.bank?.id || "",
                name: response.data.data.receiver?.bank?.name || "",
                short: ""
              },
              account: {
                name: {
                  th: response.data.data.receiver?.account?.name || "",
                  en: ""
                },
                bank: {
                  type: "BANKAC",
                  account: response.data.data.receiver?.account?.bank?.account || ""
                }
              }
            }
          }
        };
      } else if (response.data && response.data.code === "200404") {
        // ถ้าไม่พบสลิป
        return {
          status: 404,
          message: "slip_not_found"
        };
      } else {
        // กรณีข้อผิดพลาดอื่นๆ
        return {
          status: response.status || 500,
          message: response.data?.message || 'เกิดข้อผิดพลาดในการตรวจสอบสลิป'
        };
      }
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const status = error.response?.status || 500;
        const message = error.response?.data?.message || 'เกิดข้อผิดพลาดในการตรวจสอบสลิป';
        throw new SlipServiceError(message, status);
      }
      throw new SlipServiceError('ไม่สามารถเชื่อมต่อกับบริการตรวจสอบสลิปได้', 500);
    }
  }

  // ตรวจสอบสลิปซ้ำ
  private async checkDuplicateSlip(userId: number, qrData: string, isWebVerification: boolean = false): Promise<{isDuplicate: boolean, existingVerification?: any}> {
    try {
      // ดึงการตั้งค่าของผู้ใช้
      let userSetting = await storage.getUserSettings(userId);

      // ถ้าไม่มีการตั้งค่า ให้สร้างการตั้งค่าเริ่มต้น (ค่าเริ่มต้นเป็น true)
      if (!userSetting) {
        logger.debug('🔍 ไม่พบการตั้งค่าผู้ใช้ สร้างการตั้งค่าเริ่มต้น (เปิดตรวจสอบสลิปซ้ำ)');

        // สร้างการตั้งค่าเริ่มต้นให้ผู้ใช้
        try {
          userSetting = await storage.createUserSettings({
            userId,
            duplicateSlipCheck: true,
            createdAt: new Date(),
            updatedAt: new Date()
          });
        } catch (error) {
          logger.warn('ไม่สามารถสร้างการตั้งค่าเริ่มต้นสำหรับผู้ใช้ได้:', error);
          // ให้ใช้ค่าเริ่มต้นเป็น true
          userSetting = { duplicateSlipCheck: true } as any;
        }
      }

      // ตรวจสอบว่าเป็นการตรวจสอบผ่านหน้าเว็บไซต์หรือไม่
      // เงื่อนไขนี้ให้ใช้การตั้งค่า duplicateSlipCheck ของผู้ใช้เฉพาะเมื่อเป็นการตรวจสอบผ่านหน้าเว็บไซต์เท่านั้น
      if (isWebVerification && userSetting && userSetting.duplicateSlipCheck === false) {
        logger.debug('🔍 ผู้ใช้ปิดการตรวจสอบสลิปซ้ำบนเว็บไซต์');
        return { isDuplicate: false };
      }

      // สำหรับการตรวจสอบผ่าน API จะใช้การตั้งค่า duplicateSlipCheck ของ API key
      // ซึ่งจะถูกตรวจสอบโดยตรงที่ไฟล์ server/api/slip-api.ts ไม่ได้ผ่านมาทางนี้

      // ตรวจสอบสลิปซ้ำเฉพาะของผู้ใช้ปัจจุบันเท่านั้น (ไม่ตรวจสอบกับสลิปของผู้ใช้คนอื่น)
      // โดยดูจาก qrData เป็นหลัก (รหัส QR ในสลิป)
      const [existingVerification] = await db
        .select()
        .from(slipVerifications)
        .where(
          and(
            // ตรวจสอบเฉพาะสลิปของผู้ใช้นี้เท่านั้น
            eq(slipVerifications.userId, userId),
            // ตรวจสอบว่า qrData ตรงกับสลิปที่เคยตรวจสอบไปแล้วหรือไม่
            eq(slipVerifications.qrData, qrData)
          )
        )
        .orderBy(desc(slipVerifications.createdAt))
        .limit(1);

      if (existingVerification) {
        const thaiDateTime = new Date(existingVerification.createdAt).toLocaleString('th-TH', { timeZone: 'Asia/Bangkok' });
        logger.debug(`❗ พบสลิปซ้ำของผู้ใช้ ${userId}: สลิป ${qrData} เคยตรวจสอบเมื่อ ${thaiDateTime}`);
        return { isDuplicate: true, existingVerification };
      }

      // ไม่พบสลิปซ้ำ
      logger.debug(`✅ ไม่พบสลิปซ้ำของผู้ใช้ ${userId} สำหรับ QR: ${qrData.substring(0, 20)}...`);
      return { isDuplicate: false };
    } catch (error) {
      logger.error(`เกิดข้อผิดพลาดในการตรวจสอบสลิปซ้ำสำหรับผู้ใช้ ${userId}:`, error);
      return { isDuplicate: false };
    }
  }

  // Verify slip image using configured API provider
  async verifySlip(userId: number, fileBuffer: Buffer, fileName: string, skipDuplicateCheck: boolean = false): Promise<VerifySlipResponse> {
    // Get user's active package
    const userPackage = await storage.getUserActivePackage(userId);
    if (!userPackage) {
      throw new SlipServiceError('ไม่พบแพ็กเกจที่ใช้งานได้ กรุณาสมัครแพ็กเกจก่อนใช้งาน', 403);
    }

    // Check if user has remaining quota
    // ไม่ต้องตรวจสอบโควต้าในส่วนนี้ เพราะการตรวจสอบและการจัดการกรณีเกินโควต้า
    // ถูกจัดการที่ middleware checkUserPackage ในไฟล์ server/api/slip.ts แล้ว
    // เมื่อโควต้าหมดแล้วจะให้ใช้เครดิตแทน
    /*
    if (userPackage.requestsUsed >= userPackage.package.requestsLimit) {
      throw new SlipServiceError('คุณได้ใช้โควต้าเต็มจำนวนแล้ว กรุณาอัปเกรดแพ็กเกจ', 403);
    }
    */

    // ตรวจสอบว่ามี QR Code หรือไม่ก่อนส่งไปยัง API
    const { logger } = await import('./logger');
    logger.debug('🔍 ตรวจสอบ QR code ในรูปภาพก่อนส่งไปยัง API');

    try {
      // ทำการตรวจสอบว่ามี QR Code หรือไม่
      const result = await detectQRCode(fileBuffer);

      // ถ้าไม่พบ QR Code ให้ปฏิเสธการตรวจสอบทันที
      if (!result.hasQRCode) {
        logger.debug('❌ ไม่พบ QR code ในรูปภาพ - ปฏิเสธการตรวจสอบ');
        return {
          status: 400,
          message: 'ไม่พบ QR Code ในรูปภาพ กรุณาตรวจสอบว่าเป็นรูปสลิปธนาคารที่ถูกต้อง'
        };
      }

      // พบ QR Code แล้ว แสดงข้อมูลที่อ่านได้ (แสดงบางส่วนถ้ามีข้อมูลมาก)
      if (result.qrData) {
        const qrPreview = result.qrData.length > 40
          ? result.qrData.substring(0, 40) + '...'
          : result.qrData;
        logger.debug(`✅ พบ QR code ในรูปภาพ: ${qrPreview}`);

        // ตรวจสอบสลิปซ้ำเฉพาะเมื่อเป็นการตรวจสอบผ่านหน้าเว็บไซต์ (ไม่ใช่ API) และไม่ได้ขอข้ามการตรวจสอบ
        if (!skipDuplicateCheck) {
          const { isDuplicate, existingVerification } = await this.checkDuplicateSlip(userId, result.qrData, true);
          if (isDuplicate && existingVerification) {
            logger.debug('❌ พบสลิปซ้ำ - ปฏิเสธการตรวจสอบ');
            return {
              status: 409, // Conflict
              message: `สลิปฉบับนี้ถูกตรวจสอบไปแล้วเมื่อ ${new Date(existingVerification.createdAt).toLocaleString('th-TH', { timeZone: 'Asia/Bangkok' })}`,
              data: {
                transactionRef: existingVerification.transactionRef,
                amount: existingVerification.amount,
                verifiedAt: existingVerification.createdAt
              }
            };
          }
        } else {
          logger.debug('🔄 ข้ามการตรวจสอบสลิปซ้ำตามการตั้งค่าที่กำหนด');
        }
      } else {
        logger.debug('✅ พบ QR code ในรูปภาพ: ตรวจพบ QR code แต่ไม่สามารถอ่านข้อมูลได้');
      }
    } catch (error) {
      logger.error(`❌ เกิดข้อผิดพลาดในการตรวจสอบ QR code: ${error instanceof Error ? error.message : 'ไม่ทราบสาเหตุ'}`);
      // กรณีเกิดข้อผิดพลาดในการตรวจสอบ QR Code ให้ปฏิเสธการตรวจสอบ
      return {
        status: 400,
        message: 'เกิดข้อผิดพลาดในการตรวจสอบ QR Code กรุณาตรวจสอบรูปภาพสลิป'
      };
    }

    // ตรวจสอบเฉพาะ format ของไฟล์ภาพและขนาดไฟล์เพื่อดูว่าเป็นไฟล์รูปภาพที่ถูกต้องหรือไม่
    // ไม่ตรวจสอบเนื้อหาของรูปภาพว่ามี QR Code หรือไม่

    // เริ่มจับเวลาการทำงาน
    const startTime = Date.now();
    let response: VerifySlipResponse;

    // ตรวจสอบ API provider ที่ต้องการใช้งาน
    const apiProvider = await this.getApiProvider();
    logger.debug(`🔄 API: ใช้ผู้ให้บริการ API ${apiProvider}`);

    let usedFallback = false;

    try {
      if (apiProvider === 'easyslip') {
        // ใช้ EasySlip API
        logger.debug('🔄 ใช้ EasySlip API สำหรับการตรวจสอบ');
        response = await this.verifyWithEasySlip(fileBuffer, fileName);
        // เพิ่ม apiProvider เข้าไปใน response
        response = { ...response, apiProvider: 'easyslip' };
      } else {
        // ใช้ SLIPKUY API (ค่าเริ่มต้น)
        logger.debug('🔄 ใช้ SLIPKUY API สำหรับการตรวจสอบ');
        response = await this.verifyWithSlip2Go(fileBuffer, fileName);
        // เพิ่ม apiProvider เข้าไปใน response
        response = { ...response, apiProvider: 'slipkuy' };
      }
    } catch (error) {
      logger.error(`❌ เกิดข้อผิดพลาดในการเรียกใช้ API ${apiProvider}: ${error instanceof Error ? error.message : 'ไม่ทราบสาเหตุ'}`);

      // แทนที่จะลองใช้อีก API โดยอัตโนมัติให้แจ้งผู้ใช้เมื่อเกิดข้อผิดพลาด
      throw new SlipServiceError(`เกิดข้อผิดพลาดในการตรวจสอบผ่าน ${apiProvider}`, 500);

      /*
      // ปิดการทำงานแบบ fallback เพื่อป้องกันการบันทึกซ้ำซ้อน
      // คอมเมนต์ไว้หากต้องการเปิดใช้งานในอนาคต
      console.log('Primary API failed, trying fallback API');
      try {
        usedFallback = true;
        if (apiProvider === 'easyslip') {
          // ถ้าล้มเหลวจาก EasySlip ให้ลองใช้ SLIPKUY
          response = await this.verifyWithSlip2Go(fileBuffer, fileName);
          // เพิ่ม apiProvider เข้าไปใน response
          response = { ...response, apiProvider: 'slipkuy' };
        } else {
          // ถ้าล้มเหลวจาก SLIPKUY ให้ลองใช้ EasySlip
          response = await this.verifyWithEasySlip(fileBuffer, fileName);
          // เพิ่ม apiProvider เข้าไปใน response
          response = { ...response, apiProvider: 'easyslip' };
        }
      } catch (fallbackError) {
        // ถ้าทั้งสอง API ล้มเหลว ให้ส่งข้อผิดพลาดกลับไป
        throw new SlipServiceError('ไม่สามารถตรวจสอบสลิปได้ กรุณาลองใหม่อีกครั้ง', 500);
      }
      */
    }

    const endTime = Date.now();
    const responseTime = endTime - startTime;

    // ไม่ต้องบันทึกผลการตรวจสอบสลิปลงในฐานข้อมูลที่นี่
    // เนื่องจากมีการบันทึกอยู่แล้วในไฟล์ server/api/slip.ts
    // เอาส่วนการเพิ่มการใช้งาน API ออกด้วย เพราะมีการเรียกใช้ใน server/api/slip.ts แล้ว

    return response;
  }

  // Express middleware to handle API key authentication
  static apiKeyAuth = async (req: Request, res: Response, next: Function) => {
    const apiKey = req.headers.authorization?.split(' ')[1];

    if (!apiKey) {
      return res.status(401).json({
        status: 401,
        message: 'กรุณาระบุ API Key ของคุณ'
      });
    }

    try {
      const user = await storage.getUserByApiKey(apiKey);

      if (!user) {
        return res.status(401).json({
          status: 401,
          message: 'API Key ไม่ถูกต้อง'
        });
      }

      // Attach user to request object
      (req as any).user = user;
      next();
    } catch (error) {
      res.status(500).json({
        status: 500,
        message: 'เกิดข้อผิดพลาดในการตรวจสอบสิทธิ์'
      });
    }
  };
}

export const slipService = new SlipService();
