import { Request, Response, Router } from "express";
import crypto from 'crypto';
import { logger } from "../logger";
import { lineMessagingService } from "../line-messaging-service";
import { socialAuthService } from "../social-auth-service";
import { storage } from "../storage";
import { slipService } from "../slip-service";
import multer from 'multer';
import path from 'path';
import fs from 'fs';

const router = Router();

// สร้างโฟลเดอร์สำหรับเก็บรูปภาพสลิปจาก LINE
const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'line');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// ตั้งค่า Multer สำหรับอัปโหลดไฟล์
const storage_line = multer.diskStorage({
  destination: (_req, _file, cb) => {
    cb(null, uploadDir);
  },
  filename: (_req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'line-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage_line,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB
  },
  fileFilter: (_req, file, cb) => {
    const allowedMimes = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp'];
    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('ไฟล์ไม่ถูกต้อง (รองรับเฉพาะ JPG, JPEG, PNG และ WEBP)'));
    }
  }
});

/**
 * Webhook สำหรับรับข้อมูลจาก LINE Messaging API
 * ใช้สำหรับรับข้อความและรูปภาพสลิปจากผู้ใช้ผ่าน LINE OA
 */
router.post('/webhook', async (req: Request, res: Response) => {
  try {
    // ตรวจสอบว่า LINE OA ได้ถูกตั้งค่าแล้วหรือไม่
    const { isConfigured, settings } = await lineMessagingService.loadSettings();
    
    if (!isConfigured) {
      logger.error('[LINE Webhook] LINE OA ยังไม่ได้ตั้งค่า');
      return res.status(500).send('LINE OA Configuration Not Found');
    }
    
    // ตรวจสอบ signature จาก LINE
    const signature = req.headers['x-line-signature'] as string;
    if (!signature) {
      logger.error('[LINE Webhook] ไม่พบ X-Line-Signature header');
      return res.status(401).send('Invalid Signature');
    }
    
    // คำนวณ signature เพื่อตรวจสอบความถูกต้อง
    const body = JSON.stringify(req.body);
    const hmac = crypto.createHmac('sha256', settings.lineChannelSecret || '');
    const digest = hmac.update(body).digest('base64');
    
    if (digest !== signature) {
      logger.error('[LINE Webhook] Signature ไม่ถูกต้อง');
      return res.status(401).send('Invalid Signature');
    }
    
    // ตรวจสอบและประมวลผลข้อมูลที่ได้รับจาก LINE
    const events = req.body.events || [];
    
    for (const event of events) {
      await processLineEvent(event);
    }
    
    res.status(200).send('OK');
  } catch (error) {
    logger.error('[LINE Webhook] เกิดข้อผิดพลาด:', error);
    res.status(500).send('Internal Server Error');
  }
});

/**
 * ประมวลผลข้อมูล event ที่ได้รับจาก LINE
 */
async function processLineEvent(event: any) {
  try {
    const { type, source, message, replyToken } = event;
    
    if (type !== 'message') {
      logger.info(`[LINE Webhook] ได้รับ event ที่ไม่ใช่ message: ${type}`);
      return;
    }
    
    const lineUserId = source.userId;
    if (!lineUserId) {
      logger.error('[LINE Webhook] ไม่พบ userId ใน event');
      return;
    }
    
    // ตรวจสอบว่าผู้ใช้นี้มีบัญชีในระบบหรือไม่
    const userId = await lineMessagingService.getUserIdFromLineId(lineUserId);
    if (!userId) {
      // ผู้ใช้ยังไม่มีบัญชีในระบบ
      await lineMessagingService.sendTextMessage(
        lineUserId,
        'คุณยังไม่ได้เชื่อมต่อบัญชีกับระบบ SLIPKUY\nกรุณาล็อกอินผ่านเว็บไซต์ด้วยบัญชี LINE ของคุณก่อนใช้งาน'
      );
      return;
    }
    
    switch (message.type) {
      case 'text':
        await handleTextMessage(userId, lineUserId, message.text, replyToken);
        break;
      case 'image':
        await handleImageMessage(userId, lineUserId, message.id, replyToken);
        break;
      default:
        logger.info(`[LINE Webhook] ได้รับข้อความชนิดที่ไม่รองรับ: ${message.type}`);
        await lineMessagingService.sendTextMessage(
          lineUserId,
          'ขออภัย ระบบรองรับเฉพาะข้อความและรูปภาพสลิปเท่านั้น'
        );
        break;
    }
  } catch (error) {
    logger.error('[LINE Webhook] ไม่สามารถประมวลผล event ได้:', error);
  }
}

/**
 * จัดการข้อความจาก LINE
 */
async function handleTextMessage(userId: number, lineUserId: string, text: string, replyToken: string) {
  try {
    // รายการคำสั่งที่รองรับ
    const text_lower = text.toLowerCase().trim();
    
    if (text_lower === 'credit' || text_lower === 'เครดิต') {
      // สำหรับการตรวจสอบเครดิต
      await lineMessagingService.sendCreditBalance(userId);
    }
    else if (text_lower === 'help' || text_lower === 'ช่วยเหลือ') {
      // แสดงคำสั่งที่รองรับ
      await lineMessagingService.sendTextMessage(
        lineUserId,
        `
คำสั่งที่รองรับ:
• credit หรือ เครดิต - ตรวจสอบยอดเครดิตคงเหลือ
• help หรือ ช่วยเหลือ - แสดงคำสั่งที่รองรับ

หากต้องการตรวจสอบสลิป กรุณาส่งรูปภาพสลิปโดยตรง
        `.trim()
      );
    }
    else {
      // ข้อความที่ไม่รองรับ
      await lineMessagingService.sendTextMessage(
        lineUserId,
        `ขออภัย ระบบไม่รองรับคำสั่งนี้ หากต้องการทราบคำสั่งที่รองรับ โปรดพิมพ์ "help" หรือ "ช่วยเหลือ"`
      );
    }
  } catch (error) {
    logger.error('[LINE Webhook] ไม่สามารถจัดการข้อความได้:', error);
  }
}

/**
 * จัดการรูปภาพสลิปจาก LINE
 */
async function handleImageMessage(userId: number, lineUserId: string, messageId: string, replyToken: string) {
  try {
    // ตรวจสอบเครดิตของผู้ใช้
    const userCredit = await storage.getUserCredit(userId);
    if (!userCredit || userCredit.credit <= 0) {
      await lineMessagingService.sendTextMessage(
        lineUserId,
        '⚠️ เครดิตของคุณไม่เพียงพอสำหรับการตรวจสอบสลิป กรุณาเติมเครดิตผ่านเว็บไซต์'
      );
      return;
    }
    
    // ดึงรูปภาพจาก LINE
    const { isConfigured, settings } = await lineMessagingService.loadSettings();
    if (!isConfigured || !settings.lineChannelAccessToken) {
      throw new Error('LINE OA ยังไม่ได้ตั้งค่า');
    }
    
    // แจ้งผู้ใช้ว่ากำลังประมวลผล
    await lineMessagingService.sendTextMessage(
      lineUserId,
      '🔍 กำลังตรวจสอบสลิป โปรดรอสักครู่...'
    );
    
    // อัปโหลดรูปภาพสลิปเข้าระบบ
    const randomFilename = `line-${Date.now()}-${Math.round(Math.random() * 1E9)}.jpg`;
    const filePath = path.join(uploadDir, randomFilename);
    
    // สร้างข้อมูลการตรวจสอบแบบ pending ไว้ก่อน
    const verification = await storage.createVerification({
      userId,
      status: 'pending',
      amount: 0,
      transactionRef: `LINE-${messageId}`,
      bankName: 'LINE',
      sender: '',
      receiver: '',
      accountNumber: '',
      transactionDate: new Date(),
      imageUrl: `/uploads/line/${randomFilename}`,
      slipData: '',
      qrData: '',
      apiKeyId: null
    });
    
    // ดึงรูปภาพสลิป
    const response = await fetch(`https://api-data.line.me/v2/bot/message/${messageId}/content`, {
      headers: {
        'Authorization': `Bearer ${settings.lineChannelAccessToken}`
      }
    });
    
    if (!response.ok) {
      throw new Error(`[LINE Webhook] ไม่สามารถดึงรูปภาพได้: ${response.status} ${response.statusText}`);
    }
    
    const buffer = Buffer.from(await response.arrayBuffer());
    fs.writeFileSync(filePath, buffer);
    
    // ตรวจสอบสลิป
    const slipResult = await slipService.verifySlip({
      userId,
      slipImage: `/uploads/line/${randomFilename}`,
      apiKeyId: null,
      amount: null,
      verificationId: verification.id
    });
    
    if (slipResult.success) {
      // อัปเดตข้อมูลการตรวจสอบ
      await storage.updateVerification(verification.id, {
        status: 'completed',
        amount: slipResult.data?.amount || 0,
        transactionRef: slipResult.data?.transactionRef || `LINE-${messageId}`,
        bankName: slipResult.data?.bankName || 'ไม่ทราบ',
        sender: slipResult.data?.sender || '',
        receiver: slipResult.data?.receiver || '',
        accountNumber: slipResult.data?.accountNumber || '',
        transactionDate: slipResult.data?.transactionDate || new Date(),
        slipData: JSON.stringify(slipResult.data),
        qrData: slipResult.data?.qrData || ''
      });
      
      // ส่งผลการตรวจสอบกลับไปยังผู้ใช้
      const amountText = new Intl.NumberFormat('th-TH', { style: 'currency', currency: 'THB' }).format(slipResult.data?.amount || 0);
      const message = `
✅ ตรวจสอบสลิปสำเร็จ
💰 จำนวนเงิน: ${amountText}
🏦 ธนาคาร: ${slipResult.data?.bankName || 'ไม่ทราบ'}
📅 วันที่: ${new Date(slipResult.data?.transactionDate || new Date()).toLocaleString('th-TH')}
🧾 เลขอ้างอิง: ${slipResult.data?.transactionRef || 'ไม่ทราบ'}
      `.trim();
      
      await lineMessagingService.sendTextMessage(lineUserId, message);
    } else {
      // อัปเดตข้อมูลการตรวจสอบว่าล้มเหลว
      await storage.updateVerification(verification.id, {
        status: 'failed',
        slipData: JSON.stringify({ error: slipResult.message })
      });
      
      // ส่งข้อความแจ้งเตือนว่าตรวจสอบไม่สำเร็จ
      await lineMessagingService.sendTextMessage(
        lineUserId,
        `
❌ ตรวจสอบสลิปไม่สำเร็จ
สาเหตุ: ${slipResult.message || 'ไม่พบข้อมูลในสลิป'}

โปรดตรวจสอบว่าภาพถ่ายชัดเจนและเป็นสลิปที่ถูกต้อง
        `.trim()
      );
    }
  } catch (error) {
    logger.error('[LINE Webhook] ไม่สามารถจัดการรูปภาพได้:', error);
    await lineMessagingService.sendTextMessage(
      lineUserId,
      '❌ เกิดข้อผิดพลาดในการตรวจสอบสลิป กรุณาลองใหม่อีกครั้ง'
    );
  }
}

export default router;