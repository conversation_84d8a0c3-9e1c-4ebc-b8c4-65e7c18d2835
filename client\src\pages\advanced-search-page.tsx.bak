import { useState, useEffect } from "react";
import { DashboardLayout } from "@/components/layouts/dashboard-layout";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { th } from 'date-fns/locale';
import { CalendarIcon, BarChart3, TrendingUp, AlertCircle, AreaChart, History, Search, Download } from "lucide-react";
import { 
  LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, 
  Tooltip as RechartsTooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell 
} from 'recharts';
import Papa from 'papaparse';
import { saveAs } from 'file-saver';
import { useQuery } from "@tanstack/react-query";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

export default function AdvancedSearchPage() {
  // States for UI
  const [activeTab, setActiveTab] = useState("analyze");
  
  // State for analyze tab
  const [analysisType, setAnalysisType] = useState("usage_summary");
  const [timeframe, setTimeframe] = useState("30days");
  const [fromDate, setFromDate] = useState<Date | undefined>(undefined);
  const [toDate, setToDate] = useState<Date | undefined>(undefined);
  const [includeDetails, setIncludeDetails] = useState(false);
  const [analysisResults, setAnalysisResults] = useState<any>(null);
  const [isLoadingAnalysis, setIsLoadingAnalysis] = useState(false);
  
  // State for correlation tab
  const [correlationType, setCorrelationType] = useState("transaction_patterns");
  const [correlationTimeframe, setCorrelationTimeframe] = useState("90days");
  const [correlationResults, setCorrelationResults] = useState<any>(null);
  const [isLoadingCorrelation, setIsLoadingCorrelation] = useState(false);
  
  // State for prediction tab
  const [predictionType, setPredictionType] = useState("usage_forecast");
  const [predictionTimeframe, setPredictionTimeframe] = useState("90days");
  const [predictionResults, setPredictionResults] = useState<any>(null);
  const [isLoadingPrediction, setIsLoadingPrediction] = useState(false);
  
  const { toast } = useToast();
  
  // CSV Export Functions
  const exportToCSV = (data: any, filename: string) => {
    // เพิ่มข้อมูลบริษัทและหัวเอกสาร
    const documentInfo = [
      ['รายงานการวิเคราะห์ข้อมูลขั้นสูง - SLIPKUY PRM ANALYTICS SYSTEM'],
      ['บริษัท SLIPKUY จำกัด'],
      ['วันที่สร้าง:', format(new Date(), 'PPP', { locale: th })],
      ['เวลาที่สร้าง:', format(new Date(), 'HH:mm:ss', { locale: th }), 'น.'],
      ['ผู้สร้างรายงาน:', 'ระบบ SLIPKUY PRM Analytics'],
      ['เลขที่อ้างอิง:', `SLIPKUY-REPORT-${format(new Date(), 'yyyyMMdd-HHmmss')}`],
      [''],
    ];
    
    let csvContent: any[] = [];
    csvContent = [...documentInfo];
    
    // สร้างฟังก์ชันที่ใช้เก็บข้อมูลจากแต่ละแท็บเพื่อรวมในไฟล์เดียว
    const addAnalysisData = () => {
      if (!analysisResults) return [];
      
      const timeframeText = fromDate && toDate 
        ? `${format(fromDate, 'PPP', { locale: th })} ถึง ${format(toDate, 'PPP', { locale: th })}`
        : timeframe === '7days' ? '7 วันล่าสุด'
        : timeframe === '30days' ? '30 วันล่าสุด'
        : timeframe === '90days' ? '90 วันล่าสุด'
        : timeframe === '1year' ? '1 ปีล่าสุด'
        : 'ทั้งหมด';
      
      const analysisTitle = analysisType === 'usage_summary' ? 'สรุปการใช้งาน'
        : analysisType === 'bank_analysis' ? 'วิเคราะห์ตามธนาคาร'
        : analysisType === 'time_analysis' ? 'วิเคราะห์ตามเวลา'
        : analysisType === 'transaction_patterns' ? 'รูปแบบธุรกรรม'
        : 'การใช้งาน API';
      
      let analysisData = [
        ['ส่วนที่ 1: ผลการวิเคราะห์ข้อมูล'],
        ['รายงาน:', analysisTitle],
        ['ช่วงเวลา:', timeframeText],
        [''],
        ['สรุปผลการวิเคราะห์'],
        ['จำนวนรายการทั้งหมด', analysisResults.results.totalVerifications || 0],
        ['จำนวนเงินรวม', `${analysisResults.results.totalAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`],
        ['จำนวนเงินเฉลี่ยต่อรายการ', `${analysisResults.results.averageAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`],
        [''],
      ];
      
      // รายละเอียดตามประเภทการวิเคราะห์
      if (analysisType === 'bank_analysis' && analysisResults.results.bankDistribution) {
        analysisData.push(['การวิเคราะห์ตามธนาคาร'], ['ธนาคาร', 'จำนวนรายการ', 'เปอร์เซ็นต์', 'จำนวนเงินรวม'], ['']);
        
        analysisResults.results.bankDistribution.forEach((bank: any) => {
          analysisData.push([
            bank.name || 'ไม่ระบุ',
            bank.count,
            `${bank.percentage.toFixed(2)}%`,
            `${bank.totalAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`
          ]);
        });
      }
      
      // เพิ่มรายการต่างๆ ตามประเภทข้อมูล
      if (analysisType === 'time_analysis' && analysisResults.results.timeDistribution) {
        analysisData.push(['การวิเคราะห์ตามช่วงเวลา'], ['ช่วงเวลา', 'จำนวนรายการ', 'เปอร์เซ็นต์', 'จำนวนเงินรวม'], ['']);
        
        analysisResults.results.timeDistribution.forEach((time: any) => {
          analysisData.push([
            time.time || 'ไม่ระบุ',
            time.count,
            `${time.percentage.toFixed(2)}%`,
            `${time.totalAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`
          ]);
        });
      }
      
      // รายการทั้งหมด (ถ้ามี)
      if (includeDetails && analysisResults.transactions && analysisResults.transactions.length > 0) {
        analysisData.push([''], ['รายการทั้งหมด'], ['วันที่', 'เวลา', 'จำนวนเงิน', 'ธนาคารผู้รับ', 'ผู้รับ', 'สถานะ'], ['']);
        
        analysisResults.transactions.forEach((tx: any) => {
          const date = tx.createdAt ? new Date(tx.createdAt) : null;
          analysisData.push([
            date ? format(date, 'PPP', { locale: th }) : '-',
            date ? format(date, 'HH:mm', { locale: th }) : '-',
            `${tx.amount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`,
            tx.bankName || '-',
            tx.receiver || '-',
            tx.status || '-'
          ]);
        });
      }
      
      return analysisData;
    };
    
    const addCorrelationData = () => {
      if (!correlationResults) return [];
      
      let correlationData = [
        [''],
        ['ส่วนที่ 2: การวิเคราะห์ความสัมพันธ์'],
        ['ประเภท:', correlationType === 'transaction_patterns' ? 'รูปแบบการทำรายการ' : 'ตรวจจับความผิดปกติ'],
        ['ช่วงเวลา:', correlationTimeframe === '30days' ? '30 วันล่าสุด' : 
                    correlationTimeframe === '90days' ? '90 วันล่าสุด' : 
                    correlationTimeframe === '180days' ? '180 วันล่าสุด' : '1 ปีล่าสุด'],
        [''],
        ['สรุปผลการวิเคราะห์ความสัมพันธ์'],
      ];
      
      if (correlationType === 'transaction_patterns') {
        correlationData.push(
          ['รูปแบบการทำรายการซ้ำที่พบ', correlationResults.results.totalPatterns || 0],
          [''],
          ['รายละเอียดรูปแบบการทำรายการ'],
          ['ผู้รับ', 'จำนวนรายการ', 'ความถี่', 'จำนวนเงินเฉลี่ย'],
          ['']
        );
        
        if (correlationResults.results.patterns && correlationResults.results.patterns.length > 0) {
          correlationResults.results.patterns.forEach((pattern: any) => {
            correlationData.push([
              pattern.receiver || 'ไม่ระบุ',
              pattern.count,
              pattern.frequency || '-',
              `${pattern.averageAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`,
            ]);
            
            if (includeDetails && pattern.transactions && pattern.transactions.length > 0) {
              correlationData.push([''], ['รายการที่เกี่ยวข้อง'], ['วันที่', 'จำนวนเงิน', 'ธนาคาร'], ['']);
              
              pattern.transactions.forEach((tx: any) => {
                correlationData.push([
                  tx.date ? format(new Date(tx.date), 'PPP', { locale: th }) : '-',
                  `${tx.amount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`,
                  tx.bank || '-',
                ]);
              });
              
              correlationData.push(['']);
            }
          });
        }
      } else if (correlationType === 'anomaly_detection') {
        correlationData.push(
          ['จำนวนรายการผิดปกติที่พบ', correlationResults.results.totalAnomalies || 0],
          ['จำนวนเงินเฉลี่ย', `${correlationResults.results.amountStatistics?.average?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`],
          ['ช่วงปกติ', `${correlationResults.results.amountStatistics?.normalRange.min?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} - ${correlationResults.results.amountStatistics?.normalRange.max?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`],
          [''],
        );
        
        if (correlationResults.results.amountAnomalies && correlationResults.results.amountAnomalies.length > 0) {
          correlationData.push(['รายการที่มีจำนวนเงินผิดปกติ'], ['วันที่', 'จำนวนเงิน', 'ผู้รับ', 'ธนาคาร'], ['']);
          
          correlationResults.results.amountAnomalies.forEach((anomaly: any) => {
            correlationData.push([
              anomaly.date ? format(new Date(anomaly.date), 'PPP', { locale: th }) : '-',
              `${anomaly.amount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`,
              anomaly.receiver || '-',
              anomaly.bank || '-',
            ]);
          });
          
          correlationData.push(['']);
        }
        
        if (correlationResults.results.timeAnomalies && correlationResults.results.timeAnomalies.length > 0) {
          correlationData.push(['รายการที่เกิดขึ้นในเวลาผิดปกติ'], ['วันที่', 'เวลา', 'จำนวนเงิน', 'ผู้รับ'], ['']);
          
          correlationResults.results.timeAnomalies.forEach((anomaly: any) => {
            correlationData.push([
              anomaly.date ? format(new Date(anomaly.date), 'P', { locale: th }) : '-',
              anomaly.date ? format(new Date(anomaly.date), 'HH:mm', { locale: th }) + ' น.' : '-',
              `${anomaly.amount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`,
              anomaly.receiver || '-',
            ]);
          });
        }
      }
      
      return correlationData;
    };
    
    const addPredictionData = () => {
      if (!predictionResults) return [];
      
      let predictionData = [
        [''],
        ['ส่วนที่ 3: การคาดการณ์ข้อมูลในอนาคต'],
        ['ประเภท:', predictionType === 'usage_forecast' ? 'คาดการณ์การใช้งาน' : 
                  predictionType === 'amount_prediction' ? 'คาดการณ์จำนวนเงิน' : 'คาดการณ์รายการประจำ'],
        ['ข้อมูลที่ใช้วิเคราะห์:', predictionTimeframe === '30days' ? '30 วันล่าสุด' : 
                            predictionTimeframe === '90days' ? '90 วันล่าสุด' : 
                            predictionTimeframe === '180days' ? '180 วันล่าสุด' : '1 ปีล่าสุด'],
        [''],
        ['ผลการคาดการณ์'],
      ];
      
      if (predictionType === 'usage_forecast') {
        predictionData.push(
          ['คาดการณ์ 7 วันข้างหน้า', `${predictionResults.predictions.forecasts?.next7Days || 0} รายการ`],
          ['คาดการณ์ 30 วันข้างหน้า', `${predictionResults.predictions.forecasts?.next30Days || 0} รายการ`],
          ['คาดการณ์ 90 วันข้างหน้า', `${predictionResults.predictions.forecasts?.next90Days || 0} รายการ`],
          ['การใช้งานเฉลี่ย', `${predictionResults.predictions.historicalData?.averageDailyUsage?.toFixed(2) || 0} รายการ/วัน`],
          [''],
        );
        
        if (predictionResults.predictions.monthlyForecasts && predictionResults.predictions.monthlyForecasts.length > 0) {
          predictionData.push(['คาดการณ์รายเดือน'], ['เดือน', 'จำนวนคาดการณ์', 'การเปลี่ยนแปลง (%)'], ['']);
          
          predictionResults.predictions.monthlyForecasts.forEach((month: any) => {
            predictionData.push([
              month.month || '-',
              month.forecast,
              `${month.changePercentage > 0 ? '+' : ''}${month.changePercentage.toFixed(2)}%`,
            ]);
          });
          
          predictionData.push(['']);
        }
        
        if (includeDetails && predictionResults.predictions.historicalData && predictionResults.predictions.historicalData.weeklyUsage) {
          predictionData.push(['ข้อมูลการใช้งานรายสัปดาห์'], ['สัปดาห์', 'จำนวนรายการ'], ['']);
          
          Object.entries(predictionResults.predictions.historicalData.weeklyUsage).forEach(([week, count]: [string, any]) => {
            predictionData.push([`สัปดาห์ที่ ${week}`, count]);
          });
        }
      } else if (predictionType === 'amount_prediction') {
        predictionData.push(
          ['คาดการณ์จำนวนเงินเฉลี่ย 30 วันข้างหน้า', `${predictionResults.predictions.amountForecasts?.next30DaysAvg?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`],
          ['คาดการณ์จำนวนเงินรวม 30 วันข้างหน้า', `${predictionResults.predictions.amountForecasts?.next30DaysTotal?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`],
          ['แนวโน้มเทียบกับปัจจุบัน', `${predictionResults.predictions.amountForecasts?.trend > 0 ? '+' : ''}${predictionResults.predictions.amountForecasts?.trend?.toFixed(2) || 0}%`],
          [''],
        );
        
        if (predictionResults.predictions.monthlyAmountForecasts && predictionResults.predictions.monthlyAmountForecasts.length > 0) {
          predictionData.push(['คาดการณ์จำนวนเงินรายเดือน'], ['เดือน', 'จำนวนเงินเฉลี่ย', 'จำนวนเงินรวม', 'การเปลี่ยนแปลง (%)'], ['']);
          
          predictionResults.predictions.monthlyAmountForecasts.forEach((month: any) => {
            predictionData.push([
              month.month || '-',
              `${month.averageAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`,
              `${month.totalAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`,
              `${month.changePercentage > 0 ? '+' : ''}${month.changePercentage.toFixed(2)}%`,
            ]);
          });
        }
      } else if (predictionType === 'recurring_transactions') {
        predictionData.push(
          ['รายการที่คาดว่าจะเกิดซ้ำที่พบ', predictionResults.predictions.recurringTransactions?.count || 0],
          [''],
          ['รายละเอียดรายการที่คาดว่าจะเกิดซ้ำ'],
          ['ผู้รับ', 'จำนวนครั้งที่พบ', 'จำนวนเงินเฉลี่ย', 'ความถี่', 'วันที่คาดว่าจะเกิดครั้งถัดไป'],
          [''],
        );
        
        if (predictionResults.predictions.recurringTransactions && predictionResults.predictions.recurringTransactions.items) {
          predictionResults.predictions.recurringTransactions.items.forEach((item: any) => {
            predictionData.push([
              item.recipient || 'ไม่ระบุ',
              item.occurrences,
              `${item.averageAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`,
              item.frequency || '-',
              item.nextExpectedDate ? format(new Date(item.nextExpectedDate), 'PPP', { locale: th }) : '-'
            ]);
          });
        }
      }
      
      return predictionData;
    };
    
    // รวมข้อมูลจากทุกแท็บเข้าด้วยกัน ไม่ว่าจะอยู่ที่แท็บไหน
    const analysisData = analysisResults ? addAnalysisData() : [];
    const correlationData = correlationResults ? addCorrelationData() : [];
    const predictionData = predictionResults ? addPredictionData() : [];
    
    // รวมข้อมูลทั้งหมด
    csvContent = [
      ...csvContent,
      ...analysisData,
      ...correlationData,
      ...predictionData,
      [''],
      ['รายงานนี้ถูกสร้างโดยระบบ SLIPKUY PRM Analytics'],
      ['ข้อมูลในรายงานนี้เป็นกรรมสิทธิ์ของ บริษัท SLIPKUY จำกัด'],
      ['สงวนลิขสิทธิ์ © 2025 SLIPKUY']
    ];
    
    // ถ้าไม่มีข้อมูลในทุกแท็บให้แจ้งเตือน
    if (analysisData.length === 0 && correlationData.length === 0 && predictionData.length === 0) {
      toast({
        title: "ไม่พบข้อมูลสำหรับส่งออก",
        description: "กรุณาทำการวิเคราะห์ข้อมูลก่อนส่งออกไฟล์ CSV",
        variant: "destructive",
      });
      return;
    }
    
    const formattedCSV = Papa.unparse(csvContent, { delimiter: ',', header: false });
    const blob = new Blob(["\ufeff" + formattedCSV], {type: "text/csv;charset=utf-8"});
    saveAs(blob, filename);
  };
  
  // Analysis data
  if (activeTab === 'analyze') {
      const timeframeText = fromDate && toDate 
        ? `${format(fromDate, 'PPP', { locale: th })} ถึง ${format(toDate, 'PPP', { locale: th })}`
        : timeframe === '7days' ? '7 วันล่าสุด'
        : timeframe === '30days' ? '30 วันล่าสุด'
        : timeframe === '90days' ? '90 วันล่าสุด'
        : timeframe === '1year' ? '1 ปีล่าสุด'
        : 'ทั้งหมด';
      
      const analysisTitle = analysisType === 'usage_summary' ? 'สรุปการใช้งาน'
        : analysisType === 'bank_analysis' ? 'วิเคราะห์ตามธนาคาร'
        : analysisType === 'time_analysis' ? 'วิเคราะห์ตามเวลา'
        : analysisType === 'transaction_patterns' ? 'รูปแบบธุรกรรม'
        : 'การใช้งาน API';
      
      documentInfo.push(
        ['รายงาน:', analysisTitle],
        ['ช่วงเวลา:', timeframeText],
        [''],
        ['สรุปผลการวิเคราะห์'],
      );
      
      if (analysisResults) {
        // สรุปข้อมูลเบื้องต้น
        csvContent = [
          ...documentInfo,
          ['จำนวนรายการทั้งหมด', analysisResults.results.totalVerifications || 0],
          ['จำนวนเงินรวม', `${analysisResults.results.totalAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`],
          ['จำนวนเงินเฉลี่ยต่อรายการ', `${analysisResults.results.averageAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`],
          [''],
        ];
        
        // รายละเอียดตามประเภทการวิเคราะห์
        if (analysisType === 'bank_analysis' && analysisResults.results.bankDistribution) {
          csvContent.push(['การวิเคราะห์ตามธนาคาร'], ['ธนาคาร', 'จำนวนรายการ', 'เปอร์เซ็นต์', 'จำนวนเงินรวม'], ['']);
          
          analysisResults.results.bankDistribution.forEach((bank: any) => {
            csvContent.push([
              bank.name || 'ไม่ระบุ',
              bank.count,
              `${bank.percentage.toFixed(2)}%`,
              `${bank.totalAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`
            ]);
          });
        }
        
        // เพิ่มรายการต่างๆ ตามประเภทข้อมูล
        if (analysisType === 'time_analysis' && analysisResults.results.timeDistribution) {
          csvContent.push(['การวิเคราะห์ตามช่วงเวลา'], ['ช่วงเวลา', 'จำนวนรายการ', 'เปอร์เซ็นต์', 'จำนวนเงินรวม'], ['']);
          
          analysisResults.results.timeDistribution.forEach((time: any) => {
            csvContent.push([
              time.time || 'ไม่ระบุ',
              time.count,
              `${time.percentage.toFixed(2)}%`,
              `${time.totalAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`
            ]);
          });
        }
        
        // รายการทั้งหมด (ถ้ามี)
        if (analysisResults.transactions && analysisResults.transactions.length > 0) {
          csvContent.push([''], ['รายการทั้งหมด'], ['วันที่', 'เวลา', 'จำนวนเงิน', 'ธนาคารผู้รับ', 'ผู้รับ', 'สถานะ'], ['']);
          
          analysisResults.transactions.forEach((tx: any) => {
            const date = tx.createdAt ? new Date(tx.createdAt) : null;
            csvContent.push([
              date ? format(date, 'PPP', { locale: th }) : '-',
              date ? format(date, 'HH:mm', { locale: th }) : '-',
              `${tx.amount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`,
              tx.bankName || '-',
              tx.receiver || '-',
              tx.status || '-'
            ]);
          });
        }
      }
    }
    // Correlation data
    else if (activeTab === 'correlation') {
      documentInfo.push(
        ['รายงาน:', 'วิเคราะห์ความสัมพันธ์'],
        ['ประเภท:', correlationType === 'transaction_patterns' ? 'รูปแบบการทำรายการ' : 'ตรวจจับความผิดปกติ'],
        ['ช่วงเวลา:', correlationTimeframe === '30days' ? '30 วันล่าสุด' : 
                     correlationTimeframe === '90days' ? '90 วันล่าสุด' : 
                     correlationTimeframe === '180days' ? '180 วันล่าสุด' : '1 ปีล่าสุด'],
        [''],
        ['สรุปผลการวิเคราะห์'],
      );
      
      if (correlationResults) {
        csvContent = [...documentInfo];
        
        if (correlationType === 'transaction_patterns') {
          csvContent.push(
            ['รูปแบบการทำรายการซ้ำที่พบ', correlationResults.results.totalPatterns || 0],
            [''],
            ['รายละเอียดรูปแบบการทำรายการ'],
            ['ผู้รับ', 'จำนวนรายการ', 'ความถี่', 'จำนวนเงินเฉลี่ย'],
            ['']
          );
          
          if (correlationResults.results.patterns && correlationResults.results.patterns.length > 0) {
            correlationResults.results.patterns.forEach((pattern: any) => {
              csvContent.push([
                pattern.receiver || 'ไม่ระบุ',
                pattern.count,
                pattern.frequency || '-',
                `${pattern.averageAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`,
              ]);
              
              if (pattern.transactions && pattern.transactions.length > 0) {
                csvContent.push([''], ['รายการที่เกี่ยวข้อง'], ['วันที่', 'จำนวนเงิน', 'ธนาคาร'], ['']);
                
                pattern.transactions.forEach((tx: any) => {
                  csvContent.push([
                    tx.date ? format(new Date(tx.date), 'PPP', { locale: th }) : '-',
                    `${tx.amount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`,
                    tx.bank || '-',
                  ]);
                });
                
                csvContent.push(['']);
              }
            });
          }
        } else if (correlationType === 'anomaly_detection') {
          csvContent.push(
            ['จำนวนรายการผิดปกติที่พบ', correlationResults.results.totalAnomalies || 0],
            ['จำนวนเงินเฉลี่ย', `${correlationResults.results.amountStatistics?.average?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`],
            ['ช่วงปกติ', `${correlationResults.results.amountStatistics?.normalRange.min?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} - ${correlationResults.results.amountStatistics?.normalRange.max?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`],
            [''],
          );
          
          if (correlationResults.results.amountAnomalies && correlationResults.results.amountAnomalies.length > 0) {
            csvContent.push(['รายการที่มีจำนวนเงินผิดปกติ'], ['วันที่', 'จำนวนเงิน', 'ผู้รับ', 'ธนาคาร'], ['']);
            
            correlationResults.results.amountAnomalies.forEach((anomaly: any) => {
              csvContent.push([
                anomaly.date ? format(new Date(anomaly.date), 'PPP', { locale: th }) : '-',
                `${anomaly.amount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`,
                anomaly.receiver || '-',
                anomaly.bank || '-',
              ]);
            });
            
            csvContent.push(['']);
          }
          
          if (correlationResults.results.timeAnomalies && correlationResults.results.timeAnomalies.length > 0) {
            csvContent.push(['รายการที่เกิดขึ้นในเวลาผิดปกติ'], ['วันที่', 'เวลา', 'จำนวนเงิน', 'ผู้รับ'], ['']);
            
            correlationResults.results.timeAnomalies.forEach((anomaly: any) => {
              csvContent.push([
                anomaly.date ? format(new Date(anomaly.date), 'P', { locale: th }) : '-',
                anomaly.date ? format(new Date(anomaly.date), 'HH:mm', { locale: th }) + ' น.' : '-',
                `${anomaly.amount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`,
                anomaly.receiver || '-',
              ]);
            });
          }
        }
      }
    }
    // Prediction data
    else if (activeTab === 'prediction') {
      documentInfo.push(
        ['รายงาน:', 'การคาดการณ์ข้อมูล'],
        ['ประเภท:', predictionType === 'usage_forecast' ? 'คาดการณ์การใช้งาน' : 
                   predictionType === 'amount_prediction' ? 'คาดการณ์จำนวนเงิน' : 'คาดการณ์รายการประจำ'],
        ['ข้อมูลที่ใช้วิเคราะห์:', predictionTimeframe === '30days' ? '30 วันล่าสุด' : 
                             predictionTimeframe === '90days' ? '90 วันล่าสุด' : 
                             predictionTimeframe === '180days' ? '180 วันล่าสุด' : '1 ปีล่าสุด'],
        [''],
        ['ผลการคาดการณ์'],
      );
      
      if (predictionResults) {
        csvContent = [...documentInfo];
        
        if (predictionType === 'usage_forecast') {
          csvContent.push(
            ['คาดการณ์ 7 วันข้างหน้า', `${predictionResults.predictions.forecasts?.next7Days || 0} รายการ`],
            ['คาดการณ์ 30 วันข้างหน้า', `${predictionResults.predictions.forecasts?.next30Days || 0} รายการ`],
            ['คาดการณ์ 90 วันข้างหน้า', `${predictionResults.predictions.forecasts?.next90Days || 0} รายการ`],
            ['การใช้งานเฉลี่ย', `${predictionResults.predictions.historicalData?.averageDailyUsage.toFixed(2) || 0} รายการ/วัน`],
            ['แนวโน้ม', `${predictionResults.predictions.historicalData?.usageTrend.trend || '-'} (${predictionResults.predictions.historicalData?.usageTrend.changePercent.toFixed(1) || 0}%)`],
            ['ระยะเวลาวิเคราะห์', `${predictionResults.timeframe.daysAnalyzed || 0} วัน`],
            ['จำนวนรายการทั้งหมด', predictionResults.predictions.historicalData?.totalTransactions || 0],
            [''],
          );
        } else if (predictionType === 'amount_prediction') {
          csvContent.push(
            ['คาดการณ์จำนวนเงินเฉลี่ยเดือนหน้า', `${predictionResults.predictions.predictedAmount?.nextMonth?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`],
            ['คาดการณ์จำนวนเงินเฉลี่ยไตรมาสหน้า', `${predictionResults.predictions.predictedAmount?.nextQuarter?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`],
            [''],
            ['ข้อมูลย้อนหลังรายเดือน'],
            ['เดือน', 'จำนวนรายการ', 'จำนวนเงินเฉลี่ย'],
            [''],
          );
          
          if (predictionResults.predictions.monthlyData && predictionResults.predictions.monthlyData.length > 0) {
            predictionResults.predictions.monthlyData.forEach((monthData: any) => {
              csvContent.push([
                monthData.month || '-',
                monthData.count,
                `${monthData.averageAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`,
              ]);
            });
          }
        } else if (predictionType === 'recurring_prediction') {
          csvContent.push(
            ['จำนวนรายการประจำที่คาดการณ์', predictionResults.predictions.recurringPredictions?.length || 0],
            [''],
          );
          
          if (predictionResults.predictions.recurringPredictions && predictionResults.predictions.recurringPredictions.length > 0) {
            predictionResults.predictions.recurringPredictions.forEach((prediction: any, idx: number) => {
              csvContent.push(
                [`รายการประจำที่ ${idx + 1}`],
                ['ผู้รับ', prediction.receiver || '-'],
                ['ความถี่', prediction.frequency || '-'],
                ['จำนวนเงินโดยเฉลี่ย', `${prediction.amount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`],
                ['วันที่คาดว่าจะเกิดขึ้นครั้งถัดไป', prediction.nextExpectedDate ? format(new Date(prediction.nextExpectedDate), 'PPP', { locale: th }) : '-'],
                [''],
                ['ประวัติรายการ'],
                ['วันที่', 'จำนวนเงิน', 'ธนาคาร'],
                ['']
              );
              
              if (prediction.historicalTransactions && prediction.historicalTransactions.length > 0) {
                prediction.historicalTransactions.forEach((t: any) => {
                  csvContent.push([
                    t.date ? format(new Date(t.date), 'P', { locale: th }) : '-',
                    `${t.amount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`,
                    t.bank || '-',
                  ]);
                });
              }
              
              csvContent.push(['']);
            });
          }
        }
      }
    }
    
    // Add footer
    csvContent.push(
      [''],
      ['รายงานนี้สร้างโดยระบบอัตโนมัติ'],
      ['© สงวนลิขสิทธิ์ บริษัท สลิป2โก คอร์ปอเรชั่น จำกัด']
    );
    
    const csv = Papa.unparse(csvContent);
    const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
    saveAs(blob, filename);
  };
  
  // Function to handle analyze request
  const handleAnalyze = async () => {
    setIsLoadingAnalysis(true);
    try {
      const response = await fetch('/api/advanced-search/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          analysisType,
          timeframe: fromDate && toDate ? undefined : timeframe,
          startDate: fromDate?.toISOString(),
          endDate: toDate?.toISOString(),
          includeDetails,
          groupBy: 'day',
        }),
        credentials: 'include',
      });
      
      if (!response.ok) {
        throw new Error('การวิเคราะห์ข้อมูลล้มเหลว โปรดลองใหม่อีกครั้ง');
      }
      
      const data = await response.json();
      setAnalysisResults(data);
      
      toast({
        title: "วิเคราะห์ข้อมูลสำเร็จ",
        description: `พบข้อมูล ${data.results?.totalVerifications || 0} รายการ`,
      });
    } catch (error) {
      console.error("Error analyzing data:", error);
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error instanceof Error ? error.message : "ไม่สามารถวิเคราะห์ข้อมูลได้",
        variant: "destructive",
      });
    } finally {
      setIsLoadingAnalysis(false);
    }
  };
  
  // Function to handle correlation request
  const handleCorrelation = async () => {
    setIsLoadingCorrelation(true);
    try {
      const response = await fetch('/api/advanced-search/correlations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          correlationType,
          timeframe: correlationTimeframe,
        }),
        credentials: 'include',
      });
      
      if (!response.ok) {
        throw new Error('การวิเคราะห์ความสัมพันธ์ล้มเหลว โปรดลองใหม่อีกครั้ง');
      }
      
      const data = await response.json();
      setCorrelationResults(data);
      
      toast({
        title: "วิเคราะห์ความสัมพันธ์สำเร็จ",
        description: `พบข้อมูลทั้งหมด ${data.totalTransactions || 0} รายการ`,
      });
    } catch (error) {
      console.error("Error analyzing correlations:", error);
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error instanceof Error ? error.message : "ไม่สามารถวิเคราะห์ความสัมพันธ์ได้",
        variant: "destructive",
      });
    } finally {
      setIsLoadingCorrelation(false);
    }
  };
  
  // Function to handle prediction request
  const handlePrediction = async () => {
    setIsLoadingPrediction(true);
    try {
      const response = await fetch('/api/advanced-search/predictions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          predictionType,
          timeframe: predictionTimeframe,
        }),
        credentials: 'include',
      });
      
      if (!response.ok) {
        throw new Error('การคาดการณ์ข้อมูลล้มเหลว โปรดลองใหม่อีกครั้ง');
      }
      
      const data = await response.json();
      setPredictionResults(data);
      
      toast({
        title: "คาดการณ์ข้อมูลสำเร็จ",
        description: `วิเคราะห์จากข้อมูล ${data.transactionCount || 0} รายการ`,
      });
    } catch (error) {
      console.error("Error predicting data:", error);
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error instanceof Error ? error.message : "ไม่สามารถคาดการณ์ข้อมูลได้",
        variant: "destructive",
      });
    } finally {
      setIsLoadingPrediction(false);
    }
  };
  
  // Reset date range when timeframe changes
  useEffect(() => {
    setFromDate(undefined);
    setToDate(undefined);
  }, [timeframe]);
  
  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center">
          <div className="mr-3 relative">
            <div className="absolute -inset-1 rounded-full bg-indigo-600/30 animate-pulse blur-md"></div>
            <div className="relative bg-gradient-to-br from-indigo-700 to-indigo-900 h-10 w-10 rounded-full flex items-center justify-center">
              <Search className="h-5 w-5 text-indigo-300" />
            </div>
          </div>
          <div>
            <h1 className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-100 to-white">
              การค้นหาขั้นสูง
            </h1>
            <p className="text-indigo-300 text-sm">
              วิเคราะห์และค้นหาข้อมูลการตรวจสอบสลิปขั้นสูง
            </p>
          </div>
        </div>
        
        <Tabs defaultValue="analyze" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 mb-6">
            <TabsTrigger value="analyze" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              <span>วิเคราะห์ข้อมูล</span>
            </TabsTrigger>
            <TabsTrigger value="correlation" className="flex items-center gap-2">
              <AreaChart className="h-4 w-4" />
              <span>หาความสัมพันธ์</span>
            </TabsTrigger>
            <TabsTrigger value="prediction" className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              <span>คาดการณ์</span>
            </TabsTrigger>
          </TabsList>
          
          {/* Tab: วิเคราะห์ข้อมูล */}
          <TabsContent value="analyze">
            <Card>
              <CardHeader>
                <CardTitle>วิเคราะห์ข้อมูลการใช้งาน</CardTitle>
                <CardDescription>
                  ค้นหาและวิเคราะห์ข้อมูลการตรวจสอบสลิปแบบเจาะลึก
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-6 md:grid-cols-2">
                  <div>
                    <Label htmlFor="analysisType">ประเภทการวิเคราะห์</Label>
                    <Select value={analysisType} onValueChange={setAnalysisType}>
                      <SelectTrigger>
                        <SelectValue placeholder="เลือกประเภทการวิเคราะห์" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="usage_summary">สรุปการใช้งาน</SelectItem>
                        <SelectItem value="bank_analysis">วิเคราะห์ตามธนาคาร</SelectItem>
                        <SelectItem value="time_analysis">วิเคราะห์ตามเวลา</SelectItem>
                        <SelectItem value="transaction_patterns">รูปแบบธุรกรรม</SelectItem>
                        <SelectItem value="api_usage">การใช้งาน API</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="timeframe">ช่วงเวลา</Label>
                    <Select value={timeframe} onValueChange={setTimeframe} disabled={!!(fromDate && toDate)}>
                      <SelectTrigger>
                        <SelectValue placeholder="เลือกช่วงเวลา" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="7days">7 วันล่าสุด</SelectItem>
                        <SelectItem value="30days">30 วันล่าสุด</SelectItem>
                        <SelectItem value="90days">90 วันล่าสุด</SelectItem>
                        <SelectItem value="1year">1 ปีล่าสุด</SelectItem>
                        <SelectItem value="all">ทั้งหมด</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label>ตั้งแต่วันที่</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full justify-start text-left font-normal"
                        >
                          {fromDate ? (
                            format(fromDate, 'PPP', { locale: th })
                          ) : (
                            <span>เลือกวันที่เริ่มต้น</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={fromDate}
                          onSelect={setFromDate}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  
                  <div>
                    <Label>ถึงวันที่</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full justify-start text-left font-normal"
                        >
                          {toDate ? (
                            format(toDate, 'PPP', { locale: th })
                          ) : (
                            <span>เลือกวันที่สิ้นสุด</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={toDate}
                          onSelect={setToDate}
                          initialFocus
                          disabled={(date) => fromDate ? date < fromDate : false}
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="justify-between">
                <div className="flex gap-2">
                  <Button variant="outline" onClick={() => {
                    setAnalysisResults(null);
                    setTimeframe('30days');
                    setFromDate(undefined);
                    setToDate(undefined);
                    setAnalysisType('usage_summary');
                  }}>
                    รีเซ็ต
                  </Button>
                  
                  {analysisResults && (
                    <Button 
                      variant="outline" 
                      onClick={() => exportToCSV(analysisResults, `การวิเคราะห์ข้อมูล-${format(new Date(), 'yyyy-MM-dd')}.csv`)}
                      className="flex items-center gap-1"
                    >
                      <Download className="h-4 w-4" />
                      ดาวน์โหลด CSV
                    </Button>
                  )}
                </div>
                <Button onClick={handleAnalyze} disabled={isLoadingAnalysis}>
                  {isLoadingAnalysis ? (
                    <>
                      <div className="h-4 w-4 border-t-2 border-b-2 border-white rounded-full animate-spin mr-2"></div>
                      กำลังวิเคราะห์...
                    </>
                  ) : (
                    "วิเคราะห์ข้อมูล"
                  )}
                </Button>
              </CardFooter>
            </Card>
            
            {/* แสดงผลลัพธ์การวิเคราะห์ */}
            {analysisResults && (
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>ผลการวิเคราะห์ข้อมูล</CardTitle>
                  <CardDescription>
                    วิเคราะห์ข้อมูลตั้งแต่วันที่ {' '}
                    {format(new Date(analysisResults.timeframe.start), 'PPP', { locale: th })}
                    {' '} ถึง {' '}
                    {format(new Date(analysisResults.timeframe.end), 'PPP', { locale: th })}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {/* แสดงผลลัพธ์ตามประเภทการวิเคราะห์ */}
                  {analysisType === 'usage_summary' && (
                    <div className="space-y-6">
                      <div className="grid gap-4 md:grid-cols-3">
                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">
                              จำนวนรายการทั้งหมด
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="text-2xl font-bold">
                              {analysisResults.results.totalVerifications?.toLocaleString() || 0}
                            </div>
                          </CardContent>
                        </Card>
                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">
                              ยอดเงินรวม
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="text-2xl font-bold">
                              {analysisResults.results.totalAmount?.toLocaleString(undefined, {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                              }) || 0} บาท
                            </div>
                          </CardContent>
                        </Card>
                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">
                              ยอดเงินเฉลี่ยต่อรายการ
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="text-2xl font-bold">
                              {analysisResults.results.averageAmount?.toLocaleString(undefined, {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                              }) || 0} บาท
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                      
                      <Separator />
                      
                      <div className="space-y-2">
                        <h3 className="text-lg font-semibold">สถานะการตรวจสอบ</h3>
                        <div className="grid gap-4 md:grid-cols-3">
                          {Object.entries(analysisResults.results.statusCounts || {}).map(([status, count]) => (
                            <div key={status} className="flex items-center gap-2">
                              <div className={`w-3 h-3 rounded-full ${
                                status === 'success' ? 'bg-green-500' : 
                                status === 'failed' || status === 'error' ? 'bg-red-500' : 
                                'bg-yellow-500'
                              }`}></div>
                              <span className="capitalize">{status}</span>
                              <span className="ml-auto font-medium">{(count as number).toLocaleString()}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                      
                      <Separator />
                      
                      <div className="space-y-2">
                        <h3 className="text-lg font-semibold">แหล่งที่มาของการตรวจสอบ</h3>
                        <div className="grid gap-4 md:grid-cols-2">
                          {Object.entries(analysisResults.results.sourceCounts || {}).map(([source, count]) => (
                            <div key={source} className="flex items-center gap-2">
                              <div className={`w-3 h-3 rounded-full ${
                                source === 'web' ? 'bg-blue-500' : 
                                source === 'api' ? 'bg-purple-500' : 
                                'bg-gray-500'
                              }`}></div>
                              <span className="capitalize">{
                                source === 'web' ? 'เว็บไซต์' :
                                source === 'api' ? 'API' : source
                              }</span>
                              <span className="ml-auto font-medium">{(count as number).toLocaleString()}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                      
                      {analysisResults.results.highestAmount && (
                        <>
                          <Separator />
                          <div className="space-y-2">
                            <h3 className="text-lg font-semibold">รายการที่มีมูลค่าสูงสุด</h3>
                            <div className="bg-muted/20 rounded-lg p-4">
                              <div className="grid gap-2 md:grid-cols-2">
                                <div>
                                  <span className="text-sm text-muted-foreground">จำนวนเงิน</span>
                                  <p className="font-medium text-lg">{analysisResults.results.highestAmount.amount.toLocaleString(undefined, {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                  })} บาท</p>
                                </div>
                                {analysisResults.results.highestAmount.transaction && (
                                  <>
                                    <div>
                                      <span className="text-sm text-muted-foreground">วันที่</span>
                                      <p className="font-medium">{format(new Date(analysisResults.results.highestAmount.transaction.date), 'PPP', { locale: th })}</p>
                                    </div>
                                    <div>
                                      <span className="text-sm text-muted-foreground">ธนาคาร</span>
                                      <p className="font-medium">{analysisResults.results.highestAmount.transaction.bank}</p>
                                    </div>
                                    <div>
                                      <span className="text-sm text-muted-foreground">เลขอ้างอิง</span>
                                      <p className="font-medium">{analysisResults.results.highestAmount.transaction.reference}</p>
                                    </div>
                                  </>
                                )}
                              </div>
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  )}
                  
                  {analysisType === 'bank_analysis' && (
                    <div className="space-y-6">
                      <h3 className="text-lg font-semibold">วิเคราะห์ตามธนาคาร</h3>
                      
                      {/* กราฟแสดงข้อมูลเปรียบเทียบธนาคาร */}
                      {analysisResults.results.bankAnalysis && Object.entries(analysisResults.results.bankAnalysis).length > 0 && (
                        <div className="mt-6 p-4 bg-muted/10 rounded-lg">
                          <h4 className="text-base font-medium mb-3">การเปรียบเทียบระหว่างธนาคาร</h4>
                          <div className="h-80">
                            <ResponsiveContainer width="100%" height="100%">
                              <BarChart
                                data={Object.entries(analysisResults.results.bankAnalysis).map(([bank, data]: [string, any]) => ({
                                  name: bank,
                                  จำนวนรายการ: data.count,
                                  มูลค่ารวม: data.totalAmount,
                                  มูลค่าเฉลี่ย: data.averageAmount,
                                  อัตราความสำเร็จ: data.successRate
                                }))}
                                margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
                              >
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis 
                                  dataKey="name" 
                                  angle={-45} 
                                  textAnchor="end" 
                                  height={60}
                                  interval={0}
                                />
                                <YAxis />
                                <RechartsTooltip 
                                  formatter={(value: any, name: string) => {
                                    if (name === 'มูลค่ารวม' || name === 'มูลค่าเฉลี่ย') {
                                      return [`${Number(value).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })} บาท`, name];
                                    } else if (name === 'อัตราความสำเร็จ') {
                                      return [`${Number(value).toFixed(1)}%`, name];
                                    }
                                    return [value, name];
                                  }}
                                />
                                <Legend />
                                <Bar dataKey="จำนวนรายการ" fill="#8884d8" />
                                <Bar dataKey="อัตราความสำเร็จ" fill="#82ca9d" />
                              </BarChart>
                            </ResponsiveContainer>
                          </div>
                        </div>
                      )}
                      
                      <div className="overflow-x-auto">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>ธนาคาร</TableHead>
                              <TableHead>จำนวนรายการ</TableHead>
                              <TableHead>ยอดเงินรวม</TableHead>
                              <TableHead>ยอดเงินเฉลี่ย</TableHead>
                              <TableHead>อัตราความสำเร็จ</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {analysisResults.results.bankAnalysis && Object.entries(analysisResults.results.bankAnalysis).map(([bank, data]: [string, any]) => (
                              <TableRow key={bank}>
                                <TableCell className="font-medium">{bank}</TableCell>
                                <TableCell>{data.count}</TableCell>
                                <TableCell>{data.totalAmount.toLocaleString(undefined, {
                                  minimumFractionDigits: 2,
                                  maximumFractionDigits: 2
                                })} บาท</TableCell>
                                <TableCell>{data.averageAmount.toLocaleString(undefined, {
                                  minimumFractionDigits: 2,
                                  maximumFractionDigits: 2
                                })} บาท</TableCell>
                                <TableCell>
                                  <div className="flex items-center gap-2">
                                    <Progress value={data.successRate} className="h-2 w-20" />
                                    <span>{data.successRate.toFixed(1)}%</span>
                                  </div>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    </div>
                  )}
                  
                  {analysisType === 'time_analysis' && (
                    <div className="space-y-6">
                      {/* กราฟการกระจายตามช่วงเวลา */}
                      {analysisResults.results.hourlyDistribution && Object.entries(analysisResults.results.hourlyDistribution).length > 0 && (
                        <div className="mt-6 p-4 bg-muted/10 rounded-lg">
                          <h4 className="text-base font-medium mb-3">การกระจายตามช่วงเวลา</h4>
                          <div className="h-72">
                            <ResponsiveContainer width="100%" height="100%">
                              <LineChart
                                data={Object.entries(analysisResults.results.hourlyDistribution).map(([hour, count]: [string, any]) => ({
                                  name: `${hour}:00`,
                                  จำนวนรายการ: count
                                }))}
                                margin={{ top: 20, right: 30, left: 20, bottom: 10 }}
                              >
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis dataKey="name" />
                                <YAxis />
                                <RechartsTooltip />
                                <Legend />
                                <Line 
                                  type="monotone" 
                                  dataKey="จำนวนรายการ" 
                                  stroke="#8884d8" 
                                  activeDot={{ r: 8 }} 
                                  strokeWidth={2}
                                />
                              </LineChart>
                            </ResponsiveContainer>
                          </div>
                        </div>
                      )}
                      
                      <div className="grid gap-6 md:grid-cols-2">
                        <div>
                          <h3 className="text-lg font-semibold mb-4">การกระจายตามวันในสัปดาห์</h3>
                          
                          {/* กราฟแท่งแสดงการกระจายตามวันในสัปดาห์ */}
                          {analysisResults.results.weekdayDistribution && Object.entries(analysisResults.results.weekdayDistribution).length > 0 && (
                            <div className="mb-4 h-56">
                              <ResponsiveContainer width="100%" height="100%">
                                <BarChart
                                  data={Object.entries(analysisResults.results.weekdayDistribution).map(([day, count]: [string, any]) => ({
                                    name: day,
                                    จำนวนรายการ: count
                                  }))}
                                  margin={{ top: 10, right: 10, left: 10, bottom: 20 }}
                                >
                                  <CartesianGrid strokeDasharray="3 3" />
                                  <XAxis dataKey="name" />
                                  <YAxis />
                                  <RechartsTooltip />
                                  <Bar dataKey="จำนวนรายการ" fill="#4f46e5" />
                                </BarChart>
                              </ResponsiveContainer>
                            </div>
                          )}
                          
                          <div className="space-y-4">
                            {analysisResults.results.weekdayDistribution && Object.entries(analysisResults.results.weekdayDistribution).map(([day, count]: [string, any]) => (
                              <div key={day} className="flex items-center gap-2">
                                <span className="w-24">{day}</span>
                                <Progress
                                  value={count / Math.max(...Object.values(analysisResults.results.weekdayDistribution)) * 100}
                                  className="h-2 flex-1"
                                />
                                <span className="font-medium w-12 text-right">{count}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                        
                        <div>
                          <h3 className="text-lg font-semibold mb-4">ช่วงเวลาที่มีการใช้งานมากที่สุด</h3>
                          <Card className="bg-muted/20">
                            <CardContent className="pt-6">
                              <div className="space-y-2">
                                <div className="flex justify-between items-center">
                                  <span>เวลาที่ใช้งานมากที่สุด</span>
                                  <span className="font-semibold">{analysisResults.results.mostActiveTime?.hour || '-'}</span>
                                </div>
                                <div className="flex justify-between items-center">
                                  <span>จำนวนรายการ</span>
                                  <span className="font-semibold">{analysisResults.results.mostActiveTime?.count || 0}</span>
                                </div>
                                <div className="flex justify-between items-center">
                                  <span>วันที่ใช้งานมากที่สุด</span>
                                  <span className="font-semibold">{analysisResults.results.mostActiveDay?.day || '-'}</span>
                                </div>
                                <div className="flex justify-between items-center">
                                  <span>จำนวนรายการ</span>
                                  <span className="font-semibold">{analysisResults.results.mostActiveDay?.count || 0}</span>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                          
                          <h3 className="text-lg font-semibold mt-4 mb-2">การใช้งานตามช่วงเวลา</h3>
                          <div className="grid grid-cols-2 gap-2">
                            <Card className="bg-muted/20">
                              <CardContent className="py-2">
                                <div className="flex justify-between items-center">
                                  <span>เช้า (6:00-12:00)</span>
                                  <span className="font-bold">{analysisResults.results.timeOfDayDistribution?.morning || 0}</span>
                                </div>
                              </CardContent>
                            </Card>
                            <Card className="bg-muted/20">
                              <CardContent className="py-2">
                                <div className="flex justify-between items-center">
                                  <span>กลางวัน (12:00-17:00)</span>
                                  <span className="font-bold">{analysisResults.results.timeOfDayDistribution?.afternoon || 0}</span>
                                </div>
                              </CardContent>
                            </Card>
                            <Card className="bg-muted/20">
                              <CardContent className="py-2">
                                <div className="flex justify-between items-center">
                                  <span>เย็น (17:00-22:00)</span>
                                  <span className="font-bold">{analysisResults.results.timeOfDayDistribution?.evening || 0}</span>
                                </div>
                              </CardContent>
                            </Card>
                            <Card className="bg-muted/20">
                              <CardContent className="py-2">
                                <div className="flex justify-between items-center">
                                  <span>กลางคืน (22:00-6:00)</span>
                                  <span className="font-bold">{analysisResults.results.timeOfDayDistribution?.night || 0}</span>
                                </div>
                              </CardContent>
                            </Card>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {analysisType === 'transaction_patterns' && (
                    <div className="space-y-6">
                      {/* กราฟวงกลมแสดงการกระจายของจำนวนเงิน */}
                      {analysisResults.results.amountDistribution && Object.entries(analysisResults.results.amountDistribution).length > 0 && (
                        <div className="mt-6 p-4 bg-muted/10 rounded-lg">
                          <h4 className="text-base font-medium mb-3">การกระจายตามจำนวนเงิน</h4>
                          <div className="h-72">
                            <ResponsiveContainer width="100%" height="100%">
                              <PieChart>
                                <Pie
                                  data={Object.entries(analysisResults.results.amountDistribution).map(([range, count]: [string, any], index) => ({
                                    name: range,
                                    value: count,
                                    fill: `hsl(${index * 30}, 70%, 50%)`
                                  }))}
                                  cx="50%"
                                  cy="50%"
                                  labelLine={true}
                                  outerRadius={120}
                                  fill="#8884d8"
                                  dataKey="value"
                                  nameKey="name"
                                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                                >
                                  {Object.entries(analysisResults.results.amountDistribution).map(([range, count]: [string, any], index) => (
                                    <Cell key={`cell-${index}`} fill={`hsl(${index * 30}, 70%, 50%)`} />
                                  ))}
                                </Pie>
                                <RechartsTooltip formatter={(value: any, name: string) => [`${value} รายการ`, `${name} บาท`]} />
                                <Legend />
                              </PieChart>
                            </ResponsiveContainer>
                          </div>
                        </div>
                      )}
                      
                      <div className="grid gap-6 md:grid-cols-2">
                        <div>
                          <h3 className="text-lg font-semibold mb-4">การกระจายตามจำนวนเงิน</h3>
                          <div className="space-y-2">
                            {analysisResults.results.amountDistribution && Object.entries(analysisResults.results.amountDistribution).map(([range, count]: [string, any]) => (
                              <div key={range} className="flex items-center gap-2">
                                <span className="w-28">{range} บาท</span>
                                <Progress 
                                  value={count / Math.max(...Object.values(analysisResults.results.amountDistribution)) * 100}
                                  className="h-2 flex-1"
                                />
                                <span className="font-medium w-12 text-right">{count}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                        
                        <div>
                          <h3 className="text-lg font-semibold mb-4">ผู้รับเงินที่พบบ่อย</h3>
                          <div className="space-y-2">
                            {analysisResults.results.topReceivers && analysisResults.results.topReceivers.map((receiver: any, index: number) => (
                              <div key={index} className="flex items-center gap-2">
                                <span className="truncate max-w-[180px]">{receiver.name}</span>
                                <Progress 
                                  value={receiver.count / analysisResults.results.topReceivers[0].count * 100}
                                  className="h-2 flex-1"
                                />
                                <span className="font-medium w-12 text-right">{receiver.count}</span>
                              </div>
                            ))}
                          </div>
                          
                          <h3 className="text-lg font-semibold mt-6 mb-4">ผู้ส่งเงินที่พบบ่อย</h3>
                          <div className="space-y-2">
                            {analysisResults.results.topSenders && analysisResults.results.topSenders.map((sender: any, index: number) => (
                              <div key={index} className="flex items-center gap-2">
                                <span className="truncate max-w-[180px]">{sender.name}</span>
                                <Progress 
                                  value={sender.count / analysisResults.results.topSenders[0].count * 100}
                                  className="h-2 flex-1"
                                />
                                <span className="font-medium w-12 text-right">{sender.count}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {analysisType === 'api_usage' && (
                    <div className="space-y-6">
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-lg font-semibold">การใช้งาน API รวม</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-3xl font-bold">
                            {analysisResults.results.totalApiCalls?.toLocaleString() || 0} ครั้ง
                          </div>
                          <p className="text-sm text-muted-foreground mt-1">API Key ที่ใช้งานมากที่สุด: {analysisResults.results.mostUsedApiKey}</p>
                        </CardContent>
                      </Card>
                      
                      <h3 className="text-lg font-semibold">การใช้งานแยกตาม API Key</h3>
                      <div className="overflow-x-auto">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>API Key</TableHead>
                              <TableHead>จำนวนการใช้งาน</TableHead>
                              <TableHead>ความสำเร็จ</TableHead>
                              <TableHead>เวลาตอบสนองเฉลี่ย</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {analysisResults.results.apiKeyUsage && Object.entries(analysisResults.results.apiKeyUsage).map(([keyName, data]: [string, any]) => (
                              <TableRow key={keyName}>
                                <TableCell className="font-medium">{keyName}</TableCell>
                                <TableCell>{data.count}</TableCell>
                                <TableCell>
                                  <div className="flex items-center gap-2">
                                    <Progress value={data.successRate} className="h-2 w-20" />
                                    <span>{data.successRate.toFixed(1)}%</span>
                                  </div>
                                </TableCell>
                                <TableCell>{data.avgResponseTime.toFixed(0)} ms</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                      
                      <h3 className="text-lg font-semibold">IP Address ที่ใช้งานมากที่สุด</h3>
                      <div className="grid gap-4 md:grid-cols-2">
                        {analysisResults.results.topIpAddresses && analysisResults.results.topIpAddresses.map((ipData: any, index: number) => (
                          <Card key={index} className="bg-muted/20">
                            <CardContent className="py-4">
                              <div className="flex justify-between items-center">
                                <span className="font-mono">{ipData.ip}</span>
                                <span className="font-bold">{ipData.count} ครั้ง</span>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </TabsContent>
          
          {/* Tab: หาความสัมพันธ์ */}
          <TabsContent value="correlation">
            <Card>
              <CardHeader>
                <CardTitle>ค้นหาความสัมพันธ์ของข้อมูล</CardTitle>
                <CardDescription>
                  วิเคราะห์รูปแบบและความเชื่อมโยงระหว่างรายการต่างๆ
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-6 md:grid-cols-2">
                  <div>
                    <Label htmlFor="correlationType">ประเภทความสัมพันธ์</Label>
                    <Select value={correlationType} onValueChange={setCorrelationType}>
                      <SelectTrigger>
                        <SelectValue placeholder="เลือกประเภทความสัมพันธ์" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="transaction_patterns">รูปแบบการทำธุรกรรม</SelectItem>
                        <SelectItem value="recurring_transactions">ธุรกรรมที่เกิดซ้ำ</SelectItem>
                        <SelectItem value="anomaly_detection">ตรวจจับความผิดปกติ</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="timeframe">ช่วงเวลาที่วิเคราะห์</Label>
                    <Select value={correlationTimeframe} onValueChange={setCorrelationTimeframe}>
                      <SelectTrigger>
                        <SelectValue placeholder="เลือกช่วงเวลา" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="30days">30 วันล่าสุด</SelectItem>
                        <SelectItem value="90days">90 วันล่าสุด</SelectItem>
                        <SelectItem value="180days">180 วันล่าสุด</SelectItem>
                        <SelectItem value="1year">1 ปีล่าสุด</SelectItem>
                        <SelectItem value="all">ทั้งหมด</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="justify-between">
                <div className="flex gap-2">
                  <Button variant="outline" onClick={() => {
                    setCorrelationResults(null);
                    setCorrelationType('transaction_patterns');
                    setCorrelationTimeframe('90days');
                  }}>
                    รีเซ็ต
                  </Button>
                  
                  {correlationResults && (
                    <Button 
                      variant="outline" 
                      onClick={() => exportToCSV(correlationResults, `ความสัมพันธ์ของข้อมูล-${format(new Date(), 'yyyy-MM-dd')}.csv`)}
                      className="flex items-center gap-1"
                    >
                      <Download className="h-4 w-4" />
                      ดาวน์โหลด CSV
                    </Button>
                  )}
                </div>
                <Button onClick={handleCorrelation} disabled={isLoadingCorrelation}>
                  {isLoadingCorrelation ? (
                    <>
                      <div className="h-4 w-4 border-t-2 border-b-2 border-white rounded-full animate-spin mr-2"></div>
                      กำลังวิเคราะห์...
                    </>
                  ) : (
                    "ค้นหาความสัมพันธ์"
                  )}
                </Button>
              </CardFooter>
            </Card>
            
            {/* แสดงผลลัพธ์การวิเคราะห์ความสัมพันธ์ */}
            {correlationResults && (
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>ผลการวิเคราะห์ความสัมพันธ์</CardTitle>
                  <CardDescription>
                    วิเคราะห์ข้อมูลตั้งแต่วันที่ {' '}
                    {format(new Date(correlationResults.timeframe.start), 'PPP', { locale: th })}
                    {' '} ถึง {' '}
                    {format(new Date(correlationResults.timeframe.end), 'PPP', { locale: th })}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {/* Transaction Patterns */}
                  {correlationType === 'transaction_patterns' && (
                    <div className="space-y-6">
                      <div className="grid gap-6 md:grid-cols-2">
                        <div>
                          <h3 className="text-lg font-semibold mb-4">ความสัมพันธ์ระหว่างจำนวนเงินและธนาคาร</h3>
                          <div className="space-y-4">
                            {correlationResults.results.bankAmountPatterns && Object.entries(correlationResults.results.bankAmountPatterns).map(([bank, data]: [string, any]) => (
                              <Card key={bank} className="overflow-hidden">
                                <CardHeader className="py-3 px-4 bg-muted">
                                  <CardTitle className="text-base font-medium">{bank}</CardTitle>
                                </CardHeader>
                                <CardContent className="py-3 px-4">
                                  <div className="space-y-1 text-sm">
                                    <div className="flex justify-between">
                                      <span className="text-muted-foreground">จำนวนรายการ</span>
                                      <span className="font-medium">{data.totalTransactions}</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-muted-foreground">ยอดเงินเฉลี่ย</span>
                                      <span className="font-medium">{data.averageAmount.toLocaleString(undefined, {
                                        minimumFractionDigits: 2,
                                        maximumFractionDigits: 2
                                      })} บาท</span>
                                    </div>
                                    {data.mostCommonAmount && (
                                      <div className="flex justify-between">
                                        <span className="text-muted-foreground">จำนวนเงินที่พบบ่อย</span>
                                        <span className="font-medium">{data.mostCommonAmount.amount.toLocaleString(undefined, {
                                          minimumFractionDigits: 2,
                                          maximumFractionDigits: 2
                                        })} บาท</span>
                                      </div>
                                    )}
                                    {data.mostCommonAmount && (
                                      <div className="flex justify-between">
                                        <span className="text-muted-foreground">พบบ่อย</span>
                                        <span className="font-medium">{data.mostCommonAmount.percentage.toFixed(1)}%</span>
                                      </div>
                                    )}
                                  </div>
                                </CardContent>
                              </Card>
                            ))}
                          </div>
                        </div>
                        
                        <div>
                          <h3 className="text-lg font-semibold mb-4">ธนาคารที่ใช้บ่อยในแต่ละช่วงเวลา</h3>
                          <div className="space-y-4">
                            {correlationResults.results.timeOfDayBankPreference && Object.entries(correlationResults.results.timeOfDayBankPreference).map(([timeCategory, data]: [string, any]) => (
                              <Card key={timeCategory} className="overflow-hidden">
                                <CardHeader className="py-3 px-4 bg-muted">
                                  <CardTitle className="text-base font-medium">{timeCategory}</CardTitle>
                                </CardHeader>
                                <CardContent className="py-3 px-4">
                                  {data ? (
                                    <div className="space-y-1 text-sm">
                                      <div className="flex justify-between">
                                        <span className="text-muted-foreground">ธนาคารที่ใช้บ่อยที่สุด</span>
                                        <span className="font-medium">{data.bank}</span>
                                      </div>
                                      <div className="flex justify-between">
                                        <span className="text-muted-foreground">จำนวนครั้ง</span>
                                        <span className="font-medium">{data.count}</span>
                                      </div>
                                      <div className="flex justify-between">
                                        <span className="text-muted-foreground">คิดเป็น</span>
                                        <span className="font-medium">{data.percentage.toFixed(1)}%</span>
                                      </div>
                                    </div>
                                  ) : (
                                    <p className="text-sm text-muted-foreground">ไม่พบข้อมูล</p>
                                  )}
                                </CardContent>
                              </Card>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {/* Recurring Transactions */}
                  {correlationType === 'recurring_transactions' && (
                    <div className="space-y-6">
                      <div className="flex justify-between items-center">
                        <h3 className="text-lg font-semibold">รายการที่เกิดขึ้นซ้ำๆ</h3>
                        <div className="bg-primary/10 text-primary rounded-full px-3 py-1 text-sm font-medium">
                          พบทั้งหมด {correlationResults.results.totalPatterns || 0} รูปแบบ
                        </div>
                      </div>
                      
                      <div className="space-y-4">
                        {correlationResults.results.recurringTransactions && Object.entries(correlationResults.results.recurringTransactions).map(([receiver, data]: [string, any]) => (
                          <Accordion type="single" collapsible key={receiver}>
                            <AccordionItem value="details">
                              <AccordionTrigger className="hover:bg-muted/50 px-4 py-2 rounded-lg data-[state=open]:bg-muted/50">
                                <div className="flex flex-col items-start text-left">
                                  <span className="font-medium">{receiver}</span>
                                  <span className="text-sm text-muted-foreground">
                                    {data.transactionCount} รายการ • 
                                    {data.patternType === 'ประจำ' ? ' ทำรายการเป็นประจำ' : ' ทำรายการไม่สม่ำเสมอ'} • 
                                    เฉลี่ยทุก {data.averageIntervalDays.toFixed(1)} วัน
                                  </span>
                                </div>
                              </AccordionTrigger>
                              <AccordionContent className="px-4 pb-4">
                                <div className="space-y-4">
                                  <div className="grid gap-4 md:grid-cols-2">
                                    <Card className="bg-muted/20">
                                      <CardContent className="pt-4">
                                        <div className="space-y-2">
                                          <div className="flex justify-between items-center">
                                            <span className="text-muted-foreground">จำนวนเงินเฉลี่ย</span>
                                            <span className="font-medium">{data.averageAmount.toLocaleString(undefined, {
                                              minimumFractionDigits: 2,
                                              maximumFractionDigits: 2
                                            })} บาท</span>
                                          </div>
                                          <div className="flex justify-between items-center">
                                            <span className="text-muted-foreground">ความแปรผัน</span>
                                            <span className="font-medium">{data.amountStdDev.toFixed(2)}</span>
                                          </div>
                                          <div className="flex justify-between items-center">
                                            <span className="text-muted-foreground">ช่วงเวลาระหว่างรายการ</span>
                                            <span className="font-medium">{data.averageIntervalDays.toFixed(1)} วัน</span>
                                          </div>
                                        </div>
                                      </CardContent>
                                    </Card>
                                    
                                    <Card className="bg-muted/20">
                                      <CardContent className="pt-4">
                                        <div className="space-y-2">
                                          <div className="flex justify-between items-center">
                                            <span className="text-muted-foreground">รูปแบบการทำรายการ</span>
                                            <span className="font-medium">{data.patternType}</span>
                                          </div>
                                          {data.predictedNextTransaction && (
                                            <div className="flex justify-between items-center">
                                              <span className="text-muted-foreground">ทำนายรายการถัดไป</span>
                                              <span className="font-medium">{format(new Date(data.predictedNextTransaction), 'P', { locale: th })}</span>
                                            </div>
                                          )}
                                        </div>
                                      </CardContent>
                                    </Card>
                                  </div>
                                  
                                  <div>
                                    <h4 className="text-sm font-medium mb-2">ประวัติการทำรายการ</h4>
                                    <div className="overflow-x-auto">
                                      <Table>
                                        <TableHeader>
                                          <TableRow>
                                            <TableHead>วันที่</TableHead>
                                            <TableHead>จำนวนเงิน</TableHead>
                                            <TableHead>ธนาคาร</TableHead>
                                          </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                          {data.transactions.map((transaction: any, idx: number) => (
                                            <TableRow key={idx}>
                                              <TableCell>{transaction.date ? format(new Date(transaction.date), 'PPP', { locale: th }) : '-'}</TableCell>
                                              <TableCell>{transaction.amount ? transaction.amount.toLocaleString(undefined, {
                                                minimumFractionDigits: 2,
                                                maximumFractionDigits: 2
                                              }) : '0.00'} บาท</TableCell>
                                              <TableCell>{transaction.bank || '-'}</TableCell>
                                            </TableRow>
                                          ))}
                                        </TableBody>
                                      </Table>
                                    </div>
                                  </div>
                                </div>
                              </AccordionContent>
                            </AccordionItem>
                          </Accordion>
                        ))}
                        
                        {correlationResults.results.totalPatterns === 0 && (
                          <div className="bg-muted/20 rounded-lg p-6 text-center">
                            <AlertCircle className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                            <h4 className="text-lg font-medium">ไม่พบรายการที่ทำซ้ำ</h4>
                            <p className="text-muted-foreground">
                              ไม่พบรูปแบบการทำรายการซ้ำในช่วงเวลาที่เลือก
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                  
                  {/* Anomaly Detection */}
                  {correlationType === 'anomaly_detection' && (
                    <div className="space-y-6">
                      <div className="grid gap-6 md:grid-cols-2">
                        <Card>
                          <CardHeader>
                            <CardTitle className="text-base font-medium">ช่วงจำนวนเงินปกติ</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-2">
                              <div className="flex justify-between">
                                <span className="text-muted-foreground">จำนวนเงินเฉลี่ย</span>
                                <span className="font-medium">{correlationResults.results.amountStatistics?.average.toLocaleString(undefined, {
                                  minimumFractionDigits: 2,
                                  maximumFractionDigits: 2
                                })} บาท</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-muted-foreground">ความเบี่ยงเบน</span>
                                <span className="font-medium">{correlationResults.results.amountStatistics?.stdDev.toLocaleString(undefined, {
                                  minimumFractionDigits: 2,
                                  maximumFractionDigits: 2
                                })}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-muted-foreground">ช่วงปกติ</span>
                                <span className="font-medium">
                                  {correlationResults.results.amountStatistics?.normalRange.min.toLocaleString(undefined, {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                  })} - {correlationResults.results.amountStatistics?.normalRange.max.toLocaleString(undefined, {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                  })} บาท
                                </span>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                        
                        <Card>
                          <CardHeader>
                            <CardTitle className="text-base font-medium">สรุปความผิดปกติ</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-2">
                              <div className="flex justify-between">
                                <span className="text-muted-foreground">จำนวนทั้งหมด</span>
                                <span className="font-medium">{correlationResults.results.totalAnomalies || 0} รายการ</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-muted-foreground">ผิดปกติด้านจำนวนเงิน</span>
                                <span className="font-medium">{correlationResults.results.amountAnomalies?.length || 0} รายการ</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-muted-foreground">ผิดปกติด้านเวลา</span>
                                <span className="font-medium">{correlationResults.results.timeAnomalies?.length || 0} รายการ</span>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                      
                      {correlationResults.results.amountAnomalies?.length > 0 && (
                        <div>
                          <h3 className="text-lg font-semibold mb-4">รายการที่มีจำนวนเงินผิดปกติ</h3>
                          <div className="overflow-x-auto">
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead>วันที่</TableHead>
                                  <TableHead>จำนวนเงิน</TableHead>
                                  <TableHead>ผู้รับ</TableHead>
                                  <TableHead>ธนาคาร</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {correlationResults.results.amountAnomalies.map((anomaly: any, idx: number) => (
                                  <TableRow key={idx}>
                                    <TableCell>{anomaly.date ? format(new Date(anomaly.date), 'PPP', { locale: th }) : '-'}</TableCell>
                                    <TableCell className="font-medium">{anomaly.amount ? anomaly.amount.toLocaleString(undefined, {
                                      minimumFractionDigits: 2,
                                      maximumFractionDigits: 2
                                    }) : '0.00'} บาท</TableCell>
                                    <TableCell>{anomaly.receiver || '-'}</TableCell>
                                    <TableCell>{anomaly.bank || '-'}</TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          </div>
                        </div>
                      )}
                      
                      {correlationResults.results.timeAnomalies?.length > 0 && (
                        <div>
                          <h3 className="text-lg font-semibold mb-4">รายการที่เกิดขึ้นในเวลาผิดปกติ</h3>
                          <div className="overflow-x-auto">
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead>วันที่</TableHead>
                                  <TableHead>เวลา</TableHead>
                                  <TableHead>จำนวนเงิน</TableHead>
                                  <TableHead>ผู้รับ</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {correlationResults.results.timeAnomalies.map((anomaly: any, idx: number) => (
                                  <TableRow key={idx}>
                                    <TableCell>{anomaly.date ? format(new Date(anomaly.date), 'P', { locale: th }) : '-'}</TableCell>
                                    <TableCell className="font-medium">{anomaly.date ? format(new Date(anomaly.date), 'HH:mm', { locale: th }) : '-'} น.</TableCell>
                                    <TableCell>{anomaly.amount ? anomaly.amount.toLocaleString(undefined, {
                                      minimumFractionDigits: 2,
                                      maximumFractionDigits: 2
                                    }) : '0.00'} บาท</TableCell>
                                    <TableCell>{anomaly.receiver || '-'}</TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          </div>
                        </div>
                      )}
                      
                      {correlationResults.results.totalAnomalies === 0 && (
                        <div className="bg-muted/20 rounded-lg p-6 text-center">
                          <AlertCircle className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                          <h4 className="text-lg font-medium">ไม่พบรายการผิดปกติ</h4>
                          <p className="text-muted-foreground">
                            ไม่พบรายการที่มีความผิดปกติในช่วงเวลาที่เลือก
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </TabsContent>
          
          {/* Tab: คาดการณ์ */}
          <TabsContent value="prediction">
            <Card>
              <CardHeader>
                <CardTitle>คาดการณ์ข้อมูลในอนาคต</CardTitle>
                <CardDescription>
                  ใช้ข้อมูลในอดีตเพื่อทำนายรูปแบบการใช้งานในอนาคต
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-6 md:grid-cols-2">
                  <div>
                    <Label htmlFor="predictionType">ประเภทการคาดการณ์</Label>
                    <Select value={predictionType} onValueChange={setPredictionType}>
                      <SelectTrigger>
                        <SelectValue placeholder="เลือกประเภทการคาดการณ์" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="usage_forecast">คาดการณ์การใช้งาน</SelectItem>
                        <SelectItem value="amount_prediction">คาดการณ์จำนวนเงิน</SelectItem>
                        <SelectItem value="recurring_prediction">คาดการณ์รายการประจำ</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="timeframe">ข้อมูลที่ใช้วิเคราะห์</Label>
                    <Select value={predictionTimeframe} onValueChange={setPredictionTimeframe}>
                      <SelectTrigger>
                        <SelectValue placeholder="เลือกช่วงเวลา" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="30days">30 วันล่าสุด</SelectItem>
                        <SelectItem value="90days">90 วันล่าสุด</SelectItem>
                        <SelectItem value="180days">180 วันล่าสุด</SelectItem>
                        <SelectItem value="365days">1 ปีล่าสุด</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="justify-between">
                <div className="flex gap-2">
                  <Button variant="outline" onClick={() => {
                    setPredictionResults(null);
                    setPredictionType('usage_forecast');
                    setPredictionTimeframe('90days');
                  }}>
                    รีเซ็ต
                  </Button>
                  
                  {predictionResults && (
                    <Button 
                      variant="outline" 
                      onClick={() => exportToCSV(predictionResults, `การคาดการณ์ข้อมูล-${format(new Date(), 'yyyy-MM-dd')}.csv`)}
                      className="flex items-center gap-1"
                    >
                      <Download className="h-4 w-4" />
                      ดาวน์โหลด CSV
                    </Button>
                  )}
                </div>
                <Button onClick={handlePrediction} disabled={isLoadingPrediction}>
                  {isLoadingPrediction ? (
                    <>
                      <div className="h-4 w-4 border-t-2 border-b-2 border-white rounded-full animate-spin mr-2"></div>
                      กำลังคาดการณ์...
                    </>
                  ) : (
                    "คาดการณ์ข้อมูล"
                  )}
                </Button>
              </CardFooter>
            </Card>
            
            {/* แสดงผลลัพธ์การคาดการณ์ */}
            {predictionResults && (
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>ผลการคาดการณ์ข้อมูล</CardTitle>
                  <CardDescription>
                    วิเคราะห์จากข้อมูล {predictionResults.transactionCount} รายการในช่วง {predictionResults.timeframe.daysAnalyzed} วันที่ผ่านมา
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {/* Usage Forecast */}
                  {predictionType === 'usage_forecast' && (
                    <div className="space-y-6">
                      {/* กราฟแนวโน้มการใช้งาน */}
                      {predictionResults.predictions.dailyUsage && predictionResults.predictions.dailyUsage.length > 0 && (
                        <div className="mt-6 p-4 bg-muted/10 rounded-lg">
                          <h4 className="text-base font-medium mb-3">แนวโน้มการใช้งานและการคาดการณ์</h4>
                          <div className="h-80">
                            <ResponsiveContainer width="100%" height="100%">
                              <LineChart
                                data={[
                                  ...predictionResults.predictions.dailyUsage.map((day: any) => ({
                                    name: format(new Date(day.date), 'd MMM', { locale: th }),
                                    จำนวนรายการจริง: day.count,
                                    type: 'history'
                                  })),
                                  ...predictionResults.predictions.forecastDaily.map((day: any) => ({
                                    name: format(new Date(day.date), 'd MMM', { locale: th }),
                                    คาดการณ์: day.count,
                                    type: 'forecast'
                                  }))
                                ]}
                                margin={{ top: 20, right: 30, left: 20, bottom: 30 }}
                              >
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis 
                                  dataKey="name" 
                                  angle={-30} 
                                  textAnchor="end" 
                                  height={60}
                                  interval="preserveStartEnd"
                                />
                                <YAxis />
                                <RechartsTooltip 
                                  formatter={(value: any, name: string) => [value, name === 'จำนวนรายการจริง' ? 'จำนวนรายการจริง' : 'คาดการณ์']}
                                  labelFormatter={(label) => `วันที่: ${label}`}
                                />
                                <Legend />
                                <Line 
                                  type="monotone" 
                                  dataKey="จำนวนรายการจริง" 
                                  stroke="#8884d8" 
                                  strokeWidth={2}
                                  dot={{ r: 3 }}
                                  activeDot={{ r: 5 }}
                                />
                                <Line 
                                  type="monotone" 
                                  dataKey="คาดการณ์" 
                                  stroke="#82ca9d" 
                                  strokeWidth={2}
                                  strokeDasharray="5 5"
                                  dot={{ r: 3 }}
                                />
                              </LineChart>
                            </ResponsiveContainer>
                          </div>
                        </div>
                      )}
                      
                      <div className="grid gap-6 md:grid-cols-3">
                        <Card className="bg-primary/5 border-primary/20">
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">
                              คาดการณ์ 7 วันข้างหน้า
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="text-2xl font-bold">
                              {predictionResults.predictions.forecasts?.next7Days || 0} รายการ
                            </div>
                          </CardContent>
                        </Card>
                        <Card className="bg-primary/5 border-primary/20">
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">
                              คาดการณ์ 30 วันข้างหน้า
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="text-2xl font-bold">
                              {predictionResults.predictions.forecasts?.next30Days || 0} รายการ
                            </div>
                          </CardContent>
                        </Card>
                        <Card className="bg-primary/5 border-primary/20">
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">
                              คาดการณ์ 90 วันข้างหน้า
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="text-2xl font-bold">
                              {predictionResults.predictions.forecasts?.next90Days || 0} รายการ
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                      
                      <div className="grid gap-6 md:grid-cols-2">
                        <Card>
                          <CardHeader>
                            <CardTitle className="text-base font-medium">แนวโน้มการใช้งาน</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-2">
                              <div className="flex justify-between">
                                <span className="text-muted-foreground">การใช้งานเฉลี่ย</span>
                                <span className="font-medium">{predictionResults.predictions.historicalData?.averageDailyUsage.toFixed(2)} รายการ/วัน</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-muted-foreground">แนวโน้ม</span>
                                <span className={`font-medium ${
                                  predictionResults.predictions.historicalData?.usageTrend.trend === 'เพิ่มขึ้น' ? 'text-green-500' :
                                  predictionResults.predictions.historicalData?.usageTrend.trend === 'ลดลง' ? 'text-red-500' :
                                  'text-amber-500'
                                }`}>
                                  {predictionResults.predictions.historicalData?.usageTrend.trend}
                                  {' '}
                                  ({predictionResults.predictions.historicalData?.usageTrend.changePercent.toFixed(1)}%)
                                </span>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                        
                        <Card>
                          <CardHeader>
                            <CardTitle className="text-base font-medium">ข้อมูลการวิเคราะห์</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-2">
                              <div className="flex justify-between">
                                <span className="text-muted-foreground">ระยะเวลาวิเคราะห์</span>
                                <span className="font-medium">{predictionResults.timeframe.daysAnalyzed} วัน</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-muted-foreground">จำนวนรายการทั้งหมด</span>
                                <span className="font-medium">{predictionResults.predictions.historicalData?.totalTransactions} รายการ</span>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                  )}
                  
                  {/* Amount Prediction */}
                  {predictionType === 'amount_prediction' && (
                    <div className="space-y-6">
                      {/* กราฟแนวโน้มจำนวนเงิน */}
                      {predictionResults.predictions.monthlyData && predictionResults.predictions.monthlyData.length > 0 && (
                        <div className="mt-6 p-4 bg-muted/10 rounded-lg">
                          <h4 className="text-base font-medium mb-3">แนวโน้มและการคาดการณ์จำนวนเงิน</h4>
                          <div className="h-80">
                            <ResponsiveContainer width="100%" height="100%">
                              <BarChart
                                data={[
                                  ...predictionResults.predictions.monthlyData.map((month: any) => ({
                                    name: month.month,
                                    จำนวนเงินเฉลี่ย: month.averageAmount,
                                    จำนวนรายการ: month.count,
                                    type: 'history'
                                  })),
                                  {
                                    name: 'คาดการณ์',
                                    จำนวนเงินเฉลี่ย: predictionResults.predictions.predictedAmount?.nextMonth,
                                    type: 'forecast',
                                    จำนวนรายการ: predictionResults.predictions.historicalData?.averageMonthlyCount
                                  }
                                ]}
                                margin={{ top: 20, right: 30, left: 20, bottom: 30 }}
                              >
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis 
                                  dataKey="name" 
                                  angle={-45} 
                                  textAnchor="end" 
                                  height={80}
                                  interval={0}
                                />
                                <YAxis yAxisId="left" orientation="left" stroke="#8884d8" />
                                <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
                                <RechartsTooltip 
                                  formatter={(value: any, name: string) => {
                                    if (name === 'จำนวนเงินเฉลี่ย') {
                                      return [`${Number(value).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })} บาท`, 'จำนวนเงินเฉลี่ย'];
                                    }
                                    return [value, name];
                                  }}
                                />
                                <Legend />
                                <Bar 
                                  yAxisId="left"
                                  dataKey="จำนวนเงินเฉลี่ย" 
                                  fill="#8884d8" 
                                  name="จำนวนเงินเฉลี่ย"
                                  radius={[4, 4, 0, 0]}
                                >
                                  {predictionResults.predictions.monthlyData.map((entry: any, index: number) => (
                                    <Cell 
                                      key={`cell-${index}`} 
                                      fill="#8884d8" 
                                    />
                                  ))}
                                  <Cell fill="#ff7300" /> {/* สีสำหรับค่าคาดการณ์ */}
                                </Bar>
                                <Bar 
                                  yAxisId="right"
                                  dataKey="จำนวนรายการ" 
                                  fill="#82ca9d"
                                  radius={[4, 4, 0, 0]}
                                  name="จำนวนรายการ"
                                />
                              </BarChart>
                            </ResponsiveContainer>
                          </div>
                        </div>
                      )}
                      
                      <div className="grid gap-6 md:grid-cols-2">
                        <Card className="bg-primary/5 border-primary/20">
                          <CardHeader className="pb-2">
                            <CardTitle className="text-base font-medium">
                              คาดการณ์จำนวนเงินเฉลี่ยต่อรายการ
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="text-2xl font-bold">
                              {predictionResults.predictions.predictedAmount?.nextMonth.toLocaleString(undefined, {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                              })} บาท
                            </div>
                            <p className="text-sm text-muted-foreground mt-1">
                              สำหรับเดือนถัดไป
                            </p>
                          </CardContent>
                        </Card>
                        
                        <Card className="bg-primary/5 border-primary/20">
                          <CardHeader className="pb-2">
                            <CardTitle className="text-base font-medium">
                              คาดการณ์มูลค่ารวม 3 เดือนข้างหน้า
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="text-2xl font-bold">
                              {predictionResults.predictions.predictedAmount?.nextQuarter.toLocaleString(undefined, {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                              })} บาท
                            </div>
                            <p className="text-sm text-muted-foreground mt-1">
                              รวมยอดที่คาดว่าจะใช้งานใน 3 เดือน
                            </p>
                          </CardContent>
                        </Card>
                      </div>
                      
                      <div>
                        <h3 className="text-lg font-semibold mb-4">ข้อมูลการวิเคราะห์</h3>
                        <div className="overflow-x-auto">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>เดือน</TableHead>
                                <TableHead>จำนวนรายการ</TableHead>
                                <TableHead>ยอดเงินเฉลี่ย</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {predictionResults.predictions.historicalData?.monthlyAverages?.map((monthData: any) => (
                                <TableRow key={monthData.month}>
                                  <TableCell>{monthData.month}</TableCell>
                                  <TableCell>{monthData.transactionCount}</TableCell>
                                  <TableCell>{monthData.averageAmount.toLocaleString(undefined, {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                  })} บาท</TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </div>
                      </div>
                      
                      <div>
                        <h3 className="text-lg font-semibold mb-3">แนวโน้ม</h3>
                        <Card>
                          <CardContent className="pt-6">
                            <div className="space-y-2">
                              <div className="flex justify-between items-center">
                                <span className="text-muted-foreground">แนวโน้มจำนวนเงิน</span>
                                <span className={`font-medium ${
                                  predictionResults.predictions.historicalData?.amountTrend.trend === 'เพิ่มขึ้น' ? 'text-green-500' :
                                  predictionResults.predictions.historicalData?.amountTrend.trend === 'ลดลง' ? 'text-red-500' :
                                  'text-amber-500'
                                }`}>
                                  {predictionResults.predictions.historicalData?.amountTrend.trend}
                                </span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="text-muted-foreground">การเปลี่ยนแปลง</span>
                                <span className={`font-medium ${
                                  predictionResults.predictions.historicalData?.amountTrend.changePercent > 0 ? 'text-green-500' :
                                  predictionResults.predictions.historicalData?.amountTrend.changePercent < 0 ? 'text-red-500' :
                                  'text-amber-500'
                                }`}>
                                  {predictionResults.predictions.historicalData?.amountTrend.changePercent.toFixed(1)}%
                                </span>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                  )}
                  
                  {/* Recurring Prediction */}
                  {predictionType === 'recurring_prediction' && (
                    <div className="space-y-6">
                      <div className="flex justify-between items-center">
                        <h3 className="text-lg font-semibold">รายการที่คาดว่าจะเกิดขึ้นซ้ำในอนาคต</h3>
                        <div className="bg-primary/10 text-primary rounded-full px-3 py-1 text-sm font-medium">
                          พบทั้งหมด {predictionResults.predictions.predictionCount || 0} รายการ
                        </div>
                      </div>
                      
                      {predictionResults.predictions.recurringPredictions?.length > 0 ? (
                        <div className="space-y-4">
                          {predictionResults.predictions.recurringPredictions.map((prediction: any, idx: number) => (
                            <Card key={idx} className="overflow-hidden">
                              <CardHeader className="bg-muted/30 py-3">
                                <div className="flex justify-between items-center">
                                  <CardTitle className="text-base font-medium">{prediction.receiver}</CardTitle>
                                  <div className="text-xs px-2 py-1 rounded-full bg-primary/20 text-primary">
                                    ความมั่นใจ {prediction.confidence.toFixed(0)}%
                                  </div>
                                </div>
                              </CardHeader>
                              <CardContent className="p-4">
                                <div className="grid gap-4 md:grid-cols-2">
                                  <div className="space-y-2">
                                    <div className="flex justify-between">
                                      <span className="text-muted-foreground">จำนวนเงิน</span>
                                      <span className="font-medium">{prediction.amount.toLocaleString(undefined, {
                                        minimumFractionDigits: 2,
                                        maximumFractionDigits: 2
                                      })} บาท</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-muted-foreground">ทำรายการ</span>
                                      <span className="font-medium">{prediction.frequency}</span>
                                    </div>
                                  </div>
                                  
                                  <div className="space-y-2">
                                    <div className="flex justify-between">
                                      <span className="text-muted-foreground">รายการถัดไป</span>
                                      <span className="font-medium">{format(new Date(prediction.nextPredictedDate), 'PPP', { locale: th })}</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-muted-foreground">ระยะห่างเฉลี่ย</span>
                                      <span className="font-medium">{prediction.averageIntervalDays.toFixed(1)} วัน</span>
                                    </div>
                                  </div>
                                </div>
                                
                                {prediction.historicalTransactions && prediction.historicalTransactions.length > 0 && (
                                  <div className="mt-4">
                                    <h4 className="text-sm font-medium mb-2">รายการล่าสุด</h4>
                                    <div className="overflow-x-auto">
                                      <Table>
                                        <TableHeader>
                                          <TableRow>
                                            <TableHead>วันที่</TableHead>
                                            <TableHead>จำนวนเงิน</TableHead>
                                            <TableHead>ธนาคาร</TableHead>
                                          </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                          {prediction.historicalTransactions.map((t: any, i: number) => (
                                            <TableRow key={i}>
                                              <TableCell>{t.date ? format(new Date(t.date), 'P', { locale: th }) : '-'}</TableCell>
                                              <TableCell>{t.amount ? t.amount.toLocaleString(undefined, {
                                                minimumFractionDigits: 2,
                                                maximumFractionDigits: 2
                                              }) : '0.00'} บาท</TableCell>
                                              <TableCell>{t.bank || '-'}</TableCell>
                                            </TableRow>
                                          ))}
                                        </TableBody>
                                      </Table>
                                    </div>
                                  </div>
                                )}
                              </CardContent>
                            </Card>
                          ))}
                        </div>
                      ) : (
                        <div className="bg-muted/20 rounded-lg p-6 text-center">
                          <AlertCircle className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                          <h4 className="text-lg font-medium">ไม่พบรายการที่คาดว่าจะเกิดซ้ำ</h4>
                          <p className="text-muted-foreground">
                            ไม่สามารถคาดการณ์รายการที่จะเกิดขึ้นซ้ำได้จากข้อมูลที่มีอยู่
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}