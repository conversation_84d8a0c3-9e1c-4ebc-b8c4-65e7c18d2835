import { Express, Request, Response } from "express";
import { storage } from "../storage";
import { db } from "../db";
import * as schema from "@shared/schema";
import { lt, eq, gte, and, sql } from "drizzle-orm";

// ตรวจสอบสิทธิ์แอดมิน
function isAdmin(req: any, res: any, next: any) {
  if (req.isAuthenticated() && req.user.role === "admin") {
    return next();
  }
  return res.status(403).json({ message: "ไม่มีสิทธิ์เข้าถึง" });
}

// ตรวจสอบการเข้าสู่ระบบ
function isAuthenticated(req: Request, res: Response, next: any) {
  if (req.isAuthenticated()) {
    return next();
  }
  return res.status(401).json({ message: "กรุณาเข้าสู่ระบบ" });
}

export function setupCouponRoutes(app: Express) {
  // ===== แอดมิน =====
  
  // ดึงข้อมูลคูปองทั้งหมด
  app.get("/api/admin/coupons", isAdmin, async (req, res) => {
    try {
      const coupons = await db.select().from(schema.coupons);
      res.status(200).json(coupons);
    } catch (error) {
      console.error("Error fetching coupons:", error);
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการดึงข้อมูลคูปอง" });
    }
  });
  
  // สร้างคูปองใหม่
  app.post("/api/admin/coupons", isAdmin, async (req, res) => {
    try {
      const couponData = req.body;
      
      // แปลงวันที่เป็น Date object
      const startDate = new Date(couponData.startDate);
      const endDate = new Date(couponData.endDate);
      
      const newCoupon = await storage.createCoupon({
        ...couponData,
        startDate,
        endDate
      });
      
      res.status(201).json(newCoupon);
    } catch (error) {
      console.error("Error creating coupon:", error);
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการสร้างคูปอง" });
    }
  });
  
  // อัปเดตคูปอง
  app.patch("/api/admin/coupons/:id", isAdmin, async (req, res) => {
    try {
      const couponId = parseInt(req.params.id);
      const couponData = req.body;
      
      // แปลงวันที่เป็น Date object ถ้ามี
      if (couponData.startDate) {
        couponData.startDate = new Date(couponData.startDate);
      }
      
      if (couponData.endDate) {
        couponData.endDate = new Date(couponData.endDate);
      }
      
      const updatedCoupon = await storage.updateCoupon(couponId, couponData);
      
      if (!updatedCoupon) {
        return res.status(404).json({ message: "ไม่พบคูปอง" });
      }
      
      res.status(200).json(updatedCoupon);
    } catch (error) {
      console.error("Error updating coupon:", error);
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการอัปเดตคูปอง" });
    }
  });
  
  // ลบคูปอง
  app.delete("/api/admin/coupons/:id", isAdmin, async (req, res) => {
    try {
      const couponId = parseInt(req.params.id);
      await db.delete(schema.coupons).where(eq(schema.coupons.id, couponId));
      res.status(200).json({ message: "ลบคูปองสำเร็จ" });
    } catch (error) {
      console.error("Error deleting coupon:", error);
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการลบคูปอง" });
    }
  });
  
  // ===== ผู้ใช้ทั่วไป =====
  
  // ตรวจสอบคูปอง
  app.post("/api/coupons/verify", isAuthenticated, async (req, res) => {
    try {
      const { code } = req.body;
      
      if (!code) {
        return res.status(400).json({ message: "กรุณาระบุรหัสคูปอง" });
      }
      
      // ดึงข้อมูลคูปอง
      const [coupon] = await db.select()
        .from(schema.coupons)
        .where(
          and(
            eq(schema.coupons.code, code),
            eq(schema.coupons.isActive, true),
            gte(schema.coupons.endDate, new Date()),
            lt(schema.coupons.usageCount, schema.coupons.maxUsage)
          )
        );
      
      if (!coupon) {
        return res.status(404).json({ 
          valid: false,
          message: "คูปองไม่ถูกต้องหรือหมดอายุ" 
        });
      }
      
      // ส่งข้อมูลคูปองกลับไป
      res.status(200).json({
        valid: true,
        coupon: {
          id: coupon.id,
          code: coupon.code,
          discountPercent: coupon.discountPercent,
          discountAmount: coupon.discountAmount,
          endDate: coupon.endDate
        }
      });
      
    } catch (error) {
      console.error("Error verifying coupon:", error);
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการตรวจสอบคูปอง" });
    }
  });
  
  // ใช้คูปอง
  app.post("/api/coupons/redeem", isAuthenticated, async (req, res) => {
    try {
      const { code } = req.body;
      
      if (!code) {
        return res.status(400).json({ message: "กรุณาระบุรหัสคูปอง" });
      }
      
      // ดึงข้อมูลคูปอง
      const [coupon] = await db.select()
        .from(schema.coupons)
        .where(
          and(
            eq(schema.coupons.code, code),
            eq(schema.coupons.isActive, true),
            gte(schema.coupons.endDate, new Date()),
            lt(schema.coupons.usageCount, schema.coupons.maxUsage)
          )
        );
      
      if (!coupon) {
        return res.status(404).json({ 
          success: false,
          message: "คูปองไม่ถูกต้องหรือหมดอายุ" 
        });
      }
      
      // เพิ่มจำนวนการใช้งาน
      const [updatedCoupon] = await db.update(schema.coupons)
        .set({ 
          usageCount: coupon.usageCount + 1,
          updatedAt: new Date()
        })
        .where(eq(schema.coupons.id, coupon.id))
        .returning();
      
      // ส่งข้อมูลคูปองกลับไป
      res.status(200).json({
        success: true,
        message: "ใช้คูปองสำเร็จ",
        coupon: {
          id: updatedCoupon.id,
          code: updatedCoupon.code,
          discountPercent: updatedCoupon.discountPercent,
          discountAmount: updatedCoupon.discountAmount,
          usageCount: updatedCoupon.usageCount,
          maxUsage: updatedCoupon.maxUsage
        }
      });
      
    } catch (error) {
      console.error("Error redeeming coupon:", error);
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการใช้คูปอง" });
    }
  });
}