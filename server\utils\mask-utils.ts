/**
 * ซ่อนเบอร์โทรศัพท์โดยแสดงเฉพาะ 3 ตัวแรกและ 4 ตัวท้าย
 * เช่น 0812345678 เป็น 081****5678
 * 
 * @param phone เบอร์โทรศัพท์ที่ต้องการซ่อน
 * @returns เบอร์โทรศัพท์ที่ถูกซ่อนแล้ว
 */
export function maskedPhone(phone: string): string {
  if (!phone) return '';
  
  // ทำความสะอาดเบอร์โทรศัพท์ ลบอักขระที่ไม่ใช่ตัวเลข
  const cleanPhone = phone.replace(/\D/g, '');
  
  if (cleanPhone.length <= 5) {
    // เบอร์สั้นเกินไป แสดงแค่ดอกจัน
    return '*'.repeat(cleanPhone.length);
  }
  
  // แสดง 3 ตัวแรกและ 4 ตัวสุดท้าย
  const firstPart = cleanPhone.substring(0, 3);
  const lastPart = cleanPhone.substring(cleanPhone.length - 2);
  const maskedPart = '*'.repeat(cleanPhone.length - 5);
  
  return `${firstPart}${maskedPart}${lastPart}`;
}

/**
 * ซ่อนอีเมลโดยแสดงเฉพาะ 3 ตัวแรกและโดเมน
 * เช่น <EMAIL> เป็น exa****@gmail.com
 * 
 * @param email อีเมลที่ต้องการซ่อน
 * @returns อีเมลที่ถูกซ่อนแล้ว
 */
export function maskedEmail(email: string): string {
  if (!email || !email.includes('@')) return '';
  
  const [username, domain] = email.split('@');
  
  if (username.length <= 3) {
    return `${username.charAt(0)}${'*'.repeat(username.length - 1)}@${domain}`;
  }
  
  return `${username.substring(0, 3)}${'*'.repeat(Math.min(username.length - 3, 4))}@${domain}`;
}