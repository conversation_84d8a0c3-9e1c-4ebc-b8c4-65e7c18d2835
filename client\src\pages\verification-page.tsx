import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import axios from "axios";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Loader2, Mail, Phone, ArrowRight, CheckCircle2 } from "lucide-react";

// ส่วนของ schema สำหรับการตรวจสอบฟอร์ม
const emailVerificationSchema = z.object({
  code: z.string().min(6, {
    message: "รหัสยืนยันต้องมีอย่างน้อย 6 ตัวอักษร",
  }),
});

const phoneVerificationSchema = z.object({
  code: z.string().min(6, {
    message: "รหัสยืนยันต้องมีอย่างน้อย 6 ตัวอักษร",
  }),
});

type EmailVerificationFormValues = z.infer<typeof emailVerificationSchema>;
type PhoneVerificationFormValues = z.infer<typeof phoneVerificationSchema>;

export default function VerificationPage() {
  const [activeTab, setActiveTab] = useState<string>("email");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isResending, setIsResending] = useState<boolean>(false);
  const [isUpdatingEmail, setIsUpdatingEmail] = useState<boolean>(false);
  const [newEmail, setNewEmail] = useState<string>("");
  const [user, setUser] = useState<any>(null);
  const [verificationStatus, setVerificationStatus] = useState<{
    email_verified: boolean;
    phone_verified: boolean;
  }>({
    email_verified: false,
    phone_verified: false,
  });

  const { toast } = useToast();
  const [, navigate] = useLocation();

  // ฟอร์มสำหรับยืนยันอีเมล
  const emailForm = useForm<EmailVerificationFormValues>({
    resolver: zodResolver(emailVerificationSchema),
    defaultValues: {
      code: "",
    },
  });

  // ฟอร์มสำหรับยืนยัน OTP
  const phoneForm = useForm<PhoneVerificationFormValues>({
    resolver: zodResolver(phoneVerificationSchema),
    defaultValues: {
      code: "",
    },
  });

  // ดึงข้อมูลผู้ใช้และสถานะการยืนยัน
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const response = await axios.get("/api/user");
        setUser(response.data);
        setVerificationStatus({
          email_verified: response.data.email_verified || false,
          phone_verified: response.data.phone_verified || false,
        });

        // เลือกแท็บเริ่มต้นตามสถานะการยืนยัน
        if (!response.data.email_verified && response.data.email) {
          setActiveTab("email");
        } else if (!response.data.phone_verified && response.data.phoneNumber) {
          setActiveTab("phone");
        }
      } catch (error) {
        console.error("ไม่สามารถดึงข้อมูลผู้ใช้ได้:", error);
        toast({
          variant: "destructive",
          title: "เกิดข้อผิดพลาด",
          description: "ไม่สามารถดึงข้อมูลผู้ใช้ได้ กรุณาลองใหม่อีกครั้ง",
        });
      }
    };

    fetchUserData();
  }, [toast]);

  const [emailResendTimer, setEmailResendTimer] = useState<number>(0);
  const [phoneResendTimer, setPhoneResendTimer] = useState<number>(0);

  // ตัวนับเวลาถอยหลังสำหรับการส่งรหัสยืนยันซ้ำ
  useEffect(() => {
    let emailInterval: NodeJS.Timeout | null = null;
    let phoneInterval: NodeJS.Timeout | null = null;

    if (emailResendTimer > 0) {
      emailInterval = setInterval(() => {
        setEmailResendTimer((prev) => prev - 1);
      }, 1000);
    }

    if (phoneResendTimer > 0) {
      phoneInterval = setInterval(() => {
        setPhoneResendTimer((prev) => prev - 1);
      }, 1000);
    }

    return () => {
      if (emailInterval) clearInterval(emailInterval);
      if (phoneInterval) clearInterval(phoneInterval);
    };
  }, [emailResendTimer, phoneResendTimer]);

  // ส่งรหัสยืนยันไปยังอีเมล
  const sendEmailVerification = async () => {
    if (!user?.email) {
      toast({
        variant: "destructive",
        title: "ไม่พบอีเมล",
        description: "กรุณาอัพเดตอีเมลในโปรไฟล์ของคุณก่อน",
      });
      return;
    }

    if (emailResendTimer > 0) {
      toast({
        variant: "destructive",
        title: "กรุณารอสักครู่",
        description: `คุณสามารถขอรหัสใหม่ได้ในอีก ${emailResendTimer} วินาที`,
      });
      return;
    }

    setIsResending(true);
    try {
      const response = await axios.post("/api/auth/send-email-verification", {
        email: user.email,
        userId: user.id,
        type: "email",
      });

      toast({
        title: "ส่งรหัสยืนยันสำเร็จ",
        description: response.data.message || "กรุณาตรวจสอบอีเมลของคุณ",
      });
      
      // ตั้งเวลาดีเลย์ 60 วินาที
      setEmailResendTimer(60);
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "ไม่สามารถส่งรหัสยืนยันได้",
        description: error.response?.data?.message || "กรุณาลองใหม่อีกครั้ง",
      });
    } finally {
      setIsResending(false);
    }
  };

  // ส่ง OTP ไปยังเบอร์โทรศัพท์
  const sendPhoneOTP = async () => {
    if (!user?.phoneNumber) {
      toast({
        variant: "destructive",
        title: "ไม่พบเบอร์โทรศัพท์",
        description: "กรุณาอัพเดตเบอร์โทรศัพท์ในโปรไฟล์ของคุณก่อน",
      });
      return;
    }

    if (phoneResendTimer > 0) {
      toast({
        variant: "destructive",
        title: "กรุณารอสักครู่",
        description: `คุณสามารถขอรหัสใหม่ได้ในอีก ${phoneResendTimer} วินาที`,
      });
      return;
    }

    setIsResending(true);
    try {
      const response = await axios.post("/api/auth/send-otp", {
        phoneNumber: user.phoneNumber,
        userId: user.id,
      });

      toast({
        title: "ระบบ OTP อยู่ระหว่างการปรับปรุง",
        description: "ขออภัยในความไม่สะดวก กรุณาใช้การยืนยันผ่านอีเมลแทนในขณะนี้",
        variant: "destructive",
        duration: 5000,
      });
      
      // ตั้งเวลาดีเลย์ 60 วินาที
      setPhoneResendTimer(60);
    } catch (error: any) {
      toast({
        title: "ระบบ OTP อยู่ระหว่างการปรับปรุง",
        description: "ขออภัยในความไม่สะดวก กรุณาใช้การยืนยันผ่านอีเมลแทนในขณะนี้",
        variant: "destructive",
        duration: 5000,
      });
      
      // ตั้งเวลาดีเลย์ 60 วินาที
      setPhoneResendTimer(60);
    } finally {
      setIsResending(false);
    }
  };

  // ยืนยันรหัสอีเมล
  const onEmailVerify = async (data: EmailVerificationFormValues) => {
    setIsLoading(true);
    try {
      const response = await axios.post("/api/auth/verify-code", {
        type: "email",
        identifier: user.email,
        code: data.code,
        userId: user.id,
      });

      toast({
        title: "ยืนยันอีเมลสำเร็จ",
        description: response.data.message || "อีเมลของคุณได้รับการยืนยันแล้ว",
      });

      // อัพเดตสถานะการยืนยัน
      setVerificationStatus((prev) => ({
        ...prev,
        email_verified: true,
      }));

      // รีเซ็ตฟอร์ม
      emailForm.reset();

      // ถ้ายืนยันทั้งสองอย่างแล้ว ให้ไปที่หน้าแดชบอร์ด
      if (verificationStatus.phone_verified || !user.phoneNumber) {
        navigate("/dashboard");
      }
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "ยืนยันอีเมลไม่สำเร็จ",
        description: error.response?.data?.message || "รหัสยืนยันไม่ถูกต้องหรือหมดอายุแล้ว",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // ยืนยันรหัส OTP
  const onPhoneVerify = async (data: PhoneVerificationFormValues) => {
    setIsLoading(true);
    
    try {
      const response = await axios.post("/api/auth/verify-code", {
        type: "phone",
        identifier: user.phoneNumber,
        code: data.code,
        userId: user.id,
      });

      toast({
        title: "ยืนยันเบอร์โทรศัพท์สำเร็จ",
        description: response.data.message || "เบอร์โทรศัพท์ของคุณได้รับการยืนยันแล้ว",
      });

      // อัพเดตสถานะการยืนยัน
      setVerificationStatus((prev) => ({
        ...prev,
        phone_verified: true,
      }));

      // รีเซ็ตฟอร์ม
      phoneForm.reset();

      // ถ้ายืนยันทั้งสองอย่างแล้ว ให้ไปที่หน้าแดชบอร์ด
      if (verificationStatus.email_verified || !user.email) {
        navigate("/dashboard");
      }
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "ยืนยันเบอร์โทรศัพท์ไม่สำเร็จ",
        description: error.response?.data?.message || "รหัส OTP ไม่ถูกต้องหรือหมดอายุแล้ว",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // ไปที่หน้าแดชบอร์ดถ้ายืนยันแล้ว
  useEffect(() => {
    // ถ้ายืนยันอย่างใดอย่างหนึ่งแล้ว หรือไม่มีช่องทางที่ต้องยืนยัน ให้ไปที่หน้าแดชบอร์ด
    if (
      verificationStatus.email_verified || 
      verificationStatus.phone_verified ||
      (!user?.email && !user?.phoneNumber)
    ) {
      if (user) {
        navigate("/dashboard");
      }
    }
  }, [verificationStatus, user, navigate]);

  // อัพเดตแท็บที่ใช้งานเมื่อสถานะการยืนยันเปลี่ยน
  useEffect(() => {
    if (verificationStatus.email_verified && !verificationStatus.phone_verified && user?.phoneNumber) {
      setActiveTab("phone");
    } else if (!verificationStatus.email_verified && verificationStatus.phone_verified && user?.email) {
      setActiveTab("email");
    }
  }, [verificationStatus, user]);

  // อัพเดตอีเมลและส่งรหัสยืนยันไปยังอีเมลใหม่
  const updateEmail = async () => {
    if (!newEmail || newEmail === user.email) {
      toast({
        variant: "destructive",
        title: "ไม่สามารถอัพเดตอีเมลได้",
        description: "กรุณากรอกอีเมลใหม่ที่แตกต่างจากอีเมลปัจจุบัน",
      });
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(newEmail)) {
      toast({
        variant: "destructive",
        title: "รูปแบบอีเมลไม่ถูกต้อง",
        description: "กรุณากรอกอีเมลในรูปแบบที่ถูกต้อง",
      });
      return;
    }

    setIsUpdatingEmail(true);
    try {
      const response = await axios.post("/api/user/update-email", {
        userId: user.id,
        email: newEmail,
      });

      toast({
        title: "อัพเดตอีเมลสำเร็จ",
        description: "รหัสยืนยันได้ถูกส่งไปยังอีเมลใหม่ของคุณแล้ว",
      });

      // อัพเดตข้อมูลผู้ใช้
      setUser({
        ...user,
        email: newEmail,
      });

      // ส่งรหัสยืนยันไปยังอีเมลใหม่
      await axios.post("/api/auth/send-email-verification", {
        email: newEmail,
        userId: user.id,
        type: "email",
      });
      
      // รีเซ็ตค่า newEmail
      setNewEmail("");
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "ไม่สามารถอัพเดตอีเมลได้",
        description: error.response?.data?.message || "กรุณาลองใหม่อีกครั้ง",
      });
    } finally {
      setIsUpdatingEmail(false);
    }
  };

  // ส่งรหัสยืนยันอัตโนมัติเมื่อโหลดหน้า
  useEffect(() => {
    if (user) {
      if (activeTab === "email" && !verificationStatus.email_verified && user.email) {
        sendEmailVerification();
      } else if (activeTab === "phone" && !verificationStatus.phone_verified && user.phoneNumber) {
        sendPhoneOTP();
      }
    }
  }, [user, activeTab, verificationStatus]);

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-purple-950 to-indigo-950 p-4">
      <Card className="w-full max-w-md p-6 bg-background/10 backdrop-blur-md border-purple-700/50">
        <div className="space-y-2 text-center mb-6">
          <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
            ยืนยันตัวตน
          </h1>
          <p className="text-neutral-300">
            โปรดยืนยันตัวตนเพื่อความปลอดภัยของบัญชีคุณ
          </p>
        </div>

        <Alert className="bg-indigo-900/20 border-indigo-700 mb-6">
          <AlertTitle className="text-indigo-300">
            จำเป็นต้องยืนยันตัวตน
          </AlertTitle>
          <AlertDescription className="text-indigo-200">
            เพื่อความปลอดภัย คุณจำเป็นต้องยืนยันตัวตนโดยเลือกยืนยัน <strong>อีเมล</strong> หรือ <strong>เบอร์โทรศัพท์</strong> อย่างใดอย่างหนึ่ง
            <div className="mt-2 border-l-4 border-indigo-500 pl-3 text-sm bg-indigo-950/30 p-2 rounded">
              <p className="font-semibold text-indigo-300">คำแนะนำ:</p>
              <p className="text-white">เราแนะนำให้ยืนยันด้วยอีเมลของคุณ เนื่องจากระบบ OTP อาจมีปัญหาในขณะนี้</p>
            </div>
          </AlertDescription>
        </Alert>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger 
              value="email"
              disabled={!user.email}
              className="data-[state=active]:bg-indigo-700 data-[state=active]:text-white"
            >
              <Mail className="h-4 w-4 mr-2" /> อีเมล
              {verificationStatus.email_verified && (
                <CheckCircle2 className="h-4 w-4 ml-2 text-green-400" />
              )}
            </TabsTrigger>
            <TabsTrigger 
              value="phone"
              disabled={!user.phoneNumber}
              className="data-[state=active]:bg-indigo-700 data-[state=active]:text-white"
            >
              <Phone className="h-4 w-4 mr-2" /> เบอร์โทร
              {verificationStatus.phone_verified && (
                <CheckCircle2 className="h-4 w-4 ml-2 text-green-400" />
              )}
            </TabsTrigger>
          </TabsList>

          {/* Tab สำหรับยืนยันอีเมล */}
          <TabsContent value="email" className="pt-2">
            <div className="space-y-3 mb-4">
              <div className="bg-indigo-900/30 p-3 rounded-md flex items-center">
                <Mail className="h-5 w-5 mr-3 text-indigo-300" />
                <div>
                  <div className="font-medium text-white">{user.email}</div>
                  <div className="text-sm text-indigo-300">รหัสยืนยันถูกส่งไปยังอีเมลนี้</div>
                </div>
                <div className="ml-auto">
                  {verificationStatus.email_verified ? (
                    <Badge className="bg-green-600">ยืนยันแล้ว</Badge>
                  ) : (
                    <Badge className="bg-yellow-600">รอยืนยัน</Badge>
                  )}
                </div>
              </div>
              
              {!verificationStatus.email_verified && (
                <div className="flex gap-2 items-center">
                  <Input 
                    type="email" 
                    placeholder="แก้ไขอีเมลของคุณ" 
                    defaultValue={user.email}
                    onChange={(e) => setNewEmail(e.target.value)}
                    className="bg-indigo-950/50 border-indigo-700 flex-1"
                  />
                  <Button 
                    size="sm" 
                    variant="outline" 
                    onClick={updateEmail}
                    disabled={isUpdatingEmail}
                    className="border-indigo-700 hover:border-indigo-500 hover:bg-indigo-950/50 whitespace-nowrap"
                  >
                    {isUpdatingEmail && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                    อัพเดตอีเมล
                  </Button>
                </div>
              )}
            </div>

            {!verificationStatus.email_verified && (
              <Form {...emailForm}>
                <form onSubmit={emailForm.handleSubmit(onEmailVerify)} className="space-y-4">
                  <FormField
                    control={emailForm.control}
                    name="code"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white">รหัสยืนยัน</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="กรอกรหัสยืนยัน 6 หลัก"
                            {...field}
                            autoComplete="one-time-code"
                            className="bg-indigo-950/50 border-indigo-700"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="flex items-center justify-between">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={sendEmailVerification}
                      disabled={isResending || emailResendTimer > 0}
                      className="border-indigo-700 hover:border-indigo-500 hover:bg-indigo-950/50"
                    >
                      {isResending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                      {emailResendTimer > 0 ? `ส่งรหัสยืนยันใหม่ (${emailResendTimer}s)` : 'ส่งรหัสยืนยันใหม่'}
                    </Button>
                    <Button
                      type="submit"
                      disabled={isLoading}
                      className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700"
                    >
                      {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                      ยืนยันอีเมล <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </div>
                </form>
              </Form>
            )}

            {verificationStatus.email_verified && (
              <div className="text-center p-4">
                <CheckCircle2 className="h-12 w-12 mx-auto text-green-400 mb-2" />
                <h3 className="text-lg font-medium text-white">อีเมลได้รับการยืนยันแล้ว</h3>
                <p className="text-neutral-300 mb-4">
                  คุณสามารถใช้งานระบบได้ตามปกติ
                </p>
                {!verificationStatus.phone_verified && user.phoneNumber && (
                  <Button
                    onClick={() => setActiveTab("phone")}
                    className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700"
                  >
                    ยืนยันเบอร์โทรศัพท์ <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                )}
                {(verificationStatus.phone_verified || !user.phoneNumber) && (
                  <Button
                    onClick={() => navigate("/dashboard")}
                    className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700"
                  >
                    ไปที่แดชบอร์ด <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                )}
              </div>
            )}
          </TabsContent>

          {/* Tab สำหรับยืนยันเบอร์โทรศัพท์ */}
          <TabsContent value="phone" className="pt-2">
            <div className="space-y-3 mb-4">
              <div className="bg-indigo-900/30 p-3 rounded-md flex items-center">
                <Phone className="h-5 w-5 mr-3 text-indigo-300" />
                <div>
                  <div className="font-medium text-white">{user.phoneNumber}</div>
                  <div className="text-sm text-indigo-300">รหัส OTP ถูกส่งไปยังเบอร์นี้</div>
                </div>
                <div className="ml-auto">
                  {verificationStatus.phone_verified ? (
                    <Badge className="bg-green-600">ยืนยันแล้ว</Badge>
                  ) : (
                    <Badge className="bg-yellow-600">รอยืนยัน</Badge>
                  )}
                </div>
              </div>
              
              {!verificationStatus.phone_verified && (
                <Alert className="bg-amber-900/20 border-amber-700">
                  <AlertTitle className="text-amber-300 flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5">
                      <path fillRule="evenodd" d="M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003ZM12 8.25a.75.75 0 0 1 .75.75v3.75a.75.75 0 0 1-1.5 0V9a.75.75 0 0 1 .75-.75Zm0 8.25a.75.75 0 1 0 0-********* 0 0 0 0 1.5Z" clipRule="evenodd" />
                    </svg>
                    ระบบ OTP กำลังปรับปรุง
                  </AlertTitle>
                  <AlertDescription className="text-amber-200">
                    ขณะนี้ระบบยืนยันตัวตนผ่าน OTP กำลังอยู่ในช่วงปรับปรุง กรุณาเลือกยืนยันตัวตนผ่านอีเมลแทน หากพบปัญหาในการยืนยันผ่านอีเมล สามารถติดต่อเจ้าหน้าที่ได้ที่ <EMAIL>
                  </AlertDescription>
                </Alert>
              )}
            </div>

            {!verificationStatus.phone_verified && (
              <Form {...phoneForm}>
                <form onSubmit={phoneForm.handleSubmit(onPhoneVerify)} className="space-y-4">
                  <FormField
                    control={phoneForm.control}
                    name="code"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white">รหัส OTP</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="กรอกรหัส OTP 6 หลัก"
                            {...field}
                            autoComplete="one-time-code"
                            className="bg-indigo-950/50 border-indigo-700"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="flex items-center justify-between">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={sendPhoneOTP}
                      disabled={isResending || phoneResendTimer > 0}
                      className="border-indigo-700 hover:border-indigo-500 hover:bg-indigo-950/50"
                    >
                      {isResending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                      {phoneResendTimer > 0 ? `ส่ง OTP ใหม่ (${phoneResendTimer}s)` : 'ส่ง OTP ใหม่'}
                    </Button>
                    <Button
                      type="submit"
                      disabled={isLoading}
                      className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700"
                    >
                      {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                      ยืนยันเบอร์โทร <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </div>
                </form>
              </Form>
            )}

            {verificationStatus.phone_verified && (
              <div className="text-center p-4">
                <CheckCircle2 className="h-12 w-12 mx-auto text-green-400 mb-2" />
                <h3 className="text-lg font-medium text-white">เบอร์โทรศัพท์ได้รับการยืนยันแล้ว</h3>
                <p className="text-neutral-300 mb-4">
                  คุณสามารถใช้งานระบบได้ตามปกติ
                </p>
                {!verificationStatus.email_verified && user.email && (
                  <Button
                    onClick={() => setActiveTab("email")}
                    className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700"
                  >
                    ยืนยันอีเมล <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                )}
                {(verificationStatus.email_verified || !user.email) && (
                  <Button
                    onClick={() => navigate("/dashboard")}
                    className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700"
                  >
                    ไปที่แดชบอร์ด <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                )}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </Card>
    </div>
  );
}