import { Request, Response } from 'express';
import { db } from '../db';
import { eq, desc, gte, lte, and } from 'drizzle-orm';
import { isAdmin } from '../auth';
import { fraudRules, fraudDetections, insertFraudRuleSchema } from '@shared/schema';
import { fraudDetectionService } from '../fraud-detection-service';

// ดึงกฎตรวจจับการฉ้อโกงทั้งหมด
export async function getFraudRules(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'กรุณาเข้าสู่ระบบ' });
    }

    if (!isAdmin(req)) {
      return res.status(403).json({ message: 'คุณไม่มีสิทธิ์ในการเข้าถึง' });
    }

    const rules = await db.select().from(fraudRules)
      .orderBy(desc(fraudRules.createdAt));

    res.json(rules);
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการดึงกฎตรวจจับการฉ้อโกง:', error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการดึงกฎตรวจจับการฉ้อโกง' });
  }
}

// ดึงกฎตรวจจับการฉ้อโกงโดย ID
export async function getFraudRuleById(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'กรุณาเข้าสู่ระบบ' });
    }

    if (!isAdmin(req)) {
      return res.status(403).json({ message: 'คุณไม่มีสิทธิ์ในการเข้าถึง' });
    }

    const ruleId = parseInt(req.params.id);
    const rule = await db.select().from(fraudRules)
      .where(eq(fraudRules.id, ruleId));

    if (rule.length === 0) {
      return res.status(404).json({ message: 'ไม่พบกฎตรวจจับการฉ้อโกง' });
    }

    res.json(rule[0]);
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการดึงกฎตรวจจับการฉ้อโกง:', error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการดึงกฎตรวจจับการฉ้อโกง' });
  }
}

// สร้างกฎตรวจจับการฉ้อโกงใหม่
export async function createFraudRule(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'กรุณาเข้าสู่ระบบ' });
    }

    if (!isAdmin(req)) {
      return res.status(403).json({ message: 'คุณไม่มีสิทธิ์ในการเข้าถึง' });
    }

    const ruleData = insertFraudRuleSchema.parse(req.body);
    const result = await db.insert(fraudRules).values({
      ...ruleData,
      createdAt: new Date(),
      updatedAt: new Date()
    }).returning();

    res.status(201).json(result[0]);
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการสร้างกฎตรวจจับการฉ้อโกง:', error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการสร้างกฎตรวจจับการฉ้อโกง' });
  }
}

// อัปเดตกฎตรวจจับการฉ้อโกง
export async function updateFraudRule(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'กรุณาเข้าสู่ระบบ' });
    }

    if (!isAdmin(req)) {
      return res.status(403).json({ message: 'คุณไม่มีสิทธิ์ในการเข้าถึง' });
    }

    const ruleId = parseInt(req.params.id);
    const ruleData = req.body;

    // ตรวจสอบว่ากฎนี้มีอยู่จริงหรือไม่
    const rule = await db.select().from(fraudRules)
      .where(eq(fraudRules.id, ruleId));

    if (rule.length === 0) {
      return res.status(404).json({ message: 'ไม่พบกฎตรวจจับการฉ้อโกง' });
    }

    // อัปเดตกฎ
    const updatedRule = {
      ...ruleData,
      updatedAt: new Date()
    };

    await db.update(fraudRules)
      .set(updatedRule)
      .where(eq(fraudRules.id, ruleId));

    res.json({ message: 'อัปเดตกฎตรวจจับการฉ้อโกงเรียบร้อย' });
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการอัปเดตกฎตรวจจับการฉ้อโกง:', error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการอัปเดตกฎตรวจจับการฉ้อโกง' });
  }
}

// ลบกฎตรวจจับการฉ้อโกง
export async function deleteFraudRule(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'กรุณาเข้าสู่ระบบ' });
    }

    if (!isAdmin(req)) {
      return res.status(403).json({ message: 'คุณไม่มีสิทธิ์ในการเข้าถึง' });
    }

    const ruleId = parseInt(req.params.id);

    // ตรวจสอบว่ากฎนี้มีอยู่จริงหรือไม่
    const rule = await db.select().from(fraudRules)
      .where(eq(fraudRules.id, ruleId));

    if (rule.length === 0) {
      return res.status(404).json({ message: 'ไม่พบกฎตรวจจับการฉ้อโกง' });
    }

    // ลบกฎ
    await db.delete(fraudRules)
      .where(eq(fraudRules.id, ruleId));

    res.json({ message: 'ลบกฎตรวจจับการฉ้อโกงเรียบร้อย' });
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการลบกฎตรวจจับการฉ้อโกง:', error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการลบกฎตรวจจับการฉ้อโกง' });
  }
}

// ดึงการตรวจจับการฉ้อโกงทั้งหมด
export async function getFraudDetections(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'กรุณาเข้าสู่ระบบ' });
    }

    if (!isAdmin(req)) {
      return res.status(403).json({ message: 'คุณไม่มีสิทธิ์ในการเข้าถึง' });
    }

    const limit = req.query.limit ? parseInt(req.query.limit as string) : 20;
    const page = req.query.page ? parseInt(req.query.page as string) : 1;
    const offset = (page - 1) * limit;
    const status = req.query.status as string | undefined;
    const userId = req.query.userId ? parseInt(req.query.userId as string) : undefined;
    const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
    const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;

    // สร้างเงื่อนไขการค้นหา
    let conditions = [];
    
    if (status) {
      conditions.push(eq(fraudDetections.status, status));
    }
    
    if (userId) {
      conditions.push(eq(fraudDetections.userId, userId));
    }
    
    if (startDate && endDate) {
      conditions.push(
        and(
          gte(fraudDetections.createdAt, startDate),
          lte(fraudDetections.createdAt, endDate)
        )
      );
    } else if (startDate) {
      conditions.push(gte(fraudDetections.createdAt, startDate));
    } else if (endDate) {
      conditions.push(lte(fraudDetections.createdAt, endDate));
    }
    
    // ดึงข้อมูลตามเงื่อนไข
    let query = db.select().from(fraudDetections);
    
    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }
    
    const detections = await query
      .orderBy(desc(fraudDetections.createdAt))
      .limit(limit)
      .offset(offset);

    // ดึงจำนวนทั้งหมด
    let countQuery = db.select({ count: db.fn.count() }).from(fraudDetections);
    
    if (conditions.length > 0) {
      countQuery = countQuery.where(and(...conditions));
    }
    
    const totalCount = await countQuery;

    // ดึงข้อมูลเพิ่มเติม (รายละเอียดกฎและการตรวจสอบสลิป)
    const detailsPromises = detections.map(async detection => {
      const rule = await db.select().from(fraudRules)
        .where(eq(fraudRules.id, detection.ruleId));
      
      const slipVerification = await db.query.slipVerifications.findFirst({
        where: eq(fraudDetections.slipVerificationId, detection.slipVerificationId),
        with: {
          user: true
        }
      });
      
      return {
        ...detection,
        rule: rule[0],
        slipVerification
      };
    });
    
    const detailsResults = await Promise.all(detailsPromises);

    res.json({
      detections: detailsResults,
      pagination: {
        total: parseInt(totalCount[0].count as string),
        page,
        limit,
        totalPages: Math.ceil(parseInt(totalCount[0].count as string) / limit)
      }
    });
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการดึงการตรวจจับการฉ้อโกง:', error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการดึงการตรวจจับการฉ้อโกง' });
  }
}

// ดึงการตรวจจับการฉ้อโกงของผู้ใช้ปัจจุบัน
export async function getUserFraudDetections(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'กรุณาเข้าสู่ระบบ' });
    }

    const userId = req.user.id;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;

    const detections = await fraudDetectionService.getUserFraudDetections(userId, limit);
    res.json(detections);
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการดึงการตรวจจับการฉ้อโกงของผู้ใช้:', error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการดึงการตรวจจับการฉ้อโกงของผู้ใช้' });
  }
}

// อัปเดตสถานะการตรวจจับการฉ้อโกง
export async function updateFraudDetectionStatus(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'กรุณาเข้าสู่ระบบ' });
    }

    if (!isAdmin(req)) {
      return res.status(403).json({ message: 'คุณไม่มีสิทธิ์ในการเข้าถึง' });
    }

    const detectionId = parseInt(req.params.id);
    const { status, details } = req.body;

    // ตรวจสอบว่าการตรวจจับนี้มีอยู่จริงหรือไม่
    const detection = await db.select().from(fraudDetections)
      .where(eq(fraudDetections.id, detectionId));

    if (detection.length === 0) {
      return res.status(404).json({ message: 'ไม่พบการตรวจจับการฉ้อโกง' });
    }

    // อัปเดตสถานะ
    await db.update(fraudDetections)
      .set({
        status,
        details: details || detection[0].details,
        reviewedBy: req.user.id,
        reviewedAt: new Date(),
        updatedAt: new Date()
      })
      .where(eq(fraudDetections.id, detectionId));

    res.json({ message: 'อัปเดตสถานะการตรวจจับการฉ้อโกงเรียบร้อย' });
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการอัปเดตสถานะการตรวจจับการฉ้อโกง:', error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการอัปเดตสถานะการตรวจจับการฉ้อโกง' });
  }
}