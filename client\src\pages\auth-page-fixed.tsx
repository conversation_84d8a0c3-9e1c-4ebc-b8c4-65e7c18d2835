import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { Crown, LockKeyhole, Lock, MailIcon, PhoneCall, Laugh, ArrowRight, CheckCircle, User, Phone } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { useAuth } from "@/hooks/use-auth";
import axios from "axios";
import { Navbar } from "@/components/layouts/navbar";
import { Footer } from "@/components/layouts/footer";
import { motion, AnimatePresence } from "framer-motion";

// สคีมาสำหรับฟอร์มเข้าสู่ระบบ
const loginSchema = z.object({
  username: z.string().min(1, "กรุณากรอกชื่อผู้ใช้"),
  password: z.string().min(1, "กรุณากรอกรหัสผ่าน"),
});

// สคีมาสำหรับฟอร์มลงทะเบียน
const registerSchema = z.object({
  username: z.string().min(3, "ชื่อผู้ใช้ต้องมีอย่างน้อย 3 ตัวอักษร").max(50, "ชื่อผู้ใช้ต้องไม่เกิน 50 ตัวอักษร"),
  email: z.string().email("อีเมลไม่ถูกต้อง"),
  phoneNumber: z.string().min(10, "เบอร์โทรศัพท์ไม่ถูกต้อง").max(15, "เบอร์โทรศัพท์ไม่ถูกต้อง").optional().or(z.literal("")),
  firstName: z.string().min(1, "กรุณากรอกชื่อจริง").max(100, "ชื่อต้องไม่เกิน 100 ตัวอักษร"),
  lastName: z.string().min(1, "กรุณากรอกนามสกุล").max(100, "นามสกุลต้องไม่เกิน 100 ตัวอักษร"),
  companyName: z.string().max(100, "ชื่อบริษัทต้องไม่เกิน 100 ตัวอักษร").optional().or(z.literal("")),
  password: z.string().min(8, "รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร"),
  confirmPassword: z.string().min(1, "กรุณายืนยันรหัสผ่าน"),
  verificationMethod: z.enum(["email", "phone"]).default("email"),
}).refine((data) => data.password === data.confirmPassword, {
  message: "รหัสผ่านไม่ตรงกัน",
  path: ["confirmPassword"],
});

// สคีมาสำหรับฟอร์มลืมรหัสผ่าน
const forgotPasswordSchema = z.object({
  identifier: z.string().min(1, "กรุณากรอกอีเมลหรือเบอร์โทรศัพท์"),
  verificationMethod: z.enum(["email", "phone"]).default("email"),
});

// สคีมาสำหรับฟอร์มรีเซ็ตรหัสผ่าน
const resetPasswordSchema = z.object({
  verificationCode: z.string().min(1, "กรุณากรอกรหัสยืนยัน"),
  newPassword: z.string().min(8, "รหัสผ่านใหม่ต้องมีอย่างน้อย 8 ตัวอักษร"),
  confirmPassword: z.string().min(1, "กรุณายืนยันรหัสผ่านใหม่"),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "รหัสผ่านไม่ตรงกัน",
  path: ["confirmPassword"],
});

// ประเภทข้อมูลสำหรับฟอร์ม
type LoginFormValues = z.infer<typeof loginSchema>;
type RegisterFormValues = z.infer<typeof registerSchema>;
type ForgotPasswordFormValues = z.infer<typeof forgotPasswordSchema>;
type ResetPasswordFormValues = z.infer<typeof resetPasswordSchema>;

export default function AuthPageFixed() {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState<"login" | "register" | "verify" | "forgot-password">("login");
  const [lightningEffect, setLightningEffect] = useState(false);
  
  // คำคมไทยแบบเทพๆ กวนๆ
  const epicQuotes = [
    "การยืนยันตัวตนเหมือนการได้พบกับเทพเจ้า ต้องมีความอดทน",
    "ล็อกอินวันนี้ รับพรจากเทพเจ้าฟรี ไม่มีค่าใช้จ่าย",
    "อย่าลืมรหัสผ่าน เพราะแม้แต่เทพเจ้าก็ช่วยคุณไม่ได้",
    "สมัครสมาชิกวันนี้ รับส่วนลด 10% จากพระอิศวร",
    "เทพเจ้าก็ต้องยืนยันอีเมล เราเป็นใครจะไม่ต้องยืนยัน?",
    "หน้าล็อกอินที่สวยที่สุดในสามโลก",
    "รหัสผ่านควรมีความซับซ้อนพอๆ กับการต่อสู้ของเทพเจ้า",
    "ความปลอดภัยมาก่อนเสมอ แม้แต่เมื่อคุณเป็นเทพเจ้า",
  ];
  const [quote, setQuote] = useState(epicQuotes[Math.floor(Math.random() * epicQuotes.length)]);
  const [location, setLocation] = useLocation();
  
  // เมื่อเปลี่ยน tab เป็น forgot-password ให้นำทางไปยังหน้า password-reset
  useEffect(() => {
    if (activeTab === "forgot-password") {
      setLocation("/password-reset");
      // กลับมาที่แท็บ login หลังจากเด้งไปที่หน้า password-reset
      setActiveTab("login");
    }
  }, [activeTab, setLocation]);
  
  const { user, loginMutation, registerMutation } = useAuth();
  const [registrationStep, setRegistrationStep] = useState<"form" | "verification">("form");
  const [forgotPasswordStep, setForgotPasswordStep] = useState<"request" | "verify" | "reset">("request");
  const [verificationMethod, setVerificationMethod] = useState<"email" | "phone">("email");
  const [verificationCode, setVerificationCode] = useState("");
  const [isVerifying, setIsVerifying] = useState(false);
  const [isResettingPassword, setIsResettingPassword] = useState(false);
  const [secondsLeft, setSecondsLeft] = useState(0);
  const [tempUserData, setTempUserData] = useState<any>(null);
  const [resetPasswordData, setResetPasswordData] = useState<{
    identifier: string;
    type: "email" | "phone";
    userId?: number;
  } | null>(null);
  const [userVerificationStatus, setUserVerificationStatus] = useState<{
    email_verified?: boolean;
    phone_verified?: boolean;
  }>({});

  // สร้าง Form สำหรับการเข้าสู่ระบบ
  const loginForm = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: "",
      password: "",
    },
  });

  // สร้าง Form สำหรับการลงทะเบียน
  const registerForm = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      username: "",
      email: "",
      phoneNumber: "",
      firstName: "",
      lastName: "",
      companyName: "",
      password: "",
      confirmPassword: "",
      verificationMethod: "email",
    },
  });
  
  // สร้าง Form สำหรับการขอรีเซ็ตรหัสผ่าน
  const forgotPasswordForm = useForm<ForgotPasswordFormValues>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      identifier: "",
      verificationMethod: "email",
    },
  });
  
  // สร้าง Form สำหรับการรีเซ็ตรหัสผ่านหลังจากยืนยันตัวตน
  const resetPasswordForm = useForm<ResetPasswordFormValues>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      verificationCode: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  // เมื่อเปลี่ยนวิธีการยืนยัน
  useEffect(() => {
    const subscription = registerForm.watch((value, { name }) => {
      if (name === "verificationMethod") {
        setVerificationMethod(value.verificationMethod as "email" | "phone");
      }
    });
    return () => subscription.unsubscribe();
  }, [registerForm.watch]);

  // เช็คสถานะการยืนยันบัญชีเมื่อเข้าสู่ระบบแล้ว
  useEffect(() => {
    if (user) {
      // ตรวจสอบว่าผู้ใช้ยืนยันอีเมลหรือเบอร์โทรแล้วหรือไม่
      const needVerification = !user.email_verified && !user.phone_verified;
      
      // ถ้ายังไม่ได้ยืนยัน ให้อยู่ที่หน้านี้และแสดงแท็บยืนยัน
      if (needVerification) {
        setUserVerificationStatus({
          email_verified: user.email_verified || false,
          phone_verified: user.phone_verified || false
        });
        setActiveTab("verify");
      } 
      else {
        // ถ้ายืนยันแล้ว ไปที่หน้าแดชบอร์ด
        setLocation("/dashboard");
      }
    }
  }, [user]);

  // นับเวลาถอยหลัง
  useEffect(() => {
    if (secondsLeft <= 0) return;
    
    const timer = setInterval(() => {
      setSecondsLeft(prev => prev - 1);
    }, 1000);
    
    return () => clearInterval(timer);
  }, [secondsLeft]);
  
  // เมื่อเปลี่ยนวิธีการยืนยันสำหรับลืมรหัสผ่าน
  useEffect(() => {
    const subscription = forgotPasswordForm.watch((value, { name }) => {
      if (name === "verificationMethod") {
        setVerificationMethod(value.verificationMethod as "email" | "phone");
      }
    });
    return () => subscription.unsubscribe();
  }, [forgotPasswordForm.watch]);

  // สร้างเอฟเฟกต์ฟ้าแลบสุ่ม
  useEffect(() => {
    const interval = setInterval(() => {
      // สุ่มว่าจะแสดงเอฟเฟกต์ฟ้าแลบหรือไม่ (โอกาส 10%)
      const shouldShow = Math.random() < 0.1;
      
      if (shouldShow) {
        setLightningEffect(true);
        
        // ซ่อนเอฟเฟกต์หลังจาก 100ms
        setTimeout(() => {
          setLightningEffect(false);
        }, 100);
      }
    }, 3000); // ตรวจสอบทุก 3 วินาที
    
    return () => clearInterval(interval);
  }, []);

  // ส่งคำขอเข้าสู่ระบบ
  const onLoginSubmit = (values: LoginFormValues) => {
    loginMutation.mutate(values);
  };

  // ส่งคำขอลงทะเบียนขั้นแรก - ส่งข้อมูลและรับรหัสยืนยัน
  const onRegisterSubmit = async (values: RegisterFormValues) => {
    try {
      // ลบ confirmPassword ออกเพราะไม่ต้องส่งไปยัง API
      const { confirmPassword, ...registerData } = values;
      
      // เก็บข้อมูลชั่วคราวสำหรับส่งเมื่อยืนยันเรียบร้อย
      setTempUserData(registerData);
      
      // ส่งคำขอรหัสยืนยัน
      const identifier = values.verificationMethod === "email" 
        ? values.email 
        : values.phoneNumber;
        
      // ส่ง API เพื่อขอรหัสยืนยัน
      const response = await axios.post("/api/verification/send", {
        type: values.verificationMethod,
        identifier,
        userId: 0, // ใช้ 0 สำหรับผู้ใช้ใหม่ที่ยังไม่มี ID
      });
      
      if (response.data.success) {
        toast({
          title: "ส่งรหัสยืนยันสำเร็จ",
          description: response.data.message,
        });
        
        // เปลี่ยนสถานะเป็นรอยืนยัน
        setRegistrationStep("verification");
        
        // ตั้งเวลาถอยหลังสำหรับการขอรหัสใหม่
        setSecondsLeft(60); // 1 นาที
      } else {
        toast({
          variant: "destructive",
          title: "ไม่สามารถส่งรหัสยืนยันได้",
          description: response.data.message || "กรุณาลองใหม่อีกครั้ง",
        });
      }
    } catch (error: any) {
      console.error("Registration error:", error);
      toast({
        variant: "destructive",
        title: "ไม่สามารถส่งรหัสยืนยันได้",
        description: error.response?.data?.message || "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง",
      });
    }
  };
  
  // ตรวจสอบรหัสยืนยัน
  const verifyCode = async () => {
    if (!verificationCode || verificationCode.length < 6) {
      toast({
        variant: "destructive",
        title: "รหัสยืนยันไม่ถูกต้อง",
        description: "กรุณากรอกรหัสยืนยัน 6 หลัก",
      });
      return;
    }
    
    try {
      setIsVerifying(true);
      const identifier = tempUserData?.verificationMethod === "email" 
        ? tempUserData.email 
        : tempUserData.phoneNumber;
        
      // ส่ง API เพื่อตรวจสอบรหัสยืนยัน
      const verifyResponse = await axios.post("/api/verification/verify", {
        type: tempUserData?.verificationMethod,
        identifier,
        code: verificationCode,
      });
      
      if (verifyResponse.data.success) {
        // ลงทะเบียนผู้ใช้หลังจากยืนยันสำเร็จ
        registerMutation.mutate(tempUserData);
        
        // รีเซ็ตสถานะกลับไปที่ฟอร์ม
        setRegistrationStep("form");
        setVerificationCode("");
        setTempUserData(null);
      } else {
        toast({
          variant: "destructive",
          title: "รหัสยืนยันไม่ถูกต้อง",
          description: verifyResponse.data.message || "กรุณาตรวจสอบรหัสและลองใหม่อีกครั้ง",
        });
      }
    } catch (error: any) {
      console.error("Verification error:", error);
      toast({
        variant: "destructive",
        title: "ไม่สามารถยืนยันรหัสได้",
        description: error.response?.data?.message || "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง",
      });
    } finally {
      setIsVerifying(false);
    }
  };
  
  // ขอรหัสยืนยันใหม่
  const resendVerificationCode = async () => {
    if (secondsLeft > 0) return;
    
    try {
      // กรณีที่เป็นการสมัครใหม่
      if (tempUserData) {
        const identifier = tempUserData.verificationMethod === "email" 
          ? tempUserData.email 
          : tempUserData.phoneNumber;
          
        // ส่ง API เพื่อขอรหัสยืนยันใหม่
        const response = await axios.post("/api/verification/send", {
          type: tempUserData.verificationMethod,
          identifier,
          userId: 0,
        });
        
        if (response.data.success) {
          toast({
            title: "ส่งรหัสยืนยันใหม่สำเร็จ",
            description: response.data.message,
          });
          setSecondsLeft(60); // ตั้งเวลารอใหม่
        } else {
          toast({
            variant: "destructive",
            title: "ไม่สามารถส่งรหัสยืนยันได้",
            description: response.data.message || "กรุณาลองใหม่อีกครั้ง",
          });
        }
      } 
      // กรณีที่เป็นผู้ใช้เก่า
      else if (user) {
        const identifier = verificationMethod === "email" 
          ? user.email 
          : user.phoneNumber;
          
        if (!identifier) {
          toast({
            variant: "destructive",
            title: "ไม่สามารถส่งรหัสยืนยันได้",
            description: "ไม่พบข้อมูลสำหรับการยืนยัน กรุณาตรวจสอบข้อมูลส่วนตัวของคุณ",
          });
          return;
        }
          
        // ส่ง API เพื่อขอรหัสยืนยันใหม่
        const response = await axios.post("/api/verification/send", {
          type: verificationMethod,
          identifier,
          userId: user.id,
        });
        
        if (response.data.success) {
          toast({
            title: "ส่งรหัสยืนยันใหม่สำเร็จ",
            description: response.data.message,
          });
          setSecondsLeft(60); // ตั้งเวลารอใหม่
        } else {
          toast({
            variant: "destructive",
            title: "ไม่สามารถส่งรหัสยืนยันได้",
            description: response.data.message || "กรุณาลองใหม่อีกครั้ง",
          });
        }
      }
      // กรณีที่เป็นการลืมรหัสผ่าน
      else if (resetPasswordData) {
        const { identifier, type } = resetPasswordData;
        
        // ส่ง API เพื่อขอรหัสยืนยันใหม่
        const response = await axios.post("/api/password-reset/send", {
          type,
          identifier,
        });
        
        if (response.data.success) {
          toast({
            title: "ส่งรหัสยืนยันใหม่สำเร็จ",
            description: response.data.message,
          });
          setSecondsLeft(60); // ตั้งเวลารอใหม่
        } else {
          toast({
            variant: "destructive",
            title: "ไม่สามารถส่งรหัสยืนยันได้",
            description: response.data.message || "กรุณาลองใหม่อีกครั้ง",
          });
        }
      }
    } catch (error: any) {
      console.error("Resend verification code error:", error);
      toast({
        variant: "destructive",
        title: "ไม่สามารถส่งรหัสยืนยันใหม่ได้",
        description: error.response?.data?.message || "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง",
      });
    }
  };
  
  // ยืนยันบัญชีผู้ใช้ที่มีอยู่แล้ว
  const verifyExistingUser = async () => {
    if (!verificationCode || verificationCode.length < 6) {
      toast({
        variant: "destructive",
        title: "รหัสยืนยันไม่ถูกต้อง",
        description: "กรุณากรอกรหัสยืนยัน 6 หลัก",
      });
      return;
    }
    
    if (!user) {
      toast({
        variant: "destructive",
        title: "ไม่สามารถยืนยันบัญชีได้",
        description: "กรุณาเข้าสู่ระบบก่อนทำการยืนยัน",
      });
      return;
    }
    
    try {
      setIsVerifying(true);
      const identifier = verificationMethod === "email" 
        ? user.email 
        : user.phoneNumber;
        
      if (!identifier) {
        toast({
          variant: "destructive",
          title: "ไม่สามารถยืนยันรหัสได้",
          description: "ไม่พบข้อมูลสำหรับการยืนยัน กรุณาตรวจสอบข้อมูลส่วนตัวของคุณ",
        });
        return;
      }
        
      // ส่ง API เพื่อตรวจสอบรหัสยืนยัน
      const verifyResponse = await axios.post("/api/verification/verify", {
        type: verificationMethod,
        identifier,
        code: verificationCode,
        userId: user.id,
      });
      
      if (verifyResponse.data.success) {
        toast({
          title: "ยืนยันบัญชีสำเร็จ",
          description: "บัญชีของคุณได้รับการยืนยันแล้ว",
        });
        
        // ไปที่หน้าแดชบอร์ด
        setLocation("/dashboard");
      } else {
        toast({
          variant: "destructive",
          title: "รหัสยืนยันไม่ถูกต้อง",
          description: verifyResponse.data.message || "กรุณาตรวจสอบรหัสและลองใหม่อีกครั้ง",
        });
      }
    } catch (error: any) {
      console.error("Verification error:", error);
      toast({
        variant: "destructive",
        title: "ไม่สามารถยืนยันรหัสได้",
        description: error.response?.data?.message || "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง",
      });
    } finally {
      setIsVerifying(false);
    }
  };

  return (
    <div className={`relative min-h-screen overflow-x-hidden bg-background flex flex-col ${lightningEffect ? 'bg-white' : ''}`}>
      {/* เอฟเฟกต์ฟ้าแลบ */}
      <div 
        className="fixed inset-0 bg-white/40 z-50 pointer-events-none transition-opacity duration-100"
        style={{ opacity: lightningEffect ? 1 : 0 }}
      />
      
      <Navbar />

      <main className="relative z-10 flex-1 flex flex-col items-center justify-center p-4 mt-14">
        {/* ส่วนหัว */}
        <motion.div 
          className="text-center mb-8 relative"
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ type: "spring", stiffness: 200, damping: 20 }}
        >
          <div className="relative inline-block">
            <motion.div 
              className="absolute -inset-4 rounded-full bg-gradient-to-r from-amber-600/30 via-pink-500/30 to-purple-600/30 blur-2xl"
              animate={{ 
                scale: [1, 1.1, 1],
                rotate: [0, 5, 0]
              }}
              transition={{ 
                duration: 5, 
                repeat: Infinity,
                repeatType: "reverse" 
              }}
            />
            <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-amber-500 via-pink-500 to-purple-600 drop-shadow-sm">
              SLIPKUY
            </h1>
          </div>
          <p className="text-muted-foreground max-w-md mx-auto">
            {quote}
          </p>
        </motion.div>

        {/* การ์ดล็อกอิน/สมัครสมาชิก */}
        <div className="w-full max-w-md">
          <Card className="border-2 border-muted/80 shadow-xl bg-background/95 backdrop-blur-sm">
            <CardHeader>
              <div className="flex justify-center mb-2">
                <motion.div
                  animate={{ 
                    y: [0, -10, 0],
                    rotateZ: [0, 5, 0]
                  }}
                  transition={{ 
                    duration: 4, 
                    repeat: Infinity,
                    repeatType: "reverse" 
                  }}
                  className="w-12 h-12 bg-gradient-to-br from-amber-500 via-pink-500 to-purple-600 rounded-full flex items-center justify-center text-white"
                >
                  <Crown className="w-6 h-6" />
                </motion.div>
              </div>
              <CardTitle className="text-center text-2xl font-bold">
                {activeTab === "login" && "เข้าสู่ระบบ"}
                {activeTab === "register" && "สมัครสมาชิก"}
                {activeTab === "verify" && "ยืนยันบัญชีผู้ใช้"}
              </CardTitle>
              <CardDescription className="text-center">
                {activeTab === "login" && "เข้าสู่ระบบเพื่อใช้งานแพลตฟอร์ม SLIPKUY"}
                {activeTab === "register" && "สร้างบัญชีเพื่อเริ่มต้นใช้งาน SLIPKUY"}
                {activeTab === "verify" && "ยืนยันบัญชีของคุณเพื่อการใช้งานที่ครบถ้วน"}
              </CardDescription>
            </CardHeader>
            
            <CardContent>
              {(activeTab === "login" || activeTab === "register") && (
                <Tabs 
                  value={activeTab} 
                  onValueChange={(value) => setActiveTab(value as "login" | "register")}
                  className="w-full"
                >
                  <TabsList className="grid w-full grid-cols-2 mb-6">
                    <TabsTrigger value="login" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
                      <LockKeyhole className="w-4 h-4 mr-2" /> เข้าสู่ระบบ
                    </TabsTrigger>
                    <TabsTrigger value="register" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
                      <User className="w-4 h-4 mr-2" /> สมัครสมาชิก
                    </TabsTrigger>
                  </TabsList>
                  
                  {/* ฟอร์มเข้าสู่ระบบ */}
                  <TabsContent value="login" className="mt-0">
                    <Form {...loginForm}>
                      <form onSubmit={loginForm.handleSubmit(onLoginSubmit)} className="space-y-4">
                        <FormField
                          control={loginForm.control}
                          name="username"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>ชื่อผู้ใช้</FormLabel>
                              <FormControl>
                                <Input 
                                  placeholder="ชื่อผู้ใช้ของคุณ" 
                                  {...field} 
                                  className="bg-card"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={loginForm.control}
                          name="password"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>รหัสผ่าน</FormLabel>
                              <FormControl>
                                <Input 
                                  type="password" 
                                  placeholder="รหัสผ่านของคุณ" 
                                  {...field} 
                                  className="bg-card"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <div className="pt-2">
                          <Button 
                            type="submit" 
                            className="w-full bg-gradient-to-r from-amber-500 via-pink-500 to-purple-600 text-white hover:from-amber-600 hover:via-pink-600 hover:to-purple-700"
                            disabled={loginMutation.isPending}
                          >
                            {loginMutation.isPending ? (
                              <div className="flex items-center">
                                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                                กำลังเข้าสู่ระบบ...
                              </div>
                            ) : (
                              <div className="flex items-center justify-center">
                                <LockKeyhole className="w-4 h-4 mr-2" />
                                เข้าสู่ระบบ
                              </div>
                            )}
                          </Button>
                        </div>
                        
                        <div className="text-center pt-2">
                          <Button
                            type="button"
                            variant="link"
                            className="text-primary"
                            onClick={() => setActiveTab("forgot-password")}
                          >
                            ลืมรหัสผ่าน?
                          </Button>
                        </div>
                      </form>
                    </Form>
                  </TabsContent>
                  
                  {/* ฟอร์มสมัครสมาชิก */}
                  <TabsContent value="register" className="mt-0">
                    <AnimatePresence mode="wait">
                      {registrationStep === "form" ? (
                        <motion.div
                          key="registerForm"
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: -20 }}
                          transition={{ duration: 0.3 }}
                        >
                          <Form {...registerForm}>
                            <form onSubmit={registerForm.handleSubmit(onRegisterSubmit)} className="space-y-4">
                              <FormField
                                control={registerForm.control}
                                name="username"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>ชื่อผู้ใช้</FormLabel>
                                    <FormControl>
                                      <Input placeholder="ชื่อผู้ใช้ของคุณ" {...field} className="bg-card" />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <FormField
                                  control={registerForm.control}
                                  name="firstName"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>ชื่อจริง</FormLabel>
                                      <FormControl>
                                        <Input placeholder="ชื่อจริง" {...field} className="bg-card" />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                                
                                <FormField
                                  control={registerForm.control}
                                  name="lastName"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>นามสกุล</FormLabel>
                                      <FormControl>
                                        <Input placeholder="นามสกุล" {...field} className="bg-card" />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              </div>
                              
                              <FormField
                                control={registerForm.control}
                                name="email"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>อีเมล</FormLabel>
                                    <FormControl>
                                      <Input 
                                        type="email" 
                                        placeholder="<EMAIL>" 
                                        {...field} 
                                        className="bg-card"
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              
                              <FormField
                                control={registerForm.control}
                                name="phoneNumber"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>เบอร์โทรศัพท์</FormLabel>
                                    <FormControl>
                                      <Input 
                                        type="tel" 
                                        placeholder="0812345678" 
                                        {...field} 
                                        className="bg-card"
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              
                              <FormField
                                control={registerForm.control}
                                name="companyName"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>ชื่อบริษัท (ไม่บังคับ)</FormLabel>
                                    <FormControl>
                                      <Input placeholder="ชื่อบริษัทของคุณ" {...field} className="bg-card" />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              
                              <FormField
                                control={registerForm.control}
                                name="password"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>รหัสผ่าน</FormLabel>
                                    <FormControl>
                                      <Input 
                                        type="password" 
                                        placeholder="รหัสผ่านของคุณ" 
                                        {...field} 
                                        className="bg-card"
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              
                              <FormField
                                control={registerForm.control}
                                name="confirmPassword"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>ยืนยันรหัสผ่าน</FormLabel>
                                    <FormControl>
                                      <Input 
                                        type="password" 
                                        placeholder="ยืนยันรหัสผ่าน" 
                                        {...field} 
                                        className="bg-card"
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              
                              <FormField
                                control={registerForm.control}
                                name="verificationMethod"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>วิธีการยืนยันบัญชี</FormLabel>
                                    <div className="flex gap-4">
                                      <Button
                                        type="button"
                                        variant={field.value === "email" ? "default" : "outline"}
                                        className={field.value === "email" ? "flex-1 bg-gradient-to-r from-amber-500 via-pink-500 to-purple-600 text-white" : "flex-1"}
                                        onClick={() => field.onChange("email")}
                                      >
                                        <MailIcon className="w-4 h-4 mr-2" />
                                        อีเมล
                                      </Button>
                                      <Button
                                        type="button"
                                        variant={field.value === "phone" ? "default" : "outline"}
                                        className={field.value === "phone" ? "flex-1 bg-gradient-to-r from-amber-500 via-pink-500 to-purple-600 text-white" : "flex-1"}
                                        onClick={() => field.onChange("phone")}
                                      >
                                        <PhoneCall className="w-4 h-4 mr-2" />
                                        SMS
                                      </Button>
                                    </div>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              
                              <Button 
                                type="submit" 
                                className="w-full bg-gradient-to-r from-amber-500 via-pink-500 to-purple-600 text-white hover:from-amber-600 hover:via-pink-600 hover:to-purple-700 mt-6"
                              >
                                <div className="flex items-center justify-center">
                                  <User className="w-4 h-4 mr-2" />
                                  สมัครสมาชิก
                                </div>
                              </Button>
                            </form>
                          </Form>
                        </motion.div>
                      ) : (
                        <motion.div
                          key="verification"
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: -20 }}
                          transition={{ duration: 0.3 }}
                          className="space-y-4"
                        >
                          <div className="text-center mb-6">
                            <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 mb-2">
                              {verificationMethod === "email" ? (
                                <MailIcon className="w-6 h-6 text-primary" />
                              ) : (
                                <PhoneCall className="w-6 h-6 text-primary" />
                              )}
                            </div>
                            <h3 className="text-lg font-medium">
                              {verificationMethod === "email" ? "ยืนยันอีเมล" : "ยืนยันเบอร์โทรศัพท์"}
                            </h3>
                            <p className="text-sm text-muted-foreground">
                              {verificationMethod === "email" 
                                ? `รหัสยืนยันถูกส่งไปยังอีเมล ${tempUserData?.email}` 
                                : `รหัสยืนยันถูกส่งไปยังเบอร์ ${tempUserData?.phoneNumber}`}
                            </p>
                          </div>
                          
                          <div className="space-y-4">
                            <div>
                              <label htmlFor="verificationCode" className="block text-sm font-medium mb-1">
                                รหัสยืนยัน
                              </label>
                              <Input
                                id="verificationCode"
                                value={verificationCode}
                                onChange={(e) => setVerificationCode(e.target.value)}
                                placeholder="กรอกรหัสยืนยัน 6 หลัก"
                                className="bg-card"
                              />
                            </div>
                            
                            <div className="flex flex-col sm:flex-row gap-2">
                              <Button 
                                type="button"
                                className="flex-1 bg-gradient-to-r from-amber-500 via-pink-500 to-purple-600 text-white hover:from-amber-600 hover:via-pink-600 hover:to-purple-700"
                                onClick={verifyCode}
                                disabled={isVerifying}
                              >
                                {isVerifying ? (
                                  <div className="flex items-center">
                                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                                    กำลังยืนยัน...
                                  </div>
                                ) : (
                                  <div className="flex items-center justify-center">
                                    <CheckCircle className="w-4 h-4 mr-2" />
                                    ยืนยันรหัส
                                  </div>
                                )}
                              </Button>
                              
                              <Button 
                                type="button"
                                variant="outline"
                                className="flex-1"
                                onClick={resendVerificationCode}
                                disabled={secondsLeft > 0}
                              >
                                {secondsLeft > 0 ? (
                                  `ส่งรหัสใหม่ (${secondsLeft}s)`
                                ) : (
                                  "ส่งรหัสใหม่"
                                )}
                              </Button>
                            </div>
                            
                            <Button 
                              type="button"
                              variant="link"
                              className="w-full text-primary"
                              onClick={() => setRegistrationStep("form")}
                            >
                              ย้อนกลับ
                            </Button>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </TabsContent>
                </Tabs>
              )}
              
              {/* ฟอร์มยืนยันบัญชีผู้ใช้ */}
              {activeTab === "verify" && (
                <div className="space-y-4">
                  <div className="text-center mb-6">
                    <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary/10 mb-4">
                      <Laugh className="w-8 h-8 text-primary" />
                    </div>
                    <h3 className="text-lg font-medium">
                      ยืนยันบัญชีผู้ใช้
                    </h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      คุณจำเป็นต้องยืนยันตัวตนก่อนใช้งานระบบต่อ
                    </p>
                    
                    {/* แสดงสถานะการยืนยัน */}
                    <div className="flex justify-center gap-4 mb-4">
                      <div className={`text-sm px-3 py-1 rounded-full ${userVerificationStatus.email_verified ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                        {userVerificationStatus.email_verified ? 'อีเมลยืนยันแล้ว ✓' : 'อีเมลยังไม่ยืนยัน'}
                      </div>
                      <div className={`text-sm px-3 py-1 rounded-full ${userVerificationStatus.phone_verified ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                        {userVerificationStatus.phone_verified ? 'โทรศัพท์ยืนยันแล้ว ✓' : 'โทรศัพท์ยังไม่ยืนยัน'}
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        เลือกวิธีการยืนยัน
                      </label>
                      <div className="flex gap-4">
                        <Button
                          type="button"
                          variant={verificationMethod === "email" ? "default" : "outline"}
                          className={verificationMethod === "email" ? "flex-1 bg-gradient-to-r from-amber-500 via-pink-500 to-purple-600 text-white" : "flex-1"}
                          onClick={() => setVerificationMethod("email")}
                          disabled={userVerificationStatus.email_verified}
                        >
                          <MailIcon className="w-4 h-4 mr-2" />
                          {userVerificationStatus.email_verified ? 'อีเมลยืนยันแล้ว' : 'ยืนยันด้วยอีเมล'}
                        </Button>
                        <Button
                          type="button"
                          variant={verificationMethod === "phone" ? "default" : "outline"}
                          className={verificationMethod === "phone" ? "flex-1 bg-gradient-to-r from-amber-500 via-pink-500 to-purple-600 text-white" : "flex-1"}
                          onClick={() => setVerificationMethod("phone")}
                          disabled={userVerificationStatus.phone_verified}
                        >
                          <PhoneCall className="w-4 h-4 mr-2" />
                          {userVerificationStatus.phone_verified ? 'เบอร์โทรยืนยันแล้ว' : 'ยืนยันด้วย SMS'}
                        </Button>
                      </div>
                    </div>
                    
                    {(!userVerificationStatus.email_verified || !userVerificationStatus.phone_verified) && (
                      <>
                        <div>
                          <label htmlFor="verificationCode" className="block text-sm font-medium mb-1">
                            รหัสยืนยัน
                          </label>
                          <Input
                            id="verificationCode"
                            value={verificationCode}
                            onChange={(e) => setVerificationCode(e.target.value)}
                            placeholder="กรอกรหัสยืนยัน 6 หลัก"
                            className="bg-card"
                          />
                          <div className="mt-1 text-sm text-muted-foreground">
                            {verificationMethod === "email" 
                              ? "รหัสจะถูกส่งไปยังอีเมลของคุณ"
                              : "รหัสจะถูกส่งไปยังเบอร์โทรศัพท์ของคุณ"}
                          </div>
                        </div>
                        
                        <div className="flex flex-col sm:flex-row gap-2">
                          <Button 
                            type="button"
                            className="flex-1 bg-gradient-to-r from-amber-500 via-pink-500 to-purple-600 text-white hover:from-amber-600 hover:via-pink-600 hover:to-purple-700"
                            onClick={verifyExistingUser}
                            disabled={isVerifying}
                          >
                            {isVerifying ? (
                              <div className="flex items-center">
                                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                                กำลังยืนยัน...
                              </div>
                            ) : (
                              <div className="flex items-center justify-center">
                                <CheckCircle className="w-4 h-4 mr-2" />
                                ยืนยันรหัส
                              </div>
                            )}
                          </Button>
                          
                          <Button 
                            type="button"
                            variant="outline"
                            className="flex-1"
                            onClick={resendVerificationCode}
                            disabled={secondsLeft > 0}
                          >
                            {secondsLeft > 0 ? (
                              `ส่งรหัสใหม่ (${secondsLeft}s)`
                            ) : (
                              "ส่งรหัสใหม่"
                            )}
                          </Button>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
            
            <CardFooter className="flex flex-col">
              <div className="w-full pt-4 text-center text-sm text-muted-foreground">
                <p>
                  สงวนลิขสิทธิ์ให้เทพพระเจ้า © {new Date().getFullYear()} สลีปคุย (SLIPKUY)
                </p>
              </div>
            </CardFooter>
          </Card>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}