import express from 'express';
import { getApiStats, getRealTimeApiLogs, getErrorAnalysis, generateAiInsights, generateAiResponse } from './analytics-api-fix';

const router = express.Router();

// API statistics routes
router.get('/stats', getApiStats);
router.get('/logs', getRealTimeApiLogs); // แก้ไขให้ตรงกับการเรียกใช้ในไคลเอนต์
router.get('/errors', getErrorAnalysis); // แก้ไขให้ตรงกับการเรียกใช้ในไคลเอนต์
router.get('/ai-insights', generateAiInsights); // แก้ไขให้ตรงกับการเรียกใช้ในไคลเอนต์
router.post('/ai-query', generateAiResponse);

// เพิ่ม debug endpoint
router.get('/debug', (req, res) => {
  res.json({
    isAuthenticated: !!req.user,
    userId: req.user ? req.user.id : null,
    timestamp: new Date().toISOString()
  });
});

export default router;