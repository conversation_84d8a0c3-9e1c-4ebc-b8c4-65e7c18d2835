import { useCallback, useEffect, useRef, useState } from 'react';
import { io, Socket } from 'socket.io-client';

interface SocketMessage {
  [key: string]: any;
}

/**
 * ContextProvider สำหรับการจัดการการเชื่อมต่อ Socket.IO ทั่วทั้งแอปพลิเคชัน
 */
import React, { createContext, useContext } from 'react';

// สร้าง context สำหรับใช้งาน Socket.IO ทั่วทั้งแอป
interface SocketIOContextType {
  isConnected: boolean;
  error: Error | null;
  messages: Record<string, SocketMessage[]>;
  lastMessage: { event: string; data: SocketMessage } | null;
  joinRoom: (channel: string) => void;
  leaveRoom: (channel: string) => void;
  emit: (event: string, data: any) => void;
  on: (event: string, callback: (data: any) => void) => () => void;
  socket: Socket | null;
}

const SocketIOContext = createContext<SocketIOContextType | null>(null);

// Provider component สำหรับ Socket.IO
export function SocketIOProvider({ 
  children, 
  channels = []
}: { 
  children: React.ReactNode; 
  channels?: string[];
}) {
  const socketIO = useSocketIO(channels);
  
  return (
    <SocketIOContext.Provider value={socketIO}>
      {children}
    </SocketIOContext.Provider>
  );
}

// Custom hook สำหรับใช้งาน Socket.IO context
export const useSocketIOContext = () => {
  const context = useContext(SocketIOContext);
  if (!context) {
    throw new Error('useSocketIOContext must be used within a SocketIOProvider');
  }
  return context;
}

// ฟังก์ชันชื่อ socketManager เพื่อรักษาการเชื่อมต่อ Socket.IO แม้จะเกิดการตัดการเชื่อมต่อ
let globalSocket: Socket | null = null;
let attemptCount = 0;
const MAX_RECONNECT_ATTEMPTS = 15;

function connectToSocketIO(url: string): Socket {
  // ใช้ Socket.IO เชื่อมต่อกับเซิร์ฟเวอร์
  const socket = io(url, {
    reconnectionAttempts: MAX_RECONNECT_ATTEMPTS,
    reconnectionDelay: 1000,
    autoConnect: true,
    timeout: 60000,
    // เลือกใช้เฉพาะ polling ซึ่งมีความเสถียรสูงกว่า websocket ใน Replit
    transports: ['polling']
  });
  
  return socket;
}

/**
 * Custom hook สำหรับการใช้งาน Socket.IO เพื่อรับข้อมูลแบบเรียลไทม์
 * 
 * @param channels รายการช่องที่ต้องการรับข้อมูล
 * @returns Socket.IO state และฟังก์ชันที่เกี่ยวข้อง
 */
// ปรับการส่งออกฟังก์ชัน เพื่อแก้ไขปัญหา Fast Refresh
export const useSocketIO = (channels: string[] = []) => {
  const [isConnected, setIsConnected] = useState(false);
  const [messages, setMessages] = useState<Record<string, SocketMessage[]>>({});
  const [lastMessage, setLastMessage] = useState<{ event: string; data: SocketMessage } | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const socketRef = useRef<Socket | null>(null);
  const reconnectTimerRef = useRef<NodeJS.Timeout | null>(null);

  // เข้าร่วมห้องที่ระบุ
  const joinRoom = useCallback((channel: string) => {
    if (socketRef.current && isConnected) {
      console.log(`กำลังเข้าร่วมห้อง ${channel}`);
      socketRef.current.emit('subscribe', channel);
    } else {
      console.log(`ไม่สามารถเข้าร่วมห้อง ${channel} ได้ เนื่องจาก Socket.IO ยังไม่เชื่อมต่อ`);
    }
  }, [isConnected]);

  // ออกจากห้องที่ระบุ
  const leaveRoom = useCallback((channel: string) => {
    if (socketRef.current && isConnected) {
      socketRef.current.emit('unsubscribe', channel);
    }
  }, [isConnected]);

  // ส่งข้อมูลไปยัง Socket.IO server
  const emit = useCallback((event: string, data: any) => {
    if (socketRef.current && isConnected) {
      socketRef.current.emit(event, data);
    } else {
      console.log('ไม่สามารถส่งข้อมูลได้เนื่องจากยังไม่เชื่อมต่อ Socket.IO');
    }
  }, [isConnected]);

  // รับฟังเหตุการณ์จาก Socket.IO
  const handleReceiveEvent = useCallback((event: string, data: any) => {
    console.log(`ได้รับข้อความจาก Socket.IO:`, { event, data });
    setLastMessage({ event, data });
    setMessages(prev => ({
      ...prev,
      [event]: [...(prev[event] || []), data]
    }));
  }, []);

  // เชื่อมต่อกับ Socket.IO server
  const connect = useCallback(() => {
    try {
      // ใช้ socket ที่มีอยู่แล้วหากถูกสร้างไว้
      if (globalSocket) {
        console.log('ใช้การเชื่อมต่อ Socket.IO ที่มีอยู่แล้ว');
        socketRef.current = globalSocket;
        
        // ตรวจสอบว่า socket ยังเชื่อมต่ออยู่หรือไม่
        if (globalSocket.connected) {
          console.log('Socket.IO ยังคงเชื่อมต่ออยู่:', globalSocket.id);
          setIsConnected(true);
          setError(null);
          
          // เข้าร่วมห้องที่ระบุ
          channels.forEach(channel => {
            joinRoom(channel);
          });
        }
        
        return () => {};
      }
      
      // สร้าง URL สำหรับเชื่อมต่อ Socket.IO
      const protocol = window.location.protocol === 'https:' ? 'https://' : 'http://';
      const host = window.location.host;
      const url = `${protocol}${host}`;
      console.log('กำลังเชื่อมต่อกับ Socket.IO ที่ URL:', url);
      
      const socket = connectToSocketIO(url);
      socketRef.current = socket;
      globalSocket = socket;
      
      // การจัดการเหตุการณ์เชื่อมต่อ
      socket.on('connect', () => {
        console.log('Socket.IO connected:', socket.id);
        setIsConnected(true);
        setError(null);
        attemptCount = 0;
        
        // ยกเลิก reconnect timer หากมี
        if (reconnectTimerRef.current) {
          clearTimeout(reconnectTimerRef.current);
          reconnectTimerRef.current = null;
        }
        
        // เข้าร่วมห้องที่ระบุ
        channels.forEach(channel => {
          joinRoom(channel);
        });
      });

      // การจัดการเหตุการณ์เชื่อมต่อล้มเหลว
      socket.on('connect_error', (err) => {
        console.error('Socket.IO connection error:', err);
        console.log('Error details (JSON):', JSON.stringify(err));
        setError(new Error(`Socket.IO connection error: ${err.message || 'Unknown error'}`));
        
        // พยายามเชื่อมต่อใหม่หากยังไม่เกินจำนวนครั้งที่กำหนด
        attemptCount++;
        if (attemptCount < MAX_RECONNECT_ATTEMPTS) {
          console.log(`พยายามเชื่อมต่อใหม่ครั้งที่ ${attemptCount}`);
        }
      });

      // การจัดการเหตุการณ์เมื่อไม่สามารถเชื่อมต่อได้หลังจากพยายามหลายครั้ง
      socket.on('reconnect_failed', () => {
        console.error('Socket.IO reconnection failed after all attempts');
        setError(new Error('Socket.IO reconnection failed after all attempts'));
      });

      // การจัดการเหตุการณ์เมื่อตัดการเชื่อมต่อ
      socket.on('disconnect', (reason) => {
        console.log(`Socket.IO disconnected: ${reason}`);
        setIsConnected(false);
        
        // ถ้าถูกตัดการเชื่อมต่อโดยเซิร์ฟเวอร์ ให้ลองเชื่อมต่อใหม่
        if (reason === 'io server disconnect' || reason === 'transport close') {
          // เริ่มการเชื่อมต่อใหม่หลังจาก delay เล็กน้อย
          if (!reconnectTimerRef.current) {
            reconnectTimerRef.current = setTimeout(() => {
              console.log('พยายามเชื่อมต่อใหม่หลังจากถูกตัดการเชื่อมต่อ');
              socket.connect();
              reconnectTimerRef.current = null;
            }, 2000);
          }
        }
      });

      // การจัดการเหตุการณ์เมื่อได้รับการตอบรับการเข้าร่วมห้อง
      socket.on('subscribed', (data) => {
        console.log(`ได้รับการยืนยันการเข้าร่วมห้อง ${data.channel}:`, data.message);
        handleReceiveEvent('subscribed', data);
      });

      // การจัดการเมื่อได้รับข้อความ 'connected'
      socket.on('connected', (data) => {
        console.log('ได้รับการเชื่อมต่อ:', data);
        handleReceiveEvent('connected', data);
      });

      // การจัดการเมื่อได้รับข้อความ 'connection_test'
      socket.on('connection_test', (data) => {
        console.log('ได้รับข้อความทดสอบการเชื่อมต่อ:', data);
        handleReceiveEvent('connection_test', data);
      });

      // การจัดการเมื่อได้รับข้อความ 'channel_test'
      socket.on('channel_test', (data) => {
        console.log('ได้รับข้อความทดสอบห้อง:', data);
        handleReceiveEvent('channel_test', data);
      });
      
      // การจัดการเมื่อได้รับข้อความ 'credit_updated'
      socket.on('credit_updated', (data) => {
        console.log('ได้รับข้อมูลการอัพเดทเครดิต:', data);
        handleReceiveEvent('credit_updated', data);
      });
      
      // การจัดการเมื่อได้รับข้อความ 'package_updated'
      socket.on('package_updated', (data) => {
        console.log('ได้รับข้อมูลการอัพเดทแพ็คเกจ:', data);
        handleReceiveEvent('package_updated', data);
      });
      
      // การจัดการเมื่อได้รับข้อความ 'new_verification'
      socket.on('new_verification', (data) => {
        console.log('ได้รับข้อมูลการตรวจสอบสลิปใหม่:', data);
        handleReceiveEvent('new_verification', data);
      });

      // เก็บรายการ events ทั้งหมดที่ต้องยกเลิกเมื่อ cleanup
      const events = [
        'connect', 'connect_error', 'reconnect_failed', 'disconnect',
        'subscribed', 'connected', 'connection_test', 'channel_test',
        'credit_updated', 'package_updated', 'new_verification'
      ];
      
      return () => {
        // ไม่ต้อง disconnect socket เพราะเราใช้ global socket
        // แต่ยกเลิก event listeners
        if (socket) {
          events.forEach(event => {
            socket.off(event);
          });
        }
      };
    } catch (err: any) {
      console.error('Error creating Socket.IO connection:', err);
      setError(new Error(`Error creating Socket.IO connection: ${err.message}`));
      return () => {};
    }
  }, [channels, joinRoom, handleReceiveEvent]);

  // กำหนดตัวรับฟังเหตุการณ์สำหรับเหตุการณ์ที่ระบุ
  const on = useCallback((event: string, callback: (data: any) => void) => {
    if (socketRef.current) {
      socketRef.current.on(event, callback);
      
      // คืนค่าฟังก์ชันสำหรับลบตัวรับฟัง
      return () => {
        socketRef.current?.off(event, callback);
      };
    }
    return () => {};
  }, []);

  // เชื่อมต่อเมื่อ component ถูกโหลด
  useEffect(() => {
    const cleanup = connect();
    
    // ล้างการเชื่อมต่อเมื่อ component ถูกลบออก
    return () => {
      cleanup();
      
      // ยกเลิก reconnect timer หากมี
      if (reconnectTimerRef.current) {
        clearTimeout(reconnectTimerRef.current);
        reconnectTimerRef.current = null;
      }
      
      // ไม่ต้องปิดการเชื่อมต่อ socket ที่นี่เพราะเราใช้ global socket
      socketRef.current = null;
    };
  }, [connect]);

  // เชื่อมต่อห้องเมื่อรายการห้องหรือสถานะการเชื่อมต่อมีการเปลี่ยนแปลง
  useEffect(() => {
    if (isConnected && socketRef.current) {
      channels.forEach(channel => {
        joinRoom(channel);
      });
    }
  }, [channels, isConnected, joinRoom]);

  return {
    isConnected,
    messages,
    lastMessage,
    error,
    joinRoom,
    leaveRoom,
    emit,
    on,
    socket: socketRef.current
  };
}