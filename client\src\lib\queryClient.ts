import { QueryClient, QueryFunction } from "@tanstack/react-query";

async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    const text = (await res.text()) || res.statusText;
    throw new Error(`${res.status}: ${text}`);
  }
}

export async function apiRequest(
  method: string,
  url: string,
  data?: unknown | undefined,
  headers?: HeadersInit,
): Promise<Response> {
  const defaultHeaders: HeadersInit = data ? { "Content-Type": "application/json" } : {};
  
  // Merge default headers with custom headers
  const mergedHeaders = { ...defaultHeaders, ...headers };
  
  const res = await fetch(url, {
    method,
    headers: mergedHeaders,
    body: data ? JSON.stringify(data) : undefined,
    credentials: "include",
  });

  await throwIfResNotOk(res);
  return res;
}

type UnauthorizedBehavior = "returnNull" | "throw";
type QueryFnOptions = {
  on401: UnauthorizedBehavior;
  debug?: boolean;
};

export const getQueryFn: <T>(options: QueryFnOptions) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior, debug = false }) =>
  async ({ queryKey }) => {
    const endpoint = queryKey[0] as string;
    const params = queryKey[1];
    
    // ถ้ามี params ให้เพิ่มลงใน URL ด้วย URLSearchParams
    let url = endpoint;
    if (params && typeof params === 'object') {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.append(key, String(value));
        }
      });
      
      const queryString = searchParams.toString();
      if (queryString) {
        url = `${endpoint}?${queryString}`;
      }
    }
    
    if (debug) {
      console.log('Fetching with URL:', url);
    }
    
    try {
      const res = await fetch(url, {
        credentials: "include",
      });

      if (unauthorizedBehavior === "returnNull" && res.status === 401) {
        if (debug) {
          console.log(`401 Unauthorized for ${url}, returning null as configured`);
        }
        return null;
      }
      
      if (res.status === 403) {
        console.error(`Access forbidden (403) for ${url}`);
        throw new Error("ไม่มีสิทธิ์เข้าถึง กรุณาเข้าสู่ระบบด้วยบัญชีผู้ดูแลระบบ");
      }

      await throwIfResNotOk(res);
      
      // ตรวจสอบว่าเป็น HTML หรือไม่ (ซึ่งอาจจะเป็น error page)
      const contentType = res.headers.get('content-type');
      if (contentType && contentType.includes('text/html')) {
        throw new Error("ได้รับการตอบกลับที่ไม่ถูกต้อง โปรดลองเข้าสู่ระบบใหม่");
      }
      
      const data = await res.json();
      
      // Debug log for response data
      if (debug) {
        console.log(`Response for ${url}:`, data);
      }
      
      return data;
    } catch (error) {
      console.error(`Error fetching ${url}:`, error);
      throw error;
    }
  };

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      refetchInterval: false,
      refetchOnWindowFocus: false,
      staleTime: 60000, // ลดเวลาในการเก็บข้อมูลแคชลงจาก Infinity เพื่อให้มีการอัพเดทข้อมูลบ่อยขึ้น
      retry: 1, // ลองใหม่ 1 ครั้งเมื่อเกิดข้อผิดพลาด
    },
    mutations: {
      retry: false,
    },
  },
});
