import { Express, Request, Response } from "express";
import { db } from "../db";
import { users, User, verificationCodes } from "@shared/schema";
import { eq, and, gte } from "drizzle-orm";
import { verificationService } from "../verification-service";
import { socialAuthService } from "../social-auth-service";
import { emailService } from "../email-service";
import { hashPassword, comparePasswords } from "../auth";
import { z } from "zod";

/**
 * API สำหรับการสมัครสมาชิกและเข้าสู่ระบบ
 */
export function registerAuthAPI(app: Express) {
  /**
   * ตรวจสอบว่ามีอีเมลหรือ username นี้ในระบบแล้วหรือไม่
   */
  app.get("/api/auth/check-availability", async (req: Request, res: Response) => {
    try {
      const { email, username, phoneNumber } = req.query;
      
      if (!email && !username && !phoneNumber) {
        return res.status(400).json({
          success: false,
          message: "กรุณาระบุอีเมล, ชื่อผู้ใช้ หรือเบอร์โทรศัพท์"
        });
      }
      
      const result: { 
        email?: { available: boolean; message?: string };
        username?: { available: boolean; message?: string };
        phoneNumber?: { available: boolean; message?: string };
      } = {};
      
      // ตรวจสอบอีเมล
      if (email) {
        const [existingEmail] = await db.select().from(users).where(eq(users.email, email as string));
        result.email = {
          available: !existingEmail,
          message: existingEmail ? "อีเมลนี้ถูกใช้งานแล้ว" : undefined
        };
      }
      
      // ตรวจสอบชื่อผู้ใช้
      if (username) {
        const [existingUsername] = await db.select().from(users).where(eq(users.username, username as string));
        result.username = {
          available: !existingUsername,
          message: existingUsername ? "ชื่อผู้ใช้นี้ถูกใช้งานแล้ว" : undefined
        };
      }
      
      // ตรวจสอบเบอร์โทรศัพท์
      if (phoneNumber) {
        const [existingPhone] = await db.select().from(users).where(eq(users.phoneNumber, phoneNumber as string));
        result.phoneNumber = {
          available: !existingPhone,
          message: existingPhone ? "เบอร์โทรศัพท์นี้ถูกใช้งานแล้ว" : undefined
        };
      }
      
      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      console.error("Error checking availability:", error);
      res.status(500).json({
        success: false,
        message: "เกิดข้อผิดพลาดในการตรวจสอบข้อมูล"
      });
    }
  });

  /**
   * ส่งรหัสยืนยันไปยังอีเมล
   */
  app.post("/api/auth/send-email-verification", async (req: Request, res: Response) => {
    try {
      const { email, userId, type } = req.body;
      
      if (!email) {
        return res.status(400).json({
          success: false,
          message: "กรุณาระบุอีเมล"
        });
      }
      
      const result = await verificationService.sendEmailVerificationCode(
        email,
        userId,
        type || 'email'
      );
      
      if (result.success) {
        res.json({
          success: true,
          message: result.message
        });
      } else {
        res.status(400).json({
          success: false,
          message: result.message
        });
      }
    } catch (error) {
      console.error("Error sending email verification:", error);
      res.status(500).json({
        success: false,
        message: "เกิดข้อผิดพลาดในการส่งรหัสยืนยัน"
      });
    }
  });

  /**
   * ส่งรหัส OTP ไปยังเบอร์โทรศัพท์
   */
  app.post("/api/auth/send-otp", async (req: Request, res: Response) => {
    try {
      const { phoneNumber, userId } = req.body;
      
      if (!phoneNumber) {
        return res.status(400).json({
          success: false,
          message: "กรุณาระบุเบอร์โทรศัพท์"
        });
      }
      
      const result = await verificationService.sendPhoneOTP(phoneNumber, userId);
      
      if (result.success) {
        res.json({
          success: true,
          message: result.message
        });
      } else {
        res.status(400).json({
          success: false,
          message: result.message
        });
      }
    } catch (error) {
      console.error("Error sending OTP:", error);
      res.status(500).json({
        success: false,
        message: "เกิดข้อผิดพลาดในการส่งรหัส OTP"
      });
    }
  });

  /**
   * ตรวจสอบรหัสยืนยันอีเมลหรือ OTP
   */
  app.post("/api/auth/verify-code", async (req: Request, res: Response) => {
    try {
      const { type, identifier, code, userId } = req.body;
      
      if (!type || !identifier || !code) {
        return res.status(400).json({
          success: false,
          message: "กรุณาระบุประเภท, ตัวระบุ และรหัสยืนยัน"
        });
      }
      
      const result = await verificationService.verifyCode(
        type,
        identifier,
        code,
        userId
      );
      
      if (result.success) {
        // ถ้ามี userId และเป็นการยืนยันอีเมลหรือเบอร์โทร ให้อัพเดทสถานะการยืนยันในตาราง users
        if (userId) {
          if (type === 'email') {
            await db
              .update(users)
              .set({ email_verified: true })
              .where(eq(users.id, userId));
          } else if (type === 'phone') {
            await db
              .update(users)
              .set({ phone_verified: true })
              .where(eq(users.id, userId));
          }
        }
        
        res.json({
          success: true,
          message: result.message,
          verified: result.verified
        });
      } else {
        // เพิ่มจำนวนครั้งที่พยายามใช้รหัสยืนยัน
        await verificationService.incrementAttempt(type, identifier, code);
        
        res.status(400).json({
          success: false,
          message: result.message,
          verified: result.verified
        });
      }
    } catch (error) {
      console.error("Error verifying code:", error);
      res.status(500).json({
        success: false,
        message: "เกิดข้อผิดพลาดในการตรวจสอบรหัสยืนยัน",
        verified: false
      });
    }
  });

  /**
   * สมัครสมาชิกด้วยอีเมลและรหัสผ่าน
   */
  app.post("/api/auth/register-email", async (req: Request, res: Response) => {
    try {
      const registerSchema = z.object({
        username: z.string().min(3, "ชื่อผู้ใช้ต้องมีอย่างน้อย 3 ตัวอักษร"),
        email: z.string().email("อีเมลไม่ถูกต้อง"),
        password: z.string().min(8, "รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร"),
        firstName: z.string().optional(),
        lastName: z.string().optional(),
        companyName: z.string().optional(),
        phoneNumber: z.string().optional(),
        verificationCode: z.string().optional(),
      });
      
      const validationResult = registerSchema.safeParse(req.body);
      
      if (!validationResult.success) {
        return res.status(400).json({
          success: false,
          message: "ข้อมูลไม่ถูกต้อง",
          errors: validationResult.error.errors
        });
      }
      
      const { username, email, password, firstName, lastName, companyName, phoneNumber, verificationCode } = validationResult.data;
      
      // ตรวจสอบว่ามีอีเมลหรือชื่อผู้ใช้นี้ในระบบแล้วหรือไม่
      const [existingEmail] = await db.select().from(users).where(eq(users.email, email));
      const [existingUsername] = await db.select().from(users).where(eq(users.username, username));
      
      if (existingEmail) {
        return res.status(400).json({
          success: false,
          message: "อีเมลนี้ถูกใช้งานแล้ว"
        });
      }
      
      if (existingUsername) {
        return res.status(400).json({
          success: false,
          message: "ชื่อผู้ใช้นี้ถูกใช้งานแล้ว"
        });
      }
      
      // ตรวจสอบรหัสยืนยันอีเมล (ถ้ามี)
      let emailVerified = false;
      
      if (verificationCode) {
        const verificationResult = await verificationService.verifyCode(
          'email',
          email,
          verificationCode
        );
        
        emailVerified = verificationResult.verified || false;
      }
      
      // เข้ารหัสรหัสผ่าน
      const hashedPassword = await hashPassword(password);
      
      // สร้างผู้ใช้ใหม่
      const [newUser] = await db
        .insert(users)
        .values({
          username,
          email,
          password: hashedPassword,
          firstName,
          lastName,
          companyName,
          phoneNumber,
          email_verified: emailVerified,
          phone_verified: false,
          auth_providers: []
        } as any)
        .returning();
      
      // ถ้ายังไม่ได้ยืนยันอีเมล ให้ส่งรหัสยืนยันอีเมล
      if (!emailVerified) {
        await verificationService.sendEmailVerificationCode(email, newUser.id);
      }
      
      // ล็อกอินผู้ใช้ใหม่
      req.login(newUser, (err) => {
        if (err) {
          return res.status(500).json({
            success: false,
            message: "เกิดข้อผิดพลาดในการเข้าสู่ระบบ"
          });
        }
        
        res.json({
          success: true,
          message: "สมัครสมาชิกสำเร็จ",
          user: {
            id: newUser.id,
            username: newUser.username,
            email: newUser.email,
            firstName: newUser.firstName,
            lastName: newUser.lastName,
            companyName: newUser.companyName,
            phoneNumber: newUser.phoneNumber,
            email_verified: newUser.email_verified,
            phone_verified: newUser.phone_verified
          }
        });
      });
    } catch (error) {
      console.error("Error registering user:", error);
      res.status(500).json({
        success: false,
        message: "เกิดข้อผิดพลาดในการสมัครสมาชิก"
      });
    }
  });

  /**
   * สมัครสมาชิกด้วยเบอร์โทรศัพท์และ OTP
   */
  app.post("/api/auth/register-phone", async (req: Request, res: Response) => {
    try {
      const registerSchema = z.object({
        username: z.string().min(3, "ชื่อผู้ใช้ต้องมีอย่างน้อย 3 ตัวอักษร"),
        phoneNumber: z.string().min(10, "เบอร์โทรศัพท์ไม่ถูกต้อง"),
        otp: z.string().length(6, "รหัส OTP ต้องมี 6 หลัก"),
        email: z.string().email("อีเมลไม่ถูกต้อง").optional(),
        password: z.string().min(8, "รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร"),
        firstName: z.string().optional(),
        lastName: z.string().optional(),
        companyName: z.string().optional()
      });
      
      const validationResult = registerSchema.safeParse(req.body);
      
      if (!validationResult.success) {
        return res.status(400).json({
          success: false,
          message: "ข้อมูลไม่ถูกต้อง",
          errors: validationResult.error.errors
        });
      }
      
      const { username, phoneNumber, otp, email, password, firstName, lastName, companyName } = validationResult.data;
      
      // ตรวจสอบว่ามีเบอร์โทรศัพท์หรือชื่อผู้ใช้นี้ในระบบแล้วหรือไม่
      const [existingPhone] = await db.select().from(users).where(eq(users.phoneNumber, phoneNumber));
      const [existingUsername] = await db.select().from(users).where(eq(users.username, username));
      const [existingEmail] = email ? await db.select().from(users).where(eq(users.email, email)) : [null];
      
      if (existingPhone) {
        return res.status(400).json({
          success: false,
          message: "เบอร์โทรศัพท์นี้ถูกใช้งานแล้ว"
        });
      }
      
      if (existingUsername) {
        return res.status(400).json({
          success: false,
          message: "ชื่อผู้ใช้นี้ถูกใช้งานแล้ว"
        });
      }
      
      if (existingEmail) {
        return res.status(400).json({
          success: false,
          message: "อีเมลนี้ถูกใช้งานแล้ว"
        });
      }
      
      // ตรวจสอบรหัส OTP
      const verificationResult = await verificationService.verifyCode(
        'phone',
        phoneNumber,
        otp
      );
      
      if (!verificationResult.verified) {
        return res.status(400).json({
          success: false,
          message: "รหัส OTP ไม่ถูกต้องหรือหมดอายุ"
        });
      }
      
      // เข้ารหัสรหัสผ่าน
      const hashedPassword = await hashPassword(password);
      
      // สร้างผู้ใช้ใหม่
      const [newUser] = await db
        .insert(users)
        .values({
          username,
          email: email || `${username}@noemail.phone`,
          password: hashedPassword,
          phoneNumber,
          firstName,
          lastName,
          companyName,
          email_verified: false,
          phone_verified: true,
          auth_providers: ['phone']
        } as any)
        .returning();
      
      // สร้างการเชื่อมโยงกับบัญชีเบอร์โทรศัพท์
      await socialAuthService.linkExternalAccount(newUser.id, {
        provider: 'phone',
        externalId: phoneNumber,
        displayName: firstName && lastName ? `${firstName} ${lastName}` : username,
        metadata: { registeredAt: new Date() }
      });
      
      // ล็อกอินผู้ใช้ใหม่
      req.login(newUser, (err) => {
        if (err) {
          return res.status(500).json({
            success: false,
            message: "เกิดข้อผิดพลาดในการเข้าสู่ระบบ"
          });
        }
        
        res.json({
          success: true,
          message: "สมัครสมาชิกสำเร็จ",
          user: {
            id: newUser.id,
            username: newUser.username,
            email: newUser.email,
            firstName: newUser.firstName,
            lastName: newUser.lastName,
            phoneNumber: newUser.phoneNumber,
            email_verified: newUser.email_verified,
            phone_verified: newUser.phone_verified
          }
        });
      });
    } catch (error) {
      console.error("Error registering user with phone:", error);
      res.status(500).json({
        success: false,
        message: "เกิดข้อผิดพลาดในการสมัครสมาชิก"
      });
    }
  });

  /**
   * เข้าสู่ระบบด้วยชื่อผู้ใช้หรืออีเมลและรหัสผ่าน
   */
  app.post("/api/auth/login-email", async (req: Request, res: Response) => {
    try {
      const { username, email, password } = req.body;
      
      if ((!username && !email) || !password) {
        return res.status(400).json({
          success: false,
          message: "กรุณาระบุชื่อผู้ใช้หรืออีเมล และรหัสผ่าน"
        });
      }
      
      // ค้นหาผู้ใช้จากชื่อผู้ใช้หรืออีเมล
      let user: User | undefined;
      
      if (username) {
        [user] = await db.select().from(users).where(eq(users.username, username));
      } else if (email) {
        [user] = await db.select().from(users).where(eq(users.email, email));
      }
      
      if (!user) {
        return res.status(401).json({
          success: false,
          message: "ชื่อผู้ใช้หรืออีเมลไม่ถูกต้อง"
        });
      }
      
      // ตรวจสอบรหัสผ่าน
      const passwordValid = await comparePasswords(password, user.password);
      
      if (!passwordValid) {
        return res.status(401).json({
          success: false,
          message: "รหัสผ่านไม่ถูกต้อง"
        });
      }
      
      // ล็อกอินผู้ใช้
      req.login(user, (err) => {
        if (err) {
          return res.status(500).json({
            success: false,
            message: "เกิดข้อผิดพลาดในการเข้าสู่ระบบ"
          });
        }
        
        res.json({
          success: true,
          message: "เข้าสู่ระบบสำเร็จ",
          user: {
            id: user!.id,
            username: user!.username,
            email: user!.email,
            firstName: user!.firstName,
            lastName: user!.lastName,
            phoneNumber: user!.phoneNumber,
            email_verified: user!.email_verified,
            phone_verified: user!.phone_verified
          }
        });
      });
    } catch (error) {
      console.error("Error logging in:", error);
      res.status(500).json({
        success: false,
        message: "เกิดข้อผิดพลาดในการเข้าสู่ระบบ"
      });
    }
  });

  /**
   * เข้าสู่ระบบด้วยเบอร์โทรศัพท์และ OTP
   */
  app.post("/api/auth/login-phone", async (req: Request, res: Response) => {
    try {
      const { phoneNumber, otp } = req.body;
      
      if (!phoneNumber || !otp) {
        return res.status(400).json({
          success: false,
          message: "กรุณาระบุเบอร์โทรศัพท์และรหัส OTP"
        });
      }
      
      // ตรวจสอบรหัส OTP
      const verificationResult = await verificationService.verifyCode(
        'phone',
        phoneNumber,
        otp
      );
      
      if (!verificationResult.verified) {
        return res.status(401).json({
          success: false,
          message: "รหัส OTP ไม่ถูกต้องหรือหมดอายุ"
        });
      }
      
      // ค้นหาผู้ใช้จากเบอร์โทรศัพท์
      const [user] = await db.select().from(users).where(eq(users.phoneNumber, phoneNumber));
      
      if (!user) {
        return res.status(401).json({
          success: false,
          message: "ไม่พบบัญชีผู้ใช้ที่เชื่อมโยงกับเบอร์โทรศัพท์นี้"
        });
      }
      
      // อัพเดทสถานะการยืนยันเบอร์โทรศัพท์ (ถ้ายังไม่ได้ยืนยัน)
      if (!user.phone_verified) {
        await db
          .update(users)
          .set({ phone_verified: true })
          .where(eq(users.id, user.id));
        
        // อัพเดท user object
        user.phone_verified = true;
      }
      
      // ล็อกอินผู้ใช้
      req.login(user, (err) => {
        if (err) {
          return res.status(500).json({
            success: false,
            message: "เกิดข้อผิดพลาดในการเข้าสู่ระบบ"
          });
        }
        
        res.json({
          success: true,
          message: "เข้าสู่ระบบสำเร็จ",
          user: {
            id: user.id,
            username: user.username,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            phoneNumber: user.phoneNumber,
            email_verified: user.email_verified,
            phone_verified: user.phone_verified
          }
        });
      });
    } catch (error) {
      console.error("Error logging in with phone:", error);
      res.status(500).json({
        success: false,
        message: "เกิดข้อผิดพลาดในการเข้าสู่ระบบ"
      });
    }
  });

  /**
   * เข้าสู่ระบบหรือสมัครสมาชิกด้วยบัญชีโซเชียลมีเดีย
   */
  app.post("/api/auth/social-login", async (req: Request, res: Response) => {
    try {
      const { provider, socialId, profile } = req.body;
      
      if (!provider || !socialId) {
        return res.status(400).json({
          success: false,
          message: "กรุณาระบุผู้ให้บริการและ ID"
        });
      }
      
      // ตรวจสอบว่า provider ถูกต้องหรือไม่
      const validProviders = ['line', 'facebook', 'google', 'apple'];
      if (!validProviders.includes(provider)) {
        return res.status(400).json({
          success: false,
          message: "ผู้ให้บริการไม่ถูกต้อง"
        });
      }
      
      // เข้าสู่ระบบหรือสมัครสมาชิกด้วยบัญชีโซเชียลมีเดีย
      const result = await socialAuthService.loginWithSocialAccount(
        provider as any,
        socialId,
        profile || {}
      );
      
      // ล็อกอินผู้ใช้
      req.login(result.user, (err) => {
        if (err) {
          return res.status(500).json({
            success: false,
            message: "เกิดข้อผิดพลาดในการเข้าสู่ระบบ"
          });
        }
        
        res.json({
          success: true,
          message: result.isNewUser ? "สมัครสมาชิกสำเร็จ" : "เข้าสู่ระบบสำเร็จ",
          isNewUser: result.isNewUser,
          user: {
            id: result.user.id,
            username: result.user.username,
            email: result.user.email,
            firstName: result.user.firstName,
            lastName: result.user.lastName,
            phoneNumber: result.user.phoneNumber,
            email_verified: result.user.email_verified,
            phone_verified: result.user.phone_verified
          }
        });
      });
    } catch (error) {
      console.error("Error with social login:", error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : "เกิดข้อผิดพลาดในการเข้าสู่ระบบด้วยบัญชีโซเชียลมีเดีย"
      });
    }
  });

  /**
   * ส่งอีเมลสำหรับรีเซ็ตรหัสผ่าน
   */
  app.post("/api/auth/forgot-password", async (req: Request, res: Response) => {
    try {
      const { email } = req.body;
      
      if (!email) {
        return res.status(400).json({
          success: false,
          message: "กรุณาระบุอีเมล"
        });
      }
      
      // ค้นหาผู้ใช้จากอีเมล
      const [user] = await db.select().from(users).where(eq(users.email, email));
      
      if (!user) {
        // เพื่อความปลอดภัย ไม่ควรบอกว่าอีเมลนี้ไม่มีในระบบ
        return res.json({
          success: true,
          message: "หากมีบัญชีกับอีเมลนี้ในระบบ เราจะส่งลิงก์รีเซ็ตรหัสผ่านไปยังอีเมลของคุณ"
        });
      }
      
      // ส่งรหัสยืนยันสำหรับรีเซ็ตรหัสผ่าน
      const result = await verificationService.sendEmailVerificationCode(
        email,
        user.id,
        'password_reset'
      );
      
      if (result.success) {
        res.json({
          success: true,
          message: "เราได้ส่งรหัสยืนยันสำหรับรีเซ็ตรหัสผ่านไปยังอีเมลของคุณแล้ว"
        });
      } else {
        res.status(500).json({
          success: false,
          message: "เกิดข้อผิดพลาดในการส่งรหัสยืนยัน"
        });
      }
    } catch (error) {
      console.error("Error with forgot password:", error);
      res.status(500).json({
        success: false,
        message: "เกิดข้อผิดพลาดในการรีเซ็ตรหัสผ่าน"
      });
    }
  });

  /**
   * ตรวจสอบรหัสยืนยันสำหรับรีเซ็ตรหัสผ่าน
   */
  app.post("/api/auth/verify-reset-code", async (req: Request, res: Response) => {
    try {
      const { identifier, code, type } = req.body;
      
      if (!identifier || !code) {
        return res.status(400).json({
          success: false,
          message: "กรุณาระบุข้อมูลให้ครบถ้วน"
        });
      }
      
      // ค้นหาผู้ใช้จากตัวระบุ (identifier)
      const [user] = await db.select().from(users).where(eq(users.email, identifier));
      
      if (!user) {
        return res.status(404).json({
          success: false,
          message: "ไม่พบบัญชีผู้ใช้นี้ในระบบ"
        });
      }
      
      // ค้นหารหัสยืนยันที่ยังไม่หมดอายุและยังไม่ถูกใช้งานสำหรับการรีเซ็ตรหัสผ่าน
      const [verificationCode] = await db.select()
        .from(verificationCodes)
        .where(
          and(
            eq(verificationCodes.userId, user.id),
            eq(verificationCodes.code, code),
            eq(verificationCodes.type, 'password_reset'),
            eq(verificationCodes.isUsed, false),
            gte(verificationCodes.expiresAt, new Date())
          )
        );
      
      if (!verificationCode) {
        return res.status(400).json({
          success: false,
          message: "รหัสยืนยันไม่ถูกต้องหรือหมดอายุแล้ว"
        });
      }
      
      res.json({
        success: true,
        message: "รหัสยืนยันถูกต้อง"
      });
    } catch (error) {
      console.error("Error verifying reset code:", error);
      res.status(500).json({
        success: false,
        message: "เกิดข้อผิดพลาดในการตรวจสอบรหัสยืนยัน"
      });
    }
  });
  
  /**
   * รีเซ็ตรหัสผ่านด้วยรหัสยืนยัน
   */
  app.post("/api/auth/reset-password", async (req: Request, res: Response) => {
    try {
      const { email, code, newPassword } = req.body;
      
      if (!email || !code || !newPassword) {
        return res.status(400).json({
          success: false,
          message: "กรุณาระบุอีเมล รหัสยืนยัน และรหัสผ่านใหม่"
        });
      }
      
      // ตรวจสอบรหัสยืนยัน
      const verificationResult = await verificationService.verifyCode(
        'password_reset',
        email,
        code
      );
      
      if (!verificationResult.verified) {
        return res.status(401).json({
          success: false,
          message: "รหัสยืนยันไม่ถูกต้องหรือหมดอายุ"
        });
      }
      
      // ค้นหาผู้ใช้จากอีเมล
      const [user] = await db.select().from(users).where(eq(users.email, email));
      
      if (!user) {
        return res.status(404).json({
          success: false,
          message: "ไม่พบบัญชีผู้ใช้ที่เชื่อมโยงกับอีเมลนี้"
        });
      }
      
      // เข้ารหัสรหัสผ่านใหม่
      const hashedPassword = await hashPassword(newPassword);
      
      // อัพเดทรหัสผ่าน
      await db
        .update(users)
        .set({ password: hashedPassword })
        .where(eq(users.id, user.id));
      
      res.json({
        success: true,
        message: "รีเซ็ตรหัสผ่านสำเร็จ กรุณาเข้าสู่ระบบด้วยรหัสผ่านใหม่"
      });
    } catch (error) {
      console.error("Error resetting password:", error);
      res.status(500).json({
        success: false,
        message: "เกิดข้อผิดพลาดในการรีเซ็ตรหัสผ่าน"
      });
    }
  });

  /**
   * เชื่อมโยงบัญชีโซเชียลมีเดียกับบัญชีปัจจุบัน
   */
  app.post("/api/auth/link-social", async (req: Request, res: Response) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({
          success: false,
          message: "กรุณาเข้าสู่ระบบ"
        });
      }
      
      const userId = req.user?.id;
      const { provider, socialId, profile } = req.body;
      
      if (!provider || !socialId) {
        return res.status(400).json({
          success: false,
          message: "กรุณาระบุผู้ให้บริการและ ID"
        });
      }
      
      // ตรวจสอบว่า provider ถูกต้องหรือไม่
      const validProviders = ['line', 'facebook', 'google', 'apple', 'phone'];
      if (!validProviders.includes(provider)) {
        return res.status(400).json({
          success: false,
          message: "ผู้ให้บริการไม่ถูกต้อง"
        });
      }
      
      // เชื่อมโยงบัญชีโซเชียลมีเดีย
      await socialAuthService.linkExternalAccount(userId, {
        provider: provider as any,
        externalId: socialId,
        displayName: profile?.displayName,
        email: profile?.email,
        profileImage: profile?.profileImage,
        accessToken: profile?.accessToken,
        refreshToken: profile?.refreshToken,
        tokenExpiry: profile?.tokenExpiry,
        metadata: profile?.metadata || { linkedAt: new Date() }
      });
      
      // ถ้าเป็นการเชื่อมโยงกับ Google หรือ Facebook และยังไม่ได้ยืนยันอีเมล ให้อัพเดทสถานะการยืนยันอีเมล
      if ((provider === 'google' || provider === 'facebook') && profile?.email && !req.user.email_verified) {
        const [user] = await db.select().from(users).where(eq(users.id, userId));
        
        if (user && user.email === profile.email) {
          await db
            .update(users)
            .set({ email_verified: true })
            .where(eq(users.id, userId));
        }
      }
      
      res.json({
        success: true,
        message: `เชื่อมโยงบัญชี ${provider} สำเร็จ`
      });
    } catch (error) {
      console.error("Error linking social account:", error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : "เกิดข้อผิดพลาดในการเชื่อมโยงบัญชีโซเชียลมีเดีย"
      });
    }
  });

  /**
   * ยกเลิกการเชื่อมโยงบัญชีโซเชียลมีเดีย
   */
  app.post("/api/auth/unlink-social", async (req: Request, res: Response) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({
          success: false,
          message: "กรุณาเข้าสู่ระบบ"
        });
      }
      
      const userId = req.user?.id;
      const { provider } = req.body;
      
      if (!provider) {
        return res.status(400).json({
          success: false,
          message: "กรุณาระบุผู้ให้บริการ"
        });
      }
      
      // ตรวจสอบว่า provider ถูกต้องหรือไม่
      const validProviders = ['line', 'facebook', 'google', 'apple', 'phone'];
      if (!validProviders.includes(provider)) {
        return res.status(400).json({
          success: false,
          message: "ผู้ให้บริการไม่ถูกต้อง"
        });
      }
      
      // ยกเลิกการเชื่อมโยงบัญชีโซเชียลมีเดีย
      const result = await socialAuthService.unlinkExternalAccount(userId, provider as any);
      
      if (result) {
        res.json({
          success: true,
          message: `ยกเลิกการเชื่อมโยงบัญชี ${provider} สำเร็จ`
        });
      } else {
        res.status(400).json({
          success: false,
          message: `ไม่พบการเชื่อมโยงบัญชี ${provider}`
        });
      }
    } catch (error) {
      console.error("Error unlinking social account:", error);
      res.status(500).json({
        success: false,
        message: "เกิดข้อผิดพลาดในการยกเลิกการเชื่อมโยงบัญชีโซเชียลมีเดีย"
      });
    }
  });

  /**
   * ดึงข้อมูลการเชื่อมโยงบัญชีโซเชียลมีเดียทั้งหมดของผู้ใช้
   */
  app.get("/api/auth/social-connections", async (req: Request, res: Response) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({
          success: false,
          message: "กรุณาเข้าสู่ระบบ"
        });
      }
      
      const userId = req.user?.id;
      
      // ดึงข้อมูลการเชื่อมโยงบัญชีโซเชียลมีเดียทั้งหมด
      const connections = await db
        .select({
          id: externalAuth.id,
          provider: externalAuth.provider,
          externalId: externalAuth.externalId,
          displayName: externalAuth.displayName,
          email: externalAuth.email,
          profileImage: externalAuth.profileImage,
          lastLogin: externalAuth.lastLogin,
          createdAt: externalAuth.createdAt
        })
        .from(externalAuth)
        .where(eq(externalAuth.userId, userId));
      
      res.json({
        success: true,
        connections
      });
    } catch (error) {
      console.error("Error fetching social connections:", error);
      res.status(500).json({
        success: false,
        message: "เกิดข้อผิดพลาดในการดึงข้อมูลการเชื่อมโยงบัญชีโซเชียลมีเดีย"
      });
    }
  });
}