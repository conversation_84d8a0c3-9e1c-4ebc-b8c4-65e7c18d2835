import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { CircleCheck, Circle, CircleX } from "lucide-react";
import { formatDate } from "@/lib/utils";

// ประกาศ Type ของข้อมูลผลลัพธ์การตรวจสอบสลิป
interface SlipResultProps {
  result: {
    status: number;
    data?: {
      payload: string;
      transRef: string;
      date: string;
      countryCode: string;
      amount: {
        amount: number;
        local: {
          amount?: number;
          currency?: string;
        }
      };
      fee?: number;
      ref1?: string;
      ref2?: string;
      ref3?: string;
      sender: {
        bank: {
          id: string;
          name?: string;
          short?: string;
        };
        account: {
          name: {
            th?: string;
            en?: string;
          };
          bank?: {
            type: 'BANKAC' | 'TOKEN' | 'DUMMY';
            account: string;
          };
          proxy?: {
            type: 'NATID' | 'MSISDN' | 'EWALLETID' | 'EMAIL' | 'BILLERID';
            account: string;
          };
        };
      };
      receiver: {
        bank: {
          id: string;
          name?: string;
          short?: string;
        };
        account: {
          name: {
            th?: string;
            en?: string;
          };
          bank?: {
            type: 'BANKAC' | 'TOKEN' | 'DUMMY';
            account: string;
          };
          proxy?: {
            type: 'NATID' | 'MSISDN' | 'EWALLETID' | 'EMAIL' | 'BILLERID';
            account: string;
          };
        };
        merchantId?: string;
      };
    };
    message?: string;
  };
}

// ฟังก์ชันแสดงผลการตรวจสอบสลิป
export function SlipResult({ result }: SlipResultProps) {
  // ตรวจสอบว่ามีข้อมูลหรือไม่
  if (!result || (result.status !== 200 && !result.message)) {
    return null;
  }
  
  // กรณีมีข้อผิดพลาด แสดงข้อความแจ้งเตือน
  if (result.status !== 200) {
    return (
      <Card className="border-red-300 bg-red-50">
        <CardHeader>
          <CardTitle className="flex items-center text-red-700">
            <CircleX className="mr-2 h-5 w-5" />
            การตรวจสอบล้มเหลว
          </CardTitle>
          <CardDescription className="text-red-600">
            รหัสข้อผิดพลาด: {result.status}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-red-600">{result.message}</p>
          
          {result.message === 'slip_not_found' && (
            <div className="mt-4 text-sm text-red-600">
              <p>สาเหตุที่เป็นไปได้:</p>
              <ul className="list-disc pl-5 mt-1">
                <li>ไม่พบข้อมูลสลิปในระบบ</li>
                <li>รูปภาพสลิปอาจไม่ชัดเจน</li>
                <li>สลิปอาจเป็นของปลอมหรือรูปแบบไม่ถูกต้อง</li>
              </ul>
              <p className="mt-2">โปรดตรวจสอบรูปภาพสลิปและลองใหม่อีกครั้ง</p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }
  
  // กรณีตรวจสอบสำเร็จ แสดงข้อมูลสลิป
  const { data } = result;
  
  if (!data) {
    return null;
  }
  
  return (
    <Card className="border-green-300 bg-white">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="flex items-center">
              <CircleCheck className="mr-2 h-5 w-5 text-green-600" />
              ตรวจสอบสำเร็จ
            </CardTitle>
            <CardDescription>
              เลขอ้างอิง: {data.transRef}
            </CardDescription>
          </div>
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            ยืนยันแล้ว
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <h3 className="text-sm font-medium text-gray-500 mb-1">ธนาคารผู้ส่ง</h3>
            <p className="font-semibold">{data.sender.bank.name || `ธนาคารรหัส ${data.sender.bank.id}`}</p>
          </div>
          
          <div className="flex justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1">ผู้ส่ง</h3>
              <p className="font-semibold">{data.sender.account.name.th || data.sender.account.name.en || "ไม่ระบุ"}</p>
              {data.sender.account.bank && (
                <p className="text-sm text-gray-600">{data.sender.account.bank.account}</p>
              )}
            </div>
            
            <div className="text-right">
              <h3 className="text-sm font-medium text-gray-500 mb-1">ผู้รับ</h3>
              <p className="font-semibold">{data.receiver.account.name.th || data.receiver.account.name.en || "ไม่ระบุ"}</p>
              {data.receiver.account.bank && (
                <p className="text-sm text-gray-600">{data.receiver.account.bank.account}</p>
              )}
            </div>
          </div>
          
          <Separator />
          
          <div className="flex justify-between items-center">
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1">วันที่และเวลา</h3>
              <p className="font-semibold">{formatDate(new Date(data.date))}</p>
            </div>
            
            <div className="text-right">
              <h3 className="text-sm font-medium text-gray-500 mb-1">จำนวนเงิน</h3>
              <p className="text-xl font-bold text-primary-700">
                {data.amount.amount.toLocaleString('th-TH')} บาท
              </p>
            </div>
          </div>
          
          {(data.ref1 || data.ref2 || data.ref3) && (
            <>
              <Separator />
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-1">ข้อมูลอ้างอิง</h3>
                {data.ref1 && <p className="text-sm mb-1">Ref1: {data.ref1}</p>}
                {data.ref2 && <p className="text-sm mb-1">Ref2: {data.ref2}</p>}
                {data.ref3 && <p className="text-sm mb-1">Ref3: {data.ref3}</p>}
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
