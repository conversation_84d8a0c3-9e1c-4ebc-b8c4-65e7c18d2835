import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { useQuery } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import {
  Activity, BarChart4, Clock, PieChart, MapPin,
  AlertTriangle, CheckCircle, XCircle, Calendar,
  Zap, Globe, RefreshCw, Code, Database,
  Search, HelpCircle, FileQuestion, Cpu,
  Users, Building, DollarSign, Shield, FileCheck
} from "lucide-react";
import { Admin } from "@/components/layouts/admin-layout";
import { AreaChart, Area, BarChart, Bar, <PERSON><PERSON> as RechartsPie, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import worldMapData from '@/lib/world-map-data';
// @ts-ignore - เราได้ติดตั้ง package แล้วแต่ยังไม่มี type definitions
import { ComposableMap, Geographies, Geography, Marker } from "react-simple-maps";
import { useAuth } from "@/hooks/use-auth";

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#AF19FF', '#FF193F'];
const BANK_COLORS = ['#138f2d', '#4e2e7f', '#1e4598', '#1ba5e1', '#4b2777', '#fec43b', '#e51937', '#00a950', '#f58220', '#672d93'];

// ฟังก์ชันสำหรับแปลงรหัสธนาคารเป็นชื่อ
function getBankName(bankCode: string): string {
  const bankMap: Record<string, string> = {
    '002': 'ธนาคารกรุงเทพ',
    '004': 'ธนาคารกสิกรไทย',
    '006': 'ธนาคารกรุงไทย',
    '011': 'ธนาคารทหารไทยธนชาต',
    '014': 'ธนาคารไทยพาณิชย์',
    '025': 'ธนาคารกรุงศรีอยุธยา',
    '030': 'ธนาคารออมสิน',
    '034': 'ธนาคารเกียรตินาคิน',
    '069': 'ธนาคารเพื่อการเกษตรและสหกรณ์',
    '073': 'ธนาคารแลนด์แอนด์เฮ้าส์',
  };

  return bankMap[bankCode] || `ธนาคารรหัส ${bankCode}`;
}

export default function AdminAnalyticsPage() {
  const { toast } = useToast();
  const { user } = useAuth();
  const [timeRange, setTimeRange] = useState('today');
  const [apiKey, setApiKey] = useState('all');
  const [userId, setUserId] = useState('all');
  const [refreshInterval, setRefreshInterval] = useState(0);
  const [activeTab, setActiveTab] = useState('realtime');

  // Fetch API usage statistics for admin (all users)
  const { data: apiStats, isLoading: isLoadingStats, refetch: refetchStats } = useQuery({
    queryKey: ['/api/analytics/stats', timeRange, apiKey, userId],
    queryFn: async () => {
      try {
        const res = await apiRequest('GET', `/api/analytics/stats?timeRange=${timeRange}&apiKey=${apiKey}&userId=${userId}`);
        return await res.json();
      } catch (error) {
        console.error('Error fetching API stats:', error);
        return null;
      }
    },
    enabled: true,
    refetchInterval: refreshInterval > 0 ? refreshInterval * 1000 : false,
  });

  // Fetch users for filtering
  const { data: users } = useQuery({
    queryKey: ['/api/admin/users/list'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/admin/users/list');
      const data = await res.json();
      return data || [];
    },
  });

  // Fetch API keys for all users (admin only)
  const { data: apiKeys } = useQuery({
    queryKey: ['/api/admin/api-keys/all'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/admin/api-keys/all');
      const data = await res.json();
      return data || [];
    },
  });

  // Query to get overall system statistics
  const { data: systemStats } = useQuery({
    queryKey: ['/api/admin/system-stats'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/admin/system-stats');
      const data = await res.json();
      return data || {
        totalUsers: 0,
        totalApiKeys: 0,
        totalVerifications: 0,
        totalRevenue: 0,
        averageResponseTime: 0
      };
    },
  });

  // Function to manually refresh data
  const handleRefresh = () => {
    refetchStats();
    toast({
      title: 'กำลังรีเฟรชข้อมูล',
      description: 'ข้อมูลสถิติจะถูกอัพเดทในไม่กี่วินาที',
    });
  };

  // Start/stop auto refresh
  const toggleAutoRefresh = () => {
    if (refreshInterval > 0) {
      setRefreshInterval(0);
      toast({
        title: 'ปิดการรีเฟรชอัตโนมัติ',
        description: 'ข้อมูลจะไม่ถูกรีเฟรชโดยอัตโนมัติ',
      });
    } else {
      setRefreshInterval(30);
      toast({
        title: 'เปิดการรีเฟรชอัตโนมัติ',
        description: 'ข้อมูลจะถูกรีเฟรชทุก 30 วินาที',
      });
    }
  };

  // Function to format date/time
  const formatTime = (time: string) => {
    return time;
  };

  return (
    <Admin>
      <div className="container py-6 space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-white">สถิติการใช้งาน API ทั้งระบบ</h1>
            <p className="text-lg text-slate-400">
              มุมมองแอดมิน - ดูสถิติการใช้งาน API แบบเรียลไทม์ของทั้งระบบ
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              className="border-indigo-700 text-indigo-300 hover:bg-indigo-900"
              onClick={handleRefresh}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              รีเฟรช
            </Button>
            <Button
              variant={refreshInterval > 0 ? "default" : "outline"}
              className={refreshInterval > 0
                ? "bg-indigo-700 hover:bg-indigo-600"
                : "border-indigo-700 text-indigo-300 hover:bg-indigo-900"}
              onClick={toggleAutoRefresh}
            >
              <Clock className="h-4 w-4 mr-2" />
              {refreshInterval > 0 ? `รีเฟรชอัตโนมัติ (${refreshInterval}s)` : 'เปิดรีเฟรชอัตโนมัติ'}
            </Button>
          </div>
        </div>

        {/* System Overview Cards - Only for Admin */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="bg-gradient-to-br from-indigo-900 to-indigo-950 border-indigo-800/40">
            <CardHeader className="pb-2">
              <CardTitle className="text-white flex items-center">
                <Users className="h-5 w-5 mr-2 text-amber-300" />
                ผู้ใช้งานทั้งหมด
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-baseline space-x-2">
                <span className="text-3xl font-bold text-white">{systemStats?.totalUsers || 0}</span>
                <span className="text-sm text-green-400">คน</span>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-indigo-900 to-indigo-950 border-indigo-800/40">
            <CardHeader className="pb-2">
              <CardTitle className="text-white flex items-center">
                <Shield className="h-5 w-5 mr-2 text-amber-300" />
                API Keys ทั้งหมด
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-baseline space-x-2">
                <span className="text-3xl font-bold text-white">{systemStats?.totalApiKeys || 0}</span>
                <span className="text-sm text-green-400">คีย์</span>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-indigo-900 to-indigo-950 border-indigo-800/40">
            <CardHeader className="pb-2">
              <CardTitle className="text-white flex items-center">
                <FileCheck className="h-5 w-5 mr-2 text-amber-300" />
                การตรวจสอบทั้งหมด
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-baseline space-x-2">
                <span className="text-3xl font-bold text-white">{systemStats?.totalVerifications || 0}</span>
                <span className="text-sm text-green-400">ครั้ง</span>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-indigo-900 to-indigo-950 border-indigo-800/40">
            <CardHeader className="pb-2">
              <CardTitle className="text-white flex items-center">
                <DollarSign className="h-5 w-5 mr-2 text-amber-300" />
                รายได้รวม
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-baseline space-x-2">
                <span className="text-3xl font-bold text-white">฿{systemStats?.totalRevenue?.toLocaleString() || 0}</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Regular API Analytics Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="bg-indigo-950/40 border-indigo-800/40">
            <CardHeader className="pb-2">
              <CardTitle className="text-white">ข้อมูลการเรียก API</CardTitle>
              <CardDescription className="text-slate-400">จำนวนการเรียกใช้ API ทั้งหมด</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-baseline space-x-2">
                <span className="text-3xl font-bold text-white">{apiStats?.data?.summary?.totalRequests || 0}</span>
                <span className="text-sm text-green-400">ครั้ง</span>
              </div>
              <div className="h-[80px] mt-4">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={apiStats?.data?.hourlyStats?.slice(-10) || []} margin={{ top: 0, right: 0, left: 0, bottom: 0 }}>
                    <defs>
                      <linearGradient id="colorRequests" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#8884d8" stopOpacity={0}/>
                      </linearGradient>
                    </defs>
                    <Area type="monotone" dataKey="requests" stroke="#8884d8" fillOpacity={1} fill="url(#colorRequests)" />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-indigo-950/40 border-indigo-800/40">
            <CardHeader className="pb-2">
              <CardTitle className="text-white">อัตราความสำเร็จ</CardTitle>
              <CardDescription className="text-slate-400">อัตราความสำเร็จในการตรวจสอบ</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-baseline space-x-2">
                <span className="text-3xl font-bold text-white">
                  {apiStats?.data?.summary ?
                    Math.round(apiStats.data.summary.successCount / Math.max(apiStats.data.summary.totalRequests, 1) * 100) + '%'
                    : '0%'
                  }
                </span>
                <span className="text-sm text-green-400">ความสำเร็จ</span>
              </div>
              <div className="h-[80px] mt-4">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={apiStats?.data?.hourlyStats?.slice(-10) || []} margin={{ top: 0, right: 0, left: 0, bottom: 0 }}>
                    <defs>
                      <linearGradient id="colorSuccess" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#4ade80" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#4ade80" stopOpacity={0}/>
                      </linearGradient>
                    </defs>
                    <Area type="monotone" dataKey="success" stroke="#4ade80" fillOpacity={1} fill="url(#colorSuccess)" />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-indigo-950/40 border-indigo-800/40">
            <CardHeader className="pb-2">
              <CardTitle className="text-white">จำนวนข้อผิดพลาด</CardTitle>
              <CardDescription className="text-slate-400">จำนวนการเรียก API ที่ล้มเหลว</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-baseline space-x-2">
                <span className="text-3xl font-bold text-white">
                  {apiStats?.data?.summary?.errorCount || 0}
                </span>
                <span className="text-sm text-red-400">ครั้ง</span>
              </div>
              <div className="h-[80px] mt-4">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={apiStats?.data?.hourlyStats?.slice(-10) || []} margin={{ top: 0, right: 0, left: 0, bottom: 0 }}>
                    <defs>
                      <linearGradient id="colorFailed" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#f87171" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#f87171" stopOpacity={0}/>
                      </linearGradient>
                    </defs>
                    <Area type="monotone" dataKey="failed" stroke="#f87171" fillOpacity={1} fill="url(#colorFailed)" />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Admin filters with user selector */}
        <div className="flex flex-wrap gap-4 items-center">
          <div className="flex space-x-2 items-center">
            <span className="text-white">ผู้ใช้:</span>
            <Select value={userId} onValueChange={setUserId}>
              <SelectTrigger className="w-[180px] bg-indigo-950/40 border-indigo-800/40">
                <SelectValue placeholder="เลือกผู้ใช้" />
              </SelectTrigger>
              <SelectContent className="bg-indigo-950 border-indigo-800 text-white">
                <SelectItem value="all">ทุกคน</SelectItem>
                {users?.map((user: any) => (
                  <SelectItem key={user.id} value={user.id.toString()}>
                    {user.username || `User #${user.id}`}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex space-x-2 items-center">
            <span className="text-white">ช่วงเวลา:</span>
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-[180px] bg-indigo-950/40 border-indigo-800/40">
                <SelectValue placeholder="เลือกช่วงเวลา" />
              </SelectTrigger>
              <SelectContent className="bg-indigo-950 border-indigo-800 text-white">
                <SelectItem value="today">วันนี้</SelectItem>
                <SelectItem value="yesterday">เมื่อวาน</SelectItem>
                <SelectItem value="week">7 วันที่ผ่านมา</SelectItem>
                <SelectItem value="month">30 วันที่ผ่านมา</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex space-x-2 items-center">
            <span className="text-white">API Key:</span>
            <Select value={apiKey} onValueChange={setApiKey}>
              <SelectTrigger className="w-[180px] bg-indigo-950/40 border-indigo-800/40">
                <SelectValue placeholder="เลือก API Key" />
              </SelectTrigger>
              <SelectContent className="bg-indigo-950 border-indigo-800 text-white">
                <SelectItem value="all">ทั้งหมด</SelectItem>
                {apiKeys?.map((key: any) => (
                  <SelectItem key={key.id} value={key.id.toString()}>
                    {key.name ? `${key.name} (${key.username})` : `${key.apiKey.substring(0, 8)}... (${key.username})`}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <Tabs defaultValue="realtime" className="space-y-4" onValueChange={setActiveTab}>
          <TabsList className="bg-indigo-900/50 border border-indigo-800/40">
            <TabsTrigger value="realtime" className="data-[state=active]:bg-indigo-700">
              <Activity className="h-4 w-4 mr-2" />
              เรียลไทม์
            </TabsTrigger>
            <TabsTrigger value="usage" className="data-[state=active]:bg-indigo-700">
              <BarChart4 className="h-4 w-4 mr-2" />
              การใช้งาน
            </TabsTrigger>
            <TabsTrigger value="banks" className="data-[state=active]:bg-indigo-700">
              <Building className="h-4 w-4 mr-2" />
              สัดส่วนธนาคาร
            </TabsTrigger>
            <TabsTrigger value="geo" className="data-[state=active]:bg-indigo-700">
              <Globe className="h-4 w-4 mr-2" />
              ตำแหน่งทางภูมิศาสตร์
            </TabsTrigger>
            <TabsTrigger value="errors" className="data-[state=active]:bg-indigo-700">
              <AlertTriangle className="h-4 w-4 mr-2" />
              ข้อผิดพลาด
            </TabsTrigger>
          </TabsList>

          <TabsContent value="realtime" className="space-y-4">
            <div className="bg-indigo-950/30 rounded-lg border border-indigo-700/30 p-4">
              <h3 className="text-xl font-bold text-white mb-4 flex items-center">
                <Activity className="h-5 w-5 mr-2 text-amber-400" />
                การเรียกใช้งาน API แบบเรียลไทม์
              </h3>

              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b border-indigo-800/30">
                      <th className="text-left py-3 px-4 text-indigo-300 font-medium">เวลา</th>
                      <th className="text-left py-3 px-4 text-indigo-300 font-medium">API Key</th>
                      <th className="text-left py-3 px-4 text-indigo-300 font-medium">ผู้ใช้</th>
                      <th className="text-left py-3 px-4 text-indigo-300 font-medium">สถานะ</th>
                      <th className="text-left py-3 px-4 text-indigo-300 font-medium">ประเภทคำขอ</th>
                      <th className="text-left py-3 px-4 text-indigo-300 font-medium">รายละเอียด</th>
                    </tr>
                  </thead>
                  <tbody>
                    {Array.isArray(apiStats?.data?.recentLogs) && apiStats?.data?.recentLogs.map((log: any, index: number) => (
                      <tr
                        key={log.id || index}
                        className="border-b border-indigo-800/10 hover:bg-indigo-900/20"
                      >
                        <td className="py-3 px-4 text-indigo-100">
                          {new Date(log.created_at).toLocaleTimeString()}
                        </td>
                        <td className="py-3 px-4 text-indigo-100">
                          {log.api_key ? log.api_key.substring(0, 8) + '...' : 'N/A'}
                        </td>
                        <td className="py-3 px-4 text-indigo-100">
                          {log.username || 'N/A'}
                        </td>
                        <td className="py-3 px-4">
                          {log.response_status === 'success' ? (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              สำเร็จ
                            </span>
                          ) : (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                              <XCircle className="h-3 w-3 mr-1" />
                              ล้มเหลว
                            </span>
                          )}
                        </td>
                        <td className="py-3 px-4 text-indigo-100">
                          {log.request_type || 'N/A'}
                        </td>
                        <td className="py-3 px-4 text-indigo-100">
                          {log.request_data?.amount && `฿${log.request_data.amount}`}
                          {log.request_data?.bankCode && ` - ${getBankName(log.request_data.bankCode)}`}
                        </td>
                      </tr>
                    ))}

                    {/* Show placeholder when no data */}
                    {(!apiStats?.data?.recentLogs || !Array.isArray(apiStats?.data?.recentLogs) || apiStats?.data?.recentLogs.length === 0) && (
                      <tr>
                        <td colSpan={6} className="py-4 text-center text-indigo-400 italic">
                          ไม่พบข้อมูลการใช้งาน API ในช่วงเวลานี้
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="usage" className="space-y-4">
            <div className="bg-indigo-950/30 rounded-lg border border-indigo-700/30 p-4">
              <h3 className="text-xl font-bold text-white mb-4 flex items-center">
                <BarChart4 className="h-5 w-5 mr-2 text-amber-400" />
                ประวัติการใช้งาน API รายชั่วโมง
              </h3>

              <div className="h-[400px] w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={apiStats?.data?.hourlyStats || []} margin={{ top: 20, right: 30, left: 20, bottom: 70 }}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#334155" />
                    <XAxis
                      dataKey="hour"
                      stroke="#94a3b8"
                      tick={{ fill: '#94a3b8' }}
                      tickFormatter={(value) => {
                        if (!value) return '';
                        const date = new Date(value);
                        return date.getHours() + ':00';
                      }}
                      angle={-45}
                      textAnchor="end"
                      height={70}
                    />
                    <YAxis stroke="#94a3b8" tick={{ fill: '#94a3b8' }} />
                    <Tooltip
                      formatter={(value, name) => {
                        const formattedName = name === 'requests'
                          ? 'คำขอทั้งหมด'
                          : name === 'success'
                            ? 'สำเร็จ'
                            : 'ล้มเหลว';
                        return [value, formattedName];
                      }}
                      labelFormatter={(label) => {
                        if (!label) return '';
                        const date = new Date(label);
                        return `วันที่ ${date.toLocaleDateString()} เวลา ${date.getHours()}:00 น.`;
                      }}
                      contentStyle={{
                        backgroundColor: '#1e1b4b',
                        borderColor: '#4338ca',
                        color: '#e0e7ff'
                      }}
                    />
                    <Legend
                      formatter={(value) => {
                        return value === 'requests'
                          ? 'คำขอทั้งหมด'
                          : value === 'success'
                            ? 'สำเร็จ'
                            : 'ล้มเหลว';
                      }}
                    />
                    <Bar dataKey="requests" fill="#8884d8" name="requests" />
                    <Bar dataKey="success" fill="#4ade80" name="success" />
                    <Bar dataKey="failed" fill="#f87171" name="failed" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>

            <div className="bg-indigo-950/30 rounded-lg border border-indigo-700/30 p-4">
              <h3 className="text-xl font-bold text-white mb-4 flex items-center">
                <Activity className="h-5 w-5 mr-2 text-amber-400" />
                แนวโน้มอัตราความสำเร็จ
              </h3>

              <div className="h-[300px] w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={apiStats?.data?.successRateTrend || []} margin={{ top: 20, right: 30, left: 20, bottom: 70 }}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#334155" />
                    <XAxis
                      dataKey="day"
                      stroke="#94a3b8"
                      tick={{ fill: '#94a3b8' }}
                      tickFormatter={(value) => {
                        if (!value) return '';
                        const date = new Date(value);
                        return date.toLocaleDateString();
                      }}
                      angle={-45}
                      textAnchor="end"
                      height={70}
                    />
                    <YAxis
                      stroke="#94a3b8"
                      tick={{ fill: '#94a3b8' }}
                      domain={[0, 100]}
                      tickFormatter={(value) => `${value}%`}
                    />
                    <Tooltip
                      formatter={(value) => [`${value}%`, 'อัตราความสำเร็จ']}
                      labelFormatter={(label) => {
                        if (!label) return '';
                        const date = new Date(label);
                        return `วันที่ ${date.toLocaleDateString()}`;
                      }}
                      contentStyle={{
                        backgroundColor: '#1e1b4b',
                        borderColor: '#4338ca',
                        color: '#e0e7ff'
                      }}
                    />
                    <Legend />
                    <defs>
                      <linearGradient id="colorSuccessRate" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#4ade80" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#4ade80" stopOpacity={0}/>
                      </linearGradient>
                    </defs>
                    <Area
                      type="monotone"
                      dataKey="successRate"
                      stroke="#4ade80"
                      fillOpacity={0.3}
                      fill="url(#colorSuccessRate)"
                      name="อัตราความสำเร็จ"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="banks" className="space-y-4">
            <div className="bg-indigo-950/30 rounded-lg border border-indigo-700/30 p-4">
              <h3 className="text-xl font-bold text-white mb-4 flex items-center">
                <Building className="h-5 w-5 mr-2 text-amber-400" />
                สัดส่วนการใช้งานแยกตามธนาคาร
              </h3>

              <div className="flex flex-col md:flex-row">
                <div className="w-full md:w-1/2 h-[350px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsPie>
                      <Pie
                        data={apiStats?.data?.bankStats || []}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        label={({ name, percent }) => `${getBankName(name)}: ${(percent * 100).toFixed(0)}%`}
                        labelLine={true}
                        nameKey="bankCode"
                        dataKey="count"
                      >
                        {Array.isArray(apiStats?.data?.bankStats) && apiStats?.data?.bankStats.map((entry: any, index: number) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={BANK_COLORS[index % BANK_COLORS.length]}
                          />
                        ))}
                      </Pie>
                      <Tooltip
                        formatter={(value, name, props) => [value, getBankName(props.payload.bankCode)]}
                        contentStyle={{
                          backgroundColor: '#1e1b4b',
                          borderColor: '#4338ca',
                          color: '#e0e7ff'
                        }}
                      />
                    </RechartsPie>
                  </ResponsiveContainer>
                </div>

                <div className="w-full md:w-1/2 overflow-x-auto mt-4 md:mt-0">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-indigo-800/30">
                        <th className="text-left py-3 px-4 text-indigo-300 font-medium">ธนาคาร</th>
                        <th className="text-right py-3 px-4 text-indigo-300 font-medium">จำนวน</th>
                        <th className="text-right py-3 px-4 text-indigo-300 font-medium">เปอร์เซ็นต์</th>
                      </tr>
                    </thead>
                    <tbody>
                      {Array.isArray(apiStats?.data?.bankStats) && apiStats?.data?.bankStats.map((bank: any, index: number) => (
                        <tr key={bank.bankCode || index} className="border-b border-indigo-800/10">
                          <td className="py-3 px-4 text-indigo-100">
                            {getBankName(bank.bankCode)}
                          </td>
                          <td className="py-3 px-4 text-indigo-100 text-right">{bank.count}</td>
                          <td className="py-3 px-4 text-indigo-100 text-right">{bank.percentage}%</td>
                        </tr>
                      ))}

                      {(!apiStats?.data?.bankStats || apiStats.data.bankStats.length === 0) && (
                        <tr>
                          <td colSpan={3} className="py-4 text-center text-indigo-400 italic">
                            ไม่พบข้อมูลการใช้งานธนาคารในช่วงเวลานี้
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="geo" className="space-y-4">
            <div className="bg-indigo-950/30 rounded-lg border border-indigo-700/30 p-4">
              <h3 className="text-xl font-bold text-white mb-4 flex items-center">
                <Globe className="h-5 w-5 mr-2 text-amber-400" />
                การใช้งาน API ตามตำแหน่งทางภูมิศาสตร์
              </h3>

              <div className="flex flex-col md:flex-row">
                <div className="w-full md:w-1/2 h-[400px]">
                  {/* World Map */}
                  <ComposableMap
                    projection="geoMercator"
                    projectionConfig={{
                      scale: 800,
                      center: [100.5018, 13.7563] // กรุงเทพฯ
                    }}
                  >
                    <Geographies geography={worldMapData}>
                      {({ geographies }) =>
                        geographies.map(geo => (
                          <Geography
                            key={geo.rsmKey}
                            geography={geo}
                            fill="#1e40af"
                            stroke="#475569"
                            strokeWidth={0.5}
                            style={{
                              // CSS styles for default, hover, pressed states
                              outline: 'none'
                            }}
                            className="hover:fill-indigo-800 active:fill-indigo-900"
                          />
                        ))
                      }
                    </Geographies>

                    {Array.isArray(apiStats?.data?.geoStats) && apiStats?.data?.geoStats.map((geo: any, index: number) => {
                      // ถ้ามี geoData ที่แปลงข้อมูลตำแหน่ง แต่ถ้าไม่มีให้ใช้จุดตรงกรุงเทพฯ เป็นค่าเริ่มต้น
                      const geoLocation = geo.location.split(',');
                      const lat = geoLocation.length >= 2 ? parseFloat(geoLocation[0]) : 13.7563;
                      const lng = geoLocation.length >= 2 ? parseFloat(geoLocation[1]) : 100.5018;

                      return (
                        <Marker key={index} coordinates={[lng, lat]}>
                          <circle
                            r={Math.sqrt(geo.count) * 3 + 5}
                            fill="#f59e0b"
                            fillOpacity={0.7}
                            stroke="#f59e0b"
                            strokeWidth={2}
                          />
                          <text
                            textAnchor="middle"
                            y={-10}
                            style={{ fontFamily: "Arial", fill: "#ffffff", fontSize: 10 }}
                          >
                            {geo.location}
                          </text>
                        </Marker>
                      );
                    })}
                  </ComposableMap>
                </div>

                <div className="w-full md:w-1/2 overflow-x-auto mt-4 md:mt-0">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-indigo-800/30">
                        <th className="text-left py-3 px-4 text-indigo-300 font-medium">ตำแหน่ง</th>
                        <th className="text-right py-3 px-4 text-indigo-300 font-medium">จำนวน</th>
                        <th className="text-right py-3 px-4 text-indigo-300 font-medium">เปอร์เซ็นต์</th>
                      </tr>
                    </thead>
                    <tbody>
                      {Array.isArray(apiStats?.data?.geoStats) && apiStats?.data?.geoStats.map((geo: any, index: number) => (
                        <tr key={index} className="border-b border-indigo-800/10">
                          <td className="py-3 px-4 text-indigo-100">
                            {geo.location || 'ไม่ระบุ'}
                          </td>
                          <td className="py-3 px-4 text-indigo-100 text-right">{geo.count}</td>
                          <td className="py-3 px-4 text-indigo-100 text-right">{geo.percentage}%</td>
                        </tr>
                      ))}

                      {(!apiStats?.data?.geoStats || apiStats.data.geoStats.length === 0) && (
                        <tr>
                          <td colSpan={3} className="py-4 text-center text-indigo-400 italic">
                            ไม่พบข้อมูลตำแหน่งทางภูมิศาสตร์ในช่วงเวลานี้
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="errors" className="space-y-4">
            <div className="bg-indigo-950/30 rounded-lg border border-indigo-700/30 p-4">
              <h3 className="text-xl font-bold text-white mb-4 flex items-center">
                <AlertTriangle className="h-5 w-5 mr-2 text-amber-400" />
                การวิเคราะห์ข้อผิดพลาด
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="h-[300px]">
                  <h4 className="text-lg font-medium text-white mb-2">ประเภทข้อผิดพลาด</h4>
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsPie>
                      <Pie
                        data={apiStats?.data?.errorsByType || []}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        labelLine={true}
                        nameKey="errorType"
                        dataKey="count"
                      >
                        {Array.isArray(apiStats?.data?.errorsByType) && apiStats?.data?.errorsByType.map((entry: any, index: number) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={COLORS[index % COLORS.length]}
                          />
                        ))}
                      </Pie>
                      <Tooltip
                        formatter={(value, name, props) => [value, props.payload.errorType]}
                        contentStyle={{
                          backgroundColor: '#1e1b4b',
                          borderColor: '#4338ca',
                          color: '#e0e7ff'
                        }}
                      />
                    </RechartsPie>
                  </ResponsiveContainer>
                </div>

                <div className="h-[300px]">
                  <h4 className="text-lg font-medium text-white mb-2">แนวโน้มข้อผิดพลาด</h4>
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={apiStats?.data?.errorTrend || []} margin={{ top: 10, right: 30, left: 0, bottom: 30 }}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#334155" />
                      <XAxis
                        dataKey="day"
                        stroke="#94a3b8"
                        tick={{ fill: '#94a3b8' }}
                        tickFormatter={(value) => {
                          if (!value) return '';
                          const date = new Date(value);
                          return date.toLocaleDateString();
                        }}
                        angle={-45}
                        textAnchor="end"
                        height={50}
                      />
                      <YAxis stroke="#94a3b8" tick={{ fill: '#94a3b8' }} />
                      <Tooltip
                        formatter={(value) => [value, 'จำนวนข้อผิดพลาด']}
                        labelFormatter={(label) => {
                          if (!label) return '';
                          const date = new Date(label);
                          return `วันที่ ${date.toLocaleDateString()}`;
                        }}
                        contentStyle={{
                          backgroundColor: '#1e1b4b',
                          borderColor: '#4338ca',
                          color: '#e0e7ff'
                        }}
                      />
                      <defs>
                        <linearGradient id="colorErrors" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#ef4444" stopOpacity={0.8}/>
                          <stop offset="95%" stopColor="#ef4444" stopOpacity={0}/>
                        </linearGradient>
                      </defs>
                      <Area
                        type="monotone"
                        dataKey="count"
                        stroke="#ef4444"
                        fillOpacity={0.3}
                        fill="url(#colorErrors)"
                        name="ข้อผิดพลาด"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </div>

              <div className="mt-6">
                <h4 className="text-lg font-medium text-white mb-2">ข้อผิดพลาดล่าสุด</h4>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-indigo-800/30">
                        <th className="text-left py-3 px-4 text-indigo-300 font-medium">เวลา</th>
                        <th className="text-left py-3 px-4 text-indigo-300 font-medium">API Key</th>
                        <th className="text-left py-3 px-4 text-indigo-300 font-medium">ประเภทคำขอ</th>
                        <th className="text-left py-3 px-4 text-indigo-300 font-medium">ข้อความผิดพลาด</th>
                      </tr>
                    </thead>
                    <tbody>
                      {Array.isArray(apiStats?.data?.recentErrors) && apiStats?.data?.recentErrors.map((error: any, index: number) => (
                        <tr
                          key={error.id || index}
                          className="border-b border-indigo-800/10 hover:bg-indigo-900/20"
                        >
                          <td className="py-3 px-4 text-indigo-100">
                            {new Date(error.created_at).toLocaleString()}
                          </td>
                          <td className="py-3 px-4 text-indigo-100">
                            {error.api_key ? error.api_key.substring(0, 8) + '...' : 'N/A'}
                          </td>
                          <td className="py-3 px-4 text-indigo-100">
                            {error.request_type || 'N/A'}
                          </td>
                          <td className="py-3 px-4 text-indigo-100">
                            {error.error_message || 'ไม่ระบุข้อความผิดพลาด'}
                          </td>
                        </tr>
                      ))}

                      {(!apiStats?.data?.recentErrors || apiStats?.data?.recentErrors.length === 0) && (
                        <tr>
                          <td colSpan={4} className="py-4 text-center text-indigo-400 italic">
                            ไม่พบข้อมูลข้อผิดพลาดในช่วงเวลานี้
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </Admin>
  );
}