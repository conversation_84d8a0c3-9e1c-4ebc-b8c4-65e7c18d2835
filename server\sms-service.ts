/**
 * บริการส่ง SMS และ OTP ผ่าน SMSMKT
 * https://www.smsmkt.com/
 */

import axios from 'axios';
import { db } from './db';
import { systemSettings } from '@shared/schema';
import { eq } from 'drizzle-orm';
import { logger } from './logger';

export class SMSService {
  private apiKey: string | null = null;
  private secret: string | null = null;
  private sender: string = 'SLIPKUY';
  private baseUrl = 'https://apicall.deesmsx.com/v1';
  private isConfigured = false;
  private offlineMode = false;

  constructor() {
    this.init();
  }

  /**
   * ตั้งค่าเริ่มต้นสำหรับ SMS Service
   */
  async init() {
    try {
      // พยายามโหลดการตั้งค่าจากฐานข้อมูลก่อน
      const [settings] = await db
        .select()
        .from(systemSettings)
        .where(eq(systemSettings.key, 'sms_settings'));

      if (settings && settings.valueJson) {
        const config = settings.valueJson as any;
        this.apiKey = config.api_key || null;
        this.secret = config.secret || null;
        this.sender = config.sender || 'SLIPKUY';

        if (this.apiKey && this.secret) {
          this.isConfigured = true;
          this.offlineMode = config.offline_mode || false;
          console.log('SMSService: ใช้ค่าจากฐานข้อมูล');
          logger.info(`SMS Service config: API Key: ${this.apiKey ? 'set' : 'not set'}, Secret: ${this.secret ? 'set' : 'not set'}, Sender: ${this.sender}`);
        }
      }

      // ถ้าไม่มีในฐานข้อมูล ใช้ค่าจาก env
      if (!this.isConfigured) {
        this.apiKey = process.env.SMSMKT_API_KEY || null;
        this.secret = process.env.SMSMKT_SECRET || null;
        this.sender = process.env.SMSMKT_SENDER || 'SLIPKUY';

        if (this.apiKey && this.secret) {
          this.isConfigured = true;
          console.log('SMSService: ใช้ค่าจาก environment variables');
        } else {
          console.warn('SMSService: ไม่พบการตั้งค่า SMSMKT - ใช้โหมด offline');
          this.offlineMode = true;
        }
      }
    } catch (error) {
      console.error('SMSService init error:', error);
      this.offlineMode = true;
    }
  }

  /**
   * ส่ง OTP ไปยังเบอร์โทรศัพท์ตามมาตรฐาน DEESMSX OTP API
   *
   * @param phoneNumber เบอร์โทรศัพท์ที่จะส่ง OTP (เช่น "0812345678")
   * @param otpRef รหัสอ้างอิง OTP (เช่น "REF001") - ถ้าไม่ระบุจะสร้างรหัสใหม่
   * @returns ข้อมูลการส่ง OTP
   */
  async sendOTP(phoneNumber: string, otpRef?: string): Promise<{
    success: boolean;
    otpCode?: string;
    otpRef?: string;
    token?: string;
    error?: string;
    offlineMode?: boolean;
  }> {
    // ตรวจสอบเบอร์โทรศัพท์
    if (!phoneNumber || !this.isValidPhoneNumber(phoneNumber)) {
      return {
        success: false,
        error: 'เบอร์โทรศัพท์ไม่ถูกต้อง'
      };
    }

    // ถ้าอยู่ในโหมดจำลอง (offline) ให้ใช้รหัส OTP คงที่
    if (this.offlineMode) {
      logger.info(`ใช้โหมดจำลองการส่ง OTP ไปยังเบอร์ ${phoneNumber}`);
      const mockOtpCode = '123456'; // รหัสคงที่สำหรับทดสอบ
      const mockRef = otpRef || this.generateOTPRef();

      return {
        success: true,
        otpCode: mockOtpCode,
        otpRef: mockRef,
        token: `mock_token_${Date.now()}`,
        offlineMode: true
      };
    }

    try {
      if (!this.isConfigured) {
        await this.init(); // ลองโหลดการตั้งค่าอีกครั้ง

        if (!this.isConfigured) {
          return {
            success: false,
            error: 'ยังไม่ได้ตั้งค่า SMS Service'
          };
        }
      }

      // URL สำหรับส่ง OTP ตามมาตรฐาน DEESMSX
      const url = `${this.baseUrl}/otp/request`;

      logger.info(`กำลังส่ง OTP ไปยังเบอร์ ${phoneNumber} ผ่าน DEESMSX OTP API`);

      // สร้างรหัสอ้างอิงถ้าไม่มี
      const ref = otpRef || this.generateOTPRef();

      // สร้าง config ตามรูปแบบ API ที่ถูกต้องตามคู่มือ DEESMSX
      const postData = {
        apiKey: this.apiKey,
        secretKey: this.secret,
        to: this.formatPhoneNumber(phoneNumber),
        sender: this.sender,
        lang: 'th',
        isShowRef: '1'
      };

      // ส่งคำขอไปยัง DEESMSX API
      const response = await axios.post(url, postData, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      logger.info(`DEESMSX OTP API Response: ${JSON.stringify(response.data)}`);

      // ตรวจสอบว่าเป็นการตอบกลับที่สำเร็จหรือไม่
      // กรณีที่ error = 0 หรือ msg มีคำว่า ACCEPTD แสดงว่าส่งสำเร็จ
      if (response.data && (response.data.error === "0" ||
          (response.data.msg && response.data.msg.includes("ACCEPTD")))) {
        logger.info(`OTP ส่งสำเร็จไปยังเบอร์ ${phoneNumber}`);
        return {
          success: true,
          otpRef: response.data.result?.ref || ref,
          token: response.data.result?.token || '',
          offlineMode: false
        };
      } else {
        const errorMessage = response.data ? response.data.msg || 'ไม่สามารถส่ง OTP ได้' : 'ไม่สามารถส่ง OTP ได้';
        logger.error(`DEESMSX OTP API Error: ${errorMessage}`);
        return {
          success: false,
          error: errorMessage
        };
      }
    } catch (error: any) {
      logger.error('DEESMSX OTP API Error:', error);

      // ถ้าเกิดข้อผิดพลาดและมีการตั้งค่าให้ใช้โหมดจำลองเมื่อเกิดข้อผิดพลาด
      if (this.offlineMode) {
        logger.info(`เกิดข้อผิดพลาดในการส่ง OTP จริง - ใช้โหมดจำลองแทน`);
        const mockOtpCode = '123456';
        const mockRef = otpRef || this.generateOTPRef();

        return {
          success: true,
          otpCode: mockOtpCode,
          otpRef: mockRef,
          token: `mock_token_${Date.now()}`,
          offlineMode: true
        };
      }

      return {
        success: false,
        error: `ไม่สามารถส่ง OTP ได้: ${error.message || 'เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ'}`
      };
    }
  }

  /**
   * ตรวจสอบ OTP ว่าถูกต้องหรือไม่ตามมาตรฐาน DEESMSX OTP API
   *
   * @param otpCode รหัส OTP ที่ผู้ใช้กรอก
   * @param phoneNumber เบอร์โทรศัพท์ที่รับ OTP
   * @param token token ที่ได้จากการส่ง OTP
   * @param otpRef รหัสอ้างอิง OTP (ถ้ามี)
   * @returns ผลการตรวจสอบ OTP
   */
  async verifyOTP(otpCode: string, phoneNumber: string, token: string, otpRef?: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    // ตรวจสอบข้อมูลที่จำเป็น
    if (!otpCode || !phoneNumber) {
      return {
        success: false,
        error: 'ข้อมูลไม่ครบถ้วน'
      };
    }

    logger.info(`ตรวจสอบ OTP: ${otpCode} สำหรับเบอร์ ${phoneNumber} (token: ${token}, ref: ${otpRef || 'ไม่ระบุ'})`);

    // ถ้าอยู่ในโหมดจำลอง (offline) ให้ยอมรับรหัส 123456 เสมอ
    if (this.offlineMode) {
      const isValidMockOtp = (otpCode === '123456');
      logger.info(`ใช้โหมดจำลองการตรวจสอบ OTP: ${isValidMockOtp ? 'สำเร็จ' : 'ไม่สำเร็จ'}`);

      return {
        success: isValidMockOtp,
        error: isValidMockOtp ? undefined : 'รหัส OTP ไม่ถูกต้อง'
      };
    }

    try {
      if (!this.isConfigured) {
        await this.init(); // ลองโหลดการตั้งค่าอีกครั้ง

        if (!this.isConfigured) {
          return {
            success: false,
            error: 'ยังไม่ได้ตั้งค่า SMS Service'
          };
        }
      }

      // ตรวจสอบว่ามี token หรือไม่
      if (!token) {
        logger.error('ไม่มี token สำหรับการตรวจสอบ OTP');
        return {
          success: false,
          error: 'ไม่มี token สำหรับการตรวจสอบ OTP'
        };
      }

      // URL สำหรับตรวจสอบ OTP ตามมาตรฐาน DEESMSX
      const url = `${this.baseUrl}/otp/verify`;

      logger.info(`กำลังตรวจสอบ OTP กับ DEESMSX OTP API: ${otpCode}`);

      // สร้างข้อมูลตามรูปแบบ API ที่ถูกต้องตามคู่มือ DEESMSX
      const postData = {
        apiKey: this.apiKey,
        secretKey: this.secret,
        token: token,
        pin: otpCode
      };

      logger.info(`DEESMSX OTP Validate Request Data: ${JSON.stringify({
        apiKey: postData.apiKey ? 'HIDDEN' : 'NOT_SET',
        secretKey: postData.secretKey ? 'HIDDEN' : 'NOT_SET',
        token: postData.token,
        pin: postData.pin
      })}`);

      // ส่งคำขอไปยัง DEESMSX API
      const response = await axios.post(url, postData, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      logger.info(`DEESMSX OTP Validate Response: ${JSON.stringify(response.data)}`);

      // ตรวจสอบทั้ง error และ status เนื่องจาก API อาจส่งค่ากลับมาในรูปแบบที่แตกต่างกัน
      logger.info(`ตรวจสอบผลลัพธ์จาก API: ${JSON.stringify(response.data)}`);
      if (response.data && (
          response.data.error === "0" ||
          response.data.error === 0 ||
          response.data.status === "0" ||
          response.data.status === 0 ||
          response.data.code === "0" ||
          response.data.code === 0 ||
          response.data.status === "200" ||
          response.data.status === 200
      )) {
        logger.info(`ยืนยัน OTP สำเร็จสำหรับเบอร์ ${phoneNumber}`);
        return {
          success: true
        };
      } else {
        const errorMessage = response.data ? response.data.msg || 'รหัส OTP ไม่ถูกต้อง' : 'รหัส OTP ไม่ถูกต้อง';
        logger.error(`DEESMSX OTP Validate Error: ${errorMessage}`);
        return {
          success: false,
          error: errorMessage
        };
      }
    } catch (error: any) {
      logger.error(`Error verifying OTP: ${error.message}`);

      // ถ้าเกิดข้อผิดพลาดและมีการตั้งค่าให้ใช้โหมดจำลองเมื่อเกิดข้อผิดพลาด
      if (this.offlineMode) {
        logger.info(`เกิดข้อผิดพลาดในการตรวจสอบ OTP จริง - ใช้โหมดจำลองแทน`);
        const isValidMockOtp = (otpCode === '123456');

        return {
          success: isValidMockOtp,
          error: isValidMockOtp ? undefined : 'รหัส OTP ไม่ถูกต้อง'
        };
      }

      return {
        success: false,
        error: `ไม่สามารถตรวจสอบรหัส OTP ได้: ${error.message}`
      };
    }
  }

  /**
   * ส่ง SMS ไปยังเบอร์โทรศัพท์
   *
   * @param phoneNumber เบอร์โทรศัพท์ที่จะส่ง
   * @param message ข้อความ
   * @returns ผลการส่ง SMS
   */
  async sendSMS(phoneNumber: string, message: string): Promise<{
    success: boolean;
    error?: string;
    messageId?: string;
    offlineMode?: boolean;
  }> {
    // ตรวจสอบเบอร์โทรศัพท์
    if (!phoneNumber || !this.isValidPhoneNumber(phoneNumber)) {
      return {
        success: false,
        error: 'เบอร์โทรศัพท์ไม่ถูกต้อง'
      };
    }

    // ตรวจสอบข้อความ
    if (!message || message.trim() === '') {
      return {
        success: false,
        error: 'ข้อความไม่ควรเป็นค่าว่าง'
      };
    }

    // แก้ไขรูปแบบเบอร์โทรศัพท์
    const formattedPhone = this.formatPhoneNumber(phoneNumber);

    // เปลี่ยนเป็นใช้ API ภายนอกเท่านั้น ไม่ใช้โหมด offline

    try {
      if (!this.isConfigured) {
        await this.init(); // ลองโหลดการตั้งค่าอีกครั้ง

        if (!this.isConfigured) {
          return {
            success: false,
            error: 'ยังไม่ได้ตั้งค่า SMS Service'
          };
        }
      }

      // URL สำหรับส่ง SMS
      const url = `${this.baseUrl}/sms/send`;

      logger.info(`กำลังส่ง SMS ไปยังเบอร์ ${phoneNumber} ผ่าน DEESMSX API: ${message}`);

      // สร้าง config ตามรูปแบบ API ที่ถูกต้อง - แก้ไขพารามิเตอร์ให้ถูกต้อง
      const postData = {
        apiKey: this.apiKey,
        secretKey: this.secret,
        msisdn: formattedPhone,
        message: message,
        sender: this.sender
      };

      logger.info(`DEESMSX API Request Data: ${JSON.stringify(postData)}`);

      // ส่งคำขอไปยัง DEESMSX API
      const response = await axios.post(url, postData, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
      const data = response.data;

      logger.info(`DEESMSX API Response: ${JSON.stringify(data)}`);

      if (data.error === '0') {
        return {
          success: true,
          messageId: data.result?.messageId || '',
          offlineMode: false
        };
      } else {
        logger.error(`DEESMSX SMS Error: ${data.msg || 'Unknown error'}`);
        return {
          success: false,
          error: `ไม่สามารถส่ง SMS ได้: ${data.msg || 'เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ'}`
        };
      }
    } catch (error: any) {
      logger.error('DEESMSX sendSMS error:', error);
      return {
        success: false,
        error: `ไม่สามารถส่ง SMS ได้: ${error.message || 'เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ'}`
      };
    }
  }

  /**
   * ส่งการแจ้งเตือนผ่าน SMS
   *
   * @param phoneNumber เบอร์โทรศัพท์
   * @param title หัวข้อการแจ้งเตือน
   * @param content เนื้อหาการแจ้งเตือน
   * @returns ผลการส่งการแจ้งเตือน
   */
  async sendNotification(phoneNumber: string, title: string, content: string): Promise<{
    success: boolean;
    error?: string;
    messageId?: string;
    offlineMode?: boolean;
  }> {
    const message = `SLIPKUY: ${title}\n${content}`;
    return this.sendSMS(phoneNumber, message);
  }

  /**
   * ตรวจสอบว่าเบอร์โทรศัพท์ถูกต้องหรือไม่
   *
   * @param phoneNumber เบอร์โทรศัพท์ที่ต้องการตรวจสอบ
   * @returns true ถ้าเบอร์โทรศัพท์ถูกต้อง, false ถ้าไม่ถูกต้อง
   */
  private isValidPhoneNumber(phoneNumber: string): boolean {
    // รูปแบบเบอร์โทรศัพท์ไทย
    // - ขึ้นต้นด้วย 0 ตามด้วยตัวเลข 9 หลัก (รวมเป็น 10 หลัก) เช่น 0812345678
    // - หรือขึ้นต้นด้วย +66 ตามด้วยตัวเลข 9 หลัก (ไม่ต้องมี 0 นำหน้า) เช่น +66812345678
    const thaiMobileRegex = /^(0\d{9}|\+66\d{9})$/;
    return thaiMobileRegex.test(phoneNumber);
  }

  /**
   * แปลงรูปแบบเบอร์โทรศัพท์ให้เป็น 66XXXXXXXXX (ไม่มี 0 นำหน้า)
   *
   * @param phoneNumber เบอร์โทรศัพท์
   * @returns เบอร์โทรศัพท์ในรูปแบบ 66XXXXXXXXX
   */
  private formatPhoneNumber(phoneNumber: string): string {
    // ถ้าเริ่มด้วย 0 ให้แทนที่ด้วย 66
    if (phoneNumber.startsWith('0')) {
      return `66${phoneNumber.substring(1)}`;
    }

    // ถ้าเริ่มด้วย +66 ให้ตัด + ออก
    if (phoneNumber.startsWith('+66')) {
      return phoneNumber.substring(1);
    }

    // ถ้าเริ่มด้วย 66 แล้ว ให้ใช้ค่าเดิม
    if (phoneNumber.startsWith('66')) {
      return phoneNumber;
    }

    // กรณีอื่นๆ อาจไม่ใช่เบอร์ไทย แต่ส่งไปยัง API ตามรูปแบบเดิม
    return phoneNumber;
  }

  /**
   * สร้างรหัส OTP แบบสุ่ม
   *
   * @returns รหัส OTP แบบสุ่ม 6 หลัก
   */
  private generateOTP(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * สร้างรหัสอ้างอิง OTP แบบสุ่ม
   *
   * @returns รหัสอ้างอิง OTP
   */
  private generateOTPRef(): string {
    return `REF${Date.now().toString(36).toUpperCase()}${Math.random().toString(36).substring(2, 5).toUpperCase()}`;
  }

  /**
   * ตั้งค่า SMS Service
   *
   * @param config ค่าการตั้งค่า
   */
  async updateConfig(config: {
    apiKey?: string,
    secret?: string,
    sender?: string,
    offlineMode?: boolean
  }): Promise<boolean> {
    try {
      const updatedConfig = {
        api_key: config.apiKey || this.apiKey,
        secret: config.secret || this.secret,
        sender: config.sender || this.sender,
        offline_mode: config.offlineMode !== undefined ? config.offlineMode : this.offlineMode
      };

      // บันทึกการตั้งค่าลงฐานข้อมูล
      await db
        .insert(systemSettings)
        .values({
          key: 'sms_settings',
          valueJson: updatedConfig,
          description: 'การตั้งค่าสำหรับบริการ SMS'
        })
        .onConflictDoUpdate({
          target: systemSettings.key,
          set: {
            valueJson: updatedConfig,
            updatedAt: new Date()
          }
        });

      // อัพเดตค่าปัจจุบัน
      this.apiKey = updatedConfig.api_key;
      this.secret = updatedConfig.secret;
      this.sender = updatedConfig.sender;
      this.isConfigured = !!(this.apiKey && this.secret);

      // อัพเดตโหมดออฟไลน์ถ้ามีการกำหนดค่า
      if (config.offlineMode !== undefined) {
        this.offlineMode = config.offlineMode;
      } else if (!this.isConfigured) {
        // ถ้าไม่มีการกำหนดค่าและไม่มีการตั้งค่า API ให้ใช้โหมดออฟไลน์โดยอัตโนมัติ
        this.offlineMode = true;
      }

      return true;
    } catch (error) {
      logger.error('SMSService updateConfig error:', error);
      return false;
    }
  }
}

export const smsService = new SMSService();