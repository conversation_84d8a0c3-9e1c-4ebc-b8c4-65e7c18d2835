# local-db.nix - สำหรับการรัน PostgreSQL แบบ ephemeral (ชั่วคราว)
{ pkgs ? import <nixpkgs> {} }:

let
  # กำหนดชื่อฐานข้อมูล ชื่อผู้ใช้ และรหัสผ่าน
  dbName = "slipkuy_db";
  dbUser = "slipkuy_user";
  dbPassword = "slipkuy_password";

  # กำหนดพอร์ตที่ PostgreSQL จะรัน
  dbPort = 5432;

  # สร้างสคริปต์สำหรับเริ่มต้น PostgreSQL
  initPostgresScript = pkgs.writeShellScriptBin "init-postgres" ''
    set -e

    export PGDATA="$HOME/.postgres_data"
    export PGHOST="$HOME/.postgres"
    export PGPORT="${toString dbPort}"
    export LOG_PATH="$HOME/.postgres.log"

    # สร้างโฟลเดอร์สำหรับ socket และข้อมูล
    mkdir -p $PGHOST $PGDATA

    # ตรวจสอบว่า PostgreSQL ได้ถูกเริ่มต้นแล้วหรือไม่
    if [ ! -f "$PGDATA/PG_VERSION" ]; then
      echo "🔧 กำลังเริ่มต้น PostgreSQL..."
      ${pkgs.postgresql}/bin/initdb --encoding=UTF8 --locale=en_US.UTF-8 --auth=trust --nosync -D $PGDATA
    fi

    # เริ่มต้น PostgreSQL
    echo "🚀 กำลังเริ่ม PostgreSQL..."
    ${pkgs.postgresql}/bin/pg_ctl -D $PGDATA -l $LOG_PATH -o "-c unix_socket_directories=$PGHOST -c listen_addresses= -c port=$PGPORT" start

    # รอให้ PostgreSQL พร้อมใช้งาน
    echo "⏳ กำลังรอให้ PostgreSQL พร้อมใช้งาน..."
    until ${pkgs.postgresql}/bin/pg_isready -h $PGHOST -p $PGPORT; do
      sleep 0.1
    done

    # ตรวจสอบว่าฐานข้อมูลมีอยู่แล้วหรือไม่
    if ! ${pkgs.postgresql}/bin/psql -h $PGHOST -p $PGPORT -lqt | cut -d \| -f 1 | grep -qw ${dbName}; then
      echo "🔧 กำลังสร้างฐานข้อมูล ${dbName}..."
      ${pkgs.postgresql}/bin/createdb -h $PGHOST -p $PGPORT ${dbName}

      # สร้างผู้ใช้และให้สิทธิ์
      ${pkgs.postgresql}/bin/psql -h $PGHOST -p $PGPORT -d ${dbName} -c "CREATE USER ${dbUser} WITH PASSWORD '${dbPassword}';"
      ${pkgs.postgresql}/bin/psql -h $PGHOST -p $PGPORT -d ${dbName} -c "GRANT ALL PRIVILEGES ON DATABASE ${dbName} TO ${dbUser};"
      ${pkgs.postgresql}/bin/psql -h $PGHOST -p $PGPORT -d ${dbName} -c "ALTER USER ${dbUser} WITH SUPERUSER;"

      echo "✅ สร้างฐานข้อมูลและผู้ใช้เรียบร้อยแล้ว"
    else
      echo "✅ ฐานข้อมูล ${dbName} มีอยู่แล้ว"
    fi

    echo "🔄 กำลังนำเข้าข้อมูลจาก backup.sql..."
    ${pkgs.postgresql}/bin/psql -h $PGHOST -p $PGPORT -d ${dbName} -f backup.sql

    echo "✅ นำเข้าข้อมูลเรียบร้อยแล้ว"
    echo "✅ PostgreSQL พร้อมใช้งานแล้ว!"
    echo "🔌 เชื่อมต่อด้วย: postgresql://${dbUser}:${dbPassword}@localhost:${toString dbPort}/${dbName}"

    # ตั้งค่าตัวแปรสภาพแวดล้อมสำหรับการเชื่อมต่อ
    export DATABASE_URL="postgresql://${dbUser}:${dbPassword}@localhost:${toString dbPort}/${dbName}"
    export PGUSER=${dbUser}
    export PGPASSWORD=${dbPassword}
    export PGDATABASE=${dbName}

    # เปิด psql shell
    echo "🔄 กำลังเปิด psql shell..."
    ${pkgs.postgresql}/bin/psql -h $PGHOST -p $PGPORT -d ${dbName}
  '';

  # สร้างสคริปต์สำหรับหยุด PostgreSQL
  stopPostgresScript = pkgs.writeShellScriptBin "stop-postgres" ''
    set -e

    export PGDATA="$PWD/.postgres_data"

    echo "🛑 กำลังหยุด PostgreSQL..."
    ${pkgs.postgresql}/bin/pg_ctl -D $PGDATA stop

    echo "✅ หยุด PostgreSQL เรียบร้อยแล้ว"
  '';

  # สร้างสคริปต์สำหรับรีเซ็ตฐานข้อมูล
  resetPostgresScript = pkgs.writeShellScriptBin "reset-postgres" ''
    set -e

    export PGDATA="$PWD/.postgres_data"
    export PGHOST="$PWD/.postgres"

    # หยุด PostgreSQL ถ้ากำลังทำงานอยู่
    if [ -f "$PGDATA/postmaster.pid" ]; then
      echo "🛑 กำลังหยุด PostgreSQL..."
      ${pkgs.postgresql}/bin/pg_ctl -D $PGDATA stop || true
    fi

    # ลบข้อมูลทั้งหมด
    echo "🗑️ กำลังลบข้อมูล PostgreSQL..."
    rm -rf $PGDATA $PGHOST .postgres.log

    echo "✅ รีเซ็ต PostgreSQL เรียบร้อยแล้ว"
    echo "🔄 รัน 'init-postgres' เพื่อเริ่มต้นใหม่"
  '';

in pkgs.mkShell {
  buildInputs = with pkgs; [
    postgresql
    initPostgresScript
    stopPostgresScript
    resetPostgresScript
  ];

  shellHook = ''
    echo "🐘 PostgreSQL Local Development Environment"
    echo ""
    echo "📋 คำสั่งที่ใช้งานได้:"
    echo "  • init-postgres  - เริ่มต้น PostgreSQL และนำเข้าข้อมูลจาก backup.sql"
    echo "  • stop-postgres  - หยุด PostgreSQL"
    echo "  • reset-postgres - ลบข้อมูล PostgreSQL ทั้งหมดและเริ่มต้นใหม่"
    echo ""
    echo "🔌 ข้อมูลการเชื่อมต่อ:"
    echo "  • Host: localhost"
    echo "  • Port: ${toString dbPort}"
    echo "  • Database: ${dbName}"
    echo "  • Username: ${dbUser}"
    echo "  • Password: ${dbPassword}"
    echo ""
    echo "🔗 Connection string:"
    echo "  postgresql://${dbUser}:${dbPassword}@localhost:${toString dbPort}/${dbName}"
    echo ""
    echo "🚀 รัน 'init-postgres' เพื่อเริ่มต้น PostgreSQL และนำเข้าข้อมูล"
  '';
}
