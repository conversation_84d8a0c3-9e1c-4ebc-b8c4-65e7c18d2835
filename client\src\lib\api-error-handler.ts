import { useLocation } from 'wouter';

type ErrorResponse = {
  success: boolean;
  message: string;
  code?: string;
  needVerification?: boolean;
};

/**
 * ฟังก์ชันสำหรับดักจับและจัดการข้อผิดพลาดจาก API
 * 
 * @param error ข้อผิดพลาดที่เกิดขึ้นจากการเรียก API
 * @param navigate ฟังก์ชันสำหรับการเปลี่ยนเส้นทาง
 * @returns true หากมีการจัดการข้อผิดพลาดแล้ว, false หากไม่มีการจัดการ
 */
export function handleApiError(error: any, navigate: (to: string) => any): boolean {
  const errorResponse = error?.response?.data as ErrorResponse | undefined;
  
  // กรณีต้องยืนยันตัวตน
  if (errorResponse?.code === 'VERIFICATION_REQUIRED' || 
      (errorResponse?.message?.includes('ยืนยันตัวตน') && errorResponse?.needVerification)) {
    // เปลี่ยนเส้นทางไปยังหน้ายืนยันตัวตน
    navigate('/verification');
    return true;
  }
  
  // กรณีไม่ได้ล็อกอิน
  if (errorResponse?.code === 'NOT_AUTHENTICATED' || 
      (error?.status === 401 || error?.response?.status === 401)) {
    // เปลี่ยนเส้นทางไปยังหน้าล็อกอิน
    navigate('/auth');
    return true;
  }
  
  return false;
}

/**
 * สร้าง interceptor สำหรับ global fetch API เพื่อดักจับข้อผิดพลาดและจัดการอัตโนมัติ
 * 
 * @param navigate ฟังก์ชันสำหรับการเปลี่ยนเส้นทาง
 */
export function setupFetchInterceptor(navigate: (to: string) => any) {
  // บันทึก fetch ดั้งเดิม
  const originalFetch = window.fetch;
  
  // แทนที่ด้วย fetch ที่มีการดักจับข้อผิดพลาด
  window.fetch = async (input, init) => {
    try {
      const response = await originalFetch(input, init);
      
      // ถ้าสถานะเป็น 401 หรือ 403 ให้พยายามแปลง response เป็น JSON
      if (response.status === 401 || response.status === 403) {
        try {
          const clone = response.clone();
          const data = await clone.json();
          
          if (data.code === 'VERIFICATION_REQUIRED' && data.needVerification) {
            // เปลี่ยนเส้นทางไปยังหน้ายืนยันตัวตน
            navigate('/verification');
          } else if (data.code === 'NOT_AUTHENTICATED') {
            // เปลี่ยนเส้นทางไปยังหน้าล็อกอิน
            navigate('/auth');
          }
        } catch (e) {
          // ไม่สามารถแปลง response เป็น JSON ได้ ให้ดำเนินการต่อปกติ
        }
      }
      
      return response;
    } catch (error) {
      // ข้อผิดพลาดในการเชื่อมต่อ
      console.error('API connection error:', error);
      throw error;
    }
  };
}