import { execSync } from 'child_process';

try {
  // ใช้ drizzle-kit generate เพื่อสร้างการย้ายฐานข้อมูล
  console.log('Generating migration...');
  const generateOutput = execSync('npx drizzle-kit generate:pg').toString();
  console.log(generateOutput);
  
  // ใช้ drizzle-kit push สำหรับอัพเดทฐานข้อมูลโดยตรง
  console.log('\nPushing changes to database...');
  const pushOutput = execSync('npx drizzle-kit push').toString();
  console.log(pushOutput);
  
  console.log('Database migration completed successfully!');
} catch (error) {
  console.error(`Error: ${error}`);
}