import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Loader2, 
  Download, 
  Trash2, 
  Database, 
  Clock, 
  Calendar, 
  Check, 
  AlertCircle,
  RefreshCw
} from "lucide-react";
import { Switch } from "@/components/ui/switch";

interface BackupFile {
  fileName: string;
  filePath: string;
  fileSize: string;
  createdAt: string;
}

interface BackupSettings {
  enabled: boolean;
  frequency: 'daily' | 'weekly' | 'monthly';
  time: string;
  keepCount: number;
  lastBackup: string | null;
  nextBackup: string | null;
}

export function DatabaseBackupDialog() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [open, setOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('list');
  const [isCreatingBackup, setIsCreatingBackup] = useState(false);
  const [backupSettings, setBackupSettings] = useState<BackupSettings>({
    enabled: false,
    frequency: 'daily',
    time: '00:00',
    keepCount: 7,
    lastBackup: null,
    nextBackup: null
  });

  // ดึงรายการไฟล์สำรองข้อมูล
  const {
    data: backupFiles,
    isLoading: isLoadingBackupFiles,
    isError: isErrorBackupFiles,
    refetch: refetchBackupFiles
  } = useQuery<{ success: boolean; data: BackupFile[] }>({
    queryKey: ['/api/backup/list'],
    queryFn: async () => {
      const response = await fetch('/api/backup/list');
      if (!response.ok) {
        throw new Error('ไม่สามารถดึงรายการไฟล์สำรองข้อมูลได้');
      }
      return response.json();
    },
    enabled: open && activeTab === 'list'
  });

  // ดึงการตั้งค่าการสำรองข้อมูลอัตโนมัติ
  const {
    data: backupSettingsData,
    isLoading: isLoadingBackupSettings,
    isError: isErrorBackupSettings,
    refetch: refetchBackupSettings
  } = useQuery<{ success: boolean; data: BackupSettings }>({
    queryKey: ['/api/backup/schedule'],
    queryFn: async () => {
      const response = await fetch('/api/backup/schedule');
      if (!response.ok) {
        throw new Error('ไม่สามารถดึงการตั้งค่าการสำรองข้อมูลอัตโนมัติได้');
      }
      return response.json();
    },
    enabled: open && activeTab === 'settings'
  });

  // อัพเดตการตั้งค่าจากข้อมูลที่ดึงมา
  useEffect(() => {
    if (backupSettingsData?.data) {
      setBackupSettings(backupSettingsData.data);
    }
  }, [backupSettingsData]);

  // Mutation สำหรับการสร้างไฟล์สำรองข้อมูล
  const createBackupMutation = useMutation({
    mutationFn: async () => {
      setIsCreatingBackup(true);
      try {
        const response = await apiRequest('POST', '/api/backup/create');
        return await response.json();
      } finally {
        setIsCreatingBackup(false);
      }
    },
    onSuccess: (data) => {
      toast({
        title: "สร้างไฟล์สำรองข้อมูลสำเร็จ",
        description: `ไฟล์: ${data.data.fileName} (${data.data.fileSize})`,
        variant: "default",
      });
      refetchBackupFiles();
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message || "ไม่สามารถสร้างไฟล์สำรองข้อมูลได้",
        variant: "destructive",
      });
    }
  });

  // Mutation สำหรับการลบไฟล์สำรองข้อมูล
  const deleteBackupMutation = useMutation({
    mutationFn: async (fileName: string) => {
      const response = await apiRequest('DELETE', `/api/backup/delete/${fileName}`);
      return await response.json();
    },
    onSuccess: () => {
      toast({
        title: "ลบไฟล์สำรองข้อมูลสำเร็จ",
        variant: "default",
      });
      refetchBackupFiles();
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message || "ไม่สามารถลบไฟล์สำรองข้อมูลได้",
        variant: "destructive",
      });
    }
  });

  // Mutation สำหรับการตั้งค่าการสำรองข้อมูลอัตโนมัติ
  const updateBackupSettingsMutation = useMutation({
    mutationFn: async (settings: BackupSettings) => {
      const response = await apiRequest('POST', '/api/backup/schedule', settings);
      return await response.json();
    },
    onSuccess: () => {
      toast({
        title: "บันทึกการตั้งค่าสำเร็จ",
        description: "การตั้งค่าการสำรองข้อมูลอัตโนมัติได้รับการอัพเดตแล้ว",
        variant: "default",
      });
      refetchBackupSettings();
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message || "ไม่สามารถบันทึกการตั้งค่าได้",
        variant: "destructive",
      });
    }
  });

  // ฟังก์ชันสำหรับการดาวน์โหลดไฟล์สำรองข้อมูล
  const handleDownloadBackup = (fileName: string) => {
    window.open(`/api/backup/download/${fileName}`, '_blank');
  };

  // ฟังก์ชันสำหรับการลบไฟล์สำรองข้อมูล
  const handleDeleteBackup = (fileName: string) => {
    if (confirm(`คุณแน่ใจหรือไม่ที่จะลบไฟล์สำรองข้อมูล "${fileName}"?`)) {
      deleteBackupMutation.mutate(fileName);
    }
  };

  // ฟังก์ชันสำหรับการบันทึกการตั้งค่าการสำรองข้อมูลอัตโนมัติ
  const handleSaveBackupSettings = () => {
    updateBackupSettingsMutation.mutate(backupSettings);
  };

  // ฟังก์ชันสำหรับการแปลงวันที่เป็นรูปแบบที่อ่านง่าย
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('th-TH', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button 
          variant="outline" 
          className="border-purple-300 text-purple-700 hover:bg-purple-50"
        >
          <Database className="mr-2 h-4 w-4" />
          สำรองข้อมูล
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-purple-700 flex items-center">
            <Database className="mr-2 h-5 w-5" />
            จัดการการสำรองข้อมูลฐานข้อมูล
          </DialogTitle>
          <DialogDescription className="text-purple-600/70">
            สร้าง จัดการ และตั้งค่าการสำรองข้อมูลฐานข้อมูลอัตโนมัติ
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 bg-purple-50 border border-purple-200">
            <TabsTrigger 
              value="list" 
              className="flex items-center data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-600 data-[state=active]:to-purple-700 data-[state=active]:text-white"
            >
              <Database className="mr-2 h-4 w-4" />
              รายการไฟล์สำรองข้อมูล
            </TabsTrigger>
            <TabsTrigger 
              value="settings" 
              className="flex items-center data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-600 data-[state=active]:to-purple-700 data-[state=active]:text-white"
            >
              <Clock className="mr-2 h-4 w-4" />
              ตั้งค่าการสำรองข้อมูลอัตโนมัติ
            </TabsTrigger>
          </TabsList>

          <TabsContent value="list" className="mt-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-purple-700">รายการไฟล์สำรองข้อมูล</h3>
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => refetchBackupFiles()}
                  className="border-purple-300 text-purple-700 hover:bg-purple-50"
                >
                  <RefreshCw className="mr-2 h-4 w-4" />
                  รีเฟรช
                </Button>
                <Button 
                  onClick={() => createBackupMutation.mutate()}
                  disabled={isCreatingBackup}
                  className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white"
                >
                  {isCreatingBackup ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" /> กำลังสร้าง...
                    </>
                  ) : (
                    <>
                      <Database className="mr-2 h-4 w-4" /> สร้างไฟล์สำรองข้อมูลใหม่
                    </>
                  )}
                </Button>
              </div>
            </div>

            {isLoadingBackupFiles ? (
              <div className="flex items-center justify-center p-8">
                <Loader2 className="h-8 w-8 animate-spin text-purple-600" />
              </div>
            ) : isErrorBackupFiles ? (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>เกิดข้อผิดพลาด</AlertTitle>
                <AlertDescription>
                  ไม่สามารถดึงรายการไฟล์สำรองข้อมูลได้ กรุณาลองใหม่อีกครั้ง
                </AlertDescription>
              </Alert>
            ) : backupFiles?.data && backupFiles.data.length > 0 ? (
              <div className="border rounded-md overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>ชื่อไฟล์</TableHead>
                      <TableHead>ขนาด</TableHead>
                      <TableHead>วันที่สร้าง</TableHead>
                      <TableHead className="text-right">จัดการ</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {backupFiles.data.map((file) => (
                      <TableRow key={file.fileName} className="hover:bg-purple-50/50">
                        <TableCell className="font-medium">{file.fileName}</TableCell>
                        <TableCell>{file.fileSize}</TableCell>
                        <TableCell>{formatDate(file.createdAt)}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button 
                              variant="outline" 
                              size="sm" 
                              onClick={() => handleDownloadBackup(file.fileName)}
                              className="h-8 border-blue-300 text-blue-700 hover:bg-blue-50"
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                            <Button 
                              variant="outline" 
                              size="sm" 
                              onClick={() => handleDeleteBackup(file.fileName)}
                              className="h-8 border-red-300 text-red-700 hover:bg-red-50"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center p-8 border border-dashed rounded-md">
                <Database className="h-12 w-12 text-purple-300 mb-2" />
                <p className="text-purple-600">ยังไม่มีไฟล์สำรองข้อมูล</p>
                <Button 
                  onClick={() => createBackupMutation.mutate()}
                  disabled={isCreatingBackup}
                  className="mt-4 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white"
                >
                  {isCreatingBackup ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" /> กำลังสร้าง...
                    </>
                  ) : (
                    <>
                      <Database className="mr-2 h-4 w-4" /> สร้างไฟล์สำรองข้อมูลใหม่
                    </>
                  )}
                </Button>
              </div>
            )}
          </TabsContent>

          <TabsContent value="settings" className="mt-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <h3 className="text-lg font-medium text-purple-700">ตั้งค่าการสำรองข้อมูลอัตโนมัติ</h3>
                  <p className="text-sm text-purple-600/70">
                    กำหนดความถี่และเวลาในการสำรองข้อมูลอัตโนมัติ
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="auto-backup"
                    checked={backupSettings.enabled}
                    onCheckedChange={(checked) => setBackupSettings({ ...backupSettings, enabled: checked })}
                  />
                  <Label htmlFor="auto-backup" className="font-medium">
                    {backupSettings.enabled ? 'เปิดใช้งาน' : 'ปิดใช้งาน'}
                  </Label>
                </div>
              </div>

              {backupSettings.enabled && (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="backup-frequency">ความถี่</Label>
                      <Select
                        value={backupSettings.frequency}
                        onValueChange={(value: 'daily' | 'weekly' | 'monthly') => 
                          setBackupSettings({ ...backupSettings, frequency: value })
                        }
                      >
                        <SelectTrigger id="backup-frequency">
                          <SelectValue placeholder="เลือกความถี่" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="daily">รายวัน</SelectItem>
                          <SelectItem value="weekly">รายสัปดาห์ (ทุกวันอาทิตย์)</SelectItem>
                          <SelectItem value="monthly">รายเดือน (วันที่ 1)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="backup-time">เวลา</Label>
                      <Input
                        id="backup-time"
                        type="time"
                        value={backupSettings.time}
                        onChange={(e) => setBackupSettings({ ...backupSettings, time: e.target.value })}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="keep-count">จำนวนไฟล์สำรองข้อมูลที่เก็บไว้</Label>
                    <Input
                      id="keep-count"
                      type="number"
                      min="1"
                      max="30"
                      value={backupSettings.keepCount}
                      onChange={(e) => setBackupSettings({ ...backupSettings, keepCount: parseInt(e.target.value) })}
                    />
                    <p className="text-sm text-muted-foreground">
                      ระบบจะเก็บไฟล์สำรองข้อมูลล่าสุดตามจำนวนที่กำหนด และลบไฟล์เก่าที่เกินจำนวนโดยอัตโนมัติ
                    </p>
                  </div>

                  {backupSettings.lastBackup && (
                    <div className="p-4 border rounded-md bg-purple-50/50">
                      <h4 className="font-medium text-purple-700 flex items-center mb-2">
                        <Calendar className="mr-2 h-4 w-4" />
                        ข้อมูลการสำรองล่าสุด
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-purple-600/70">สำรองข้อมูลล่าสุด:</p>
                          <p className="font-medium">{formatDate(backupSettings.lastBackup)}</p>
                        </div>
                        {backupSettings.nextBackup && (
                          <div>
                            <p className="text-sm text-purple-600/70">สำรองข้อมูลครั้งถัดไป:</p>
                            <p className="font-medium">{formatDate(backupSettings.nextBackup)}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </>
              )}

              <Button 
                onClick={handleSaveBackupSettings}
                disabled={updateBackupSettingsMutation.isPending}
                className="mt-4 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white"
              >
                {updateBackupSettingsMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" /> กำลังบันทึก...
                  </>
                ) : (
                  <>
                    <Check className="mr-2 h-4 w-4" /> บันทึกการตั้งค่า
                  </>
                )}
              </Button>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={() => setOpen(false)}
            className="border-purple-300 text-purple-700 hover:bg-purple-50"
          >
            ปิด
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
