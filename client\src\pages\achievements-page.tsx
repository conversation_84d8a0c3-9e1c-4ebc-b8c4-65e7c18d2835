import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Medal, Trophy, Star, Check, Lock, Sparkles, Zap } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

// คอมโพเนนต์แสดงรายละเอียดความสำเร็จ
const AchievementCard = ({ 
  achievement, 
  progress = 0, 
  completed = false, 
  onComplete 
}: { 
  achievement: any, 
  progress?: number, 
  completed?: boolean, 
  onComplete?: () => void 
}) => {
  const progressPercent = Math.min(100, Math.round((progress / achievement.requirement) * 100));
  
  return (
    <Card className={`overflow-hidden transition-all duration-300 ${completed ? 'border-2 border-primary shadow-lg' : ''}`}>
      <div 
        className={`h-1.5 ${completed ? 'bg-gradient-to-r from-purple-400 via-blue-500 to-green-400' : 'bg-muted'}`} 
      />
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="flex items-center gap-2">
              {completed ? (
                <Sparkles className="h-5 w-5 text-yellow-500" />
              ) : (
                <Badge variant="outline" className="h-5 px-1 py-0 text-xs">
                  {achievement.type}
                </Badge>
              )}
              {achievement.name}
            </CardTitle>
            <CardDescription className="mt-1">{achievement.description}</CardDescription>
          </div>
          <div>
            {completed ? (
              <div className="flex items-center">
                <Badge className="bg-gradient-to-r from-green-400 to-blue-500 hover:from-blue-500 hover:to-green-400">
                  <Check className="h-3 w-3 mr-1" /> สำเร็จแล้ว
                </Badge>
              </div>
            ) : (
              <Badge variant="outline" className="text-sm">
                ระดับ {achievement.level}
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="mb-1 flex justify-between text-sm text-muted-foreground">
          <span>ความก้าวหน้า</span>
          <span>{progress} / {achievement.requirement}</span>
        </div>
        <Progress 
          value={progressPercent} 
          className={`h-2 ${completed ? 'bg-muted' : ''}`}
        />
      </CardContent>
      <CardFooter className="pt-0 flex justify-between">
        <div className="flex items-center gap-2">
          <div className={`rounded-full p-1 ${completed ? 'bg-green-100 text-green-500' : 'bg-amber-100 text-amber-500'}`}>
            {completed ? <Trophy className="h-4 w-4" /> : <Trophy className="h-4 w-4" />}
          </div>
          <span className="text-sm text-muted-foreground">
            {achievement.points} คะแนน
          </span>
        </div>
        
        {!completed && progress >= achievement.requirement && onComplete && (
          <Button 
            size="sm" 
            onClick={onComplete}
            className="text-xs h-8 rounded-full px-3 bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600"
          >
            <Star className="h-3.5 w-3.5 mr-1" /> รับรางวัล
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

// หน้าแสดงความสำเร็จทั้งหมด
const AchievementsPage = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("all");
  const [displayAchievements, setDisplayAchievements] = useState<any[]>([]);
  
  // ดึงข้อมูลความสำเร็จของผู้ใช้
  const { data: achievements = [], isLoading, error, refetch } = useQuery<any[]>({
    queryKey: ['/api/achievements'],
    retry: 1,
  });
  
  // ดึงข้อมูลแพ็กเกจที่ผู้ใช้ใช้งานอยู่
  const { data: activePackage } = useQuery<any>({
    queryKey: ['/api/user/active-package'],
    retry: 1,
  });
  
  useEffect(() => {
    if (achievements && achievements.length > 0) {
      filterAchievements(activeTab);
    }
  }, [achievements, activeTab]);
  
  // กรองความสำเร็จตามประเภท
  const filterAchievements = (tabKey: string) => {
    if (!achievements || achievements.length === 0) return;
    
    if (tabKey === "all") {
      setDisplayAchievements(achievements);
    } else if (tabKey === "active-package" && activePackage) {
      setDisplayAchievements(
        achievements.filter((a: any) => a.achievement.packageId === activePackage.packageId)
      );
    } else if (tabKey === "completed") {
      setDisplayAchievements(
        achievements.filter((a: any) => a.completed)
      );
    } else if (tabKey === "in-progress") {
      setDisplayAchievements(
        achievements.filter((a: any) => !a.completed && a.progress > 0)
      );
    } else if (tabKey === "locked") {
      setDisplayAchievements(
        achievements.filter((a: any) => !a.completed && a.progress === 0)
      );
    }
  };
  
  // เมื่อกดปุ่มรับรางวัล
  const handleComplete = async (achievementId: number) => {
    try {
      const response = await fetch(`/api/achievements/${achievementId}/complete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (response.ok) {
        toast({
          title: "🌟 รับรางวัลสำเร็จ!",
          description: "คุณได้รับความสำเร็จใหม่และได้รับคะแนนพิเศษ!",
          variant: "default",
        });
        
        // ดึงข้อมูลใหม่
        refetch();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'เกิดข้อผิดพลาดในการรับรางวัล');
      }
    } catch (error) {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error instanceof Error ? error.message : 'ไม่สามารถรับรางวัลได้',
        variant: "destructive",
      });
    }
  };
  
  if (isLoading) {
    return (
      <div className="container py-10">
        <div className="flex justify-center items-center min-h-[50vh]">
          <div className="animate-spin w-12 h-12 border-4 border-primary border-t-transparent rounded-full"></div>
        </div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="container py-10">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">เกิดข้อผิดพลาด</h1>
          <p className="text-muted-foreground mb-6">ไม่สามารถโหลดข้อมูลความสำเร็จได้</p>
          <Button onClick={() => refetch()}>ลองใหม่</Button>
        </div>
      </div>
    );
  }
  
  const completedCount = achievements?.filter((a: any) => a.completed)?.length || 0;
  const totalCount = achievements?.length || 0;
  const completionPercent = totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0;
  
  return (
    <div className="container py-6 md:py-10">
      <div className="flex flex-col gap-y-8">
        {/* ส่วนหัว */}
        <div className="grid grid-cols-1 md:grid-cols-12 gap-6">
          <div className="md:col-span-8">
            <h1 className="text-3xl font-bold mb-2 bg-gradient-to-r from-primary to-purple-600 text-transparent bg-clip-text">
              ความสำเร็จของคุณ
            </h1>
            <p className="text-muted-foreground">
              เก็บรวบรวมความสำเร็จและปลดล็อครางวัลพิเศษเมื่อคุณใช้งานแพ็กเกจต่างๆ ของ SLIPKUY
            </p>
          </div>
          
          <Card className="md:col-span-4 flex flex-row items-center p-4 bg-gradient-to-br from-purple-900/10 to-indigo-900/10 border-purple-200/20">
            <div className="mr-4">
              <div className="relative h-16 w-16 flex items-center justify-center">
                <svg className="h-16 w-16" viewBox="0 0 100 100">
                  <circle
                    className="text-muted stroke-current"
                    strokeWidth="6"
                    cx="50"
                    cy="50"
                    r="40"
                    fill="transparent"
                  />
                  <circle
                    className="text-blue-500 stroke-current"
                    strokeWidth="6"
                    strokeLinecap="round"
                    strokeDasharray={`${completionPercent * 2.51}, 251.2`}
                    strokeDashoffset="0"
                    cx="50"
                    cy="50"
                    r="40"
                    fill="transparent"
                    transform="rotate(-90 50 50)"
                  />
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <Medal className="h-8 w-8 text-blue-500" />
                </div>
              </div>
            </div>
            <div>
              <h3 className="font-medium">ความก้าวหน้ารวม</h3>
              <div className="text-2xl font-bold">{completionPercent}%</div>
              <div className="text-xs text-muted-foreground">
                {completedCount} จาก {totalCount} ความสำเร็จ
              </div>
            </div>
          </Card>
        </div>
        
        {/* แท็บความสำเร็จ */}
        <Tabs defaultValue="all" onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="all" className="min-w-[100px]">
              ทั้งหมด
            </TabsTrigger>
            {activePackage && (
              <TabsTrigger value="active-package" className="min-w-[100px]">
                แพ็กเกจปัจจุบัน
              </TabsTrigger>
            )}
            <TabsTrigger value="completed" className="min-w-[100px]">
              <Check className="h-4 w-4 mr-1" /> สำเร็จแล้ว
            </TabsTrigger>
            <TabsTrigger value="in-progress" className="min-w-[100px]">
              <Zap className="h-4 w-4 mr-1" /> กำลังทำ
            </TabsTrigger>
            <TabsTrigger value="locked" className="min-w-[100px]">
              <Lock className="h-4 w-4 mr-1" /> ยังไม่ปลดล็อค
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value={activeTab}>
            {displayAchievements?.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {displayAchievements.map((item: any) => (
                  <AchievementCard
                    key={item.achievementId}
                    achievement={item.achievement}
                    progress={item.progress}
                    completed={item.completed}
                    onComplete={() => handleComplete(item.achievementId)}
                  />
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-12 px-4 text-center">
                <Lock className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">ไม่พบความสำเร็จในหมวดนี้</h3>
                <p className="text-muted-foreground max-w-md">
                  ใช้งานแพ็กเกจต่างๆ ของ SLIPKUY เพื่อปลดล็อคความสำเร็จพิเศษและรับรางวัล
                </p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default AchievementsPage;