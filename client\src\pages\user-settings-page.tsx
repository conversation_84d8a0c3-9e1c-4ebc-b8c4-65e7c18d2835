import { useState } from "react";
import { Helmet } from "react-helmet-async";
import { DashboardLayout } from "@/components/layouts/dashboard-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { useAuth } from "@/hooks/use-auth";
import { useTheme } from "@/hooks/use-theme";
import { Settings, UserCog, Bell, Moon, Sun, ShieldCheck, Globe } from "lucide-react";
import DuplicateSlipToggle from "@/components/duplicate-slip-toggle";
import { motion } from "framer-motion";

export default function UserSettingsPage() {
  const { user } = useAuth();
  const { theme, toggleTheme, isDarkMode } = useTheme();
  const [activeTab, setActiveTab] = useState("general");

  // อนิเมชันสำหรับหัวข้อแบบภาษาเทพเจ้า
  const titleAnimation = {
    initial: { opacity: 0, y: -20 },
    animate: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    }
  };

  // อนิเมชันสำหรับการ์ด
  const cardAnimation = {
    initial: { opacity: 0, scale: 0.9 },
    animate: { 
      opacity: 1, 
      scale: 1,
      transition: {
        duration: 0.4,
        ease: "easeOut"
      }
    }
  };

  return (
    <DashboardLayout>
      <Helmet>
        <title>การตั้งค่าส่วนตัว | SLIPKUY</title>
      </Helmet>

      <div className="container mx-auto p-4 md:p-6">
        {/* หัวข้อหน้า */}
        <motion.div 
          className="mb-6 flex items-center"
          initial={titleAnimation.initial}
          animate={titleAnimation.animate}
        >
          <div className="mr-3 p-2 rounded-full bg-gradient-to-br from-indigo-900 to-purple-900">
            <UserCog className="h-8 w-8 text-amber-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-amber-200 to-amber-100 text-transparent bg-clip-text">
              การตั้งค่าส่วนตัว
            </h1>
            <p className="text-indigo-300 text-sm">
              ปรับแต่งการทำงานของระบบตามต้องการ
            </p>
          </div>
        </motion.div>

        {/* แท็บการตั้งค่า */}
        <Tabs 
          defaultValue="general" 
          value={activeTab} 
          onValueChange={setActiveTab}
          className="space-y-4"
        >
          <TabsList className="grid grid-cols-3 md:grid-cols-5 lg:w-[600px] bg-indigo-950/50 p-1 rounded-lg border border-indigo-800/30">
            <TabsTrigger 
              value="general" 
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-800 data-[state=active]:to-indigo-700 data-[state=active]:text-white"
            >
              <Settings className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">ทั่วไป</span>
            </TabsTrigger>
            <TabsTrigger 
              value="security" 
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-800 data-[state=active]:to-indigo-700 data-[state=active]:text-white"
            >
              <ShieldCheck className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">ความปลอดภัย</span>
            </TabsTrigger>
            <TabsTrigger 
              value="notifications" 
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-800 data-[state=active]:to-indigo-700 data-[state=active]:text-white"
            >
              <Bell className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">การแจ้งเตือน</span>
            </TabsTrigger>
            <TabsTrigger 
              value="appearance" 
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-800 data-[state=active]:to-indigo-700 data-[state=active]:text-white"
            >
              {isDarkMode ? (
                <Moon className="h-4 w-4 mr-2" />
              ) : (
                <Sun className="h-4 w-4 mr-2" />
              )}
              <span className="hidden sm:inline">การแสดงผล</span>
            </TabsTrigger>
            <TabsTrigger 
              value="language" 
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-800 data-[state=active]:to-indigo-700 data-[state=active]:text-white"
            >
              <Globe className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">ภาษา</span>
            </TabsTrigger>
          </TabsList>

          {/* เนื้อหาแท็บ "ทั่วไป" */}
          <TabsContent value="general">
            <motion.div 
              className="grid gap-4 md:grid-cols-2"
              initial="initial"
              animate="animate"
              variants={{
                animate: {
                  transition: {
                    staggerChildren: 0.1
                  }
                }
              }}
            >
              <motion.div variants={cardAnimation}>
                <Card className="border border-indigo-800/30 bg-indigo-950/20 backdrop-blur-md">
                  <CardHeader>
                    <CardTitle className="text-xl bg-gradient-to-r from-indigo-200 to-indigo-100 text-transparent bg-clip-text">
                      ข้อมูลบัญชี
                    </CardTitle>
                    <CardDescription>
                      ข้อมูลพื้นฐานของบัญชีผู้ใช้
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <label className="text-sm text-indigo-300">ชื่อผู้ใช้</label>
                        <div className="mt-1 p-2 bg-indigo-900/30 border border-indigo-800/40 rounded-md text-indigo-100">
                          {user?.username || 'ไม่พบข้อมูล'}
                        </div>
                      </div>
                      <div>
                        <label className="text-sm text-indigo-300">อีเมล</label>
                        <div className="mt-1 p-2 bg-indigo-900/30 border border-indigo-800/40 rounded-md text-indigo-100">
                          {user?.email || 'ไม่พบข้อมูล'}
                        </div>
                      </div>
                      <div>
                        <label className="text-sm text-indigo-300">ระดับผู้ใช้</label>
                        <div className="mt-1 p-2 bg-indigo-900/30 border border-indigo-800/40 rounded-md text-indigo-100">
                          {user?.role === 'admin' ? 'ผู้ดูแลระบบ' : 'ผู้ใช้ทั่วไป'}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
              
              <motion.div variants={cardAnimation}>
                <Card className="border border-indigo-800/30 bg-indigo-950/20 backdrop-blur-md">
                  <CardHeader>
                    <CardTitle className="text-xl bg-gradient-to-r from-indigo-200 to-indigo-100 text-transparent bg-clip-text">
                      ลิงก์การจัดการบัญชี
                    </CardTitle>
                    <CardDescription>
                      ตัวเลือกในการจัดการบัญชีของคุณ
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <a href="/profile" className="flex items-center p-2 bg-indigo-900/30 hover:bg-indigo-900/50 border border-indigo-800/40 rounded-md text-indigo-100 transition-colors">
                        <UserCog className="h-5 w-5 mr-2 text-amber-400" />
                        แก้ไขข้อมูลส่วนตัว
                      </a>
                      <a href="/change-password" className="flex items-center p-2 bg-indigo-900/30 hover:bg-indigo-900/50 border border-indigo-800/40 rounded-md text-indigo-100 transition-colors">
                        <ShieldCheck className="h-5 w-5 mr-2 text-amber-400" />
                        เปลี่ยนรหัสผ่าน
                      </a>
                      <a href="/notifications" className="flex items-center p-2 bg-indigo-900/30 hover:bg-indigo-900/50 border border-indigo-800/40 rounded-md text-indigo-100 transition-colors">
                        <Bell className="h-5 w-5 mr-2 text-amber-400" />
                        จัดการการแจ้งเตือน
                      </a>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </motion.div>
          </TabsContent>

          {/* เนื้อหาแท็บ "ความปลอดภัย" */}
          <TabsContent value="security">
            <motion.div 
              className="grid gap-4 md:grid-cols-2"
              initial="initial"
              animate="animate"
              variants={{
                animate: {
                  transition: {
                    staggerChildren: 0.1
                  }
                }
              }}
            >
              <motion.div variants={cardAnimation} className="md:col-span-2">
                <DuplicateSlipToggle />
              </motion.div>
              
              {/* เพิ่มการตั้งค่าความปลอดภัยอื่นๆ ในอนาคต */}
            </motion.div>
          </TabsContent>

          {/* เนื้อหาแท็บอื่นๆ สามารถเพิ่มในอนาคต */}
          <TabsContent value="notifications">
            <motion.div 
              className="grid gap-4"
              initial="initial"
              animate="animate"
              variants={{
                animate: {
                  transition: {
                    staggerChildren: 0.1
                  }
                }
              }}
            >
              <motion.div variants={cardAnimation}>
                <Card className="border border-indigo-800/30 bg-indigo-950/20 backdrop-blur-md">
                  <CardHeader>
                    <CardTitle className="text-xl bg-gradient-to-r from-indigo-200 to-indigo-100 text-transparent bg-clip-text">
                      การแจ้งเตือน
                    </CardTitle>
                    <CardDescription>
                      การตั้งค่าการแจ้งเตือนจะมาในอนาคต
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="p-4 bg-indigo-900/30 border border-indigo-800/40 rounded-md text-indigo-100">
                      <p className="text-center text-indigo-300">
                        ฟีเจอร์นี้กำลังอยู่ระหว่างการพัฒนา
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </motion.div>
          </TabsContent>

          <TabsContent value="appearance">
            <motion.div 
              className="grid gap-4"
              initial="initial"
              animate="animate"
              variants={{
                animate: {
                  transition: {
                    staggerChildren: 0.1
                  }
                }
              }}
            >
              <motion.div variants={cardAnimation}>
                <Card className="border border-indigo-800/30 bg-indigo-950/20 backdrop-blur-md">
                  <CardHeader>
                    <CardTitle className="text-xl bg-gradient-to-r from-indigo-200 to-indigo-100 text-transparent bg-clip-text">
                      ธีม
                    </CardTitle>
                    <CardDescription>
                      การตั้งค่าธีมจะมาในอนาคต
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="p-4 bg-indigo-900/30 border border-indigo-800/40 rounded-md text-indigo-100">
                      <p className="text-center text-indigo-300">
                        ระบบใช้ธีมมืดแบบเทพเจ้าเป็นค่าเริ่มต้น
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </motion.div>
          </TabsContent>

          <TabsContent value="language">
            <motion.div 
              className="grid gap-4"
              initial="initial"
              animate="animate"
              variants={{
                animate: {
                  transition: {
                    staggerChildren: 0.1
                  }
                }
              }}
            >
              <motion.div variants={cardAnimation}>
                <Card className="border border-indigo-800/30 bg-indigo-950/20 backdrop-blur-md">
                  <CardHeader>
                    <CardTitle className="text-xl bg-gradient-to-r from-indigo-200 to-indigo-100 text-transparent bg-clip-text">
                      ภาษา
                    </CardTitle>
                    <CardDescription>
                      การตั้งค่าภาษาจะมาในอนาคต
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="p-4 bg-indigo-900/30 border border-indigo-800/40 rounded-md text-indigo-100">
                      <p className="text-center text-indigo-300">
                        ปัจจุบันรองรับเฉพาะภาษาไทยเท่านั้น
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </motion.div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}