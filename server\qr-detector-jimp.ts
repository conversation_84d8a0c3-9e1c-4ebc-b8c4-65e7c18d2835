// ประกาศ module เพื่อแก้ปัญหา TypeScript
declare module 'qrcode-reader';
declare module 'jimp';

// ใช้ require แทนเพื่อแก้ปัญหาการ import 
const Jimp = require('jimp');
const QrCode = require('qrcode-reader');

/**
 * ตรวจสอบ QR Code จากรูปภาพโดยใช้ jimp และ qrcode-reader
 * 
 * @param imageBuffer Buffer ของรูปภาพที่ต้องการตรวจสอบ
 * @returns Promise<{ hasQRCode: boolean; qrData?: string }> ผลการตรวจสอบ QR Code
 */
export async function detectQRCode(imageBuffer: Buffer): Promise<{ hasQRCode: boolean; qrData?: string }> {
  try {
    // ตรวจสอบว่าเป็นไฟล์รูปภาพที่ถูกต้องหรือไม่
    const isJPEG = imageBuffer.slice(0, 3).toString('hex') === 'ffd8ff';
    const isPNG = imageBuffer.slice(0, 8).toString('hex') === '89504e470d0a1a0a';
    
    if (!isJPEG && !isPNG) {
      console.log('Not a valid image file (JPEG/PNG)');
      return { hasQRCode: false };
    }
    
    // ถ้าขนาดไฟล์ไม่เหมาะสม
    if (imageBuffer.length < 1000 || imageBuffer.length > 10 * 1024 * 1024) {
      console.log('File size invalid, too small or too large');
      return { hasQRCode: false };
    }

    // อ่านรูปภาพด้วย Jimp
    const image = await Jimp.read(imageBuffer);
    
    // สร้าง QR code reader
    const qr = new QrCode();
    
    // สร้าง Promise เพื่อรองรับการอ่าน QR code แบบ callback
    return new Promise((resolve) => {
      qr.callback = (err: Error | null, value: { result: string } | null) => {
        if (err) {
          console.error('Error reading QR code:', err);
          resolve({ hasQRCode: false });
          return;
        }
        
        if (!value || !value.result) {
          console.log('No QR code found in image');
          resolve({ hasQRCode: false });
          return;
        }
        
        console.log('Found QR code in image with data:', value.result);
        resolve({
          hasQRCode: true,
          qrData: value.result
        });
      };
      
      // ส่งรูปภาพเข้าไปให้ qrcode-reader ตรวจสอบ
      qr.decode(image.bitmap);
    });
  } catch (error) {
    console.error('Error in detectQRCode:', error);
    return { hasQRCode: false };
  }
}

/**
 * ตรวจสอบว่ารูปภาพมี QR Code หรือไม่
 * 
 * @param imageBuffer Buffer ของรูปภาพที่ต้องการตรวจสอบ
 * @returns Promise<boolean> true ถ้าพบ QR Code, false ถ้าไม่พบ
 */
export async function hasQRCode(imageBuffer: Buffer): Promise<boolean> {
  const result = await detectQRCode(imageBuffer);
  return result.hasQRCode;
}