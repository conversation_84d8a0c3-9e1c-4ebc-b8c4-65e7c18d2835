import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { PackageCard } from "@/components/packages/package-card";
import { Skeleton } from "@/components/ui/skeleton";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Crown, Sparkles, Star, Shield, Zap, Percent, Check, ArrowRight, ShieldCheck, Bell, Activity } from "lucide-react";
import { motion } from "framer-motion";
import { useAuth } from "@/hooks/use-auth";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { useLocation } from "wouter";
import { calculatePriceWithDiscount } from "@/lib/price-utils";

// Interface สำหรับข้อมูลแพ็กเกจ 
interface Package {
  id: number;
  name: string;
  description: string;
  price: number;
  requestsLimit: number;
  features: string | null;
  discount3Months: number | null;
  discount6Months: number | null;
  discount12Months: number | null;
  tag?: string;
  creditPerVerification: number;
}

// พื้นหลังสไตล์เทพเจ้า
const CelestialBackground = () => (
  <div className="absolute inset-0 overflow-hidden">
    <div className="absolute inset-0 bg-gradient-to-b from-indigo-900/20 via-transparent to-transparent"></div>
    <div className="absolute top-0 left-0 w-full h-96 bg-gradient-radial from-indigo-600/10 via-transparent to-transparent"></div>
    <div className="absolute -top-40 -right-40 w-96 h-96 bg-indigo-600/5 rounded-full blur-3xl"></div>
    <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-purple-600/5 rounded-full blur-3xl"></div>
    
    {/* เพิ่มแสงของดาวเทพเจ้าที่เคลื่อนไหว */}
    <div className="absolute top-1/4 right-1/4 w-40 h-40 bg-indigo-400/5 rounded-full blur-3xl cosmic-pulse"></div>
    <div className="absolute bottom-1/4 left-1/3 w-32 h-32 bg-purple-500/5 rounded-full blur-3xl cosmic-pulse-slow"></div>
    
    {/* เพิ่มประกายรัศมีที่สว่างวาบ */}
    <div className="absolute top-1/3 left-1/5 w-2 h-2 bg-yellow-300/30 rounded-full divine-flash"></div>
    <div className="absolute bottom-1/3 right-1/5 w-1 h-1 bg-blue-300/30 rounded-full divine-flash" style={{ animationDelay: '1.5s' }}></div>
    <div className="absolute top-2/3 left-1/2 w-1.5 h-1.5 bg-purple-300/30 rounded-full divine-flash" style={{ animationDelay: '0.7s' }}></div>
    
    <style dangerouslySetInnerHTML={{ __html: `
      @keyframes floatingStar {
        0% { transform: translate(0, 0) rotate(0deg); }
        50% { transform: translate(10px, -10px) rotate(5deg); }
        100% { transform: translate(0, 0) rotate(0deg); }
      }
      .floating-star {
        animation: floatingStar 8s ease-in-out infinite;
      }
      
      @keyframes cosmicPulse {
        0% { opacity: 0.3; transform: scale(0.9); }
        50% { opacity: 0.8; transform: scale(1.1); }
        100% { opacity: 0.3; transform: scale(0.9); }
      }
      .cosmic-pulse {
        animation: cosmicPulse 6s ease-in-out infinite;
      }
      .cosmic-pulse-slow {
        animation: cosmicPulse 9s ease-in-out infinite;
      }
      
      @keyframes divineFlash {
        0% { opacity: 0; transform: scale(0); }
        20% { opacity: 1; transform: scale(1.2); }
        40% { opacity: 0.5; transform: scale(0.8); }
        60% { opacity: 0.8; transform: scale(1.1); }
        80% { opacity: 0.2; transform: scale(0.9); }
        100% { opacity: 0; transform: scale(0); }
      }
      .divine-flash {
        animation: divineFlash 4s ease-out infinite;
      }
      
      @keyframes glowPulse {
        0% { box-shadow: 0 0 5px 1px rgba(139, 92, 246, 0.3); }
        50% { box-shadow: 0 0 15px 5px rgba(139, 92, 246, 0.6); }
        100% { box-shadow: 0 0 5px 1px rgba(139, 92, 246, 0.3); }
      }
      
      .tab-active-glow {
        animation: glowPulse 3s ease-in-out infinite;
      }
      
      .glow-gold {
        animation: glowGold 2s ease-in-out infinite;
      }
      
      @keyframes glowGold {
        0% { box-shadow: 0 0 5px 1px rgba(245, 158, 11, 0.3); }
        50% { box-shadow: 0 0 15px 5px rgba(245, 158, 11, 0.5); }
        100% { box-shadow: 0 0 5px 1px rgba(245, 158, 11, 0.3); }
      }
      
      .animate-pulse-gentle {
        animation: pulse-gentle 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
      }
      
      @keyframes pulse-gentle {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.8; }
      }
    `}} />
    <div className="absolute top-20 right-20 text-amber-300 opacity-20 floating-star">
      <Star className="h-16 w-16" />
    </div>
    <div className="absolute bottom-20 left-40 text-purple-300 opacity-10 floating-star" style={{ animationDelay: "-2s" }}>
      <Star className="h-24 w-24" />
    </div>
  </div>
);

export default function PackagesPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [, setLocation] = useLocation();
  const [selectedDuration, setSelectedDuration] = useState(1);

  // ดึงข้อมูลแพ็กเกจ
  const { data: packages, isLoading } = useQuery<Package[]>({
    queryKey: ["/api/packages"],
  });
  
  // คำนวณค่าเฉลี่ยของเปอร์เซ็นต์ส่วนลดจากแพ็กเกจทั้งหมด
  const getAverageDiscountPercent = (months: number): number => {
    if (!packages || packages.length === 0) return 0;
    
    let totalDiscount = 0;
    let count = 0;
    
    packages.forEach(pkg => {
      if (months === 3 && pkg.discount3Months) {
        totalDiscount += pkg.discount3Months;
        count++;
      } else if (months === 6 && pkg.discount6Months) {
        totalDiscount += pkg.discount6Months;
        count++;
      } else if (months === 12 && pkg.discount12Months) {
        totalDiscount += pkg.discount12Months;
        count++;
      }
    });
    
    return count > 0 ? Math.round(totalDiscount / count) : 0;
  };

  // ดึงข้อมูล active package ของผู้ใช้
  const { data: activePackage } = useQuery<any>({
    queryKey: ["/api/user/active-package"],
    enabled: !!user,
  });

  // นำเอาข้อความคุณสมบัติมาเป็นออบเจ็กต์
  const parseFeatures = (
    features: string | null | undefined
  ): { name: string; included: boolean }[] => {
    if (!features) return [];
    
    const featureArray = typeof features === "string" ? 
      features.split(",").map(f => f.trim()) : 
      Array.isArray(features) ? features : [];

    return featureArray.map((feature) => {
      const included = !feature.startsWith("!");
      const name = included ? feature : feature.substring(1);
      return { name, included };
    });
  };

  // จัดการแท็บเลือกระยะเวลาแพ็กเกจ
  const handleTabChange = (value: string) => {
    console.log("เลือกแท็บ:", value);
    if (value === "monthly") {
      setSelectedDuration(1);
    } else if (value === "3months") {
      setSelectedDuration(3);
    } else if (value === "6months") {
      setSelectedDuration(6);
    } else if (value === "yearly") {
      setSelectedDuration(12);
    }
  };

  // ไปยังหน้า Login เมื่อต้องการสมัครแต่ยังไม่ได้ล็อกอิน
  const handleNotLoggedInSubscribe = () => {
    toast({
      title: "กรุณาล็อกอินก่อน",
      description: "คุณต้องล็อกอินเพื่อสมัครแพ็กเกจ",
    });
    
    setTimeout(() => {
      setLocation("/auth");
    }, 1500);
  };

  // แจ้งเตือนเมื่อสมัครสำเร็จ
  const handleSubscriptionSuccess = () => {
    toast({
      title: "สมัครแพ็กเกจสำเร็จ",
      description: "แพ็กเกจของคุณได้ถูกอัปเดตแล้ว",
    });
    setTimeout(() => {
      setLocation("/dashboard");
    }, 1500);
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 py-8 px-4 sm:px-6 lg:px-8 relative">
      <CelestialBackground />
      
      <div className="relative z-10 max-w-7xl mx-auto pt-20">
        {/* หัวข้อและคำอธิบาย */}
        <div className="text-center mb-12">
          <motion.div
            className="inline-block mb-3"
            initial={{ scale: 0.5, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="bg-gradient-to-r from-indigo-600/20 to-purple-600/20 rounded-full p-3 inline-block border border-indigo-500/30">
              <Crown className="h-10 w-10 text-amber-400" />
            </div>
          </motion.div>
          
          <motion.h1 
            className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-amber-300 to-yellow-200 mb-4"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            แพ็กเกจสรรค์สร้างโดยเทพเจ้า
          </motion.h1>
          
          <motion.p
            className="text-indigo-200 max-w-3xl mx-auto text-lg mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            เพิ่มพลังให้ธุรกิจของคุณด้วยระบบตรวจสอบสลิปที่แม่นยำและเชื่อถือได้ ด้วยการันตีความถูกต้องสูงสุด
          </motion.p>
          
          <div className="flex justify-center mb-6 space-x-4">
            <motion.div
              whileHover={{ y: -2 }}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: 0.3 }}
              className="flex items-center text-sm font-medium text-indigo-100 bg-indigo-800/30 rounded-full py-1.5 px-4 border border-indigo-700/40"
            >
              <Shield className="h-4 w-4 text-green-400 mr-2" />
              <span>การันตีความแม่นยำสูงสุด 99.9%</span>
            </motion.div>
            <motion.div
              whileHover={{ y: -2 }}
              initial={{ opacity: 0, x: 0 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: 0.4 }}
              className="flex items-center text-sm font-medium text-indigo-100 bg-indigo-800/30 rounded-full py-1.5 px-4 border border-indigo-700/40"
            >
              <Zap className="h-4 w-4 text-amber-400 mr-2" />
              <span>เริ่มใช้งานทันทีหลังจากสมัคร</span>
            </motion.div>
            <motion.div
              whileHover={{ y: -2 }}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: 0.5 }}
              className="flex items-center text-sm font-medium text-indigo-100 bg-indigo-800/30 rounded-full py-1.5 px-4 border border-indigo-700/40"
            >
              <Star className="h-4 w-4 text-purple-400 mr-2" />
              <span>ปลอดภัยด้วยเทคโนโลยีระดับสูงสุด</span>
            </motion.div>
          </div>
        </div>
        
        {/* แท็บเลือกแพ็กเกจและระยะเวลา */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="mb-16"
        >
          <Tabs defaultValue="monthly" onValueChange={handleTabChange}>
            <TabsList className="relative w-[600px] max-w-full h-16 grid grid-cols-4 mx-auto bg-gradient-to-b from-indigo-900/90 to-indigo-950/90 backdrop-blur-sm border border-indigo-700/30 p-1 rounded-xl overflow-hidden shadow-xl">
              {/* พื้นหลังแสงและเงาแบบมืออาชีพ */}
              <div className="absolute inset-0 overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-900/80 to-purple-950/90 z-0"></div>
                <div className="absolute bottom-0 left-0 right-0 h-1/3 bg-gradient-to-t from-indigo-500/10 to-transparent"></div>
                
                {/* เส้นแสงด้านบนสไตล์ไฮเทค */}
                <div className="absolute top-0 left-0 right-0 h-[1px] bg-indigo-400/30"></div>
                
                {/* แสงออร่าด้านล่าง */}
                <div className="absolute -bottom-10 left-1/4 right-1/4 h-16 bg-violet-500/20 blur-2xl rounded-full"></div>
              </div>
              
              {/* แท็บทริกเกอร์ 1 เดือน */}
              <TabsTrigger
                value="monthly"
                className="relative z-10 mx-1 rounded-lg h-full flex items-center justify-center 
                  transition-all duration-300 
                  data-[state=active]:shadow-lg data-[state=active]:shadow-blue-400/20
                  data-[state=active]:bg-gradient-to-b data-[state=active]:from-blue-500 data-[state=active]:to-blue-700
                  data-[state=active]:border data-[state=active]:border-blue-400/40
                  data-[state=active]:text-white
                  data-[state=inactive]:text-indigo-200 data-[state=inactive]:hover:text-white
                  data-[state=inactive]:hover:bg-indigo-700/50"
              >
                <div className="absolute inset-0 rounded-lg overflow-hidden">
                  {/* เอฟเฟกต์เส้นแสงเคลื่อนไหวเมื่อ active */}
                  <div className="absolute inset-0 opacity-0 data-[state=active]:opacity-100">
                    <div className="absolute top-0 left-0 right-0 h-[1px] bg-blue-300/70"></div>
                    <div className="absolute bottom-0 left-0 right-0 h-[1px] bg-blue-300/30"></div>
                    <div className="absolute top-0 bottom-0 left-0 w-[1px] bg-blue-300/30"></div>
                    <div className="absolute top-0 bottom-0 right-0 w-[1px] bg-blue-300/30"></div>
                  </div>
                </div>
                
                <div className="relative flex flex-col items-center px-3 z-10">
                  <span className="text-sm font-medium">1 เดือน</span>
                  <div className="text-xs text-blue-300/90 mt-0.5">
                    <span>ราคาปกติ</span>
                  </div>
                </div>
              </TabsTrigger>
              
              {/* แท็บทริกเกอร์ 3 เดือน */}
              <TabsTrigger
                value="3months"
                className="relative z-10 mx-1 rounded-lg h-full flex items-center justify-center 
                  transition-all duration-300 
                  data-[state=active]:shadow-lg data-[state=active]:shadow-indigo-400/20
                  data-[state=active]:bg-gradient-to-b data-[state=active]:from-indigo-500 data-[state=active]:to-indigo-700
                  data-[state=active]:border data-[state=active]:border-indigo-400/40
                  data-[state=active]:text-white
                  data-[state=inactive]:text-indigo-200 data-[state=inactive]:hover:text-white
                  data-[state=inactive]:hover:bg-indigo-700/50"
              >
                <div className="absolute inset-0 rounded-lg overflow-hidden">
                  {/* เอฟเฟกต์เส้นแสงเคลื่อนไหวเมื่อ active */}
                  <div className="absolute inset-0 opacity-0 data-[state=active]:opacity-100">
                    <div className="absolute top-0 left-0 right-0 h-[1px] bg-indigo-300/70"></div>
                    <div className="absolute bottom-0 left-0 right-0 h-[1px] bg-indigo-300/30"></div>
                    <div className="absolute top-0 bottom-0 left-0 w-[1px] bg-indigo-300/30"></div>
                    <div className="absolute top-0 bottom-0 right-0 w-[1px] bg-indigo-300/30"></div>
                  </div>
                </div>
                
                <div className="relative flex flex-col items-center px-3 z-10">
                  <span className="text-sm font-medium">3 เดือน</span>
                  <div className="text-xs text-indigo-300/90 mt-0.5 flex items-center">
                    <Percent className="h-3 w-3 mr-0.5" />
                    <span>ส่วนลด {getAverageDiscountPercent(3)}%</span>
                  </div>
                </div>
              </TabsTrigger>
              
              {/* แท็บทริกเกอร์ 6 เดือน */}
              <TabsTrigger
                value="6months"
                className="relative z-10 mx-1 rounded-lg h-full flex items-center justify-center 
                  transition-all duration-300 
                  data-[state=active]:shadow-lg data-[state=active]:shadow-purple-400/20
                  data-[state=active]:bg-gradient-to-b data-[state=active]:from-purple-500 data-[state=active]:to-purple-700
                  data-[state=active]:border data-[state=active]:border-purple-400/40
                  data-[state=active]:text-white
                  data-[state=inactive]:text-indigo-200 data-[state=inactive]:hover:text-white
                  data-[state=inactive]:hover:bg-indigo-700/50"
              >
                <div className="absolute inset-0 rounded-lg overflow-hidden">
                  {/* เอฟเฟกต์เส้นแสงเคลื่อนไหวเมื่อ active */}
                  <div className="absolute inset-0 opacity-0 data-[state=active]:opacity-100">
                    <div className="absolute top-0 left-0 right-0 h-[1px] bg-purple-300/70"></div>
                    <div className="absolute bottom-0 left-0 right-0 h-[1px] bg-purple-300/30"></div>
                    <div className="absolute top-0 bottom-0 left-0 w-[1px] bg-purple-300/30"></div>
                    <div className="absolute top-0 bottom-0 right-0 w-[1px] bg-purple-300/30"></div>
                  </div>
                </div>
                
                <div className="relative flex flex-col items-center px-3 z-10">
                  <span className="text-sm font-medium">6 เดือน</span>
                  <div className="text-xs text-purple-300/90 mt-0.5 flex items-center">
                    <Percent className="h-3 w-3 mr-0.5" />
                    <span>ส่วนลด {getAverageDiscountPercent(6)}%</span>
                  </div>
                </div>
              </TabsTrigger>
              
              {/* แท็บทริกเกอร์ 12 เดือน */}
              <TabsTrigger
                value="yearly"
                className="relative z-10 mx-1 rounded-lg h-full flex items-center justify-center 
                  transition-all duration-300 
                  data-[state=active]:shadow-lg data-[state=active]:shadow-amber-400/20
                  data-[state=active]:bg-gradient-to-b data-[state=active]:from-amber-500 data-[state=active]:to-amber-700
                  data-[state=active]:border data-[state=active]:border-amber-400/40
                  data-[state=active]:text-white
                  data-[state=inactive]:text-indigo-200 data-[state=inactive]:hover:text-white
                  data-[state=inactive]:hover:bg-indigo-700/50"
              >
                <div className="absolute inset-0 rounded-lg overflow-hidden">
                  {/* เอฟเฟกต์เส้นแสงเคลื่อนไหวเมื่อ active */}
                  <div className="absolute inset-0 opacity-0 data-[state=active]:opacity-100">
                    <div className="absolute top-0 left-0 right-0 h-[1px] bg-amber-300/70"></div>
                    <div className="absolute bottom-0 left-0 right-0 h-[1px] bg-amber-300/30"></div>
                    <div className="absolute top-0 bottom-0 left-0 w-[1px] bg-amber-300/30"></div>
                    <div className="absolute top-0 bottom-0 right-0 w-[1px] bg-amber-300/30"></div>
                    
                    {/* ออร่าเรืองแสงเฉพาะแท็บ 12 เดือน */}
                    <motion.div 
                      className="absolute inset-0 bg-amber-500/10"
                      animate={{ opacity: [0.05, 0.15, 0.05] }}
                      transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                    />
                  </div>
                </div>
                
                <div className="relative flex flex-col items-center px-3 z-10">
                  <span className="text-sm font-medium">12 เดือน</span>
                  <div className="text-xs text-amber-300/90 mt-0.5 flex items-center">
                    <Percent className="h-3 w-3 mr-0.5" />
                    <span>ส่วนลด {getAverageDiscountPercent(12)}%</span>
                  </div>
                  <div className="absolute -top-1 -right-1">
                    <motion.div
                      animate={{ scale: [0.9, 1.1, 0.9], rotate: [0, 5, 0] }}
                      transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
                      className="opacity-0 data-[state=active]:opacity-100"
                    >
                      <div className="text-amber-400 text-xs">✦</div>
                    </motion.div>
                  </div>
                </div>
              </TabsTrigger>
            </TabsList>

            {/* แสดงรายการแพ็กเกจตามแท็บที่เลือก */}
            <TabsContent value="monthly" className="mt-6">
              {isLoading ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[...Array(3)].map((_, index) => (
                    <div
                      key={index}
                      className="bg-gradient-to-br from-indigo-900/40 to-purple-900/30 border border-indigo-800/30 rounded-xl p-5"
                    >
                      <Skeleton className="h-7 w-1/2 mb-1 bg-indigo-700/20" />
                      <Skeleton className="h-4 w-3/4 mb-4 bg-indigo-700/20" />
                      <Skeleton className="h-10 w-1/2 mb-6 bg-indigo-700/20" />
                      <div className="space-y-3 mb-6">
                        {[...Array(3)].map((_, i) => (
                          <div key={i} className="flex items-start">
                            <Skeleton className="h-4 w-4 mr-2 bg-indigo-700/20" />
                            <Skeleton className="h-4 w-full bg-indigo-700/20" />
                          </div>
                        ))}
                      </div>
                      <Skeleton className="h-9 w-full bg-indigo-700/20" />
                    </div>
                  ))}
                </div>
              ) : packages ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {packages.map((pkg) => {
                    const isCurrentPackage =
                      activePackage && activePackage.packageId === pkg.id;
                    return (
                      <motion.div
                        key={pkg.id}
                        whileHover={{ scale: 1.02 }}
                        transition={{ type: "spring", stiffness: 400, damping: 10 }}
                        className={`relative overflow-hidden rounded-xl ${isCurrentPackage ? "shadow-xl shadow-amber-500/40 animate-pulse-gentle" : ""}`}
                      >
                        <PackageCard
                          id={pkg.id}
                          name={pkg.name}
                          description={pkg.description}
                          price={pkg.price}
                          discount3Months={pkg.discount3Months || undefined}
                          discount6Months={pkg.discount6Months || undefined}
                          discount12Months={pkg.discount12Months || undefined}
                          requestsLimit={pkg.requestsLimit}
                          creditPerVerification={pkg.creditPerVerification}
                          durationMonths={1}
                          isPopular={pkg.name === "มืออาชีพ" || pkg.name === "ธรรดา"}
                          features={parseFeatures(pkg.features)}
                          tag={isCurrentPackage ? "แพ็กเกจที่ใช้งานอยู่" : pkg.tag}
                          onSubscribe={handleSubscriptionSuccess}
                          usageData={isCurrentPackage && activePackage ? {
                            used: activePackage.requestsUsed,
                            total: activePackage.package?.requestsLimit || pkg.requestsLimit,
                            percentage: Math.min(Math.round((activePackage.requestsUsed / (activePackage.package?.requestsLimit || pkg.requestsLimit)) * 100), 100)
                          } : undefined}
                        />
                        {isCurrentPackage && (
                          <div className="absolute inset-0 border-4 border-amber-500 rounded-xl glow-gold"></div>
                        )}
                      </motion.div>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-12 bg-indigo-900/20 rounded-lg border border-indigo-800/30">
                  <Sparkles className="h-8 w-8 mx-auto mb-3 text-amber-400/70" />
                  <p className="text-indigo-200">ไม่พบข้อมูลแพ็กเกจในขณะนี้</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="3months" className="mt-6">
              {isLoading ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[...Array(3)].map((_, index) => (
                    <div
                      key={index}
                      className="bg-gradient-to-br from-indigo-900/40 to-purple-900/30 border border-indigo-800/30 rounded-xl p-5"
                    >
                      <Skeleton className="h-7 w-1/2 mb-1 bg-indigo-700/20" />
                      <Skeleton className="h-4 w-3/4 mb-4 bg-indigo-700/20" />
                      <Skeleton className="h-10 w-1/2 mb-6 bg-indigo-700/20" />
                      <div className="space-y-3 mb-6">
                        {[...Array(3)].map((_, i) => (
                          <div key={i} className="flex items-start">
                            <Skeleton className="h-4 w-4 mr-2 bg-indigo-700/20" />
                            <Skeleton className="h-4 w-full bg-indigo-700/20" />
                          </div>
                        ))}
                      </div>
                      <Skeleton className="h-9 w-full bg-indigo-700/20" />
                    </div>
                  ))}
                </div>
              ) : packages ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {packages.map((pkg) => (
                    <PackageCard
                      key={pkg.id}
                      id={pkg.id}
                      name={pkg.name}
                      description={pkg.description}
                      price={pkg.price}
                      discount3Months={pkg.discount3Months || undefined}
                      discount6Months={pkg.discount6Months || undefined}
                      discount12Months={pkg.discount12Months || undefined}
                      requestsLimit={pkg.requestsLimit}
                      creditPerVerification={pkg.creditPerVerification}
                      durationMonths={3}
                      isPopular={pkg.name === "มืออาชีพ" || pkg.name === "ธรรดา"}
                      features={parseFeatures(pkg.features)}
                      tag={pkg.tag}
                      onSubscribe={user ? handleSubscriptionSuccess : handleNotLoggedInSubscribe}
                      usageData={activePackage && pkg.id === activePackage.packageId ? {
                        used: activePackage.requestsUsed,
                        total: pkg.requestsLimit,
                        percentage: Math.min(Math.round((activePackage.requestsUsed / pkg.requestsLimit) * 100), 100)
                      } : undefined}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12 bg-indigo-900/20 rounded-lg border border-indigo-800/30">
                  <Sparkles className="h-8 w-8 mx-auto mb-3 text-amber-400/70" />
                  <p className="text-indigo-200">ไม่พบข้อมูลแพ็กเกจในขณะนี้</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="6months" className="mt-6">
              {isLoading ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[...Array(3)].map((_, index) => (
                    <div
                      key={index}
                      className="bg-gradient-to-br from-indigo-900/40 to-purple-900/30 border border-indigo-800/30 rounded-xl p-5"
                    >
                      <Skeleton className="h-7 w-1/2 mb-1 bg-indigo-700/20" />
                      <Skeleton className="h-4 w-3/4 mb-4 bg-indigo-700/20" />
                      <Skeleton className="h-10 w-1/2 mb-6 bg-indigo-700/20" />
                      <div className="space-y-3 mb-6">
                        {[...Array(3)].map((_, i) => (
                          <div key={i} className="flex items-start">
                            <Skeleton className="h-4 w-4 mr-2 bg-indigo-700/20" />
                            <Skeleton className="h-4 w-full bg-indigo-700/20" />
                          </div>
                        ))}
                      </div>
                      <Skeleton className="h-9 w-full bg-indigo-700/20" />
                    </div>
                  ))}
                </div>
              ) : packages ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {packages.map((pkg) => (
                    <PackageCard
                      key={pkg.id}
                      id={pkg.id}
                      name={pkg.name}
                      description={pkg.description}
                      price={pkg.price}
                      discount3Months={pkg.discount3Months || undefined}
                      discount6Months={pkg.discount6Months || undefined}
                      discount12Months={pkg.discount12Months || undefined}
                      requestsLimit={pkg.requestsLimit}
                      creditPerVerification={pkg.creditPerVerification}
                      durationMonths={6}
                      isPopular={pkg.name === "มืออาชีพ" || pkg.name === "ธรรดา"}
                      features={parseFeatures(pkg.features)}
                      tag={pkg.tag}
                      onSubscribe={user ? handleSubscriptionSuccess : handleNotLoggedInSubscribe}
                      usageData={activePackage && pkg.id === activePackage.packageId ? {
                        used: activePackage.requestsUsed,
                        total: pkg.requestsLimit,
                        percentage: Math.min(Math.round((activePackage.requestsUsed / pkg.requestsLimit) * 100), 100)
                      } : undefined}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12 bg-indigo-900/20 rounded-lg border border-indigo-800/30">
                  <Sparkles className="h-8 w-8 mx-auto mb-3 text-amber-400/70" />
                  <p className="text-indigo-200">ไม่พบข้อมูลแพ็กเกจในขณะนี้</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="yearly" className="mt-6">
              {isLoading ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[...Array(3)].map((_, index) => (
                    <div
                      key={index}
                      className="bg-gradient-to-br from-indigo-900/40 to-purple-900/30 border border-indigo-800/30 rounded-xl p-5"
                    >
                      <Skeleton className="h-7 w-1/2 mb-1 bg-indigo-700/20" />
                      <Skeleton className="h-4 w-3/4 mb-4 bg-indigo-700/20" />
                      <Skeleton className="h-10 w-1/2 mb-6 bg-indigo-700/20" />
                      <div className="space-y-3 mb-6">
                        {[...Array(3)].map((_, i) => (
                          <div key={i} className="flex items-start">
                            <Skeleton className="h-4 w-4 mr-2 bg-indigo-700/20" />
                            <Skeleton className="h-4 w-full bg-indigo-700/20" />
                          </div>
                        ))}
                      </div>
                      <Skeleton className="h-9 w-full bg-indigo-700/20" />
                    </div>
                  ))}
                </div>
              ) : packages ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {packages.map((pkg) => (
                    <PackageCard
                      key={pkg.id}
                      id={pkg.id}
                      name={pkg.name}
                      description={pkg.description}
                      price={pkg.price}
                      discount3Months={pkg.discount3Months || undefined}
                      discount6Months={pkg.discount6Months || undefined}
                      discount12Months={pkg.discount12Months || undefined}
                      requestsLimit={pkg.requestsLimit}
                      creditPerVerification={pkg.creditPerVerification}
                      durationMonths={12}
                      isPopular={pkg.name === "มืออาชีพ" || pkg.name === "ธรรดา"}
                      features={parseFeatures(pkg.features)}
                      tag={pkg.tag}
                      onSubscribe={user ? handleSubscriptionSuccess : handleNotLoggedInSubscribe}
                      usageData={activePackage && pkg.id === activePackage.packageId ? {
                        used: activePackage.requestsUsed,
                        total: pkg.requestsLimit,
                        percentage: Math.min(Math.round((activePackage.requestsUsed / pkg.requestsLimit) * 100), 100)
                      } : undefined}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12 bg-indigo-900/20 rounded-lg border border-indigo-800/30">
                  <Sparkles className="h-8 w-8 mx-auto mb-3 text-amber-400/70" />
                  <p className="text-indigo-200">ไม่พบข้อมูลแพ็กเกจในขณะนี้</p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </motion.div>

        {/* ส่วนคุณสมบัติและข้อมูลเพิ่มเติมของบริการ */}
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <h2 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-200 to-indigo-400 mb-8 text-center">
              คุณสมบัติพิเศษจากเทพเจ้า
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              <motion.div 
                className="backdrop-blur-sm bg-indigo-900/20 rounded-xl p-6 border border-indigo-800/50"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.5 }}
                whileHover={{ y: -5, boxShadow: "0 10px 30px -10px rgba(139, 92, 246, 0.3)" }}
              >
                <div className="h-12 w-12 rounded-lg bg-gradient-to-br from-purple-500 to-indigo-600 flex items-center justify-center mb-4">
                  <ShieldCheck className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-lg font-bold text-white mb-2">ความปลอดภัยสูงสุด</h3>
                <p className="text-indigo-300 text-sm">
                  ระบบของเราใช้เทคโนโลยีการเข้ารหัสข้อมูลระดับสูง เพื่อปกป้องข้อมูลสำคัญของคุณทั้งหมด
                </p>
              </motion.div>

              <motion.div 
                className="backdrop-blur-sm bg-indigo-900/20 rounded-xl p-6 border border-indigo-800/50"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.6 }}
                whileHover={{ y: -5, boxShadow: "0 10px 30px -10px rgba(139, 92, 246, 0.3)" }}
              >
                <div className="h-12 w-12 rounded-lg bg-gradient-to-br from-amber-500 to-yellow-400 flex items-center justify-center mb-4">
                  <Bell className="h-6 w-6 text-indigo-900" />
                </div>
                <h3 className="text-lg font-bold text-white mb-2">แจ้งเตือนทันที</h3>
                <p className="text-indigo-300 text-sm">
                  รับการแจ้งเตือนทันทีเมื่อมีการทำรายการใหม่ ช่วยให้คุณไม่พลาดทุกความเคลื่อนไหว
                </p>
              </motion.div>

              <motion.div 
                className="backdrop-blur-sm bg-indigo-900/20 rounded-xl p-6 border border-indigo-800/50"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.7 }}
                whileHover={{ y: -5, boxShadow: "0 10px 30px -10px rgba(139, 92, 246, 0.3)" }}
              >
                <div className="h-12 w-12 rounded-lg bg-gradient-to-br from-emerald-500 to-green-400 flex items-center justify-center mb-4">
                  <Activity className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-lg font-bold text-white mb-2">รายงานละเอียด</h3>
                <p className="text-indigo-300 text-sm">
                  ดูรายงานและสถิติการใช้งานอย่างละเอียด ช่วยให้คุณเข้าใจและบริหารธุรกิจได้ดียิ่งขึ้น
                </p>
              </motion.div>
            </div>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="bg-gradient-to-br from-indigo-900/40 to-purple-900/40 rounded-2xl p-8 border border-indigo-800/40 mb-12"
          >
            <div className="flex flex-col md:flex-row md:items-center">
              <div className="flex-1">
                <h3 className="text-2xl font-bold text-white mb-4">ยังไม่แน่ใจว่าต้องเลือกแพ็กเกจไหน?</h3>
                <p className="text-indigo-300 mb-6 md:mb-0 md:pr-8">
                  ทดลองใช้แพ็กเกจฟรีของเราเพื่อสัมผัสประสบการณ์การใช้งานระบบ SLIPKUY หรือติดต่อทีมงานของเราเพื่อรับคำแนะนำในการเลือกแพ็กเกจที่เหมาะกับธุรกิจของคุณ
                </p>
              </div>
              <div className="flex flex-shrink-0 space-x-4 mt-6 md:mt-0">
                <Button className="bg-amber-500 hover:bg-amber-600 text-indigo-950 shadow-lg shadow-amber-500/20" asChild>
                  <a href="/auth">
                    <span>สมัครสมาชิก</span>
                  </a>
                </Button>
                <Button variant="outline" className="border-indigo-600 text-indigo-300 hover:bg-indigo-800/30" asChild>
                  <a href="mailto:<EMAIL>">
                    <span>ติดต่อเรา</span>
                  </a>
                </Button>
              </div>
            </div>
          </motion.div>
        </div>
        
        <div className="relative z-10 my-12 text-center">
          <p className="text-indigo-400 text-sm">
            สงวนลิขสิทธิ์ © 2025 สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า
          </p>
        </div>
      </div>
    </div>
  );
}