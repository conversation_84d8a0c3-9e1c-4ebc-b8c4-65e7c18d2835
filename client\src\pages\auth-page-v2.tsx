import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/hooks/use-auth";
import { insertUserSchema } from "@shared/schema";
import { LucideLogIn, LucideUserPlus, LucideUser, LucideMail, LucideKey, LucideShield, LucideCheck, LucideBuilding } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

// กำหนด Schema สำหรับการเข้าสู่ระบบ
const loginSchema = z.object({
  username: z.string().min(1, "กรุณากรอกชื่อผู้ใช้"),
  password: z.string().min(1, "กรุณากรอกรหัสผ่าน"),
});

// กำหนด Schema สำหรับการลงทะเบียน (โดยใช้ Schema ที่มีอยู่แล้ว)
const registerSchema = insertUserSchema.extend({
  password: z.string().min(8, "รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร"),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "รหัสผ่านไม่ตรงกัน",
  path: ["confirmPassword"],
});

type LoginFormValues = z.infer<typeof loginSchema>;
type RegisterFormValues = z.infer<typeof registerSchema>;

export default function AuthPage() {
  const [activeTab, setActiveTab] = useState<"login" | "register">("login");
  const [location, setLocation] = useLocation();
  const { user, loginMutation, registerMutation } = useAuth();

  // สร้าง Form สำหรับการเข้าสู่ระบบ
  const loginForm = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: "",
      password: "",
    },
  });

  // สร้าง Form สำหรับการลงทะเบียน
  const registerForm = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      username: "",
      email: "",
      firstName: "",
      lastName: "",
      companyName: "",
      password: "",
      confirmPassword: "",
    },
  });

  // ส่งคำขอเข้าสู่ระบบ
  const onLoginSubmit = (values: LoginFormValues) => {
    loginMutation.mutate(values);
  };

  // ส่งคำขอลงทะเบียน
  const onRegisterSubmit = (values: RegisterFormValues) => {
    // ลบ confirmPassword ออกเพราะไม่ต้องส่งไปยัง API
    const { confirmPassword, ...registerData } = values;
    registerMutation.mutate(registerData);
  };

  // เมื่อมีผู้ใช้งานเข้าสู่ระบบแล้ว ให้ไปยังหน้าแดชบอร์ด
  useEffect(() => {
    if (user) {
      setLocation("/dashboard");
    }
  }, [user, setLocation]);

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-indigo-950 via-purple-900 to-indigo-900 overflow-hidden relative">
      {/* เอฟเฟกต์พื้นหลัง */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxkZWZzPjxwYXR0ZXJuIGlkPSJzdGFycyIgeD0iMCIgeT0iMCIgd2lkdGg9IjEwMCIgaGVpZ2h0PSIxMDAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiPjxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InRyYW5zcGFyZW50Ii8+PGNpcmNsZSBjeD0iNTAiIGN5PSI1MCIgcj0iMC4yIiBmaWxsPSIjZmZmIiBvcGFjaXR5PSIwLjYiLz48Y2lyY2xlIGN4PSIyNSIgY3k9IjI1IiByPSIwLjEiIGZpbGw9IiNmZmYiIG9wYWNpdHk9IjAuNCIvPjxjaXJjbGUgY3g9Ijc1IiBjeT0iNzUiIHI9IjAuMyIgZmlsbD0iI2ZmZiIgb3BhY2l0eT0iMC41Ii8+PGNpcmNsZSBjeD0iNzUiIGN5PSIyNSIgcj0iMC4xNSIgZmlsbD0iI2ZmZiIgb3BhY2l0eT0iMC4zIi8+PGNpcmNsZSBjeD0iMjUiIGN5PSI3NSIgcj0iMC4yIiBmaWxsPSIjZmZmIiBvcGFjaXR5PSIwLjQiLz48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjc3RhcnMpIi8+PC9zdmc+')]"></div>
        <div className="absolute inset-0 bg-gradient-radial from-indigo-500/5 to-indigo-900/0"></div>
        
        {/* ลูกแก้วลอยอยู่ */}
        <div className="absolute top-[20%] left-[15%] w-64 h-64 bg-gradient-to-br from-amber-500/30 to-purple-500/30 rounded-full blur-3xl animate-float opacity-30"></div>
        <div className="absolute top-[60%] right-[15%] w-72 h-72 bg-gradient-to-br from-indigo-500/30 to-fuchsia-500/30 rounded-full blur-3xl animate-float-slow opacity-30"></div>
        
        {/* รังสีเทพเจ้า */}
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full h-[50vh] bg-gradient-radial from-amber-500/20 to-transparent"></div>
      </div>

      {/* โลโก้ด้านบน */}
      <div className="relative z-10 pt-10 pb-5 text-center">
        <div className="inline-block bg-gradient-to-r from-indigo-500 via-purple-500 to-amber-500 text-transparent bg-clip-text">
          <h1 className="text-5xl font-bold font-serif tracking-tight">SLIPKUY</h1>
        </div>
        <p className="mt-2 text-indigo-200 text-lg">ระบบตรวจสอบสลิปธนาคารอัจฉริยะ</p>
      </div>

      {/* เนื้อหาหลัก */}
      <div className="relative z-10 flex-1 flex items-center justify-center px-4 py-8">
        <div className="w-full max-w-7xl mx-auto grid gap-10 grid-cols-1 lg:grid-cols-2 items-center">
          {/* ส่วนแบบฟอร์ม */}
          <div className="order-2 lg:order-1">
            <Card className="backdrop-blur-xl bg-white/10 border-0 shadow-2xl overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-indigo-900/80 to-purple-900/80 -z-10"></div>
              <div className="flex">
                {/* แท็บเลือกรูปแบบ */}
                <div className="w-1/4 bg-gradient-to-b from-indigo-800/80 to-purple-900/80 p-4">
                  <div className="space-y-6 pt-8">
                    <button
                      onClick={() => setActiveTab("login")}
                      className={`w-full flex flex-col items-center justify-center p-3 rounded-lg transition-all ${
                        activeTab === "login"
                          ? "bg-gradient-to-r from-amber-500/20 to-purple-500/20 text-amber-300 shadow-lg shadow-amber-500/20"
                          : "text-indigo-300 hover:bg-white/5"
                      }`}
                    >
                      <LucideLogIn className={`h-8 w-8 ${activeTab === "login" ? "text-amber-300" : "text-indigo-400"}`} />
                      <span className="mt-2 text-sm font-medium">เข้าสู่ระบบ</span>
                    </button>
                    
                    <button
                      onClick={() => setActiveTab("register")}
                      className={`w-full flex flex-col items-center justify-center p-3 rounded-lg transition-all ${
                        activeTab === "register"
                          ? "bg-gradient-to-r from-amber-500/20 to-purple-500/20 text-amber-300 shadow-lg shadow-amber-500/20"
                          : "text-indigo-300 hover:bg-white/5"
                      }`}
                    >
                      <LucideUserPlus className={`h-8 w-8 ${activeTab === "register" ? "text-amber-300" : "text-indigo-400"}`} />
                      <span className="mt-2 text-sm font-medium">ลงทะเบียน</span>
                    </button>
                  </div>
                </div>
                
                {/* แบบฟอร์ม */}
                <div className="w-3/4 p-6">
                  <div className="pt-4 pb-6">
                    <h2 className="text-2xl font-bold text-white mb-1">
                      {activeTab === "login" ? "ยินดีต้อนรับกลับ" : "เริ่มต้นใช้งาน"}
                    </h2>
                    <p className="text-indigo-300">
                      {activeTab === "login"
                        ? "เข้าสู่ระบบเพื่อจัดการการตรวจสอบสลิปและ API"
                        : "สร้างบัญชีใหม่เพื่อเริ่มต้นใช้งานฟรี"}
                    </p>
                  </div>

                  {activeTab === "login" ? (
                    <Form {...loginForm}>
                      <form
                        onSubmit={loginForm.handleSubmit(onLoginSubmit)}
                        className="space-y-5"
                      >
                        <FormField
                          control={loginForm.control}
                          name="username"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-indigo-200">ชื่อผู้ใช้</FormLabel>
                              <FormControl>
                                <div className="relative">
                                  <LucideUser className="absolute left-3 top-3 h-4 w-4 text-indigo-300" />
                                  <Input
                                    placeholder="ชื่อผู้ใช้ของคุณ"
                                    className="pl-10 bg-indigo-950/50 border-indigo-700 text-white placeholder:text-indigo-400"
                                    {...field}
                                  />
                                </div>
                              </FormControl>
                              <FormMessage className="text-amber-300" />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={loginForm.control}
                          name="password"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-indigo-200">รหัสผ่าน</FormLabel>
                              <FormControl>
                                <div className="relative">
                                  <LucideKey className="absolute left-3 top-3 h-4 w-4 text-indigo-300" />
                                  <Input
                                    type="password"
                                    placeholder="รหัสผ่านของคุณ"
                                    className="pl-10 bg-indigo-950/50 border-indigo-700 text-white placeholder:text-indigo-400"
                                    {...field}
                                  />
                                </div>
                              </FormControl>
                              <FormMessage className="text-amber-300" />
                            </FormItem>
                          )}
                        />

                        <Button
                          type="submit"
                          className="w-full bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white"
                          disabled={loginMutation.isPending}
                        >
                          {loginMutation.isPending ? "กำลังเข้าสู่ระบบ..." : "เข้าสู่ระบบ"}
                        </Button>
                      </form>
                    </Form>
                  ) : (
                    <Form {...registerForm}>
                      <form
                        onSubmit={registerForm.handleSubmit(onRegisterSubmit)}
                        className="space-y-5"
                      >
                        <FormField
                          control={registerForm.control}
                          name="username"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-indigo-200">ชื่อผู้ใช้</FormLabel>
                              <FormControl>
                                <div className="relative">
                                  <LucideUser className="absolute left-3 top-3 h-4 w-4 text-indigo-300" />
                                  <Input
                                    placeholder="ชื่อผู้ใช้ที่ต้องการ"
                                    className="pl-10 bg-indigo-950/50 border-indigo-700 text-white placeholder:text-indigo-400"
                                    {...field}
                                  />
                                </div>
                              </FormControl>
                              <FormMessage className="text-amber-300" />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={registerForm.control}
                          name="email"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-indigo-200">อีเมล</FormLabel>
                              <FormControl>
                                <div className="relative">
                                  <LucideMail className="absolute left-3 top-3 h-4 w-4 text-indigo-300" />
                                  <Input
                                    type="email"
                                    placeholder="<EMAIL>"
                                    className="pl-10 bg-indigo-950/50 border-indigo-700 text-white placeholder:text-indigo-400"
                                    {...field}
                                  />
                                </div>
                              </FormControl>
                              <FormMessage className="text-amber-300" />
                            </FormItem>
                          )}
                        />

                        <div className="grid grid-cols-2 gap-4">
                          <FormField
                            control={registerForm.control}
                            name="firstName"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-indigo-200">ชื่อ</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="ชื่อของคุณ"
                                    className="bg-indigo-950/50 border-indigo-700 text-white placeholder:text-indigo-400"
                                    value={field.value || ""}
                                    onChange={field.onChange}
                                    onBlur={field.onBlur}
                                    name={field.name}
                                    ref={field.ref}
                                  />
                                </FormControl>
                                <FormMessage className="text-amber-300" />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={registerForm.control}
                            name="lastName"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-indigo-200">นามสกุล</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="นามสกุลของคุณ"
                                    className="bg-indigo-950/50 border-indigo-700 text-white placeholder:text-indigo-400"
                                    value={field.value || ""}
                                    onChange={field.onChange}
                                    onBlur={field.onBlur}
                                    name={field.name}
                                    ref={field.ref}
                                  />
                                </FormControl>
                                <FormMessage className="text-amber-300" />
                              </FormItem>
                            )}
                          />
                        </div>

                        <FormField
                          control={registerForm.control}
                          name="companyName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-indigo-200">ชื่อบริษัท (ไม่บังคับ)</FormLabel>
                              <FormControl>
                                <div className="relative">
                                  <LucideBuilding className="absolute left-3 top-3 h-4 w-4 text-indigo-300" />
                                  <Input
                                    placeholder="ชื่อบริษัทของคุณ"
                                    className="pl-10 bg-indigo-950/50 border-indigo-700 text-white placeholder:text-indigo-400"
                                    value={field.value || ""}
                                    onChange={field.onChange}
                                    onBlur={field.onBlur}
                                    name={field.name}
                                    ref={field.ref}
                                  />
                                </div>
                              </FormControl>
                              <FormMessage className="text-amber-300" />
                            </FormItem>
                          )}
                        />

                        <div className="grid grid-cols-2 gap-4">
                          <FormField
                            control={registerForm.control}
                            name="password"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-indigo-200">รหัสผ่าน</FormLabel>
                                <FormControl>
                                  <div className="relative">
                                    <LucideKey className="absolute left-3 top-3 h-4 w-4 text-indigo-300" />
                                    <Input
                                      type="password"
                                      placeholder="รหัสผ่าน"
                                      className="pl-10 bg-indigo-950/50 border-indigo-700 text-white placeholder:text-indigo-400"
                                      value={field.value || ""}
                                      onChange={field.onChange}
                                      onBlur={field.onBlur}
                                      name={field.name}
                                      ref={field.ref}
                                    />
                                  </div>
                                </FormControl>
                                <FormMessage className="text-amber-300" />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={registerForm.control}
                            name="confirmPassword"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-indigo-200">ยืนยันรหัสผ่าน</FormLabel>
                                <FormControl>
                                  <div className="relative">
                                    <LucideShield className="absolute left-3 top-3 h-4 w-4 text-indigo-300" />
                                    <Input
                                      type="password"
                                      placeholder="ยืนยันรหัสผ่าน"
                                      className="pl-10 bg-indigo-950/50 border-indigo-700 text-white placeholder:text-indigo-400"
                                      value={field.value || ""}
                                      onChange={field.onChange}
                                      onBlur={field.onBlur}
                                      name={field.name}
                                      ref={field.ref}
                                    />
                                  </div>
                                </FormControl>
                                <FormMessage className="text-amber-300" />
                              </FormItem>
                            )}
                          />
                        </div>

                        <div className="flex items-start">
                          <div className="flex items-center h-5">
                            <input
                              id="terms"
                              type="checkbox"
                              className="h-4 w-4 border-indigo-600 rounded focus:ring-amber-500"
                              required
                            />
                          </div>
                          <div className="ml-3 text-sm">
                            <label htmlFor="terms" className="font-medium text-indigo-200">
                              ฉันยอมรับ{" "}
                              <a href="#terms" className="text-amber-400 hover:text-amber-300">
                                ข้อกำหนดและเงื่อนไข
                              </a>{" "}
                              และ{" "}
                              <a href="#privacy" className="text-amber-400 hover:text-amber-300">
                                นโยบายความเป็นส่วนตัว
                              </a>
                            </label>
                          </div>
                        </div>

                        <Button
                          type="submit"
                          className="w-full bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white"
                          disabled={registerMutation.isPending}
                        >
                          {registerMutation.isPending ? "กำลังลงทะเบียน..." : "ลงทะเบียน"}
                        </Button>
                      </form>
                    </Form>
                  )}
                </div>
              </div>
            </Card>
          </div>

          {/* ส่วนข้อมูลบริการ */}
          <div className="order-1 lg:order-2 md:px-6">
            <div className="text-center lg:text-left max-w-xl mx-auto lg:mx-0">
              <div className="inline-block p-2 rounded-xl bg-white/10 backdrop-blur-md mb-6">
                <div className="bg-gradient-to-br from-amber-500/20 to-purple-500/20 rounded-lg px-4 py-2 text-amber-300 flex items-center justify-center">
                  <LucideShield className="h-5 w-5 mr-2" />
                  <span className="text-sm font-medium">ปลอดภัย เชื่อถือได้ รวดเร็ว</span>
                </div>
              </div>
              
              <h2 className="text-4xl font-bold text-white mb-6 leading-tight">
                <span className="block">อำนาจแห่งการตรวจสอบสลิป</span>
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-amber-400 to-amber-300">เทคโนโลยีระดับเทพเจ้า</span>
              </h2>
              
              <p className="text-lg text-indigo-200 mb-8">
                ตรวจสอบความถูกต้องของรายการโอนเงินด้วยระบบอัจฉริยะที่แม่นยำที่สุด
                พร้อมการวิเคราะห์เชิงลึกและจัดการผ่าน API ที่ปลอดภัย
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10">
                <div className="bg-white/5 backdrop-blur-sm rounded-xl p-5 border border-indigo-800/30">
                  <div className="text-amber-400 mb-3">
                    <LucideCheck className="h-6 w-6" />
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-1">ตรวจสอบได้ทุกธนาคาร</h3>
                  <p className="text-indigo-300">
                    รองรับการตรวจสอบสลิปของทุกธนาคารในประเทศไทย ด้วยความแม่นยำสูง
                  </p>
                </div>
                
                <div className="bg-white/5 backdrop-blur-sm rounded-xl p-5 border border-indigo-800/30">
                  <div className="text-amber-400 mb-3">
                    <LucideCheck className="h-6 w-6" />
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-1">API ที่ยืดหยุ่น</h3>
                  <p className="text-indigo-300">
                    เชื่อมต่อกับระบบของคุณผ่าน API ที่ปลอดภัยและใช้งานง่าย
                  </p>
                </div>
                
                <div className="bg-white/5 backdrop-blur-sm rounded-xl p-5 border border-indigo-800/30">
                  <div className="text-amber-400 mb-3">
                    <LucideCheck className="h-6 w-6" />
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-1">จัดการข้อมูลแบบเรียลไทม์</h3>
                  <p className="text-indigo-300">
                    ติดตามและวิเคราะห์ข้อมูลการตรวจสอบได้ทันที พร้อมระบบการแจ้งเตือน
                  </p>
                </div>
                
                <div className="bg-white/5 backdrop-blur-sm rounded-xl p-5 border border-indigo-800/30">
                  <div className="text-amber-400 mb-3">
                    <LucideCheck className="h-6 w-6" />
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-1">ความปลอดภัยระดับโลก</h3>
                  <p className="text-indigo-300">
                    ข้อมูลของคุณได้รับการปกป้องด้วยเทคโนโลยีการเข้ารหัสขั้นสูง
                  </p>
                </div>
              </div>
              
              <div className="sm:flex sm:space-x-4 space-y-3 sm:space-y-0">
                <Button
                  onClick={() => setActiveTab("register")}
                  className="w-full sm:w-auto bg-amber-500 hover:bg-amber-600 text-white shadow-lg shadow-amber-500/30 text-base px-8 py-6"
                >
                  เริ่มต้นใช้งานฟรี
                </Button>
                <Button
                  onClick={() => setLocation("/docs")}
                  variant="outline"
                  className="w-full sm:w-auto border-indigo-400 text-indigo-200 hover:bg-indigo-800/20 hover:text-white text-base px-8 py-6"
                >
                  ดูเอกสาร API
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* ส่วนท้าย */}
      <div className="relative z-10 mt-10">
        <div className="py-6 text-center text-indigo-400 text-sm">
          &copy; {new Date().getFullYear()} SLIPKUY. All rights reserved.
        </div>
      </div>
    </div>
  );
}