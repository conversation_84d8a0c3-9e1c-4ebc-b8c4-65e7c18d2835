import { useState, useRef } from "react";
import { useToast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { UploadCloud, X, Image, FileImage, Sparkles, Loader2, Upload } from "lucide-react";
import { apiRequest } from "@/lib/queryClient";
import { motion } from "framer-motion";

interface SlipUploaderProps {
  onVerificationComplete: (result: any) => void;
}

export function SlipUploader({ onVerificationComplete }: SlipUploaderProps) {
  const { toast } = useToast();
  const [file, setFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const queryClient = useQueryClient();
  
  const verifyMutation = useMutation({
    mutationFn: async (formData: FormData) => {
      const xhr = new XMLHttpRequest();
      
      // ติดตามความคืบหน้าการอัปโหลด
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          setUploadProgress(Math.round((event.loaded / event.total) * 100));
        }
      });
      
      return new Promise((resolve, reject) => {
        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            resolve(JSON.parse(xhr.responseText));
          } else {
            try {
              reject(JSON.parse(xhr.responseText));
            } catch (e) {
              reject({ message: "การตรวจสอบล้มเหลว" });
            }
          }
        };
        xhr.onerror = () => {
          reject({ message: "การเชื่อมต่อล้มเหลว กรุณาลองใหม่อีกครั้ง" });
        };
        
        xhr.open('POST', '/api/v1/verify');
        xhr.withCredentials = true;
        xhr.send(formData);
      });
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/verifications'] });
      queryClient.invalidateQueries({ queryKey: ['/api/stats'] });
      onVerificationComplete(data);
      setUploadProgress(0);
      toast({
        title: "การตรวจสอบสำเร็จ",
        description: "เทพเจ้าได้พิจารณาสลิปของท่านเรียบร้อยแล้ว",
      });
    },
    onError: (error: any) => {
      setUploadProgress(0);
      toast({
        title: "พลังเทพเจ้าไม่สามารถอ่านสลิปได้",
        description: error.message || "ไม่สามารถตรวจสอบสลิปได้ กรุณาลองใหม่อีกครั้ง",
        variant: "destructive",
      });
    }
  });
  
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const selectedFile = event.target.files[0];
      
      // ตรวจสอบว่าเป็นไฟล์รูปภาพหรือไม่
      if (!selectedFile.type.match('image.*')) {
        toast({
          title: "รูปแบบไฟล์ไม่ถูกต้อง",
          description: "กรุณาอัปโหลดไฟล์รูปภาพเท่านั้น",
          variant: "destructive",
        });
        return;
      }
      
      // ตรวจสอบขนาดไฟล์
      if (selectedFile.size > 5 * 1024 * 1024) { // 5MB
        toast({
          title: "ไฟล์มีขนาดใหญ่เกินไป",
          description: "กรุณาอัปโหลดไฟล์ขนาดไม่เกิน 5MB",
          variant: "destructive",
        });
        return;
      }
      
      setFile(selectedFile);
      
      // สร้าง URL สำหรับแสดงตัวอย่างรูปภาพ
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result as string);
      };
      reader.readAsDataURL(selectedFile);
    }
  };
  
  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    
    if (event.dataTransfer.files && event.dataTransfer.files[0]) {
      const droppedFile = event.dataTransfer.files[0];
      
      if (!droppedFile.type.match('image.*')) {
        toast({
          title: "รูปแบบไฟล์ไม่ถูกต้อง",
          description: "กรุณาอัปโหลดไฟล์รูปภาพเท่านั้น",
          variant: "destructive",
        });
        return;
      }
      
      if (droppedFile.size > 5 * 1024 * 1024) {
        toast({
          title: "ไฟล์มีขนาดใหญ่เกินไป",
          description: "กรุณาอัปโหลดไฟล์ขนาดไม่เกิน 5MB",
          variant: "destructive",
        });
        return;
      }
      
      setFile(droppedFile);
      
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result as string);
      };
      reader.readAsDataURL(droppedFile);
    }
  };
  
  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
  };
  
  const removeFile = () => {
    setFile(null);
    setPreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
  // ตรวจสอบว่ามี QR Code ในรูปภาพหรือไม่
  const detectQRCode = async (imageUrl: string): Promise<boolean> => {
    return new Promise<boolean>((resolve) => {
      // สร้าง element Image ในเบราวเซอร์
      const img = document.createElement('img');
      
      img.onload = () => {
        // สร้าง canvas สำหรับวิเคราะห์รูปภาพ
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        
        if (!context) {
          console.error('ไม่สามารถสร้าง canvas context ได้');
          resolve(false);
          return;
        }
        
        // กำหนดขนาด canvas ตามรูปภาพ
        canvas.width = img.width;
        canvas.height = img.height;
        context.drawImage(img, 0, 0);
        
        try {
          // ดึงข้อมูลรูปภาพจาก canvas
          const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
          
          // ใช้ jsQR ในการตรวจสอบ QR code
          import('jsqr').then((jsQRModule) => {
            const jsQR = jsQRModule.default;
            const code = jsQR(
              imageData.data,
              imageData.width,
              imageData.height
            );
            
            if (code) {
              console.log('พบ QR code ในรูปภาพ:', code.data);
              resolve(true);
            } else {
              console.log('ไม่พบ QR code ในรูปภาพ');
              resolve(false);
            }
          }).catch(err => {
            console.error('เกิดข้อผิดพลาดในการโหลด jsQR:', err);
            resolve(false);
          });
        } catch (error) {
          console.error('เกิดข้อผิดพลาดในการตรวจจับ QR code:', error);
          resolve(false);
        }
      };
      
      img.onerror = () => {
        console.error('ไม่สามารถโหลดรูปภาพได้');
        resolve(false);
      };
      
      // กำหนด src ให้ img เพื่อเริ่มการโหลด
      img.src = imageUrl;
    });
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!file) {
      toast({
        title: "ไม่พบรูปภาพสลิป",
        description: "กรุณาอัปโหลดรูปภาพสลิปก่อนดำเนินการ",
        variant: "destructive",
      });
      return;
    }
    
    // แสดงสถานะกำลังตรวจสอบ QR Code
    toast({
      title: "กำลังตรวจสอบ QR Code",
      description: "กรุณารอสักครู่...",
    });
    
    // ตรวจสอบ QR Code ก่อนส่งไปที่ API
    if (preview) {
      const hasQRCode = await detectQRCode(preview);
      
      if (!hasQRCode) {
        toast({
          title: "ไม่พบ QR Code ในรูปภาพ",
          description: "กรุณาตรวจสอบว่าเป็นรูปสลิปธนาคารที่ถูกต้องและมี QR Code",
          variant: "destructive",
        });
        return;
      }
      
      // พบ QR Code แล้ว ดำเนินการส่งไปตรวจสอบต่อ
      toast({
        title: "พบ QR Code ในรูปภาพ",
        description: "กำลังส่งไปตรวจสอบข้อมูล...",
      });
    }
    
    const formData = new FormData();
    formData.append('file', file);
    
    verifyMutation.mutate(formData);
  };
  
  return (
    <div className="w-full">
      <form onSubmit={handleSubmit}>
        <div
          className={`relative border-2 border-dashed rounded-lg p-6 text-center ${
            preview ? 'border-amber-400/40 bg-indigo-900/30' : 'border-indigo-700/30 hover:border-amber-400/40'
          }`}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
        >
          {/* Decorative background effects */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            {Array.from({ length: 10 }).map((_, i) => (
              <motion.div 
                key={i}
                className="absolute h-1 w-1 rounded-full bg-amber-300"
                animate={{ 
                  opacity: [Math.random() * 0.3 + 0.2, Math.random() * 0.7 + 0.3, Math.random() * 0.3 + 0.2],
                  scale: [1, 1.5, 1]
                }}
                transition={{ 
                  duration: Math.random() * 3 + 2,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
                style={{
                  top: `${Math.random() * 100}%`,
                  left: `${Math.random() * 100}%`,
                  boxShadow: `0 0 ${Math.random() * 5 + 2}px ${Math.random() * 3 + 1}px rgba(251, 191, 36, 0.3)`
                }}
              />
            ))}
          </div>
          
          {preview ? (
            <div className="relative z-10">
              <motion.img
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
                src={preview}
                alt="ตัวอย่างสลิป"
                className="mx-auto max-h-64 rounded object-contain relative"
              />
              <motion.button
                type="button"
                onClick={removeFile}
                className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 border border-red-300"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <X className="h-4 w-4" />
              </motion.button>
              <motion.div 
                className="mt-4"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                <p className="text-sm text-indigo-200 mb-1">{file?.name}</p>
                <p className="text-xs text-indigo-400">{(file?.size && (file.size / 1024 / 1024).toFixed(2)) || 0} MB</p>
              </motion.div>
            </div>
          ) : (
            <div className="py-8 relative z-10">
              <input
                type="file"
                id="slip-image"
                ref={fileInputRef}
                onChange={handleFileChange}
                className="hidden"
                accept="image/*"
              />
              <motion.div
                initial={{ y: 10 }}
                animate={{ y: [0, -10, 0] }}
                transition={{ 
                  duration: 4,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
              >
                <div className="relative mb-3 mx-auto w-20 h-20 flex items-center justify-center">
                  <div className="absolute inset-0 bg-indigo-900/70 rounded-full"></div>
                  <div className="absolute inset-0 rounded-full bg-gradient-to-r from-indigo-600/40 to-purple-600/40 blur-md"></div>
                  <UploadCloud className="relative z-10 h-10 w-10 text-indigo-100" />
                  <motion.div
                    className="absolute -right-1 -top-1"
                    animate={{ 
                      opacity: [0.5, 1, 0.5],
                      scale: [1, 1.2, 1]
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      repeatType: "reverse"
                    }}
                  >
                    <Sparkles className="h-5 w-5 text-amber-400" />
                  </motion.div>
                </div>
              </motion.div>
              <p className="mt-2 text-indigo-100">ลากและวางรูปภาพสลิป หรือ <label htmlFor="slip-image" className="text-amber-400 hover:text-amber-300 cursor-pointer underline">คลิกเพื่อเลือกไฟล์</label></p>
              <p className="mt-1 text-xs text-indigo-400">รองรับรูปภาพ JPG, PNG, GIF ขนาดไม่เกิน 5MB</p>
            </div>
          )}
        </div>
        
        {verifyMutation.isPending && (
          <motion.div 
            className="mt-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            <p className="text-sm font-medium text-indigo-100 mb-2 flex items-center">
              <Loader2 className="h-4 w-4 animate-spin mr-2 text-amber-400" />
              กำลังเรียกใช้พลังเทพเจ้าตรวจสอบสลิป...
            </p>
            <Progress 
              value={uploadProgress} 
              className="h-2 bg-indigo-900/50"
            />
          </motion.div>
        )}
        
        <motion.div 
          className="mt-6 flex justify-center"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Button
            type="submit"
            disabled={!file || verifyMutation.isPending}
            className="bg-gradient-to-r from-amber-600 to-amber-800 hover:from-amber-700 hover:to-amber-900 text-white border-none py-6 px-8"
            size="lg"
          >
            {verifyMutation.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                กำลังตรวจสอบ...
              </>
            ) : (
              <>
                <Sparkles className="mr-2 h-4 w-4" />
                ตรวจสอบด้วยพลังเทพเจ้า
              </>
            )}
          </Button>
        </motion.div>
      </form>
    </div>
  );
}