import axios from 'axios';
import { db } from './db';
import * as schema from '../shared/schema';
import { eq, and } from 'drizzle-orm';
import { Webhook, WebhookLog, InsertWebhook, InsertWebhookLog } from '../shared/schema';

export class WebhookError extends Error {
  status: number;

  constructor(message: string, status: number = 500) {
    super(message);
    this.name = 'WebhookError';
    this.status = status;
  }
}

export class WebhookService {
  private static MAX_RETRIES = 3;
  private static RETRY_DELAY_MS = 1000;
  
  /**
   * ตรวจสอบว่าข้อมูลตรงตามเงื่อนไขหรือไม่
   */
  private checkConditions(conditions: any, payload: any): boolean {
    try {
      // ถ้าไม่มี payload หรือไม่มี conditions ถือว่าตรงเงื่อนไข
      if (!payload || !conditions) {
        return true;
      }
      
      // ตรวจสอบยอดเงินขั้นต่ำ
      if (conditions.minAmount && payload.amount) {
        const amount = typeof payload.amount === 'number' 
          ? payload.amount 
          : (payload.amount?.amount || 0);
          
        if (amount < conditions.minAmount) {
          return false;
        }
      }
      
      // ตรวจสอบยอดเงินสูงสุด
      if (conditions.maxAmount && payload.amount) {
        const amount = typeof payload.amount === 'number' 
          ? payload.amount 
          : (payload.amount?.amount || 0);
          
        if (amount > conditions.maxAmount) {
          return false;
        }
      }
      
      // ตรวจสอบรหัสธนาคาร (ถ้ามีการระบุ)
      if (conditions.bankCodes && conditions.bankCodes.length > 0) {
        // ถ้ามีการระบุธนาคาร แต่ไม่มีข้อมูลธนาคารใน payload ถือว่าไม่ตรงเงื่อนไข
        if (!payload.sender?.bank?.id && !payload.receiver?.bank?.id) {
          return false;
        }
        
        // ตรวจสอบธนาคารของผู้ส่ง
        const senderBankId = payload.sender?.bank?.id;
        // ตรวจสอบธนาคารของผู้รับ
        const receiverBankId = payload.receiver?.bank?.id;
        
        // ถ้ามีการระบุธนาคาร และไม่ตรงกับธนาคารของผู้ส่งหรือผู้รับ ถือว่าไม่ตรงเงื่อนไข
        if (!(conditions.bankCodes.includes(senderBankId) || conditions.bankCodes.includes(receiverBankId))) {
          return false;
        }
      }
      
      // ตรวจสอบช่วงเวลา (ถ้ามีการระบุ) - อาจใช้สำหรับ feature ในอนาคต
      if (conditions.timeRange && (conditions.timeRange.start || conditions.timeRange.end)) {
        // implementation สำหรับ timeRange
        // TODO: เพิ่มการตรวจสอบ timeRange เมื่อต้องการใช้งาน
      }
      
      // ถ้าผ่านทุกเงื่อนไข ถือว่าตรงเงื่อนไข
      return true;
    } catch (error) {
      console.error('Error checking conditions:', error);
      // กรณีเกิด error ให้ถือว่าตรงเงื่อนไข (เพื่อให้ส่ง webhook ไปให้ดีกว่า)
      return true;
    }
  }

  /**
   * สร้าง webhook ใหม่
   */
  async createWebhook(data: InsertWebhook): Promise<Webhook> {
    try {
      const [webhook] = await db.insert(schema.webhooks).values(data).returning();
      return webhook;
    } catch (error) {
      console.error('Error creating webhook:', error);
      throw new WebhookError('ไม่สามารถสร้าง webhook ได้', 500);
    }
  }

  /**
   * แก้ไข webhook ที่มีอยู่แล้ว
   */
  async updateWebhook(id: number, userId: number, data: Partial<Webhook>): Promise<Webhook> {
    try {
      const [webhook] = await db.update(schema.webhooks)
        .set({ ...data, updatedAt: new Date() })
        .where(and(
          eq(schema.webhooks.id, id),
          eq(schema.webhooks.userId, userId)
        ))
        .returning();
      
      if (!webhook) {
        throw new WebhookError('ไม่พบ webhook ที่ต้องการแก้ไข', 404);
      }
      
      return webhook;
    } catch (error) {
      if (error instanceof WebhookError) throw error;
      console.error('Error updating webhook:', error);
      throw new WebhookError('ไม่สามารถอัพเดท webhook ได้', 500);
    }
  }

  /**
   * ลบ webhook
   */
  async deleteWebhook(id: number, userId: number): Promise<void> {
    try {
      const result = await db.delete(schema.webhooks)
        .where(and(
          eq(schema.webhooks.id, id),
          eq(schema.webhooks.userId, userId)
        ));
      
      if (!result) {
        throw new WebhookError('ไม่พบ webhook ที่ต้องการลบ', 404);
      }
    } catch (error) {
      if (error instanceof WebhookError) throw error;
      console.error('Error deleting webhook:', error);
      throw new WebhookError('ไม่สามารถลบ webhook ได้', 500);
    }
  }

  /**
   * ดึงข้อมูล webhook ตาม id
   */
  async getWebhook(id: number, userId: number): Promise<Webhook> {
    try {
      const [webhook] = await db.select()
        .from(schema.webhooks)
        .where(and(
          eq(schema.webhooks.id, id),
          eq(schema.webhooks.userId, userId)
        ));
      
      if (!webhook) {
        throw new WebhookError('ไม่พบ webhook ที่ต้องการ', 404);
      }
      
      return webhook;
    } catch (error) {
      if (error instanceof WebhookError) throw error;
      console.error('Error fetching webhook:', error);
      throw new WebhookError('ไม่สามารถดึงข้อมูล webhook ได้', 500);
    }
  }

  /**
   * ดึงข้อมูล webhooks ทั้งหมดของ user
   */
  async getUserWebhooks(userId: number): Promise<Webhook[]> {
    try {
      const webhooks = await db.select()
        .from(schema.webhooks)
        .where(eq(schema.webhooks.userId, userId))
        .orderBy(schema.webhooks.createdAt);
      
      return webhooks;
    } catch (error) {
      console.error('Error fetching user webhooks:', error);
      throw new WebhookError('ไม่สามารถดึงข้อมูล webhooks ของผู้ใช้งานได้', 500);
    }
  }

  /**
   * ดึงประวัติการส่ง webhook
   */
  async getWebhookLogs(webhookId: number, userId: number, limit: number = 10): Promise<WebhookLog[]> {
    try {
      // ตรวจสอบก่อนว่า webhook นี้เป็นของ user จริงหรือไม่
      const [webhook] = await db.select()
        .from(schema.webhooks)
        .where(and(
          eq(schema.webhooks.id, webhookId),
          eq(schema.webhooks.userId, userId)
        ));
      
      if (!webhook) {
        throw new WebhookError('ไม่พบ webhook ที่ต้องการดูประวัติ', 404);
      }
      
      const logs = await db.select()
        .from(schema.webhookLogs)
        .where(eq(schema.webhookLogs.webhookId, webhookId))
        .orderBy(schema.webhookLogs.createdAt)
        .limit(limit);
      
      return logs;
    } catch (error) {
      if (error instanceof WebhookError) throw error;
      console.error('Error fetching webhook logs:', error);
      throw new WebhookError('ไม่สามารถดึงประวัติการส่ง webhook ได้', 500);
    }
  }

  /**
   * ทดสอบส่ง webhook ไปที่ URL ที่กำหนด
   */
  async testWebhook(webhook: Webhook): Promise<{success: boolean, response?: any, error?: string}> {
    try {
      const testPayload = {
        event_type: "test_event",
        timestamp: new Date().toISOString(),
        data: {
          message: 'นี่คือการทดสอบ webhook จากระบบ SLIPKUY'
        }
      };
      
      // สร้าง headers
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      };
      
      // เพิ่ม secret key ถ้ามี
      if (webhook.secretKey) {
        headers['X-Webhook-Secret'] = webhook.secretKey;
      }
      
      // ทำการส่ง request
      const response = await axios.post(webhook.url, testPayload, {
        headers,
        timeout: 5000 // timeout 5 วินาที
      });
      
      // บันทึกการทดสอบลงในประวัติ
      await this.logWebhookCall({
        webhookId: webhook.id,
        eventType: "system_update", // ใช้เป็น system_update เพราะเป็นการทดสอบ
        payload: JSON.stringify(testPayload),
        responseStatus: response.status,
        responseBody: JSON.stringify(response.data),
        success: true
      });
      
      // อัพเดทเวลาที่ทริกเกอร์ล่าสุด
      await db.update(schema.webhooks)
        .set({ 
          lastTriggeredAt: new Date(),
          updatedAt: new Date()
        })
        .where(eq(schema.webhooks.id, webhook.id));
      
      return {
        success: true,
        response: {
          status: response.status,
          data: response.data
        }
      };
    } catch (error) {
      let errorMessage = 'เกิดข้อผิดพลาดในการทดสอบ webhook';
      let statusCode = 500;
      
      if (axios.isAxiosError(error)) {
        errorMessage = `HTTP Error: ${error.message}`;
        statusCode = error.response?.status || 500;
        
        // บันทึกความล้มเหลวลงในประวัติ
        await this.logWebhookCall({
          webhookId: webhook.id,
          eventType: "system_update",
          payload: JSON.stringify({ event_type: "test_event" }),
          responseStatus: statusCode,
          error: errorMessage,
          success: false
        });
      }
      
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * ส่ง webhook ตามเหตุการณ์ที่เกิดขึ้น
   */
  async trigger(
    userId: number, 
    eventType: string, 
    payload: any,
    apiKeyId?: number
  ): Promise<{success: boolean, results: {webhookId: number, success: boolean, error?: string}[]}> {
    try {
      // ดึง webhooks ทั้งหมดที่เป็นของ user นี้
      const webhooks = await db.select()
        .from(schema.webhooks)
        .where(eq(schema.webhooks.userId, userId));
      
      // กรองเอาเฉพาะ webhooks ที่มี eventType ตรงกัน และตรงกับ apiKeyId (ถ้ามีการระบุ)
      const matchedWebhooks = webhooks.filter((webhook: Webhook) => {
        // ตรวจสอบว่า webhook นี้ active และมี event type ที่ตรงกัน
        const isActiveAndMatchEventType = webhook.isActive && webhook.eventType === eventType;
        
        if (!isActiveAndMatchEventType) return false;
        
        // ถ้ามีการระบุ apiKeyId ในการเรียกใช้ฟังก์ชัน
        if (apiKeyId) {
          // webhook ที่ไม่ได้ระบุ apiKeyId ให้ถือว่าใช้กับทุก API Key
          if (!webhook.apiKeyId) return true;
          
          // webhook ที่ระบุ apiKeyId ต้องตรงกับ apiKeyId ที่ส่งมา
          return webhook.apiKeyId === apiKeyId;
        }
        
        // ถ้าไม่มีการระบุ apiKeyId ในการเรียกใช้ฟังก์ชัน ให้ส่งไปทั้งหมด
        return true;
      });
      
      if (matchedWebhooks.length === 0) {
        return {
          success: true,
          results: []
        };
      }
      
      // กรองเอาเฉพาะ webhooks ที่ตรงตามเงื่อนไข
      const webhooksMatchingConditions = matchedWebhooks.filter((webhook: Webhook) => {
        // ถ้าไม่มีเงื่อนไข ให้ถือว่าตรงเงื่อนไข
        if (!webhook.conditions) {
          return true;
        }
        
        return this.checkConditions(webhook.conditions, payload);
      });
      
      if (webhooksMatchingConditions.length === 0) {
        return {
          success: true,
          results: []
        };
      }
      
      const results = [];
      
      // ส่ง webhook ทั้งหมดที่ตรงเงื่อนไข
      for (const webhook of webhooksMatchingConditions) {
        try {
          // สร้าง headers
          const headers: Record<string, string> = {
            'Content-Type': 'application/json'
          };
          
          // เพิ่ม secret key ถ้ามี
          if (webhook.secretKey) {
            headers['X-Webhook-Secret'] = webhook.secretKey;
          }
          
          // เพิ่ม timestamp และ event_type ในข้อมูลที่ส่ง
          const webhookPayload = {
            event_type: eventType,
            timestamp: new Date().toISOString(),
            data: payload
          };
          
          // ทำการส่ง request
          const response = await axios.post(webhook.url, webhookPayload, {
            headers,
            timeout: 10000 // timeout 10 วินาที
          });
          
          // อัพเดทเวลาที่ส่งล่าสุด
          await db.update(schema.webhooks)
            .set({ 
              lastTriggeredAt: new Date(),
              updatedAt: new Date()
            })
            .where(eq(schema.webhooks.id, webhook.id));
          
          // บันทึกการส่งลงในประวัติ
          await this.logWebhookCall({
            webhookId: webhook.id,
            eventType: eventType,
            payload: JSON.stringify(webhookPayload),
            responseStatus: response.status,
            responseBody: JSON.stringify(response.data),
            success: true
          });
          
          results.push({
            webhookId: webhook.id,
            success: true
          });
        } catch (error) {
          let errorMessage = 'เกิดข้อผิดพลาดในการส่ง webhook';
          let statusCode = 500;
          
          if (axios.isAxiosError(error)) {
            errorMessage = `HTTP Error: ${error.message}`;
            statusCode = error.response?.status || 500;
          }
          
          // บันทึกความล้มเหลวลงในประวัติ
          await this.logWebhookCall({
            webhookId: webhook.id,
            eventType: eventType,
            payload: JSON.stringify(payload),
            responseStatus: statusCode,
            error: errorMessage,
            success: false
          });
          
          results.push({
            webhookId: webhook.id,
            success: false,
            error: errorMessage
          });
        }
      }
      
      return {
        success: true,
        results
      };
    } catch (error) {
      console.error('Error triggering webhooks:', error);
      throw new WebhookError('ไม่สามารถส่ง webhook ได้', 500);
    }
  }

  /**
   * บันทึกการส่ง webhook ลงในประวัติ
   */
  private async logWebhookCall(logData: InsertWebhookLog): Promise<WebhookLog> {
    try {
      const [logEntry] = await db.insert(schema.webhookLogs).values(logData).returning();
      return logEntry;
    } catch (error) {
      console.error('Error logging webhook call:', error);
      throw new WebhookError('ไม่สามารถบันทึกประวัติการส่ง webhook ได้', 500);
    }
  }

  /**
   * ลองส่ง webhook ใหม่ที่ล้มเหลว (retry)
   */
  async retryFailedWebhook(logId: number, userId: number): Promise<{success: boolean, error?: string}> {
    try {
      // ดึงข้อมูล log ที่ต้องการ retry
      const [log] = await db.select()
        .from(schema.webhookLogs)
        .where(eq(schema.webhookLogs.id, logId));
      
      if (!log) {
        throw new WebhookError('ไม่พบประวัติการส่ง webhook ที่ต้องการลองใหม่', 404);
      }
      
      // ตรวจสอบว่า webhook นี้เป็นของ user จริงหรือไม่
      const [webhook] = await db.select()
        .from(schema.webhooks)
        .where(and(
          eq(schema.webhooks.id, log.webhookId),
          eq(schema.webhooks.userId, userId)
        ));
      
      if (!webhook) {
        throw new WebhookError('ไม่มีสิทธิ์ในการลองส่ง webhook นี้ใหม่', 403);
      }
      
      // สร้าง headers
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      };
      
      // เพิ่ม secret key ถ้ามี
      if (webhook.secretKey) {
        headers['X-Webhook-Secret'] = webhook.secretKey;
      }
      
      try {
        // สร้าง payload ใหม่หรือใช้ payload เดิมจาก log
        let payload = {};
        try {
          payload = JSON.parse(log.payload || '{}');
        } catch (e) {
          console.warn('Error parsing payload from log:', e);
        }
        
        // ทำการส่ง request
        const response = await axios.post(webhook.url, payload, {
          headers,
          timeout: 10000 // timeout 10 วินาที
        });
        
        // อัพเดทเวลาที่ส่งล่าสุด
        await db.update(schema.webhooks)
          .set({ 
            lastTriggeredAt: new Date(),
            updatedAt: new Date()
          })
          .where(eq(schema.webhooks.id, webhook.id));
        
        // บันทึกการส่งลงในประวัติ
        await db.update(schema.webhookLogs)
          .set({
            responseStatus: response.status,
            responseBody: JSON.stringify(response.data),
            error: null,
            success: true,
            retryCount: (log.retryCount || 0) + 1,
            updatedAt: new Date()
          })
          .where(eq(schema.webhookLogs.id, logId));
        
        return {
          success: true
        };
      } catch (error) {
        let errorMessage = 'เกิดข้อผิดพลาดในการลองส่ง webhook ใหม่';
        let statusCode = 500;
        
        if (axios.isAxiosError(error)) {
          errorMessage = `HTTP Error: ${error.message}`;
          statusCode = error.response?.status || 500;
        }
        
        // บันทึกความล้มเหลวลงในประวัติ
        await db.update(schema.webhookLogs)
          .set({
            responseStatus: statusCode,
            error: errorMessage,
            success: false,
            retryCount: (log.retryCount || 0) + 1,
            updatedAt: new Date()
          })
          .where(eq(schema.webhookLogs.id, logId));
        
        return {
          success: false,
          error: errorMessage
        };
      }
    } catch (error) {
      if (error instanceof WebhookError) throw error;
      console.error('Error retrying webhook:', error);
      throw new WebhookError('ไม่สามารถลองส่ง webhook ใหม่ได้', 500);
    }
  }
}

export const webhookService = new WebhookService();