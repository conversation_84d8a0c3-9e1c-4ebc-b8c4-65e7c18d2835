import { Pool } from 'pg';
import { drizzle } from 'drizzle-orm/node-postgres';
import * as schema from "@shared/schema";
import { logger } from './logger';
import dotenv from 'dotenv';

// โหลดค่าจากไฟล์ .env
dotenv.config();

// ใช้ค่า DATABASE_URL จาก environment variable
const DATABASE_URL = process.env.DATABASE_URL;

if (!DATABASE_URL) {
  throw new Error(
    "DATABASE_URL must be set. Did you forget to provision a database?",
  );
}

// สร้าง connection pool ด้วยการตั้งค่าเพิ่มเติม
export const pool = new Pool({
  connectionString: DATABASE_URL,
  max: 20, // จำนวน connection สูงสุด
  idleTimeoutMillis: 30000, // เวลาที่ connection ไม่ได้ใช้งานก่อนจะถูกปิด
  connectionTimeoutMillis: 30000, // เวลาที่รอการเชื่อมต่อก่อนจะ timeout
  // ปิดการใช้งาน SSL สำหรับฐานข้อมูลท้องถิ่น
  // ssl: {
  //   rejectUnauthorized: true, // ตรวจสอบใบรับรอง SSL
  // }
});

// ตรวจสอบการเชื่อมต่อเมื่อเริ่มต้น
pool.on('error', (err) => {
  logger.error('Unexpected error on idle database client', err);
  // แสดงข้อผิดพลาดแต่ไม่หยุดการทำงานของแอพพลิเคชัน
  // process.exit(-1); // ปิดการใช้งานคำสั่งนี้เพื่อให้แอพพลิเคชันทำงานต่อไป
});

// สร้าง drizzle client
export const db = drizzle(pool, { schema });

// ฟังก์ชันสำหรับทดสอบการเชื่อมต่อ
export const testConnection = async () => {
  let client;
  try {
    logger.debug('Testing database connection...');
    client = await pool.connect();
    const result = await client.query('SELECT NOW()');
    logger.success('Database connection successful:', result.rows[0]);
    return true;
  } catch (err) {
    logger.error('Error testing database connection:', err);
    throw err; // ส่งต่อข้อผิดพลาดเพื่อให้ผู้เรียกใช้จัดการต่อ
  } finally {
    if (client) {
      client.release();
    }
  }
};

// ทดสอบการเชื่อมต่อเมื่อโมดูลถูกโหลด
setTimeout(async () => {
  try {
    await testConnection();
    logger.info('Database connection established successfully');
  } catch (err) {
    logger.error('Failed to connect to database. Application may not function correctly:', err);
    // แสดงข้อผิดพลาดแต่ไม่หยุดการทำงานของแอพพลิเคชัน
    // เนื่องจากเราต้องการให้แอพพลิเคชันทำงานต่อไปแม้จะมีปัญหากับฐานข้อมูล
    // process.exit(-1); // ปิดการใช้งานคำสั่งนี้เพื่อให้แอพพลิเคชันทำงานต่อไป
  }
}, 1000); // รอ 1 วินาทีก่อนทดสอบการเชื่อมต่อ
