/**
 * session-cleanup.ts - เครื่องมือสำหรับการจัดการ session ที่หมดอายุ
 */

import { db } from "../db";
import { sql } from "drizzle-orm";
import { Request, Response, NextFunction } from "express";

export async function clearExpiredSessions() {
  try {
    const result = await db.execute(sql`DELETE FROM sessions WHERE expire < NOW()`);
    return { success: true, result };
  } catch (error) {
    console.error("เกิดข้อผิดพลาดในการล้าง session ที่หมดอายุ:", error);
    return { success: false, error };
  }
}

export function clearAllSessions() {
  return new Promise<{ success: boolean, result?: any, error?: any }>(async (resolve) => {
    try {
      const result = await db.execute(sql`DELETE FROM sessions`);
      resolve({ success: true, result });
    } catch (error) {
      console.error("เกิดข้อผิดพลาดในการล้าง session ทั้งหมด:", error);
      resolve({ success: false, error });
    }
  });
}

export function clearUserSession(userId: number) {
  return new Promise<{ success: boolean, result?: any, error?: any }>(async (resolve) => {
    try {
      // ค้นหา session ของผู้ใช้และลบ
      const result = await db.execute(sql`
        DELETE FROM sessions 
        WHERE sess::jsonb->'passport'->>'user' = ${userId.toString()}
      `);
      resolve({ success: true, result });
    } catch (error) {
      console.error(`เกิดข้อผิดพลาดในการล้าง session ของผู้ใช้ ID ${userId}:`, error);
      resolve({ success: false, error });
    }
  });
}

// Middleware ที่บังคับให้ต้องเข้าสู่ระบบใหม่
export function enforceReauthentication(req: Request, res: Response, next: NextFunction) {
  if (req.isAuthenticated()) {
    // ลงชื่อออกผู้ใช้
    req.logout((err) => {
      if (err) {
        return next(err);
      }
      
      // ลบ session
      req.session.destroy((err) => {
        if (err) {
          return next(err);
        }
        
        // ล้าง cookie
        res.clearCookie('slipkuy.sid', { path: '/' });
        
        // ส่งกลับไปที่หน้าล็อกอิน
        res.redirect('/auth');
      });
    });
  } else {
    // ถ้าไม่ได้ล็อกอิน ส่งกลับไปที่หน้าล็อกอิน
    res.redirect('/auth');
  }
}