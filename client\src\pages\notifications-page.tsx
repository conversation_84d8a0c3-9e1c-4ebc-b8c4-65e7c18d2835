import React, { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { motion, AnimatePresence } from 'framer-motion';
import { useToast } from '@/hooks/use-toast';
import { Bell, Calendar, Check, Filter, RefreshCw, Settings, Shield, Zap } from 'lucide-react';
import { GodNotificationList } from '@/components/notifications/god-notification';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Card } from '@/components/ui/card';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { apiRequest } from '@/lib/queryClient';

// ประเภทของการแจ้งเตือน
type NotificationFilter = 'all' | 'unread' | 'unusual_transaction' | 'api_key_expiring' | 'fraud_detection' | 'reports';

export default function NotificationsPage() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [filter, setFilter] = useState<NotificationFilter>('all');

  // ดึงข้อมูลการแจ้งเตือน
  const { data, isLoading, isError, error } = useQuery({
    queryKey: ['/api/notifications', filter],
    queryFn: async () => {
      const queryParams = new URLSearchParams();
      
      if (filter === 'unread') {
        queryParams.append('unreadOnly', 'true');
      } else if (filter !== 'all') {
        queryParams.append('type', filter);
      }
      
      const response = await fetch(`/api/notifications?${queryParams.toString()}`);
      if (!response.ok) {
        throw new Error('ไม่สามารถดึงข้อมูลการแจ้งเตือนได้');
      }
      return response.json();
    }
  });

  // Mutation สำหรับการทำเครื่องหมายว่าอ่านแล้ว
  const markAsReadMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await apiRequest('PUT', `/api/notifications/${id}/read`);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/notifications'] });
    },
    onError: (error: Error) => {
      toast({
        title: 'เกิดข้อผิดพลาด',
        description: error.message,
        variant: 'destructive',
      });
    }
  });

  // Mutation สำหรับการทำเครื่องหมายว่าอ่านแล้วทั้งหมด
  const markAllAsReadMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest('PUT', '/api/notifications/read-all');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/notifications'] });
      toast({
        title: 'สำเร็จ',
        description: 'ทำเครื่องหมายว่าอ่านแล้วทั้งหมด',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'เกิดข้อผิดพลาด',
        description: error.message,
        variant: 'destructive',
      });
    }
  });

  // Mutation สำหรับการลบการแจ้งเตือน
  const deleteNotificationMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await apiRequest('DELETE', `/api/notifications/${id}`);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/notifications'] });
    },
    onError: (error: Error) => {
      toast({
        title: 'เกิดข้อผิดพลาด',
        description: error.message,
        variant: 'destructive',
      });
    }
  });

  // ฟังก์ชันสำหรับการทำเครื่องหมายว่าอ่านแล้ว
  const handleMarkAsRead = (id: number) => {
    markAsReadMutation.mutate(id);
  };

  // ฟังก์ชันสำหรับการลบการแจ้งเตือน
  const handleDeleteNotification = (id: number) => {
    deleteNotificationMutation.mutate(id);
  };

  // ฟังก์ชันสำหรับการรีเฟรชข้อมูล
  const handleRefresh = () => {
    queryClient.invalidateQueries({ queryKey: ['/api/notifications'] });
  };

  // ฟังก์ชันสำหรับการเปลี่ยนฟิลเตอร์
  const handleFilterChange = (newFilter: NotificationFilter) => {
    setFilter(newFilter);
  };

  // คำอธิบายสำหรับแต่ละฟิลเตอร์
  const getFilterDescription = () => {
    switch (filter) {
      case 'unread':
        return 'การแจ้งเตือนที่ยังไม่ได้อ่าน';
      case 'unusual_transaction':
        return 'การแจ้งเตือนเกี่ยวกับธุรกรรมผิดปกติ';
      case 'api_key_expiring':
        return 'การแจ้งเตือนเกี่ยวกับ API Key ที่ใกล้หมดอายุ';
      case 'fraud_detection':
        return 'การแจ้งเตือนเกี่ยวกับการตรวจจับการฉ้อโกง';
      case 'reports':
        return 'รายงานและการแจ้งเตือนประจำวัน/สัปดาห์/เดือน';
      case 'all':
      default:
        return 'การแจ้งเตือนทั้งหมด';
    }
  };

  // เลือกไอคอนสำหรับแต่ละฟิลเตอร์
  const getFilterIcon = () => {
    switch (filter) {
      case 'unread':
        return <Bell className="h-5 w-5 mr-2 text-purple-400" />;
      case 'unusual_transaction':
        return <Zap className="h-5 w-5 mr-2 text-yellow-400" />;
      case 'api_key_expiring':
        return <Settings className="h-5 w-5 mr-2 text-blue-400" />;
      case 'fraud_detection':
        return <Shield className="h-5 w-5 mr-2 text-red-400" />;
      case 'reports':
        return <Calendar className="h-5 w-5 mr-2 text-green-400" />;
      case 'all':
      default:
        return <Bell className="h-5 w-5 mr-2 text-indigo-400" />;
    }
  };

  // กรองการแจ้งเตือนตามประเภท
  const filterNotifications = (notifications: any[]) => {
    if (filter === 'all') return notifications;
    if (filter === 'unread') return notifications.filter(n => !n.isRead);
    if (filter === 'reports') return notifications.filter(n => 
      ['daily_report', 'weekly_report', 'monthly_report'].includes(n.type)
    );
    return notifications.filter(n => n.type === filter);
  };

  // จำนวนการแจ้งเตือนตามหมวดหมู่
  const getNotificationCount = (type: NotificationFilter) => {
    if (!data?.notifications) return 0;
    
    if (type === 'all') return data.notifications.length;
    if (type === 'unread') return data.notifications.filter(n => !n.isRead).length;
    if (type === 'reports') return data.notifications.filter(n => 
      ['daily_report', 'weekly_report', 'monthly_report'].includes(n.type)
    ).length;
    return data.notifications.filter(n => n.type === type).length;
  };

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="container mx-auto py-6 px-4 sm:px-6"
    >
      {/* พื้นหลังสไตล์เทพเจ้า */}
      <div className="fixed inset-0 -z-10 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-b from-gray-900 via-indigo-950/70 to-gray-900"></div>
        <div className="absolute inset-0 opacity-20 bg-[radial-gradient(#2c0a8f_1px,transparent_1px)] [background-size:32px_32px]"></div>
        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-indigo-500/50 to-transparent"></div>
      </div>
      
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-indigo-300 to-blue-400">
              การแจ้งเตือน
            </h1>
            <p className="text-gray-400 mt-1">
              ตรวจสอบและจัดการการแจ้งเตือนทั้งหมดในระบบ
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
              className="text-indigo-300 hover:text-indigo-200 hover:bg-indigo-950 border-indigo-800"
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
              รีเฟรช
            </Button>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-indigo-300 hover:text-indigo-200 hover:bg-indigo-950 border-indigo-800"
                >
                  <Filter className="h-4 w-4 mr-1" />
                  กรอง
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="bg-gray-900 border-gray-800">
                <DropdownMenuLabel>ประเภทการแจ้งเตือน</DropdownMenuLabel>
                <DropdownMenuSeparator className="bg-gray-800" />
                <DropdownMenuItem
                  className={`${filter === 'all' ? 'bg-indigo-900/30 text-indigo-300' : ''}`}
                  onClick={() => handleFilterChange('all')}
                >
                  <Bell className="h-4 w-4 mr-2" />
                  ทั้งหมด
                  <span className="ml-auto bg-gray-800 px-1.5 py-0.5 rounded-full text-xs">
                    {getNotificationCount('all')}
                  </span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  className={`${filter === 'unread' ? 'bg-indigo-900/30 text-indigo-300' : ''}`}
                  onClick={() => handleFilterChange('unread')}
                >
                  <Bell className="h-4 w-4 mr-2 text-purple-400" />
                  ยังไม่ได้อ่าน
                  <span className="ml-auto bg-gray-800 px-1.5 py-0.5 rounded-full text-xs">
                    {getNotificationCount('unread')}
                  </span>
                </DropdownMenuItem>
                <DropdownMenuSeparator className="bg-gray-800" />
                <DropdownMenuItem
                  className={`${filter === 'unusual_transaction' ? 'bg-indigo-900/30 text-indigo-300' : ''}`}
                  onClick={() => handleFilterChange('unusual_transaction')}
                >
                  <Zap className="h-4 w-4 mr-2 text-yellow-400" />
                  ธุรกรรมผิดปกติ
                  <span className="ml-auto bg-gray-800 px-1.5 py-0.5 rounded-full text-xs">
                    {getNotificationCount('unusual_transaction')}
                  </span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  className={`${filter === 'api_key_expiring' ? 'bg-indigo-900/30 text-indigo-300' : ''}`}
                  onClick={() => handleFilterChange('api_key_expiring')}
                >
                  <Settings className="h-4 w-4 mr-2 text-blue-400" />
                  API Key ใกล้หมดอายุ
                  <span className="ml-auto bg-gray-800 px-1.5 py-0.5 rounded-full text-xs">
                    {getNotificationCount('api_key_expiring')}
                  </span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  className={`${filter === 'fraud_detection' ? 'bg-indigo-900/30 text-indigo-300' : ''}`}
                  onClick={() => handleFilterChange('fraud_detection')}
                >
                  <Shield className="h-4 w-4 mr-2 text-red-400" />
                  ตรวจพบการฉ้อโกง
                  <span className="ml-auto bg-gray-800 px-1.5 py-0.5 rounded-full text-xs">
                    {getNotificationCount('fraud_detection')}
                  </span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  className={`${filter === 'reports' ? 'bg-indigo-900/30 text-indigo-300' : ''}`}
                  onClick={() => handleFilterChange('reports')}
                >
                  <Calendar className="h-4 w-4 mr-2 text-green-400" />
                  รายงานประจำงวด
                  <span className="ml-auto bg-gray-800 px-1.5 py-0.5 rounded-full text-xs">
                    {getNotificationCount('reports')}
                  </span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => markAllAsReadMutation.mutate()}
              disabled={markAllAsReadMutation.isPending || getNotificationCount('unread') === 0}
              className="text-indigo-300 hover:text-indigo-200 hover:bg-indigo-950 border-indigo-800"
            >
              <Check className="h-4 w-4 mr-1" />
              อ่านทั้งหมด
            </Button>
          </div>
        </div>
        
        {/* แสดงฟิลเตอร์ปัจจุบัน */}
        <div className="flex items-center gap-2 text-sm text-gray-400 bg-gray-800/50 rounded-md py-2 px-3 border border-gray-800">
          {getFilterIcon()}
          <span>กำลังแสดง: {getFilterDescription()}</span>
        </div>
        
        {/* แสดงการแจ้งเตือน */}
        <Tabs defaultValue="notifications">
          <TabsList className="bg-gray-800/50 border border-gray-800">
            <TabsTrigger value="notifications" className="data-[state=active]:bg-indigo-900/40">
              <Bell className="h-4 w-4 mr-1" />
              การแจ้งเตือน
            </TabsTrigger>
            <TabsTrigger value="settings" className="data-[state=active]:bg-indigo-900/40">
              <Settings className="h-4 w-4 mr-1" />
              ตั้งค่าการแจ้งเตือน
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="notifications" className="mt-4">
            <Card className="bg-gray-900/60 backdrop-blur-sm border-gray-800 shadow-xl">
              <div className="p-4 sm:p-6">
                {isLoading ? (
                  <div className="flex flex-col items-center justify-center p-8">
                    <div className="w-12 h-12 rounded-full border-4 border-t-indigo-500 border-indigo-800/30 animate-spin"></div>
                    <p className="mt-4 text-gray-400">กำลังโหลดการแจ้งเตือน...</p>
                  </div>
                ) : isError ? (
                  <div className="flex flex-col items-center justify-center p-8 text-red-400">
                    <p>เกิดข้อผิดพลาดในการโหลดการแจ้งเตือน</p>
                    <p className="text-sm text-gray-500">{error instanceof Error ? error.message : 'เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ'}</p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleRefresh}
                      className="mt-4 text-indigo-300 hover:text-indigo-200 hover:bg-indigo-950 border-indigo-800"
                    >
                      <RefreshCw className="h-4 w-4 mr-1" />
                      ลองอีกครั้ง
                    </Button>
                  </div>
                ) : (
                  <AnimatePresence mode="popLayout">
                    <GodNotificationList
                      notifications={filterNotifications(data?.notifications || [])}
                      onMarkAsRead={handleMarkAsRead}
                      onDelete={handleDeleteNotification}
                      emptyMessage={`ไม่มีการแจ้งเตือน${filter !== 'all' ? 'ในหมวดหมู่นี้' : ''}`}
                    />
                  </AnimatePresence>
                )}
              </div>
            </Card>
          </TabsContent>
          
          <TabsContent value="settings" className="mt-4">
            <Card className="bg-gray-900/60 backdrop-blur-sm border-gray-800 shadow-xl">
              <div className="p-4 sm:p-6">
                <h2 className="text-xl font-semibold mb-4 text-indigo-300">ตั้งค่าการแจ้งเตือน</h2>
                <p className="text-gray-400 mb-4">
                  ปรับแต่งการแจ้งเตือนที่คุณต้องการได้ที่นี่
                </p>
                
                <div className="space-y-4">
                  {/* รายการตั้งค่าการแจ้งเตือนจะแสดงที่นี่ */}
                  <p className="text-gray-500 italic">อยู่ระหว่างการพัฒนา</p>
                </div>
              </div>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </motion.div>
  );
}