/**
 * API สำหรับการจัดการการยืนยันตัวตนด้วยอีเมล และ OTP
 */

import type { Express, Request, Response, NextFunction } from "express";
import { verificationService } from "../verification-service";
import { body, validationResult } from "express-validator";
import { logger } from "../logger";

// สร้างฟังก์ชันสำหรับตรวจสอบการล็อกอิน - ใช้เฉพาะกับบางเส้นทางที่ต้องการ
const requireLogin = (req: Request, res: Response, next: NextFunction) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ error: "กรุณาล็อกอินก่อนใช้งาน" });
  }
  next();
};

export function registerVerificationAPI(app: Express) {
  /**
   * ส่งรหัสยืนยันอเนกประสงค์ (ทั้งอีเมลและเบอร์โทร)
   * @route POST /api/verification/send
   * @bodyparam type ประเภทของการยืนยัน (email หรือ phone)
   * @bodyparam identifier อีเมลหรือเบอร์โทรศัพท์
   * @bodyparam userId ID ของผู้ใช้ (ถ้ามี)
   */
  app.post(
    "/api/verification/send",
    [
      body("type").isIn(["email", "phone"]).withMessage("ประเภทการยืนยันต้องเป็น email หรือ phone เท่านั้น"),
      body("identifier").isString().withMessage("กรุณาระบุอีเมลหรือเบอร์โทรศัพท์"),
      body("userId").optional().isNumeric().withMessage("รหัสผู้ใช้ต้องเป็นตัวเลข"),
    ],
    async (req: Request, res: Response) => {
      try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
          return res.status(400).json({
            success: false,
            message: "ข้อมูลไม่ถูกต้อง",
            errors: errors.array()
          });
        }

        const { type, identifier } = req.body;
        const userId = req.user?.id;

        // เลือกวิธีการส่งรหัสตามประเภท
        let result;
        if (type === 'email') {
          result = await verificationService.sendEmailVerificationCode(
            identifier,
            userId,
            'email'
          );
        } else if (type === 'phone') {
          result = await verificationService.sendPhoneOTP(
            identifier,
            userId
          );
        } else {
          return res.status(400).json({
            success: false,
            message: "ประเภทการยืนยันไม่ถูกต้อง",
          });
        }

        if (!result.success) {
          return res.status(400).json({
            success: false,
            message: result.message || `ไม่สามารถส่งรหัสยืนยันไปยัง${type === 'email' ? 'อีเมล' : 'เบอร์โทรศัพท์'}ได้`,
          });
        }

        return res.status(200).json({
          success: true,
          message: result.message || `ส่งรหัสยืนยันไปยัง${type === 'email' ? 'อีเมล' : 'เบอร์โทรศัพท์'}เรียบร้อยแล้ว`,
          reference: result.reference || null
        });
      } catch (error) {
        logger.error("เกิดข้อผิดพลาดในการส่งรหัสยืนยัน:", error);
        return res.status(500).json({
          success: false,
          message: "เกิดข้อผิดพลาดในการส่งรหัสยืนยัน",
        });
      }
    }
  );

  /**
   * ตรวจสอบรหัสยืนยันอเนกประสงค์ (ทั้งอีเมลและเบอร์โทร)
   * @route POST /api/verification/verify
   * @bodyparam type ประเภทของการยืนยัน (email หรือ phone)
   * @bodyparam identifier อีเมลหรือเบอร์โทรศัพท์
   * @bodyparam code รหัสยืนยัน
   * @bodyparam reference รหัสอ้างอิงสำหรับ OTP (เฉพาะกรณี phone)
   */
  app.post(
    "/api/verification/verify",
    [
      body("type").isIn(["email", "phone"]).withMessage("ประเภทการยืนยันต้องเป็น email หรือ phone เท่านั้น"),
      body("identifier").isString().withMessage("กรุณาระบุอีเมลหรือเบอร์โทรศัพท์"),
      body("code").isString().withMessage("กรุณาระบุรหัสยืนยัน"),
      body("reference").optional().isString(),
    ],
    async (req: Request, res: Response) => {
      try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
          return res.status(400).json({
            success: false,
            message: "ข้อมูลไม่ถูกต้อง",
            errors: errors.array()
          });
        }

        const { type, identifier, code, reference } = req.body;
        const userId = req.user?.id; // ไม่ต้องกำหนดค่าเริ่มต้นเป็น 0 เนื่องจากฟังก์ชัน verifyCode รองรับ undefined

        // เลือกวิธีการตรวจสอบรหัสตามประเภท
        let result;
        if (type === 'email') {
          result = await verificationService.verifyCode(
            'email',
            identifier,
            code,
            userId
          );
        } else if (type === 'phone') {
          result = await verificationService.verifyCode(
            'phone',
            identifier,
            code,
            userId,
            reference
          );
        } else {
          return res.status(400).json({
            success: false,
            message: "ประเภทการยืนยันไม่ถูกต้อง",
          });
        }

        if (!result.success) {
          return res.status(400).json({
            success: false,
            message: result.message || "รหัสยืนยันไม่ถูกต้องหรือหมดอายุแล้ว",
          });
        }

        return res.status(200).json({
          success: true,
          message: result.message || `ยืนยัน${type === 'email' ? 'อีเมล' : 'เบอร์โทรศัพท์'}เรียบร้อยแล้ว`,
          verified: result.verified || true
        });
      } catch (error) {
        logger.error("เกิดข้อผิดพลาดในการตรวจสอบรหัสยืนยัน:", error);
        return res.status(500).json({
          success: false,
          message: "เกิดข้อผิดพลาดในการตรวจสอบรหัสยืนยัน",
        });
      }
    }
  );
  /**
   * ส่งรหัสยืนยันไปยังอีเมล
   * @route POST /api/verify/email/send
   * @bodyparam email อีเมลที่ต้องการส่งรหัสยืนยัน
   * @bodyparam type ประเภทของรหัสยืนยัน (email, password_reset, two_factor, account_deletion)
   */
  app.post(
    "/api/verify/email/send",
    [
      body("email").isEmail().withMessage("กรุณาระบุอีเมลที่ถูกต้อง"),
      body("type")
        .optional()
        .isIn(["email", "password_reset", "two_factor", "account_deletion"])
        .withMessage("ประเภทของรหัสยืนยันไม่ถูกต้อง"),
    ],
    async (req: Request, res: Response) => {
      try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
          return res.status(400).json({ errors: errors.array() });
        }

        const { email, type = "email" } = req.body;
        const userId = req.user?.id;

        const result = await verificationService.sendEmailVerificationCode(
          email,
          userId,
          type as any
        );

        if (!result.success) {
          return res.status(400).json({ error: result.message });
        }

        return res.status(200).json({ message: result.message });
      } catch (error) {
        logger.error("Error sending email verification:", error);
        return res
          .status(500)
          .json({ error: "เกิดข้อผิดพลาดในการส่งรหัสยืนยัน" });
      }
    }
  );

  /**
   * ตรวจสอบรหัสยืนยันอีเมล
   * @route POST /api/verify/email
   * @bodyparam email อีเมลที่ต้องการตรวจสอบ
   * @bodyparam code รหัสยืนยัน
   * @bodyparam type ประเภทของรหัสยืนยัน (email, password_reset, two_factor, account_deletion)
   */
  app.post(
    "/api/verify/email",
    [
      body("email").isEmail().withMessage("กรุณาระบุอีเมลที่ถูกต้อง"),
      body("code").isString().withMessage("กรุณาระบุรหัสยืนยัน"),
      body("type")
        .optional()
        .isIn(["email", "password_reset", "two_factor", "account_deletion"])
        .withMessage("ประเภทของรหัสยืนยันไม่ถูกต้อง"),
    ],
    async (req: Request, res: Response) => {
      try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
          return res.status(400).json({ errors: errors.array() });
        }

        const { email, code, type = "email" } = req.body;
        const userId = req.user?.id;

        const result = await verificationService.verifyCode(
          type as any,
          email,
          code,
          userId
        );

        if (!result.success) {
          return res.status(400).json({ error: result.message });
        }

        return res.status(200).json({
          message: result.message,
          verified: result.verified,
        });
      } catch (error) {
        logger.error("Error verifying email code:", error);
        return res
          .status(500)
          .json({ error: "เกิดข้อผิดพลาดในการตรวจสอบรหัสยืนยัน" });
      }
    }
  );

  /**
   * ส่ง OTP ไปยังเบอร์โทรศัพท์
   * @route POST /api/verify/phone/send
   * @bodyparam phoneNumber เบอร์โทรศัพท์ที่ต้องการส่ง OTP
   */
  app.post(
    "/api/verify/phone/send",
    [
      body("phoneNumber")
        .matches(/^(0\d{9}|\+66\d{9})$/)
        .withMessage("กรุณาระบุเบอร์โทรศัพท์ที่ถูกต้อง (เช่น 0891234567 หรือ +66891234567)"),
    ],
    async (req: Request, res: Response) => {
      try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
          return res.status(400).json({ errors: errors.array() });
        }

        const { phoneNumber } = req.body;
        const userId = req.user?.id;

        const result = await verificationService.sendPhoneOTP(phoneNumber, userId);

        if (!result.success) {
          return res.status(400).json({ error: result.message });
        }

        return res.status(200).json({
          message: result.message,
          otpRef: result.otpRef
        });
      } catch (error) {
        logger.error("Error sending OTP:", error);
        return res.status(500).json({ error: "เกิดข้อผิดพลาดในการส่ง OTP" });
      }
    }
  );

  /**
   * ตรวจสอบ OTP
   * @route POST /api/verify/phone
   * @bodyparam phoneNumber เบอร์โทรศัพท์ที่ต้องการตรวจสอบ
   * @bodyparam code รหัส OTP
   * @bodyparam otpRef รหัสอ้างอิง OTP (ได้รับจากการส่ง OTP)
   */
  app.post(
    "/api/verify/phone",
    [
      body("phoneNumber")
        .matches(/^(0\d{9}|\+66\d{9})$/)
        .withMessage("กรุณาระบุเบอร์โทรศัพท์ที่ถูกต้อง (เช่น 0891234567 หรือ +66891234567)"),
      body("code").isString().withMessage("กรุณาระบุรหัส OTP"),
      body("otpRef").isString().withMessage("กรุณาระบุรหัสอ้างอิง OTP"),
    ],
    async (req: Request, res: Response) => {
      try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
          return res.status(400).json({ errors: errors.array() });
        }

        const { phoneNumber, code, otpRef } = req.body;
        const userId = req.user?.id;

        logger.info(`API ได้รับคำขอตรวจสอบ OTP: ${code} สำหรับเบอร์ ${phoneNumber}, otpRef: ${otpRef}`);

        const result = await verificationService.verifyCode(
          "phone",
          phoneNumber,
          code,
          userId,
          otpRef
        );

        if (!result.success) {
          return res.status(400).json({ error: result.message });
        }

        return res.status(200).json({
          message: result.message,
          verified: result.verified
        });
      } catch (error) {
        logger.error("Error verifying OTP:", error);
        return res.status(500).json({ error: "เกิดข้อผิดพลาดในการตรวจสอบ OTP" });
      }
    }
  );

  /**
   * ตรวจสอบสถานะการยืนยันของผู้ใช้
   * @route GET /api/verify/status
   */
  app.get(
    "/api/verify/status",
    requireLogin,
    async (req: Request, res: Response) => {
      try {
        // ดึงข้อมูลผู้ใช้
        const user = req.user;
        const userId = user?.id;

        if (!user) {
          return res.status(404).json({ error: "ไม่พบข้อมูลผู้ใช้" });
        }

        return res.status(200).json({
          email: user.email,
          email_verified: user.email_verified || false,
          phone: user.phoneNumber,
          phone_verified: user.phone_verified || false,
          auth_providers: user.auth_providers || []
        });
      } catch (error) {
        logger.error("Error checking verification status:", error);
        return res.status(500).json({ error: "เกิดข้อผิดพลาดในการตรวจสอบสถานะการยืนยัน" });
      }
    }
  );
}