import axios from 'axios';
import { logger } from './logger';
import { storage } from './storage';
import { socialAuthService } from './social-auth-service';

/**
 * LINE Messaging API Service
 * บริการสำหรับส่งข้อความและรับข้อมูลจาก LINE Messaging API
 */
export class LineMessagingService {
  private channelAccessToken: string | null = null;
  private channelSecret: string | null = null;
  private apiBaseUrl = 'https://api.line.me/v2/bot';
  
  /**
   * โหลดการตั้งค่า LINE OA จากฐานข้อมูล
   */
  async loadSettings() {
    try {
      const settings = await socialAuthService.getSocialLoginSettings();
      this.channelAccessToken = settings.lineChannelAccessToken || null;
      this.channelSecret = settings.lineChannelSecret || null;
      
      return {
        isConfigured: !!(this.channelAccessToken && this.channelSecret),
        settings
      };
    } catch (error) {
      logger.error('[LINE Messaging] ไม่สามารถโหลดการตั้งค่า LINE OA:', error);
      return { isConfigured: false, settings: {} };
    }
  }
  
  /**
   * ตรวจสอบว่าบริการ LINE OA พร้อมใช้งานหรือไม่
   */
  async isReady() {
    const { isConfigured } = await this.loadSettings();
    return isConfigured;
  }
  
  /**
   * ส่งข้อความข้อความทั่วไปไปยังผู้ใช้ LINE
   * @param lineUserId LINE User ID ของผู้รับ
   * @param message ข้อความที่ต้องการส่ง
   */
  async sendTextMessage(lineUserId: string, message: string) {
    try {
      await this.loadSettings();
      
      if (!this.channelAccessToken) {
        throw new Error('LINE Channel Access Token ไม่ได้กำหนด');
      }
      
      const response = await axios.post(
        `${this.apiBaseUrl}/message/push`,
        {
          to: lineUserId,
          messages: [
            {
              type: 'text',
              text: message
            }
          ]
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.channelAccessToken}`
          }
        }
      );
      
      return {
        success: true,
        response: response.data
      };
    } catch (error) {
      logger.error('[LINE Messaging] ไม่สามารถส่งข้อความได้:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ'
      };
    }
  }
  
  /**
   * ส่งรูปภาพไปยังผู้ใช้ LINE
   * @param lineUserId LINE User ID ของผู้รับ
   * @param originalContentUrl URL ของรูปภาพขนาดใหญ่ (1:1)
   * @param previewImageUrl URL ของรูปภาพขนาดย่อ (1:1)
   */
  async sendImage(lineUserId: string, originalContentUrl: string, previewImageUrl: string) {
    try {
      await this.loadSettings();
      
      if (!this.channelAccessToken) {
        throw new Error('LINE Channel Access Token ไม่ได้กำหนด');
      }
      
      const response = await axios.post(
        `${this.apiBaseUrl}/message/push`,
        {
          to: lineUserId,
          messages: [
            {
              type: 'image',
              originalContentUrl,
              previewImageUrl
            }
          ]
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.channelAccessToken}`
          }
        }
      );
      
      return {
        success: true,
        response: response.data
      };
    } catch (error) {
      logger.error('[LINE Messaging] ไม่สามารถส่งรูปภาพได้:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ'
      };
    }
  }
  
  /**
   * ส่งการแจ้งเตือนผลการตรวจสอบสลิปผ่าน LINE
   * @param userId ID ผู้ใช้ในระบบ
   * @param verificationId ID การตรวจสอบสลิป
   * @param success สถานะความสำเร็จของการตรวจสอบ
   * @param amount จำนวนเงินในสลิป
   * @param imageUrl URL รูปภาพสลิป (ถ้ามี)
   */
  async sendSlipVerificationResult(
    userId: number, 
    verificationId: number, 
    success: boolean, 
    amount: number, 
    imageUrl?: string
  ) {
    try {
      // ค้นหา LINE User ID ของผู้ใช้
      const externalAuth = await socialAuthService.findExternalAuthByUserId(userId, 'line');
      
      if (!externalAuth || !externalAuth.providerId) {
        logger.warn(`[LINE Messaging] ไม่พบข้อมูล LINE ของผู้ใช้ ID ${userId}`);
        return { success: false, error: 'ไม่พบข้อมูล LINE ของผู้ใช้' };
      }
      
      const lineUserId = externalAuth.providerId;
      
      // สร้างข้อความแจ้งเตือน
      const statusText = success ? '✅ ตรวจสอบสำเร็จ' : '❌ ตรวจสอบไม่สำเร็จ';
      const amountText = new Intl.NumberFormat('th-TH', { style: 'currency', currency: 'THB' }).format(amount);
      
      const message = `
🧾 ผลการตรวจสอบสลิป #${verificationId}
${statusText}
💰 จำนวนเงิน: ${amountText}
⏱️ เวลา: ${new Date().toLocaleString('th-TH')}
      `.trim();
      
      // ส่งข้อความแจ้งเตือน
      const textResult = await this.sendTextMessage(lineUserId, message);
      
      // ถ้ามีรูปภาพและการส่งข้อความสำเร็จ ให้ส่งรูปภาพด้วย
      if (imageUrl && textResult.success) {
        await this.sendImage(lineUserId, imageUrl, imageUrl);
      }
      
      return textResult;
    } catch (error) {
      logger.error('[LINE Messaging] ไม่สามารถส่งผลการตรวจสอบสลิปได้:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ'
      };
    }
  }
  
  /**
   * แปลง LINE User ID เป็น userId ในระบบ
   * @param lineUserId LINE User ID
   * @returns User ID ในระบบ หรือ null ถ้าไม่พบ
   */
  async getUserIdFromLineId(lineUserId: string): Promise<number | null> {
    try {
      const externalAuth = await socialAuthService.findUserByExternalAuth('line', lineUserId);
      return externalAuth?.id || null;
    } catch (error) {
      logger.error('[LINE Messaging] ไม่สามารถแปลง LINE User ID เป็น User ID ได้:', error);
      return null;
    }
  }
  
  /**
   * ส่งข้อมูลยอดเครดิตคงเหลือผ่าน LINE
   * @param userId User ID ในระบบ
   */
  async sendCreditBalance(userId: number) {
    try {
      // ค้นหา LINE User ID ของผู้ใช้
      const externalAuth = await socialAuthService.findExternalAuthByUserId(userId, 'line');
      
      if (!externalAuth || !externalAuth.providerId) {
        logger.warn(`[LINE Messaging] ไม่พบข้อมูล LINE ของผู้ใช้ ID ${userId}`);
        return { success: false, error: 'ไม่พบข้อมูล LINE ของผู้ใช้' };
      }
      
      const lineUserId = externalAuth.providerId;
      
      // ดึงข้อมูลเครดิตจากระบบ
      const userCredit = await storage.getUserCredit(userId);
      const creditAmount = userCredit?.credit || 0;
      const formattedCredit = new Intl.NumberFormat('th-TH').format(creditAmount);
      
      // สร้างข้อความแจ้งเตือน
      const message = `
💰 ยอดเครดิตคงเหลือ
จำนวน: ${formattedCredit} เครดิต
เวลาอัปเดตล่าสุด: ${new Date().toLocaleString('th-TH')}
      `.trim();
      
      // ส่งข้อความแจ้งเตือน
      return await this.sendTextMessage(lineUserId, message);
    } catch (error) {
      logger.error('[LINE Messaging] ไม่สามารถส่งข้อมูลเครดิตได้:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ'
      };
    }
  }
}

export const lineMessagingService = new LineMessagingService();