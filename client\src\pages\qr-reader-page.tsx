import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';
import { AlertCircle, Check, Upload } from 'lucide-react';
import jsQR from 'jsqr';

/**
 * หน้าสำหรับอ่าน QR Code โดยเฉพาะ
 * รองรับการอัปโหลดรูปภาพและแสดงผลการอ่าน QR Code
 */
export default function QRReaderPage() {
  const { toast } = useToast();
  const [file, setFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [qrResult, setQrResult] = useState<string | null>(null);
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [detectMethod, setDetectMethod] = useState<'jsqr' | 'zxing'>('jsqr');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const showError = (message: string) => {
    setStatus('error');
    setErrorMessage(message);
    toast({
      variant: 'destructive',
      title: 'ข้อผิดพลาด',
      description: message,
    });
  };

  const detectQRCodeWithJsQR = async (imageUrl: string): Promise<string | null> => {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        
        if (!context) {
          resolve(null);
          return;
        }
        
        canvas.width = img.width;
        canvas.height = img.height;
        context.drawImage(img, 0, 0);
        
        const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
        const code = jsQR(imageData.data, imageData.width, imageData.height);
        
        if (code) {
          resolve(code.data);
        } else {
          resolve(null);
        }
      };
      
      img.onerror = () => {
        resolve(null);
      };
      
      img.src = imageUrl;
    });
  };

  const detectQRCodeWithZXing = async (imageUrl: string): Promise<string | null> => {
    try {
      // ใช้ dynamic import เพื่อเรียกใช้ ZXing เมื่อต้องการ
      const { BrowserQRCodeReader } = await import('@zxing/browser');
      const reader = new BrowserQRCodeReader();
      
      const img = document.createElement('img');
      img.src = imageUrl;
      document.body.appendChild(img);
      
      try {
        const result = await reader.decodeFromImageElement(img);
        document.body.removeChild(img);
        return result.getText();
      } catch (error) {
        console.error('ZXing Error:', error);
        document.body.removeChild(img);
        return null;
      }
    } catch (error) {
      console.error('Failed to load ZXing:', error);
      return null;
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (!selectedFile) return;
    
    setFile(selectedFile);
    setStatus('loading');
    setQrResult(null);
    setErrorMessage('');
    
    // สร้าง URL สำหรับแสดงตัวอย่างรูปภาพ
    const previewUrl = URL.createObjectURL(selectedFile);
    setImagePreview(previewUrl);
    
    // ตรวจสอบ QR Code
    detectQRCode(previewUrl);
  };
  
  const detectQRCode = async (imageUrl: string) => {
    try {
      setStatus('loading');
      let result: string | null = null;
      
      if (detectMethod === 'jsqr') {
        result = await detectQRCodeWithJsQR(imageUrl);
      } else if (detectMethod === 'zxing') {
        result = await detectQRCodeWithZXing(imageUrl);
      }
      
      if (result) {
        setQrResult(result);
        setStatus('success');
        toast({
          title: 'ตรวจพบ QR Code',
          description: 'ระบบอ่าน QR Code สำเร็จ',
        });
      } else {
        showError('ไม่พบ QR Code ในรูปภาพ');
      }
    } catch (error) {
      console.error('Error detecting QR code:', error);
      showError('เกิดข้อผิดพลาดในการอ่าน QR Code');
    }
  };
  
  const handleRetry = () => {
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    setFile(null);
    setImagePreview(null);
    setQrResult(null);
    setStatus('idle');
    setErrorMessage('');
  };
  
  const handleMethodChange = (value: string) => {
    setDetectMethod(value as 'jsqr' | 'zxing');
    if (imagePreview) {
      // ตรวจสอบใหม่ด้วยวิธีที่เลือก
      detectQRCode(imagePreview);
    }
  };

  return (
    <div className="container mx-auto py-10">
      <div className="max-w-5xl mx-auto">
        <Card className="shadow-lg">
          <CardHeader className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white">
            <CardTitle className="text-2xl font-bold">
              เครื่องมือตรวจสอบ QR Code
            </CardTitle>
            <CardDescription className="text-blue-100">
              ใช้สำหรับตรวจสอบว่ารูปภาพมี QR Code หรือไม่ ก่อนส่งไปตรวจสอบผ่าน API
            </CardDescription>
          </CardHeader>
          
          <CardContent className="p-6">
            <Tabs defaultValue="jsqr" onValueChange={handleMethodChange}>
              <TabsList className="grid w-full grid-cols-2 mb-6">
                <TabsTrigger value="jsqr">วิธีที่ 1: jsQR</TabsTrigger>
                <TabsTrigger value="zxing">วิธีที่ 2: ZXing</TabsTrigger>
              </TabsList>
              
              <TabsContent value="jsqr" className="mt-0">
                <div className="bg-blue-50 p-4 rounded-md mb-6">
                  <h3 className="font-semibold text-blue-800">เกี่ยวกับ jsQR</h3>
                  <p className="text-blue-700 text-sm mt-1">
                    ไลบรารีประมวลผลภาพเพื่ออ่าน QR Code ในเบราว์เซอร์ ทำงานได้ดีกับรูปภาพที่มีความคมชัดสูง
                    และมี QR Code ที่ชัดเจน
                  </p>
                </div>
              </TabsContent>
              
              <TabsContent value="zxing" className="mt-0">
                <div className="bg-indigo-50 p-4 rounded-md mb-6">
                  <h3 className="font-semibold text-indigo-800">เกี่ยวกับ ZXing</h3>
                  <p className="text-indigo-700 text-sm mt-1">
                    ไลบรารีประมวลผลบาร์โค้ดที่รองรับหลายรูปแบบ รวมถึง QR Code มีความสามารถในการอ่านรูปภาพ
                    ที่มีคุณภาพต่ำหรือมีข้อผิดพลาดได้ดี
                  </p>
                </div>
              </TabsContent>
            </Tabs>

            <div className="space-y-6">
              <div>
                <Label htmlFor="qr-image" className="text-lg font-medium">
                  อัปโหลดรูปภาพที่มี QR Code
                </Label>
                <div className="mt-2">
                  <Input
                    id="qr-image"
                    type="file"
                    accept="image/*"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    className="cursor-pointer"
                  />
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  รองรับไฟล์ JPEG, PNG และรูปภาพทั่วไป
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {imagePreview && (
                  <div className="space-y-3">
                    <h3 className="font-medium">รูปภาพ</h3>
                    <div className="border rounded-md overflow-hidden">
                      <img
                        src={imagePreview}
                        alt="ตัวอย่างรูปภาพ"
                        className="w-full object-contain"
                        style={{ maxHeight: '300px' }}
                      />
                    </div>
                  </div>
                )}

                <div className="space-y-3">
                  <h3 className="font-medium">ผลการอ่าน QR Code</h3>
                  <div className="min-h-[100px] border rounded-md p-4 bg-gray-50">
                    {status === 'loading' && (
                      <div className="flex items-center justify-center h-full">
                        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
                      </div>
                    )}

                    {status === 'success' && qrResult && (
                      <div className="space-y-3">
                        <div className="flex items-center space-x-2 text-green-600">
                          <Check className="h-5 w-5" />
                          <span className="font-medium">ตรวจพบ QR Code</span>
                        </div>
                        <ScrollArea className="h-[200px]">
                          <div className="whitespace-pre-wrap break-all bg-white p-3 rounded border">
                            {qrResult}
                          </div>
                        </ScrollArea>
                      </div>
                    )}

                    {status === 'error' && (
                      <div className="flex items-start space-x-2 text-red-600">
                        <AlertCircle className="h-5 w-5 mt-0.5" />
                        <div>
                          <p className="font-medium">การอ่าน QR Code ล้มเหลว</p>
                          <p className="text-sm">{errorMessage}</p>
                        </div>
                      </div>
                    )}

                    {status === 'idle' && !imagePreview && (
                      <div className="flex flex-col items-center justify-center h-full text-gray-500 space-y-2">
                        <Upload className="h-8 w-8" />
                        <p>อัปโหลดรูปภาพเพื่อตรวจสอบ QR Code</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {status !== 'idle' && (
                <div className="flex justify-end">
                  <Button variant="outline" onClick={handleRetry}>
                    ตรวจสอบรูปใหม่
                  </Button>
                </div>
              )}

              {status === 'success' && (
                <Alert className="bg-green-50 border-green-200">
                  <Check className="h-4 w-4 text-green-600" />
                  <AlertTitle className="text-green-800">การตรวจสอบสำเร็จ</AlertTitle>
                  <AlertDescription className="text-green-700">
                    รูปภาพนี้มี QR Code และสามารถอ่านข้อมูลได้ สามารถส่งไปตรวจสอบยัง API ต่อไปได้
                  </AlertDescription>
                </Alert>
              )}

              {status === 'error' && (
                <Alert className="bg-red-50 border-red-200">
                  <AlertCircle className="h-4 w-4 text-red-600" />
                  <AlertTitle className="text-red-800">ไม่พบ QR Code</AlertTitle>
                  <AlertDescription className="text-red-700">
                    ไม่พบ QR Code ในรูปภาพนี้ ไม่ควรส่งไปตรวจสอบยัง API หากไม่ใช่รูปภาพสลิปที่มี QR Code
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </CardContent>
          
          <CardFooter className="bg-gray-50 border-t p-6">
            <div className="w-full">
              <h3 className="font-semibold mb-2">คำแนะนำการใช้งาน</h3>
              <p className="text-sm text-gray-600">
                1. อัปโหลดรูปภาพสลิปที่มี QR Code ที่ต้องการตรวจสอบ<br />
                2. ระบบจะทำการอ่าน QR Code และแสดงผลลัพธ์<br />
                3. หากพบ QR Code แสดงว่ารูปภาพสามารถส่งไปตรวจสอบผ่าน API ได้<br />
                4. หากไม่พบ QR Code ควรตรวจสอบว่าเป็นรูปภาพสลิปที่ถูกต้องหรือไม่
              </p>
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}