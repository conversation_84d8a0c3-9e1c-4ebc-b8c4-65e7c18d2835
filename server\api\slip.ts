import type { Express, Request, Response, NextFunction } from "express";
import { storage } from "../storage";
import axios from "axios";
import { upload } from "../routes";
import { SlipVerification } from "@shared/schema";
import { slipService } from "../slip-service";
import { detectQRCode } from "../qr-detector-new";
import fs from "fs";
import path from "path";
import { promisify } from "util";
import { requireVerified } from "../middleware/verification-middleware";

// ตรวจสอบว่าผู้ใช้งานมีแพ็กเกจที่ใช้งานได้และมีจำนวนคำขอเหลืออยู่หรือไม่
async function checkUserPackage(req: Request, res: Response, next: NextFunction) {
  if (!req.user) {
    return res.status(401).json({ message: "กรุณาเข้าสู่ระบบก่อนใช้งาน" });
  }

  try {
    // ดึงข้อมูลแพ็กเกจที่ใช้งานอยู่
    const userPackage = await storage.getUserActivePackage(req.user.id);
    
    if (!userPackage) {
      return res.status(403).json({ message: "ไม่พบแพ็กเกจที่ใช้งานได้ กรุณาสมัครแพ็กเกจก่อนใช้งาน" });
    }
    
    // ตรวจสอบว่าเกินโควต้าหรือไม่
    if (userPackage.requestsUsed >= userPackage.package.requestsLimit) {
      // ระบบใหม่: ถ้าเกินโควต้า ให้ตรวจสอบว่าผู้ใช้มีเครดิตเพียงพอไหม
      
      // ใช้ค่า creditPerVerification จากฐานข้อมูล หากไม่มีให้คำนวณใหม่
      let pricePerRequest: number;
      
      if (userPackage.package.creditPerVerification !== null && 
          userPackage.package.creditPerVerification !== undefined) {
        pricePerRequest = userPackage.package.creditPerVerification;
      } else {
        // คำนวณราคาต่อครั้ง = ราคาแพ็กเกจ / จำนวนครั้งที่ใช้งานได้
        pricePerRequest = userPackage.package.price / userPackage.package.requestsLimit;
        
        // บันทึกค่านี้กลับเข้าฐานข้อมูล
        await storage.updatePackage(userPackage.package.id, {
          creditPerVerification: pricePerRequest,
          updatedAt: new Date()
        });
      }
      
      // ดึงข้อมูลเครดิตของผู้ใช้
      const userCredit = await storage.getUserCredit(req.user.id);
      
      // ถ้าเครดิตไม่พอ
      if (userCredit < pricePerRequest) {
        return res.status(403).json({ 
          message: `คุณได้ใช้งานครบตามจำนวนที่กำหนดในแพ็กเกจแล้ว และมีเครดิตไม่เพียงพอสำหรับการตรวจสอบเพิ่มเติม (${pricePerRequest.toFixed(2)} บาท/ครั้ง) กรุณาเติมเครดิตหรืออัปเกรดแพ็กเกจ`,
          overQuota: true,
          insufficientCredit: true,
          creditNeeded: pricePerRequest,
          currentCredit: userCredit
        });
      }
      
      // ถ้าเครดิตพอ ให้แจ้งว่าจะหักเครดิต
      req.userPackage = {
        ...userPackage,
        overQuota: true,
        pricePerRequest: pricePerRequest
      };
      next();
    } else {
      // ถ้ายังไม่เกินโควต้า ให้ใช้งานปกติ
      req.userPackage = userPackage;
      next();
    }
  } catch (error) {
    console.error("Error checking user package:", error);
    res.status(500).json({ message: "เกิดข้อผิดพลาดในการตรวจสอบแพ็กเกจ กรุณาลองใหม่อีกครั้ง" });
  }
}

// ฟังก์ชันเพื่อบันทึกไฟล์ภาพสลิป
const writeFileAsync = promisify(fs.writeFile);
const mkdir = promisify(fs.mkdir);

// ฟังก์ชันเพื่อบันทึกไฟล์ภาพสลิปและคืนค่าพาธของไฟล์
async function saveSlipImage(buffer: Buffer, userId: number, verificationId: number): Promise<string> {
  try {
    // ตรวจสอบว่าโฟลเดอร์ uploads/slips มีอยู่หรือไม่
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads', 'slips');
    try {
      await mkdir(uploadsDir, { recursive: true });
    } catch (err) {
      // โฟลเดอร์มีอยู่แล้ว หรือเกิดข้อผิดพลาดอื่น ๆ
      console.log('Directory exists or error creating directory:', err);
    }

    // สร้างชื่อไฟล์แบบไม่ซ้ำกัน
    const timestamp = Date.now();
    const filename = `slip-${userId}-${verificationId}-${timestamp}.png`;
    const filepath = path.join(uploadsDir, filename);
    
    // บันทึกไฟล์
    await writeFileAsync(filepath, buffer);
    
    // คืนค่าพาธสัมพัทธ์สำหรับบันทึกในฐานข้อมูล
    return `/uploads/slips/${filename}`;
  } catch (error) {
    console.error('Error saving slip image:', error);
    throw error;
  }
}

export function setupSlipRoutes(app: Express) {
  // Endpoint สำหรับดึงรูปภาพสลิปจาก ID (จะใช้เวอร์ชันที่สมบูรณ์กว่าด้านล่าง)
  // *** รอเวอร์ชันใหม่ด้านล่าง ***

  // Endpoint สำหรับตรวจสอบสลิปธนาคารด้วยภาพ
  app.post("/api/v1/verify", requireVerified, checkUserPackage, upload.single('file'), async (req, res) => {
    if (!req.file) {
      return res.status(400).json({ message: "กรุณาอัปโหลดไฟล์ภาพสลิป" });
    }
    
    try {
      // สร้างข้อมูลสำหรับส่งไปยัง easyslip API
      // ใช้ slipService ในการจัดการกับไฟล์โดยตรง ไม่ต้องสร้าง FormData เอง
      
      try {
        console.log("File info:", req.file);
        // ตรวจสอบว่า req.file มี buffer หรือไม่
        if (!req.file.buffer) {
          throw new Error("ไม่พบข้อมูลไฟล์ในคำขอ");
        }
        
        // ตรวจสอบว่ามี QR code หรือไม่ และอ่านข้อมูล QR code จากรูปภาพ
        let qrData = null;
        try {
          const qrResult = await detectQRCode(req.file.buffer);
          if (qrResult.hasQRCode && qrResult.qrData) {
            qrData = qrResult.qrData;
            console.log(`พบและอ่านข้อมูล QR Code ได้: ${qrData.substring(0, 40)}${qrData.length > 40 ? '...' : ''}`);
          }
        } catch (qrError) {
          console.error('เกิดข้อผิดพลาดในการอ่านข้อมูล QR Code:', qrError);
        }
        
        // ใช้ slipService.verifySlip ที่จะเลือก API ตาม system settings และตรวจสอบสลิปซ้ำ
        const data = await slipService.verifySlip(req.user!.id, req.file.buffer, req.file.originalname);
        
        // ถ้าเป็นสลิปซ้ำ จะได้รับสถานะ 409 จาก verifySlip
        if (data.status === 409) {
          return res.status(409).json(data);
        }
        
        // สร้าง verification record หลังจากตรวจสอบแล้วว่าไม่ใช่สลิปซ้ำ
        const verificationRecord: SlipVerification = await storage.createSlipVerification({
          userId: req.user!.id,
          status: 'pending',
          verificationSource: 'web', // ระบุแหล่งที่มาเป็นเว็บไซต์
          responseData: JSON.stringify({ message: 'กำลังประมวลผล' }),
          qrData: qrData // บันทึก qrData ทันที
        });
        
        // ส่งข้อมูลการตรวจสอบใหม่ผ่าน WebSocket (สถานะ pending เริ่มต้น)
        if (typeof global.broadcastWebsocketEvent === 'function') {
          try {
            global.broadcastWebsocketEvent('dashboard', 'new_verification', {
              id: verificationRecord.id,
              status: 'pending',
              transactionRef: 'กำลังประมวลผล',
              amount: 0,
              sender: 'รอผลการตรวจสอบ',
              receiver: 'รอผลการตรวจสอบ',
              bankName: 'รอผลการตรวจสอบ',
              transactionDate: new Date().toISOString(),
              createdAt: new Date().toISOString(),
              source: 'web'
            });
          } catch (error) {
            console.error('เกิดข้อผิดพลาดในการส่งข้อมูล WebSocket:', error);
          }
        }
        
        // บันทึกไฟล์ภาพสลิปและได้รับพาธ
        const imagePath = await saveSlipImage(req.file.buffer, req.user!.id, verificationRecord.id);
        
        // อัปเดต imagePath ในฐานข้อมูล
        await storage.updateSlipVerification(verificationRecord.id, {
          imagePath
        });
        
        // อัปเดตข้อมูลการตรวจสอบเมื่อสำเร็จ
        
        if (data.status === 200) {
          // สร้างข้อมูลที่จะอัปเดต
          const updateData: Partial<SlipVerification> = {
            status: 'success',
            transactionRef: data.data?.transRef,
            bankName: data.data?.sender?.bank?.name || '',
            amount: data.data?.amount?.amount || 0,
            sender: data.data?.sender?.account?.name?.th || '',
            receiver: data.data?.receiver?.account?.name?.th || '',
            transactionDate: data.data?.date ? new Date(data.data.date) : null,
            responseData: JSON.stringify(data)
          };
          
          // ถ้ามีข้อมูล QR code ให้บันทึกด้วย
          if (qrData) {
            updateData.qrData = qrData;
          }
          
          const updatedVerification = await storage.updateSlipVerification(verificationRecord.id, updateData);
          
          // ส่งข้อมูลการตรวจสอบใหม่ผ่าน WebSocket (ถ้ามีฟังก์ชั่นอยู่)
          if (typeof global.broadcastWebsocketEvent === 'function') {
            try {
              const verification = await storage.getSlipVerification(verificationRecord.id);
              if (verification) {
                global.broadcastWebsocketEvent('dashboard', 'new_verification', {
                  id: verification.id,
                  transactionRef: verification.transactionRef,
                  bankName: verification.bankName,
                  amount: verification.amount,
                  sender: verification.sender,
                  receiver: verification.receiver,
                  transactionDate: verification.transactionDate,
                  status: verification.status,
                  createdAt: new Date(),
                  source: 'web'
                });
                console.log('Broadcast verification update via WebSocket');
              }
            } catch (err) {
              console.error('Error broadcasting WebSocket event:', err);
            }
          }
          
          // เพิ่มจำนวนการใช้งาน API หรือหักเครดิตถ้าเกินโควต้า
          if (req.userPackage!.overQuota) {
            // กรณีเกินโควต้า ให้หักเครดิตแทน
            const pricePerRequest = req.userPackage!.pricePerRequest;
            await storage.addUserCredit(req.user!.id, -pricePerRequest); // ใช้ค่าลบเพื่อหักเงิน
            
            // อัพเดตข้อมูลว่าใช้เครดิตในการตรวจสอบ
            await storage.updateSlipVerification(verificationRecord.id, {
              creditUsed: pricePerRequest,
              usedCredit: true
            });
            
            return res.status(200).json({
              ...data,
              overQuota: true,
              creditDeducted: pricePerRequest,
              message: `ใช้งานเกินโควต้า ระบบหักเครดิต ${pricePerRequest.toFixed(2)} บาท`
            });
          } else {
            // กรณีปกติ ให้เพิ่มจำนวนครั้งที่ใช้งาน
            await storage.incrementRequestsUsed(req.userPackage!.id);
            return res.status(200).json(data);
          }
        } else {
          // กรณีที่มีการตอบกลับแต่ไม่สำเร็จ
          await storage.updateSlipVerification(verificationRecord.id, {
            status: 'failed',
            responseData: JSON.stringify(data)
          });
          
          return res.status(data.status).json(data);
        }
      } catch (error: any) {
        // กรณีเกิดข้อผิดพลาดในการเรียกใช้ API
        console.error('Error calling slip verification API:', error);
        
        // ตรวจสอบว่ามีตัวแปร verificationRecord หรือไม่
        // อาจจะยังไม่มี เพราะอาจเกิดข้อผิดพลาดก่อนจะสร้าง verification record
        let errorResponse = {
          status: error.response?.status || 500,
          message: error.response?.data?.message || 'เกิดข้อผิดพลาดในการตรวจสอบสลิป'
        };
        
        // ถ้ามีการสร้าง verification record แล้ว ให้อัปเดตสถานะเป็น failed
        if (typeof verificationRecord !== 'undefined') {
          await storage.updateSlipVerification(verificationRecord.id, {
            status: 'failed',
            responseData: JSON.stringify(errorResponse)
          });
        }
        
        return res.status(errorResponse.status).json(errorResponse);
      }
    } catch (error) {
      console.error('Server error during verification:', error);
      return res.status(500).json({ 
        status: 500, 
        message: 'เกิดข้อผิดพลาดบนเซิร์ฟเวอร์ กรุณาลองใหม่อีกครั้ง'
      });
    }
  });
  
  // ดึงประวัติการตรวจสอบสลิปของผู้ใช้
  app.get("/api/verifications", requireVerified, async (req, res) => {
    try {
      const verifications = await storage.listUserSlipVerifications(req.user!.id);
      res.status(200).json(verifications);
    } catch (error) {
      console.error('Error fetching verifications:', error);
      res.status(500).json({ message: 'เกิดข้อผิดพลาดในการดึงข้อมูลประวัติการตรวจสอบ' });
    }
  });
  
  // ดึงข้อมูลสถิติการใช้งานของผู้ใช้
  app.get("/api/stats", requireVerified, async (req, res) => {
    try {
      const stats = await storage.getUserStats(req.user!.id);
      
      // ดึงข้อมูลแพ็กเกจที่ใช้งานอยู่
      const activePackage = await storage.getUserActivePackage(req.user!.id);
      
      // ถ้ามีแพ็กเกจที่ใช้งานอยู่ ให้ตรวจสอบและคำนวณค่า creditPerVerification ถ้ายังไม่มี
      if (activePackage && (activePackage.package.creditPerVerification === null || 
          activePackage.package.creditPerVerification === undefined)) {
        const price = activePackage.package.price || 0;
        const requestsLimit = activePackage.package.requestsLimit || 1;
        const creditPerVerification = price / requestsLimit;
        
        // อัพเดตค่าในฐานข้อมูล
        await storage.updatePackage(activePackage.package.id, {
          creditPerVerification,
          updatedAt: new Date()
        });
        
        // อัพเดตค่าในออบเจ็กต์ activePackage
        activePackage.package.creditPerVerification = creditPerVerification;
      }
      
      const response = {
        ...stats,
        activePackage: activePackage || null,
        requestsRemaining: activePackage 
          ? activePackage.package.requestsLimit - activePackage.requestsUsed 
          : 0,
        creditPerVerification: activePackage
          ? activePackage.package.creditPerVerification
          : null
      };
      
      res.status(200).json(response);
    } catch (error) {
      console.error('Error fetching user stats:', error);
      res.status(500).json({ message: 'เกิดข้อผิดพลาดในการดึงข้อมูลสถิติการใช้งาน' });
    }
  });
  
  // ดึงข้อมูลรูปภาพสลิป (รวมความสามารถจากทั้งสองเวอร์ชัน)
  app.get("/api/slip-image/:id", requireVerified, async (req, res) => {
    try {
      const verificationId = parseInt(req.params.id);
      if (isNaN(verificationId)) {
        return res.status(400).json({ message: 'รหัสการตรวจสอบไม่ถูกต้อง' });
      }
      
      // ตรวจสอบว่าเป็นการตรวจสอบของผู้ใช้นี้หรือไม่
      const verification = await storage.getSlipVerification(verificationId);
      
      if (!verification) {
        return res.status(404).json({ message: 'ไม่พบข้อมูลการตรวจสอบ' });
      }
      
      // ตรวจสอบสิทธิ์ - เฉพาะเจ้าของสลิปหรือแอดมินเท่านั้น
      if (verification.userId !== req.user!.id && !req.user!.isAdmin) {
        return res.status(403).json({ message: 'คุณไม่มีสิทธิ์เข้าถึงข้อมูลนี้' });
      }
      
      // ตรวจสอบว่ามาจาก API หรือไม่
      const isFromApi = verification.apiKeyId !== null;
      
      // ตรวจสอบ imagePath ในข้อมูล verification
      if (verification.imagePath) {
        console.log('Found imagePath:', verification.imagePath);
        return res.status(200).json({ 
          imagePath: verification.imagePath,
          isFromApi: isFromApi
        });
      }
      
      // ถ้าไม่มี imagePath ให้ตรวจสอบ response_data เพื่อหา filePath ที่มาจาก API
      if (verification.responseData) {
        try {
          const responseData = JSON.parse(verification.responseData);
          if (responseData.filePath) {
            console.log('Found filePath in responseData:', responseData.filePath);
            
            // ตรวจสอบว่าไฟล์มีอยู่จริงหรือไม่
            const filePath = responseData.filePath;
            const absoluteFilePath = path.join(process.cwd(), filePath);
            
            if (fs.existsSync(absoluteFilePath)) {
              // คืนค่า filePath ที่เป็น URL path ไม่ใช่ filesystem path
              const relativePath = `/${filePath}`;
              return res.status(200).json({ 
                imagePath: relativePath,
                isFromApi: isFromApi
              });
            } else {
              console.error('File does not exist:', absoluteFilePath);
            }
          }
          
          // ถ้าเป็นข้อมูลจาก API อาจจะมีพาธรูปภาพในรูปแบบอื่น
          if (responseData.data && responseData.data.imageUrl) {
            console.log('Found imageUrl in API response:', responseData.data.imageUrl);
            return res.status(200).json({ 
              imagePath: responseData.data.imageUrl,
              isFromApi: isFromApi,
              isExternalUrl: true
            });
          }
        } catch (e) {
          console.error('Error parsing responseData:', e);
        }
      }
      
      // ถ้าไม่พบรูปภาพทั้งใน imagePath และ responseData
      return res.status(404).json({ message: 'ไม่พบรูปภาพสลิปสำหรับการตรวจสอบนี้' });
    } catch (error) {
      console.error('Error fetching slip image:', error);
      res.status(500).json({ message: 'เกิดข้อผิดพลาดในการดึงข้อมูลรูปภาพสลิป' });
    }
  });
}
