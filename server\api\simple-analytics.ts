import { Request, Response } from 'express';
import { storage } from '../storage';
import { format, subDays, isAfter, isBefore, parseISO } from 'date-fns';

/**
 * API สำหรับการค้นหาขั้นสูงแบบง่าย
 * รองรับการวิเคราะห์ข้อมูลการทำธุรกรรมของผู้ใช้ในรูปแบบที่เข้าใจง่าย
 */
export async function analyzeUserData(req: Request, res: Response) {
  if (!req.user) {
    return res.status(401).json({ success: false, message: 'กรุณาเข้าสู่ระบบ' });
  }

  // ตรวจสอบสิทธิ์แอดมินสำหรับการค้นหาขั้นสูงระดับเทพเจ้า
  const isAdmin = req.user.isAdmin || req.user.role === 'admin';
  console.log("ผู้ใช้เป็นแอดมิน:", isAdmin);

  try {
    const { startDate, endDate, includeDetails = false, username = null } = req.body;
    const userId = isAdmin && username ? null : req.user.id; // แอดมินสามารถดูข้อมูลของผู้ใช้ทุกคนได้

    if (!startDate || !endDate) {
      return res.status(400).json({ success: false, message: 'กรุณาระบุช่วงวันที่ต้องการค้นหา' });
    }

    // ดึงข้อมูลการทำธุรกรรมทั้งหมดของผู้ใช้
    let verifications;
    if (isAdmin && !userId) {
      // ถ้าเป็นแอดมินและไม่ได้ระบุ userId ให้ดึงข้อมูลทั้งหมด
      console.log("แอดมินกำลังดึงข้อมูลทั้งหมดในระบบ");
      verifications = await storage.listAllSlipVerifications();
    } else {
      // ถ้าเป็นผู้ใช้ทั่วไปหรือแอดมินที่ต้องการดูข้อมูลของตัวเอง
      console.log("กำลังดึงข้อมูลเฉพาะผู้ใช้:", userId);
      verifications = await storage.listUserSlipVerifications(userId);
    }
    
    // กรองข้อมูลตามช่วงวันที่
    const startDateTime = new Date(startDate);
    const endDateTime = new Date(endDate);
    
    // ตรวจสอบว่าวันที่ถูกต้องหรือไม่
    if (isNaN(startDateTime.getTime()) || isNaN(endDateTime.getTime())) {
      return res.status(400).json({ success: false, message: 'รูปแบบวันที่ไม่ถูกต้อง' });
    }
    
    // ปรับ endDateTime ให้เป็นสิ้นวัน
    endDateTime.setHours(23, 59, 59, 999);
    
    // กรองข้อมูลตามช่วงวันที่
    const filteredVerifications = verifications.filter((v: any) => {
      const vDate = new Date(v.createdAt);
      return isAfter(vDate, startDateTime) && isBefore(vDate, endDateTime);
    });
    
    // เรียงข้อมูลตามวันที่ล่าสุด
    filteredVerifications.sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    
    // คำนวณข้อมูลสรุป
    const totalTransactions = filteredVerifications.length;
    const totalAmount = filteredVerifications.reduce((sum: number, v: any) => sum + (v.amount || 0), 0);
    const avgAmount = totalTransactions > 0 ? totalAmount / totalTransactions : 0;
    
    // วิเคราะห์การกระจายตามธนาคาร
    const bankMap = new Map();
    
    filteredVerifications.forEach((v: any) => {
      const bankName = v.bankName || 'ไม่ระบุ';
      
      if (!bankMap.has(bankName)) {
        bankMap.set(bankName, {
          name: bankName,
          count: 0,
          totalAmount: 0,
          percentage: 0
        });
      }
      
      const bankData = bankMap.get(bankName);
      bankData.count += 1;
      bankData.totalAmount += (v.amount || 0);
    });
    
    // คำนวณเปอร์เซ็นต์
    const bankDistribution = Array.from(bankMap.values()).map(bank => {
      bank.percentage = (bank.count / totalTransactions) * 100;
      return bank;
    });
    
    // เรียงลำดับตามจำนวนรายการ
    bankDistribution.sort((a, b) => b.count - a.count);
    
    // วิเคราะห์การกระจายตามช่วงเวลา
    const timeMap = new Map();
    const hours = [
      '00:00-03:59', '04:00-07:59', '08:00-11:59', 
      '12:00-15:59', '16:00-19:59', '20:00-23:59'
    ];
    
    // สร้างช่วงเวลาเริ่มต้น
    hours.forEach(hour => {
      timeMap.set(hour, {
        time: hour,
        count: 0,
        totalAmount: 0,
        percentage: 0
      });
    });
    
    filteredVerifications.forEach(v => {
      const date = new Date(v.createdAt);
      const hour = date.getHours();
      
      let timeKey;
      if (hour < 4) timeKey = '00:00-03:59';
      else if (hour < 8) timeKey = '04:00-07:59';
      else if (hour < 12) timeKey = '08:00-11:59';
      else if (hour < 16) timeKey = '12:00-15:59';
      else if (hour < 20) timeKey = '16:00-19:59';
      else timeKey = '20:00-23:59';
      
      const timeData = timeMap.get(timeKey);
      timeData.count += 1;
      timeData.totalAmount += (v.amount || 0);
    });
    
    // คำนวณเปอร์เซ็นต์
    const timeDistribution = Array.from(timeMap.values()).map(time => {
      time.percentage = (time.count / totalTransactions) * 100;
      return time;
    });
    
    // วิเคราะห์ผู้รับเงินบ่อยที่สุด
    const receiverMap = new Map();
    
    filteredVerifications.forEach(v => {
      const receiverName = v.receiver || 'ไม่ระบุ';
      
      if (!receiverMap.has(receiverName)) {
        receiverMap.set(receiverName, {
          name: receiverName,
          count: 0,
          totalAmount: 0,
          percentage: 0
        });
      }
      
      const receiverData = receiverMap.get(receiverName);
      receiverData.count += 1;
      receiverData.totalAmount += (v.amount || 0);
    });
    
    // คำนวณเปอร์เซ็นต์
    const topReceivers = Array.from(receiverMap.values()).map(receiver => {
      receiver.percentage = (receiver.count / totalTransactions) * 100;
      return receiver;
    });
    
    // เรียงลำดับตามจำนวนรายการ
    topReceivers.sort((a, b) => b.count - a.count);
    
    // จำกัดจำนวนตัวอย่าง
    const top10Receivers = topReceivers.slice(0, 10);
    
    // วิเคราะห์แนวโน้มตามเวลา (timeline)
    const timelineMap = new Map();
    
    // สร้างวันที่สำหรับ timeline
    let currentDate = new Date(startDateTime);
    while (currentDate <= endDateTime) {
      const dateKey = format(currentDate, 'yyyy-MM-dd');
      timelineMap.set(dateKey, {
        date: dateKey,
        count: 0,
        amount: 0
      });
      
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    filteredVerifications.forEach(v => {
      const date = new Date(v.createdAt);
      const dateKey = format(date, 'yyyy-MM-dd');
      
      if (timelineMap.has(dateKey)) {
        const timelineData = timelineMap.get(dateKey);
        timelineData.count += 1;
        timelineData.amount += (v.amount || 0);
      }
    });
    
    const timeline = Array.from(timelineMap.values());
    
    // ปรับข้อมูลสำหรับการส่งกลับ
    const results = {
      totalTransactions,
      totalAmount,
      avgAmount,
      bankDistribution,
      timeDistribution,
      topReceivers: top10Receivers,
      timeline
    };
    
    // เตรียมข้อมูลสำหรับส่งกลับ
    const response: any = {
      success: true,
      timeframe: {
        start: startDate,
        end: endDate
      },
      results
    };
    
    // เพิ่มรายละเอียดทั้งหมดถ้าต้องการ
    if (includeDetails) {
      // แปลงข้อมูลให้ง่ายขึ้น
      const transactions = filteredVerifications.map((v: any) => ({
        id: v.id,
        createdAt: v.createdAt,
        amount: v.amount,
        bankName: v.bankName || 'ไม่ระบุ',
        receiver: v.receiver || 'ไม่ระบุ',
        status: v.status,
        transactionRef: v.transactionRef || 'ไม่มี'
      }));
      
      response.transactions = transactions;
    }
    
    return res.status(200).json(response);
    
  } catch (error) {
    console.error('Error analyzing user data:', error);
    return res.status(500).json({
      success: false,
      message: 'เกิดข้อผิดพลาดในการวิเคราะห์ข้อมูล'
    });
  }
}