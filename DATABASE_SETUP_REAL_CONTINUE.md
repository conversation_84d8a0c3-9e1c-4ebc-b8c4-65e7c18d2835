# การตั้งค่าฐานข้อมูลสำหรับ SLIPKUY (ฐานข้อมูลจริงแต่ไม่หยุดการทำงาน)

เอกสารนี้อธิบายการตั้งค่าและการแก้ไขปัญหาการเชื่อมต่อฐานข้อมูลสำหรับโปรเจค SLIPKUY โดยใช้ฐานข้อมูลจริงเท่านั้น แต่ไม่หยุดการทำงานของแอพพลิเคชันเมื่อไม่สามารถเชื่อมต่อได้

## การตั้งค่าฐานข้อมูล

โปรเจคนี้ใช้ฐานข้อมูล PostgreSQL จาก NeonDB โดยมีการตั้งค่าดังนี้:

```
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require
```

## การจัดการกับปัญหาการเชื่อมต่อ

โปรเจคนี้ถูกตั้งค่าให้ใช้ฐานข้อมูลจริงเท่านั้น แต่จะไม่หยุดการทำงานของแอพพลิเคชันเมื่อไม่สามารถเชื่อมต่อกับฐานข้อมูลได้ ซึ่งจะทำให้:

1. แอพพลิเคชันยังคงทำงานได้ แม้จะไม่สามารถเชื่อมต่อกับฐานข้อมูลได้
2. ฟังก์ชันที่ต้องใช้ฐานข้อมูลจะไม่ทำงาน แต่ส่วนอื่นๆ ของแอพพลิเคชันยังคงทำงานได้
3. แอพพลิเคชันจะแสดงข้อความข้อผิดพลาดเมื่อไม่สามารถเชื่อมต่อกับฐานข้อมูลได้

## ไฟล์ที่เกี่ยวข้อง

- **server/db.ts**: ไฟล์หลักสำหรับการเชื่อมต่อกับฐานข้อมูล
- **.env**: ไฟล์สำหรับเก็บตัวแปรสภาพแวดล้อม รวมถึง DATABASE_URL

## การทดสอบการเชื่อมต่อ

คุณสามารถทดสอบการเชื่อมต่อกับฐานข้อมูลได้โดยใช้คำสั่ง:

```bash
node scripts/test-db-connection.js
```

## การแก้ไขปัญหาการเชื่อมต่อ

หากพบปัญหาในการเชื่อมต่อกับฐานข้อมูล คุณสามารถลองแก้ไขได้ดังนี้:

1. **ตรวจสอบ DATABASE_URL**: ตรวจสอบว่า DATABASE_URL ถูกต้องและเป็นปัจจุบัน
2. **ตรวจสอบการเชื่อมต่ออินเทอร์เน็ต**: ตรวจสอบว่าเซิร์ฟเวอร์สามารถเชื่อมต่ออินเทอร์เน็ตได้
3. **ตรวจสอบไฟร์วอลล์**: ตรวจสอบว่าไฟร์วอลล์ไม่ได้บล็อกการเชื่อมต่อไปยัง NeonDB
4. **ตรวจสอบสถานะ NeonDB**: ตรวจสอบว่า NeonDB ไม่มีปัญหาหรือการบำรุงรักษา
5. **ตรวจสอบการตั้งค่า SSL**: ตรวจสอบว่าการตั้งค่า SSL ถูกต้อง

## การตั้งค่าเพิ่มเติม

ในไฟล์ `server/db.ts` มีการตั้งค่าเพิ่มเติมสำหรับการเชื่อมต่อฐานข้อมูล:

- **max**: จำนวน connection สูงสุด (20)
- **idleTimeoutMillis**: เวลาที่ connection ไม่ได้ใช้งานก่อนจะถูกปิด (30000 มิลลิวินาที)
- **connectionTimeoutMillis**: เวลาที่รอการเชื่อมต่อก่อนจะ timeout (30000 มิลลิวินาที)
- **ssl.rejectUnauthorized**: ตรวจสอบใบรับรอง SSL (true)

## การจัดการข้อผิดพลาด

เมื่อเกิดข้อผิดพลาดในการเชื่อมต่อกับฐานข้อมูล:

1. แอพพลิเคชันจะบันทึกข้อผิดพลาดลงในล็อก
2. แอพพลิเคชันจะแสดงข้อความข้อผิดพลาด แต่จะไม่หยุดการทำงาน
3. ฟังก์ชันที่ต้องใช้ฐานข้อมูลจะไม่ทำงาน แต่ส่วนอื่นๆ ของแอพพลิเคชันยังคงทำงานได้

## การใช้งาน Nix

เมื่อใช้งาน Nix ในการตั้งค่าสภาพแวดล้อม จะมีการตั้งค่าตัวแปรสภาพแวดล้อมที่จำเป็นสำหรับการเชื่อมต่อฐานข้อมูล:

```bash
export DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"
export PGHOST=ep-blue-sound-a5gmtgsh-pooler.us-east-2.aws.neon.tech
export PGUSER=neondb_owner
export PGPASSWORD=npg_2SG4UjLartCJ
export PGDATABASE=neondb
export PGSSLMODE=require
```

## การเริ่มต้นใช้งาน

เมื่อต้องการเริ่มต้นใช้งานโปรเจค:

```bash
nix-shell --run "npm run dev"
```

หากมีปัญหาในการเชื่อมต่อกับฐานข้อมูล แอพพลิเคชันจะแสดงข้อความข้อผิดพลาด แต่จะไม่หยุดการทำงาน
