import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { 
  Check, 
  X, 
  ChevronRight, 
  Sparkles, 
  ArrowRight, 
  Star, 
  AlertTriangle, 
  Crown, 
  Zap, 
  GemIcon, 
  CloudLightning,
  Info,
  Percent,
  Ticket,
  RefreshCw
} from "lucide-react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { useAuth } from "@/hooks/use-auth";
import { motion, AnimatePresence } from "framer-motion";
import { useState } from "react";
import { calculatePriceWithDiscount } from "@/lib/price-utils";
import { Input } from "@/components/ui/input";

interface PackageFeature {
  name: string;
  included: boolean;
}

interface PackageProps {
  id: number;
  name: string;
  description: string;
  price: number;
  requestsLimit: number;
  isPopular?: boolean;
  features: PackageFeature[];
  tag?: string; // เพิ่ม tag สำหรับป้ายมุมบนขวา
  onSubscribe?: () => void;
  usageData?: {
    used: number;
    total: number;
    percentage: number;
  };
  durationMonths?: number; // เพิ่ม duration months
  discount3Months?: number;
  discount6Months?: number;
  discount12Months?: number;
  creditPerVerification?: number; // เครดิตที่ต้องใช้ต่อการตรวจสอบ 1 ครั้ง (กรณีโควต้าหมด)
  appliedCoupon?: {
    id: number;
    code: string;
    discountPercent: number;
    discountAmount: number;
  };
  onCouponApply?: (code: string) => void; // ฟังก์ชั่นเมื่อกดใช้คูปอง
  onCouponRemove?: () => void; // ฟังก์ชั่นเมื่อลบคูปอง
  showCouponInput?: boolean; // ตัวควบคุมการแสดงช่องป้อนคูปอง
}

export function PackageCard({
  id,
  name,
  description,
  price,
  requestsLimit,
  isPopular = false,
  features,
  tag,
  onSubscribe,
  usageData,
  durationMonths = 1,
  discount3Months,
  discount6Months,
  discount12Months,
  creditPerVerification
}: PackageProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  // State for hover effect and coupon
  const [isHovered, setIsHovered] = useState(false);
  const [couponCode, setCouponCode] = useState("");
  const [showCouponInput, setShowCouponInput] = useState(false);
  const [appliedCoupon, setAppliedCoupon] = useState<{
    code: string;
    discountPercent: number;
    discountAmount: number;
  } | null>(null);
  
  // Validate coupon mutation
  const validateCouponMutation = useMutation({
    mutationFn: async (code: string) => {
      const res = await apiRequest("POST", "/api/coupons/verify", { code });
      return await res.json();
    },
    onSuccess: (data) => {
      if (data.valid) {
        setAppliedCoupon(data.coupon);
        toast({
          title: "คูปองถูกต้อง",
          description: `ใช้คูปอง ${data.coupon.code} สำเร็จ`,
        });
      } else {
        toast({
          title: "คูปองไม่ถูกต้อง",
          description: data.message || "คูปองไม่ถูกต้องหรือหมดอายุ",
          variant: "destructive",
        });
        setAppliedCoupon(null);
      }
    },
    onError: (error: Error) => {
      toast({
        title: "ไม่สามารถตรวจสอบคูปองได้",
        description: error.message,
        variant: "destructive",
      });
      setAppliedCoupon(null);
    }
  });
  
  // Apply coupon
  const applyCoupon = () => {
    if (!couponCode) {
      toast({
        title: "กรุณาระบุรหัสคูปอง",
        description: "โปรดป้อนรหัสคูปองก่อนทำการตรวจสอบ",
        variant: "destructive",
      });
      return;
    }
    
    validateCouponMutation.mutate(couponCode);
  };
  
  // Calculate all discount types and final price
  const calculateDiscounts = () => {
    // ราคาเต็มก่อนหักส่วนลดใดๆ
    const fullPrice = price * durationMonths;
    
    // คำนวณราคาหลังส่วนลดจากระยะเวลา
    const priceAfterDurationDiscount = calculatePriceWithDiscount({
      price, 
      discount3Months: discount3Months || 0, 
      discount6Months: discount6Months || 0, 
      discount12Months: discount12Months || 0
    }, durationMonths);
    
    // ส่วนลดจากระยะเวลา
    const durationDiscountAmount = fullPrice - priceAfterDurationDiscount;
    
    // ส่วนลดจากคูปอง
    let couponDiscountAmount = 0;
    let finalPrice = priceAfterDurationDiscount;
    
    // ถ้ามีคูปอง ให้หักส่วนลดจากคูปอง
    if (appliedCoupon) {
      if (appliedCoupon.discountPercent > 0) {
        // คำนวณส่วนลดเป็นเปอร์เซ็นต์
        couponDiscountAmount = (priceAfterDurationDiscount * appliedCoupon.discountPercent) / 100;
      } else if (appliedCoupon.discountAmount > 0) {
        // คำนวณส่วนลดเป็นจำนวนเงิน
        couponDiscountAmount = appliedCoupon.discountAmount;
      }
      finalPrice = Math.max(0, priceAfterDurationDiscount - couponDiscountAmount);
    }
    
    // ส่วนลดรวมทั้งหมด
    const totalDiscount = durationDiscountAmount + couponDiscountAmount;
    
    return {
      fullPrice,
      durationDiscountAmount,
      couponDiscountAmount,
      totalDiscount,
      finalPrice
    };
  };
  
  // Calculate final price with coupon discount
  const calculateFinalPrice = () => {
    return calculateDiscounts().finalPrice;
  };
  
  const subscribeMutation = useMutation({
    mutationFn: async (params: { packageId: number, durationMonths: number, couponCode?: string }) => {
      const res = await apiRequest("POST", "/api/user/subscribe", params);
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/user/packages'] });
      queryClient.invalidateQueries({ queryKey: ['/api/user/active-package'] });
      queryClient.invalidateQueries({ queryKey: ['/api/stats'] });
      
      toast({
        title: "สมัครแพ็กเกจสำเร็จ",
        description: `คุณได้สมัครแพ็กเกจ ${name} ${durationMonths > 1 ? `${durationMonths} เดือน` : ''} เรียบร้อยแล้ว`,
      });
      
      if (onSubscribe) {
        onSubscribe();
      }
    },
    onError: (error: Error) => {
      toast({
        title: "ไม่สามารถสมัครแพ็กเกจได้",
        description: error.message,
        variant: "destructive",
      });
    }
  });
  
  const handleSubscribe = () => {
    const params: { packageId: number, durationMonths: number, couponCode?: string } = { 
      packageId: id, 
      durationMonths
    };
    
    if (appliedCoupon) {
      params.couponCode = appliedCoupon.code;
    }
    
    subscribeMutation.mutate(params);
  };
  
  return (
    <motion.div
      whileHover={{ y: -5 }}
      transition={{ type: "spring", stiffness: 300 }}
      className="h-full"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Card className={`flex flex-col h-full overflow-hidden relative shadow-xl backdrop-blur-sm ${
        isPopular 
          ? 'border-2 border-amber-400/70 glow-gold' 
          : price === 0 
            ? 'border border-indigo-500/30 shadow-indigo-500/10' 
            : 'border border-amber-400/30 shadow-amber-500/5'
      } bg-transparent`}>
        {/* สไตล์ CSS เพิ่มเติม */}
        <style>{`
          @keyframes shine {
            0% { background-position: -200% center; }
            100% { background-position: 200% center; }
          }
          .shine-effect {
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            background-size: 200% 100%;
            animation: shine 3s linear infinite;
          }
          
          @keyframes pulse-border {
            0% { box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.6); }
            70% { box-shadow: 0 0 0 4px rgba(245, 158, 11, 0); }
            100% { box-shadow: 0 0 0 0 rgba(245, 158, 11, 0); }
          }
          .pulse-border {
            animation: pulse-border 2s infinite;
          }
        `}</style>
      
        {/* พื้นหลังไฮเทคแบบมืออาชีพ */}
        <div className={`absolute inset-0 bg-gradient-to-tr transition-opacity duration-300 ${
          isPopular 
            ? 'from-[#0E0A25] to-[#190E2B] divine-shimmer' 
            : price === 0 
              ? 'from-[#0B0F1A] to-[#0E132B]' 
              : 'from-[#0E0A25] to-[#130B2B]'
        } ${isHovered ? 'opacity-100' : 'opacity-90'}`} />
        
        {/* เอฟเฟคไฮเทคแนว Tech UI */}
        <div className="absolute bottom-0 left-0 right-0 h-1/2 bg-gradient-to-t from-indigo-600/5 to-transparent"></div>
        
        {/* เส้นแสงแนวนอนสไตล์ไซเบอร์ */}
        <div className="absolute top-0 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-indigo-500/30 to-transparent"></div>
        <div className="absolute bottom-0 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-indigo-500/20 to-transparent"></div>
        
        {/* แสงกระพริบบริเวณมุม */}
        <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-purple-600/10 to-transparent rounded-bl-full"></div>
        <div className="absolute bottom-0 left-0 w-20 h-20 bg-gradient-to-tr from-indigo-600/10 to-transparent rounded-tr-full"></div>
        
        {/* ดาวระยิบระยับสไตล์มืออาชีพ */}
        {isPopular && (
          <>
            <div className="absolute top-[15%] left-[10%] w-[2px] h-[2px] bg-amber-300/70 rounded-full animate-pulse"></div>
            <div className="absolute top-[30%] right-[15%] w-[3px] h-[3px] bg-purple-300/60 rounded-full animate-pulse" style={{animationDelay: '0.5s'}}></div>
            <div className="absolute bottom-[25%] left-[18%] w-[2px] h-[2px] bg-blue-300/60 rounded-full animate-pulse" style={{animationDelay: '1s'}}></div>
            <div className="absolute bottom-[15%] right-[12%] w-[2px] h-[2px] bg-amber-300/70 rounded-full animate-pulse" style={{animationDelay: '1.5s'}}></div>
          </>
        )}
        
        {/* แสงสไตล์ Premium สำหรับแพ็กเกจแนะนำ */}
        {isPopular && (
          <>
            <motion.div 
              className="absolute -inset-1 bg-gradient-to-r from-amber-400/8 via-yellow-300/12 to-amber-400/8 blur-md rounded-xl"
              animate={{ 
                opacity: [0.3, 0.6, 0.3],
                scale: [0.98, 1.01, 0.98]
              }}
              transition={{ duration: 4, repeat: Infinity }}
            />
            <div className="absolute inset-0 shine-effect"></div>
            <div className="absolute inset-0 pulse-border rounded-xl"></div>
          </>
        )}
        
        {/* แสดงป้ายกำกับจาก tag หรือใช้ค่า isPopular ถ้าไม่มี tag */}
        {(tag || isPopular) && (
          <div className="absolute top-0 right-0 z-10">
            <motion.div 
              initial={{ x: 50 }}
              animate={{ x: 0 }}
              transition={{ duration: 0.3, type: "spring", stiffness: 200 }}
              className={`${isPopular 
                ? 'bg-gradient-to-r from-amber-400 to-yellow-300 text-indigo-900 font-semibold' 
                : 'bg-gradient-to-r from-indigo-600 to-purple-700 text-white'
              } text-xs px-4 py-1.5 rounded-bl-xl font-medium shadow-md flex items-center`}
            >
              {tag || (isPopular ? "แพ็กเกจจากเทพเจ้า" : "แนะนำ")}
              {isPopular && <Crown className="h-3.5 w-3.5 ml-1.5 text-indigo-900" />}
            </motion.div>
          </div>
        )}
        
        <CardHeader className={`relative z-10 ${isPopular ? 'pt-8' : 'pt-6'}`}>
          <div className="mb-2 flex items-center">
            {isPopular ? (
              <motion.div 
                className="flex h-7 w-7 items-center justify-center rounded-full bg-gradient-to-r from-amber-400 to-yellow-300 mr-2 shadow-glow"
                whileHover={{ rotate: 20 }}
                animate={{ rotate: [0, 5, 0] }}
                transition={{ duration: 5, repeat: Infinity }}
              >
                <Crown className="h-4 w-4 text-indigo-900" />
              </motion.div>
            ) : price === 0 ? (
              <motion.div className="flex h-6 w-6 items-center justify-center rounded-full bg-indigo-700/50 mr-2 border border-indigo-500/30">
                <Star className="h-3.5 w-3.5 text-amber-300" />
              </motion.div>
            ) : (
              <motion.div className="flex h-6 w-6 items-center justify-center rounded-full bg-indigo-700/50 mr-2 border border-indigo-500/30">
                <Zap className="h-3.5 w-3.5 text-amber-300" />
              </motion.div>
            )}
            <CardTitle className={`text-xl font-bold ${
              isPopular 
                ? 'gold-text' 
                : 'text-white'
            }`}>{name}</CardTitle>
          </div>
          <p className="text-indigo-200 mb-4 text-sm">{description}</p>
          
          <div className="flex items-baseline mb-6">
            {isPopular ? (
              <motion.div className="relative">
                <div className="absolute -inset-2 bg-amber-300/10 rounded-xl blur-md"></div>
                <motion.span 
                  whileHover={{ scale: 1.05 }}
                  className="relative text-4xl font-extrabold bg-clip-text text-transparent bg-gradient-to-r from-amber-300 to-yellow-300"
                >
                  {price === 0 ? 'ฟรี' : `฿${calculateFinalPrice().toLocaleString()}`}
                </motion.span>
              </motion.div>
            ) : (
              <motion.span 
                whileHover={{ scale: 1.05 }}
                className="text-3xl font-extrabold text-white"
              >
                {price === 0 ? 'ฟรี' : `฿${calculateFinalPrice().toLocaleString()}`}
              </motion.span>
            )}
            
            {price > 0 && (
              <div className="flex flex-col">
                <motion.span 
                  className="text-indigo-200 ml-1.5"
                  animate={isPopular ? { 
                    opacity: [0.7, 1, 0.7], 
                    y: [0, -2, 0] 
                  } : {}}
                  transition={{ duration: 3, repeat: isPopular ? Infinity : 0 }}
                >
                  {durationMonths > 1 ? `/${durationMonths} เดือน` : `/เดือน`}
                </motion.span>
                
                {/* ไม่ต้องแสดงราคาเดิมแบบซ้ำซ้อน เพราะจะแสดงรวมด้านล่างแล้ว */}
                
                {/* แสดงส่วนลดที่ได้รับ */}
                {(durationMonths > 1 || appliedCoupon) && (
                  <div className="mt-1 ml-1.5 flex flex-col gap-1">
                    {/* แสดงเฉพาะส่วนลดจากระยะเวลา */}
                    {durationMonths > 1 && (
                      <span className={`text-xs font-medium px-2 py-0.5 rounded-full ${
                        isPopular 
                          ? "bg-amber-400/20 text-amber-300" 
                          : "bg-indigo-400/20 text-indigo-300"
                      }`}>
                        {durationMonths === 3 && discount3Months ? `ส่วนลด ${discount3Months}%` : ""}
                        {durationMonths === 6 && discount6Months ? `ส่วนลด ${discount6Months}%` : ""}
                        {durationMonths === 12 && discount12Months ? `ส่วนลด ${discount12Months}%` : ""}
                      </span>
                    )}
                    
                    {/* แสดงราคาก่อนหักส่วนลดและยอดประหยัดรวม */}
                    <div className="text-xs text-indigo-400">
                      <span className="line-through">฿{calculateDiscounts().fullPrice.toLocaleString()}</span>
                      <span className="ml-2 font-semibold px-2 py-0.5 rounded-md bg-gradient-to-r from-green-600/30 to-emerald-500/30 border border-green-500/30">
                        <span className="bg-gradient-to-r from-green-400 to-emerald-300 bg-clip-text text-transparent">
                          ประหยัดรวม ฿{calculateDiscounts().totalDiscount.toLocaleString()}
                        </span>
                      </span>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
          
          {/* รูปเงาเทพเจ้าด้านหลังสำหรับแพ็กเกจพิเศษ */}
          {isPopular && (
            <motion.div 
              className="absolute -top-1 -right-4 opacity-10 text-amber-300"
              animate={{ 
                y: [0, -5, 0],
                opacity: [0.1, 0.15, 0.1]
              }}
              transition={{ duration: 8, repeat: Infinity }}
            >
              <CloudLightning className="h-20 w-20" />
            </motion.div>
          )}
        </CardHeader>
        
        <CardContent className="flex-1 relative">
          {/* แสดงข้อมูลการใช้งานถ้ามี usageData */}
          {usageData && (
            <motion.div 
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-4 p-4 bg-gradient-to-br from-indigo-500/10 to-purple-500/10 rounded-lg border border-indigo-500/20 overflow-hidden relative"
            >
              {/* เอฟเฟคพื้นหลังแบบเรืองแสง */}
              <div className="absolute inset-0 overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-600/5 to-purple-600/5"></div>
                <div className="absolute -bottom-2 -right-2 w-10 h-10 bg-purple-500/10 rounded-full blur-md"></div>
                <div className="absolute -top-2 -left-2 w-10 h-10 bg-indigo-500/10 rounded-full blur-md"></div>
              </div>
              
              {/* สีของเส้นขอบเรืองแสง */}
              <div className="absolute top-0 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-indigo-500/40 to-transparent"></div>
              <div className="absolute bottom-0 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-purple-500/30 to-transparent"></div>
              
              <div className="relative flex justify-between text-sm mb-2">
                <span className="text-indigo-100 font-medium">การใช้งานต่อเดือน</span>
                <motion.span
                  className="font-semibold bg-clip-text text-transparent bg-gradient-to-r from-amber-300 to-yellow-200"
                  whileHover={{ scale: 1.05 }}
                  transition={{ type: "spring", stiffness: 400 }}
                >
                  {usageData.used.toLocaleString()} / {usageData.total.toLocaleString()}
                </motion.span>
              </div>
              
              <div className="relative h-2.5 w-full bg-indigo-950/60 rounded-full overflow-hidden mb-2 shadow-inner">
                {/* เพิ่มแกลนเอฟเฟคในแถบความคืบหน้า */}
                <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-white/5 via-transparent to-transparent opacity-50"></div>
                
                <motion.div 
                  initial={{ width: "5%" }}
                  animate={{ width: `${Math.max(5, usageData.percentage)}%` }}
                  transition={{ duration: 1.5, ease: "easeOut" }}
                  className={`absolute h-full left-0 rounded-full ${
                    usageData.percentage > 80 
                      ? "bg-gradient-to-r from-red-600 to-red-500" 
                      : usageData.percentage > 50
                        ? "bg-gradient-to-r from-amber-500 to-amber-400" 
                        : "bg-gradient-to-r from-emerald-500 to-emerald-400"
                  }`}
                >
                  {/* เอฟเฟคประกาย */}
                  <motion.div 
                    className="absolute top-0 right-0 bottom-0 w-5 bg-gradient-to-r from-transparent to-white/30"
                    animate={{ x: ["100%", "-100%"] }}
                    transition={{ duration: 2, repeat: Infinity, repeatDelay: 1 }}
                  />
                </motion.div>
              </div>
              
              <div className="flex justify-between items-center relative">
                <motion.div 
                  className={`text-xs flex items-center ${
                    usageData.percentage > 80 
                      ? "text-red-400" 
                      : usageData.percentage > 50 
                        ? "text-amber-300" 
                        : "text-emerald-300"
                  }`}
                  whileHover={{ x: 3 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  {usageData.percentage > 80 && (
                    <div className="flex items-center">
                      <AlertTriangle className="h-3 w-3 mr-1.5" />
                      <span className="font-medium">เหลือน้อย! ใช้ไปแล้ว {usageData.percentage}%</span>
                    </div>
                  )}
                  {usageData.percentage > 50 && usageData.percentage <= 80 && (
                    <div className="flex items-center">
                      <AlertTriangle className="h-3 w-3 mr-1.5" />
                      <span className="font-medium">ใช้ไปแล้ว {usageData.percentage}%</span>
                    </div>
                  )}
                  {usageData.percentage <= 50 && (
                    <div className="flex items-center">
                      <Check className="h-3 w-3 mr-1.5" />
                      <span className="font-medium">เหลืออีก {(usageData.total - usageData.used).toLocaleString()} ครั้ง</span>
                    </div>
                  )}
                </motion.div>
                
                <motion.div 
                  className={`text-xs font-bold px-2 py-0.5 rounded-full ${
                    usageData.percentage > 80 
                      ? "bg-red-500/20 text-red-300" 
                      : usageData.percentage > 50 
                        ? "bg-amber-500/20 text-amber-300" 
                        : "bg-emerald-500/20 text-emerald-300"
                  }`}
                  whileHover={{ scale: 1.1 }}
                  transition={{ type: "spring", stiffness: 400 }}
                >
                  {usageData.percentage}%
                </motion.div>
              </div>
            </motion.div>
          )}

          <ul className="space-y-3 mb-6 relative z-10">
            <motion.li 
              className="flex items-start p-2 rounded-md" 
              whileHover={{ backgroundColor: "rgba(99, 102, 241, 0.15)" }}
            >
              <Check className="h-5 w-5 text-emerald-400 mt-0.5 mr-2 flex-shrink-0" />
              <span className="font-medium text-indigo-100">{requestsLimit.toLocaleString()} ครั้งต่อเดือน</span>
            </motion.li>
            
            {/* แสดงค่าเครดิตต่อครั้ง - รูปแบบที่ปรับปรุงใหม่ */}
            {creditPerVerification !== undefined && creditPerVerification !== null && (
              <motion.li 
                className="flex items-start p-3 rounded-md relative overflow-hidden" 
                style={{ background: "linear-gradient(140deg, rgba(88, 28, 135, 0.3), rgba(124, 58, 237, 0.15))" }}
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                {/* เอฟเฟกต์พื้นหลังเคลื่อนไหว */}
                <motion.div 
                  className="absolute inset-0 opacity-20"
                  animate={{ 
                    background: [
                      "linear-gradient(45deg, rgba(139, 92, 246, 0) 0%, rgba(139, 92, 246, 0.3) 50%, rgba(139, 92, 246, 0) 100%)",
                      "linear-gradient(45deg, rgba(139, 92, 246, 0.3) 0%, rgba(139, 92, 246, 0) 50%, rgba(139, 92, 246, 0.3) 100%)",
                      "linear-gradient(45deg, rgba(139, 92, 246, 0) 0%, rgba(139, 92, 246, 0.3) 50%, rgba(139, 92, 246, 0) 100%)"
                    ],
                    backgroundPosition: ["0% 0%", "100% 100%", "0% 0%"]
                  }}
                  transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                />
                
                {/* ขอบเรืองแสง */}
                <div className="absolute inset-0 border border-purple-500/30 rounded-md"></div>
                <div className="absolute top-0 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-purple-400/40 to-transparent"></div>
                <div className="absolute bottom-0 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-violet-400/30 to-transparent"></div>
                
                {/* ไอคอนปรับปรุงใหม่ */}
                <div className="flex h-7 w-7 items-center justify-center rounded-full bg-gradient-to-br from-purple-700/80 to-indigo-700/80 mr-3 flex-shrink-0 mt-0.5 border border-purple-500/50 shadow-lg shadow-purple-900/20">
                  <motion.div
                    animate={{ rotate: [0, 15, 0, -15, 0] }}
                    transition={{ duration: 5, repeat: Infinity, ease: "easeInOut" }}
                  >
                    <Zap className="h-3.5 w-3.5 text-purple-200" />
                  </motion.div>
                </div>
                
                {/* ข้อความปรับปรุงใหม่ */}
                <div className="flex flex-col">
                  <div className="font-medium text-indigo-100 flex items-center">
                    <span className="bg-clip-text text-transparent bg-gradient-to-r from-violet-300 to-purple-300 font-semibold text-base">
                      ฿{creditPerVerification.toFixed(2)}
                    </span>
                    <span className="ml-1.5">ต่อการตรวจสอบ 1 ครั้ง</span>
                    {isPopular && (
                      <div className="ml-2 px-1.5 py-0.5 rounded-sm text-[10px] font-semibold bg-gradient-to-r from-amber-600/40 to-amber-500/40 text-amber-300 border border-amber-500/30">คุ้มค่า</div>
                    )}
                  </div>
                  <div className="text-xs text-indigo-300 mt-1.5 flex items-center">
                    <Info className="h-3 w-3 mr-1 text-purple-400" />
                    <span>เมื่อใช้งานเกินจำนวนที่มาในแพ็กเกจ</span>
                  </div>
                </div>
              </motion.li>
            )}
            
            {features.map((feature, index) => (
              <motion.li 
                key={index} 
                className="flex items-start p-2 rounded-md"
                whileHover={{ backgroundColor: feature.included ? "rgba(99, 102, 241, 0.15)" : "rgba(255, 255, 255, 0.05)" }}
              >
                {feature.included ? (
                  <Check className="h-5 w-5 text-emerald-400 mt-0.5 mr-2 flex-shrink-0" />
                ) : (
                  <X className="h-5 w-5 text-gray-400 mt-0.5 mr-2 flex-shrink-0" />
                )}
                <span className={feature.included ? 'text-indigo-100' : 'text-gray-400'}>
                  {feature.name}
                </span>
              </motion.li>
            ))}
          </ul>
        </CardContent>
        
        <CardFooter className="pt-2 pb-6 relative">
          {/* ส่วนช่องคูปอง */}
          {price > 0 && user && (
            <div className="mb-4 w-full">
              {showCouponInput ? (
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Input
                      placeholder="กรอกรหัสคูปอง"
                      value={couponCode}
                      onChange={(e) => setCouponCode(e.target.value)}
                      className="bg-indigo-950/30 border-indigo-500/30 text-indigo-100 placeholder:text-indigo-300/50 focus:border-purple-500"
                    />
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="p-2 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-md text-white"
                      onClick={applyCoupon}
                      disabled={validateCouponMutation.isPending}
                    >
                      {validateCouponMutation.isPending ? (
                        <RefreshCw className="h-5 w-5 animate-spin" />
                      ) : (
                        <Ticket className="h-5 w-5" />
                      )}
                    </motion.button>
                  </div>
                  
                  {/* แสดงข้อมูลคูปองที่ใช้ */}
                  {appliedCoupon && (
                    <motion.div 
                      initial={{ opacity: 0, y: 5 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="flex items-center p-2 bg-gradient-to-r from-emerald-500/20 to-cyan-500/20 rounded-md border border-emerald-500/30"
                    >
                      <Check className="h-4 w-4 text-emerald-400 mr-2" />
                      <div className="text-sm text-emerald-300">
                        <p className="font-medium">คูปอง: {appliedCoupon.code}</p>
                        <p>
                          {appliedCoupon.discountPercent > 0 
                            ? `ส่วนลด ${appliedCoupon.discountPercent}%`
                            : `ส่วนลด ฿${appliedCoupon.discountAmount}`
                          }
                        </p>
                      </div>
                    </motion.div>
                  )}
                </div>
              ) : (
                <motion.button
                  whileHover={{ scale: 1.03 }}
                  whileTap={{ scale: 0.97 }}
                  className="w-full flex items-center justify-center py-1.5 px-2 bg-indigo-900/30 hover:bg-indigo-800/40 border border-indigo-500/30 rounded-lg text-sm text-indigo-300 transition-colors"
                  onClick={() => setShowCouponInput(true)}
                >
                  <Ticket className="h-4 w-4 mr-1.5 text-indigo-400" />
                  มีรหัสคูปอง?
                </motion.button>
              )}
            </div>
          )}
          
          <motion.div
            className="w-full"
            whileHover={{ scale: 1.03 }}
            whileTap={{ scale: 0.98 }}
          >
            <Button 
              onClick={user ? handleSubscribe : onSubscribe} 
              className={`w-full relative overflow-hidden z-10 h-12 rounded-xl font-bold text-lg ${
                isPopular 
                  ? 'bg-gradient-to-r from-rose-500 via-amber-500 to-yellow-400 hover:from-rose-600 hover:via-amber-600 hover:to-yellow-500 text-white shadow-lg shadow-amber-500/30 border border-amber-400/30' 
                  : price === 0 
                    ? 'bg-gradient-to-r from-emerald-500 to-cyan-500 hover:from-emerald-600 hover:to-cyan-600 text-white shadow-lg shadow-emerald-500/30 border border-emerald-400/30' 
                    : 'bg-gradient-to-r from-fuchsia-600 via-purple-600 to-indigo-600 hover:from-fuchsia-700 hover:via-purple-700 hover:to-indigo-700 text-white shadow-lg shadow-purple-500/30 border border-purple-400/30'
              }`}
              disabled={subscribeMutation.isPending}
            >
              {subscribeMutation.isPending ? (
                <div className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  กำลังดำเนินการ...
                </div>
              ) : (
                <div className="flex items-center justify-center font-medium">
                  {price === 0 ? (
                    <>
                      <Sparkles className="h-5 w-5 mr-2 text-cyan-300" />
                      เริ่มต้นใช้งานฟรี
                      <ArrowRight className="ml-1.5 h-4 w-4" />
                    </>
                  ) : isPopular ? (
                    <>
                      <Zap className="h-5 w-5 mr-2 text-yellow-300 animate-pulse" />
                      อัปเกรดแพ็กเกจ
                      <Star className="ml-1.5 h-5 w-5 text-yellow-300" />
                    </>
                  ) : (
                    <>
                      <GemIcon className="h-5 w-5 mr-2 text-indigo-300" />
                      สั่งซื้อแพ็กเกจ
                      <ArrowRight className="ml-1.5 h-4 w-4 animate-pulse" />
                    </>
                  )}
                </div>
              )}
              
              {/* เอฟเฟกต์เรืองแสงด้านล่างปุ่ม */}
              <div className="absolute bottom-0 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-white/50 to-transparent"></div>
              
              {/* เพิ่มเอฟเฟกต์กระพริบสำหรับปุ่มแพ็กเกจยอดนิยม */}
              {isPopular && (
                <>
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-300 rounded-full animate-ping"></div>
                  <div className="absolute -bottom-1 -left-1 w-3 h-3 bg-rose-400 rounded-full animate-ping" style={{ animationDelay: '1s' }}></div>
                </>
              )}
            </Button>
          </motion.div>
        </CardFooter>
      </Card>
    </motion.div>
  );
}
