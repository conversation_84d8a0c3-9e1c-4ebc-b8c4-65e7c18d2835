import { Request, Response } from 'express';
import { db } from '../db';
import { apiLogs, slipVerifications, apiKeys } from '../../shared/schema';
import { eq, and, gte, lte, desc, count, sum, sql, inArray } from 'drizzle-orm';
import { isAdmin } from '../auth';

// Get API usage statistics
export async function getApiStats(req: Request, res: Response) {
  try {
    const { timeRange = 'today', apiKey = 'all' } = req.query;
    
    // Determine date range based on timeRange
    const now = new Date();
    let startDate = new Date();
    
    switch (timeRange) {
      case 'today':
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'yesterday':
        startDate.setDate(startDate.getDate() - 1);
        startDate.setHours(0, 0, 0, 0);
        now.setDate(now.getDate() - 1);
        now.setHours(23, 59, 59, 999);
        break;
      case 'week':
        startDate.setDate(startDate.getDate() - 7);
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'month':
        startDate.setDate(startDate.getDate() - 30);
        startDate.setHours(0, 0, 0, 0);
        break;
      default:
        startDate.setHours(0, 0, 0, 0);
    }
    
    // Build the query conditions
    let conditions = and(
      gte(apiLogs.createdAt, startDate),
      lte(apiLogs.createdAt, now)
    );
    
    // Add API key filter if specified
    if (apiKey !== 'all') {
      conditions = and(conditions, eq(apiLogs.apiKeyId, Number(apiKey)));
    }
    
    // Add user filter if not admin
    if (!isAdmin(req)) {
      conditions = and(conditions, eq(apiLogs.userId, req.user!.id));
    }
    
    // Get summary statistics
    const apiStats = await db.select({
      totalRequests: count(apiLogs.id),
      successCount: count(apiLogs.id).filter(eq(apiLogs.responseStatus, 'success')),
      errorCount: count(apiLogs.id).filter(eq(apiLogs.responseStatus, 'error')),
    }).from(apiLogs)
    .where(conditions);
    
    // Get hourly statistics
    const hourlyStats = await db.select({
      hour: sql<string>`date_trunc('hour', ${apiLogs.createdAt}::timestamp)`,
      requests: count(apiLogs.id),
      success: count(apiLogs.id).filter(eq(apiLogs.responseStatus, 'success')),
      failed: count(apiLogs.id).filter(eq(apiLogs.responseStatus, 'error')),
    }).from(apiLogs)
    .where(conditions)
    .groupBy(sql`date_trunc('hour', ${apiLogs.createdAt}::timestamp)`)
    .orderBy(sql`date_trunc('hour', ${apiLogs.createdAt}::timestamp)`);
    
    // Get bank statistics
    const bankStats = await db.select({
      bankCode: sql<string>`COALESCE(data->>'bankCode', 'unknown')`,
      count: count(),
      percentage: sql<number>`ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM ${slipVerifications} WHERE ${conditions}), 1)`,
    }).from(slipVerifications)
    .where(conditions)
    .groupBy(sql`COALESCE(data->>'bankCode', 'unknown')`)
    .orderBy(desc(count()));
    
    // Get geolocation statistics
    const geoStats = await db.select({
      location: sql<string>`COALESCE(${apiLogs.location}, 'unknown')`,
      count: count(),
      percentage: sql<number>`ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM ${apiLogs} WHERE ${conditions}), 1)`,
    }).from(apiLogs)
    .where(conditions)
    .groupBy(apiLogs.location)
    .orderBy(desc(count()));
    
    // Get recent API logs
    const recentLogs = await db.select()
      .from(apiLogs)
      .where(conditions)
      .orderBy(desc(apiLogs.createdAt))
      .limit(10);
    
    // Get success rate trend
    const successRateByDay = await db.select({
      day: sql<string>`date_trunc('day', ${apiLogs.createdAt}::timestamp)`,
      successRate: sql<number>`ROUND(SUM(CASE WHEN ${apiLogs.responseStatus} = 'success' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 1)`,
    }).from(apiLogs)
    .where(and(
      gte(apiLogs.createdAt, new Date(startDate.getTime() - 7 * 24 * 60 * 60 * 1000)), // 1 week before the start date
      lte(apiLogs.createdAt, now)
    ))
    .groupBy(sql`date_trunc('day', ${apiLogs.createdAt}::timestamp)`)
    .orderBy(sql`date_trunc('day', ${apiLogs.createdAt}::timestamp)`);
    
    return res.status(200).json({
      code: "200000",
      message: "ดึงข้อมูลสถิติ API สำเร็จ",
      data: {
        summary: apiStats[0] || { totalRequests: 0, successCount: 0, errorCount: 0 },
        hourlyStats,
        bankStats,
        geoStats,
        recentLogs,
        successRateTrend: successRateByDay,
        timeRange,
      }
    });
  } catch (error: any) {
    console.error('Error getting API stats:', error);
    return res.status(500).json({
      code: "500000",
      message: "เกิดข้อผิดพลาดในการดึงข้อมูลสถิติ API",
      error: error.message
    });
  }
}

// Get real-time API logs
export async function getRealTimeApiLogs(req: Request, res: Response) {
  try {
    const { limit = 20 } = req.query;
    
    // Build the query conditions
    let conditions = sql`1=1`;
    
    // Add user filter if not admin
    if (!isAdmin(req)) {
      conditions = and(conditions, eq(apiLogs.userId, req.user!.id));
    }
    
    // Get recent API logs
    const recentLogs = await db.select()
      .from(apiLogs)
      .where(conditions)
      .orderBy(desc(apiLogs.createdAt))
      .limit(Number(limit));
    
    return res.status(200).json({
      code: "200000",
      message: "ดึงข้อมูล API logs สำเร็จ",
      data: recentLogs
    });
  } catch (error: any) {
    console.error('Error getting real-time API logs:', error);
    return res.status(500).json({
      code: "500000",
      message: "เกิดข้อผิดพลาดในการดึงข้อมูล API logs",
      error: error.message
    });
  }
}

// Get error analysis
export async function getErrorAnalysis(req: Request, res: Response) {
  try {
    const { timeRange = 'week' } = req.query;
    
    // Determine date range based on timeRange
    const now = new Date();
    let startDate = new Date();
    
    switch (timeRange) {
      case 'today':
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'yesterday':
        startDate.setDate(startDate.getDate() - 1);
        startDate.setHours(0, 0, 0, 0);
        now.setDate(now.getDate() - 1);
        now.setHours(23, 59, 59, 999);
        break;
      case 'week':
        startDate.setDate(startDate.getDate() - 7);
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'month':
        startDate.setDate(startDate.getDate() - 30);
        startDate.setHours(0, 0, 0, 0);
        break;
      default:
        startDate.setDate(startDate.getDate() - 7);
        startDate.setHours(0, 0, 0, 0);
    }
    
    // Build the query conditions
    let conditions = and(
      gte(apiLogs.createdAt, startDate),
      lte(apiLogs.createdAt, now),
      eq(apiLogs.responseStatus, 'error')
    );
    
    // Add user filter if not admin
    if (!isAdmin(req)) {
      conditions = and(conditions, eq(apiLogs.userId, req.user!.id));
    }
    
    // Get error breakdown by type
    const errorsByType = await db.select({
      errorType: sql<string>`COALESCE(${apiLogs.error}->>'type', 'unknown')`,
      count: count(),
      percentage: sql<number>`ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM ${apiLogs} WHERE ${conditions}), 1)`,
    }).from(apiLogs)
    .where(conditions)
    .groupBy(sql`COALESCE(${apiLogs.error}->>'type', 'unknown')`)
    .orderBy(desc(count()));
    
    // Get error trend over time
    const errorTrend = await db.select({
      day: sql<string>`date_trunc('day', ${apiLogs.createdAt}::timestamp)`,
      count: count(),
    }).from(apiLogs)
    .where(conditions)
    .groupBy(sql`date_trunc('day', ${apiLogs.createdAt}::timestamp)`)
    .orderBy(sql`date_trunc('day', ${apiLogs.createdAt}::timestamp)`);
    
    // Get recent errors
    const recentErrors = await db.select()
      .from(apiLogs)
      .where(conditions)
      .orderBy(desc(apiLogs.createdAt))
      .limit(10);
    
    return res.status(200).json({
      code: "200000",
      message: "ดึงข้อมูลการวิเคราะห์ข้อผิดพลาดสำเร็จ",
      data: {
        errorsByType,
        errorTrend,
        recentErrors,
        timeRange,
      }
    });
  } catch (error: any) {
    console.error('Error getting error analysis:', error);
    return res.status(500).json({
      code: "500000",
      message: "เกิดข้อผิดพลาดในการดึงข้อมูลการวิเคราะห์ข้อผิดพลาด",
      error: error.message
    });
  }
}

// Generate AI insights
export async function generateAiInsights(req: Request, res: Response) {
  // TODO: Integrate with Perplexity API for AI insights
  try {
    // For now, return placeholder insights
    return res.status(200).json({
      code: "200000",
      message: "สร้างข้อมูลเชิงลึกสำเร็จ",
      data: {
        insights: [
          {
            type: "usage",
            title: "การใช้งาน API",
            content: "ในช่วง 24 ชั่วโมงที่ผ่านมา มีการเรียกใช้ API เพิ่มขึ้น 15% เมื่อเทียบกับช่วงเวลาเดียวกันของวันก่อนหน้า"
          },
          {
            type: "performance",
            title: "ประสิทธิภาพ",
            content: "เวลาตอบสนองเฉลี่ยของ API อยู่ที่ 245ms ซึ่งเร็วกว่าค่าเฉลี่ยของสัปดาห์ที่ผ่านมา (278ms)"
          },
          {
            type: "error",
            title: "ข้อผิดพลาดที่พบบ่อย",
            content: "ข้อผิดพลาดที่พบบ่อยที่สุดคือ 'ไม่สามารถอ่านข้อมูลสลิปได้' (42% ของข้อผิดพลาดทั้งหมด) ซึ่งมักเกิดจากคุณภาพรูปภาพต่ำ"
          },
          {
            type: "recommendation",
            title: "คำแนะนำ",
            content: "1. ตรวจสอบคุณภาพรูปภาพสลิปก่อนส่ง\n2. หลีกเลี่ยงการส่งคำขอซ้ำในเวลาใกล้เคียงกัน\n3. พิจารณาเพิ่มการตรวจสอบข้อมูลฝั่งไคลเอนต์ก่อนส่งไปยัง API"
          }
        ]
      }
    });
  } catch (error: any) {
    console.error('Error generating AI insights:', error);
    return res.status(500).json({
      code: "500000",
      message: "เกิดข้อผิดพลาดในการสร้างข้อมูลเชิงลึก",
      error: error.message
    });
  }
}

// Generate AI response to user query
export async function generateAiResponse(req: Request, res: Response) {
  try {
    const { query } = req.body;
    
    if (!query) {
      return res.status(400).json({
        code: "400000",
        message: "กรุณาระบุคำถาม",
      });
    }
    
    // TODO: Integrate with Perplexity API
    // For now, return placeholder response
    return res.status(200).json({
      code: "200000",
      message: "สร้างคำตอบสำเร็จ",
      data: {
        answer: "นี่เป็นคำตอบอัตโนมัติสำหรับคำถาม: " + query + ". ฟีเจอร์นี้อยู่ระหว่างการพัฒนาและจะใช้ Perplexity API ในการให้คำตอบที่ถูกต้องและมีประโยชน์"
      }
    });
  } catch (error: any) {
    console.error('Error generating AI response:', error);
    return res.status(500).json({
      code: "500000",
      message: "เกิดข้อผิดพลาดในการสร้างคำตอบ",
      error: error.message
    });
  }
}