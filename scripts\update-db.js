import { db } from '../server/db.ts';
import * as schema from '../shared/schema.ts';
import { sql } from 'drizzle-orm';

// ตรวจสอบความพร้อมของการเชื่อมต่อกับฐานข้อมูล
async function checkConnection() {
  try {
    // ทดสอบการเชื่อมต่อด้วยการ query ง่ายๆ
    await db.execute(sql`SELECT 1`);
    console.log('✅ เชื่อมต่อกับฐานข้อมูลสำเร็จ');
    return true;
  } catch (error) {
    console.error('❌ ไม่สามารถเชื่อมต่อกับฐานข้อมูลได้:', error);
    return false;
  }
}

// สร้างตาราง Enum types ก่อน
async function createEnums() {
  try {
    // สร้าง enum types
    await db.execute(sql`
      DO $$ 
      BEGIN 
        -- ตรวจสอบว่า Enum มีอยู่แล้วหรือไม่ ถ้ายังไม่มีให้สร้าง
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'alert_type') THEN
          CREATE TYPE alert_type AS ENUM (
            'unusual_transaction', 'quota_low', 'api_key_expiring', 'fraud_detection',
            'daily_report', 'weekly_report', 'monthly_report', 'system_update'
          );
        END IF;

        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'alert_channel') THEN
          CREATE TYPE alert_channel AS ENUM ('email', 'in_app', 'line', 'sms');
        END IF;

        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'alert_status') THEN
          CREATE TYPE alert_status AS ENUM ('pending', 'sent', 'failed', 'read');
        END IF;

        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'alert_priority') THEN
          CREATE TYPE alert_priority AS ENUM ('low', 'medium', 'high', 'critical');
        END IF;

        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'report_frequency') THEN
          CREATE TYPE report_frequency AS ENUM ('daily', 'weekly', 'monthly');
        END IF;
      END $$;
    `);
    console.log('✅ สร้าง Enum Types สำเร็จ');
    return true;
  } catch (error) {
    console.error('❌ ไม่สามารถสร้าง Enum Types ได้:', error);
    return false;
  }
}

// สร้างตารางใหม่หากยังไม่มี
async function createTables() {
  try {
    // ตาราง users
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        username TEXT NOT NULL UNIQUE,
        password TEXT NOT NULL,
        email TEXT NOT NULL UNIQUE,
        first_name TEXT,
        last_name TEXT,
        company_name TEXT,
        phone_number TEXT,
        address TEXT,
        bio TEXT,
        profile_image TEXT,
        credit DOUBLE PRECISION NOT NULL DEFAULT 0,
        tier TEXT NOT NULL DEFAULT 'standard',
        role TEXT NOT NULL DEFAULT 'user',
        status TEXT NOT NULL DEFAULT 'active',
        allowed_packages INTEGER[],
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
    `);
    
    // ตาราง system_settings
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS system_settings (
        id SERIAL PRIMARY KEY,
        key TEXT NOT NULL UNIQUE,
        value TEXT,
        value_json JSONB,
        description TEXT,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
    `);
    
    // ตาราง packages
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS packages (
        id SERIAL PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        price INTEGER NOT NULL,
        discount_3_months INTEGER DEFAULT 0,
        discount_6_months INTEGER DEFAULT 0,
        discount_12_months INTEGER DEFAULT 0,
        duration_days INTEGER NOT NULL DEFAULT 30,
        requests_limit INTEGER NOT NULL,
        credit_per_verification DOUBLE PRECISION,
        is_active BOOLEAN NOT NULL DEFAULT TRUE,
        features TEXT[],
        tag TEXT,
        sort_order INTEGER DEFAULT 0,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
    `);
    
    // ตาราง user_packages
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS user_packages (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id),
        package_id INTEGER NOT NULL REFERENCES packages(id),
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        is_active BOOLEAN NOT NULL DEFAULT TRUE,
        requests_used INTEGER NOT NULL DEFAULT 0,
        last_quota_reset_date DATE,
        duration_months INTEGER NOT NULL DEFAULT 1,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
    `);
    
    // ตาราง api_keys
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS api_keys (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id),
        api_key VARCHAR(64) NOT NULL UNIQUE,
        name TEXT NOT NULL,
        description TEXT,
        status TEXT NOT NULL DEFAULT 'active',
        last_used TIMESTAMP,
        request_count INTEGER NOT NULL DEFAULT 0,
        ip_whitelist TEXT[],
        expires_at TIMESTAMP,
        limit_enabled BOOLEAN NOT NULL DEFAULT FALSE,
        usage_limit INTEGER,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
    `);
    
    // ตาราง slip_verifications
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS slip_verifications (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id),
        transaction_ref TEXT,
        bank_name TEXT,
        amount DOUBLE PRECISION,
        sender TEXT,
        receiver TEXT,
        transaction_date TIMESTAMP,
        status TEXT NOT NULL DEFAULT 'pending',
        response_data TEXT,
        credit_used DOUBLE PRECISION,
        used_credit BOOLEAN DEFAULT FALSE,
        verification_source TEXT DEFAULT 'web',
        api_key_id INTEGER REFERENCES api_keys(id),
        image_path TEXT,
        created_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
    `);
    
    // ตาราง notifications
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS notifications (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id),
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        type alert_type NOT NULL,
        priority alert_priority NOT NULL DEFAULT 'medium',
        status alert_status NOT NULL DEFAULT 'pending',
        channel alert_channel NOT NULL,
        metadata JSONB,
        is_read BOOLEAN NOT NULL DEFAULT FALSE,
        read_at TIMESTAMP,
        sent_at TIMESTAMP,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
    `);
    
    // ตาราง alert_settings
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS alert_settings (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id),
        alert_type alert_type NOT NULL,
        enabled BOOLEAN NOT NULL DEFAULT TRUE,
        channels alert_channel[],
        threshold JSONB,
        frequency report_frequency,
        time_preference JSONB,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
    `);
    
    // ตาราง email_templates
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS email_templates (
        id SERIAL PRIMARY KEY,
        name TEXT NOT NULL UNIQUE,
        subject TEXT NOT NULL,
        html_content TEXT NOT NULL,
        text_content TEXT NOT NULL,
        variables TEXT[],
        is_default BOOLEAN NOT NULL DEFAULT FALSE,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
    `);
    
    // ตาราง email_logs
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS email_logs (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id),
        "to" TEXT NOT NULL,
        subject TEXT NOT NULL,
        content TEXT,
        template_id INTEGER REFERENCES email_templates(id),
        status TEXT NOT NULL DEFAULT 'pending',
        error_message TEXT,
        metadata JSONB,
        sent_at TIMESTAMP,
        created_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
    `);
    
    // ตาราง fraud_rules
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS fraud_rules (
        id SERIAL PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        rule_type TEXT NOT NULL,
        conditions JSONB NOT NULL,
        action TEXT NOT NULL,
        severity TEXT NOT NULL DEFAULT 'medium',
        is_active BOOLEAN NOT NULL DEFAULT TRUE,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
    `);
    
    // ตาราง fraud_detections
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS fraud_detections (
        id SERIAL PRIMARY KEY,
        slip_verification_id INTEGER NOT NULL REFERENCES slip_verifications(id),
        user_id INTEGER NOT NULL REFERENCES users(id),
        rule_id INTEGER NOT NULL REFERENCES fraud_rules(id),
        status TEXT NOT NULL DEFAULT 'detected',
        details JSONB,
        reviewed_by INTEGER REFERENCES users(id),
        reviewed_at TIMESTAMP,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
    `);
    
    // ตาราง reports
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS reports (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id),
        name TEXT NOT NULL,
        type report_frequency NOT NULL,
        format TEXT NOT NULL DEFAULT 'html',
        data JSONB,
        start_date TIMESTAMP NOT NULL,
        end_date TIMESTAMP NOT NULL,
        status TEXT NOT NULL DEFAULT 'generated',
        email_log_id INTEGER REFERENCES email_logs(id),
        created_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
    `);
    
    console.log('✅ สร้างตารางทั้งหมดสำเร็จ');
    return true;
  } catch (error) {
    console.error('❌ เกิดข้อผิดพลาดในการสร้างตาราง:', error);
    return false;
  }
}

// เพิ่มข้อมูลตั้งต้นในระบบส่งอีเมล
async function setupEmailSystem() {
  try {
    // ตรวจสอบว่ามีการตั้งค่าระบบอีเมลหรือไม่
    const emailSettings = await db.select().from(schema.systemSettings).where(sql`${schema.systemSettings.key} = 'email_settings'`);
    
    // ถ้ายังไม่มีการตั้งค่า ให้เพิ่มการตั้งค่าเริ่มต้น
    if (emailSettings.length === 0) {
      await db.insert(schema.systemSettings).values({
        key: 'email_settings',
        valueJson: {
          service: 'gmail',
          host: 'smtp.gmail.com',
          port: 587,
          secure: false,
          auth: {
            user: '',
            pass: ''
          },
          from: '<EMAIL>',
          defaultName: 'SLIPKUY - ระบบตรวจสอบสลิป'
        },
        description: 'การตั้งค่าระบบส่งอีเมลของระบบ'
      });
      console.log('✅ เพิ่มการตั้งค่าระบบอีเมลเริ่มต้นสำเร็จ');
    } else {
      console.log('ℹ️ มีการตั้งค่าระบบอีเมลอยู่แล้ว');
    }
    
    // เพิ่มเทมเพลตอีเมลเริ่มต้น
    // ตรวจสอบว่ามีเทมเพลตอีเมลหรือไม่
    const templates = await db.select().from(schema.emailTemplates);
    
    if (templates.length === 0) {
      // เพิ่มเทมเพลตการแจ้งเตือนธุรกรรมผิดปกติ
      await db.insert(schema.emailTemplates).values({
        name: 'unusual_transaction',
        subject: 'SLIPKUY - แจ้งเตือน: ตรวจพบธุรกรรมผิดปกติ',
        htmlContent: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <div style="text-align: center; margin-bottom: 20px;">
              <img src="https://slipkuy.com/logo.png" alt="SLIPKUY Logo" style="width: 150px;">
            </div>
            <h2 style="color: #d32f2f;">แจ้งเตือน: ตรวจพบธุรกรรมผิดปกติ</h2>
            <p>เรียน {{userName}},</p>
            <p>ระบบได้ตรวจพบธุรกรรมที่มีมูลค่าสูงผิดปกติในบัญชีของคุณ:</p>
            <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 15px 0;">
              <p><strong>รหัสธุรกรรม:</strong> {{transactionRef}}</p>
              <p><strong>วันที่:</strong> {{transactionDate}}</p>
              <p><strong>จำนวนเงิน:</strong> ฿{{amount}} บาท</p>
              <p><strong>ธนาคารผู้ส่ง:</strong> {{sender}}</p>
              <p><strong>ธนาคารผู้รับ:</strong> {{receiver}}</p>
            </div>
            <p>กรุณาตรวจสอบธุรกรรมดังกล่าวว่าถูกต้องหรือไม่</p>
            <p>หากคุณไม่ได้ทำรายการดังกล่าว กรุณา <a href="{{supportLink}}" style="color: #1976d2;">ติดต่อเจ้าหน้าที่</a> โดยทันที</p>
            <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #e0e0e0; text-align: center; font-size: 12px; color: #757575;">
              <p>อีเมลนี้เป็นการแจ้งเตือนแบบอัตโนมัติ กรุณาอย่าตอบกลับ</p>
              <p>&copy; {{currentYear}} SLIPKUY - ระบบตรวจสอบสลิป</p>
            </div>
          </div>
        `,
        textContent: `
แจ้งเตือน: ตรวจพบธุรกรรมผิดปกติ

เรียน {{userName}},

ระบบได้ตรวจพบธุรกรรมที่มีมูลค่าสูงผิดปกติในบัญชีของคุณ:

รหัสธุรกรรม: {{transactionRef}}
วันที่: {{transactionDate}}
จำนวนเงิน: ฿{{amount}} บาท
ธนาคารผู้ส่ง: {{sender}}
ธนาคารผู้รับ: {{receiver}}

กรุณาตรวจสอบธุรกรรมดังกล่าวว่าถูกต้องหรือไม่ หากคุณไม่ได้ทำรายการดังกล่าว กรุณาติดต่อเจ้าหน้าที่โดยทันที: {{supportLink}}

อีเมลนี้เป็นการแจ้งเตือนแบบอัตโนมัติ กรุณาอย่าตอบกลับ

© {{currentYear}} SLIPKUY - ระบบตรวจสอบสลิป
        `,
        variables: ['userName', 'transactionRef', 'transactionDate', 'amount', 'sender', 'receiver', 'supportLink', 'currentYear'],
        isDefault: true
      });

      // เพิ่มเทมเพลตรายงานประจำวัน
      await db.insert(schema.emailTemplates).values({
        name: 'daily_report',
        subject: 'SLIPKUY - รายงานประจำวัน {{date}}',
        htmlContent: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <div style="text-align: center; margin-bottom: 20px;">
              <img src="https://slipkuy.com/logo.png" alt="SLIPKUY Logo" style="width: 150px;">
            </div>
            <h2 style="color: #2e7d32;">รายงานประจำวัน - {{date}}</h2>
            <p>เรียน {{userName}},</p>
            <p>นี่คือรายงานประจำวันสำหรับการใช้งาน SLIPKUY ของคุณ:</p>
            
            <h3 style="color: #1976d2; margin-top: 20px;">สรุปธุรกรรม</h3>
            <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 15px 0;">
              <p><strong>จำนวนธุรกรรมทั้งหมด:</strong> {{totalTransactions}} รายการ</p>
              <p><strong>มูลค่ารวม:</strong> ฿{{totalAmount}} บาท</p>
              <p><strong>มูลค่าสูงสุด:</strong> ฿{{maxAmount}} บาท</p>
              <p><strong>มูลค่าเฉลี่ย:</strong> ฿{{avgAmount}} บาท</p>
            </div>
            
            <h3 style="color: #1976d2; margin-top: 20px;">ธุรกรรมล่าสุด</h3>
            <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 15px 0;">
              {{recentTransactions}}
            </div>
            
            <p style="margin-top: 20px;">คุณสามารถดูรายละเอียดเพิ่มเติมได้ที่ <a href="{{dashboardLink}}" style="color: #1976d2;">หน้าแดชบอร์ด</a> ของคุณ</p>
            
            <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #e0e0e0; text-align: center; font-size: 12px; color: #757575;">
              <p>หากคุณไม่ต้องการรับรายงานนี้อีก คุณสามารถ <a href="{{unsubscribeLink}}" style="color: #1976d2;">ยกเลิกการรับอีเมล</a> ได้</p>
              <p>&copy; {{currentYear}} SLIPKUY - ระบบตรวจสอบสลิป</p>
            </div>
          </div>
        `,
        textContent: `
รายงานประจำวัน - {{date}}

เรียน {{userName}},

นี่คือรายงานประจำวันสำหรับการใช้งาน SLIPKUY ของคุณ:

สรุปธุรกรรม
---------------------------------
จำนวนธุรกรรมทั้งหมด: {{totalTransactions}} รายการ
มูลค่ารวม: ฿{{totalAmount}} บาท
มูลค่าสูงสุด: ฿{{maxAmount}} บาท
มูลค่าเฉลี่ย: ฿{{avgAmount}} บาท

ธุรกรรมล่าสุด
---------------------------------
{{recentTransactions}}

คุณสามารถดูรายละเอียดเพิ่มเติมได้ที่หน้าแดชบอร์ดของคุณ: {{dashboardLink}}

หากคุณไม่ต้องการรับรายงานนี้อีก คุณสามารถยกเลิกการรับอีเมลได้: {{unsubscribeLink}}

© {{currentYear}} SLIPKUY - ระบบตรวจสอบสลิป
        `,
        variables: ['userName', 'date', 'totalTransactions', 'totalAmount', 'maxAmount', 'avgAmount', 'recentTransactions', 'dashboardLink', 'unsubscribeLink', 'currentYear'],
        isDefault: true
      });

      // เพิ่มเทมเพลตแจ้งเตือน API Key ใกล้หมดอายุ
      await db.insert(schema.emailTemplates).values({
        name: 'api_key_expiring',
        subject: 'SLIPKUY - แจ้งเตือน: API Key ของคุณใกล้หมดอายุ',
        htmlContent: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <div style="text-align: center; margin-bottom: 20px;">
              <img src="https://slipkuy.com/logo.png" alt="SLIPKUY Logo" style="width: 150px;">
            </div>
            <h2 style="color: #ff9800;">แจ้งเตือน: API Key ของคุณใกล้หมดอายุ</h2>
            <p>เรียน {{userName}},</p>
            <p>เราขอแจ้งให้ทราบว่า API Key ของคุณจะหมดอายุใน <strong>{{daysLeft}} วัน</strong>:</p>
            <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 15px 0;">
              <p><strong>ชื่อ API Key:</strong> {{apiKeyName}}</p>
              <p><strong>วันที่หมดอายุ:</strong> {{expiryDate}}</p>
              <p><strong>จำนวนการใช้งาน:</strong> {{usageCount}} ครั้ง</p>
            </div>
            <p>เพื่อป้องกันการหยุดชะงักของบริการ กรุณาต่ออายุหรือสร้าง API Key ใหม่ก่อนวันที่หมดอายุ</p>
            <div style="text-align: center; margin: 30px 0;">
              <a href="{{renewLink}}" style="background-color: #1976d2; color: white; text-decoration: none; padding: 12px 24px; border-radius: 4px; display: inline-block; font-weight: bold;">ต่ออายุ API Key</a>
            </div>
            <p>หากคุณมีคำถามหรือต้องการความช่วยเหลือ กรุณา <a href="{{supportLink}}" style="color: #1976d2;">ติดต่อเจ้าหน้าที่</a></p>
            <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #e0e0e0; text-align: center; font-size: 12px; color: #757575;">
              <p>อีเมลนี้เป็นการแจ้งเตือนแบบอัตโนมัติ กรุณาอย่าตอบกลับ</p>
              <p>&copy; {{currentYear}} SLIPKUY - ระบบตรวจสอบสลิป</p>
            </div>
          </div>
        `,
        textContent: `
แจ้งเตือน: API Key ของคุณใกล้หมดอายุ

เรียน {{userName}},

เราขอแจ้งให้ทราบว่า API Key ของคุณจะหมดอายุใน {{daysLeft}} วัน:

ชื่อ API Key: {{apiKeyName}}
วันที่หมดอายุ: {{expiryDate}}
จำนวนการใช้งาน: {{usageCount}} ครั้ง

เพื่อป้องกันการหยุดชะงักของบริการ กรุณาต่ออายุหรือสร้าง API Key ใหม่ก่อนวันที่หมดอายุ

คุณสามารถต่ออายุ API Key ได้ที่: {{renewLink}}

หากคุณมีคำถามหรือต้องการความช่วยเหลือ กรุณาติดต่อเจ้าหน้าที่: {{supportLink}}

อีเมลนี้เป็นการแจ้งเตือนแบบอัตโนมัติ กรุณาอย่าตอบกลับ

© {{currentYear}} SLIPKUY - ระบบตรวจสอบสลิป
        `,
        variables: ['userName', 'apiKeyName', 'daysLeft', 'expiryDate', 'usageCount', 'renewLink', 'supportLink', 'currentYear'],
        isDefault: true
      });

      console.log('✅ เพิ่มเทมเพลตอีเมลเริ่มต้นสำเร็จ');
    } else {
      console.log('ℹ️ มีเทมเพลตอีเมลอยู่แล้ว');
    }

    return true;
  } catch (error) {
    console.error('❌ เกิดข้อผิดพลาดในการตั้งค่าระบบอีเมล:', error);
    return false;
  }
}

// เพิ่มกฎตรวจจับการฉ้อโกงเริ่มต้น
async function setupFraudRules() {
  try {
    // ตรวจสอบว่ามีกฎตรวจจับการฉ้อโกงหรือไม่
    const rules = await db.select().from(schema.fraudRules);
    
    if (rules.length === 0) {
      // กฎตรวจจับมูลค่าสูงผิดปกติ
      await db.insert(schema.fraudRules).values({
        name: 'ตรวจจับมูลค่าสูงผิดปกติ',
        description: 'ตรวจจับธุรกรรมที่มีมูลค่าสูงกว่าเกณฑ์ที่กำหนด',
        ruleType: 'unusual_amount',
        conditions: {
          thresholdAmount: 50000, // 50,000 บาท
          operator: 'greaterThan'
        },
        action: 'notify',
        severity: 'medium',
        isActive: true
      });
      
      // กฎตรวจจับสลิปซ้ำ
      await db.insert(schema.fraudRules).values({
        name: 'ตรวจจับสลิปซ้ำ',
        description: 'ตรวจจับการใช้รูปสลิปเดียวกันหลายครั้ง',
        ruleType: 'duplicate_slip',
        conditions: {
          timeWindow: 86400, // 24 ชั่วโมง (วินาที)
          minSimilarity: 0.9 // ความคล้ายคลึงกันขั้นต่ำ 90%
        },
        action: 'block',
        severity: 'high',
        isActive: true
      });
      
      // กฎตรวจจับการใช้งานผิดปกติ
      await db.insert(schema.fraudRules).values({
        name: 'ตรวจจับการใช้งานผิดปกติ',
        description: 'ตรวจจับการใช้งาน API บ่อยเกินไปในระยะเวลาสั้นๆ',
        ruleType: 'unusual_usage',
        conditions: {
          maxRequests: 100, // จำนวนคำขอสูงสุด
          timeWindow: 60 // ในเวลา 60 วินาที
        },
        action: 'notify',
        severity: 'low',
        isActive: true
      });
      
      console.log('✅ เพิ่มกฎตรวจจับการฉ้อโกงเริ่มต้นสำเร็จ');
    } else {
      console.log('ℹ️ มีกฎตรวจจับการฉ้อโกงอยู่แล้ว');
    }
    
    return true;
  } catch (error) {
    console.error('❌ เกิดข้อผิดพลาดในการตั้งค่ากฎตรวจจับการฉ้อโกง:', error);
    return false;
  }
}

// เพิ่มการตั้งค่าการแจ้งเตือนเริ่มต้นให้กับผู้ใช้
async function setupUserAlertSettings() {
  try {
    // ดึงรายชื่อผู้ใช้ทั้งหมด
    const users = await db.select().from(schema.users);
    
    for (const user of users) {
      // ตรวจสอบว่าผู้ใช้มีการตั้งค่าการแจ้งเตือนหรือไม่
      const alertSettings = await db.select().from(schema.alertSettings).where(sql`${schema.alertSettings.userId} = ${user.id}`);
      
      // ถ้ายังไม่มีการตั้งค่า ให้เพิ่มการตั้งค่าเริ่มต้น
      if (alertSettings.length === 0) {
        // การแจ้งเตือนธุรกรรมผิดปกติ
        await db.insert(schema.alertSettings).values({
          userId: user.id,
          alertType: 'unusual_transaction',
          enabled: true,
          channels: ['email', 'in_app'],
          threshold: {
            amount: 50000 // แจ้งเตือนเมื่อมูลค่าธุรกรรมเกิน 50,000 บาท
          }
        });
        
        // การแจ้งเตือน API Key ใกล้หมดอายุ
        await db.insert(schema.alertSettings).values({
          userId: user.id,
          alertType: 'api_key_expiring',
          enabled: true,
          channels: ['email', 'in_app'],
          threshold: {
            daysBeforeExpiry: 7 // แจ้งเตือนก่อนหมดอายุ 7 วัน
          }
        });
        
        // การแจ้งเตือนโควต้าใกล้หมด
        await db.insert(schema.alertSettings).values({
          userId: user.id,
          alertType: 'quota_low',
          enabled: true,
          channels: ['email', 'in_app'],
          threshold: {
            percentageLeft: 10 // แจ้งเตือนเมื่อเหลือโควต้า 10%
          }
        });
        
        // รายงานประจำวัน (ปิดไว้เริ่มต้น)
        await db.insert(schema.alertSettings).values({
          userId: user.id,
          alertType: 'daily_report',
          enabled: false,
          channels: ['email'],
          frequency: 'daily',
          timePreference: {
            hour: 8, // ส่งตอน 8 โมงเช้า
            minute: 0
          }
        });
        
        // รายงานประจำสัปดาห์ (เปิดไว้เริ่มต้น)
        await db.insert(schema.alertSettings).values({
          userId: user.id,
          alertType: 'weekly_report',
          enabled: true,
          channels: ['email'],
          frequency: 'weekly',
          timePreference: {
            dayOfWeek: 1, // วันจันทร์
            hour: 8, // 8 โมงเช้า
            minute: 0
          }
        });
        
        console.log(`✅ เพิ่มการตั้งค่าการแจ้งเตือนเริ่มต้นให้กับผู้ใช้ ${user.username} สำเร็จ`);
      } else {
        console.log(`ℹ️ ผู้ใช้ ${user.username} มีการตั้งค่าการแจ้งเตือนอยู่แล้ว`);
      }
    }
    
    return true;
  } catch (error) {
    console.error('❌ เกิดข้อผิดพลาดในการตั้งค่าการแจ้งเตือนเริ่มต้นให้กับผู้ใช้:', error);
    return false;
  }
}

// เพิ่ม Enum types ใหม่สำหรับฟีเจอร์ใหม่
async function updateEnums() {
  try {
    // เพิ่ม Enum types สำหรับ tier ของลูกค้า และ webhook
    await db.execute(sql`
      DO $$ 
      BEGIN 
        -- สร้าง enum customer_tier ถ้ายังไม่มี
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'customer_tier') THEN
          CREATE TYPE customer_tier AS ENUM (
            'standard', 'premium', 'vip', 'enterprise'
          );
        END IF;

        -- สร้าง enum webhook_event_type ถ้ายังไม่มี
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'webhook_event_type') THEN
          CREATE TYPE webhook_event_type AS ENUM (
            'slip_verification', 'quota_low', 'credit_low', 
            'api_key_expire', 'package_expire', 'fraud_detected', 'system_update'
          );
        END IF;

        -- อัพเดท enum alert_type ถ้ามีแล้ว (เพิ่ม package_recommendation)
        IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'alert_type') THEN
          ALTER TYPE alert_type ADD VALUE IF NOT EXISTS 'package_recommendation';
        END IF;
      END $$;
    `);
    console.log('✅ อัพเดท Enum Types ใหม่สำเร็จ');
    return true;
  } catch (error) {
    console.error('❌ ไม่สามารถอัพเดท Enum Types ใหม่ได้:', error);
    return false;
  }
}

// เพิ่มคอลัมน์ tier ในตาราง users และสร้างตารางใหม่
async function updateSchema() {
  try {
    // เพิ่มคอลัมน์ tier ในตาราง users ถ้ายังไม่มี
    await db.execute(sql`
      DO $$
      BEGIN
        IF NOT EXISTS (
          SELECT 1
          FROM information_schema.columns
          WHERE table_name = 'users' AND column_name = 'tier'
        ) THEN
          ALTER TABLE users ADD COLUMN tier customer_tier NOT NULL DEFAULT 'standard';
        END IF;
      END $$;
    `);

    // สร้างตาราง webhooks
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS webhooks (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id),
        name TEXT NOT NULL,
        url TEXT NOT NULL,
        secret TEXT,
        event_types webhook_event_type[],
        is_active BOOLEAN NOT NULL DEFAULT TRUE,
        headers JSONB,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
        last_triggered_at TIMESTAMP
      );
    `);

    // สร้างตาราง webhook_logs
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS webhook_logs (
        id SERIAL PRIMARY KEY,
        webhook_id INTEGER NOT NULL REFERENCES webhooks(id),
        event_type webhook_event_type NOT NULL,
        payload JSONB NOT NULL,
        response_status INTEGER,
        response_body TEXT,
        duration INTEGER,
        success BOOLEAN NOT NULL DEFAULT FALSE,
        error TEXT,
        retry_count INTEGER NOT NULL DEFAULT 0,
        created_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
    `);

    // สร้างตาราง customer_behavior
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS customer_behavior (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id),
        avg_daily_verifications INTEGER DEFAULT 0,
        avg_weekly_verifications INTEGER DEFAULT 0,
        avg_monthly_verifications INTEGER DEFAULT 0,
        peak_usage_time JSONB,
        most_used_bank TEXT,
        avg_transaction_amount DOUBLE PRECISION,
        last_activity_date TIMESTAMP,
        api_usage_pattern JSONB,
        recommended_package_id INTEGER REFERENCES packages(id),
        analysis_date TIMESTAMP NOT NULL DEFAULT NOW(),
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
    `);

    console.log('✅ อัพเดทโครงสร้างฐานข้อมูลสำเร็จ');
    return true;
  } catch (error) {
    console.error('❌ ไม่สามารถอัพเดทโครงสร้างฐานข้อมูลได้:', error);
    return false;
  }
}

// ฟังก์ชันหลักในการอัปเดตฐานข้อมูล
async function main() {
  console.log('🚀 เริ่มต้นอัปเดตฐานข้อมูล...');
  
  // ตรวจสอบการเชื่อมต่อฐานข้อมูล
  const connected = await checkConnection();
  if (!connected) {
    console.error('❌ ไม่สามารถเชื่อมต่อกับฐานข้อมูลได้ กรุณาตรวจสอบการเชื่อมต่อของคุณ');
    process.exit(1);
  }
  
  // สร้าง Enum types
  const enumsCreated = await createEnums();
  if (!enumsCreated) {
    console.error('❌ ไม่สามารถสร้าง Enum types ได้ กรุณาตรวจสอบข้อผิดพลาด');
    process.exit(1);
  }
  
  // อัพเดท Enum types ใหม่
  const enumsUpdated = await updateEnums();
  if (!enumsUpdated) {
    console.error('❌ ไม่สามารถอัพเดท Enum types ใหม่ได้ กรุณาตรวจสอบข้อผิดพลาด');
    process.exit(1);
  }
  
  // อัพเดทโครงสร้างฐานข้อมูล
  const schemaUpdated = await updateSchema();
  if (!schemaUpdated) {
    console.error('❌ ไม่สามารถอัพเดทโครงสร้างฐานข้อมูลได้ กรุณาตรวจสอบข้อผิดพลาด');
    process.exit(1);
  }
  
  // สร้างตาราง
  const tablesCreated = await createTables();
  if (!tablesCreated) {
    console.error('❌ ไม่สามารถสร้างตารางได้ กรุณาตรวจสอบข้อผิดพลาด');
    process.exit(1);
  }
  
  // ตั้งค่าระบบอีเมล
  await setupEmailSystem();
  
  // ตั้งค่ากฎตรวจจับการฉ้อโกง
  await setupFraudRules();
  
  // ตั้งค่าการแจ้งเตือนเริ่มต้นให้กับผู้ใช้
  await setupUserAlertSettings();
  
  console.log('✅ อัปเดตฐานข้อมูลสำเร็จ');
}

// รันฟังก์ชันหลัก
main().catch(error => {
  console.error('❌ เกิดข้อผิดพลาดในการอัปเดตฐานข้อมูล:', error);
  process.exit(1);
});