/**
 * ระบบติดตามและจัดการ session แบบเรียลไทม์
 * สามารถติดตามผู้ใช้ที่กำลังออนไลน์, สถานะการใช้งาน, และจัดการการเตะผู้ใช้ออกจากระบบ
 */

import { storage } from "../storage";
import { Request, Response } from "express";

// เก็บข้อมูลผู้ใช้ที่กำลังออนไลน์
interface OnlineUser {
  userId: number;
  username: string;
  sessionId: string;
  ip: string;
  userAgent: string;
  lastActivity: Date;
  location: string;
  role: string;
  loginTime: Date;
}

// คลาสสำหรับติดตามผู้ใช้แบบเรียลไทม์
export class SessionMonitor {
  private onlineUsers: Map<string, OnlineUser> = new Map();
  private sessionActivity: Map<string, Date> = new Map();
  private sessionBlacklist: Set<string> = new Set();
  private userAccessHistory: Map<number, { timestamp: Date, count: number }[]> = new Map();
  public hourlyStats: { hour: number, activeUsers: number, newSessions: number }[] = [];
  private lastHourlyUpdate: Date = new Date();
  public dailySessionCounts: Map<string, number> = new Map(); // วัน -> จำนวนเซสชัน
  public userRanking: { userId: number, username: string, sessionCount: number, lastActive: Date }[] = [];
  
  // เก็บข้อมูลเซสชันที่ลิงก์กับเครื่องมือ/อุปกรณ์
  private deviceSessions: Map<string, Set<string>> = new Map(); // fingerprint -> เซตของ sessionIds
  private suspiciousActivity: Map<string, { count: number, lastDetected: Date }> = new Map(); // IP -> ข้อมูลกิจกรรมต้องสงสัย
  
  constructor() {
    // เริ่มต้นสถิติรายชั่วโมง
    const now = new Date();
    const currentHour = now.getHours();
    
    // สร้างข้อมูลสถิติสำหรับ 24 ชั่วโมงย้อนหลัง
    for (let i = 0; i < 24; i++) {
      const hour = (currentHour - i + 24) % 24;
      this.hourlyStats.push({
        hour,
        activeUsers: 0,
        newSessions: 0 // ใช้ข้อมูลจริงจากฐานข้อมูลเท่านั้น ไม่ใช้ข้อมูลจำลอง
      });
    }
    
    // ตั้งเวลาอัพเดทสถิติรายชั่วโมงทุก 5 นาที
    setInterval(() => {
      this.updateHourlyStats();
      this.updateUserRanking();
    }, 5 * 60 * 1000);
    
    // เริ่มต้นอัพเดทข้อมูล
    this.updateHourlyStats();
    this.updateUserRanking();
  }
  
  // อัพเดทสถิติรายชั่วโมง
  private updateHourlyStats(): void {
    const now = new Date();
    const currentHour = now.getHours();
    const lastUpdateHour = this.lastHourlyUpdate.getHours();
    
    if (currentHour !== lastUpdateHour) {
      // เพิ่มชั่วโมงใหม่เข้าไปและลบชั่วโมงเก่าสุดออก
      this.hourlyStats.pop();
      this.hourlyStats.unshift({
        hour: currentHour,
        activeUsers: this.getOnlineUsers().length,
        newSessions: 0
      });
      
      this.lastHourlyUpdate = now;
    } else {
      // อัพเดทข้อมูลชั่วโมงปัจจุบัน
      this.hourlyStats[0].activeUsers = this.getOnlineUsers().length;
    }
    
    // อัพเดทสถิติรายวัน
    const today = now.toISOString().split('T')[0];
    const todayCount = this.dailySessionCounts.get(today) || 0;
    this.dailySessionCounts.set(today, this.getAllSessions().length);
    
    // เก็บข้อมูลไม่เกิน 30 วัน
    if (this.dailySessionCounts.size > 30) {
      const oldest = Array.from(this.dailySessionCounts.keys()).sort()[0];
      this.dailySessionCounts.delete(oldest);
    }
  }
  
  // อัพเดทการจัดอันดับผู้ใช้
  private updateUserRanking(): void {
    const userSessionCounts = new Map<number, { count: number, username: string, lastActive: Date }>();
    
    // นับจำนวนเซสชันของแต่ละผู้ใช้
    for (const user of this.onlineUsers.values()) {
      const existing = userSessionCounts.get(user.userId);
      if (existing) {
        existing.count++;
        if (user.lastActivity > existing.lastActive) {
          existing.lastActive = user.lastActivity;
        }
      } else {
        userSessionCounts.set(user.userId, { 
          count: 1, 
          username: user.username,
          lastActive: user.lastActivity
        });
      }
    }
    
    // แปลงเป็นอาเรย์และเรียงลำดับ
    this.userRanking = Array.from(userSessionCounts.entries())
      .map(([userId, data]) => ({
        userId,
        username: data.username,
        sessionCount: data.count,
        lastActive: data.lastActive
      }))
      .sort((a, b) => b.sessionCount - a.sessionCount);
  }
  
  // ตรวจสอบการใช้ session ID ซ้ำจากอุปกรณ์หลายเครื่อง
  public trackDeviceActivity(sessionId: string, fingerprint: string): boolean {
    if (!fingerprint || !sessionId) return false;
    
    // เก็บข้อมูล fingerprint ที่กำลังใช้งานสำหรับแต่ละ session - เราจะเก็บเพียงค่า base (ค่าแรก)
    // และค่าล่าสุดที่เห็น เพื่อให้มีความยืดหยุ่นในการเปลี่ยนแปลงเล็กน้อย
    const user = this.onlineUsers.get(sessionId);
    if (!user) {
      return false; // ไม่มีข้อมูลผู้ใช้สำหรับ session นี้
    }
    
    // สร้างเซตของ sessionIds สำหรับ fingerprint นี้ถ้ายังไม่มี
    if (!this.deviceSessions.has(fingerprint)) {
      this.deviceSessions.set(fingerprint, new Set());
    }
    
    // เพิ่ม sessionId นี้เข้าไปในเซตของ fingerprint
    const sessionsForDevice = this.deviceSessions.get(fingerprint);
    if (sessionsForDevice) {
      sessionsForDevice.add(sessionId);
    }
    
    // ในระหว่างการพัฒนา เราจะไม่มีการตรวจสอบและบล็อก session ID ซ้ำ
    // แต่จะเก็บบันทึกการพบเจอเพื่อการตรวจสอบแทน
    if (process.env.NODE_ENV === 'development') {
      return false;
    }
    
    // ตรวจสอบการใช้งาน session ID ซ้ำบนอุปกรณ์ที่แตกต่างกันมาก (เกินระดับความเหมือนที่ยอมรับได้)
    // เราจะยอมรับความแตกต่างของ fingerprint เล็กน้อยได้
    for (const [otherFingerprint, sessionSet] of this.deviceSessions) {
      // ข้ามถ้าเป็น fingerprint เดียวกัน
      if (otherFingerprint === fingerprint) continue;

      // คำนวณความเหมือนระหว่าง fingerprint (ใช้วิธีง่ายๆ โดยนับตัวอักษรที่ตรงกัน)
      const fingerprintSimilarity = this.calculateFingerprintSimilarity(fingerprint, otherFingerprint);
      
      // ถ้าความเหมือนน้อยเกินไป (fingerprint ต่างกันมาก) และใช้ session ID เดียวกัน
      // ให้ถือว่าเป็นการใช้งานที่ต้องสงสัย
      const similarityThreshold = 0.3; // ลดความเข้มงวดลงเพื่อให้ใช้งานได้ง่ายขึ้น
      
      // ตรวจสอบว่ามี sessionId ซ้ำกันและความเหมือนต่ำกว่าเกณฑ์หรือไม่
      if (sessionSet.has(sessionId) && fingerprintSimilarity < similarityThreshold) {
        console.warn(`[SECURITY ALERT] Session ID ซ้ำพบบนอุปกรณ์ต่างกัน: 
          Session ID: ${sessionId}
          Current Fingerprint: ${fingerprint}
          Other Fingerprint: ${otherFingerprint}
          Similarity: ${(fingerprintSimilarity * 100).toFixed(2)}%
        `);
        
        // บันทึกว่าพบการใช้งานที่ต้องสงสัย
        const ip = user.ip || "unknown";
        const suspiciousIp = this.suspiciousActivity.get(ip) || { count: 0, lastDetected: new Date() };
        suspiciousIp.count++;
        suspiciousIp.lastDetected = new Date();
        this.suspiciousActivity.set(ip, suspiciousIp);
        
        // แบล็คลิสต์ session นี้ทันที - เฉพาะในสถานการณ์ที่ไม่ใช่การพัฒนา
        // ในโหมดพัฒนาจะไม่เตะผู้ใช้ออกจากระบบ
        if (process.env.NODE_ENV === 'production') {
          this.blacklistSession(sessionId);
          return true;
        }
      }
    }
    
    return false;
  }
  
  // คำนวณความเหมือนระหว่าง fingerprint สองค่า
  private calculateFingerprintSimilarity(fp1: string, fp2: string): number {
    if (!fp1 || !fp2) return 0;
    
    // ตรวจสอบความยาวที่เท่ากัน
    if (fp1.length !== fp2.length) {
      // ถ้าความยาวไม่เท่ากัน ให้นำค่าที่สั้นกว่ามาเทียบ
      const minLength = Math.min(fp1.length, fp2.length);
      fp1 = fp1.substring(0, minLength);
      fp2 = fp2.substring(0, minLength);
    }
    
    // นับจำนวนตัวอักษรที่ตรงกัน
    let sameChars = 0;
    for (let i = 0; i < fp1.length; i++) {
      if (fp1[i] === fp2[i]) {
        sameChars++;
      }
    }
    
    return sameChars / fp1.length;
  }
  
  // บันทึกกิจกรรมล่าสุดของผู้ใช้
  public trackActivity(sessionId: string, req: Request): void {
    if (!sessionId || this.isSessionBlacklisted(sessionId)) return;
    
    const now = new Date();
    const wasActive = this.sessionActivity.has(sessionId);
    this.sessionActivity.set(sessionId, now);
    
    // ตรวจสอบการใช้ session ซ้ำกับ device fingerprint
    const fingerprint = (req.session as any)?.fingerprint;
    if (fingerprint && req.isAuthenticated()) {
      const isSuspicious = this.trackDeviceActivity(sessionId, fingerprint);
      if (isSuspicious) {
        // ถ้าพบว่ามีความผิดปกติ ให้ออกจากฟังก์ชันเลย
        return;
      }
    }
    
    // อัพเดทข้อมูลผู้ใช้ที่ออนไลน์
    if (req.isAuthenticated() && req.user) {
      const user = req.user;
      const sessionData = req.session as any;
      const userId = user.id;
      
      // บันทึกประวัติการเข้าใช้งาน
      if (!this.userAccessHistory.has(userId)) {
        this.userAccessHistory.set(userId, []);
      }
      
      const userHistory = this.userAccessHistory.get(userId);
      if (userHistory) {
        // เพิ่มจำนวนการเข้าใช้งานในรอบนี้หรือเพิ่มรายการใหม่ถ้าเวลาผ่านไปมากกว่า 5 นาที
        const lastAccess = userHistory.length > 0 ? userHistory[userHistory.length - 1] : null;
        if (lastAccess && (now.getTime() - lastAccess.timestamp.getTime() < 5 * 60 * 1000)) {
          lastAccess.count++;
        } else {
          userHistory.push({ timestamp: now, count: 1 });
          
          // ถ้าเป็น session ใหม่ให้เพิ่มจำนวน newSessions ในชั่วโมงปัจจุบัน
          if (!wasActive) {
            this.hourlyStats[0].newSessions++;
          }
        }
        
        // จำกัดประวัติการเข้าใช้งานให้เก็บแค่ 100 รายการล่าสุด
        if (userHistory.length > 100) {
          userHistory.shift();
        }
      }
      
      this.onlineUsers.set(sessionId, {
        userId: user.id,
        username: user.username,
        sessionId,
        ip: req.ip || sessionData.ip || 'unknown',
        userAgent: req.headers['user-agent'] || sessionData.userAgent || 'unknown',
        lastActivity: now,
        location: sessionData.location || 'ประเทศไทย',
        role: user.role || 'user',
        loginTime: new Date(sessionData.loginTime || now)
      });
    }
  }
  
  // เพิ่ม sessionId เข้าแบล็คลิสต์ (เมื่อเตะผู้ใช้ออก)
  public blacklistSession(sessionId: string): boolean {
    if (!sessionId) return false;
    
    this.sessionBlacklist.add(sessionId);
    this.onlineUsers.delete(sessionId);
    
    // ลบจากแบล็คลิสต์หลังจาก 30 นาที เพื่อป้องกันการสะสมข้อมูลมากเกินไป
    setTimeout(() => {
      this.sessionBlacklist.delete(sessionId);
    }, 30 * 60 * 1000);
    
    return true;
  }
  
  // ตรวจสอบว่า session อยู่ในแบล็คลิสต์หรือไม่
  public isSessionBlacklisted(sessionId: string): boolean {
    return this.sessionBlacklist.has(sessionId);
  }
  
  // ดึงข้อมูลผู้ใช้ที่กำลังออนไลน์ทั้งหมด
  public getOnlineUsers(): OnlineUser[] {
    const now = new Date();
    const activeUsers: OnlineUser[] = [];
    
    // กรองเฉพาะผู้ใช้ที่มีกิจกรรมในช่วงเวลาที่ผ่านมา
    this.onlineUsers.forEach((user) => {
      const lastActivityTime = user.lastActivity.getTime();
      const timeDiff = now.getTime() - lastActivityTime;
      
      // ถ้ามีกิจกรรมใน 3 นาทีล่าสุด ถือว่ายังออนไลน์อยู่ (ลดลงจาก 5 นาที)
      if (timeDiff < 3 * 60 * 1000) {
        activeUsers.push(user);
      }
    });
    
    return activeUsers;
  }
  
  // ดึงข้อมูลผู้ใช้ทั้งหมดที่มี session (ทั้งออนไลน์และไม่ได้ออนไลน์แล้ว)
  public getAllSessions(): OnlineUser[] {
    return Array.from(this.onlineUsers.values());
  }
  
  // ดึงสถิติของ session
  public getSessionStats() {
    const activeUsers = this.getOnlineUsers();
    const totalSessions = this.onlineUsers.size;
    const adminUsers = activeUsers.filter(user => user.role === 'admin').length;
    const regularUsers = activeUsers.filter(user => user.role !== 'admin').length;
    const suspiciousActivities = this.suspiciousActivity.size;
    
    return {
      activeUsers: activeUsers.length,
      totalSessions,
      adminUsers,
      regularUsers,
      blacklistedSessions: this.sessionBlacklist.size,
      suspiciousActivities,
      deviceFingerprints: this.deviceSessions.size
    };
  }
  
  // ดึงข้อมูลกิจกรรมที่น่าสงสัย
  public getSuspiciousActivities() {
    const result: { ip: string, count: number, lastDetected: Date }[] = [];
    
    this.suspiciousActivity.forEach((data, ip) => {
      result.push({
        ip,
        count: data.count,
        lastDetected: data.lastDetected
      });
    });
    
    return result.sort((a, b) => b.count - a.count);
  }
  
  // เคลียร์ session ที่ไม่ได้ใช้งานเป็นเวลานาน
  public cleanupInactiveSessions(): number {
    const now = new Date();
    let cleanedCount = 0;
    
    this.onlineUsers.forEach((user, sessionId) => {
      const lastActivityTime = user.lastActivity.getTime();
      const timeDiff = now.getTime() - lastActivityTime;
      
      // ถ้าไม่มีกิจกรรมเกิน 10 นาที ถือว่าไม่ได้ออนไลน์แล้ว
      // (ลดลงจาก 2 ชั่วโมงเป็น 10 นาทีเพื่อการตรวจจับที่แม่นยำมากขึ้น)
      if (timeDiff > 10 * 60 * 1000) {
        console.log(`[SESSION CLEANUP] Removing inactive session: ${sessionId}, username: ${user.username}, inactive for ${Math.round(timeDiff/60000)} minutes`);
        this.onlineUsers.delete(sessionId);
        cleanedCount++;
      }
    });
    
    return cleanedCount;
  }
  
  // เตะผู้ใช้ออกจากระบบโดยใช้ userId
  public kickUserById(userId: number): string[] {
    const kickedSessions: string[] = [];
    
    this.onlineUsers.forEach((user, sessionId) => {
      if (user.userId === userId) {
        this.blacklistSession(sessionId);
        kickedSessions.push(sessionId);
      }
    });
    
    return kickedSessions;
  }
  
  // เตะผู้ใช้ออกจากระบบโดยใช้ sessionId
  public kickSessionById(sessionId: string): boolean {
    return this.blacklistSession(sessionId);
  }
}

// สร้าง instance เดียวสำหรับใช้ทั่วทั้งแอพ
export const sessionMonitor = new SessionMonitor();

// API endpoints สำหรับจัดการ session
export function registerSessionMonitorRoutes(app: any) {

  // Middleware สำหรับเช็คสิทธิ์แอดมิน
  const requireAdmin = (req: Request, res: Response, next: Function) => {
    if (!req.isAuthenticated()) {
      console.log("[DEBUG] ไม่ได้ล็อกอิน:", { 
        url: req.url,
        method: req.method,
        ip: req.ip,
        headers: req.headers
      });
      return res.status(401).json({ message: "กรุณาเข้าสู่ระบบก่อนใช้งาน" });
    }
    
    if (!req.user || req.user.role !== 'admin') {
      console.log("[DEBUG] ไม่ใช่แอดมิน:", { 
        user: req.user,
        url: req.url,
        method: req.method
      });
      return res.status(403).json({ message: "สิทธิ์ไม่เพียงพอสำหรับการดำเนินการนี้" });
    }
    
    console.log("[DEBUG] Admin API Access:", {
      url: req.url,
      userId: req.user.id,
      role: req.user.role,
      sessionID: req.sessionID
    });
    
    next();
  };

  // Middleware สำหรับติดตามกิจกรรมของผู้ใช้
  app.use((req: Request, res: Response, next: Function) => {
    if (req.session) {
      const sessionId = req.sessionID;
      
      // ตรวจสอบว่า session อยู่ในแบล็คลิสต์หรือไม่
      if (sessionMonitor.isSessionBlacklisted(sessionId)) {
        // ถ้า session อยู่ในแบล็คลิสต์ ให้ล้าง session และส่งสถานะ 401
        req.session.destroy((err) => {
          res.clearCookie('slipkuy.sid');
          return res.status(401).json({ message: "คุณถูกเตะออกจากระบบโดยผู้ดูแล" });
        });
        return;
      }
      
      // บันทึกเวลาเริ่มต้น login หากยังไม่มี
      if (req.isAuthenticated() && !(req.session as any).loginTime) {
        (req.session as any).loginTime = new Date().toISOString();
      }
      
      // บันทึกกิจกรรมของผู้ใช้
      sessionMonitor.trackActivity(sessionId, req);
    }
    
    next();
  });

  // API สำหรับดึงข้อมูลผู้ใช้ที่กำลังออนไลน์ (สำหรับแอดมิน)
  app.get('/api/admin/online-users', requireAdmin, (req: Request, res: Response) => {
    const activeUsers = sessionMonitor.getOnlineUsers();
    res.setHeader('Content-Type', 'application/json');
    res.send(JSON.stringify(activeUsers));
  });

  // API สำหรับดึงข้อมูลสถิติของ session (สำหรับแอดมิน)
  app.get('/api/admin/session-stats', requireAdmin, (req: Request, res: Response) => {
    const stats = sessionMonitor.getSessionStats();
    res.setHeader('Content-Type', 'application/json');
    res.send(JSON.stringify(stats));
  });

  // API สำหรับดึงข้อมูล session ทั้งหมด (สำหรับแอดมิน)
  app.get('/api/admin/all-sessions', requireAdmin, (req: Request, res: Response) => {
    const allSessions = sessionMonitor.getAllSessions();
    res.setHeader('Content-Type', 'application/json');
    res.send(JSON.stringify(allSessions));
  });

  // API สำหรับเตะผู้ใช้ออกจากระบบโดยใช้ userId (สำหรับแอดมิน)
  app.post('/api/admin/kick-user', requireAdmin, (req: Request, res: Response) => {
    const { userId } = req.body;
    
    if (!userId) {
      return res.status(400).json({ message: "กรุณาระบุ userId" });
    }
    
    // ป้องกันไม่ให้แอดมินเตะตัวเองออกจากระบบ
    if (req.user && req.user.id === parseInt(userId)) {
      return res.status(400).json({ 
        success: false, 
        message: "ไม่สามารถเตะตัวคุณเองออกจากระบบได้" 
      });
    }
    
    const kickedSessions = sessionMonitor.kickUserById(parseInt(userId));
    
    res.status(200).json({ 
      success: true, 
      message: `เตะผู้ใช้ออกจากระบบสำเร็จ (${kickedSessions.length} sessions)`,
      kickedSessions
    });
  });

  // API สำหรับเตะผู้ใช้ออกจากระบบโดยใช้ sessionId (สำหรับแอดมิน)
  app.post('/api/admin/kick-session', requireAdmin, (req: Request, res: Response) => {
    const { sessionId } = req.body;
    
    if (!sessionId) {
      return res.status(400).json({ message: "กรุณาระบุ sessionId" });
    }
    
    // ป้องกันไม่ให้แอดมินเตะตัวเองออกจากระบบ
    if (req.user && sessionId === req.sessionID) {
      return res.status(400).json({ 
        success: false, 
        message: "ไม่สามารถเตะเซสชันปัจจุบันของคุณออกจากระบบได้" 
      });
    }
    
    const result = sessionMonitor.kickSessionById(sessionId);
    
    res.status(200).json({ 
      success: result, 
      message: result ? "เตะผู้ใช้ออกจากระบบสำเร็จ" : "ไม่พบเซสชันดังกล่าว"
    });
  });

  // API สำหรับเคลียร์ session ที่ไม่ได้ใช้งาน (สำหรับแอดมิน)
  app.post('/api/admin/cleanup-inactive-sessions', requireAdmin, (req: Request, res: Response) => {
    const cleanedCount = sessionMonitor.cleanupInactiveSessions();
    
    res.status(200).json({ 
      success: true, 
      message: `เคลียร์ session ที่ไม่ได้ใช้งานสำเร็จ (${cleanedCount} sessions)`,
      cleanedCount
    });
  });

  // API สำหรับดึงข้อมูลสถิติรายชั่วโมง (สำหรับแอดมิน)
  app.get('/api/admin/hourly-stats', requireAdmin, (req: Request, res: Response) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(JSON.stringify(sessionMonitor.hourlyStats));
  });

  // API สำหรับดึงข้อมูลการจัดอันดับผู้ใช้ (สำหรับแอดมิน)
  app.get('/api/admin/user-ranking', requireAdmin, (req: Request, res: Response) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(JSON.stringify(sessionMonitor.userRanking));
  });

  // API สำหรับดึงข้อมูลทั้งหมดสำหรับแดชบอร์ดเรียลไทม์ (สำหรับแอดมิน)
  app.get('/api/admin/realtime-dashboard', requireAdmin, (req: Request, res: Response) => {
    try {
      const sessionStats = sessionMonitor.getSessionStats();
      
      res.setHeader('Content-Type', 'application/json');
      res.send(JSON.stringify({
        success: true,
        sessionStats,
        onlineUsers: sessionMonitor.getOnlineUsers(),
        hourlyStats: sessionMonitor.hourlyStats,
        userRanking: sessionMonitor.userRanking,
        dailySessionCounts: Object.fromEntries(sessionMonitor.dailySessionCounts)
      }));
    } catch (err) {
      console.error("[ERROR] ดึงข้อมูลแดชบอร์ดเรียลไทม์:", err);
      res.status(500).json({ 
        success: false, 
        message: "เกิดข้อผิดพลาดในการดึงข้อมูลแดชบอร์ดเรียลไทม์" 
      });
    }
  });

  // API สำหรับดึงข้อมูลกิจกรรมที่น่าสงสัย
  app.get('/api/admin/suspicious-activities', requireAdmin, (req: Request, res: Response) => {
    try {
      const suspiciousActivities = sessionMonitor.getSuspiciousActivities();
      res.setHeader('Content-Type', 'application/json');
      res.send(JSON.stringify({
        success: true,
        suspiciousActivities
      }));
    } catch (err) {
      console.error("[ERROR] ดึงข้อมูลกิจกรรมที่น่าสงสัย:", err);
      res.status(500).json({ 
        success: false, 
        message: "เกิดข้อผิดพลาดในการดึงข้อมูลกิจกรรมที่น่าสงสัย" 
      });
    }
  });

  console.log('Session monitor API routes registered');

  // ตั้งเวลาเคลียร์ session ที่ไม่ได้ใช้งานทุก 1 ชั่วโมง
  setInterval(() => {
    const cleanedCount = sessionMonitor.cleanupInactiveSessions();
    if (cleanedCount > 0) {
      console.log(`[Session Cleanup] เคลียร์ session ที่ไม่ได้ใช้งานอัตโนมัติ: ${cleanedCount} sessions`);
    }
  }, 60 * 60 * 1000);
}