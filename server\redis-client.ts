import Redis from 'ioredis';
import { logger } from './logger';
import path from 'path';

// สร้าง Redis client
const redisClient = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD || undefined,
  db: parseInt(process.env.REDIS_DB || '0'),
  retryStrategy: (times) => {
    const delay = Math.min(times * 50, 2000);
    logger.warn(`Redis connection attempt ${times} failed. Retrying in ${delay}ms...`);
    return delay;
  }
});

// ตรวจสอบการเชื่อมต่อ
redisClient.on('connect', () => {
  logger.success('Redis client connected successfully');
});

redisClient.on('ready', () => {
  logger.info('Redis client is ready to use');
});

redisClient.on('error', (err) => {
  logger.error('Redis client error:', err);
});

redisClient.on('reconnecting', () => {
  logger.warn('Redis client reconnecting...');
});

// ฟังก์ชันสำหรับเก็บข้อมูล QR code ใน Redis
export async function cacheQRCodeVerification(userId: number, qrData: string, verificationData: any, expirySeconds: number = 86400): Promise<void> {
  try {
    const key = `qrcode:${userId}:${qrData}`;
    await redisClient.set(key, JSON.stringify(verificationData), 'EX', expirySeconds);
    logger.debug(`Cached QR code verification data for user ${userId}, QR: ${qrData.substring(0, 20)}...`);
  } catch (error) {
    logger.error('Error caching QR code verification:', error);
  }
}

// ฟังก์ชันสำหรับดึงข้อมูล QR code จาก Redis
export async function getCachedQRCodeVerification(userId: number, qrData: string): Promise<any | null> {
  try {
    const key = `qrcode:${userId}:${qrData}`;
    const cachedData = await redisClient.get(key);

    if (cachedData) {
      logger.debug(`Cache hit for QR code verification: user ${userId}, QR: ${qrData.substring(0, 20)}...`);
      return JSON.parse(cachedData);
    }

    logger.debug(`Cache miss for QR code verification: user ${userId}, QR: ${qrData.substring(0, 20)}...`);
    return null;
  } catch (error) {
    logger.error('Error getting cached QR code verification:', error);
    return null;
  }
}

// ฟังก์ชันสำหรับลบข้อมูล QR code จาก Redis
export async function removeCachedQRCodeVerification(userId: number, qrData: string): Promise<boolean> {
  try {
    const key = `qrcode:${userId}:${qrData}`;
    const result = await redisClient.del(key);

    if (result > 0) {
      logger.debug(`Removed cached QR code verification for user ${userId}, QR: ${qrData.substring(0, 20)}...`);
      return true;
    }

    logger.debug(`No cached QR code verification found to remove for user ${userId}, QR: ${qrData.substring(0, 20)}...`);
    return false;
  } catch (error) {
    logger.error('Error removing cached QR code verification:', error);
    return false;
  }
}

// ฟังก์ชันสำหรับทดสอบการเชื่อมต่อ Redis
export async function testRedisConnection(): Promise<boolean> {
  try {
    const result = await redisClient.ping();
    logger.success(`Redis connection test: ${result}`);
    return result === 'PONG';
  } catch (error) {
    logger.error('Redis connection test failed:', error);
    return false;
  }
}

// ฟังก์ชันสำหรับล้างข้อมูลทั้งหมดใน Redis (ใช้ในกรณีทดสอบเท่านั้น)
export async function flushRedisCache(): Promise<void> {
  try {
    await redisClient.flushdb();
    logger.warn('Redis cache flushed (all data cleared)');
  } catch (error) {
    logger.error('Error flushing Redis cache:', error);
  }
}

// ฟังก์ชันสำหรับเก็บข้อมูลชื่อไฟล์สลิปใน Redis
export async function cacheSlipFilename(userId: number, filename: string, qrData: string, verificationData: any, expirySeconds: number = 86400): Promise<void> {
  try {
    // ดึงชื่อไฟล์ล้วนๆ ไม่รวมพาธ
    const baseFilename = path.basename(filename);
    const key = `filename:${userId}:${baseFilename}`;

    const cacheData = {
      qrData,
      verificationData
    };

    await redisClient.set(key, JSON.stringify(cacheData), 'EX', expirySeconds);
    logger.debug(`Cached slip filename data for user ${userId}, filename: ${baseFilename}`);
  } catch (error) {
    logger.error('Error caching slip filename:', error);
  }
}

// ฟังก์ชันสำหรับดึงข้อมูลจาก Redis โดยใช้ชื่อไฟล์
export async function getCachedSlipByFilename(userId: number, filename: string): Promise<any | null> {
  try {
    // ดึงชื่อไฟล์ล้วนๆ ไม่รวมพาธ
    const baseFilename = path.basename(filename);
    const key = `filename:${userId}:${baseFilename}`;
    const cachedData = await redisClient.get(key);

    if (cachedData) {
      logger.debug(`Cache hit for slip filename: user ${userId}, filename: ${baseFilename}`);
      return JSON.parse(cachedData);
    }

    logger.debug(`Cache miss for slip filename: user ${userId}, filename: ${baseFilename}`);
    return null;
  } catch (error) {
    logger.error('Error getting cached slip by filename:', error);
    return null;
  }
}

// ฟังก์ชันสำหรับลบข้อมูลชื่อไฟล์จาก Redis
export async function removeCachedSlipFilename(userId: number, filename: string): Promise<boolean> {
  try {
    const baseFilename = path.basename(filename);
    const key = `filename:${userId}:${baseFilename}`;
    const result = await redisClient.del(key);

    if (result > 0) {
      logger.debug(`Removed cached slip filename for user ${userId}, filename: ${baseFilename}`);
      return true;
    }

    logger.debug(`No cached slip filename found to remove for user ${userId}, filename: ${baseFilename}`);
    return false;
  } catch (error) {
    logger.error('Error removing cached slip filename:', error);
    return false;
  }
}

// ฟังก์ชันสำหรับตรวจสอบว่าไฟล์นี้เคยถูกส่งมาแล้วหรือไม่
export async function isSlipFilenameExists(userId: number, filename: string): Promise<boolean> {
  try {
    const cachedData = await getCachedSlipByFilename(userId, filename);
    return cachedData !== null;
  } catch (error) {
    logger.error('Error checking if slip filename exists:', error);
    return false;
  }
}

export default redisClient;
