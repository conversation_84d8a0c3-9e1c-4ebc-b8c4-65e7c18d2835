import { db } from './db';
import { eq, and, gte, lte, sql } from 'drizzle-orm';
import { fraudRules, fraudDetections, slipVerifications, insertFraudDetectionSchema, notifications, insertNotificationSchema } from '@shared/schema';
import { emailService } from './email-service';

export class FraudDetectionService {
  // ตรวจสอบมูลค่าสูงผิดปกติ (Unusual Amount)
  public async checkUnusualAmount(slipVerificationId: number): Promise<boolean> {
    try {
      // ดึงข้อมูลการตรวจสอบสลิป
      const verification = await db.query.slipVerifications.findFirst({
        where: eq(slipVerifications.id, slipVerificationId),
        with: {
          user: true
        }
      });

      if (!verification) {
        console.error(`FraudDetectionService: ไม่พบข้อมูลการตรวจสอบสลิป ID ${slipVerificationId}`);
        return false;
      }

      // ดึงกฎตรวจจับ unusual_amount
      const rule = await db.query.fraudRules.findFirst({
        where: and(
          eq(fraudRules.ruleType, 'unusual_amount'),
          eq(fraudRules.isActive, true)
        )
      });

      if (!rule) {
        console.log('FraudDetectionService: ไม่พบกฎตรวจจับมูลค่าสูงผิดปกติที่เปิดใช้งาน');
        return false;
      }

      // ตรวจสอบมูลค่า
      const thresholdAmount = rule.conditions.thresholdAmount;
      if (verification.amount && verification.amount > thresholdAmount) {
        // สร้างบันทึกการตรวจจับ
        const detection = insertFraudDetectionSchema.parse({
          slipVerificationId,
          userId: verification.userId,
          ruleId: rule.id,
          status: 'detected',
          details: {
            amount: verification.amount,
            thresholdAmount,
            transactionRef: verification.transactionRef,
            transactionDate: verification.transactionDate
          }
        });

        await db.insert(fraudDetections).values(detection);

        // สร้างการแจ้งเตือน
        const notification = insertNotificationSchema.parse({
          userId: verification.userId,
          title: 'พบธุรกรรมที่มีมูลค่าสูงผิดปกติ',
          message: `ระบบตรวจพบธุรกรรมที่มีมูลค่าสูงผิดปกติ (฿${verification.amount?.toLocaleString('th-TH')}) กรุณาตรวจสอบความถูกต้อง`,
          type: 'unusual_transaction',
          priority: 'medium',
          channel: 'in_app'
        });

        await db.insert(notifications).values(notification);

        // ส่งอีเมลแจ้งเตือน
        if (verification.user && verification.user.email) {
          await emailService.sendUnusualTransactionAlert(verification.userId, {
            transactionRef: verification.transactionRef,
            transactionDate: verification.transactionDate,
            amount: verification.amount,
            sender: verification.sender,
            receiver: verification.receiver
          });
        }

        return true;
      }

      return false;
    } catch (error) {
      console.error('FraudDetectionService: เกิดข้อผิดพลาดในการตรวจสอบมูลค่าสูงผิดปกติ:', error);
      return false;
    }
  }

  // ตรวจสอบสลิปซ้ำ (Duplicate Slip)
  public async checkDuplicateSlip(slipVerificationId: number): Promise<boolean> {
    try {
      // ดึงข้อมูลการตรวจสอบสลิป
      const verification = await db.query.slipVerifications.findFirst({
        where: eq(slipVerifications.id, slipVerificationId),
        with: {
          user: true
        }
      });

      if (!verification) {
        console.error(`FraudDetectionService: ไม่พบข้อมูลการตรวจสอบสลิป ID ${slipVerificationId}`);
        return false;
      }

      // ถ้าไม่มี transactionRef ไม่สามารถตรวจสอบได้
      if (!verification.transactionRef) {
        return false;
      }

      // ดึงกฎตรวจจับ duplicate_slip
      const rule = await db.query.fraudRules.findFirst({
        where: and(
          eq(fraudRules.ruleType, 'duplicate_slip'),
          eq(fraudRules.isActive, true)
        )
      });

      if (!rule) {
        console.log('FraudDetectionService: ไม่พบกฎตรวจจับสลิปซ้ำที่เปิดใช้งาน');
        return false;
      }

      // ตรวจสอบสลิปซ้ำในช่วงเวลาที่กำหนด
      const timeWindow = rule.conditions.timeWindow || 86400; // 24 ชั่วโมงในหน่วยวินาที
      const timeThreshold = new Date();
      timeThreshold.setSeconds(timeThreshold.getSeconds() - timeWindow);

      // ค้นหาธุรกรรมที่มี transaction_ref เดียวกันในช่วงเวลาที่กำหนด
      const duplicateVerifications = await db.select().from(slipVerifications)
        .where(
          and(
            eq(slipVerifications.transactionRef, verification.transactionRef),
            gte(slipVerifications.transactionDate || slipVerifications.createdAt, timeThreshold),
            sql`${slipVerifications.id} != ${slipVerificationId}`
          )
        );

      if (duplicateVerifications.length > 0) {
        // สร้างบันทึกการตรวจจับ
        const detection = insertFraudDetectionSchema.parse({
          slipVerificationId,
          userId: verification.userId,
          ruleId: rule.id,
          status: 'detected',
          details: {
            transactionRef: verification.transactionRef,
            duplicateIds: duplicateVerifications.map(v => v.id),
            duplicateCount: duplicateVerifications.length,
            timeWindow
          }
        });

        await db.insert(fraudDetections).values(detection);

        // สร้างการแจ้งเตือน
        const notification = insertNotificationSchema.parse({
          userId: verification.userId,
          title: 'พบสลิปซ้ำ',
          message: `ระบบตรวจพบการใช้สลิปซ้ำ (ref: ${verification.transactionRef}) กรุณาตรวจสอบความถูกต้อง`,
          type: 'fraud_detection',
          priority: 'high',
          channel: 'in_app'
        });

        await db.insert(notifications).values(notification);

        return true;
      }

      return false;
    } catch (error) {
      console.error('FraudDetectionService: เกิดข้อผิดพลาดในการตรวจสอบสลิปซ้ำ:', error);
      return false;
    }
  }

  // ตรวจสอบการฉ้อโกงทั้งหมด
  public async checkAllFraudRules(slipVerificationId: number): Promise<{
    detected: boolean;
    detections: string[];
  }> {
    try {
      const unusualAmount = await this.checkUnusualAmount(slipVerificationId);
      const duplicateSlip = await this.checkDuplicateSlip(slipVerificationId);

      const detections = [];
      if (unusualAmount) detections.push('unusual_amount');
      if (duplicateSlip) detections.push('duplicate_slip');

      return {
        detected: unusualAmount || duplicateSlip,
        detections
      };
    } catch (error) {
      console.error('FraudDetectionService: เกิดข้อผิดพลาดในการตรวจสอบการฉ้อโกง:', error);
      return {
        detected: false,
        detections: []
      };
    }
  }

  // ดึงข้อมูลการตรวจจับการฉ้อโกงทั้งหมดของผู้ใช้
  public async getUserFraudDetections(userId: number, limit: number = 10): Promise<any[]> {
    try {
      const detections = await db.query.fraudDetections.findMany({
        where: eq(fraudDetections.userId, userId),
        with: {
          slipVerification: true,
          rule: true
        },
        orderBy: [sql`${fraudDetections.createdAt} desc`],
        limit
      });

      return detections;
    } catch (error) {
      console.error('FraudDetectionService: เกิดข้อผิดพลาดในการดึงข้อมูลการตรวจจับการฉ้อโกง:', error);
      return [];
    }
  }
}

export const fraudDetectionService = new FraudDetectionService();