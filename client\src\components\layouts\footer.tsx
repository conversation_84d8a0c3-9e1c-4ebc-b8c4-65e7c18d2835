import { Link } from "wouter";
import { 
  ReceiptTextIcon, 
  Facebook, 
  Twitter, 
  Linkedin, 
  Star, 
  Sun, 
  Crown,
  Sparkles,
  CloudLightning,
  Zap,
  MountainSnow
} from "lucide-react";
import { motion } from "framer-motion";
import React from "react";

interface LinkItem {
  href: string;
  label: string;
  icon: React.ReactNode;
}

interface DivineLinksProps {
  title: string;
  links: LinkItem[];
}

// ส่วนลิงก์ในธีมเทพเจ้า
const DivineLinks = ({ title, links }: DivineLinksProps) => {
  return (
    <div className="relative">
      <h4 className="text-white font-bold mb-5 flex items-center">
        <div className="h-8 w-8 rounded-full bg-gradient-to-r from-amber-400 to-yellow-300 flex items-center justify-center mr-2 shadow-glow">
          <Crown className="h-4 w-4 text-indigo-900" />
        </div>
        <span className="bg-clip-text text-transparent bg-gradient-to-r from-amber-300 to-yellow-200">{title}</span>
      </h4>
      <ul className="space-y-3">
        {links.map((link, index) => (
          <motion.li 
            key={index} 
            whileHover={{ x: 3 }}
            className="transition-all duration-300"
          >
            <Link 
              href={link.href} 
              className="text-white/80 hover:text-amber-300 transition-colors flex items-center group"
            >
              <motion.div 
                className="w-6 h-6 flex items-center justify-center mr-2 text-amber-400 group-hover:text-amber-300"
                whileHover={{ rotate: 15 }}
              >
                {link.icon}
              </motion.div>
              <span>{link.label}</span>
            </Link>
          </motion.li>
        ))}
      </ul>
    </div>
  );
};

export function Footer() {
  // ข้อมูลลิงก์
  const companyLinks: LinkItem[] = [
    { href: "#about", label: "เกี่ยวกับเรา", icon: <Sun className="h-4 w-4" /> },
    { href: "#careers", label: "ร่วมงานกับเรา", icon: <Star className="h-4 w-4" /> },
    { href: "#contact", label: "ติดต่อเรา", icon: <CloudLightning className="h-4 w-4" /> },
    { href: "#blog", label: "บล็อกและบทความ", icon: <Sparkles className="h-4 w-4" /> },
  ];
  
  const serviceLinks: LinkItem[] = [
    { href: "/packages", label: "แพ็กเกจและราคา", icon: <Crown className="h-4 w-4" /> },
    { href: "/docs", label: "เอกสารประกอบ API", icon: <Zap className="h-4 w-4" /> },
    { href: "#support", label: "ฝ่ายสนับสนุน", icon: <CloudLightning className="h-4 w-4" /> },
    { href: "#status", label: "สถานะบริการ", icon: <Star className="h-4 w-4" /> },
  ];
  
  const legalLinks: LinkItem[] = [
    { href: "#terms", label: "ข้อกำหนดการให้บริการ", icon: <Sun className="h-4 w-4" /> },
    { href: "#privacy", label: "นโยบายความเป็นส่วนตัว", icon: <Sparkles className="h-4 w-4" /> },
    { href: "#security", label: "ความปลอดภัย", icon: <CloudLightning className="h-4 w-4" /> },
    { href: "#compliance", label: "การปฏิบัติตามกฎหมาย", icon: <Crown className="h-4 w-4" /> },
  ];

  return (
    <footer className="relative text-white py-16 overflow-hidden border-t border-amber-400/20">
      {/* พื้นหลังแบบเทพเจ้า */}
      <div className="absolute inset-0 bg-gradient-to-b from-indigo-950 via-purple-900 to-indigo-950 opacity-95"></div>
      
      {/* ดาวระยิบระยับบนท้องฟ้า */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(50)].map((_, i) => (
          <div 
            key={i}
            className="absolute w-0.5 h-0.5 bg-white rounded-full animate-pulse"
            style={{
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              opacity: Math.random() * 0.7 + 0.3,
              animationDelay: `${Math.random() * 10}s`,
            }}
          ></div>
        ))}
      </div>
      
      {/* ภูเขาลอยเทพเจ้า */}
      <div className="absolute bottom-0 w-full overflow-hidden h-32 opacity-40 pointer-events-none">
        <div className="absolute -bottom-10 -left-10 w-40 h-36 bg-gradient-to-t from-indigo-900 to-indigo-800 rounded-t-[100%] opacity-60"></div>
        <div className="absolute -bottom-10 left-1/4 w-60 h-32 bg-gradient-to-t from-indigo-800 to-indigo-700 rounded-t-[100%] opacity-40"></div>
        <div className="absolute -bottom-10 right-1/4 w-48 h-28 bg-gradient-to-t from-indigo-800 to-indigo-700 rounded-t-[100%] opacity-30"></div>
        <div className="absolute -bottom-10 -right-10 w-40 h-36 bg-gradient-to-t from-indigo-900 to-indigo-800 rounded-t-[100%] opacity-60"></div>
        <div className="absolute -bottom-10 left-[40%] w-72 h-40 bg-gradient-to-t from-indigo-900 to-purple-900 rounded-t-[100%] opacity-50"></div>
        
        {/* วัดทองบนยอดเขา */}
        <div className="absolute bottom-20 left-[45%] w-4 h-8 bg-gradient-to-t from-amber-400 to-amber-300 rounded-full opacity-90 animate-pulse-slow"></div>
        <div className="absolute bottom-28 left-[25%] w-3 h-5 bg-gradient-to-t from-amber-400 to-amber-300 rounded-full opacity-70 animate-pulse-slow"></div>
        <div className="absolute bottom-22 right-[32%] w-3 h-6 bg-gradient-to-t from-amber-400 to-amber-300 rounded-full opacity-80 animate-pulse-slow"></div>
        
        {/* เมฆลอยเหนือภูเขา */}
        <div className="absolute bottom-28 left-1/3 transform -translate-x-1/2 w-16 h-6 bg-indigo-500/40 rounded-full blur-sm animate-float"></div>
        <div className="absolute bottom-32 right-1/3 transform translate-x-1/2 w-20 h-8 bg-indigo-500/30 rounded-full blur-sm animate-float" style={{ animationDelay: '2s' }}></div>
      </div>
      
      {/* แสงเทพเจ้าที่ส่องลงมา */}
      <div className="absolute inset-0 opacity-30 pointer-events-none">
        <div className="absolute top-0 left-1/4 w-40 h-60 bg-amber-300 rounded-full filter blur-[100px]"></div>
        <div className="absolute top-0 right-1/4 w-40 h-60 bg-amber-400 rounded-full filter blur-[100px]"></div>
        <div className="absolute bottom-0 left-1/3 w-60 h-40 bg-indigo-500 rounded-full filter blur-[120px]"></div>
        <div className="absolute bottom-10 right-1/3 w-60 h-40 bg-purple-500 rounded-full filter blur-[120px]"></div>
      </div>
      
      {/* เส้นสายฟ้าด้านบน */}
      <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-amber-400/0 via-amber-300/80 to-amber-400/0 shadow-lg shadow-amber-300/50"></div>
      
      {/* รัศมีทองคำด้านบน */}
      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-[500px] h-[3px] bg-gradient-to-r from-amber-400/0 via-amber-300/60 to-amber-400/0 blur-sm"></div>
      
      {/* เนื้อหาหลัก */}
      <div className="container relative z-10 mx-auto px-4 md:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-12 mb-12">
          <div className="col-span-1 md:col-span-1">
            <motion.div 
              className="flex items-center mb-6" 
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <div className="h-14 w-14 rounded-full bg-gradient-to-r from-amber-300 to-yellow-300 flex items-center justify-center mr-3 shadow-lg shadow-amber-500/30 relative overflow-hidden">
                {/* เอฟเฟคแสงเทพเจ้า */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/80 to-transparent opacity-70 mix-blend-overlay"></div>
                <ReceiptTextIcon className="h-7 w-7 text-indigo-900 relative z-10" />
                
                {/* รัศมีแสงรอบโลโก้ */}
                <div className="absolute -inset-1 bg-gradient-to-r from-amber-300 to-yellow-300 rounded-full opacity-40 blur-sm animate-pulse-slow"></div>
              </div>
              <div>
                <div className="text-3xl font-extrabold tracking-tight leading-none">
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-purple-500">SLIP</span>
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-amber-400 to-yellow-300">KUY</span>
                </div>
                <div className="text-xs text-amber-300 tracking-widest font-semibold">บริการของเทพเจ้า</div>
              </div>
            </motion.div>
            
            <p className="text-white/80 mb-6 bg-transparent p-4 rounded-xl border border-amber-400/20 shadow-amber-300/5 relative overflow-hidden">
              <span className="relative z-10">บริการตรวจสอบสลิปจากเทพเจ้า เพื่อให้คุณได้รับความปลอดภัยสูงสุด พร้อมเทคโนโลยีที่ล้ำสมัยไร้ขีดจำกัด</span>
              
              {/* กราฟิกพิเศษในกล่องข้อความ */}
              <span className="absolute top-0 right-0 opacity-20 -rotate-6">
                <Crown className="h-12 w-12 text-amber-300" />
              </span>
            </p>
            
            <div className="flex space-x-3">
              <motion.a 
                href="#" 
                whileHover={{ y: -3 }}
                className="bg-transparent p-2 rounded-full transition-colors border border-amber-400/30 hover:border-amber-300"
              >
                <Facebook className="h-5 w-5 text-amber-300 transition-colors" />
              </motion.a>
              <motion.a 
                href="#" 
                whileHover={{ y: -3 }}
                className="bg-transparent p-2 rounded-full transition-colors border border-amber-400/30 hover:border-amber-300"
              >
                <Twitter className="h-5 w-5 text-amber-300 transition-colors" />
              </motion.a>
              <motion.a 
                href="#" 
                whileHover={{ y: -3 }}
                className="bg-transparent p-2 rounded-full transition-colors border border-amber-400/30 hover:border-amber-300"
              >
                <Linkedin className="h-5 w-5 text-amber-300 transition-colors" />
              </motion.a>
            </div>
          </div>
          
          <div>
            <DivineLinks title="เกี่ยวกับเทพเจ้า" links={companyLinks} />
          </div>
          
          <div>
            <DivineLinks title="บริการแห่งสรวงสวรรค์" links={serviceLinks} />
          </div>
          
          <div>
            <DivineLinks title="กฎแห่งเทพ" links={legalLinks} />
          </div>
        </div>
        
        <div className="relative">
          {/* จุดตัดมงคล แบบเทพเจ้า */}
          <div className="absolute -top-20 left-1/2 transform -translate-x-1/2 w-full h-20 pointer-events-none">
            <div className="absolute top-0 left-1/2 -translate-x-1/2 w-1 h-16 bg-gradient-to-b from-amber-400/50 to-transparent blur-[2px]"></div>
            <div className="absolute top-14 left-1/2 -translate-x-1/2 w-5 h-5">
              <div className="absolute inset-0 bg-amber-400 rounded-full animate-pulse"></div>
              <div className="absolute inset-0 bg-gradient-to-r from-amber-500 to-amber-300 rounded-full mix-blend-overlay"></div>
              <div className="absolute inset-0 bg-gradient-radial from-white to-transparent opacity-70 rounded-full"></div>
            </div>
          </div>

          <div className="text-center text-white/70 text-sm relative mt-8">
            <div className="inline-flex items-center bg-transparent px-5 py-3 rounded-full border border-amber-400/20 bg-indigo-900/30 backdrop-blur-md">
              <Crown className="h-4 w-4 mr-2 text-amber-300" />
              <span>&copy; {new Date().getFullYear()} SlipKuy • </span>
              <span className="text-amber-300/80 ml-1">บริการแห่งเทพเจ้า</span>
            </div>
            
            <div className="mt-4 flex justify-center space-x-5">
              <motion.span 
                whileHover={{ scale: 1.1 }}
                className="text-indigo-300 text-xs hover:text-amber-300 transition-colors cursor-pointer"
              >
                นโยบายความเป็นส่วนตัว
              </motion.span>
              <motion.span 
                whileHover={{ scale: 1.1 }}
                className="text-indigo-300 text-xs hover:text-amber-300 transition-colors cursor-pointer"
              >
                เงื่อนไขการใช้บริการ
              </motion.span>
              <motion.span 
                whileHover={{ scale: 1.1 }}
                className="text-indigo-300 text-xs hover:text-amber-300 transition-colors cursor-pointer"
              >
                ติดต่อเรา
              </motion.span>
            </div>
            
            {/* แสงสว่างแห่งเทพเจ้าด้านล่าง */}
            <div className="absolute -bottom-10 left-1/2 -translate-x-1/2 w-80 h-1 bg-gradient-to-r from-amber-400/0 via-amber-300/30 to-amber-400/0 rounded-full blur-sm"></div>
          </div>
        </div>
      </div>
      
      {/* เราจะใช้ animate-pulse จาก Tailwind แทน */}
    </footer>
  );
}