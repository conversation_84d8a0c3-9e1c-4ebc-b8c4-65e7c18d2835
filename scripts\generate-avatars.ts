import fs from 'fs';
import path from 'path';
import sharp from 'sharp';

const main = async () => {
  const avatarNames = [
    'เทพเจ้าแห่งแสง',
    'เทพเจ้าแห่งความมืด',
    'เทพเจ้าแห่งสายฟ้า',
    'เทพเจ้าแห่งทะเล',
    'เทพเจ้าแห่งไฟ',
    'เทพเจ้าแห่งพายุ',
    'เทพเจ้าแห่งดาวเคราะห์',
    'เทพเจ้าแห่งโชคลาภ',
    'เทพเจ้าแห่งปัญญา',
    'เทพเจ้าแห่งเวลา'
  ];

  const defaultAvatarsDir = path.join(process.cwd(), 'public', 'uploads', 'default-avatars');
  
  // สร้างโฟลเดอร์ถ้ายังไม่มี
  if (!fs.existsSync(defaultAvatarsDir)) {
    fs.mkdirSync(defaultAvatarsDir, { recursive: true });
  }

  // หาไฟล์ต้นฉบับ
  const sourceFiles = fs.readdirSync(defaultAvatarsDir)
    .filter(file => file.startsWith('avatar-raw-') && (file.endsWith('.jpg') || file.endsWith('.png')));

  if (sourceFiles.length === 0) {
    console.error('ไม่พบไฟล์ต้นฉบับในโฟลเดอร์ default-avatars');
    return;
  }

  // สร้างอวตารเทพ 10 รูป
  for (let i = 1; i <= 10; i++) {
    const sourceIndex = (i - 1) % sourceFiles.length;
    const sourcePath = path.join(defaultAvatarsDir, sourceFiles[sourceIndex]);
    const outputPath = path.join(defaultAvatarsDir, `avatar-${i}.webp`);
    
    console.log(`กำลังสร้างอวตาร ${i}: ${avatarNames[i-1]}`);
    
    try {
      // ปรับแต่งภาพและบันทึกเป็น WebP
      await sharp(sourcePath)
        .resize(200, 200, { fit: 'cover', position: 'centre' })
        .webp({ quality: 80 })
        .toFile(outputPath);
      
      console.log(`สร้างอวตาร ${i} สำเร็จ: ${outputPath}`);
    } catch (error) {
      console.error(`เกิดข้อผิดพลาดในการสร้างอวตาร ${i}:`, error);
    }
  }

  console.log('สร้างอวตารเทพทั้งหมดเรียบร้อยแล้ว!');
};

main().catch(err => {
  console.error('เกิดข้อผิดพลาด:', err);
  process.exit(1);
});