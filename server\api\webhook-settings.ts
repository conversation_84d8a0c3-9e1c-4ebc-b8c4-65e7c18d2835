import { Router, Request, Response } from 'express';
import { webhookService } from '../webhook-service';
import { insertWebhookSchema } from '../../shared/schema';
import { z } from 'zod';

const router = Router();

/**
 * ตรวจสอบว่ามีการล็อกอินหรือไม่
 */
const isAuthenticated = (req: Request, res: Response, next: Function) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ 
      code: '401001', 
      message: 'กรุณาเข้าสู่ระบบก่อนใช้งาน' 
    });
  }
  next();
};

// สร้าง webhook ใหม่
router.post('/', isAuthenticated, async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    
    // ตรวจสอบข้อมูลที่ส่งมา
    const validationResult = insertWebhookSchema.safeParse({
      ...req.body,
      userId
    });
    
    if (!validationResult.success) {
      return res.status(400).json({
        code: '400001',
        message: 'ข้อมูลไม่ถูกต้อง',
        errors: validationResult.error.errors
      });
    }
    
    const webhook = await webhookService.createWebhook(validationResult.data);
    
    res.status(201).json({
      code: '201000',
      message: 'สร้าง webhook สำเร็จ',
      data: webhook
    });
  } catch (error) {
    console.error('Error creating webhook:', error);
    res.status(500).json({
      code: '500000',
      message: 'เกิดข้อผิดพลาดในการสร้าง webhook'
    });
  }
});

// แก้ไข webhook
router.put('/:id', isAuthenticated, async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const webhookId = parseInt(req.params.id);
    
    if (isNaN(webhookId)) {
      return res.status(400).json({
        code: '400001',
        message: 'รหัส webhook ไม่ถูกต้อง'
      });
    }
    
    // ตรวจสอบข้อมูลที่ส่งมา - ไม่ต้องตรวจสอบ userId เพราะเราจะบังคับใช้ userId จาก session
    const updateSchema = insertWebhookSchema.omit({ userId: true });
    const validationResult = updateSchema.safeParse(req.body);
    
    if (!validationResult.success) {
      return res.status(400).json({
        code: '400001',
        message: 'ข้อมูลไม่ถูกต้อง',
        errors: validationResult.error.errors
      });
    }
    
    const webhook = await webhookService.updateWebhook(webhookId, userId, validationResult.data);
    
    res.json({
      code: '200000',
      message: 'อัพเดท webhook สำเร็จ',
      data: webhook
    });
  } catch (error: any) {
    console.error('Error updating webhook:', error);
    
    if (error.status === 404) {
      return res.status(404).json({
        code: '404001',
        message: 'ไม่พบ webhook ที่ต้องการแก้ไข'
      });
    }
    
    res.status(500).json({
      code: '500000',
      message: 'เกิดข้อผิดพลาดในการอัพเดท webhook'
    });
  }
});

// ลบ webhook
router.delete('/:id', isAuthenticated, async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const webhookId = parseInt(req.params.id);
    
    if (isNaN(webhookId)) {
      return res.status(400).json({
        code: '400001',
        message: 'รหัส webhook ไม่ถูกต้อง'
      });
    }
    
    await webhookService.deleteWebhook(webhookId, userId);
    
    res.json({
      code: '200000',
      message: 'ลบ webhook สำเร็จ'
    });
  } catch (error: any) {
    console.error('Error deleting webhook:', error);
    
    if (error.status === 404) {
      return res.status(404).json({
        code: '404001',
        message: 'ไม่พบ webhook ที่ต้องการลบ'
      });
    }
    
    res.status(500).json({
      code: '500000',
      message: 'เกิดข้อผิดพลาดในการลบ webhook'
    });
  }
});

// ดึงข้อมูล webhook ทั้งหมดของผู้ใช้
router.get('/', isAuthenticated, async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const webhooks = await webhookService.getUserWebhooks(userId);
    
    res.json({
      code: '200000',
      message: 'ดึงข้อมูล webhooks สำเร็จ',
      data: webhooks
    });
  } catch (error) {
    console.error('Error fetching webhooks:', error);
    res.status(500).json({
      code: '500000',
      message: 'เกิดข้อผิดพลาดในการดึงข้อมูล webhooks'
    });
  }
});

// ดึงข้อมูล webhook ตาม id
router.get('/:id', isAuthenticated, async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const webhookId = parseInt(req.params.id);
    
    if (isNaN(webhookId)) {
      return res.status(400).json({
        code: '400001',
        message: 'รหัส webhook ไม่ถูกต้อง'
      });
    }
    
    const webhook = await webhookService.getWebhook(webhookId, userId);
    
    res.json({
      code: '200000',
      message: 'ดึงข้อมูล webhook สำเร็จ',
      data: webhook
    });
  } catch (error: any) {
    console.error('Error fetching webhook:', error);
    
    if (error.status === 404) {
      return res.status(404).json({
        code: '404001',
        message: 'ไม่พบ webhook ที่ต้องการ'
      });
    }
    
    res.status(500).json({
      code: '500000',
      message: 'เกิดข้อผิดพลาดในการดึงข้อมูล webhook'
    });
  }
});

// ดึงประวัติการส่ง webhook
router.get('/:id/logs', isAuthenticated, async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const webhookId = parseInt(req.params.id);
    
    if (isNaN(webhookId)) {
      return res.status(400).json({
        code: '400001',
        message: 'รหัส webhook ไม่ถูกต้อง'
      });
    }
    
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
    
    const logs = await webhookService.getWebhookLogs(webhookId, userId, limit);
    
    res.json({
      code: '200000',
      message: 'ดึงประวัติการส่ง webhook สำเร็จ',
      data: logs
    });
  } catch (error: any) {
    console.error('Error fetching webhook logs:', error);
    
    if (error.status === 404) {
      return res.status(404).json({
        code: '404001',
        message: 'ไม่พบ webhook ที่ต้องการดูประวัติ'
      });
    }
    
    res.status(500).json({
      code: '500000',
      message: 'เกิดข้อผิดพลาดในการดึงประวัติการส่ง webhook'
    });
  }
});

// ทดสอบส่ง webhook
router.post('/:id/test', isAuthenticated, async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const webhookId = parseInt(req.params.id);
    
    if (isNaN(webhookId)) {
      return res.status(400).json({
        code: '400001',
        message: 'รหัส webhook ไม่ถูกต้อง'
      });
    }
    
    // ดึงข้อมูล webhook ก่อน
    const webhook = await webhookService.getWebhook(webhookId, userId);
    
    // ทดสอบส่ง webhook
    const result = await webhookService.testWebhook(webhook);
    
    if (result.success) {
      return res.json({
        code: '200000',
        message: 'ทดสอบส่ง webhook สำเร็จ',
        data: result.response
      });
    } else {
      return res.status(400).json({
        code: '400002',
        message: 'ทดสอบส่ง webhook ไม่สำเร็จ',
        error: result.error
      });
    }
  } catch (error: any) {
    console.error('Error testing webhook:', error);
    
    if (error.status === 404) {
      return res.status(404).json({
        code: '404001',
        message: 'ไม่พบ webhook ที่ต้องการทดสอบ'
      });
    }
    
    res.status(500).json({
      code: '500000',
      message: 'เกิดข้อผิดพลาดในการทดสอบส่ง webhook'
    });
  }
});

// ลองส่ง webhook ที่ล้มเหลวใหม่
router.post('/logs/:id/retry', isAuthenticated, async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const logId = parseInt(req.params.id);
    
    if (isNaN(logId)) {
      return res.status(400).json({
        code: '400001',
        message: 'รหัสประวัติการส่ง webhook ไม่ถูกต้อง'
      });
    }
    
    const result = await webhookService.retryFailedWebhook(logId, userId);
    
    if (result.success) {
      return res.json({
        code: '200000',
        message: 'ลองส่ง webhook ใหม่สำเร็จ'
      });
    } else {
      return res.status(400).json({
        code: '400002',
        message: 'ลองส่ง webhook ใหม่ไม่สำเร็จ',
        error: result.error
      });
    }
  } catch (error: any) {
    console.error('Error retrying webhook:', error);
    
    if (error.status === 404) {
      return res.status(404).json({
        code: '404001',
        message: 'ไม่พบประวัติการส่ง webhook ที่ต้องการลองใหม่'
      });
    } else if (error.status === 403) {
      return res.status(403).json({
        code: '403001',
        message: 'ไม่มีสิทธิ์ในการลองส่ง webhook นี้ใหม่'
      });
    } else if (error.status === 400) {
      return res.status(400).json({
        code: '400003',
        message: error.message
      });
    }
    
    res.status(500).json({
      code: '500000',
      message: 'เกิดข้อผิดพลาดในการลองส่ง webhook ใหม่'
    });
  }
});

export default router;