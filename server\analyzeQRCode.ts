/**
 * ไฟล์นี้ใช้สำหรับการตรวจสอบสภาพของรูปภาพสลิปและ QR Code
 * ด้วยเทคนิคการตรวจสอบความถี่ของสีและรูปแบบ
 */
import sharp from 'sharp';
import { promisify } from 'util';
import { createHash } from 'crypto';

/**
 * ตรวจสอบความถูกต้องของรูปภาพสลิปธนาคาร
 * 
 * @param imageBuffer Buffer ของรูปภาพที่ต้องการตรวจสอบ
 * @returns Promise<{ isValid: boolean; reason?: string }> ผลการตรวจสอบ
 */
export async function analyzeBankSlip(imageBuffer: Buffer): Promise<{ isValid: boolean; reason?: string }> {
  try {
    // ตรวจสอบไฟล์รูปภาพ
    const isJPEG = imageBuffer.slice(0, 3).toString('hex') === 'ffd8ff';
    const isPNG = imageBuffer.slice(0, 8).toString('hex') === '89504e470d0a1a0a';
    
    if (!isJPEG && !isPNG) {
      return { isValid: false, reason: 'ไฟล์ไม่ใช่รูปภาพที่รองรับ (JPEG/PNG)' };
    }
    
    // ตรวจสอบขนาดไฟล์
    if (imageBuffer.length < 5000 || imageBuffer.length > 5 * 1024 * 1024) {
      return { isValid: false, reason: 'ขนาดไฟล์ไม่เหมาะสมสำหรับสลิปธนาคาร' };
    }
    
    // คำนวณแฮชของไฟล์เพื่อตรวจสอบกับรายการไฟล์ที่รู้ว่าไม่ใช่สลิป
    const hash = createHash('md5').update(imageBuffer).digest('hex');
    
    // รายการแฮชของไฟล์ที่ทราบว่าไม่ใช่สลิป
    const knownNonSlipHashes = [
      // รายการแฮชของไฟล์ที่ทราบว่าไม่ใช่สลิป
      'a1b2c3d4e5f6g7h8i9j0', // ตัวอย่างแฮช
    ];
    
    if (knownNonSlipHashes.includes(hash)) {
      return { isValid: false, reason: 'ตรวจพบว่าเป็นรูปภาพที่ไม่ใช่สลิป' };
    }
    
    // ตรวจสอบเนื้อหาของรูปภาพ
    const metadata = await sharp(imageBuffer).metadata();
    
    // ตรวจสอบขนาดภาพ
    if (!metadata.width || !metadata.height) {
      return { isValid: false, reason: 'ไม่สามารถระบุขนาดของรูปภาพได้' };
    }
    
    // สลิปธนาคารควรมีขนาดที่เหมาะสม
    if (metadata.width < 300 || metadata.height < 300) {
      return { isValid: false, reason: 'ความละเอียดของรูปภาพต่ำเกินไป' };
    }
    
    // ตรวจสอบอัตราส่วนของภาพ
    const aspectRatio = metadata.width / metadata.height;
    
    // สลิปธนาคารมักมีอัตราส่วนภาพที่ใกล้เคียงกับอัตราส่วนมาตรฐานของโทรศัพท์มือถือ
    // แต่เราจะยอมรับอัตราส่วนที่หลากหลายมากขึ้น
    if (aspectRatio < 0.2 || aspectRatio > 5.0) {
      console.log('อัตราส่วนของรูปภาพไม่ปกติ แต่ยังยอมรับได้: ' + aspectRatio);
      // ไม่ตัดสินว่าใช้ไม่ได้ แต่บันทึกข้อมูลไว้
    }
    
    // ตรวจสอบสีหลักในรูปภาพ
    try {
      // ย่อขนาดรูปภาพเพื่อประมวลผลเร็วขึ้น
      const resizedImageBuffer = await sharp(imageBuffer)
        .resize(300, 300, { fit: 'inside' })
        .toBuffer();
      
      // นับจำนวนพิกเซลสีขาว (พื้นหลังของสลิปมักเป็นสีขาว)
      const { dominant, channels } = await sharp(resizedImageBuffer).stats();
      
      // ตรวจสอบว่ามีพื้นที่สีขาวมากเพียงพอหรือไม่
      // สลิปธนาคารมักมีพื้นหลังสีขาวเป็นส่วนใหญ่
      // แต่เราจะลดความเข้มงวดลงเพื่อรองรับภาพที่มีความสว่างต่างกัน
      if (channels[0].mean < 100 && channels[1].mean < 100 && channels[2].mean < 100) {
        // ถ้าค่าเฉลี่ยของทุกช่องสีต่ำกว่า 100 แสดงว่าภาพมืดมาก
        console.log('ภาพมืดมาก แต่ยังยอมรับได้: R=' + channels[0].mean + ', G=' + channels[1].mean + ', B=' + channels[2].mean);
        // ไม่ตัดสินว่าใช้ไม่ได้ แต่บันทึกข้อมูลไว้
      }
      
      // ตรวจสอบค่าเบี่ยงเบนมาตรฐานของสี
      // สลิปธนาคารมักมีความแตกต่างของสีที่ชัดเจนระหว่างข้อความและพื้นหลัง
      // แต่เราจะลดความเข้มงวดลงเพื่อรองรับภาพที่มีคอนทราสต์ต่างกัน
      if (channels[0].stdev < 10 && channels[1].stdev < 10 && channels[2].stdev < 10) {
        // ถ้าค่าเบี่ยงเบนมาตรฐานของทุกช่องสีต่ำมาก แสดงว่าภาพแทบไม่มีความแตกต่างของสีเลย
        console.log('ภาพมีความหลากหลายของสีน้อยมาก: R=' + channels[0].stdev + ', G=' + channels[1].stdev + ', B=' + channels[2].stdev);
        // ยังให้ผ่านได้ แต่บันทึกข้อมูลไว้
      }
    } catch (error) {
      console.error('ข้อผิดพลาดในการวิเคราะห์สี:', error);
    }
    
    // หากผ่านการตรวจสอบทั้งหมด
    return { isValid: true };
    
  } catch (error) {
    console.error('ข้อผิดพลาดในการวิเคราะห์รูปภาพ:', error);
    return { isValid: false, reason: 'เกิดข้อผิดพลาดในการวิเคราะห์รูปภาพ' };
  }
}

/**
 * ตรวจสอบว่าไฟล์รูปภาพมีลักษณะคล้ายกับรูปภาพ QR Code หรือไม่
 * 
 * @param imageBuffer Buffer ของรูปภาพที่ต้องการตรวจสอบ
 * @returns Promise<{ hasQRCode: boolean; confidence: number }> ผลการตรวจสอบและความมั่นใจ
 */
export async function detectQRPattern(imageBuffer: Buffer): Promise<{ hasQRCode: boolean; confidence: number }> {
  try {
    // ตรวจสอบความถูกต้องของรูปภาพก่อน
    const validationResult = await analyzeBankSlip(imageBuffer);
    if (!validationResult.isValid) {
      return { hasQRCode: false, confidence: 0 };
    }
    
    // ย่อขนาดรูปภาพและแปลงเป็นขาวดำ
    const bwImage = await sharp(imageBuffer)
      .resize(300, 300, { fit: 'inside' })
      .greyscale()
      .toBuffer();
    
    // คุณสมบัติที่บ่งชี้ว่ามี QR Code
    let confidence = 0;
    
    // 1. ตรวจสอบรูปแบบขาวดำที่เป็นลักษณะเฉพาะของ QR Code
    // แปลงเป็นขาวดำแบบเข้ม (threshold) เพื่อให้เห็นรูปแบบชัดเจนขึ้น
    const binaryImage = await sharp(bwImage)
      .threshold(128)
      .toBuffer();
    
    // 2. ตรวจสอบสัดส่วนของสีขาวและดำ
    // QR Code มักมีสัดส่วนของพื้นที่สีขาวและดำที่ใกล้เคียงกัน
    const { channels } = await sharp(binaryImage).stats();
    
    // ค่าเฉลี่ยของช่องสีเทาควรอยู่ประมาณ 128 ถ้าเป็น QR Code
    // (QR Code มีสัดส่วนของสีดำและขาวใกล้เคียงกัน)
    const meanGrey = channels[0].mean;
    
    if (meanGrey > 100 && meanGrey < 200) {
      confidence += 30;
    }
    
    // 3. ตรวจสอบค่าเบี่ยงเบนมาตรฐานของสี
    // QR Code มักมีค่าเบี่ยงเบนมาตรฐานสูงเนื่องจากมีการสลับระหว่างสีขาวและดำบ่อย
    const stdevGrey = channels[0].stdev;
    
    if (stdevGrey > 80) {
      confidence += 40;
    }
    
    // 4. ตรวจสอบการมีรูปแบบการวางตัวที่เป็นระเบียบ (เช่น จุดมุม 3 จุดของ QR Code)
    // ทำได้ยากโดยไม่ใช้การประมวลผลภาพขั้นสูง แต่เราสามารถตรวจสอบรูปแบบเบื้องต้นได้
    
    // 5. ตรวจสอบจากผลการวิเคราะห์สลิปธนาคาร (เราสันนิษฐานว่าสลิปธนาคารมักมี QR Code)
    if (validationResult.isValid) {
      confidence += 30;
    }
    
    // สรุปผลการตรวจสอบ - ต้องมีความมั่นใจสูงมาก (80+ จาก 100)
    return {
      hasQRCode: confidence >= 80,
      confidence
    };
    
  } catch (error) {
    console.error('ข้อผิดพลาดในการตรวจสอบรูปแบบ QR Code:', error);
    return { hasQRCode: false, confidence: 0 };
  }
}