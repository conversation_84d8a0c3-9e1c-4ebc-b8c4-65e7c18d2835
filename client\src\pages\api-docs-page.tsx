import { useState, useEffect } from "react";
import { DashboardLayout } from "@/components/layouts/dashboard-layout";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Code, FileText, Terminal, Book, Copy, Check, Info, AlertTriangle, 
  ChevronRight, List, HelpCircle, Globe, Zap, Shield, Database
} from "lucide-react";
import { motion } from "framer-motion";
import { useQuery } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";

// กำหนดประเภทข้อมูลของเอกสาร API
interface ApiDocsType {
  title: string;
  version: string;
  description: string;
  baseUrl: string;
  security: Array<any>;
  endpoints: Array<{
    path: string;
    method: string;
    description: string;
    headers?: Array<{
      name: string;
      required: boolean;
      description: string;
    }>;
    requestBody?: {
      contentType: string;
      parameters: Array<{
        name: string;
        type: string;
        required: boolean;
        description: string;
      }>;
    };
    responses: {
      [key: string]: {
        description: string;
        example?: any;
        examples?: Array<any>;
      };
    };
  }>;
  testEndpoint: {
    path: string;
    method: string;
    description: string;
    headers: Array<any>;
    requestBody: {
      contentType: string;
      parameters: Array<any>;
    };
  };
  errorCodes: {
    [key: string]: string;
  };
  examples: {
    curlExample: string;
    javascriptExample: string;
    pythonExample: string;
    phpExample: string;
    javaExample: string;
  };
  guidelines: {
    title: string;
    content: Array<{
      title: string;
      description: string;
    }>;
  };
}

// กำหนดประเภทข้อมูลของคู่มือการใช้งาน
interface UsageGuideType {
  title: string;
  overview: string;
  apiKeyInstructions: string;
  sections: Array<{
    title: string;
    description?: string;
    steps?: Array<string>;
    tips?: Array<string>;
    responseStructure?: any;
    commonErrors?: Array<{
      code: string;
      message: string;
      solution: string;
    }>;
  }>;
}

export default function ApiDocsPage() {
  const { toast } = useToast();
  const [copyStatus, setCopyStatus] = useState<{ [key: string]: boolean }>({});
  const [activeExample, setActiveExample] = useState<string>("curl");
  
  // ดึงข้อมูลเอกสาร API
  const { data: apiDocs, isLoading: isLoadingDocs } = useQuery<ApiDocsType>({
    queryKey: ['/api/docs'],
  });
  
  // ดึงข้อมูลคู่มือการใช้งาน
  const { data: usageGuide, isLoading: isLoadingUsage } = useQuery<UsageGuideType>({
    queryKey: ['/api/docs/usage'],
  });
  
  // ฟังก์ชันคัดลอกข้อความไปยังคลิปบอร์ด
  const copyToClipboard = (text: string, id: string) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopyStatus({ ...copyStatus, [id]: true });
      setTimeout(() => {
        setCopyStatus({ ...copyStatus, [id]: false });
      }, 2000);
      
      toast({
        title: "คัดลอกสำเร็จ",
        description: "คัดลอกโค้ดไปยังคลิปบอร์ดแล้ว",
      });
    }).catch(err => {
      toast({
        title: "คัดลอกไม่สำเร็จ",
        description: "ไม่สามารถคัดลอกโค้ดได้",
        variant: "destructive",
      });
    });
  };
  
  // ฟังก์ชันจัดรูปแบบ JSON
  const formatJSON = (json: any) => {
    return JSON.stringify(json, null, 2);
  };
  
  // แสดงตัวโหลดในระหว่างโหลดข้อมูล
  if (isLoadingDocs || isLoadingUsage) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500 mb-2"></div>
            <p className="text-indigo-300">กำลังโหลดเอกสาร API...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }
  
  // แสดงข้อผิดพลาดถ้าไม่สามารถโหลดข้อมูลได้
  if (!apiDocs || !usageGuide) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-96">
          <div className="text-center text-red-400">
            <AlertTriangle className="h-10 w-10 mx-auto mb-2" />
            <p>เกิดข้อผิดพลาดในการโหลดเอกสาร API</p>
            <p className="text-sm mt-2">โปรดลองโหลดหน้านี้ใหม่อีกครั้ง</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }
  
  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* ส่วนหัวหน้าเอกสาร */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex items-center mb-6">
            <div className="mr-4 relative">
              <div className="absolute -inset-1 rounded-full bg-blue-600/30 animate-pulse blur-md"></div>
              <div className="relative bg-gradient-to-br from-blue-700 to-indigo-900 h-12 w-12 rounded-full flex items-center justify-center">
                <FileText className="h-6 w-6 text-blue-200" />
              </div>
            </div>
            <div>
              <h1 className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-300 to-white">
                {apiDocs.title}
              </h1>
              <div className="flex items-center mt-1">
                <Badge variant="outline" className="text-indigo-300 border-indigo-700 mr-2">
                  v{apiDocs.version}
                </Badge>
                <p className="text-indigo-300 text-sm">{apiDocs.description}</p>
              </div>
            </div>
          </div>
        </motion.div>
        
        {/* แท็บหลักสำหรับแยกเนื้อหา */}
        <Tabs defaultValue="overview" className="mt-6">
          <TabsList className="mb-6 bg-black/20 border border-indigo-800/30 rounded-lg p-1">
            <TabsTrigger
              value="overview"
              className="data-[state=active]:bg-indigo-800/40 data-[state=active]:text-indigo-100"
            >
              <Info className="h-4 w-4 mr-2" />
              ภาพรวม
            </TabsTrigger>
            <TabsTrigger
              value="endpoints"
              className="data-[state=active]:bg-indigo-800/40 data-[state=active]:text-indigo-100"
            >
              <Terminal className="h-4 w-4 mr-2" />
              API Endpoints
            </TabsTrigger>
            <TabsTrigger
              value="examples"
              className="data-[state=active]:bg-indigo-800/40 data-[state=active]:text-indigo-100"
            >
              <Code className="h-4 w-4 mr-2" />
              ตัวอย่างโค้ด
            </TabsTrigger>
            <TabsTrigger
              value="guide"
              className="data-[state=active]:bg-indigo-800/40 data-[state=active]:text-indigo-100"
            >
              <Book className="h-4 w-4 mr-2" />
              คู่มือการใช้งาน
            </TabsTrigger>
          </TabsList>
          
          {/* แท็บภาพรวม */}
          <TabsContent value="overview" className="space-y-6">
            <Card className="bg-gradient-to-br from-indigo-950/70 to-purple-950/60 border-indigo-800/50">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Globe className="h-5 w-5 mr-2 text-indigo-300" />
                  ภาพรวมของ API
                </CardTitle>
                <CardDescription className="text-indigo-300">
                  API นี้ช่วยให้คุณสามารถตรวจสอบและอ่านข้อมูลจากรูปภาพสลิปธนาคาร
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6 text-indigo-200">
                {/* ส่วนความปลอดภัย */}
                <div>
                  <h3 className="text-lg font-medium text-white mb-3 flex items-center">
                    <Shield className="h-5 w-5 mr-2 text-indigo-300" />
                    การรับรองความปลอดภัย
                  </h3>
                  <div className="bg-indigo-950/60 border border-indigo-800/40 rounded-lg p-4">
                    <p className="text-indigo-300 mb-3">
                      API นี้ต้องการการรับรองความปลอดภัยด้วย API Key ในทุกการเรียกใช้งาน:
                    </p>
                    <div className="bg-black/30 border border-indigo-800/40 rounded-md p-3 font-mono text-sm">
                      <div className="flex justify-between items-center">
                        <code className="text-amber-400">X-API-Key: your_api_key_here</code>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-7 px-2 text-indigo-400 hover:text-amber-400 hover:bg-transparent"
                          onClick={() => copyToClipboard('X-API-Key: your_api_key_here', 'apikey')}
                        >
                          {copyStatus['apikey'] ? <Check className="h-3.5 w-3.5" /> : <Copy className="h-3.5 w-3.5" />}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* ส่วนคำแนะนำการใช้งาน */}
                <div>
                  <h3 className="text-lg font-medium text-white mb-3 flex items-center">
                    <HelpCircle className="h-5 w-5 mr-2 text-indigo-300" />
                    คำแนะนำการใช้งาน
                  </h3>
                  <div className="bg-indigo-950/60 border border-indigo-800/40 rounded-lg p-4 space-y-4">
                    {apiDocs.guidelines.content.map((guideline, index) => (
                      <div key={index} className="space-y-1">
                        <h4 className="text-white font-medium">{guideline.title}</h4>
                        <p className="text-indigo-300 text-sm">{guideline.description}</p>
                      </div>
                    ))}
                  </div>
                </div>
                
                {/* ส่วนรหัสข้อผิดพลาด */}
                <div>
                  <h3 className="text-lg font-medium text-white mb-3 flex items-center">
                    <AlertTriangle className="h-5 w-5 mr-2 text-indigo-300" />
                    รหัสข้อผิดพลาด
                  </h3>
                  <div className="bg-indigo-950/60 border border-indigo-800/40 rounded-lg overflow-hidden">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b border-indigo-800/40 bg-black/20">
                          <th className="py-2 px-4 text-left text-indigo-300 font-medium">รหัส</th>
                          <th className="py-2 px-4 text-left text-indigo-300 font-medium">ความหมาย</th>
                        </tr>
                      </thead>
                      <tbody>
                        {Object.entries(apiDocs.errorCodes).map(([code, description], index) => (
                          <tr 
                            key={index}
                            className={index % 2 === 0 ? "bg-black/10" : "bg-black/5"}
                          >
                            <td className="py-2 px-4 font-mono">
                              <span className={`
                                ${code.startsWith('2') ? 'text-green-400' : 
                                  code.startsWith('4') ? 'text-amber-400' : 
                                  'text-red-400'}
                              `}>
                                {code}
                              </span>
                            </td>
                            <td className="py-2 px-4 text-indigo-300">{description}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          {/* แท็บ API Endpoints */}
          <TabsContent value="endpoints" className="space-y-6">
            <Card className="bg-gradient-to-br from-indigo-950/70 to-purple-950/60 border-indigo-800/50">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Terminal className="h-5 w-5 mr-2 text-indigo-300" />
                  API Endpoints
                </CardTitle>
                <CardDescription className="text-indigo-300">
                  รายละเอียดของ endpoints ทั้งหมดที่มีให้บริการ
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6 text-indigo-200">
                <Accordion type="single" collapsible className="w-full">
                  {apiDocs.endpoints.map((endpoint, index) => (
                    <AccordionItem 
                      key={index} 
                      value={`endpoint-${index}`}
                      className="border-indigo-800/30 overflow-hidden"
                    >
                      <AccordionTrigger className="py-4 px-4 hover:bg-indigo-900/30 rounded-md transition-all hover:text-amber-400 group">
                        <div className="flex items-center gap-3 text-left">
                          <div className={`
                            px-2 py-1 rounded text-xs font-bold uppercase
                            ${endpoint.method === 'GET' ? 'bg-green-950/70 text-green-400 border border-green-800/40' : 
                              endpoint.method === 'POST' ? 'bg-blue-950/70 text-blue-400 border border-blue-800/40' : 
                              'bg-yellow-950/70 text-yellow-400 border border-yellow-800/40'}
                          `}>
                            {endpoint.method}
                          </div>
                          <span className="font-mono text-sm group-hover:text-amber-400 transition-colors">
                            {apiDocs.baseUrl}{endpoint.path}
                          </span>
                        </div>
                      </AccordionTrigger>
                      <AccordionContent className="bg-indigo-950/40 border-t border-indigo-800/40 pt-4 pb-2 px-4">
                        <div className="space-y-4">
                          <div>
                            <h4 className="text-white font-medium mb-2">คำอธิบาย</h4>
                            <p className="text-indigo-300">{endpoint.description}</p>
                          </div>
                          
                          {endpoint.headers && endpoint.headers.length > 0 && (
                            <div>
                              <h4 className="text-white font-medium mb-2">ส่วนหัว (Headers)</h4>
                              <div className="bg-indigo-950/50 border border-indigo-800/40 rounded-md overflow-hidden">
                                <table className="w-full text-sm">
                                  <thead>
                                    <tr className="border-b border-indigo-800/40 bg-black/20">
                                      <th className="py-2 px-3 text-left text-indigo-300 font-medium">ชื่อ</th>
                                      <th className="py-2 px-3 text-left text-indigo-300 font-medium">จำเป็น</th>
                                      <th className="py-2 px-3 text-left text-indigo-300 font-medium">คำอธิบาย</th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    {endpoint.headers.map((header, i) => (
                                      <tr key={i} className={i % 2 === 0 ? "bg-black/10" : "bg-black/5"}>
                                        <td className="py-2 px-3 font-mono text-blue-400">{header.name}</td>
                                        <td className="py-2 px-3">
                                          {header.required ? 
                                            <span className="text-amber-400">✓</span> : 
                                            <span className="text-indigo-500">✗</span>
                                          }
                                        </td>
                                        <td className="py-2 px-3 text-indigo-300">{header.description}</td>
                                      </tr>
                                    ))}
                                  </tbody>
                                </table>
                              </div>
                            </div>
                          )}
                          
                          {endpoint.requestBody && (
                            <div>
                              <h4 className="text-white font-medium mb-2">ข้อมูลคำขอ ({endpoint.requestBody.contentType})</h4>
                              <div className="bg-indigo-950/50 border border-indigo-800/40 rounded-md overflow-hidden">
                                <table className="w-full text-sm">
                                  <thead>
                                    <tr className="border-b border-indigo-800/40 bg-black/20">
                                      <th className="py-2 px-3 text-left text-indigo-300 font-medium">ชื่อพารามิเตอร์</th>
                                      <th className="py-2 px-3 text-left text-indigo-300 font-medium">ประเภท</th>
                                      <th className="py-2 px-3 text-left text-indigo-300 font-medium">จำเป็น</th>
                                      <th className="py-2 px-3 text-left text-indigo-300 font-medium">คำอธิบาย</th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    {endpoint.requestBody.parameters.map((param, i) => (
                                      <tr key={i} className={i % 2 === 0 ? "bg-black/10" : "bg-black/5"}>
                                        <td className="py-2 px-3 font-mono text-purple-400">{param.name}</td>
                                        <td className="py-2 px-3 text-indigo-300">{param.type}</td>
                                        <td className="py-2 px-3">
                                          {param.required ? 
                                            <span className="text-amber-400">✓</span> : 
                                            <span className="text-indigo-500">✗</span>
                                          }
                                        </td>
                                        <td className="py-2 px-3 text-indigo-300">{param.description}</td>
                                      </tr>
                                    ))}
                                  </tbody>
                                </table>
                              </div>
                            </div>
                          )}
                          
                          <div>
                            <h4 className="text-white font-medium mb-2">การตอบกลับ</h4>
                            <Accordion type="single" collapsible className="w-full">
                              {Object.entries(endpoint.responses).map(([code, response], i) => (
                                <AccordionItem 
                                  key={i} 
                                  value={`response-${endpoint.path}-${code}`}
                                  className="border-indigo-800/30 overflow-hidden mt-2"
                                >
                                  <AccordionTrigger className="py-2 px-3 hover:bg-indigo-900/30 rounded-md transition-all hover:text-amber-400 group">
                                    <div className="flex items-center gap-3 text-left">
                                      <div className={`
                                        px-2 py-0.5 rounded text-xs font-bold
                                        ${code.startsWith('2') ? 'bg-green-950/70 text-green-400 border border-green-800/40' : 
                                          code.startsWith('4') ? 'bg-amber-950/70 text-amber-400 border border-amber-800/40' : 
                                          'bg-red-950/70 text-red-400 border border-red-800/40'}
                                      `}>
                                        {code}
                                      </div>
                                      <span className="text-sm group-hover:text-amber-400 transition-colors">
                                        {response.description}
                                      </span>
                                    </div>
                                  </AccordionTrigger>
                                  <AccordionContent className="bg-indigo-950/30 border-t border-indigo-800/30 pt-3 pb-2 px-3">
                                    {response.example && (
                                      <div className="relative">
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          className="absolute right-2 top-2 h-7 text-indigo-400 hover:text-amber-400 hover:bg-transparent"
                                          onClick={() => copyToClipboard(formatJSON(response.example), `response-${endpoint.path}-${code}`)}
                                        >
                                          {copyStatus[`response-${endpoint.path}-${code}`] ? 
                                            <Check className="h-3.5 w-3.5" /> : 
                                            <Copy className="h-3.5 w-3.5" />
                                          }
                                        </Button>
                                        <pre className="bg-black/30 border border-indigo-800/40 rounded-md p-3 font-mono text-xs text-indigo-300 overflow-x-auto">
                                          {formatJSON(response.example)}
                                        </pre>
                                      </div>
                                    )}
                                    
                                    {response.examples && (
                                      <div className="space-y-2">
                                        {response.examples.map((ex, j) => (
                                          <div key={j} className="relative">
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              className="absolute right-2 top-2 h-7 text-indigo-400 hover:text-amber-400 hover:bg-transparent"
                                              onClick={() => copyToClipboard(formatJSON(ex), `response-${endpoint.path}-${code}-${j}`)}
                                            >
                                              {copyStatus[`response-${endpoint.path}-${code}-${j}`] ? 
                                                <Check className="h-3.5 w-3.5" /> : 
                                                <Copy className="h-3.5 w-3.5" />
                                              }
                                            </Button>
                                            <pre className="bg-black/30 border border-indigo-800/40 rounded-md p-3 font-mono text-xs text-indigo-300 overflow-x-auto">
                                              {formatJSON(ex)}
                                            </pre>
                                          </div>
                                        ))}
                                      </div>
                                    )}
                                  </AccordionContent>
                                </AccordionItem>
                              ))}
                            </Accordion>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
                
                {apiDocs.testEndpoint && (
                  <div className="mt-8 pt-6 border-t border-indigo-800/30">
                    <h3 className="text-lg font-medium text-white mb-4 flex items-center">
                      <Zap className="h-5 w-5 mr-2 text-amber-400" />
                      เอนด์พอยต์สำหรับทดสอบ
                    </h3>
                    <div className="bg-amber-900/20 border border-amber-800/30 rounded-md p-4 mb-4">
                      <div className="flex items-center gap-3 mb-2">
                        <div className="px-2 py-1 rounded text-xs font-bold uppercase bg-amber-950/70 text-amber-400 border border-amber-800/40">
                          {apiDocs.testEndpoint.method}
                        </div>
                        <span className="font-mono text-sm text-amber-300">
                          {apiDocs.baseUrl}{apiDocs.testEndpoint.path}
                        </span>
                      </div>
                      <p className="text-amber-300 text-sm mt-1">{apiDocs.testEndpoint.description}</p>
                      
                      <div className="mt-4">
                        <div className="bg-black/30 border border-amber-800/20 rounded-md p-3">
                          <h4 className="text-amber-400 text-sm font-medium mb-2">Headers ที่จำเป็น</h4>
                          <div className="font-mono text-xs text-amber-300">
                            {apiDocs.testEndpoint.headers.map((header, i) => (
                              <div key={i} className="mb-1">
                                {header.name}: <span className="opacity-80">your_api_key_here</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          
          {/* แท็บตัวอย่างโค้ด */}
          <TabsContent value="examples" className="space-y-6">
            <Card className="bg-gradient-to-br from-indigo-950/70 to-purple-950/60 border-indigo-800/50">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Code className="h-5 w-5 mr-2 text-indigo-300" />
                  ตัวอย่างโค้ด
                </CardTitle>
                <CardDescription className="text-indigo-300">
                  ตัวอย่างการใช้งาน API ในภาษาโปรแกรมต่างๆ
                </CardDescription>
              </CardHeader>
              <CardContent className="text-indigo-200">
                <div className="bg-indigo-950/60 border border-indigo-800/40 rounded-lg">
                  <div className="flex justify-between items-center p-2 border-b border-indigo-800/40">
                    <div className="flex space-x-1">
                      <Button
                        variant={activeExample === "curl" ? "secondary" : "ghost"}
                        size="sm"
                        onClick={() => setActiveExample("curl")}
                        className={activeExample === "curl" ? "bg-indigo-800/40 text-indigo-200" : "text-indigo-400"}
                      >
                        cURL
                      </Button>
                      <Button
                        variant={activeExample === "javascript" ? "secondary" : "ghost"}
                        size="sm"
                        onClick={() => setActiveExample("javascript")}
                        className={activeExample === "javascript" ? "bg-indigo-800/40 text-indigo-200" : "text-indigo-400"}
                      >
                        JavaScript
                      </Button>
                      <Button
                        variant={activeExample === "python" ? "secondary" : "ghost"}
                        size="sm"
                        onClick={() => setActiveExample("python")}
                        className={activeExample === "python" ? "bg-indigo-800/40 text-indigo-200" : "text-indigo-400"}
                      >
                        Python
                      </Button>
                      <Button
                        variant={activeExample === "php" ? "secondary" : "ghost"}
                        size="sm"
                        onClick={() => setActiveExample("php")}
                        className={activeExample === "php" ? "bg-indigo-800/40 text-indigo-200" : "text-indigo-400"}
                      >
                        PHP
                      </Button>
                      <Button
                        variant={activeExample === "java" ? "secondary" : "ghost"}
                        size="sm"
                        onClick={() => setActiveExample("java")}
                        className={activeExample === "java" ? "bg-indigo-800/40 text-indigo-200" : "text-indigo-400"}
                      >
                        Java
                      </Button>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-indigo-400 hover:text-amber-400 hover:bg-transparent"
                      onClick={() => {
                        let code = "";
                        if (activeExample === "curl") code = apiDocs.examples.curlExample;
                        else if (activeExample === "javascript") code = apiDocs.examples.javascriptExample;
                        else if (activeExample === "python") code = apiDocs.examples.pythonExample;
                        else if (activeExample === "php") code = apiDocs.examples.phpExample;
                        else if (activeExample === "java") code = apiDocs.examples.javaExample;
                        
                        copyToClipboard(code, `${activeExample}-example`);
                      }}
                    >
                      {copyStatus[`${activeExample}-example`] ? (
                        <span className="flex items-center">
                          <Check className="h-3.5 w-3.5 mr-1" /> คัดลอกแล้ว
                        </span>
                      ) : (
                        <span className="flex items-center">
                          <Copy className="h-3.5 w-3.5 mr-1" /> คัดลอกโค้ด
                        </span>
                      )}
                    </Button>
                  </div>
                  <div className="p-4">
                    <pre className="font-mono text-xs text-indigo-300 overflow-x-auto whitespace-pre-wrap bg-black/30 p-4 rounded-md">
                      {activeExample === "curl" && apiDocs.examples.curlExample}
                      {activeExample === "javascript" && apiDocs.examples.javascriptExample}
                      {activeExample === "python" && apiDocs.examples.pythonExample}
                      {activeExample === "php" && apiDocs.examples.phpExample}
                      {activeExample === "java" && apiDocs.examples.javaExample}
                    </pre>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          {/* แท็บคู่มือการใช้งาน */}
          <TabsContent value="guide" className="space-y-6">
            <Card className="bg-gradient-to-br from-indigo-950/70 to-purple-950/60 border-indigo-800/50">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Book className="h-5 w-5 mr-2 text-indigo-300" />
                  {usageGuide.title}
                </CardTitle>
                <CardDescription className="text-indigo-300">
                  {usageGuide.overview}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-8 text-indigo-200">
                <div className="bg-indigo-950/60 border border-indigo-800/40 rounded-lg p-4">
                  <h3 className="text-white text-lg mb-2">API Key</h3>
                  <p className="text-indigo-300">{usageGuide.apiKeyInstructions}</p>
                </div>
                
                {usageGuide.sections.map((section, index) => (
                  <div key={index} className="space-y-4">
                    <h3 className="text-white text-lg font-medium flex items-center">
                      <span className="inline-flex justify-center items-center w-6 h-6 rounded-full bg-indigo-900/70 text-indigo-300 text-sm mr-2">
                        {index + 1}
                      </span>
                      {section.title}
                    </h3>
                    
                    {section.description && (
                      <p className="text-indigo-300">{section.description}</p>
                    )}
                    
                    {section.steps && (
                      <div className="bg-indigo-950/60 border border-indigo-800/40 rounded-lg p-4">
                        <h4 className="text-white font-medium mb-3 flex items-center">
                          <List className="h-4 w-4 mr-2 text-indigo-300" />
                          ขั้นตอนการดำเนินการ
                        </h4>
                        <ul className="space-y-2">
                          {section.steps.map((step, i) => (
                            <li key={i} className="flex items-start">
                              <ChevronRight className="h-4 w-4 text-indigo-400 mt-0.5 mr-2 flex-shrink-0" />
                              <span className="text-indigo-300">{step}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                    
                    {section.tips && (
                      <div className="bg-indigo-950/60 border border-indigo-800/40 rounded-lg p-4">
                        <h4 className="text-white font-medium mb-3 flex items-center">
                          <Info className="h-4 w-4 mr-2 text-indigo-300" />
                          คำแนะนำเพิ่มเติม
                        </h4>
                        <ul className="space-y-2">
                          {section.tips.map((tip, i) => (
                            <li key={i} className="flex items-start">
                              <span className="text-amber-400 mr-2 font-bold">•</span>
                              <span className="text-indigo-300">{tip}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                    
                    {section.responseStructure && (
                      <div className="bg-indigo-950/60 border border-indigo-800/40 rounded-lg p-4">
                        <h4 className="text-white font-medium mb-3 flex items-center">
                          <Database className="h-4 w-4 mr-2 text-indigo-300" />
                          โครงสร้างข้อมูลการตอบกลับ
                        </h4>
                        <div className="bg-black/30 border border-indigo-800/40 rounded-md p-3 font-mono text-xs text-indigo-300 overflow-x-auto">
                          {formatJSON(section.responseStructure)}
                        </div>
                      </div>
                    )}
                    
                    {section.commonErrors && (
                      <div className="bg-indigo-950/60 border border-indigo-800/40 rounded-lg p-4">
                        <h4 className="text-white font-medium mb-3 flex items-center">
                          <AlertTriangle className="h-4 w-4 mr-2 text-amber-400" />
                          ข้อผิดพลาดที่พบบ่อยและการแก้ไข
                        </h4>
                        <div className="space-y-3">
                          {section.commonErrors.map((error, i) => (
                            <div key={i} className="bg-black/30 border border-amber-900/30 rounded-md p-3">
                              <div className="flex items-center gap-2 mb-1">
                                <span className="text-amber-400 font-mono font-bold">{error.code}</span>
                                <span className="text-amber-300">{error.message}</span>
                              </div>
                              <div className="text-indigo-300 text-sm">
                                <span className="text-indigo-400 font-medium">การแก้ไข:</span> {error.solution}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </CardContent>
              <CardFooter className="bg-black/20 border-t border-indigo-800/20 px-6 py-4">
                <div className="text-indigo-300 text-sm">
                  <p>หากคุณต้องการความช่วยเหลือเพิ่มเติม สามารถติดต่อทีมสนับสนุนได้ทางอีเมล <EMAIL></p>
                </div>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}