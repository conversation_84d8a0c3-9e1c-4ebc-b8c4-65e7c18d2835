{"id": "9a2de1cb-be07-44fb-8ae8-ca697eba4585", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.api_keys": {"name": "api_keys", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "api_key": {"name": "api_key", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "api_key_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "last_used": {"name": "last_used", "type": "timestamp", "primaryKey": false, "notNull": false}, "request_count": {"name": "request_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "ip_whitelist": {"name": "ip_whitelist", "type": "text[]", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "limit_enabled": {"name": "limit_enabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "usage_limit": {"name": "usage_limit", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"api_keys_user_id_users_id_fk": {"name": "api_keys_user_id_users_id_fk", "tableFrom": "api_keys", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"api_keys_api_key_unique": {"name": "api_keys_api_key_unique", "nullsNotDistinct": false, "columns": ["api_key"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.api_logs": {"name": "api_logs", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "api_key_id": {"name": "api_key_id", "type": "integer", "primaryKey": false, "notNull": true}, "request_type": {"name": "request_type", "type": "api_request_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "request_data": {"name": "request_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "response_status": {"name": "response_status", "type": "api_response_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "response_data": {"name": "response_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "slip_verification_id": {"name": "slip_verification_id", "type": "integer", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": true}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "processing_time": {"name": "processing_time", "type": "integer", "primaryKey": false, "notNull": true}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"api_logs_api_key_id_api_keys_id_fk": {"name": "api_logs_api_key_id_api_keys_id_fk", "tableFrom": "api_logs", "tableTo": "api_keys", "columnsFrom": ["api_key_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "api_logs_slip_verification_id_slip_verifications_id_fk": {"name": "api_logs_slip_verification_id_slip_verifications_id_fk", "tableFrom": "api_logs", "tableTo": "slip_verifications", "columnsFrom": ["slip_verification_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.api_response_templates": {"name": "api_response_templates", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "status_code": {"name": "status_code", "type": "integer", "primaryKey": false, "notNull": true}, "status_type": {"name": "status_type", "type": "text", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "template": {"name": "template", "type": "jsonb", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.coupons": {"name": "coupons", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "discount_percent": {"name": "discount_percent", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "discount_amount": {"name": "discount_amount", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "max_usage": {"name": "max_usage", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "usage_count": {"name": "usage_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"coupons_code_unique": {"name": "coupons_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.packages": {"name": "packages", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "price": {"name": "price", "type": "integer", "primaryKey": false, "notNull": true}, "discount_3_months": {"name": "discount_3_months", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "discount_6_months": {"name": "discount_6_months", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "discount_12_months": {"name": "discount_12_months", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "duration_days": {"name": "duration_days", "type": "integer", "primaryKey": false, "notNull": true, "default": 30}, "requests_limit": {"name": "requests_limit", "type": "integer", "primaryKey": false, "notNull": true}, "credit_per_verification": {"name": "credit_per_verification", "type": "double precision", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "features": {"name": "features", "type": "text[]", "primaryKey": false, "notNull": false}, "tag": {"name": "tag", "type": "text", "primaryKey": false, "notNull": false}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.slip_verifications": {"name": "slip_verifications", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "transaction_ref": {"name": "transaction_ref", "type": "text", "primaryKey": false, "notNull": false}, "bank_name": {"name": "bank_name", "type": "text", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "double precision", "primaryKey": false, "notNull": false}, "sender": {"name": "sender", "type": "text", "primaryKey": false, "notNull": false}, "receiver": {"name": "receiver", "type": "text", "primaryKey": false, "notNull": false}, "transaction_date": {"name": "transaction_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "response_data": {"name": "response_data", "type": "text", "primaryKey": false, "notNull": false}, "credit_used": {"name": "credit_used", "type": "double precision", "primaryKey": false, "notNull": false}, "used_credit": {"name": "used_credit", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "verification_source": {"name": "verification_source", "type": "text", "primaryKey": false, "notNull": false, "default": "'web'"}, "api_key_id": {"name": "api_key_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"slip_verifications_user_id_users_id_fk": {"name": "slip_verifications_user_id_users_id_fk", "tableFrom": "slip_verifications", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "slip_verifications_api_key_id_api_keys_id_fk": {"name": "slip_verifications_api_key_id_api_keys_id_fk", "tableFrom": "slip_verifications", "tableTo": "api_keys", "columnsFrom": ["api_key_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.system_settings": {"name": "system_settings", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": false}, "value_json": {"name": "value_json", "type": "jsonb", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"system_settings_key_unique": {"name": "system_settings_key_unique", "nullsNotDistinct": false, "columns": ["key"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.top_up_transactions": {"name": "top_up_transactions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "double precision", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "verification_id": {"name": "verification_id", "type": "integer", "primaryKey": false, "notNull": false}, "reference_code": {"name": "reference_code", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"top_up_transactions_user_id_users_id_fk": {"name": "top_up_transactions_user_id_users_id_fk", "tableFrom": "top_up_transactions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_packages": {"name": "user_packages", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "package_id": {"name": "package_id", "type": "integer", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "requests_used": {"name": "requests_used", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "last_quota_reset_date": {"name": "last_quota_reset_date", "type": "date", "primaryKey": false, "notNull": false}, "duration_months": {"name": "duration_months", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_packages_user_id_users_id_fk": {"name": "user_packages_user_id_users_id_fk", "tableFrom": "user_packages", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_packages_package_id_packages_id_fk": {"name": "user_packages_package_id_packages_id_fk", "tableFrom": "user_packages", "tableTo": "packages", "columnsFrom": ["package_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "company_name": {"name": "company_name", "type": "text", "primaryKey": false, "notNull": false}, "phone_number": {"name": "phone_number", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "profile_image": {"name": "profile_image", "type": "text", "primaryKey": false, "notNull": false}, "credit": {"name": "credit", "type": "double precision", "primaryKey": false, "notNull": true, "default": 0}, "role": {"name": "role", "type": "user_role", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'user'"}, "status": {"name": "status", "type": "user_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "nullsNotDistinct": false, "columns": ["username"]}, "users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.api_key_status": {"name": "api_key_status", "schema": "public", "values": ["active", "inactive", "revoked"]}, "public.api_request_type": {"name": "api_request_type", "schema": "public", "values": ["verify_slip", "get_usage", "get_history"]}, "public.api_response_status": {"name": "api_response_status", "schema": "public", "values": ["success", "error", "invalid_request", "quota_exceeded", "unauthorized"]}, "public.user_role": {"name": "user_role", "schema": "public", "values": ["user", "admin"]}, "public.user_status": {"name": "user_status", "schema": "public", "values": ["active", "inactive", "suspended"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}