import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';
import { db } from './db';
import { systemSettings } from '@shared/schema';
import { eq } from 'drizzle-orm';
import { logger } from './logger';

const execPromise = promisify(exec);

// สร้างโฟลเดอร์สำหรับเก็บไฟล์สำรองข้อมูล
// ใช้ import.meta.url เพื่อให้ได้พาธที่สมบูรณ์โดยไม่มีช่องว่าง (สำหรับ ESM)
import { fileURLToPath } from 'url';
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const backupDir = path.resolve(__dirname, '../backups');
if (!fs.existsSync(backupDir)) {
  fs.mkdirSync(backupDir, { recursive: true });
}
logger.info(`สร้างโฟลเดอร์สำรองข้อมูลที่: ${backupDir}`);

export class BackupService {
  /**
   * สร้างไฟล์สำรองข้อมูลฐานข้อมูล
   */
  public async createBackup(): Promise<{ fileName: string; filePath: string; fileSize: string }> {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupFileName = `slipkuy_backup_${timestamp}.sql`;
      const backupFilePath = path.join(backupDir, backupFileName);

      // ดึงค่า DATABASE_URL จาก environment variable
      const databaseUrl = process.env.DATABASE_URL;
      if (!databaseUrl) {
        throw new Error('ไม่พบค่า DATABASE_URL ในตัวแปรสภาพแวดล้อม');
      }

      // แยกข้อมูลการเชื่อมต่อจาก DATABASE_URL
      // รูปแบบ: postgresql://username:password@host:port/database
      const dbUrlRegex = /postgresql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/([^?]+)/;
      const match = databaseUrl.match(dbUrlRegex);

      if (!match) {
        throw new Error('รูปแบบ DATABASE_URL ไม่ถูกต้อง');
      }

      const [, username, password, host, port, database] = match;

      // สร้างคำสั่ง pg_dump
      // ใช้ quotes รอบพาธของไฟล์เพื่อป้องกันปัญหาเรื่องช่องว่าง
      // ใช้ pg_dump ที่มาพร้อมกับ PostgreSQL เวอร์ชัน 16.8
      const pgDumpPath = '/usr/bin/pg_dump';
      const command = `PGPASSWORD=${password} ${pgDumpPath} -h ${host} -p ${port} -U ${username} -d ${database} -F p -f "${backupFilePath}"`;

      logger.info(`กำลังสร้างไฟล์สำรองข้อมูล: ${backupFileName}`);
      logger.info(`คำสั่ง pg_dump: ${command.replace(password, '********')}`);
      const { stdout, stderr } = await execPromise(command);

      if (stderr && !stderr.includes('warning')) {
        throw new Error(stderr);
      }

      // ตรวจสอบว่าไฟล์ถูกสร้างขึ้นหรือไม่
      if (!fs.existsSync(backupFilePath)) {
        throw new Error('ไม่สามารถสร้างไฟล์สำรองข้อมูลได้');
      }

      const fileSize = fs.statSync(backupFilePath).size;
      const fileSizeInMB = (fileSize / (1024 * 1024)).toFixed(2);

      logger.success(`สร้างไฟล์สำรองข้อมูลสำเร็จ: ${backupFileName} (${fileSizeInMB} MB)`);

      // อัพเดตการตั้งค่าการสำรองข้อมูลอัตโนมัติ
      await this.updateLastBackupTime();

      // ลบไฟล์สำรองข้อมูลเก่าตามจำนวนที่กำหนด
      await this.cleanupOldBackups();

      return {
        fileName: backupFileName,
        filePath: backupFilePath,
        fileSize: `${fileSizeInMB} MB`
      };
    } catch (error) {
      logger.error('เกิดข้อผิดพลาดในการสร้างไฟล์สำรองข้อมูล:', error);
      throw error;
    }
  }

  /**
   * อัพเดตเวลาสำรองข้อมูลล่าสุดและคำนวณเวลาสำรองข้อมูลครั้งถัดไป
   */
  private async updateLastBackupTime(): Promise<void> {
    try {
      const setting = await db.select()
        .from(systemSettings)
        .where(eq(systemSettings.key, 'backup_settings'));

      if (setting.length === 0) {
        return;
      }

      const backupSettings = setting[0].valueJson;
      if (!backupSettings || !backupSettings.enabled) {
        return;
      }

      // อัพเดตเวลาสำรองข้อมูลล่าสุด
      backupSettings.lastBackup = new Date().toISOString();

      // คำนวณเวลาสำรองข้อมูลครั้งถัดไป
      const [hours, minutes] = backupSettings.time.split(':').map(Number);
      const nextBackup = new Date();
      nextBackup.setHours(hours, minutes, 0, 0);

      // เพิ่มวันตามความถี่
      if (backupSettings.frequency === 'daily') {
        nextBackup.setDate(nextBackup.getDate() + 1);
      } else if (backupSettings.frequency === 'weekly') {
        nextBackup.setDate(nextBackup.getDate() + 7);
      } else if (backupSettings.frequency === 'monthly') {
        nextBackup.setMonth(nextBackup.getMonth() + 1);
      }

      backupSettings.nextBackup = nextBackup.toISOString();

      // บันทึกการตั้งค่าลงในฐานข้อมูล
      await db.update(systemSettings)
        .set({
          valueJson: backupSettings,
          updatedAt: new Date()
        })
        .where(eq(systemSettings.key, 'backup_settings'));

      logger.info(`อัพเดตเวลาสำรองข้อมูลล่าสุดและครั้งถัดไปสำเร็จ: ${backupSettings.lastBackup} -> ${backupSettings.nextBackup}`);
    } catch (error) {
      logger.error('เกิดข้อผิดพลาดในการอัพเดตเวลาสำรองข้อมูล:', error);
    }
  }

  /**
   * ลบไฟล์สำรองข้อมูลเก่าตามจำนวนที่กำหนด
   */
  private async cleanupOldBackups(): Promise<void> {
    try {
      const setting = await db.select()
        .from(systemSettings)
        .where(eq(systemSettings.key, 'backup_settings'));

      if (setting.length === 0) {
        return;
      }

      const backupSettings = setting[0].valueJson;
      if (!backupSettings || !backupSettings.keepCount) {
        return;
      }

      const keepCount = backupSettings.keepCount;

      // ดึงรายการไฟล์สำรองข้อมูลทั้งหมด
      const files = fs.readdirSync(backupDir)
        .filter(file => file.endsWith('.sql'))
        .map(file => {
          const filePath = path.join(backupDir, file);
          const stats = fs.statSync(filePath);

          return {
            fileName: file,
            filePath: filePath,
            createdAt: stats.birthtime
          };
        })
        .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime()); // เรียงจากใหม่ไปเก่า

      // ลบไฟล์เก่าที่เกินจำนวนที่กำหนด
      if (files.length > keepCount) {
        const filesToDelete = files.slice(keepCount);

        for (const file of filesToDelete) {
          fs.unlinkSync(file.filePath);
          logger.info(`ลบไฟล์สำรองข้อมูลเก่า: ${file.fileName}`);
        }

        logger.success(`ลบไฟล์สำรองข้อมูลเก่าสำเร็จ ${filesToDelete.length} ไฟล์`);
      }
    } catch (error) {
      logger.error('เกิดข้อผิดพลาดในการลบไฟล์สำรองข้อมูลเก่า:', error);
    }
  }

  /**
   * ตรวจสอบและสร้างไฟล์สำรองข้อมูลอัตโนมัติ
   * ใช้สำหรับเรียกจาก cron job
   */
  public async checkAndCreateBackup(): Promise<void> {
    try {
      const setting = await db.select()
        .from(systemSettings)
        .where(eq(systemSettings.key, 'backup_settings'));

      if (setting.length === 0) {
        return;
      }

      const backupSettings = setting[0].valueJson;
      if (!backupSettings || !backupSettings.enabled || !backupSettings.nextBackup) {
        return;
      }

      const nextBackup = new Date(backupSettings.nextBackup);
      const now = new Date();

      // ถ้าถึงเวลาสำรองข้อมูล
      if (nextBackup <= now) {
        logger.info('ถึงเวลาสำรองข้อมูลอัตโนมัติ');
        await this.createBackup();
      }
    } catch (error) {
      logger.error('เกิดข้อผิดพลาดในการตรวจสอบและสร้างไฟล์สำรองข้อมูลอัตโนมัติ:', error);
    }
  }
}

// สร้าง instance เดียวของ BackupService
export const backupService = new BackupService();
