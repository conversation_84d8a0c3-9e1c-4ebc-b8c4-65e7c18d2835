import { Express, Request, Response, NextFunction } from "express";
import { storage } from "../storage";
import { isAdmin } from "../auth";

// Middleware ตรวจสอบว่า user ล็อกอินแล้วหรือยัง
function isAuthenticated(req: Request, res: Response, next: NextFunction) {
  if (req.isAuthenticated()) {
    return next();
  }
  res.status(401).json({ error: "ผู้ใช้ยังไม่ได้เข้าสู่ระบบ" });
}

export function setupPaymentRoutes(app: Express) {
  // GET /api/payment-accounts - ดึงข้อมูลบัญชีธนาคารสำหรับการชำระเงิน
  app.get("/api/payment-accounts", async (req, res) => {
    try {
      const paymentSettings = await storage.getSetting("payment_accounts");
      
      // หากไม่มีข้อมูล ให้ส่งค่าเริ่มต้น
      if (!paymentSettings) {
        return res.status(200).json({
          bankAccounts: [
            {
              bankCode: "014",
              accountNumber: "123-456-7890",
              accountName: "บริษัท สลิปคุย จำกัด",
              accountNameEn: "SLIPKUY CO., LTD.",
              qrPaymentImage: ""
            }
          ],
          promptpays: [
            {
              number: "0-9999-99999-99",
              qrPaymentImage: ""
            }
          ]
        });
      }
      
      // หากมีข้อมูล แต่เป็น string ให้แปลงเป็น object
      let accountData;
      if (typeof paymentSettings.value === 'string') {
        try {
          accountData = JSON.parse(paymentSettings.value);
        } catch (error) {
          accountData = {
            bankAccounts: [],
            promptpays: []
          };
        }
      } else {
        accountData = paymentSettings.value;
      }
      
      // ตรวจสอบและเปลี่ยนโครงสร้างของ promptpays หากยังเป็นแบบเก่า (array ของ string)
      if (accountData.promptpays && Array.isArray(accountData.promptpays)) {
        if (accountData.promptpays.length > 0 && typeof accountData.promptpays[0] === 'string') {
          accountData.promptpays = accountData.promptpays.map((number: string) => ({
            number,
            qrPaymentImage: ""
          }));
        }
      }
      
      res.status(200).json(accountData);
    } catch (error) {
      console.error("Error fetching payment accounts:", error);
      res.status(500).json({ error: "เกิดข้อผิดพลาดในการดึงข้อมูลบัญชีการชำระเงิน" });
    }
  });

  // POST /api/payment-accounts - บันทึกข้อมูลบัญชีธนาคารสำหรับการชำระเงิน (สำหรับแอดมินเท่านั้น)
  app.post("/api/payment-accounts", isAuthenticated, async (req, res) => {
    try {
      // ตรวจสอบว่าเป็นแอดมินหรือไม่
      if (!isAdmin(req)) {
        return res.status(403).json({ error: "คุณไม่มีสิทธิ์เข้าถึงส่วนนี้" });
      }
      
      const { bankAccounts, promptpays } = req.body;
      
      // ตรวจสอบว่าข้อมูลถูกต้องหรือไม่
      if (!bankAccounts || !Array.isArray(bankAccounts) || !promptpays || !Array.isArray(promptpays)) {
        return res.status(400).json({ error: "รูปแบบข้อมูลไม่ถูกต้อง" });
      }
      
      // บันทึกข้อมูลเข้า database
      await storage.setSetting("payment_accounts", { bankAccounts, promptpays });
      
      res.status(200).json({ message: "บันทึกข้อมูลบัญชีการชำระเงินเรียบร้อยแล้ว" });
    } catch (error) {
      console.error("Error saving payment accounts:", error);
      res.status(500).json({ error: "เกิดข้อผิดพลาดในการบันทึกข้อมูลบัญชีการชำระเงิน" });
    }
  });
}