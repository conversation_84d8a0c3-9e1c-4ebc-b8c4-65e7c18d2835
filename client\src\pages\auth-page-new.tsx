import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Navbar } from "@/components/layouts/navbar";
import { Footer } from "@/components/layouts/footer";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { useAuth } from "@/hooks/use-auth";
import { insertUserSchema } from "@shared/schema";
import { 
  ReceiptText, 
  Building, 
  ExternalLink, 
  Check,
  Loader2,
  User,
  Mail,
  Phone,
  Lock,
  KeyRound,
  ArrowLeft,
  AlertCircle,
  ShieldCheck,
  RefreshCw,
  RotateCw,
  Sword,
  Sparkles,
  Skull,
  Zap,
  Shield,
  Crown,
  BoltIcon,
  Laugh,
  FlameKindling,
  Stars,
  BadgeCheck,
  LucideIcon
} from "lucide-react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { toast } from "@/hooks/use-toast";
import axios from "axios";
import { motion, AnimatePresence } from "framer-motion";

// กำหนด Schema สำหรับการเข้าสู่ระบบ
const loginSchema = z.object({
  username: z.string().min(1, "กรุณากรอกชื่อผู้ใช้"),
  password: z.string().min(1, "กรุณากรอกรหัสผ่าน"),
});

// กำหนด Schema สำหรับการลงทะเบียน
const registerSchema = insertUserSchema.extend({
  password: z.string().min(8, "รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร (เพื่อป้องกันมนุษย์ธรรมดา)"),
  confirmPassword: z.string(),
  verificationMethod: z.enum(["email", "phone"]),
}).refine((data) => data.password === data.confirmPassword, {
  message: "รหัสผ่านไม่ตรงกัน! คุณพิมพ์ผิดหรือว่าลืมรหัสภายใน 2 วินาทีกันแน่?",
  path: ["confirmPassword"],
});

// คำคมกวนๆ สำหรับแสดงสุ่ม
const epicQuotes = [
  "หากท่านล็อกอินสำเร็จ จะได้รับพลังเทพเจ้า... แต่ถ้าลืมรหัสผ่าน ก็จำไว้ว่าท่านไม่ใช่เทพ!",
  "เข้าสู่ระบบวันนี้ รับฟรี! ความอลังการของระบบ (ไม่มีค่าส่ง)",
  "แม้แต่เทพเจ้ายังต้องล็อกอินก่อนเปิดสวรรค์",
  "รหัสผ่านของท่านปลอดภัยกับเรา... เทพเจ้าสาบานว่าไม่แอบดู (จริงๆนะ)",
  "ลงทะเบียนวันนี้ ได้สิทธิ์ลงชื่อบนกำแพงแห่งเทพเจ้า (เป็นไม้หมายเหตุชั้นล่างสุด)",
  "อย่ากรอกรหัสผิด 3 ครั้ง เดี๋ยวเทพเจ้าจะหัวเราะเยาะท่าน (ในที่ลับหลัง)",
  "กรอกข้อมูลให้ถูกต้อง หรือจะถูกส่งไปพบยมทูต (แปลว่าหน้า login อีกรอบนั่นแหละ)",
  "ท่านคือผู้ถูกเลือก... ให้กรอกฟอร์มนี้",
  "ระบบนี้สร้างโดยโปรแกรมเมอร์ที่อดนอน เคารพเขาด้วยการกรอกฟอร์มให้ถูกต้อง",
];

// กำหนด Schema สำหรับการขอรีเซ็ตรหัสผ่าน
const forgotPasswordSchema = z.object({
  identifier: z.string().min(1, "กรุณากรอกชื่อผู้ใช้ อีเมล หรือเบอร์โทรศัพท์"),
  verificationMethod: z.enum(["email", "phone"]),
});

// กำหนด Schema สำหรับการยืนยันรหัส OTP และตั้งรหัสผ่านใหม่
const resetPasswordSchema = z.object({
  verificationCode: z.string().min(6, "รหัสยืนยันต้องมี 6 หลัก").max(6, "รหัสยืนยันต้องมี 6 หลัก"),
  newPassword: z.string().min(8, "รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร"),
  confirmPassword: z.string(),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "รหัสผ่านไม่ตรงกัน",
  path: ["confirmPassword"],
});

type LoginFormValues = z.infer<typeof loginSchema>;
type RegisterFormValues = z.infer<typeof registerSchema>;
type ForgotPasswordFormValues = z.infer<typeof forgotPasswordSchema>;
type ResetPasswordFormValues = z.infer<typeof resetPasswordSchema>;

export default function AuthPageNew() {
  const [activeTab, setActiveTab] = useState<"login" | "register" | "verify" | "forgot-password">("login");
  const [lightningEffect, setLightningEffect] = useState(false);
  
  // คำคมไทยแบบเทพๆ กวนๆ
  const epicQuotes = [
    "การยืนยันตัวตนเหมือนการได้พบกับเทพเจ้า ต้องมีความอดทน",
    "ล็อกอินวันนี้ รับพรจากเทพเจ้าฟรี ไม่มีค่าใช้จ่าย",
    "อย่าลืมรหัสผ่าน เพราะแม้แต่เทพเจ้าก็ช่วยคุณไม่ได้",
    "สมัครสมาชิกวันนี้ รับส่วนลด 10% จากพระอิศวร",
    "เทพเจ้าก็ต้องยืนยันอีเมล เราเป็นใครจะไม่ต้องยืนยัน?",
    "หน้าล็อกอินที่สวยที่สุดในสามโลก",
    "รหัสผ่านควรมีความซับซ้อนพอๆ กับการต่อสู้ของเทพเจ้า",
    "ความปลอดภัยมาก่อนเสมอ แม้แต่เมื่อคุณเป็นเทพเจ้า",
  ];
  const [quote, setQuote] = useState(epicQuotes[Math.floor(Math.random() * epicQuotes.length)]);
  const [location, setLocation] = useLocation();
  
  // เมื่อเปลี่ยน tab เป็น forgot-password ให้นำทางไปยังหน้า password-reset
  useEffect(() => {
    if (activeTab === "forgot-password") {
      setLocation("/password-reset");
      // กลับมาที่แท็บ login หลังจากเด้งไปที่หน้า password-reset
      setActiveTab("login");
    }
  }, [activeTab, setLocation]);
  const { user, loginMutation, registerMutation } = useAuth();
  const [registrationStep, setRegistrationStep] = useState<"form" | "verification">("form");
  const [forgotPasswordStep, setForgotPasswordStep] = useState<"request" | "verify" | "reset">("request");
  const [verificationMethod, setVerificationMethod] = useState<"email" | "phone">("email");
  const [verificationCode, setVerificationCode] = useState("");
  const [isVerifying, setIsVerifying] = useState(false);
  const [isResettingPassword, setIsResettingPassword] = useState(false);
  const [secondsLeft, setSecondsLeft] = useState(0);
  const [tempUserData, setTempUserData] = useState<any>(null);
  const [resetPasswordData, setResetPasswordData] = useState<{
    identifier: string;
    type: "email" | "phone";
    userId?: number;
  } | null>(null);
  const [userVerificationStatus, setUserVerificationStatus] = useState<{
    email_verified?: boolean;
    phone_verified?: boolean;
  }>({});

  // สร้าง Form สำหรับการเข้าสู่ระบบ
  const loginForm = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: "",
      password: "",
    },
  });

  // สร้าง Form สำหรับการลงทะเบียน
  const registerForm = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      username: "",
      email: "",
      phoneNumber: "",
      firstName: "",
      lastName: "",
      companyName: "",
      password: "",
      confirmPassword: "",
      verificationMethod: "email",
    },
  });
  
  // สร้าง Form สำหรับการขอรีเซ็ตรหัสผ่าน
  const forgotPasswordForm = useForm<ForgotPasswordFormValues>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      identifier: "",
      verificationMethod: "email",
    },
  });
  
  // สร้าง Form สำหรับการรีเซ็ตรหัสผ่านหลังจากยืนยันตัวตน
  const resetPasswordForm = useForm<ResetPasswordFormValues>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      verificationCode: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  // เมื่อเปลี่ยนวิธีการยืนยัน
  useEffect(() => {
    const subscription = registerForm.watch((value, { name }) => {
      if (name === "verificationMethod") {
        setVerificationMethod(value.verificationMethod as "email" | "phone");
      }
    });
    return () => subscription.unsubscribe();
  }, [registerForm.watch]);

  // เช็คสถานะการยืนยันบัญชีเมื่อเข้าสู่ระบบแล้ว
  useEffect(() => {
    if (user) {
      // ตรวจสอบว่าผู้ใช้ยืนยันอีเมลหรือเบอร์โทรแล้วหรือไม่
      const needVerification = !user.email_verified && !user.phone_verified;
      
      // ถ้ายังไม่ได้ยืนยัน ให้อยู่ที่หน้านี้และแสดงแท็บยืนยัน
      if (needVerification) {
        setUserVerificationStatus({
          email_verified: user.email_verified || false,
          phone_verified: user.phone_verified || false
        });
        setActiveTab("verify");
      } 
      else {
        // ถ้ายืนยันแล้ว ไปที่หน้าแดชบอร์ด
        setLocation("/dashboard");
      }
    }
  }, [user]);

  // นับเวลาถอยหลัง
  useEffect(() => {
    if (secondsLeft <= 0) return;
    
    const timer = setInterval(() => {
      setSecondsLeft(prev => prev - 1);
    }, 1000);
    
    return () => clearInterval(timer);
  }, [secondsLeft]);
  
  // เมื่อเปลี่ยนวิธีการยืนยันสำหรับลืมรหัสผ่าน
  useEffect(() => {
    const subscription = forgotPasswordForm.watch((value, { name }) => {
      if (name === "verificationMethod") {
        setVerificationMethod(value.verificationMethod as "email" | "phone");
      }
    });
    return () => subscription.unsubscribe();
  }, [forgotPasswordForm.watch]);

  // ส่งคำขอเข้าสู่ระบบ
  const onLoginSubmit = (values: LoginFormValues) => {
    loginMutation.mutate(values);
  };

  // ส่งคำขอลงทะเบียนขั้นแรก - ส่งข้อมูลและรับรหัสยืนยัน
  const onRegisterSubmit = async (values: RegisterFormValues) => {
    try {
      // ลบ confirmPassword ออกเพราะไม่ต้องส่งไปยัง API
      const { confirmPassword, ...registerData } = values;
      
      // เก็บข้อมูลชั่วคราวสำหรับส่งเมื่อยืนยันเรียบร้อย
      setTempUserData(registerData);
      
      // ส่งคำขอรหัสยืนยัน
      const identifier = values.verificationMethod === "email" 
        ? values.email 
        : values.phoneNumber;
        
      // ส่ง API เพื่อขอรหัสยืนยัน
      const response = await axios.post("/api/verification/send", {
        type: values.verificationMethod,
        identifier,
        userId: 0, // ใช้ 0 สำหรับผู้ใช้ใหม่ที่ยังไม่มี ID
      });
      
      if (response.data.success) {
        toast({
          title: "ส่งรหัสยืนยันสำเร็จ",
          description: response.data.message,
        });
        
        // เปลี่ยนสถานะเป็นรอยืนยัน
        setRegistrationStep("verification");
        
        // ตั้งเวลาถอยหลังสำหรับการขอรหัสใหม่
        setSecondsLeft(120); // 2 นาที
      } else {
        toast({
          variant: "destructive",
          title: "ไม่สามารถส่งรหัสยืนยันได้",
          description: response.data.message || "กรุณาลองใหม่อีกครั้ง",
        });
      }
    } catch (error: any) {
      console.error("Registration error:", error);
      toast({
        variant: "destructive",
        title: "ไม่สามารถส่งรหัสยืนยันได้",
        description: error.response?.data?.message || "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง",
      });
    }
  };
  
  // ตรวจสอบรหัสยืนยัน
  const verifyCode = async () => {
    if (!verificationCode || verificationCode.length < 6) {
      toast({
        variant: "destructive",
        title: "รหัสยืนยันไม่ถูกต้อง",
        description: "กรุณากรอกรหัสยืนยัน 6 หลัก",
      });
      return;
    }
    
    try {
      setIsVerifying(true);
      const identifier = tempUserData?.verificationMethod === "email" 
        ? tempUserData.email 
        : tempUserData.phoneNumber;
        
      // ส่ง API เพื่อตรวจสอบรหัสยืนยัน
      const verifyResponse = await axios.post("/api/verification/verify", {
        type: tempUserData?.verificationMethod,
        identifier,
        code: verificationCode,
      });
      
      if (verifyResponse.data.success) {
        // ลงทะเบียนผู้ใช้หลังจากยืนยันสำเร็จ
        registerMutation.mutate(tempUserData);
      } else {
        toast({
          variant: "destructive",
          title: "รหัสยืนยันไม่ถูกต้อง",
          description: verifyResponse.data.message || "กรุณาตรวจสอบรหัสและลองใหม่อีกครั้ง",
        });
      }
    } catch (error: any) {
      console.error("Verification error:", error);
      toast({
        variant: "destructive",
        title: "ไม่สามารถยืนยันรหัสได้",
        description: error.response?.data?.message || "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง",
      });
    } finally {
      setIsVerifying(false);
    }
  };
  
  // ขอรหัสยืนยันใหม่
  const resendVerificationCode = async () => {
    if (secondsLeft > 0) return;
    
    try {
      // กรณีที่เป็นการสมัครใหม่
      if (tempUserData) {
        const identifier = tempUserData.verificationMethod === "email" 
          ? tempUserData.email 
          : tempUserData.phoneNumber;
          
        // ส่ง API เพื่อขอรหัสยืนยันใหม่
        const response = await axios.post("/api/verification/send", {
          type: tempUserData.verificationMethod,
          identifier,
          userId: 0,
        });
        
        if (response.data.success) {
          toast({
            title: "ส่งรหัสยืนยันใหม่สำเร็จ",
            description: response.data.message,
          });
          setSecondsLeft(120); // ตั้งเวลารอใหม่
        } else {
          toast({
            variant: "destructive",
            title: "ไม่สามารถส่งรหัสยืนยันได้",
            description: response.data.message || "กรุณาลองใหม่อีกครั้ง",
          });
        }
      } 
      // กรณีที่เป็นผู้ใช้เก่า
      else if (user) {
        const identifier = verificationMethod === "email" 
          ? user.email 
          : user.phoneNumber;
          
        if (!identifier) {
          toast({
            variant: "destructive",
            title: "ไม่สามารถส่งรหัสยืนยันได้",
            description: "ไม่พบข้อมูลสำหรับการยืนยัน กรุณาตรวจสอบข้อมูลส่วนตัวของคุณ",
          });
          return;
        }
          
        // ส่ง API เพื่อขอรหัสยืนยันใหม่
        const response = await axios.post("/api/verification/send", {
          type: verificationMethod,
          identifier,
          userId: user.id,
        });
        
        if (response.data.success) {
          toast({
            title: "ส่งรหัสยืนยันใหม่สำเร็จ",
            description: response.data.message,
          });
          setSecondsLeft(120); // ตั้งเวลารอใหม่
        } else {
          toast({
            variant: "destructive",
            title: "ไม่สามารถส่งรหัสยืนยันได้",
            description: response.data.message || "กรุณาลองใหม่อีกครั้ง",
          });
        }
      }
    } catch (error: any) {
      console.error("Resend error:", error);
      toast({
        variant: "destructive",
        title: "ไม่สามารถส่งรหัสยืนยันได้",
        description: error.response?.data?.message || "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง",
      });
    }
  };
  
  // ยืนยันบัญชีผู้ใช้ที่ล็อกอินแล้ว
  const verifyExistingUser = async () => {
    if (!verificationCode || verificationCode.length < 6) {
      toast({
        variant: "destructive",
        title: "รหัสยืนยันไม่ถูกต้อง",
        description: "กรุณากรอกรหัสยืนยัน 6 หลัก",
      });
      return;
    }
    
    if (!user) return;
    
    try {
      setIsVerifying(true);
      const identifier = verificationMethod === "email" 
        ? user.email 
        : user.phoneNumber;
        
      if (!identifier) {
        toast({
          variant: "destructive",
          title: "ไม่สามารถยืนยันรหัสได้",
          description: "ไม่พบข้อมูลสำหรับการยืนยัน กรุณาตรวจสอบข้อมูลส่วนตัวของคุณ",
        });
        setIsVerifying(false);
        return;
      }
        
      // ส่ง API เพื่อตรวจสอบรหัสยืนยัน
      const verifyResponse = await axios.post("/api/verification/verify", {
        type: verificationMethod,
        identifier,
        code: verificationCode,
        userId: user.id
      });
      
      if (verifyResponse.data.success) {
        toast({
          title: "ยืนยันบัญชีสำเร็จ",
          description: "คุณสามารถใช้งานระบบได้เต็มรูปแบบแล้ว",
        });
        
        // อัพเดตสถานะการยืนยัน
        setUserVerificationStatus(prev => ({
          ...prev,
          [verificationMethod === "email" ? "email_verified" : "phone_verified"]: true
        }));
        
        // รีโหลดหน้าเพื่ออัพเดตข้อมูลผู้ใช้
        window.location.href = "/dashboard";
      } else {
        toast({
          variant: "destructive",
          title: "รหัสยืนยันไม่ถูกต้อง",
          description: verifyResponse.data.message || "กรุณาตรวจสอบรหัสและลองใหม่อีกครั้ง",
        });
      }
    } catch (error: any) {
      console.error("Verification error:", error);
      toast({
        variant: "destructive",
        title: "ไม่สามารถยืนยันรหัสได้",
        description: error.response?.data?.message || "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง",
      });
    } finally {
      setIsVerifying(false);
    }
  };
  
  // ส่งคำขอลืมรหัสผ่าน
  const onForgotPasswordSubmit = async (values: ForgotPasswordFormValues) => {
    try {
      // ส่ง API เพื่อขอรหัสสำหรับรีเซ็ตรหัสผ่าน
      const response = await axios.post("/api/auth/forgot-password", {
        email: values.identifier, // ส่ง identifier เป็น email ตามที่ API ต้องการ
      });
      
      if (response.data.success) {
        toast({
          title: "ส่งรหัสยืนยันสำเร็จ",
          description: response.data.message,
        });
        
        // เก็บข้อมูลสำหรับขั้นตอนต่อไป
        setResetPasswordData({
          identifier: values.identifier,
          type: values.verificationMethod,
          userId: response.data.userId || undefined,
        });
        
        // เปลี่ยนสถานะเป็นขั้นตอนยืนยันรหัส
        setForgotPasswordStep("verify");
        
        // ตั้งเวลาถอยหลังสำหรับการขอรหัสใหม่
        setSecondsLeft(120); // 2 นาที
      } else {
        toast({
          variant: "destructive",
          title: "ไม่สามารถส่งรหัสยืนยันได้",
          description: response.data.message || "กรุณาลองใหม่อีกครั้ง",
        });
      }
    } catch (error: any) {
      console.error("Forgot password error:", error);
      toast({
        variant: "destructive",
        title: "ไม่สามารถดำเนินการได้",
        description: error.response?.data?.message || "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง",
      });
    }
  };
  
  // ขอรหัสยืนยันใหม่สำหรับรีเซ็ตรหัสผ่าน
  const resendResetPasswordCode = async () => {
    if (secondsLeft > 0 || !resetPasswordData) return;
    
    try {
      // ส่ง API เพื่อขอรหัสยืนยันใหม่
      const response = await axios.post("/api/auth/forgot-password", {
        identifier: resetPasswordData.identifier,
        type: resetPasswordData.type,
      });
      
      if (response.data.success) {
        toast({
          title: "ส่งรหัสยืนยันใหม่สำเร็จ",
          description: response.data.message,
        });
        setSecondsLeft(120); // ตั้งเวลารอใหม่
      } else {
        toast({
          variant: "destructive",
          title: "ไม่สามารถส่งรหัสยืนยันได้",
          description: response.data.message || "กรุณาลองใหม่อีกครั้ง",
        });
      }
    } catch (error: any) {
      console.error("Resend reset code error:", error);
      toast({
        variant: "destructive",
        title: "ไม่สามารถส่งรหัสยืนยันได้",
        description: error.response?.data?.message || "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง",
      });
    }
  };
  
  // ตรวจสอบรหัสยืนยันสำหรับรีเซ็ตรหัสผ่าน
  const verifyResetPasswordCode = async () => {
    if (!verificationCode || verificationCode.length < 6 || !resetPasswordData) {
      toast({
        variant: "destructive",
        title: "รหัสยืนยันไม่ถูกต้อง",
        description: "กรุณากรอกรหัสยืนยัน 6 หลัก",
      });
      return;
    }
    
    try {
      setIsVerifying(true);
      
      // ส่ง API เพื่อตรวจสอบรหัสยืนยัน
      const verifyResponse = await axios.post("/api/auth/verify-reset-code", {
        type: resetPasswordData.type,
        identifier: resetPasswordData.identifier,
        code: verificationCode,
      });
      
      if (verifyResponse.data.success) {
        toast({
          title: "ยืนยันรหัสสำเร็จ",
          description: "กรุณาตั้งรหัสผ่านใหม่",
        });
        
        // เปลี่ยนไปขั้นตอนรีเซ็ตรหัสผ่าน
        setForgotPasswordStep("reset");
      } else {
        toast({
          variant: "destructive",
          title: "รหัสยืนยันไม่ถูกต้อง",
          description: verifyResponse.data.message || "กรุณาตรวจสอบรหัสและลองใหม่อีกครั้ง",
        });
      }
    } catch (error: any) {
      console.error("Verification error:", error);
      toast({
        variant: "destructive",
        title: "ไม่สามารถยืนยันรหัสได้",
        description: error.response?.data?.message || "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง",
      });
    } finally {
      setIsVerifying(false);
    }
  };
  
  // รีเซ็ตรหัสผ่าน
  const onResetPasswordSubmit = async (values: ResetPasswordFormValues) => {
    if (!resetPasswordData) return;
    
    try {
      setIsResettingPassword(true);
      
      // ส่ง API เพื่อรีเซ็ตรหัสผ่าน
      const response = await axios.post("/api/password-reset/reset", {
        identifier: resetPasswordData.identifier,
        code: verificationCode, // ใช้ verificationCode ที่ยืนยันไปแล้ว
        newPassword: values.newPassword,
        confirmPassword: values.confirmPassword,
      });
      
      if (response.data.success) {
        toast({
          title: "รีเซ็ตรหัสผ่านสำเร็จ",
          description: "คุณสามารถใช้รหัสผ่านใหม่ในการเข้าสู่ระบบได้แล้ว",
        });
        
        // กลับไปหน้าเข้าสู่ระบบ
        setActiveTab("login");
        // รีเซ็ตข้อมูล
        setResetPasswordData(null);
        setVerificationCode("");
        resetPasswordForm.reset();
      } else {
        toast({
          variant: "destructive",
          title: "ไม่สามารถรีเซ็ตรหัสผ่านได้",
          description: response.data.message || "กรุณาลองใหม่อีกครั้ง",
        });
      }
    } catch (error: any) {
      console.error("Reset password error:", error);
      toast({
        variant: "destructive",
        title: "ไม่สามารถรีเซ็ตรหัสผ่านได้",
        description: error.response?.data?.message || "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง",
      });
    } finally {
      setIsResettingPassword(false);
    }
  };

  // ฟังก์ชันสำหรับเปลี่ยนแท็บพร้อมเอฟเฟกต์ฟ้าผ่า
  const changeTab = (tab: "login" | "register" | "verify" | "forgot-password") => {
    setLightningEffect(true);
    setTimeout(() => setLightningEffect(false), 300);
    setActiveTab(tab);
    
    // สุ่มคำคมใหม่เมื่อเปลี่ยนแท็บ
    setQuote(epicQuotes[Math.floor(Math.random() * epicQuotes.length)]);
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-purple-950 via-indigo-900 to-purple-950 relative">
      
      {/* เอฟเฟกต์พื้นหลังอลังการ */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* ดาวระยิบระยับในจักรวาล */}
        <div className="absolute inset-0" style={{ 
          backgroundImage: 'radial-gradient(white, rgba(255, 255, 255, 0.2) 2px, transparent 2px)',
          backgroundSize: '50px 50px'
        }}></div>
        
        {/* วงแหวนพลังเทพเจ้า */}
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[150vh] h-[150vh]">
          <div className="absolute inset-0 rounded-full border-8 border-amber-400/10 animate-[spin_120s_linear_infinite]"></div>
          <div className="absolute inset-10 rounded-full border-4 border-pink-500/10 animate-[spin_100s_linear_infinite_reverse]"></div>
          <div className="absolute inset-20 rounded-full border-2 border-blue-400/10 animate-[spin_80s_linear_infinite]"></div>
        </div>
        
        {/* คลื่นพลังงานเทพเจ้า */}
        <div className="absolute bottom-0 inset-x-0 h-48 bg-gradient-to-t from-pink-500/20 to-transparent"></div>
        
        {/* ฟ้าแลบที่มุมจอ */}
        <div className="absolute top-1/4 right-10 w-20 h-40">
          <motion.div 
            className="absolute inset-0 bg-yellow-300" 
            initial={{ opacity: 0 }}
            animate={{ 
              opacity: [0, 0.8, 0],
              rotate: [0, 5, 0],
              pathLength: [0, 1, 0]
            }}
            transition={{ 
              duration: 1.5, 
              repeat: Infinity, 
              repeatDelay: 7
            }}
            style={{ clipPath: "polygon(50% 0%, 15% 50%, 50% 50%, 0% 100%, 50% 70%, 50% 50%, 85% 100%)" }}
          />
        </div>
        
        {/* เฮลิกซ์พลังงานลึกลับ */}
        <div className="absolute top-[10%] left-[5%] w-20 h-80 origin-center">
          <motion.div
            className="absolute top-0 left-0 right-0 h-6 rounded-full bg-purple-500/30 blur-sm"
            animate={{ y: [0, 80, 0], opacity: [0.2, 0.8, 0.2] }}
            transition={{ duration: 5, repeat: Infinity, ease: "easeInOut" }}
          />
          <motion.div
            className="absolute top-20 left-0 right-0 h-6 rounded-full bg-indigo-500/30 blur-sm"
            animate={{ y: [80, 0, 80], opacity: [0.2, 0.8, 0.2] }}
            transition={{ duration: 5, repeat: Infinity, ease: "easeInOut", delay: 1 }}
          />
        </div>
        
        {/* ฟ้าแลบเมื่อเปลี่ยนแท็บ */}
        <AnimatePresence>
          {lightningEffect && (
            <motion.div 
              initial={{ opacity: 0 }}
              animate={{ opacity: 0.7 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="absolute inset-0 bg-white pointer-events-none"
            />
          )}
        </AnimatePresence>
      </div>
      
      <Navbar />

      <main className="relative z-10 flex-1 flex flex-col items-center justify-center p-4 mt-14">
        {/* ส่วนหัว */}
        <motion.div 
          className="text-center mb-8 relative"
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ type: "spring", stiffness: 200, damping: 20 }}
        >
          <div className="relative inline-block">
            <motion.div 
              className="absolute -inset-4 rounded-full bg-gradient-to-r from-amber-600/30 via-pink-500/30 to-purple-600/30 blur-2xl"
              animate={{ 
                scale: [1, 1.1, 1],
                rotate: [0, 5, 0]
              }}
              transition={{ 
                duration: 5, 
                repeat: Infinity,
                repeatType: "reverse"
              }}
            />
            <h1 className="text-6xl font-bold bg-gradient-to-r from-amber-300 via-yellow-200 to-amber-400 bg-clip-text text-transparent font-serif relative">
              SLIPKUY
              <motion.div 
                className="absolute -right-8 -top-8 text-amber-300"
                animate={{ rotate: 360 }}
                transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
              >
                <Crown className="h-10 w-10" />
              </motion.div>
            </h1>
          </div>
          <p className="mt-2 text-xl text-fuchsia-200 font-semibold">ระบบตรวจสอบสลิปแห่งเทพเจ้า</p>
          
          {/* คำคมกวนๆ */}
          <motion.div 
            className="mt-4 max-w-xl mx-auto bg-indigo-950/50 p-3 rounded-lg border border-indigo-800/50 relative overflow-hidden group"
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 via-fuchsia-600/5 to-amber-600/10 group-hover:opacity-80 transition-opacity" />
            <Laugh className="h-5 w-5 text-amber-300 inline-block mr-2 mb-1" />
            <span className="italic text-indigo-200">{quote}</span>
            <motion.div
              className="absolute bottom-0 left-0 h-[2px] bg-gradient-to-r from-amber-400 via-fuchsia-500 to-purple-500"
              initial={{ width: "0%" }}
              animate={{ width: "100%" }}
              transition={{ duration: 3 }}
            />
          </motion.div>
        </motion.div>
        
        {/* ส่วนหลักสำหรับล็อกอิน/ลงทะเบียน */}
        <div className="flex-1 w-full lg:w-3/5">
                <Card className="bg-purple-950/50 border-purple-800 backdrop-blur-sm shadow-2xl">
                  <CardHeader className="space-y-1">
                    <CardTitle className="text-3xl font-bold bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 bg-clip-text text-transparent">
                      {activeTab === "login" 
                        ? "เข้าสู่ระบบ SLIPKUY" 
                        : activeTab === "register" 
                          ? "สมัครสมาชิก SLIPKUY" 
                          : activeTab === "verify" 
                            ? "ยืนยันบัญชีของคุณ"
                            : "ลืมรหัสผ่าน"}
                    </CardTitle>
                    <CardDescription className="text-purple-300">
                      {activeTab === "login" 
                        ? "กรอกข้อมูลเพื่อเข้าสู่ระบบ" 
                        : activeTab === "register" 
                          ? "สร้างบัญชีใหม่เพื่อใช้งานระบบ" 
                          : activeTab === "verify"
                            ? "ยืนยันบัญชีของคุณเพื่อเข้าใช้งานระบบเต็มรูปแบบ"
                            : "กรอกข้อมูลเพื่อรีเซ็ตรหัสผ่านของคุณ"}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {/* แท็บสำหรับเลือกระหว่างล็อกอินและลงทะเบียน */}
                    {!user && (
                      <Tabs
                        defaultValue="login"
                        value={activeTab}
                        onValueChange={(value) => setActiveTab(value as any)}
                        className="w-full mb-6"
                      >
                        <TabsList className="grid w-full grid-cols-2 bg-purple-900/50">
                          <TabsTrigger 
                            value="login" 
                            className="text-purple-300 data-[state=active]:bg-gradient-to-r data-[state=active]:from-pink-600/80 data-[state=active]:to-purple-600/80 data-[state=active]:text-white"
                          >
                            เข้าสู่ระบบ
                          </TabsTrigger>
                          <TabsTrigger 
                            value="register" 
                            className="text-purple-300 data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-600/80 data-[state=active]:to-indigo-600/80 data-[state=active]:text-white"
                          >
                            สมัครสมาชิก
                          </TabsTrigger>
                        </TabsList>
                      
                        {/* แท็บเข้าสู่ระบบ */}
                        <TabsContent value="login" className="mt-4">
                          <Form {...loginForm}>
                            <form onSubmit={loginForm.handleSubmit(onLoginSubmit)} className="space-y-4">
                              <FormField
                                control={loginForm.control}
                                name="username"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel className="text-white">ชื่อผู้ใช้</FormLabel>
                                    <FormControl>
                                      <div className="relative">
                                        <User className="absolute left-3 top-3 h-5 w-5 text-purple-400" />
                                        <Input
                                          placeholder="กรอกชื่อผู้ใช้"
                                          {...field}
                                          className="pl-10 bg-purple-900/30 border-purple-700 text-white"
                                        />
                                      </div>
                                    </FormControl>
                                    <FormMessage className="text-red-300" />
                                  </FormItem>
                                )}
                              />
                              <FormField
                                control={loginForm.control}
                                name="password"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel className="text-white">รหัสผ่าน</FormLabel>
                                    <FormControl>
                                      <div className="relative">
                                        <Lock className="absolute left-3 top-3 h-5 w-5 text-purple-400" />
                                        <Input
                                          type="password"
                                          placeholder="กรอกรหัสผ่าน"
                                          {...field}
                                          className="pl-10 bg-purple-900/30 border-purple-700 text-white"
                                        />
                                      </div>
                                    </FormControl>
                                    <FormMessage className="text-red-300" />
                                  </FormItem>
                                )}
                              />
                              <Button 
                                type="submit"
                                className="w-full mt-4 bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 font-semibold"
                                disabled={loginMutation.isPending}
                              >
                                {loginMutation.isPending ? (
                                  <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    กำลังเข้าสู่ระบบ...
                                  </>
                                ) : (
                                  "เข้าสู่ระบบ"
                                )}
                              </Button>
                              
                              <div className="text-sm text-center mt-4 text-purple-300">
                                <span>ลืมรหัสผ่าน? </span>
                                <button 
                                  type="button" 
                                  onClick={() => setLocation("/password-reset")}
                                  className="text-pink-400 hover:underline"
                                >
                                  คลิกที่นี่
                                </button>
                              </div>
                            </form>
                          </Form>
                        </TabsContent>
                      
                        {/* แท็บสมัครสมาชิก */}
                        <TabsContent value="register" className="mt-4">
                          {registrationStep === "form" ? (
                            <Form {...registerForm}>
                              <form onSubmit={registerForm.handleSubmit(onRegisterSubmit)} className="space-y-4">
                                {/* ชื่อผู้ใช้ */}
                                <FormField
                                  control={registerForm.control}
                                  name="username"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel className="text-white">ชื่อผู้ใช้ <span className="text-red-400">*</span></FormLabel>
                                      <FormControl>
                                        <div className="relative">
                                          <User className="absolute left-3 top-3 h-5 w-5 text-purple-400" />
                                          <Input
                                            placeholder="กรอกชื่อผู้ใช้"
                                            {...field}
                                            className="pl-10 bg-purple-900/30 border-purple-700 text-white"
                                          />
                                        </div>
                                      </FormControl>
                                      <FormMessage className="text-red-300" />
                                    </FormItem>
                                  )}
                                />
                                
                                {/* อีเมล */}
                                <FormField
                                  control={registerForm.control}
                                  name="email"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel className="text-white">อีเมล <span className="text-red-400">*</span></FormLabel>
                                      <FormControl>
                                        <div className="relative">
                                          <Mail className="absolute left-3 top-3 h-5 w-5 text-purple-400" />
                                          <Input
                                            type="email"
                                            placeholder="กรอกอีเมล"
                                            {...field}
                                            className="pl-10 bg-purple-900/30 border-purple-700 text-white"
                                          />
                                        </div>
                                      </FormControl>
                                      <FormMessage className="text-red-300" />
                                    </FormItem>
                                  )}
                                />
                                
                                {/* เบอร์โทรศัพท์ */}
                                <FormField
                                  control={registerForm.control}
                                  name="phoneNumber"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel className="text-white">เบอร์โทรศัพท์ <span className="text-red-400">*</span></FormLabel>
                                      <FormControl>
                                        <div className="relative">
                                          <Phone className="absolute left-3 top-3 h-5 w-5 text-purple-400" />
                                          <Input
                                            type="tel"
                                            placeholder="กรอกเบอร์โทรศัพท์"
                                            {...field}
                                            className="pl-10 bg-purple-900/30 border-purple-700 text-white"
                                          />
                                        </div>
                                      </FormControl>
                                      <FormMessage className="text-red-300" />
                                    </FormItem>
                                  )}
                                />
                                
                                {/* ชื่อ */}
                                <FormField
                                  control={registerForm.control}
                                  name="firstName"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel className="text-white">ชื่อ</FormLabel>
                                      <FormControl>
                                        <Input
                                          placeholder="กรอกชื่อ"
                                          {...field}
                                          className="bg-purple-900/30 border-purple-700 text-white"
                                        />
                                      </FormControl>
                                      <FormMessage className="text-red-300" />
                                    </FormItem>
                                  )}
                                />
                                
                                {/* นามสกุล */}
                                <FormField
                                  control={registerForm.control}
                                  name="lastName"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel className="text-white">นามสกุล</FormLabel>
                                      <FormControl>
                                        <Input
                                          placeholder="กรอกนามสกุล"
                                          {...field}
                                          className="bg-purple-900/30 border-purple-700 text-white"
                                        />
                                      </FormControl>
                                      <FormMessage className="text-red-300" />
                                    </FormItem>
                                  )}
                                />
                                
                                {/* ชื่อบริษัท */}
                                <FormField
                                  control={registerForm.control}
                                  name="companyName"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel className="text-white">ชื่อบริษัท</FormLabel>
                                      <FormControl>
                                        <div className="relative">
                                          <Building className="absolute left-3 top-3 h-5 w-5 text-purple-400" />
                                          <Input
                                            placeholder="กรอกชื่อบริษัท (ถ้ามี)"
                                            {...field}
                                            className="pl-10 bg-purple-900/30 border-purple-700 text-white"
                                          />
                                        </div>
                                      </FormControl>
                                      <FormMessage className="text-red-300" />
                                    </FormItem>
                                  )}
                                />
                                
                                {/* รหัสผ่าน */}
                                <FormField
                                  control={registerForm.control}
                                  name="password"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel className="text-white">รหัสผ่าน <span className="text-red-400">*</span></FormLabel>
                                      <FormControl>
                                        <div className="relative">
                                          <Lock className="absolute left-3 top-3 h-5 w-5 text-purple-400" />
                                          <Input
                                            type="password"
                                            placeholder="กรอกรหัสผ่าน (อย่างน้อย 8 ตัวอักษร)"
                                            {...field}
                                            className="pl-10 bg-purple-900/30 border-purple-700 text-white"
                                          />
                                        </div>
                                      </FormControl>
                                      <FormMessage className="text-red-300" />
                                    </FormItem>
                                  )}
                                />
                                
                                {/* ยืนยันรหัสผ่าน */}
                                <FormField
                                  control={registerForm.control}
                                  name="confirmPassword"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel className="text-white">ยืนยันรหัสผ่าน <span className="text-red-400">*</span></FormLabel>
                                      <FormControl>
                                        <div className="relative">
                                          <Lock className="absolute left-3 top-3 h-5 w-5 text-purple-400" />
                                          <Input
                                            type="password"
                                            placeholder="กรอกรหัสผ่านอีกครั้ง"
                                            {...field}
                                            className="pl-10 bg-purple-900/30 border-purple-700 text-white"
                                          />
                                        </div>
                                      </FormControl>
                                      <FormMessage className="text-red-300" />
                                    </FormItem>
                                  )}
                                />
                                
                                {/* เลือกวิธีการยืนยัน */}
                                <FormField
                                  control={registerForm.control}
                                  name="verificationMethod"
                                  render={({ field }) => (
                                    <FormItem className="mt-6">
                                      <FormLabel className="text-white">เลือกวิธีการยืนยันตัวตน <span className="text-red-400">*</span></FormLabel>
                                      <FormControl>
                                        <RadioGroup
                                          onValueChange={field.onChange}
                                          value={field.value}
                                          className="flex flex-col gap-4 mt-2"
                                        >
                                          <div className="flex items-center space-x-2">
                                            <RadioGroupItem 
                                              value="email" 
                                              id="verification-email" 
                                              className="border-purple-500 text-purple-400" 
                                            />
                                            <label 
                                              htmlFor="verification-email" 
                                              className="text-white cursor-pointer flex items-center gap-2">
                                              <Mail className="h-4 w-4 text-purple-400" />
                                              ยืนยันด้วยอีเมล
                                            </label>
                                          </div>
                                          <div className="flex items-center space-x-2">
                                            <RadioGroupItem 
                                              value="phone" 
                                              id="verification-phone" 
                                              className="border-purple-500 text-purple-400" 
                                            />
                                            <label 
                                              htmlFor="verification-phone" 
                                              className="text-white cursor-pointer flex items-center gap-2">
                                              <Phone className="h-4 w-4 text-purple-400" />
                                              ยืนยันด้วย SMS
                                            </label>
                                          </div>
                                        </RadioGroup>
                                      </FormControl>
                                      <FormMessage className="text-red-300" />
                                    </FormItem>
                                  )}
                                />
                                
                                <Button 
                                  type="submit"
                                  className="w-full mt-8 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 font-semibold"
                                >
                                  ลงทะเบียนและรับรหัสยืนยัน
                                </Button>
                              </form>
                            </Form>
                          ) : (
                            <div className="space-y-6">
                              <Alert className="bg-purple-800/50 border-purple-600 text-white">
                                <Check className="h-5 w-5 text-green-400" />
                                <AlertTitle className="text-white font-semibold">รหัสยืนยันถูกส่งแล้ว</AlertTitle>
                                <AlertDescription className="text-purple-200">
                                  {verificationMethod === "email" 
                                    ? `รหัสยืนยันถูกส่งไปยังอีเมล ${tempUserData?.email} แล้ว`
                                    : `รหัสยืนยันถูกส่งไปยังเบอร์โทรศัพท์ ${tempUserData?.phoneNumber} แล้ว`}
                                </AlertDescription>
                              </Alert>
                              
                              <div className="space-y-4">
                                <label className="text-white">รหัสยืนยัน</label>
                                <div className="flex gap-2">
                                  <Input
                                    type="text"
                                    placeholder="กรอกรหัสยืนยัน 6 หลัก"
                                    value={verificationCode}
                                    onChange={(e) => setVerificationCode(e.target.value)}
                                    className="flex-1 bg-purple-900/30 border-purple-700 text-white text-center tracking-[0.5em] text-xl"
                                    maxLength={6}
                                  />
                                </div>
                                {secondsLeft > 0 && (
                                  <p className="text-purple-300 text-sm text-right">
                                    ส่งรหัสใหม่ได้ในอีก {Math.floor(secondsLeft / 60)}:{(secondsLeft % 60).toString().padStart(2, '0')}
                                  </p>
                                )}
                              </div>
                              
                              <div className="flex flex-col sm:flex-row gap-3">
                                <Button 
                                  onClick={verifyCode}
                                  disabled={isVerifying || !verificationCode || verificationCode.length < 6}
                                  className="flex-1 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 font-semibold"
                                >
                                  {isVerifying ? (
                                    <>
                                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                      กำลังยืนยัน...
                                    </>
                                  ) : (
                                    "ยืนยันรหัส"
                                  )}
                                </Button>
                                <Button 
                                  onClick={resendVerificationCode}
                                  disabled={secondsLeft > 0}
                                  variant="outline"
                                  className="border-purple-600 text-purple-300 hover:bg-purple-700/30"
                                >
                                  ส่งรหัสใหม่
                                </Button>
                              </div>
                              
                              <div className="flex justify-center">
                                <Button 
                                  variant="link" 
                                  onClick={() => setRegistrationStep("form")}
                                  className="text-purple-300 hover:text-white"
                                >
                                  กลับไปแก้ไขข้อมูล
                                </Button>
                              </div>
                            </div>
                          )}
                        </TabsContent>
                        {/* แท็บลืมรหัสผ่าน */}
                        <TabsContent value="forgot-password" className="mt-4">
                          {forgotPasswordStep === "request" ? (
                            <Form {...forgotPasswordForm}>
                              <form onSubmit={forgotPasswordForm.handleSubmit(onForgotPasswordSubmit)} className="space-y-4">
                                {/* อีเมลหรือชื่อผู้ใช้ */}
                                <FormField
                                  control={forgotPasswordForm.control}
                                  name="identifier"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel className="text-white">อีเมลหรือเบอร์โทรศัพท์ <span className="text-red-400">*</span></FormLabel>
                                      <FormControl>
                                        <div className="relative">
                                          <Mail className="absolute left-3 top-3 h-5 w-5 text-purple-400" />
                                          <Input
                                            placeholder="กรอกอีเมลหรือเบอร์โทรศัพท์"
                                            {...field}
                                            className="pl-10 bg-purple-900/30 border-purple-700 text-white"
                                          />
                                        </div>
                                      </FormControl>
                                      <FormMessage className="text-red-300" />
                                    </FormItem>
                                  )}
                                />
                                
                                {/* เลือกวิธีการยืนยัน */}
                                <FormField
                                  control={forgotPasswordForm.control}
                                  name="verificationMethod"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel className="text-white">เลือกวิธีการยืนยัน <span className="text-red-400">*</span></FormLabel>
                                      <Select
                                        value={field.value}
                                        onValueChange={field.onChange}
                                      >
                                        <FormControl>
                                          <SelectTrigger className="bg-purple-900/30 border-purple-700 text-white">
                                            <SelectValue placeholder="เลือกวิธีการยืนยัน" />
                                          </SelectTrigger>
                                        </FormControl>
                                        <SelectContent className="bg-purple-900 border-purple-700 text-white">
                                          <SelectItem value="email">ยืนยันด้วยอีเมล</SelectItem>
                                          <SelectItem value="phone">ยืนยันด้วย SMS</SelectItem>
                                        </SelectContent>
                                      </Select>
                                      <FormDescription className="text-purple-300">
                                        เราจะส่งรหัสยืนยันไปยัง{field.value === "email" ? "อีเมล" : "เบอร์โทรศัพท์"}ของคุณ
                                      </FormDescription>
                                      <FormMessage className="text-red-300" />
                                    </FormItem>
                                  )}
                                />
                                
                                <Button 
                                  type="submit"
                                  className="w-full mt-4 bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 font-semibold"
                                >
                                  ขอรหัสยืนยัน
                                </Button>
                                
                                <div className="text-sm text-center mt-4 text-purple-300">
                                  <button 
                                    type="button" 
                                    onClick={() => setActiveTab("login")}
                                    className="text-pink-400 hover:underline"
                                  >
                                    กลับไปหน้าเข้าสู่ระบบ
                                  </button>
                                </div>
                              </form>
                            </Form>
                          ) : forgotPasswordStep === "verify" ? (
                            <div className="space-y-4">
                              <div className="bg-purple-900/50 p-4 rounded-lg border border-purple-700 mb-6">
                                <p className="text-purple-200 text-center mb-2">
                                  เราได้ส่งรหัสยืนยันไปยัง{resetPasswordData?.type === "email" ? "อีเมล" : "เบอร์โทรศัพท์"}ของคุณแล้ว
                                </p>
                                <p className="text-pink-300 text-center font-medium">
                                  กรุณาตรวจสอบและกรอกรหัสยืนยัน 6 หลัก
                                </p>
                              </div>
                              
                              <div className="space-y-4">
                                <Label htmlFor="verificationCode" className="text-white">รหัสยืนยัน</Label>
                                <Input
                                  id="verificationCode"
                                  className="bg-purple-900/30 border-purple-700 text-white text-center text-xl tracking-widest font-semibold"
                                  value={verificationCode}
                                  onChange={(e) => setVerificationCode(e.target.value)}
                                  maxLength={6}
                                  placeholder="000000"
                                />
                                <div className="flex justify-between items-center">
                                  <Button
                                    variant="link"
                                    type="button"
                                    className="text-sm text-purple-300 hover:text-purple-200 px-0"
                                    onClick={resendResetPasswordCode}
                                    disabled={secondsLeft > 0}
                                  >
                                    {secondsLeft > 0 
                                      ? `ขอรหัสใหม่ได้ใน ${secondsLeft} วินาที` 
                                      : "ส่งรหัสยืนยันใหม่"}
                                  </Button>
                                  <Button
                                    type="button"
                                    className="bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700"
                                    onClick={verifyResetPasswordCode}
                                    disabled={isVerifying}
                                  >
                                    {isVerifying ? (
                                      <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        กำลังตรวจสอบ...
                                      </>
                                    ) : (
                                      "ยืนยันรหัส"
                                    )}
                                  </Button>
                                </div>
                                
                                <div className="text-sm text-center mt-6 text-purple-300">
                                  <button 
                                    type="button" 
                                    onClick={() => {
                                      setForgotPasswordStep("form");
                                      setVerificationCode("");
                                    }}
                                    className="text-pink-400 hover:underline"
                                  >
                                    กลับไปแก้ไขข้อมูล
                                  </button>
                                </div>
                              </div>
                            </div>
                          ) : (
                            <Form {...resetPasswordForm}>
                              <form onSubmit={resetPasswordForm.handleSubmit(onResetPasswordSubmit)} className="space-y-4">
                                {/* รหัสยืนยัน - ปิดช่องกรอกเนื่องจากได้ยืนยันรหัสไปแล้ว */}
                                <div className="space-y-2">
                                  <div className="flex items-center justify-between">
                                    <div className="font-medium text-white">รหัสยืนยัน</div>
                                    <Badge variant="outline" className="bg-green-900/50 text-green-300 border-green-500">
                                      ยืนยันแล้ว
                                    </Badge>
                                  </div>
                                  <div className="text-purple-300 text-sm">
                                    รหัสยืนยันถูกต้อง ท่านสามารถตั้งรหัสผ่านใหม่ได้
                                  </div>
                                </div>
                                
                                {/* รหัสผ่านใหม่ */}
                                <FormField
                                  control={resetPasswordForm.control}
                                  name="newPassword"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel className="text-white">รหัสผ่านใหม่ <span className="text-red-400">*</span></FormLabel>
                                      <FormControl>
                                        <div className="relative">
                                          <Lock className="absolute left-3 top-3 h-5 w-5 text-purple-400" />
                                          <Input
                                            type="password"
                                            placeholder="กรอกรหัสผ่านใหม่ (อย่างน้อย 8 ตัวอักษร)"
                                            {...field}
                                            className="pl-10 bg-purple-900/30 border-purple-700 text-white"
                                          />
                                        </div>
                                      </FormControl>
                                      <FormMessage className="text-red-300" />
                                    </FormItem>
                                  )}
                                />
                                
                                {/* ยืนยันรหัสผ่านใหม่ */}
                                <FormField
                                  control={resetPasswordForm.control}
                                  name="confirmPassword"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel className="text-white">ยืนยันรหัสผ่านใหม่ <span className="text-red-400">*</span></FormLabel>
                                      <FormControl>
                                        <div className="relative">
                                          <Lock className="absolute left-3 top-3 h-5 w-5 text-purple-400" />
                                          <Input
                                            type="password"
                                            placeholder="กรอกรหัสผ่านใหม่อีกครั้ง"
                                            {...field}
                                            className="pl-10 bg-purple-900/30 border-purple-700 text-white"
                                          />
                                        </div>
                                      </FormControl>
                                      <FormMessage className="text-red-300" />
                                    </FormItem>
                                  )}
                                />
                                
                                <Button 
                                  type="submit"
                                  className="w-full mt-4 bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 font-semibold"
                                  disabled={isResettingPassword}
                                >
                                  {isResettingPassword ? (
                                    <>
                                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                      กำลังบันทึกรหัสผ่านใหม่...
                                    </>
                                  ) : (
                                    "บันทึกรหัสผ่านใหม่"
                                  )}
                                </Button>
                                
                                <div className="text-sm text-center mt-4 text-purple-300">
                                  <button 
                                    type="button" 
                                    onClick={() => {
                                      setForgotPasswordStep("verify");
                                    }}
                                    className="text-pink-400 hover:underline"
                                  >
                                    กลับไปยืนยันรหัส
                                  </button>
                                </div>
                              </form>
                            </Form>
                          )}
                        </TabsContent>
                      </Tabs>
                    )}
                      
                    {/* ยืนยันบัญชี (แสดงเมื่อล็อกอินแล้วแต่ยังไม่ได้ยืนยัน) */}
                    {user && activeTab === "verify" && (
                      <div className="space-y-6">
                        <Alert className="bg-purple-800/50 border-yellow-500 text-white">
                          <ExternalLink className="h-5 w-5 text-yellow-400" />
                          <AlertTitle className="text-white font-semibold">ต้องยืนยันตัวตนก่อนใช้งาน</AlertTitle>
                          <AlertDescription className="text-purple-200">
                            คุณต้องยืนยันตัวตนผ่านอีเมลหรือเบอร์โทรศัพท์ก่อนใช้งานระบบ
                          </AlertDescription>
                        </Alert>
                        
                        <div className="space-y-4">
                          {/* สถานะการยืนยัน */}
                          <div className="space-y-4">
                            <div className="bg-purple-900/30 border border-purple-700 rounded-lg p-4">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center">
                                  <div className="bg-purple-800 rounded-full p-2 mr-3">
                                    <Mail className="h-5 w-5 text-purple-200" />
                                  </div>
                                  <div>
                                    <div className="font-medium text-white">{user?.email}</div>
                                    <div className="text-sm text-purple-300">อีเมล</div>
                                  </div>
                                </div>
                                <div>
                                  {userVerificationStatus.email_verified ? (
                                    <Badge className="bg-green-600">ยืนยันแล้ว</Badge>
                                  ) : (
                                    <Button 
                                      size="sm" 
                                      variant="outline"
                                      className="border-purple-500 text-purple-200 hover:bg-purple-700"
                                      onClick={() => {
                                        setVerificationMethod("email");
                                        resendVerificationCode();
                                      }}
                                    >
                                      ยืนยันตอนนี้
                                    </Button>
                                  )}
                                </div>
                              </div>
                            </div>
                            
                            <div className="bg-purple-900/30 border border-purple-700 rounded-lg p-4">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center">
                                  <div className="bg-purple-800 rounded-full p-2 mr-3">
                                    <Phone className="h-5 w-5 text-purple-200" />
                                  </div>
                                  <div>
                                    <div className="font-medium text-white">{user?.phoneNumber || "ไม่ได้ระบุ"}</div>
                                    <div className="text-sm text-purple-300">เบอร์โทรศัพท์</div>
                                  </div>
                                </div>
                                <div>
                                  {userVerificationStatus.phone_verified ? (
                                    <Badge className="bg-green-600">ยืนยันแล้ว</Badge>
                                  ) : user?.phoneNumber ? (
                                    <Button 
                                      size="sm" 
                                      variant="outline"
                                      className="border-purple-500 text-purple-200 hover:bg-purple-700"
                                      onClick={() => {
                                        setVerificationMethod("phone");
                                        resendVerificationCode();
                                      }}
                                    >
                                      ยืนยันตอนนี้
                                    </Button>
                                  ) : (
                                    <Badge variant="outline" className="border-yellow-500 text-yellow-400">
                                      ไม่มีข้อมูล
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                          
                          {/* ฟอร์มสำหรับกรอกรหัสยืนยัน */}
                          <div className="pt-6 space-y-4">
                            <div className="text-lg font-medium text-white">
                              {verificationMethod === "email" ? "ยืนยันด้วยอีเมล" : "ยืนยันด้วย SMS"}
                            </div>
                            
                            <div className="space-y-4">
                              <label className="text-white">รหัสยืนยัน</label>
                              <div className="flex gap-2">
                                <Input
                                  type="text"
                                  placeholder="กรอกรหัสยืนยัน 6 หลัก"
                                  value={verificationCode}
                                  onChange={(e) => setVerificationCode(e.target.value)}
                                  className="flex-1 bg-purple-900/30 border-purple-700 text-white text-center tracking-[0.5em] text-xl"
                                  maxLength={6}
                                />
                              </div>
                              {secondsLeft > 0 && (
                                <p className="text-purple-300 text-sm text-right">
                                  ส่งรหัสใหม่ได้ในอีก {Math.floor(secondsLeft / 60)}:{(secondsLeft % 60).toString().padStart(2, '0')}
                                </p>
                              )}
                            </div>
                            
                            <div className="flex flex-col sm:flex-row gap-3">
                              <Button 
                                onClick={verifyExistingUser}
                                disabled={isVerifying || !verificationCode || verificationCode.length < 6}
                                className="flex-1 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 font-semibold"
                              >
                                {isVerifying ? (
                                  <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    กำลังยืนยัน...
                                  </>
                                ) : (
                                  "ยืนยันรหัส"
                                )}
                              </Button>
                              <Button 
                                onClick={resendVerificationCode}
                                disabled={secondsLeft > 0}
                                variant="outline"
                                className="border-purple-600 text-purple-300 hover:bg-purple-700/30"
                              >
                                ส่งรหัสใหม่
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
              
              {/* ส่วนแสดงรายละเอียดบริการ */}
              <div className="w-full lg:w-2/5">
                <Card className="bg-purple-950/50 border-purple-800 backdrop-blur-sm shadow-2xl overflow-hidden">
                  <div className="h-24 bg-gradient-to-r from-purple-800 to-indigo-800 flex items-center justify-center">
                    <div className="flex items-center space-x-2">
                      <ReceiptText className="h-8 w-8 text-white" />
                      <h3 className="text-2xl font-bold text-white">SLIPKUY</h3>
                    </div>
                  </div>
                  <CardContent className="pt-6 pb-4">
                    <div className="space-y-4">
                      <h3 className="text-xl font-bold text-white">ยินดีต้อนรับสู่ SLIPKUY</h3>
                      <p className="text-purple-300">
                        ระบบตรวจสอบสลิปธนาคารอัจฉริยะ ที่ช่วยให้คุณตรวจสอบการโอนเงินได้อย่างสะดวก รวดเร็ว และปลอดภัย
                      </p>
                      
                      <div className="p-4 mt-4 border border-purple-700 rounded-lg bg-purple-900/30">
                        <h4 className="text-lg font-semibold text-white mb-3">ฟีเจอร์หลัก</h4>
                        <ul className="space-y-2">
                          <li className="flex items-start space-x-2 text-purple-300">
                            <Check className="h-5 w-5 text-green-400 flex-shrink-0 mt-0.5" />
                            <span>ตรวจสอบสลิปธนาคารอัตโนมัติด้วย AI</span>
                          </li>
                          <li className="flex items-start space-x-2 text-purple-300">
                            <Check className="h-5 w-5 text-green-400 flex-shrink-0 mt-0.5" />
                            <span>ค้นหาประวัติการตรวจสอบย้อนหลัง</span>
                          </li>
                          <li className="flex items-start space-x-2 text-purple-300">
                            <Check className="h-5 w-5 text-green-400 flex-shrink-0 mt-0.5" />
                            <span>ระบบ API สำหรับนักพัฒนา</span>
                          </li>
                          <li className="flex items-start space-x-2 text-purple-300">
                            <Check className="h-5 w-5 text-green-400 flex-shrink-0 mt-0.5" />
                            <span>รองรับทุกธนาคารในประเทศไทย</span>
                          </li>
                          <li className="flex items-start space-x-2 text-purple-300">
                            <Check className="h-5 w-5 text-green-400 flex-shrink-0 mt-0.5" />
                            <span>แดชบอร์ดแสดงสถิติและรายงาน</span>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="pt-0 pb-6">
                    <div className="w-full text-center">
                      <p className="text-sm text-purple-400 mb-2">
                        สงวนลิขสิทธิ์ให้เทพพระเจ้า © {new Date().getFullYear()} สลีปคุย (SLIPKUY)
                      </p>
                    </div>
                  </CardFooter>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}