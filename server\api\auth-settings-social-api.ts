import { Request, Response, Router } from "express";
import { socialAuthService } from "../social-auth-service";
import { logger } from "../logger";

const router = Router();

/**
 * เรียกดูการตั้งค่า Social Login ปัจจุบันสำหรับการแสดงปุ่มล็อกอิน
 * ในหน้าล็อกอินของผู้ใช้
 */
router.get('/settings/social', async (req: Request, res: Response) => {
  try {
    const settings = await socialAuthService.getSocialLoginSettings();
    
    // ส่งเฉพาะค่าที่จำเป็นสำหรับหน้าล็อกอิน
    res.json({
      enableLineLogin: settings.enableLineLogin,
      enableFacebookLogin: settings.enableFacebookLogin,
      enableGoogleLogin: settings.enableGoogleLogin
    });
  } catch (error) {
    logger.error('เกิดข้อผิดพลาดในการดึงการตั้งค่า Social Login', error);
    res.status(500).json({
      error: 'เกิดข้อผิดพลาดในการดึงการตั้งค่า'
    });
  }
});

export const authSettingsSocialApi = router;