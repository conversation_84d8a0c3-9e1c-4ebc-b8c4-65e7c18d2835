import { Router } from "express";
import { storage } from "../storage";
import { z } from "zod";
import { verificationCodes, users } from "@shared/schema";
import { emailService } from "../email-service";
import { smsService } from "../sms-service";
import { randomBytes, scrypt } from "crypto";
import { promisify } from "util";
import { db } from "../db";
import { and, eq, gte } from "drizzle-orm";

const scryptAsync = promisify(scrypt);

// ฟังก์ชันเข้ารหัสรหัสผ่าน
async function hashPassword(password: string) {
  const salt = randomBytes(16).toString("hex");
  const buf = await scryptAsync(password, salt, 64) as Buffer;
  return `${buf.toString("hex")}.${salt}`;
}

// ฟังก์ชันสร้างรหัสสุ่ม
function generateRandomCode(length: number = 6): string {
  return Math.floor(Math.random() * Math.pow(10, length)).toString().padStart(length, '0');
}

// ฟังก์ชันปกปิดเบอร์โทรศัพท์
function maskedPhone(phone: string): string {
  if (!phone) return "";
  const len = phone.length;
  if (len <= 4) return phone;
  return `${phone.slice(0, 3)}****${phone.slice(len - 3)}`;
}

const router = Router();

// Schema สำหรับตรวจสอบคำขอรีเซ็ตรหัสผ่าน
const requestResetSchema = z.object({
  identifier: z.string().min(3, "กรุณาระบุชื่อผู้ใช้ อีเมล หรือเบอร์โทรศัพท์"),
  method: z.enum(["email", "phone"])
});

// Schema สำหรับตรวจสอบการยืนยันรหัส
const verifyCodeSchema = z.object({
  identifier: z.string().min(3, "กรุณาระบุชื่อผู้ใช้ อีเมล หรือเบอร์โทรศัพท์"),
  code: z.string().min(4, "รหัสยืนยันไม่ถูกต้อง")
});

// Schema สำหรับตั้งรหัสผ่านใหม่
const resetPasswordSchema = z.object({
  identifier: z.string().min(3, "กรุณาระบุชื่อผู้ใช้ อีเมล หรือเบอร์โทรศัพท์"),
  code: z.string().min(4, "รหัสยืนยันไม่ถูกต้อง"),
  newPassword: z.string().min(8, "รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร"),
  confirmPassword: z.string().min(8, "ยืนยันรหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร")
}).refine(data => data.newPassword === data.confirmPassword, {
  message: "รหัสผ่านและยืนยันรหัสผ่านไม่ตรงกัน",
  path: ["confirmPassword"]
});

/**
 * ขอรีเซ็ตรหัสผ่าน (ส่งรหัสยืนยันทางอีเมลหรือ SMS)
 */
router.post("/request", async (req, res) => {
  try {
    // ตรวจสอบข้อมูลที่ส่งมา
    const validatedData = requestResetSchema.parse(req.body);
    const { identifier, method } = validatedData;

    // ค้นหาผู้ใช้จากตัวระบุ (identifier)
    const user = await storage.getUserByIdentifier(identifier);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "ไม่พบบัญชีผู้ใช้นี้ในระบบ"
      });
    }

    // สร้างรหัสยืนยัน 6 หลัก
    const verificationCode = generateRandomCode(6);
    
    // คำนวณเวลาหมดอายุ (10 นาที)
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + 10);

    // บันทึกรหัสยืนยันลงในฐานข้อมูล
    await storage.saveVerificationCode({
      userId: user.id,
      code: verificationCode,
      type: "password_reset",
      identifier: identifier,
      expiresAt: expiresAt,
      isUsed: false,
      attempts: 0
    });

    // ส่งรหัสยืนยันไปยังอีเมลหรือเบอร์โทร
    if (method === "email") {
      if (!user.email) {
        return res.status(400).json({
          success: false,
          message: "บัญชีนี้ไม่มีอีเมล โปรดเลือกวิธีอื่น"
        });
      }
      
      // ส่งรหัสผ่านทางอีเมล
      const emailSent = await emailService.sendPasswordResetEmail(user.email, {
        username: user.username,
        code: verificationCode
      });
      
      if (!emailSent) {
        return res.status(500).json({
          success: false,
          message: "ไม่สามารถส่งอีเมลได้ โปรดลองอีกครั้งในภายหลัง"
        });
      }
      
      return res.status(200).json({
        success: true,
        message: `ส่งรหัสยืนยันไปยังอีเมล ${user.email.substring(0, 3)}***${user.email.substring(user.email.indexOf('@'))} แล้ว`,
        expiresAt: expiresAt
      });
    } else if (method === "phone") {
      if (!user.phoneNumber) {
        return res.status(400).json({
          success: false,
          message: "บัญชีนี้ไม่มีเบอร์โทรศัพท์ โปรดเลือกวิธีอื่น"
        });
      }
      
      // TODO: ส่ง SMS (จะพัฒนาในอนาคต)
      // จะใช้ smsService.sendSMS ซึ่งยังไม่ได้พัฒนา
      
      return res.status(200).json({
        success: true,
        message: `ส่งรหัสยืนยันไปยังเบอร์โทรศัพท์ ${maskedPhone(user.phoneNumber)} แล้ว`,
        expiresAt: expiresAt
      });
    } else {
      return res.status(400).json({
        success: false,
        message: "วิธีการยืนยันไม่ถูกต้อง"
      });
    }
  } catch (error) {
    console.error("เกิดข้อผิดพลาดในการขอรีเซ็ตรหัสผ่าน:", error);
    return res.status(400).json({
      success: false,
      message: error instanceof z.ZodError 
        ? error.errors[0].message 
        : "เกิดข้อผิดพลาดในการขอรีเซ็ตรหัสผ่าน"
    });
  }
});

/**
 * ตรวจสอบรหัสยืนยัน
 */
router.post("/verify-code", async (req, res) => {
  try {
    // ตรวจสอบข้อมูลที่ส่งมา
    const validatedData = verifyCodeSchema.parse(req.body);
    const { identifier, code } = validatedData;

    // ค้นหาผู้ใช้จากตัวระบุ (identifier)
    const user = await storage.getUserByIdentifier(identifier);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "ไม่พบบัญชีผู้ใช้นี้ในระบบ"
      });
    }

    // ตรวจสอบรหัสยืนยันในฐานข้อมูล
    const now = new Date();
    const [verificationResult] = await db
      .select()
      .from(verificationCodes)
      .where(
        and(
          eq(verificationCodes.userId, user.id),
          eq(verificationCodes.code, code),
          eq(verificationCodes.type, 'password_reset'),
          eq(verificationCodes.isUsed, false),
          gte(verificationCodes.expiresAt, now)
        )
      );
    
    if (!verificationResult) {
      return res.status(400).json({
        success: false,
        message: "รหัสยืนยันไม่ถูกต้องหรือหมดอายุแล้ว"
      });
    }

    // เพิ่มจำนวนครั้งที่ใช้งาน
    await db
      .update(verificationCodes)
      .set({
        attempts: verificationResult.attempts + 1
      })
      .where(eq(verificationCodes.id, verificationResult.id));

    return res.status(200).json({
      success: true,
      message: "รหัสยืนยันถูกต้อง"
    });
  } catch (error) {
    console.error("เกิดข้อผิดพลาดในการตรวจสอบรหัสยืนยัน:", error);
    return res.status(400).json({
      success: false,
      message: error instanceof z.ZodError 
        ? error.errors[0].message 
        : "เกิดข้อผิดพลาดในการตรวจสอบรหัสยืนยัน"
    });
  }
});

/**
 * ตั้งรหัสผ่านใหม่
 */
router.post("/reset", async (req, res) => {
  try {
    // ตรวจสอบข้อมูลที่ส่งมา
    const validatedData = resetPasswordSchema.parse(req.body);
    const { identifier, code, newPassword } = validatedData;

    // ค้นหาผู้ใช้จากตัวระบุ (identifier)
    const user = await storage.getUserByIdentifier(identifier);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "ไม่พบบัญชีผู้ใช้นี้ในระบบ"
      });
    }

    // ตรวจสอบรหัสยืนยันในฐานข้อมูล
    const [verificationCode] = await db.select()
      .from(verificationCodes)
      .where(
        and(
          eq(verificationCodes.userId, user.id),
          eq(verificationCodes.code, code),
          eq(verificationCodes.type, 'password_reset'),
          eq(verificationCodes.isUsed, false),
          gte(verificationCodes.expiresAt, new Date())
        )
      );
    
    if (!verificationCode) {
      return res.status(400).json({
        success: false,
        message: "รหัสยืนยันไม่ถูกต้องหรือหมดอายุแล้ว"
      });
    }

    // เข้ารหัสรหัสผ่านใหม่
    const hashedPassword = await hashPassword(newPassword);

    // เปลี่ยนรหัสผ่าน
    const [updatedUser] = await db
      .update(users)
      .set({
        password: hashedPassword,
        updatedAt: new Date()
      })
      .where(eq(users.id, user.id))
      .returning();
    
    if (!updatedUser) {
      return res.status(500).json({
        success: false,
        message: "ไม่สามารถอัปเดตรหัสผ่านได้"
      });
    }

    // ทำเครื่องหมายว่ารหัสยืนยันถูกใช้งานแล้ว
    await db
      .update(verificationCodes)
      .set({
        isUsed: true,
        usedAt: new Date()
      })
      .where(
        and(
          eq(verificationCodes.userId, user.id),
          eq(verificationCodes.code, code),
          eq(verificationCodes.type, 'password_reset')
        )
      );

    return res.status(200).json({
      success: true,
      message: "เปลี่ยนรหัสผ่านเรียบร้อยแล้ว คุณสามารถเข้าสู่ระบบด้วยรหัสผ่านใหม่ได้ทันที"
    });
  } catch (error) {
    console.error("เกิดข้อผิดพลาดในการรีเซ็ตรหัสผ่าน:", error);
    return res.status(400).json({
      success: false,
      message: error instanceof z.ZodError 
        ? error.errors[0].message 
        : "เกิดข้อผิดพลาดในการรีเซ็ตรหัสผ่าน"
    });
  }
});

export const passwordResetRoutes = router;