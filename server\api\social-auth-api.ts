import { Request, Response, Router } from "express";
import axios from "axios";
import { v4 as uuidv4 } from 'uuid';
import { socialAuthService } from "../social-auth-service";
import { storage } from "../storage";
import { logger } from "../logger";
import { hashPassword } from "../auth";

// เพิ่มประเภทสำหรับ session เพื่อเพิ่ม oauthState
declare module 'express-session' {
  interface SessionData {
    oauthState?: string;
  }
}

const router = Router();

/**
 * API สำหรับการล็อกอินผ่านโซเชียลมีเดีย (LINE, Facebook, Google)
 */

// =========== LINE LOGIN ===========
router.get('/line/auth', async (req: Request, res: Response) => {
  try {
    const settings = await socialAuthService.getSocialLoginSettings();
    
    if (!settings.enableLineLogin) {
      return res.status(400).json({
        error: 'การล็อกอินด้วย LINE ถูกปิดใช้งานอยู่'
      });
    }
    
    if (!settings.lineClientId) {
      return res.status(500).json({
        error: 'ไม่พบการตั้งค่า LINE Client ID'
      });
    }
    
    // สร้าง state สำหรับป้องกัน CSRF
    const state = uuidv4();
    req.session.oauthState = state;
    
    // สร้าง URL สำหรับการล็อกอิน LINE
    const lineAuthUrl = `https://access.line.me/oauth2/v2.1/authorize?response_type=code&client_id=${settings.lineClientId}&redirect_uri=${encodeURIComponent(settings.lineCallbackUrl || `https://${req.hostname}/api/social/line/callback`)}&state=${state}&scope=profile%20openid%20email`;
    
    res.redirect(lineAuthUrl);
  } catch (error) {
    logger.error('เกิดข้อผิดพลาดในการล็อกอินด้วย LINE', error);
    res.status(500).json({
      error: 'เกิดข้อผิดพลาดในการล็อกอินด้วย LINE'
    });
  }
});

router.get('/line/callback', async (req: Request, res: Response) => {
  try {
    const { code, state, error, error_description } = req.query;
    
    // ตรวจสอบว่ามีข้อผิดพลาดหรือไม่
    if (error) {
      logger.error('LINE ส่งข้อผิดพลาดกลับมา:', { error, error_description });
      return res.redirect('/auth?error=line_error');
    }
    
    // ตรวจสอบว่า state ตรงกับที่ส่งไปหรือไม่
    if (!req.session.oauthState || req.session.oauthState !== state) {
      logger.error('CSRF state ไม่ตรงกัน:', { sessionState: req.session.oauthState, receivedState: state });
      return res.redirect('/auth?error=invalid_state');
    }
    
    // ดึงการตั้งค่า LINE Login
    const settings = await socialAuthService.getSocialLoginSettings();
    
    if (!settings.lineClientId || !settings.lineClientSecret) {
      return res.redirect('/auth?error=invalid_config');
    }
    
    // แลกโค้ดเป็น token
    const tokenResponse = await axios.post('https://api.line.me/oauth2/v2.1/token', 
      new URLSearchParams({
        grant_type: 'authorization_code',
        code: code as string,
        redirect_uri: settings.lineCallbackUrl || `https://${req.hostname}/api/social/line/callback`,
        client_id: settings.lineClientId,
        client_secret: settings.lineClientSecret
      }),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );
    
    const { access_token, id_token } = tokenResponse.data;
    
    // ดึงข้อมูลโปรไฟล์จาก LINE
    const profileResponse = await axios.get('https://api.line.me/v2/profile', {
      headers: {
        Authorization: `Bearer ${access_token}`
      }
    });
    
    const lineProfile = profileResponse.data;
    
    // ตรวจสอบว่ามีผู้ใช้ในระบบแล้วหรือไม่
    const existingUser = await socialAuthService.findUserByExternalAuth('line', lineProfile.userId);
    
    if (existingUser) {
      // ล็อกอินผู้ใช้ที่มีอยู่แล้ว
      await req.login(existingUser, async (err) => {
        if (err) {
          logger.error('เกิดข้อผิดพลาดในการล็อกอินด้วย LINE', err);
          return res.redirect('/auth?error=login_failed');
        }
        
        // อัปเดตข้อมูลการล็อกอิน
        await socialAuthService.saveExternalAuth(existingUser.id, 'line', lineProfile.userId, lineProfile);
        
        // บันทึกประวัติการล็อกอิน
        await storage.recordLoginSuccess(existingUser.id, 'line');
        
        res.redirect('/dashboard');
      });
    } else {
      // สร้างผู้ใช้ใหม่
      try {
        // สร้างผู้ใช้ใหม่โดยใช้ข้อมูลจาก LINE
        const username = `line_${lineProfile.userId.substring(0, 8)}`;
        const randomPassword = Math.random().toString(36).slice(-10);
        
        const newUser = await storage.createUser({
          username,
          password: await hashPassword(randomPassword),
          email: lineProfile.email || `${username}@line.example.com`,
          firstName: lineProfile.displayName || '',
          lastName: '',
          profileImage: lineProfile.pictureUrl || '',
          status: 'active' as any
        });
        
        // บันทึกข้อมูลการล็อกอินด้วย LINE
        await socialAuthService.saveExternalAuth(newUser.id, 'line', lineProfile.userId, lineProfile);
        
        // บันทึกประวัติการลงทะเบียน
        await storage.recordNewRegistration(newUser.id, 'line');
        
        // ล็อกอินผู้ใช้ใหม่
        await req.login(newUser, (err) => {
          if (err) {
            logger.error('เกิดข้อผิดพลาดในการล็อกอินผู้ใช้ใหม่ด้วย LINE', err);
            return res.redirect('/auth?error=login_failed');
          }
          
          res.redirect('/dashboard');
        });
      } catch (error) {
        logger.error('เกิดข้อผิดพลาดในการสร้างผู้ใช้ใหม่จาก LINE', error);
        res.redirect('/auth?error=registration_failed');
      }
    }
  } catch (error) {
    logger.error('เกิดข้อผิดพลาดในการประมวลผล LINE callback', error);
    res.redirect('/auth?error=callback_failed');
  }
});

// =========== FACEBOOK LOGIN ===========
router.get('/facebook/auth', async (req: Request, res: Response) => {
  try {
    const settings = await socialAuthService.getSocialLoginSettings();
    
    if (!settings.enableFacebookLogin) {
      return res.status(400).json({
        error: 'การล็อกอินด้วย Facebook ถูกปิดใช้งานอยู่'
      });
    }
    
    if (!settings.facebookAppId) {
      return res.status(500).json({
        error: 'ไม่พบการตั้งค่า Facebook App ID'
      });
    }
    
    // สร้าง state สำหรับป้องกัน CSRF
    const state = uuidv4();
    req.session.oauthState = state;
    
    // สร้าง URL สำหรับการล็อกอิน Facebook
    const fbAuthUrl = `https://www.facebook.com/v13.0/dialog/oauth?client_id=${settings.facebookAppId}&redirect_uri=${encodeURIComponent(settings.facebookCallbackUrl || `https://${req.hostname}/api/social/facebook/callback`)}&state=${state}&scope=email,public_profile`;
    
    res.redirect(fbAuthUrl);
  } catch (error) {
    logger.error('เกิดข้อผิดพลาดในการล็อกอินด้วย Facebook', error);
    res.status(500).json({
      error: 'เกิดข้อผิดพลาดในการล็อกอินด้วย Facebook'
    });
  }
});

router.get('/facebook/callback', async (req: Request, res: Response) => {
  try {
    const { code, state, error, error_reason, error_description } = req.query;
    
    // ตรวจสอบว่ามีข้อผิดพลาดหรือไม่
    if (error) {
      logger.error('Facebook ส่งข้อผิดพลาดกลับมา:', { error, error_reason, error_description });
      return res.redirect('/auth?error=facebook_error');
    }
    
    // ตรวจสอบว่า state ตรงกับที่ส่งไปหรือไม่
    if (!req.session.oauthState || req.session.oauthState !== state) {
      logger.error('CSRF state ไม่ตรงกัน:', { sessionState: req.session.oauthState, receivedState: state });
      return res.redirect('/auth?error=invalid_state');
    }
    
    // ดึงการตั้งค่า Facebook Login
    const settings = await socialAuthService.getSocialLoginSettings();
    
    if (!settings.facebookAppId || !settings.facebookAppSecret) {
      return res.redirect('/auth?error=invalid_config');
    }
    
    // แลกโค้ดเป็น token
    const tokenUrl = `https://graph.facebook.com/v13.0/oauth/access_token?client_id=${settings.facebookAppId}&redirect_uri=${encodeURIComponent(settings.facebookCallbackUrl || `https://${req.hostname}/api/social/facebook/callback`)}&client_secret=${settings.facebookAppSecret}&code=${code}`;
    
    const tokenResponse = await axios.get(tokenUrl);
    const { access_token } = tokenResponse.data;
    
    // ดึงข้อมูลโปรไฟล์จาก Facebook
    const profileResponse = await axios.get(`https://graph.facebook.com/me?fields=id,name,email,picture&access_token=${access_token}`);
    const fbProfile = profileResponse.data;
    
    // ตรวจสอบว่ามีผู้ใช้ในระบบแล้วหรือไม่
    const existingUser = await socialAuthService.findUserByExternalAuth('facebook', fbProfile.id);
    
    if (existingUser) {
      // ล็อกอินผู้ใช้ที่มีอยู่แล้ว
      await req.login(existingUser, async (err) => {
        if (err) {
          logger.error('เกิดข้อผิดพลาดในการล็อกอินด้วย Facebook', err);
          return res.redirect('/auth?error=login_failed');
        }
        
        // อัปเดตข้อมูลการล็อกอิน
        await socialAuthService.saveExternalAuth(existingUser.id, 'facebook', fbProfile.id, fbProfile);
        
        // บันทึกประวัติการล็อกอิน
        await storage.recordLoginSuccess(existingUser.id, 'facebook');
        
        res.redirect('/dashboard');
      });
    } else {
      // สร้างผู้ใช้ใหม่
      try {
        // สร้างผู้ใช้ใหม่โดยใช้ข้อมูลจาก Facebook
        const username = `fb_${fbProfile.id.substring(0, 8)}`;
        const randomPassword = Math.random().toString(36).slice(-10);
        
        // แยกชื่อและนามสกุล
        const nameParts = fbProfile.name.split(' ');
        const firstName = nameParts[0] || '';
        const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';
        
        const newUser = await storage.createUser({
          username,
          password: await hashPassword(randomPassword),
          email: fbProfile.email || `${username}@facebook.example.com`,
          firstName,
          lastName,
          profileImage: fbProfile.picture?.data?.url || '',
          status: 'active' as any
        });
        
        // บันทึกข้อมูลการล็อกอินด้วย Facebook
        await socialAuthService.saveExternalAuth(newUser.id, 'facebook', fbProfile.id, fbProfile);
        
        // บันทึกประวัติการลงทะเบียน
        await storage.recordNewRegistration(newUser.id, 'facebook');
        
        // ล็อกอินผู้ใช้ใหม่
        await req.login(newUser, (err) => {
          if (err) {
            logger.error('เกิดข้อผิดพลาดในการล็อกอินผู้ใช้ใหม่ด้วย Facebook', err);
            return res.redirect('/auth?error=login_failed');
          }
          
          res.redirect('/dashboard');
        });
      } catch (error) {
        logger.error('เกิดข้อผิดพลาดในการสร้างผู้ใช้ใหม่จาก Facebook', error);
        res.redirect('/auth?error=registration_failed');
      }
    }
  } catch (error) {
    logger.error('เกิดข้อผิดพลาดในการประมวลผล Facebook callback', error);
    res.redirect('/auth?error=callback_failed');
  }
});

// =========== GOOGLE LOGIN ===========
router.get('/google/auth', async (req: Request, res: Response) => {
  try {
    const settings = await socialAuthService.getSocialLoginSettings();
    
    if (!settings.enableGoogleLogin) {
      return res.status(400).json({
        error: 'การล็อกอินด้วย Google ถูกปิดใช้งานอยู่'
      });
    }
    
    if (!settings.googleClientId) {
      return res.status(500).json({
        error: 'ไม่พบการตั้งค่า Google Client ID'
      });
    }
    
    // สร้าง state สำหรับป้องกัน CSRF
    const state = uuidv4();
    req.session.oauthState = state;
    
    // สร้าง URL สำหรับการล็อกอิน Google
    const googleAuthUrl = `https://accounts.google.com/o/oauth2/v2/auth?client_id=${settings.googleClientId}&redirect_uri=${encodeURIComponent(settings.googleCallbackUrl || `https://${req.hostname}/api/social/google/callback`)}&response_type=code&scope=email%20profile&state=${state}`;
    
    res.redirect(googleAuthUrl);
  } catch (error) {
    logger.error('เกิดข้อผิดพลาดในการล็อกอินด้วย Google', error);
    res.status(500).json({
      error: 'เกิดข้อผิดพลาดในการล็อกอินด้วย Google'
    });
  }
});

router.get('/google/callback', async (req: Request, res: Response) => {
  try {
    const { code, state, error } = req.query;
    
    // ตรวจสอบว่ามีข้อผิดพลาดหรือไม่
    if (error) {
      logger.error('Google ส่งข้อผิดพลาดกลับมา:', { error });
      return res.redirect('/auth?error=google_error');
    }
    
    // ตรวจสอบว่า state ตรงกับที่ส่งไปหรือไม่
    if (!req.session.oauthState || req.session.oauthState !== state) {
      logger.error('CSRF state ไม่ตรงกัน:', { sessionState: req.session.oauthState, receivedState: state });
      return res.redirect('/auth?error=invalid_state');
    }
    
    // ดึงการตั้งค่า Google Login
    const settings = await socialAuthService.getSocialLoginSettings();
    
    if (!settings.googleClientId || !settings.googleClientSecret) {
      return res.redirect('/auth?error=invalid_config');
    }
    
    // แลกโค้ดเป็น token
    const tokenResponse = await axios.post('https://oauth2.googleapis.com/token', {
      client_id: settings.googleClientId,
      client_secret: settings.googleClientSecret,
      code: code as string,
      redirect_uri: settings.googleCallbackUrl || `https://${req.hostname}/api/social/google/callback`,
      grant_type: 'authorization_code'
    });
    
    const { access_token, id_token } = tokenResponse.data;
    
    // ดึงข้อมูลโปรไฟล์จาก Google
    const profileResponse = await axios.get('https://www.googleapis.com/oauth2/v3/userinfo', {
      headers: {
        Authorization: `Bearer ${access_token}`
      }
    });
    
    const googleProfile = profileResponse.data;
    
    // ตรวจสอบว่ามีผู้ใช้ในระบบแล้วหรือไม่
    const existingUser = await socialAuthService.findUserByExternalAuth('google', googleProfile.sub);
    
    if (existingUser) {
      // ล็อกอินผู้ใช้ที่มีอยู่แล้ว
      await req.login(existingUser, async (err) => {
        if (err) {
          logger.error('เกิดข้อผิดพลาดในการล็อกอินด้วย Google', err);
          return res.redirect('/auth?error=login_failed');
        }
        
        // อัปเดตข้อมูลการล็อกอิน
        await socialAuthService.saveExternalAuth(existingUser.id, 'google', googleProfile.sub, googleProfile);
        
        // บันทึกประวัติการล็อกอิน
        await storage.recordLoginSuccess(existingUser.id, 'google');
        
        res.redirect('/dashboard');
      });
    } else {
      // สร้างผู้ใช้ใหม่
      try {
        // สร้างผู้ใช้ใหม่โดยใช้ข้อมูลจาก Google
        const username = `g_${googleProfile.sub.substring(0, 8)}`;
        const randomPassword = Math.random().toString(36).slice(-10);
        
        const newUser = await storage.createUser({
          username,
          password: await hashPassword(randomPassword),
          email: googleProfile.email,
          firstName: googleProfile.given_name || '',
          lastName: googleProfile.family_name || '',
          profileImage: googleProfile.picture || '',
          status: 'active' as any
        });
        
        // บันทึกข้อมูลการล็อกอินด้วย Google
        await socialAuthService.saveExternalAuth(newUser.id, 'google', googleProfile.sub, googleProfile);
        
        // บันทึกประวัติการลงทะเบียน
        await storage.recordNewRegistration(newUser.id, 'google');
        
        // ล็อกอินผู้ใช้ใหม่
        await req.login(newUser, (err) => {
          if (err) {
            logger.error('เกิดข้อผิดพลาดในการล็อกอินผู้ใช้ใหม่ด้วย Google', err);
            return res.redirect('/auth?error=login_failed');
          }
          
          res.redirect('/dashboard');
        });
      } catch (error) {
        logger.error('เกิดข้อผิดพลาดในการสร้างผู้ใช้ใหม่จาก Google', error);
        res.redirect('/auth?error=registration_failed');
      }
    }
  } catch (error) {
    logger.error('เกิดข้อผิดพลาดในการประมวลผล Google callback', error);
    res.redirect('/auth?error=callback_failed');
  }
});

export const socialAuthApi = router;