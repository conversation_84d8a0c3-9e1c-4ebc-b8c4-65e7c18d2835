import nodemailer from 'nodemailer';
import hbs from 'nodemailer-express-handlebars';
import path from 'path';
import { systemSettings, emailTemplates, insertEmailLogSchema, emailLogs, users } from '@shared/schema';
import { db } from './db';
import { eq, and } from 'drizzle-orm';

// การกำหนดประเภทข้อมูลสำหรับการส่งอีเมล
type SendEmailOptions = {
  to: string;
  subject: string;
  templateName: string;
  variables: Record<string, any>;
};

export class EmailService {
  private transporter: nodemailer.Transporter | null = null;
  private emailConfig: any = null;
  private isConfigured: boolean = false;

  constructor() {
    this.initTransporter();
  }

  // กำหนดค่า transporter
  private async initTransporter() {
    try {
      // ดึงการตั้งค่าอีเมลจากฐานข้อมูลก่อน
      const settings = await db.select().from(systemSettings).where(eq(systemSettings.key, 'email_settings'));
      
      if (settings.length > 0 && settings[0].valueJson) {
        // ใช้ค่าจากฐานข้อมูล
        this.emailConfig = settings[0].valueJson;
        
        // ตรวจสอบว่ามีการตั้งค่าผู้ใช้และรหัสผ่านหรือไม่
        if (this.emailConfig.auth && this.emailConfig.auth.user && this.emailConfig.auth.pass) {
          // สร้าง transporter
          this.transporter = nodemailer.createTransport({
            service: this.emailConfig.service,
            host: this.emailConfig.host,
            port: this.emailConfig.port,
            secure: this.emailConfig.secure,
            auth: {
              user: this.emailConfig.auth.user,
              pass: this.emailConfig.auth.pass
            }
          });
          
          console.log('EmailService: ใช้ค่าจากฐานข้อมูล');
          this.isConfigured = true;
          
          // ทดสอบการเชื่อมต่อ
          try {
            await this.transporter.verify();
            console.log('EmailService: เชื่อมต่อ SMTP สำเร็จ');
          } catch (verifyError) {
            console.warn('EmailService: ไม่สามารถเชื่อมต่อกับ SMTP ในขณะนี้ - ทำงานในโหมดออฟไลน์:', verifyError.message);
            // ไม่ตั้งค่า isConfigured เป็น false เพื่อให้ระบบยังทำงานต่อไปได้ในโหมดจำลอง
            // this.isConfigured = false;
          }
          
          return;
        }
      }
      
      // หากไม่พบในฐานข้อมูลหรือข้อมูลไม่ครบถ้วน ใช้ค่าจาก environment variables แทน
      if (process.env.EMAIL_USER && process.env.EMAIL_PASS && process.env.EMAIL_FROM) {
        // ใช้ค่าจาก environment variables
        this.emailConfig = {
          service: 'gmail',
          host: 'smtp.gmail.com',
          port: 465,
          secure: true,
          auth: {
            user: process.env.EMAIL_USER,
            pass: process.env.EMAIL_PASS
          },
          from: process.env.EMAIL_FROM,
          defaultName: 'SLIPKUY Alert'
        };
        
        // สร้าง transporter
        this.transporter = nodemailer.createTransport({
          service: this.emailConfig.service,
          host: this.emailConfig.host,
          port: this.emailConfig.port,
          secure: this.emailConfig.secure,
          auth: {
            user: this.emailConfig.auth.user,
            pass: this.emailConfig.auth.pass
          }
        });
        
        console.log('EmailService: ใช้ค่าจาก environment variables (สำรอง)');
        this.isConfigured = true;
        
        // อัพเดตค่าเข้าฐานข้อมูลเพื่อให้ใช้ได้ในครั้งต่อไป
        try {
          await this.updateEmailConfig(this.emailConfig);
          console.log('EmailService: อัพเดตค่าเข้าฐานข้อมูลสำเร็จ');
        } catch (updateError) {
          console.error('EmailService: ไม่สามารถอัพเดตค่าเข้าฐานข้อมูลได้:', updateError);
        }
        
        return;
      }
      
      // หากไม่พบการตั้งค่าทั้งในฐานข้อมูลและ environment variables
      console.error('ไม่พบการตั้งค่าอีเมลในระบบ ทั้งในฐานข้อมูลและ environment variables');
      this.isConfigured = false;
      return;
      
      // ตั้งค่า template engine
      const handlebarOptions = {
        viewEngine: {
          extName: '.hbs',
          partialsDir: path.resolve('./email-templates/'),
          defaultLayout: false,
        },
        viewPath: path.resolve('./email-templates/'),
        extName: '.hbs',
      };
      
      this.transporter.use('compile', hbs(handlebarOptions));
      
      // ทดสอบการเชื่อมต่อ
      await this.transporter.verify();
      
      console.log('EmailService: เชื่อมต่อ SMTP สำเร็จ');
      this.isConfigured = true;
    } catch (error) {
      console.error('EmailService: ไม่สามารถเชื่อมต่อกับ SMTP ได้:', error);
      this.isConfigured = false;
    }
  }

  // อัปเดตการตั้งค่าอีเมล
  public async updateEmailConfig(newConfig: any) {
    try {
      // บันทึกการตั้งค่าใหม่ลงฐานข้อมูล
      await db.update(systemSettings)
        .set({ valueJson: newConfig })
        .where(eq(systemSettings.key, 'email_settings'));
      
      // อัปเดต transporter
      this.emailConfig = newConfig;
      
      // สร้าง transporter ใหม่
      await this.initTransporter();
      
      return this.isConfigured;
    } catch (error) {
      console.error('EmailService: ไม่สามารถอัปเดตการตั้งค่าอีเมลได้:', error);
      return false;
    }
  }

  // ส่งอีเมลทดสอบ
  public async sendTestEmail(to: string): Promise<boolean> {
    try {
      if (!this.isConfigured || !this.transporter) {
        await this.initTransporter();
        if (!this.isConfigured || !this.transporter) {
          throw new Error('EmailService: ยังไม่ได้ตั้งค่าอีเมล');
        }
      }

      try {
        const info = await this.transporter.sendMail({
          from: `"${this.emailConfig.defaultName}" <${this.emailConfig.from}>`,
          to,
          subject: 'SLIPKUY - ทดสอบการส่งอีเมล',
          text: 'นี่เป็นอีเมลทดสอบจากระบบ SLIPKUY',
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
              <h2 style="color: #1976d2;">ทดสอบการส่งอีเมล</h2>
              <p>สวัสดี,</p>
              <p>นี่เป็นอีเมลทดสอบจากระบบ SLIPKUY</p>
              <p>หากคุณได้รับอีเมลนี้ แสดงว่าการตั้งค่าอีเมลของคุณถูกต้องแล้ว</p>
              <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #e0e0e0; text-align: center; font-size: 12px; color: #757575;">
                <p>อีเมลนี้เป็นการทดสอบจากระบบ SLIPKUY</p>
                <p>&copy; ${new Date().getFullYear()} SLIPKUY - ระบบตรวจสอบสลิป</p>
              </div>
            </div>
          `
        });
        
        // บันทึกประวัติการส่งอีเมล
        const emailLog = insertEmailLogSchema.parse({
          userId: 1, // หรือ ID ของผู้ใช้ที่ส่ง (ในกรณีนี้ใช้ admin ID 1)
          to,
          subject: 'SLIPKUY - ทดสอบการส่งอีเมล',
          content: 'นี่เป็นอีเมลทดสอบจากระบบ SLIPKUY',
          status: 'sent',
          sentAt: new Date(),
          metadata: {
            messageId: info.messageId
          }
        });
        
        await db.insert(emailLogs).values(emailLog);
        
        return true;
      } catch (sendError) {
        console.warn('EmailService: ไม่สามารถส่งอีเมลจริงได้ - ทำงานในโหมดจำลอง:', sendError.message);
        
        // บันทึกประวัติการส่งอีเมลที่ล้มเหลว แต่ใช้สถานะ "simulated" แทน
        const emailLog = insertEmailLogSchema.parse({
          userId: 1,
          to,
          subject: 'SLIPKUY - ทดสอบการส่งอีเมล (จำลอง)',
          content: 'นี่เป็นอีเมลทดสอบจากระบบ SLIPKUY (จำลอง)',
          status: 'simulated',
          sentAt: new Date(),
          errorMessage: sendError instanceof Error ? sendError.message : String(sendError)
        });
        
        await db.insert(emailLogs).values(emailLog);
        
        // ส่งคืนค่า true เพื่อให้ระบบทำงานต่อไปได้
        return true;
      }
    } catch (error) {
      console.error('EmailService: ไม่สามารถส่งอีเมลทดสอบได้:', error);
      
      // บันทึกประวัติการส่งอีเมลที่ล้มเหลว
      const emailLog = insertEmailLogSchema.parse({
        userId: 1, // หรือ ID ของผู้ใช้ที่ส่ง (ในกรณีนี้ใช้ admin ID 1)
        to,
        subject: 'SLIPKUY - ทดสอบการส่งอีเมล',
        content: 'นี่เป็นอีเมลทดสอบจากระบบ SLIPKUY',
        status: 'failed',
        errorMessage: error instanceof Error ? error.message : String(error)
      });
      
      await db.insert(emailLogs).values(emailLog);
      
      return false;
    }
  }

  // ส่งอีเมลโดยใช้เทมเพลต
  public async sendTemplateEmail(
    to: string,
    templateName: string,
    data: Record<string, any>,
    userId: number
  ): Promise<boolean> {
    try {
      if (!this.isConfigured || !this.transporter) {
        await this.initTransporter();
        if (!this.isConfigured || !this.transporter) {
          throw new Error('EmailService: ยังไม่ได้ตั้งค่าอีเมล');
        }
      }

      // ดึงข้อมูลเทมเพลตจากฐานข้อมูล email_templates
      const templates = await db.select().from(emailTemplates)
        .where(eq(emailTemplates.name, templateName));
      
      if (templates.length === 0) {
        throw new Error(`EmailService: ไม่พบเทมเพลต ${templateName}`);
      }
      
      const template = templates[0];
      
      // แทนที่ตัวแปรในเทมเพลต
      let subject = template.subject;
      let htmlContent = template.htmlContent;
      let textContent = template.textContent;
      
      // เพิ่มตัวแปรมาตรฐาน
      data.currentYear = new Date().getFullYear();
      
      // แทนที่ตัวแปรใน subject, htmlContent, textContent
      Object.keys(data).forEach(key => {
        const regex = new RegExp(`{{${key}}}`, 'g');
        subject = subject.replace(regex, data[key]);
        htmlContent = htmlContent.replace(regex, data[key]);
        textContent = textContent.replace(regex, data[key]);
      });
      
      try {
        // ส่งอีเมล
        const info = await this.transporter.sendMail({
          from: `"${this.emailConfig.defaultName}" <${this.emailConfig.from}>`,
          to,
          subject,
          text: textContent,
          html: htmlContent
        });
        
        // บันทึกประวัติการส่งอีเมล
        const emailLog = insertEmailLogSchema.parse({
          userId,
          to,
          subject,
          content: htmlContent,
          status: 'sent',
          sentAt: new Date(),
          metadata: {
            messageId: info.messageId,
            templateName,
            data
          }
        });
        
        await db.insert(emailLogs).values(emailLog);
        
        return true;
      } catch (sendError) {
        console.warn(`EmailService: ไม่สามารถส่งอีเมล ${templateName} จริงได้ - ทำงานในโหมดจำลอง:`, sendError.message);
        
        // บันทึกประวัติการส่งอีเมลที่ล้มเหลว แต่ใช้สถานะ "simulated" แทน
        const emailLog = insertEmailLogSchema.parse({
          userId,
          to,
          subject: `SLIPKUY - ${templateName} (จำลอง)`,
          content: JSON.stringify(data),
          status: 'simulated',
          sentAt: new Date(),
          errorMessage: sendError instanceof Error ? sendError.message : String(sendError)
        });
        
        await db.insert(emailLogs).values(emailLog);
        
        // ส่งคืนค่า true เพื่อให้ระบบทำงานต่อไปได้
        return true;
      }
    } catch (error) {
      console.error(`EmailService: ไม่สามารถส่งอีเมลด้วยเทมเพลต ${templateName} ได้:`, error);
      
      // บันทึกประวัติการส่งอีเมลที่ล้มเหลว
      const emailLog = insertEmailLogSchema.parse({
        userId,
        to,
        subject: `SLIPKUY - ${templateName}`,
        content: JSON.stringify(data),
        status: 'failed',
        errorMessage: error instanceof Error ? error.message : String(error)
      });
      
      await db.insert(emailLogs).values(emailLog);
      
      return false;
    }
  }

  // ส่งการแจ้งเตือนธุรกรรมผิดปกติ
  public async sendUnusualTransactionAlert(userId: number, transactionData: any): Promise<boolean> {
    try {
      // TODO: ตรวจสอบการตั้งค่าการแจ้งเตือนจะถูกเพิ่มในอนาคต
      // ตอนนี้ส่งการแจ้งเตือนให้ทุกผู้ใช้ที่มีอีเมล
      
      // ดึงข้อมูลผู้ใช้
      const userResult = await db.select().from(users).where(eq(users.id, userId));
      if (userResult.length === 0) {
        console.error(`EmailService: ไม่พบผู้ใช้ ID ${userId}`);
        return false;
      }
      
      const user = userResult[0];
      
      // เตรียมข้อมูลสำหรับการแจ้งเตือน
      const data = {
        userName: user.firstName || user.username,
        transactionRef: transactionData.transactionRef,
        transactionDate: new Date(transactionData.transactionDate).toLocaleString('th-TH'),
        amount: transactionData.amount.toLocaleString('th-TH'),
        sender: transactionData.sender,
        receiver: transactionData.receiver,
        supportLink: 'https://slipkuy.com/support'
      };
      
      // ส่งอีเมลแจ้งเตือน
      return await this.sendTemplateEmail(user.email, 'unusual_transaction', data, userId);
    } catch (error) {
      console.error('EmailService: ไม่สามารถส่งการแจ้งเตือนธุรกรรมผิดปกติได้:', error);
      return false;
    }
  }

  // ส่งรายงานประจำวัน/สัปดาห์/เดือน
  public async sendPeriodicReport(userId: number, reportType: 'daily' | 'weekly' | 'monthly', reportData: any): Promise<boolean> {
    try {
      // TODO: ตรวจสอบการตั้งค่าการแจ้งเตือนจะถูกเพิ่มในอนาคต
      // ตอนนี้ส่งการแจ้งเตือนให้ทุกผู้ใช้ที่มีอีเมล
      
      // ดึงข้อมูลผู้ใช้
      const userResult = await db.select().from(users).where(eq(users.id, userId));
      if (userResult.length === 0) {
        console.error(`EmailService: ไม่พบผู้ใช้ ID ${userId}`);
        return false;
      }
      
      const user = userResult[0];
      
      // เตรียมข้อมูลสำหรับรายงาน
      const data = {
        userName: user.firstName || user.username,
        date: new Date().toLocaleDateString('th-TH'),
        ...reportData,
        dashboardLink: 'https://slipkuy.com/dashboard',
        unsubscribeLink: 'https://slipkuy.com/settings/notifications'
      };
      
      // ส่งอีเมลรายงาน
      return await this.sendTemplateEmail(user.email, `${reportType}_report`, data, userId);
    } catch (error) {
      console.error(`EmailService: ไม่สามารถส่งรายงาน ${reportType} ได้:`, error);
      return false;
    }
  }

  // แจ้งเตือน API key ใกล้หมดอายุ
  public async sendApiKeyExpiringAlert(userId: number, apiKeyData: any): Promise<boolean> {
    try {
      // TODO: ตรวจสอบการตั้งค่าการแจ้งเตือนจะถูกเพิ่มในอนาคต
      // ตอนนี้ส่งการแจ้งเตือนให้ทุกผู้ใช้ที่มีอีเมล
      
      // ดึงข้อมูลผู้ใช้
      const userResult = await db.select().from(users).where(eq(users.id, userId));
      if (userResult.length === 0) {
        console.error(`EmailService: ไม่พบผู้ใช้ ID ${userId}`);
        return false;
      }
      
      const user = userResult[0];
      
      // เตรียมข้อมูลสำหรับการแจ้งเตือน
      const data = {
        userName: user.firstName || user.username,
        apiKeyName: apiKeyData.name,
        daysLeft: apiKeyData.daysLeft,
        expiryDate: new Date(apiKeyData.expiresAt).toLocaleDateString('th-TH'),
        usageCount: apiKeyData.requestCount,
        renewLink: `https://slipkuy.com/api-keys/${apiKeyData.id}/renew`,
        supportLink: 'https://slipkuy.com/support'
      };
      
      // ส่งอีเมลแจ้งเตือน
      return await this.sendTemplateEmail(user.email, 'api_key_expiring', data, userId);
    } catch (error) {
      console.error('EmailService: ไม่สามารถส่งการแจ้งเตือน API key ใกล้หมดอายุได้:', error);
      return false;
    }
  }

  // ฟังก์ชันสำหรับการส่งอีเมลโดยใช้เทมเพลต (สำหรับระบบยืนยันตัวตน)
  public async sendEmail(options: SendEmailOptions): Promise<{ success: boolean; error?: string }> {
    try {
      if (!options.to || !options.subject || !options.templateName) {
        return { 
          success: false, 
          error: 'ข้อมูลไม่ครบถ้วน กรุณาระบุ to, subject และ templateName' 
        };
      }

      const userId = 1; // ใช้ ID เริ่มต้นหากไม่ได้กำหนดเป็นอย่างอื่น
      
      // ส่งอีเมลโดยใช้เทมเพลต
      const emailSent = await this.sendTemplateEmail(
        options.to,
        options.templateName,
        options.variables,
        userId
      );
      
      if (emailSent) {
        return { success: true };
      } else {
        return { success: false, error: 'ไม่สามารถส่งอีเมลได้' };
      }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'ไม่สามารถส่งอีเมลได้' 
      };
    }
  }

  /**
   * ส่งอีเมลรีเซ็ตรหัสผ่าน
   * 
   * @param email อีเมลที่ต้องการส่ง
   * @param data ข้อมูลที่ใช้ในการส่ง (username, code)
   * @returns สถานะการส่งอีเมล
   */
  public async sendPasswordResetEmail(email: string, data: { username: string, code: string }): Promise<boolean> {
    try {
      if (!this.isConfigured || !this.transporter) {
        await this.initTransporter();
        if (!this.isConfigured || !this.transporter) {
          throw new Error('EmailService: ยังไม่ได้ตั้งค่าอีเมล');
        }
      }
      
      // ควรมีการใช้ template จากฐานข้อมูล แต่ตอนนี้สร้าง HTML ตรงนี้ก่อน
      const subject = 'SLIPKUY - รหัสยืนยันสำหรับรีเซ็ตรหัสผ่าน';
      const html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <h2 style="color: #8B5CF6;">รีเซ็ตรหัสผ่าน SLIPKUY</h2>
          <p>สวัสดี ${data.username},</p>
          <p>คุณได้ขอรีเซ็ตรหัสผ่านสำหรับบัญชี SLIPKUY ของคุณ</p>
          <p>รหัสยืนยันของคุณคือ:</p>
          <div style="text-align: center; margin: 20px 0;">
            <div style="display: inline-block; background-color: #f0f0f0; padding: 15px 30px; border-radius: 5px; letter-spacing: 5px; font-size: 24px; font-weight: bold;">
              ${data.code}
            </div>
          </div>
          <p>รหัสนี้จะหมดอายุใน 10 นาที</p>
          <p>หากคุณไม่ได้ร้องขอการรีเซ็ตรหัสผ่าน คุณสามารถเพิกเฉยอีเมลนี้ได้</p>
          
          <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #e0e0e0; text-align: center; font-size: 12px; color: #757575;">
            <p>อีเมลนี้ถูกส่งโดยอัตโนมัติจากระบบ SLIPKUY กรุณาอย่าตอบกลับ</p>
            <p>&copy; ${new Date().getFullYear()} สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>
          </div>
        </div>
      `;
      
      // ส่งอีเมล
      const info = await this.transporter.sendMail({
        from: `"${this.emailConfig.defaultName}" <${this.emailConfig.from}>`,
        to: email,
        subject: subject,
        html: html
      });
      
      // ลอกอีเมลที่ส่ง
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.email, email));
      
      const userId = user ? user.id : null;
      
      // บันทึกประวัติการส่งอีเมล
      const emailLog = insertEmailLogSchema.parse({
        userId: userId,
        to: email,
        subject: subject,
        content: html,
        status: 'sent',
        sentAt: new Date(),
        metadata: {
          messageId: info.messageId,
          type: 'password_reset'
        }
      });
      
      await db.insert(emailLogs).values(emailLog);
      
      return true;
    } catch (error) {
      console.error('EmailService: ไม่สามารถส่งอีเมลรีเซ็ตรหัสผ่านได้:', error);
      return false;
    }
  }

  // ตรวจสอบสถานะการตั้งค่าอีเมล
  public isEmailConfigured(): boolean {
    return this.isConfigured;
  }

  // ดึงการตั้งค่าอีเมลปัจจุบัน
  public async getEmailConfig(): Promise<any> {
    if (!this.emailConfig) {
      // ดึงค่าการตั้งค่าอีเมลจากฐานข้อมูล
      const settings = await db.select().from(systemSettings).where(eq(systemSettings.key, 'email_settings'));
      
      if (settings.length === 0 || !settings[0].valueJson) {
        return null;
      }
      
      this.emailConfig = settings[0].valueJson;
    }
    
    // ลบข้อมูลที่ละเอียดอ่อนออก
    const safeConfig = { ...this.emailConfig };
    if (safeConfig.auth) {
      safeConfig.auth = {
        user: safeConfig.auth.user,
        hasPassword: !!safeConfig.auth.pass
      };
    }
    
    return safeConfig;
  }
}

export const emailService = new EmailService();