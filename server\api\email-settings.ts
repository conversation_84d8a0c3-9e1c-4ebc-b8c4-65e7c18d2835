import { Request, Response } from 'express';
import { db } from '../db';
import { eq } from 'drizzle-orm';
import { systemSettings, emailTemplates, emailLogs, insertEmailTemplateSchema } from '@shared/schema';
import { isAdmin } from '../auth';
import { emailService } from '../email-service';

// ดึงการตั้งค่า email
export async function getEmailSettings(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'กรุณาเข้าสู่ระบบ' });
    }

    if (!isAdmin(req)) {
      return res.status(403).json({ message: 'คุณไม่มีสิทธิ์ในการเข้าถึง' });
    }

    const settings = await emailService.getEmailConfig();
    
    if (!settings) {
      return res.status(404).json({ message: 'ไม่พบการตั้งค่าอีเมล' });
    }

    res.json(settings);
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการดึงการตั้งค่าอีเมล:', error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการดึงการตั้งค่าอีเมล' });
  }
}

// อัปเดตการตั้งค่า email
export async function updateEmailSettings(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'กรุณาเข้าสู่ระบบ' });
    }

    if (!isAdmin(req)) {
      return res.status(403).json({ message: 'คุณไม่มีสิทธิ์ในการเข้าถึง' });
    }

    const newSettings = req.body;
    
    // ตรวจสอบว่ามีการตั้งค่ารหัสผ่านใหม่หรือไม่
    const currentConfig = await emailService.getEmailConfig();
    
    if (!newSettings.auth) {
      newSettings.auth = {};
    }
    
    // ตรวจสอบ header ว่าต้องการเก็บรหัสผ่านเดิมหรือไม่
    const keepPassword = req.headers['x-keep-password'] === 'true';
    
    // ถ้าต้องการเก็บรหัสผ่านเดิม หรือ ไม่ได้ส่งรหัสผ่านมา
    if ((keepPassword || !newSettings.auth.pass) && currentConfig && currentConfig.auth) {
      // ดึงรหัสผ่านเดิมจากฐานข้อมูล
      const originalSettings = await db.select().from(systemSettings)
        .where(eq(systemSettings.key, 'email_settings'));
      
      if (originalSettings.length > 0 && originalSettings[0].valueJson) {
        newSettings.auth.pass = originalSettings[0].valueJson.auth.pass;
      }
    }
    
    // อัปเดตการตั้งค่า
    const result = await emailService.updateEmailConfig(newSettings);
    
    if (!result) {
      return res.status(400).json({ message: 'ไม่สามารถอัปเดตการตั้งค่าอีเมลได้' });
    }

    res.json({ message: 'อัปเดตการตั้งค่าอีเมลเรียบร้อย' });
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการอัปเดตการตั้งค่าอีเมล:', error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการอัปเดตการตั้งค่าอีเมล' });
  }
}

// ส่งอีเมลทดสอบ
export async function sendTestEmail(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'กรุณาเข้าสู่ระบบ' });
    }

    if (!isAdmin(req)) {
      return res.status(403).json({ message: 'คุณไม่มีสิทธิ์ในการเข้าถึง' });
    }

    const { to } = req.body;
    
    if (!to) {
      return res.status(400).json({ message: 'กรุณาระบุอีเมลปลายทาง' });
    }
    
    const result = await emailService.sendTestEmail(to);
    
    if (!result) {
      return res.status(400).json({ message: 'ไม่สามารถส่งอีเมลทดสอบได้' });
    }

    res.json({ message: 'ส่งอีเมลทดสอบเรียบร้อย' });
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการส่งอีเมลทดสอบ:', error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการส่งอีเมลทดสอบ' });
  }
}

// ดึงเทมเพลตอีเมลทั้งหมด
export async function getEmailTemplates(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'กรุณาเข้าสู่ระบบ' });
    }

    if (!isAdmin(req)) {
      return res.status(403).json({ message: 'คุณไม่มีสิทธิ์ในการเข้าถึง' });
    }

    const templates = await db.select().from(emailTemplates);
    res.json(templates);
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการดึงเทมเพลตอีเมล:', error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการดึงเทมเพลตอีเมล' });
  }
}

// ดึงเทมเพลตอีเมลโดย ID
export async function getEmailTemplateById(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'กรุณาเข้าสู่ระบบ' });
    }

    if (!isAdmin(req)) {
      return res.status(403).json({ message: 'คุณไม่มีสิทธิ์ในการเข้าถึง' });
    }

    const templateId = parseInt(req.params.id);
    const template = await db.select().from(emailTemplates)
      .where(eq(emailTemplates.id, templateId));
    
    if (template.length === 0) {
      return res.status(404).json({ message: 'ไม่พบเทมเพลตอีเมล' });
    }

    res.json(template[0]);
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการดึงเทมเพลตอีเมล:', error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการดึงเทมเพลตอีเมล' });
  }
}

// อัปเดตเทมเพลตอีเมล
export async function updateEmailTemplate(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'กรุณาเข้าสู่ระบบ' });
    }

    if (!isAdmin(req)) {
      return res.status(403).json({ message: 'คุณไม่มีสิทธิ์ในการเข้าถึง' });
    }

    const templateId = parseInt(req.params.id);
    const templateData = req.body;
    
    // ตรวจสอบว่าเทมเพลตนี้มีอยู่จริงหรือไม่
    const template = await db.select().from(emailTemplates)
      .where(eq(emailTemplates.id, templateId));
    
    if (template.length === 0) {
      return res.status(404).json({ message: 'ไม่พบเทมเพลตอีเมล' });
    }
    
    // อัปเดตเทมเพลต
    const updatedTemplate = {
      ...templateData,
      updatedAt: new Date()
    };
    
    await db.update(emailTemplates)
      .set(updatedTemplate)
      .where(eq(emailTemplates.id, templateId));

    res.json({ message: 'อัปเดตเทมเพลตอีเมลเรียบร้อย' });
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการอัปเดตเทมเพลตอีเมล:', error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการอัปเดตเทมเพลตอีเมล' });
  }
}

// สร้างเทมเพลตอีเมลใหม่
export async function createEmailTemplate(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'กรุณาเข้าสู่ระบบ' });
    }

    if (!isAdmin(req)) {
      return res.status(403).json({ message: 'คุณไม่มีสิทธิ์ในการเข้าถึง' });
    }

    const templateData = insertEmailTemplateSchema.parse(req.body);
    
    // ตรวจสอบว่าชื่อเทมเพลตซ้ำหรือไม่
    const existingTemplate = await db.select().from(emailTemplates)
      .where(eq(emailTemplates.name, templateData.name));
    
    if (existingTemplate.length > 0) {
      return res.status(400).json({ message: 'มีเทมเพลตชื่อนี้อยู่แล้ว' });
    }
    
    // สร้างเทมเพลตใหม่
    const result = await db.insert(emailTemplates).values({
      ...templateData,
      createdAt: new Date(),
      updatedAt: new Date()
    }).returning();

    res.status(201).json(result[0]);
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการสร้างเทมเพลตอีเมล:', error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการสร้างเทมเพลตอีเมล' });
  }
}

// ดึงประวัติการส่งอีเมล
export async function getEmailLogs(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'กรุณาเข้าสู่ระบบ' });
    }

    if (!isAdmin(req)) {
      return res.status(403).json({ message: 'คุณไม่มีสิทธิ์ในการเข้าถึง' });
    }

    const limit = req.query.limit ? parseInt(req.query.limit as string) : 20;
    const page = req.query.page ? parseInt(req.query.page as string) : 1;
    const offset = (page - 1) * limit;

    const logs = await db.select().from(emailLogs)
      .limit(limit)
      .offset(offset)
      .orderBy(db.desc(emailLogs.createdAt));

    const totalCount = await db.select({ count: db.fn.count() }).from(emailLogs);

    res.json({
      logs,
      pagination: {
        total: parseInt(totalCount[0].count as string),
        page,
        limit,
        totalPages: Math.ceil(parseInt(totalCount[0].count as string) / limit)
      }
    });
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการดึงประวัติการส่งอีเมล:', error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการดึงประวัติการส่งอีเมล' });
  }
}