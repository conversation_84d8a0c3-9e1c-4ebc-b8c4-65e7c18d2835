import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { DashboardLayout } from "@/components/layouts/dashboard-layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { 
  Search, 
  CheckCircle2, 
  XCircle, 
  Clock, 
  Info, 
  ArrowRight, 
  ChevronRight, 
  Copy
} from "lucide-react";
import { formatDate, formatNumber, formatCurrency } from "@/lib/utils";

// ประกาศ Interface สำหรับข้อมูลการตรวจสอบสลิป
interface SlipVerification {
  id: number;
  userId: number;
  transactionRef: string | null;
  bankName: string | null;
  amount: number | null;
  sender: string | null;
  receiver: string | null;
  transactionDate: string | null;
  status: string;
  responseData: string | null;
  createdAt: string;
}

// Interface สำหรับ Response Data ที่เก็บใน JSON
interface SlipResponseData {
  status: number;
  data?: {
    payload: string;
    transRef: string;
    date: string;
    countryCode: string;
    amount: {
      amount: number;
      local: {
        amount?: number;
        currency?: string;
      }
    };
    fee?: number;
    ref1?: string;
    ref2?: string;
    ref3?: string;
    sender: {
      bank: {
        id: string;
        name?: string;
        short?: string;
      };
      account: {
        name: {
          th?: string;
          en?: string;
        };
        bank?: {
          type: 'BANKAC' | 'TOKEN' | 'DUMMY';
          account: string;
        };
        proxy?: {
          type: 'NATID' | 'MSISDN' | 'EWALLETID' | 'EMAIL' | 'BILLERID';
          account: string;
        };
      };
    };
    receiver: {
      bank: {
        id: string;
        name?: string;
        short?: string;
      };
      account: {
        name: {
          th?: string;
          en?: string;
        };
        bank?: {
          type: 'BANKAC' | 'TOKEN' | 'DUMMY';
          account: string;
        };
        proxy?: {
          type: 'NATID' | 'MSISDN' | 'EWALLETID' | 'EMAIL' | 'BILLERID';
          account: string;
        };
      };
    };
  };
  message?: string;
}

// คอมโพเนนต์สำหรับแสดงรายละเอียดสลิปเต็มรูปแบบเหมือนสลิปธนาคารจริงๆ
function SlipDetailModal({ 
  verification, 
  open, 
  onOpenChange 
}: { 
  verification: SlipVerification | null, 
  open: boolean, 
  onOpenChange: (open: boolean) => void 
}) {
  // ถ้าไม่มีข้อมูลหรือไม่ได้เปิด dialog ให้ return null
  if (!verification || !open) return null;
  
  // แปลง responseData จาก JSON string เป็น object
  let slipData: SlipResponseData | null = null;
  try {
    if (verification.responseData) {
      slipData = JSON.parse(verification.responseData) as SlipResponseData;
    }
  } catch (error) {
    console.error("Error parsing slip response data:", error);
  }
  
  // ฟังก์ชัน copy ข้อความไปยัง clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
      .then(() => {
        alert("คัดลอกข้อมูลเรียบร้อยแล้ว");
      })
      .catch(err => {
        console.error("ไม่สามารถคัดลอกข้อมูลได้:", err);
      });
  };
  
  // วันที่ทำรายการ
  const transactionDate = verification.transactionDate 
    ? new Date(verification.transactionDate)
    : new Date(verification.createdAt);
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="text-center">รายละเอียดสลิป</DialogTitle>
          <DialogDescription className="text-center">
            {verification.status === "success" ? "ยืนยันการโอนเงินสำเร็จ" : "ข้อมูลการตรวจสอบสลิป"}
          </DialogDescription>
        </DialogHeader>
        
        {/* หน้าตาสลิปแบบธนาคารจริงๆ */}
        <div className="py-4 px-2 bg-white rounded-lg border border-gray-200">
          {/* โลโก้ธนาคาร */}
          <div className="flex justify-center mb-6">
            <div className="bg-green-100 rounded-full p-4">
              <CheckCircle2 className="h-12 w-12 text-green-600" />
            </div>
          </div>
          
          {/* ชื่อธนาคาร */}
          <div className="text-center mb-6">
            <h3 className="text-lg font-bold">{verification.bankName || "ธนาคารไม่ระบุ"}</h3>
            <p className="text-sm text-gray-500">ธุรกรรมสำเร็จ</p>
          </div>
          
          {/* จำนวนเงิน */}
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold text-green-600">
              {verification.amount ? formatCurrency(verification.amount) : "ไม่ระบุจำนวนเงิน"}
            </h2>
          </div>
          
          {/* รายละเอียดการโอน */}
          <div className="space-y-4 text-sm">
            {/* วัน/เวลาที่ทำรายการ */}
            <div className="flex justify-between items-center border-b border-gray-100 pb-2">
              <span className="text-gray-500">วัน/เวลาที่ทำรายการ</span>
              <span className="font-medium">{formatDate(transactionDate, "PPp")}</span>
            </div>
            
            {/* หมายเลขอ้างอิง */}
            {verification.transactionRef && (
              <div className="flex justify-between items-center border-b border-gray-100 pb-2">
                <span className="text-gray-500">หมายเลขอ้างอิง</span>
                <div className="flex items-center">
                  <span className="font-medium mr-2">{verification.transactionRef}</span>
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-6 w-6" 
                    onClick={() => copyToClipboard(verification.transactionRef!)}
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            )}
            
            {/* จากบัญชี */}
            <div className="flex justify-between items-start border-b border-gray-100 pb-2">
              <span className="text-gray-500">จาก</span>
              <div className="text-right">
                <div className="font-medium">{verification.sender || "ไม่ระบุชื่อผู้ส่ง"}</div>
                {slipData?.data?.sender?.bank?.name && (
                  <div className="text-xs text-gray-500">
                    {slipData.data.sender.bank.name}
                  </div>
                )}
                {slipData?.data?.sender?.account?.bank?.account && (
                  <div className="text-xs text-gray-500">
                    {slipData.data.sender.account.bank.account}
                  </div>
                )}
              </div>
            </div>
            
            {/* ไปยังบัญชี */}
            <div className="flex justify-between items-start border-b border-gray-100 pb-2">
              <span className="text-gray-500">ไปยัง</span>
              <div className="text-right">
                <div className="font-medium">{verification.receiver || "ไม่ระบุชื่อผู้รับ"}</div>
                {slipData?.data?.receiver?.bank?.name && (
                  <div className="text-xs text-gray-500">
                    {slipData.data.receiver.bank.name}
                  </div>
                )}
                {slipData?.data?.receiver?.account?.bank?.account && (
                  <div className="text-xs text-gray-500">
                    {slipData.data.receiver.account.bank.account}
                  </div>
                )}
              </div>
            </div>
            
            {/* ค่าธรรมเนียม */}
            {slipData?.data?.fee !== undefined && (
              <div className="flex justify-between items-center border-b border-gray-100 pb-2">
                <span className="text-gray-500">ค่าธรรมเนียม</span>
                <span className="font-medium">{formatCurrency(slipData.data.fee)}</span>
              </div>
            )}
            
            {/* รายละเอียดเพิ่มเติม */}
            {(slipData?.data?.ref1 || slipData?.data?.ref2 || slipData?.data?.ref3) && (
              <div className="pt-2">
                <h4 className="font-medium mb-2">รายละเอียดเพิ่มเติม</h4>
                {slipData?.data?.ref1 && (
                  <div className="flex justify-between items-center text-xs">
                    <span>Ref 1:</span>
                    <span>{slipData.data.ref1}</span>
                  </div>
                )}
                {slipData?.data?.ref2 && (
                  <div className="flex justify-between items-center text-xs">
                    <span>Ref 2:</span>
                    <span>{slipData.data.ref2}</span>
                  </div>
                )}
                {slipData?.data?.ref3 && (
                  <div className="flex justify-between items-center text-xs">
                    <span>Ref 3:</span>
                    <span>{slipData.data.ref3}</span>
                  </div>
                )}
              </div>
            )}
          </div>
          
          {/* ส่วนท้าย */}
          <div className="mt-6 text-center">
            <div className="text-xs text-gray-500 mb-2">ตรวจสอบโดย SLIPKUY</div>
            <div className="text-xs text-gray-500">{formatDate(new Date(), "Pp")}</div>
          </div>
        </div>
        
        <div className="flex justify-center mt-4">
          <Button 
            onClick={() => onOpenChange(false)}
            className="px-8"
          >
            ปิด
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default function HistoryPage() {
  const [search, setSearch] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedVerification, setSelectedVerification] = useState<SlipVerification | null>(null);
  const [slipModalOpen, setSlipModalOpen] = useState(false);
  const itemsPerPage = 10;

  // ดึงข้อมูลประวัติการตรวจสอบ
  const { data: verifications, isLoading } = useQuery<SlipVerification[]>({
    queryKey: ['/api/verifications'],
  });

  // กรองข้อมูลตามเงื่อนไขค้นหาและสถานะ
  const filteredVerifications = verifications?.filter((verification) => {
    // กรองตามข้อความค้นหา
    const searchMatch =
      search === "" ||
      (verification.transactionRef?.toLowerCase().includes(search.toLowerCase()) ||
       verification.bankName?.toLowerCase().includes(search.toLowerCase()) ||
       verification.sender?.toLowerCase().includes(search.toLowerCase()) ||
       verification.receiver?.toLowerCase().includes(search.toLowerCase()));
    
    // กรองตามสถานะ
    const statusMatch = statusFilter === "all" || verification.status === statusFilter;

    return searchMatch && statusMatch;
  });

  // คำนวณจำนวนหน้าทั้งหมด
  const totalPages = filteredVerifications
    ? Math.ceil(filteredVerifications.length / itemsPerPage)
    : 0;

  // คำนวณดัชนีเริ่มต้นและสิ้นสุดของรายการที่จะแสดงในหน้าปัจจุบัน
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;

  // รายการที่จะแสดงในหน้าปัจจุบัน
  const currentVerifications = filteredVerifications?.slice(startIndex, endIndex);

  // ฟังก์ชันแสดงสถานะด้วยสีและไอคอน
  const renderStatus = (status: string) => {
    switch (status) {
      case "success":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle2 className="h-3 w-3 mr-1" />
            สำเร็จ
          </Badge>
        );
      case "failed":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            <XCircle className="h-3 w-3 mr-1" />
            ล้มเหลว
          </Badge>
        );
      case "pending":
        return (
          <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
            <Clock className="h-3 w-3 mr-1" />
            กำลังดำเนินการ
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-neutral-50 text-neutral-700 border-neutral-200">
            {status}
          </Badge>
        );
    }
  };

  // แสดงข้อมูลทั้งหมดในรูปแบบตาราง
  return (
    <DashboardLayout>
      <div className="space-y-8">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">ประวัติการตรวจสอบ</h1>
          <p className="text-muted-foreground">
            ดูประวัติการตรวจสอบสลิปทั้งหมดและสถานะการดำเนินการ
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>ประวัติการตรวจสอบสลิป</CardTitle>
            <CardDescription>
              รายการตรวจสอบสลิปทั้งหมดที่คุณได้ดำเนินการผ่านระบบ
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* ส่วนกรองและค้นหา */}
              <div className="flex flex-col gap-4 md:flex-row">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="ค้นหาจากรายละเอียดสลิป..."
                    className="pl-10"
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                  />
                </div>
                <Select
                  value={statusFilter}
                  onValueChange={setStatusFilter}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="สถานะ" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">ทั้งหมด</SelectItem>
                    <SelectItem value="success">สำเร็จ</SelectItem>
                    <SelectItem value="failed">ล้มเหลว</SelectItem>
                    <SelectItem value="pending">กำลังดำเนินการ</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* ตารางแสดงข้อมูล */}
              {isLoading ? (
                <div className="space-y-4">
                  {[...Array(5)].map((_, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <Skeleton className="h-6 w-1/4" />
                      <Skeleton className="h-6 w-1/5" />
                      <Skeleton className="h-6 w-1/6" />
                      <Skeleton className="h-6 w-1/6" />
                    </div>
                  ))}
                </div>
              ) : currentVerifications && currentVerifications.length > 0 ? (
                <>
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>วันที่</TableHead>
                          <TableHead>ธนาคาร / รายละเอียด</TableHead>
                          <TableHead>จำนวนเงิน</TableHead>
                          <TableHead>ผู้ส่ง / ผู้รับ</TableHead>
                          <TableHead>สถานะ</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {currentVerifications.map((verification) => (
                          <TableRow 
                            key={verification.id}
                            className="cursor-pointer hover:bg-gray-50"
                            onClick={() => {
                              setSelectedVerification(verification);
                              setSlipModalOpen(true);
                            }}
                          >
                            <TableCell className="font-medium">
                              {verification.transactionDate
                                ? formatDate(new Date(verification.transactionDate), "Pp")
                                : formatDate(new Date(verification.createdAt), "Pp")}
                            </TableCell>
                            <TableCell>
                              <div className="font-medium">
                                {verification.bankName || "ไม่ระบุธนาคาร"}
                              </div>
                              {verification.transactionRef && (
                                <div className="text-xs text-muted-foreground">
                                  Ref: {verification.transactionRef}
                                </div>
                              )}
                            </TableCell>
                            <TableCell>
                              {verification.amount
                                ? `${formatNumber(verification.amount)} บาท`
                                : "ไม่ระบุ"}
                            </TableCell>
                            <TableCell>
                              <div className="text-sm">
                                {verification.sender ? (
                                  <>จาก: {verification.sender}</>
                                ) : (
                                  <>ผู้ส่ง: ไม่ระบุ</>
                                )}
                              </div>
                              <div className="text-sm">
                                {verification.receiver ? (
                                  <>ถึง: {verification.receiver}</>
                                ) : (
                                  <>ผู้รับ: ไม่ระบุ</>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center justify-between">
                                {renderStatus(verification.status)}
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="ml-2 h-6 w-6"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setSelectedVerification(verification);
                                    setSlipModalOpen(true);
                                  }}
                                >
                                  <Info className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>

                  {/* การแบ่งหน้า */}
                  {totalPages > 1 && (
                    <Pagination>
                      <PaginationContent>
                        <PaginationItem>
                          <PaginationPrevious
                            onClick={() => currentPage > 1 && setCurrentPage(currentPage - 1)}
                            className={currentPage === 1 ? "cursor-not-allowed opacity-50" : "cursor-pointer"}
                          />
                        </PaginationItem>
                        
                        {/* แสดงหมายเลขหน้า */}
                        {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                          // ถ้ามีหน้ามากกว่า 5 หน้า
                          let pageNum = i + 1;
                          if (totalPages > 5 && currentPage > 3) {
                            pageNum = currentPage - 2 + i;
                            if (pageNum > totalPages) {
                              pageNum = totalPages - (4 - i);
                            }
                          }
                          
                          if (pageNum > 0 && pageNum <= totalPages) {
                            return (
                              <PaginationItem key={i}>
                                <PaginationLink
                                  isActive={pageNum === currentPage}
                                  onClick={() => setCurrentPage(pageNum)}
                                >
                                  {pageNum}
                                </PaginationLink>
                              </PaginationItem>
                            );
                          }
                          return null;
                        })}
                        
                        <PaginationItem>
                          <PaginationNext
                            onClick={() => currentPage < totalPages && setCurrentPage(currentPage + 1)}
                            className={currentPage === totalPages ? "cursor-not-allowed opacity-50" : "cursor-pointer"}
                          />
                        </PaginationItem>
                      </PaginationContent>
                    </Pagination>
                  )}
                </>
              ) : (
                <div className="py-8 text-center">
                  <p className="text-muted-foreground">ไม่พบประวัติการตรวจสอบสลิป</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* แสดง Modal รายละเอียดสลิป */}
      <SlipDetailModal 
        verification={selectedVerification} 
        open={slipModalOpen}
        onOpenChange={setSlipModalOpen}
      />
    </DashboardLayout>
  );
}
