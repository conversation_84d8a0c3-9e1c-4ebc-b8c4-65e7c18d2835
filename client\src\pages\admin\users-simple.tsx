import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription
} from "@/components/ui/form";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Admin } from "@/components/layouts/admin-layout";
import { useAuth } from "@/hooks/use-auth";
import { apiRequest } from "@/lib/queryClient";
import { queryClient } from "@/lib/queryClient";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { formatDate, formatCurrency } from "@/lib/utils";
import { z } from "zod";
import { User as UserType, Package as PackageType, UserPackage as UserPackageType } from "@shared/schema";
import { useToast } from "@/hooks/use-toast";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { format } from "date-fns";
import {
  Search,
  RefreshCw,
  Edit,
  Trash,
  AlertCircle,
  UserCog,
  CreditCard,
  Plus,
  Minus,
  PlusCircle,
  Package,
  Calendar as CalendarIcon,
  Check,
  RefreshCcw,
  Clock
} from "lucide-react";

// สร้าง Schema สำหรับแก้ไขผู้ใช้งาน
const editUserSchema = z.object({
  username: z.string().min(3, "ชื่อผู้ใช้ต้องมีอย่างน้อย 3 ตัวอักษร"),
  email: z.string().email("รูปแบบอีเมลไม่ถูกต้อง"),
  firstName: z.string().min(1, "กรุณากรอกชื่อ"),
  lastName: z.string().min(1, "กรุณากรอกนามสกุล"),
  status: z.enum(["active", "inactive", "suspended"]),
  role: z.enum(["user", "admin"])
});

type EditUserFormValues = z.infer<typeof editUserSchema>;

// สร้าง Schema สำหรับการเพิ่ม/หักเครดิต
const creditActionSchema = z.object({
  amount: z.preprocess(
    (val) => Number(val),
    z.number().positive("จำนวนเครดิตต้องมากกว่า 0")
  ),
  description: z.string().optional(),
});

type CreditActionFormValues = z.infer<typeof creditActionSchema>;

// สร้าง Schema สำหรับเพิ่มแพ็คเกจให้ผู้ใช้
const addPackageSchema = z.object({
  packageId: z.preprocess(
    (val) => Number(val),
    z.number().positive("กรุณาเลือกแพ็คเกจ")
  ),
  durationMonths: z.preprocess(
    (val) => Number(val),
    z.number().int().positive("ระยะเวลาต้องเป็นจำนวนเต็มบวก")
  ),
  isActive: z.boolean().default(true),
});

type AddPackageFormValues = z.infer<typeof addPackageSchema>;

export default function UsersManagement() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [search, setSearch] = useState("");
  const [selectedUser, setSelectedUser] = useState<UserType | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isCreditDialogOpen, setIsCreditDialogOpen] = useState(false);
  const [isPackagesDialogOpen, setIsPackagesDialogOpen] = useState(false);
  const [creditAction, setCreditAction] = useState<'add' | 'deduct'>('add');

  // ข้อมูลแพ็คเกจของผู้ใช้
  const [userPackages, setUserPackages] = useState<(UserPackageType & { package: PackageType })[]>([]);

  // ฟอร์มสำหรับเพิ่มแพ็คเกจ
  const addPackageForm = useForm<AddPackageFormValues>({
    resolver: zodResolver(addPackageSchema),
    defaultValues: {
      packageId: 0,
      durationMonths: 1,
      isActive: true
    }
  });

  // ดึงข้อมูลแพ็คเกจทั้งหมด
  const { data: packagesList } = useQuery<PackageType[]>({
    queryKey: ['/api/packages'],
    enabled: isPackagesDialogOpen,
  });

  // ฟอร์มสำหรับแก้ไขผู้ใช้งาน
  const editUserForm = useForm<EditUserFormValues>({
    resolver: zodResolver(editUserSchema),
    defaultValues: {
      username: "",
      email: "",
      firstName: "",
      lastName: "",
      status: "active",
      role: "user"
    }
  });

  // ฟอร์มสำหรับเพิ่ม/หักเครดิต
  const creditForm = useForm<CreditActionFormValues>({
    resolver: zodResolver(creditActionSchema),
    defaultValues: {
      amount: 0,
      description: ""
    }
  });

  // ดึงข้อมูลผู้ใช้งานทั้งหมด
  const { data: users, isLoading } = useQuery<UserType[]>({
    queryKey: ['/api/admin/users'],
  });

  // Mutation สำหรับอัปเดตข้อมูลผู้ใช้งาน
  const updateUserMutation = useMutation({
    mutationFn: async (userData: Partial<UserType>) => {
      const res = await apiRequest("PATCH", `/api/admin/users/${selectedUser?.id}`, userData);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "อัปเดตข้อมูลสำเร็จ",
        description: "ข้อมูลผู้ใช้งานถูกอัปเดตเรียบร้อยแล้ว",
      });
      setIsEditDialogOpen(false);
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users'] });
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับลบผู้ใช้งาน
  const deleteUserMutation = useMutation({
    mutationFn: async () => {
      const res = await apiRequest("DELETE", `/api/admin/users/${selectedUser?.id}`, {});
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "ลบผู้ใช้งานสำเร็จ",
        description: "ผู้ใช้งานถูกลบออกจากระบบเรียบร้อยแล้ว",
      });
      setIsDeleteDialogOpen(false);
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users'] });
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับเพิ่มเครดิต
  const addCreditMutation = useMutation({
    mutationFn: async (data: CreditActionFormValues) => {
      const res = await apiRequest("POST", `/api/admin/users/${selectedUser?.id}/credit`, data);
      return await res.json();
    },
    onSuccess: (response) => {
      toast({
        title: "เพิ่มเครดิตสำเร็จ",
        description: `เพิ่มเครดิต ${formatCurrency(creditForm.getValues().amount)} บาท ให้กับ ${selectedUser?.username} เรียบร้อยแล้ว`,
      });
      setIsCreditDialogOpen(false);
      creditForm.reset();
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users'] });
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับหักเครดิต
  const deductCreditMutation = useMutation({
    mutationFn: async (data: CreditActionFormValues) => {
      const res = await apiRequest("POST", `/api/admin/users/${selectedUser?.id}/deduct-credit`, data);
      return await res.json();
    },
    onSuccess: (response) => {
      toast({
        title: "หักเครดิตสำเร็จ",
        description: `หักเครดิต ${formatCurrency(creditForm.getValues().amount)} บาท จาก ${selectedUser?.username} เรียบร้อยแล้ว`,
      });
      setIsCreditDialogOpen(false);
      creditForm.reset();
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users'] });
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับเพิ่มแพ็คเกจให้ผู้ใช้
  const addPackageMutation = useMutation({
    mutationFn: async (data: AddPackageFormValues) => {
      const res = await apiRequest("POST", `/api/admin/users/${selectedUser?.id}/packages`, data);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "เพิ่มแพ็คเกจสำเร็จ",
        description: "เพิ่มแพ็คเกจให้ผู้ใช้งานเรียบร้อยแล้ว",
      });
      // รีเฟรชข้อมูลแพ็คเกจของผู้ใช้
      handleOpenPackagesDialog(selectedUser!);
      addPackageForm.reset();
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับรีเซ็ตยอดการใช้งานแพ็คเกจ
  const resetPackageUsageMutation = useMutation({
    mutationFn: async (packageId: number) => {
      const res = await apiRequest("PATCH", `/api/admin/users/${selectedUser?.id}/packages/${packageId}`, { reset: true });
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "รีเซ็ตยอดการใช้งานสำเร็จ",
        description: "รีเซ็ตยอดการใช้งานแพ็คเกจเรียบร้อยแล้ว",
      });
      // รีเฟรชข้อมูลแพ็คเกจของผู้ใช้
      handleOpenPackagesDialog(selectedUser!);
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับเปลี่ยนสถานะแพ็คเกจ (active/inactive)
  const togglePackageStatusMutation = useMutation({
    mutationFn: async ({ packageId, isActive }: { packageId: number; isActive: boolean }) => {
      const res = await apiRequest("PATCH", `/api/admin/users/${selectedUser?.id}/packages/${packageId}`, { isActive });
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "อัปเดตสถานะแพ็คเกจสำเร็จ",
        description: "อัปเดตสถานะแพ็คเกจเรียบร้อยแล้ว",
      });
      // รีเฟรชข้อมูลแพ็คเกจของผู้ใช้
      handleOpenPackagesDialog(selectedUser!);
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // กรองข้อมูลผู้ใช้งานตามเงื่อนไขค้นหา
  const filteredUsers = users?.filter((user) =>
    search === "" ||
    user.username.toLowerCase().includes(search.toLowerCase()) ||
    user.email.toLowerCase().includes(search.toLowerCase()) ||
    user.firstName?.toLowerCase().includes(search.toLowerCase()) ||
    user.lastName?.toLowerCase().includes(search.toLowerCase())
  );

  // เปิดไดอะล็อกแก้ไขข้อมูลผู้ใช้
  const handleOpenEditDialog = (user: UserType) => {
    setSelectedUser(user);
    editUserForm.reset({
      username: user.username,
      email: user.email,
      firstName: user.firstName || "",
      lastName: user.lastName || "",
      status: user.status as "active" | "inactive" | "suspended",
      role: user.role as "user" | "admin"
    });
    setIsEditDialogOpen(true);
  };

  // เปิดไดอะล็อกลบผู้ใช้
  const handleOpenDeleteDialog = (user: UserType) => {
    setSelectedUser(user);
    setIsDeleteDialogOpen(true);
  };

  // เปิดไดอะล็อกเพิ่ม/หักเครดิต
  const handleOpenCreditDialog = (user: UserType, action: 'add' | 'deduct' = 'add') => {
    setSelectedUser(user);
    setCreditAction(action);
    creditForm.reset({
      amount: 0,
      description: action === 'add' ? 'เติมเครดิต' : 'หักเครดิต'
    });
    setIsCreditDialogOpen(true);
  };

  // เปิดไดอะล็อกจัดการแพ็คเกจ
  const handleOpenPackagesDialog = async (user: UserType) => {
    setSelectedUser(user);
    setIsPackagesDialogOpen(true);

    try {
      // ดึงข้อมูลแพ็คเกจของผู้ใช้งาน
      const res = await apiRequest("GET", `/api/admin/users/${user.id}/packages`);
      const data = await res.json();
      setUserPackages(data);

      // รีเซ็ตฟอร์มเพิ่มแพ็คเกจ
      addPackageForm.reset({
        packageId: 0,
        durationMonths: 1,
        isActive: true
      });
    } catch (error) {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: "ไม่สามารถดึงข้อมูลแพ็คเกจได้",
        variant: "destructive"
      });
    }
  };

  // ฟังก์ชันบันทึกข้อมูลผู้ใช้
  const onSubmitEditForm = (values: EditUserFormValues) => {
    updateUserMutation.mutate(values);
  };

  // ฟังก์ชันบันทึกการเพิ่ม/หักเครดิต
  const onSubmitCreditForm = (values: CreditActionFormValues) => {
    if (creditAction === 'add') {
      addCreditMutation.mutate(values);
    } else {
      deductCreditMutation.mutate(values);
    }
  };

  // ฟังก์ชันบันทึกการเพิ่มแพ็คเกจ
  const onSubmitAddPackageForm = (values: AddPackageFormValues) => {
    addPackageMutation.mutate(values);
  };

  return (
    <Admin>
      <div className="container mx-auto py-8">
        <Card className="border-indigo-200 shadow-sm">
          <CardHeader className="bg-gradient-to-r from-indigo-50/60 to-indigo-100/40">
            <div className="flex justify-between items-center">
              <div>
                <CardTitle className="text-2xl font-bold text-indigo-800">จัดการผู้ใช้งาน</CardTitle>
                <CardDescription className="text-indigo-600">
                  ดูและจัดการข้อมูลผู้ใช้งานทั้งหมดในระบบ
                </CardDescription>
              </div>
              <div className="flex gap-2">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="ค้นหาผู้ใช้งาน..."
                    className="pl-10 w-[300px] border-indigo-200 focus:border-indigo-400"
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                  />
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="rounded-md border border-indigo-100 shadow-sm overflow-hidden">
              {isLoading ? (
                <div className="p-8 text-center">
                  <RefreshCw className="h-8 w-8 mx-auto animate-spin text-indigo-400" />
                  <p className="mt-2 text-muted-foreground">กำลังโหลดข้อมูล...</p>
                </div>
              ) : filteredUsers && filteredUsers.length > 0 ? (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader className="bg-indigo-50/50">
                      <TableRow>
                        <TableHead className="w-[100px]">ID</TableHead>
                        <TableHead>ชื่อผู้ใช้</TableHead>
                        <TableHead>อีเมล</TableHead>
                        <TableHead>ชื่อ</TableHead>
                        <TableHead>สถานะ</TableHead>
                        <TableHead>บทบาท</TableHead>
                        <TableHead>สมัครเมื่อ</TableHead>
                        <TableHead className="text-right">ดำเนินการ</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredUsers.map((user) => (
                        <TableRow key={user.id} className="hover:bg-indigo-50/30">
                          <TableCell className="font-medium">{user.id}</TableCell>
                          <TableCell>{user.username}</TableCell>
                          <TableCell>{user.email}</TableCell>
                          <TableCell>{user.firstName} {user.lastName}</TableCell>
                          <TableCell>
                            <span
                              className={`px-2 py-1 rounded-full text-xs ${
                                user.status === "active"
                                  ? "bg-green-100 text-green-700"
                                  : user.status === "inactive"
                                  ? "bg-gray-100 text-gray-700"
                                  : "bg-red-100 text-red-700"
                              }`}
                            >
                              {user.status === "active" ? "ใช้งาน" : user.status === "inactive" ? "ไม่ได้ใช้งาน" : "ระงับการใช้งาน"}
                            </span>
                          </TableCell>
                          <TableCell>
                            <span
                              className={`px-2 py-1 rounded-full text-xs ${
                                user.role === "admin"
                                  ? "bg-indigo-100 text-indigo-700"
                                  : "bg-blue-100 text-blue-700"
                              }`}
                            >
                              {user.role === "admin" ? "ผู้ดูแลระบบ" : "ผู้ใช้งานทั่วไป"}
                            </span>
                          </TableCell>
                          <TableCell className="text-indigo-600">{formatDate(new Date(user.createdAt))}</TableCell>
                          <TableCell>
                            <div className="flex justify-end gap-2">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="hover:bg-green-100 text-green-600 hover:text-green-700"
                                  >
                                    <CreditCard className="h-4 w-4" />
                                    <span className="sr-only">จัดการเครดิต</span>
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end" className="w-48 border-green-100">
                                  <DropdownMenuLabel className="text-green-700">จัดการเครดิต</DropdownMenuLabel>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    className="flex items-center cursor-pointer text-green-600 hover:text-green-700 focus:text-green-700"
                                    onClick={() => handleOpenCreditDialog(user, 'add')}
                                  >
                                    <Plus className="mr-2 h-4 w-4" />
                                    <span>เพิ่มเครดิต</span>
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    className="flex items-center cursor-pointer text-orange-600 hover:text-orange-700 focus:text-orange-700"
                                    onClick={() => handleOpenCreditDialog(user, 'deduct')}
                                  >
                                    <Minus className="mr-2 h-4 w-4" />
                                    <span>หักเครดิต</span>
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleOpenPackagesDialog(user)}
                                className="hover:bg-blue-100 text-blue-600 hover:text-blue-700"
                              >
                                <Package className="h-4 w-4" />
                                <span className="sr-only">จัดการแพ็คเกจ</span>
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleOpenEditDialog(user)}
                                className="hover:bg-indigo-100 text-indigo-600 hover:text-indigo-700"
                              >
                                <Edit className="h-4 w-4" />
                                <span className="sr-only">แก้ไข</span>
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleOpenDeleteDialog(user)}
                                className="hover:bg-red-100 text-red-500 hover:text-red-600"
                              >
                                <Trash className="h-4 w-4" />
                                <span className="sr-only">ลบ</span>
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="py-8 text-center">
                  <p className="text-muted-foreground">ไม่พบข้อมูลผู้ใช้งาน</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* ไดอะล็อกแก้ไขข้อมูลผู้ใช้ */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[525px] border border-indigo-200 bg-gradient-to-b from-white to-indigo-50/20">
          <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-indigo-500/50 via-purple-500/50 to-indigo-500/50"></div>
          <DialogHeader>
            <DialogTitle className="flex items-center text-indigo-700">
              <div className="p-1.5 rounded-full bg-gradient-to-r from-indigo-100 to-indigo-200 mr-2 flex items-center justify-center">
                <UserCog className="h-5 w-5 text-indigo-600" />
              </div>
              <span>แก้ไขข้อมูลผู้ใช้งาน</span>
            </DialogTitle>
            <DialogDescription className="text-indigo-600/70">
              แก้ไขข้อมูลของผู้ใช้งาน <span className="font-semibold text-indigo-700">{selectedUser?.username}</span>
            </DialogDescription>
          </DialogHeader>
          <Form {...editUserForm}>
            <form onSubmit={editUserForm.handleSubmit(onSubmitEditForm)} className="space-y-4">
              <FormField
                control={editUserForm.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>ชื่อผู้ใช้</FormLabel>
                    <FormControl>
                      <Input placeholder="ชื่อผู้ใช้" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editUserForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>อีเมล</FormLabel>
                    <FormControl>
                      <Input type="email" placeholder="อีเมล" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={editUserForm.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>ชื่อ</FormLabel>
                      <FormControl>
                        <Input placeholder="ชื่อ" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={editUserForm.control}
                  name="lastName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>นามสกุล</FormLabel>
                      <FormControl>
                        <Input placeholder="นามสกุล" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsEditDialogOpen(false)}
                  className="border-indigo-200 hover:bg-indigo-50 hover:text-indigo-700"
                >
                  ยกเลิก
                </Button>
                <Button
                  type="submit"
                  disabled={updateUserMutation.isPending}
                  className="bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white"
                >
                  {updateUserMutation.isPending && (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  )}
                  บันทึกข้อมูล
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* ไดอะล็อกลบผู้ใช้งาน */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px] border border-red-200 bg-gradient-to-b from-white to-red-50/20">
          <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-red-500/50 via-red-400/50 to-red-500/50"></div>
          <DialogHeader>
            <DialogTitle className="flex items-center text-red-700">
              <div className="p-1.5 rounded-full bg-gradient-to-r from-red-100 to-red-200 mr-2 flex items-center justify-center">
                <Trash className="h-5 w-5 text-red-600" />
              </div>
              <span>ยืนยันการลบผู้ใช้งาน</span>
            </DialogTitle>
            <DialogDescription className="text-red-600/70">
              คุณต้องการลบผู้ใช้งาน <span className="font-semibold text-red-700">{selectedUser?.username}</span> หรือไม่?
            </DialogDescription>
          </DialogHeader>
          <div className="p-4 rounded-lg bg-red-50/50 border border-red-200/50 text-red-600">
            <AlertCircle className="h-5 w-5 mr-2 text-red-500 inline-block" />
            เมื่อลบแล้วจะไม่สามารถกู้คืนข้อมูลได้
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              className="border-red-200 hover:bg-red-50 hover:text-red-700"
            >
              ยกเลิก
            </Button>
            <Button
              onClick={() => deleteUserMutation.mutate()}
              disabled={deleteUserMutation.isPending}
              className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white"
            >
              {deleteUserMutation.isPending && (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              )}
              ยืนยันการลบ
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* ไดอะล็อกเพิ่ม/หักเครดิต */}
      <Dialog open={isCreditDialogOpen} onOpenChange={setIsCreditDialogOpen}>
        <DialogContent className={`sm:max-w-[425px] border ${creditAction === 'add' ? 'border-green-200 bg-gradient-to-b from-white to-green-50/20' : 'border-orange-200 bg-gradient-to-b from-white to-orange-50/20'}`}>
          <div className={`absolute top-0 left-0 right-0 h-1 bg-gradient-to-r ${creditAction === 'add' ? 'from-green-500/50 via-green-400/50 to-green-500/50' : 'from-orange-500/50 via-orange-400/50 to-orange-500/50'}`}></div>
          <DialogHeader>
            <DialogTitle className={`flex items-center ${creditAction === 'add' ? 'text-green-700' : 'text-orange-700'}`}>
              <div className={`p-1.5 rounded-full mr-2 flex items-center justify-center ${creditAction === 'add' ? 'bg-gradient-to-r from-green-100 to-green-200' : 'bg-gradient-to-r from-orange-100 to-orange-200'}`}>
                {creditAction === 'add' ? (
                  <PlusCircle className="h-5 w-5 text-green-600" />
                ) : (
                  <Minus className="h-5 w-5 text-orange-600" />
                )}
              </div>
              <span>{creditAction === 'add' ? 'เพิ่มเครดิต' : 'หักเครดิต'}</span>
            </DialogTitle>
            <DialogDescription className={creditAction === 'add' ? 'text-green-600/70' : 'text-orange-600/70'}>
              {creditAction === 'add' ? 'เพิ่มเครดิตให้กับผู้ใช้งาน' : 'หักเครดิตจากผู้ใช้งาน'} <span className="font-semibold">{selectedUser?.username}</span>
            </DialogDescription>
          </DialogHeader>
          <Form {...creditForm}>
            <form onSubmit={creditForm.handleSubmit(onSubmitCreditForm)} className="space-y-4">
              <FormField
                control={creditForm.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>จำนวนเครดิต</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="จำนวนเครดิต"
                        min="1"
                        {...field}
                        className={`focus:ring-2 ${creditAction === 'add' ? 'focus:ring-green-200 focus:border-green-300' : 'focus:ring-orange-200 focus:border-orange-300'}`}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={creditForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>คำอธิบาย (ไม่บังคับ)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="คำอธิบายการเพิ่ม/หักเครดิต"
                        className={`resize-none focus:ring-2 ${creditAction === 'add' ? 'focus:ring-green-200 focus:border-green-300' : 'focus:ring-orange-200 focus:border-orange-300'}`}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsCreditDialogOpen(false)}
                  className={`${creditAction === 'add' ? 'border-green-200 hover:bg-green-50 hover:text-green-700' : 'border-orange-200 hover:bg-orange-50 hover:text-orange-700'}`}
                >
                  ยกเลิก
                </Button>
                <Button
                  type="submit"
                  disabled={creditAction === 'add' ? addCreditMutation.isPending : deductCreditMutation.isPending}
                  className={`text-white ${
                    creditAction === 'add'
                      ? 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700'
                      : 'bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700'
                  }`}
                >
                  {(creditAction === 'add' ? addCreditMutation.isPending : deductCreditMutation.isPending) && (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  )}
                  {creditAction === 'add' ? 'เพิ่มเครดิต' : 'หักเครดิต'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      {/* ไดอะล็อกจัดการแพ็คเกจ */}
      <Dialog open={isPackagesDialogOpen} onOpenChange={setIsPackagesDialogOpen}>
        <DialogContent className="sm:max-w-[650px] border border-blue-200 bg-gradient-to-b from-white to-blue-50/20">
          <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500/50 via-indigo-500/50 to-blue-500/50"></div>
          <DialogHeader>
            <DialogTitle className="flex items-center text-blue-700">
              <div className="p-1.5 rounded-full bg-gradient-to-r from-blue-100 to-blue-200 mr-2 flex items-center justify-center">
                <Package className="h-5 w-5 text-blue-600" />
              </div>
              <span>จัดการแพ็คเกจผู้ใช้งาน</span>
            </DialogTitle>
            <DialogDescription className="text-blue-600/70">
              จัดการแพ็คเกจสำหรับผู้ใช้งาน <span className="font-semibold text-blue-700">{selectedUser?.username}</span>
            </DialogDescription>
          </DialogHeader>

          <div className="border border-blue-100 rounded-md p-4 bg-blue-50/40 mb-4">
            <h3 className="text-sm font-semibold text-blue-700 mb-2">แพ็คเกจของผู้ใช้งานในปัจจุบัน</h3>
            {userPackages.length > 0 ? (
              <div className="space-y-3">
                {userPackages.map((userPackage) => (
                  <div key={userPackage.id} className="bg-white p-3 rounded-md border border-blue-100 shadow-sm">
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="text-sm font-medium text-blue-800">{userPackage.package.name}</h4>
                        <div className="mt-1 flex flex-wrap gap-x-4 gap-y-1 text-xs text-gray-600">
                          <span className="flex items-center">
                            <CalendarIcon className="h-3 w-3 mr-1 text-blue-500" />
                            เริ่มใช้: {formatDate(userPackage.startDate, "dd MMM yyyy")}
                          </span>
                          <span className="flex items-center">
                            <Clock className="h-3 w-3 mr-1 text-blue-500" />
                            สิ้นสุด: {formatDate(userPackage.endDate, "dd MMM yyyy")}
                          </span>
                          <span className="flex items-center">
                            <RefreshCcw className="h-3 w-3 mr-1 text-blue-500" />
                            ใช้ไปแล้ว: {userPackage.requestsUsed}/{userPackage.package.requestsLimit || 0} ครั้ง
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => resetPackageUsageMutation.mutate(userPackage.id)}
                          disabled={resetPackageUsageMutation.isPending}
                          className="h-7 px-2 text-xs border-blue-200 hover:bg-blue-50 hover:text-blue-700"
                        >
                          <RefreshCw className="h-3 w-3 mr-1" />
                          รีเซ็ต
                        </Button>
                        <Button
                          variant={userPackage.isActive ? "outline" : "default"}
                          size="sm"
                          onClick={() => togglePackageStatusMutation.mutate({
                            packageId: userPackage.id,
                            isActive: !userPackage.isActive
                          })}
                          disabled={togglePackageStatusMutation.isPending}
                          className={`h-7 px-2 text-xs ${
                            userPackage.isActive
                              ? "border-orange-200 hover:bg-orange-50 hover:text-orange-700"
                              : "bg-green-600 hover:bg-green-700 text-white"
                          }`}
                        >
                          {userPackage.isActive ? (
                            <>
                              <Minus className="h-3 w-3 mr-1" />
                              ระงับ
                            </>
                          ) : (
                            <>
                              <Check className="h-3 w-3 mr-1" />
                              เปิดใช้งาน
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                    <div className={`mt-2 py-1 px-2 rounded-full text-xs inline-flex items-center ${
                      userPackage.isActive
                        ? "bg-green-100 text-green-700"
                        : "bg-gray-100 text-gray-700"
                    }`}>
                      {userPackage.isActive ? (
                        <>
                          <Check className="h-3 w-3 mr-1" />
                          กำลังใช้งาน
                        </>
                      ) : (
                        <>
                          <Minus className="h-3 w-3 mr-1" />
                          ระงับการใช้งาน
                        </>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-sm text-gray-500">
                ผู้ใช้รายนี้ยังไม่มีแพ็คเกจ
              </div>
            )}
          </div>

          <div className="border border-blue-100 rounded-md p-4 bg-blue-50/40">
            <h3 className="text-sm font-semibold text-blue-700 mb-2">เพิ่มแพ็คเกจใหม่</h3>
            <Form {...addPackageForm}>
              <form onSubmit={addPackageForm.handleSubmit(onSubmitAddPackageForm)} className="space-y-4">
                <FormField
                  control={addPackageForm.control}
                  name="packageId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>เลือกแพ็คเกจ</FormLabel>
                      <Select
                        onValueChange={(value) => field.onChange(parseInt(value))}
                        defaultValue={field.value.toString()}
                      >
                        <FormControl>
                          <SelectTrigger className="focus:ring-2 focus:ring-blue-200 focus:border-blue-300">
                            <SelectValue placeholder="เลือกแพ็คเกจ" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {packagesList?.map((pkg) => (
                            <SelectItem key={pkg.id} value={pkg.id.toString()}>
                              {pkg.name} ({formatCurrency(pkg.price)})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={addPackageForm.control}
                  name="durationMonths"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>ระยะเวลา (เดือน)</FormLabel>
                      <Select
                        onValueChange={(value) => field.onChange(parseInt(value))}
                        defaultValue={field.value.toString()}
                      >
                        <FormControl>
                          <SelectTrigger className="focus:ring-2 focus:ring-blue-200 focus:border-blue-300">
                            <SelectValue placeholder="เลือกระยะเวลา" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="1">1 เดือน</SelectItem>
                          <SelectItem value="3">3 เดือน</SelectItem>
                          <SelectItem value="6">6 เดือน</SelectItem>
                          <SelectItem value="12">12 เดือน</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription className="text-xs text-blue-600">
                        ส่วนลดจะถูกคำนวณอัตโนมัติตามระยะเวลา
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={addPackageForm.control}
                  name="isActive"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border border-blue-100 p-3">
                      <div className="space-y-0.5">
                        <FormLabel>สถานะการใช้งาน</FormLabel>
                        <FormDescription className="text-xs">
                          เปิดใช้งานแพ็คเกจนี้ทันทีหลังจากเพิ่ม
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <DialogFooter className="mt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsPackagesDialogOpen(false)}
                    className="border-blue-200 hover:bg-blue-50 hover:text-blue-700"
                  >
                    ปิด
                  </Button>
                  <Button
                    type="submit"
                    disabled={
                      addPackageForm.getValues().packageId === 0 ||
                      addPackageMutation.isPending
                    }
                    className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white"
                  >
                    {addPackageMutation.isPending && (
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    )}
                    เพิ่มแพ็คเกจ
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </div>
        </DialogContent>
      </Dialog>
    </Admin>
  );
}