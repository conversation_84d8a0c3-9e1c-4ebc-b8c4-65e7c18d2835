import { Express, Request, Response, Router } from "express";
import { storage } from "../storage";
import { isAdmin } from "../auth";
import fs from 'fs';
import path from 'path';
import { db } from "../db";
import { eq, gte, lte, desc, sql } from "drizzle-orm";
import { slipVerifications, apiKeys } from "@shared/schema";

const router = Router();

// API endpoint สำหรับดึงรายการตรวจสอบสลิปทั้งหมด (สำหรับแอดมิน) หรือเฉพาะของผู้ใช้ (สำหรับผู้ใช้ทั่วไป)
// รองรับการกรองข้อมูลด้วย query parameters
router.get('/verifications-fixed', async (req: Request, res: Response) => {
  try {
    // ตรวจสอบสถานะการล็อกอิน
    if (!req.isAuthenticated()) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const isUserAdmin = isAdmin(req);
    
    // แสดงข้อมูลการกรองเพื่อการดีบัก
    console.log('Search filters (RAW):', req.query);
    
    // ตรวจสอบว่ามีพารามิเตอร์การค้นหาหรือไม่
    const {
      search,
      bank,
      status,
      startDate,
      endDate,
      minAmount,
      maxAmount,
      source
    } = req.query;
    
    // ดีบักค่าพารามิเตอร์
    console.log('Parsed search filters:', { 
      search: search || 'null', 
      bank: bank || 'null', 
      status: status || 'null', 
      startDate: startDate || 'null', 
      endDate: endDate || 'null', 
      minAmount: minAmount || 'null', 
      maxAmount: maxAmount || 'null', 
      source: source || 'null'
    });

    // ตัวแปรสำหรับเก็บข้อมูลที่จะส่งกลับ
    let results;
    
    // ดึงข้อมูลจากฐานข้อมูลโดยตรงแทนการดึงทั้งหมดมาแล้วกรอง
    // ฟังก์ชันเสริมสำหรับกรองข้อมูลโดยใช้ฐานข้อมูล
    const filterVerificationsFromDB = async (userId: number | null) => {
      const query = db
        .select()
        .from(slipVerifications)
        .leftJoin(apiKeys, eq(slipVerifications.apiKeyId, apiKeys.id));
      
      // กรองตามผู้ใช้ (ยกเว้นแอดมินดูได้ทั้งหมด)
      if (!isUserAdmin && userId) {
        query.where(eq(slipVerifications.userId, userId));
      }
      
      // กรองตามสถานะ
      if (status && typeof status === 'string' && status !== 'all') {
        const statusValue = status.toLowerCase();
        if (statusValue === 'error') {
          query.where(eq(slipVerifications.status, 'failed'));
        } else {
          query.where(eq(slipVerifications.status, statusValue));
        }
      }
      
      // กรองตามธนาคาร
      if (bank && typeof bank === 'string' && bank !== 'all') {
        let bankSearch = bank;
        // แปลงชื่อย่อธนาคารให้เป็นชื่อเต็มภาษาไทย
        const bankMapping: Record<string, string> = {
          'KBank': 'ธนาคารกสิกรไทย',
          'SCB': 'ธนาคารไทยพาณิชย์',
          'BBL': 'ธนาคารกรุงเทพ',
          'BAY': 'ธนาคารกรุงศรีอยุธยา',
          'TTB': 'ธนาคารทหารไทยธนชาต',
          'KTB': 'ธนาคารกรุงไทย',
          'GSB': 'ธนาคารออมสิน',
          'BAAC': 'ธนาคารเพื่อการเกษตรและสหกรณ์การเกษตร',
          'GHB': 'ธนาคารอาคารสงเคราะห์',
          'TISCO': 'ธนาคารทิสโก้',
          'TBank': 'ธนาคารธนชาต',
          'CIMB': 'ธนาคารซีไอเอ็มบี',
          'UOB': 'ธนาคารยูโอบี',
          'LHBANK': 'ธนาคารแลนด์แอนด์เฮ้าส์',
          'KBANK': 'ธนาคารกสิกรไทย'
        };
        
        if (bankMapping[bank]) {
          bankSearch = bankMapping[bank];
        }
        
        query.where(sql`LOWER(${slipVerifications.bankName}) LIKE LOWER(${'%' + bankSearch + '%'})`);
      }
      
      // กรองตามคำค้นหา
      if (search && typeof search === 'string') {
        const searchLower = '%' + search.toLowerCase() + '%';
        
        // ค้นหาให้ครอบคลุมทุกฟิลด์ที่เกี่ยวข้อง
        query.where(
          sql`(
            LOWER(COALESCE(${slipVerifications.transactionRef}, '')) LIKE LOWER(${searchLower}) OR
            LOWER(COALESCE(${slipVerifications.sender}, '')) LIKE LOWER(${searchLower}) OR
            LOWER(COALESCE(${slipVerifications.receiver}, '')) LIKE LOWER(${searchLower}) OR
            LOWER(COALESCE(${slipVerifications.bankName}, '')) LIKE LOWER(${searchLower})
          )`
        );
      }
      
      // กรองตามวันที่
      if (startDate && typeof startDate === 'string') {
        query.where(gte(slipVerifications.createdAt, new Date(startDate)));
      }
      
      if (endDate && typeof endDate === 'string') {
        const to = new Date(endDate);
        to.setDate(to.getDate() + 1); // เพิ่ม 1 วันเพื่อให้รวมถึงวันสุดท้ายด้วย
        query.where(lte(slipVerifications.createdAt, to));
      }
      
      // กรองตามจำนวนเงิน
      if (minAmount !== undefined && minAmount !== null) {
        const min = parseFloat(minAmount as string);
        if (!isNaN(min)) {
          query.where(gte(slipVerifications.amount, min));
        }
      }
      
      if (maxAmount !== undefined && maxAmount !== null) {
        const max = parseFloat(maxAmount as string);
        if (!isNaN(max)) {
          query.where(lte(slipVerifications.amount, max));
        }
      }
      
      // กรองตามแหล่งที่มา
      if (source && typeof source === 'string' && source !== 'all') {
        query.where(eq(slipVerifications.verificationSource, source));
      }
      
      // เรียงลำดับข้อมูล
      query.orderBy(desc(slipVerifications.createdAt));
      
      // ดึงข้อมูล
      return await query;
    };
    
    // ดึงข้อมูลจากฐานข้อมูล
    const userId = req.user?.id || 0;
    const filteredResults = await filterVerificationsFromDB(userId);
    
    // แปลงข้อมูลให้อยู่ในรูปแบบที่ต้องการ
    results = filteredResults.map(row => ({
      ...row.slip_verifications,
      apiKeyInfo: row.api_keys || null
    }));
    
    console.log(`Found ${results.length} verifications after filtering`);
    
    // ตรวจสอบและแก้ไข URL ของรูปภาพสลิป
    results = results.map(verification => {
      // ถ้ามี imagePath แต่ไม่ได้เริ่มต้นด้วย http หรือ https
      if (verification.imagePath && !verification.imagePath.startsWith('http')) {
        if (verification.imagePath.startsWith('/uploads/')) {
          // ถ้าเป็น path ที่ถูกต้องแล้ว ไม่ต้องแก้ไข
          return verification;
        }
        
        // ตรวจสอบว่าไฟล์มีอยู่จริงหรือไม่
        const fullPath = path.join(process.cwd(), 'public', verification.imagePath);
        try {
          if (fs.existsSync(fullPath)) {
            // หากพบไฟล์ให้ใช้ path นั้น
            return verification;
          } else {
            // หากไม่พบไฟล์ ให้ลองตรวจสอบใน uploads/slips
            const slipPath = path.join(process.cwd(), 'public', 'uploads', 'slips', path.basename(verification.imagePath));
            if (fs.existsSync(slipPath)) {
              const newPath = `/uploads/slips/${path.basename(verification.imagePath)}`;
              verification.imagePath = newPath;
            } else {
              // ถ้าไม่พบไฟล์ในทั้งสองที่ ให้เป็น null
              verification.imagePath = null;
            }
          }
        } catch (err) {
          console.error('Error checking image path:', err);
          verification.imagePath = null;
        }
      }
      return verification;
    });
    
    res.json(results);
  } catch (error) {
    console.error('Error fetching verifications:', error);
    res.status(500).json({ error: 'Failed to fetch verifications' });
  }
});

// API endpoint สำหรับสถิติ
router.get('/verifications/stats', async (req: Request, res: Response) => {
  try {
    // ตรวจสอบสถานะการล็อกอิน
    if (!req.isAuthenticated()) {
      return res.status(401).json({ error: 'Unauthorized' });
    }
    
    const isUserAdmin = isAdmin(req);
    
    // ดึงข้อมูลทั้งหมดก่อน (ในกรณีที่เป็นแอดมิน) หรือข้อมูลเฉพาะผู้ใช้ (ในกรณีที่เป็นผู้ใช้ทั่วไป)
    let verifications;
    if (isUserAdmin) {
      verifications = await storage.listAllSlipVerifications();
    } else {
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({ error: 'Cannot identify user' });
      }
      verifications = await storage.listUserSlipVerifications(userId);
    }
    
    // คำนวณสถิติ
    const total = verifications.length;
    
    // คำนวณยอดเงินรวม
    const totalAmount = verifications.reduce((sum, v) => sum + (v.amount || 0), 0);
    
    // คำนวณยอดเงินเฉลี่ย
    const averageAmount = total > 0 ? totalAmount / total : 0;
    
    // จำนวนรายการตามสถานะ
    const successCount = verifications.filter(v => v.status?.toLowerCase() === 'success').length;
    const errorCount = verifications.filter(v => ['error', 'failed'].includes(v.status?.toLowerCase() || '')).length;
    const pendingCount = verifications.filter(v => v.status?.toLowerCase() === 'pending').length;
    
    // คำนวณอัตราความสำเร็จ
    const successRate = total > 0 ? (successCount / total) * 100 : 0;
    
    // จัดกลุ่มตามธนาคาร
    const bankGroups: Record<string, number> = {};
    verifications.forEach(v => {
      const bank = v.bankName || 'unknown';
      bankGroups[bank] = (bankGroups[bank] || 0) + 1;
    });
    
    // จัดกลุ่มตามวันที่
    const dateGroups: Record<string, number> = {};
    verifications.forEach(v => {
      if (!v.createdAt) return;
      const date = new Date(v.createdAt);
      const dateStr = date.toISOString().split('T')[0]; // แปลงเป็น YYYY-MM-DD
      dateGroups[dateStr] = (dateGroups[dateStr] || 0) + 1;
    });
    
    // จัดกลุ่มตามช่วงเวลาของวัน
    const hourlyGroups: Record<string, number> = {};
    verifications.forEach(v => {
      if (!v.createdAt) return;
      const date = new Date(v.createdAt);
      const hour = date.getHours(); // 0-23
      const hourStr = `${hour < 10 ? '0' : ''}${hour}:00`;
      hourlyGroups[hourStr] = (hourlyGroups[hourStr] || 0) + 1;
    });
    
    // หาธนาคารที่มีการโอนมากที่สุด
    let topBank: { name: string; count: number } | null = null;
    Object.entries(bankGroups).forEach(([name, count]) => {
      if (!topBank || count > topBank.count) {
        topBank = { name, count };
      }
    });
    
    // คำนวณมูลค่ารวมแยกตามธนาคาร
    const bankAmounts: Record<string, number> = {};
    verifications.forEach(v => {
      if (!v.bankName || !v.amount) return;
      const bank = v.bankName;
      bankAmounts[bank] = (bankAmounts[bank] || 0) + v.amount;
    });
    
    // หา 3 ช่วงเวลาที่มีการใช้งานมากที่สุด
    const mostActiveHours = Object.entries(hourlyGroups)
      .map(([hour, count]) => ({ hour, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 3);
    
    // ส่งข้อมูลสถิติกลับไป
    res.json({
      total,
      totalAmount,
      averageAmount,
      successCount,
      errorCount,
      pendingCount,
      successRate,
      bankGroups,
      dateGroups,
      hourlyGroups,
      topBank,
      bankAmounts,
      mostActiveHours
    });
  } catch (error) {
    console.error('Error calculating verification stats:', error);
    res.status(500).json({ error: 'Failed to calculate verification stats' });
  }
});

// ฟังก์ชันสำหรับเพิ่ม routes เข้าไปใน Express app
export function setupAdvancedSearchFixRoutes(app: Express) {
  app.use('/api', router);
}

export default setupAdvancedSearchFixRoutes;