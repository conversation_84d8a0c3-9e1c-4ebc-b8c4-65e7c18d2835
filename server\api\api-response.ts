import { Response } from 'express';
import { storage } from '../storage';

// ฟังก์ชันสำหรับดึง template และสร้าง API response ตาม status code
export async function createApiResponse(
  res: Response, 
  statusCode: number, 
  data?: any, 
  message?: string, 
  additionalFields?: Record<string, any>
) {
  try {
    // ดึง template จากฐานข้อมูล
    const template = await storage.getApiResponseTemplateByStatusCode(statusCode);
    
    let responseObj: any = {
      status: statusCode >= 200 && statusCode < 300 ? 'success' : 'error',
      message: message || (statusCode >= 200 && statusCode < 300 
        ? 'การดำเนินการเสร็จสมบูรณ์' 
        : 'เกิดข้อผิดพลาด'),
      time: new Date().toISOString()
    };
    
    // ถ้ามี template ให้ใช้ template นั้น
    if (template) {
      const templateData = template.template as Record<string, any>;
      responseObj = {
        ...templateData,
        time: new Date().toISOString(),
      };
      
      // ถ้ามี message ส่งมาให้ใช้ message ที่ส่งมา
      if (message) {
        responseObj.message = message;
      }
    }
    
    // เพิ่มข้อมูลเพิ่มเติม (ถ้ามี)
    if (additionalFields) {
      responseObj = {
        ...responseObj,
        ...additionalFields
      };
    }
    
    // เพิ่มข้อมูลหลัก (ถ้ามี)
    if (data !== undefined) {
      responseObj.data = data;
    }
    
    return res.status(statusCode).json(responseObj);
  } catch (error) {
    console.error('Error creating API response:', error);
    
    // กรณีเกิดข้อผิดพลาดในการสร้าง response ให้ใช้รูปแบบเริ่มต้น
    return res.status(statusCode).json({
      status: statusCode >= 200 && statusCode < 300 ? 'success' : 'error',
      message: message || (statusCode >= 200 && statusCode < 300 
        ? 'การดำเนินการเสร็จสมบูรณ์' 
        : 'เกิดข้อผิดพลาด'),
      time: new Date().toISOString(),
      data: data
    });
  }
}

// ฟังก์ชันลัดสำหรับสร้าง response ทั่วไป
export const apiResponse = {
  success: (res: Response, data?: any, message?: string, additionalFields?: Record<string, any>) => {
    return createApiResponse(res, 200, data, message || 'การดำเนินการเสร็จสมบูรณ์', additionalFields);
  },
  
  created: (res: Response, data?: any, message?: string, additionalFields?: Record<string, any>) => {
    return createApiResponse(res, 201, data, message || 'สร้างข้อมูลเรียบร้อยแล้ว', additionalFields);
  },
  
  badRequest: (res: Response, message?: string, data?: any, additionalFields?: Record<string, any>) => {
    return createApiResponse(res, 400, data, message || 'คำขอไม่ถูกต้อง', additionalFields);
  },
  
  unauthorized: (res: Response, message?: string, additionalFields?: Record<string, any>) => {
    return createApiResponse(res, 401, undefined, message || 'ไม่ได้รับอนุญาต', additionalFields);
  },
  
  forbidden: (res: Response, message?: string, additionalFields?: Record<string, any>) => {
    return createApiResponse(res, 403, undefined, message || 'ไม่มีสิทธิ์เข้าถึง', additionalFields);
  },
  
  notFound: (res: Response, message?: string, additionalFields?: Record<string, any>) => {
    return createApiResponse(res, 404, undefined, message || 'ไม่พบข้อมูล', additionalFields);
  },
  
  conflict: (res: Response, message?: string, data?: any, additionalFields?: Record<string, any>) => {
    return createApiResponse(res, 409, data, message || 'ข้อมูลมีความขัดแย้ง', additionalFields);
  },
  
  tooManyRequests: (res: Response, message?: string, additionalFields?: Record<string, any>) => {
    return createApiResponse(res, 429, undefined, message || 'คำขอมากเกินไป', additionalFields);
  },
  
  serverError: (res: Response, message?: string, additionalFields?: Record<string, any>) => {
    return createApiResponse(res, 500, undefined, message || 'เกิดข้อผิดพลาดบนเซิร์ฟเวอร์', additionalFields);
  }
};