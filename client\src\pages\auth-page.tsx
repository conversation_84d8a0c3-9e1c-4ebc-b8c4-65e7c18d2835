import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Navbar } from "@/components/layouts/navbar";
import { Footer } from "@/components/layouts/footer";
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/hooks/use-auth";
import { insertUserSchema } from "@shared/schema";
import { 
  ReceiptText, 
  Building, 
  ExternalLink, 
  Check,
  Loader2,
  User,
  Mail,
  Phone,
  Lock
} from "lucide-react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { toast } from "@/hooks/use-toast";
import axios from "axios";

// กำหนด Schema สำหรับการเข้าสู่ระบบ
const loginSchema = z.object({
  username: z.string().min(1, "กรุณากรอกชื่อผู้ใช้"),
  password: z.string().min(1, "กรุณากรอกรหัสผ่าน"),
});

// กำหนด Schema สำหรับการลงทะเบียน
const registerBaseSchema = insertUserSchema.extend({
  password: z.string().min(8, "รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร"),
  confirmPassword: z.string(),
  verificationMethod: z.enum(["email", "phone"]),
}).refine((data) => data.password === data.confirmPassword, {
  message: "รหัสผ่านไม่ตรงกัน",
  path: ["confirmPassword"],
});

type LoginFormValues = z.infer<typeof loginSchema>;
type RegisterFormValues = z.infer<typeof registerBaseSchema>;

export default function AuthPage() {
  const [activeTab, setActiveTab] = useState<"login" | "register" | "verify">("login");
  const [location, setLocation] = useLocation();
  const { user, loginMutation, registerMutation } = useAuth();
  const [registrationStep, setRegistrationStep] = useState<"form" | "verification">("form");
  const [verificationMethod, setVerificationMethod] = useState<"email" | "phone">("email");
  const [verificationCode, setVerificationCode] = useState("");
  const [isVerifying, setIsVerifying] = useState(false);
  const [secondsLeft, setSecondsLeft] = useState(0);
  const [tempUserData, setTempUserData] = useState<any>(null);
  const [userVerificationStatus, setUserVerificationStatus] = useState<{
    email_verified?: boolean;
    phone_verified?: boolean;
  }>({});

  // สร้าง Form สำหรับการเข้าสู่ระบบ
  const loginForm = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: "",
      password: "",
    },
  });

  // สร้าง Form สำหรับการลงทะเบียน
  const registerForm = useForm<RegisterFormValues>({
    resolver: zodResolver(registerBaseSchema),
    defaultValues: {
      username: "",
      email: "",
      phoneNumber: "",
      firstName: "",
      lastName: "",
      companyName: "",
      password: "",
      confirmPassword: "",
      verificationMethod: "email",
    },
  });

  // เมื่อเปลี่ยนวิธีการยืนยัน
  useEffect(() => {
    const subscription = registerForm.watch((value, { name }) => {
      if (name === "verificationMethod") {
        setVerificationMethod(value.verificationMethod as "email" | "phone");
      }
    });
    return () => subscription.unsubscribe();
  }, [registerForm.watch]);

  // ส่งคำขอเข้าสู่ระบบ
  const onLoginSubmit = (values: LoginFormValues) => {
    loginMutation.mutate(values);
  };

  // ส่งคำขอลงทะเบียนขั้นแรก - ส่งข้อมูลและรับรหัสยืนยัน
  const onRegisterSubmit = async (values: RegisterFormValues) => {
    try {
      // ลบ confirmPassword ออกเพราะไม่ต้องส่งไปยัง API
      const { confirmPassword, ...registerData } = values;
      
      // เก็บข้อมูลชั่วคราวสำหรับส่งเมื่อยืนยันเรียบร้อย
      setTempUserData(registerData);
      
      // ส่งคำขอรหัสยืนยัน
      const identifier = values.verificationMethod === "email" 
        ? values.email 
        : values.phoneNumber;
        
      // ส่ง API เพื่อขอรหัสยืนยัน
      const response = await axios.post("/api/verification/send", {
        type: values.verificationMethod,
        identifier,
        userId: 0, // ใช้ 0 สำหรับผู้ใช้ใหม่ที่ยังไม่มี ID
      });
      
      if (response.data.success) {
        toast({
          title: "ส่งรหัสยืนยันสำเร็จ",
          description: response.data.message,
        });
        
        // เปลี่ยนสถานะเป็นรอยืนยัน
        setRegistrationStep("verification");
        
        // ตั้งเวลาถอยหลังสำหรับการขอรหัสใหม่
        setSecondsLeft(120); // 2 นาที
      } else {
        toast({
          variant: "destructive",
          title: "ไม่สามารถส่งรหัสยืนยันได้",
          description: response.data.message || "กรุณาลองใหม่อีกครั้ง",
        });
      }
    } catch (error: any) {
      console.error("Registration error:", error);
      toast({
        variant: "destructive",
        title: "ไม่สามารถส่งรหัสยืนยันได้",
        description: error.response?.data?.message || "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง",
      });
    }
  };

  // เช็คสถานะการยืนยันบัญชีเมื่อเข้าสู่ระบบแล้ว
  useEffect(() => {
    if (user) {
      // ตรวจสอบว่าผู้ใช้ยืนยันอีเมลหรือเบอร์โทรแล้วหรือไม่
      const needVerification = !user.email_verified && !user.phone_verified;
      
      // ถ้ายังไม่ได้ยืนยัน ให้อยู่ที่หน้านี้และแสดงแท็บยืนยัน
      if (needVerification) {
        setUserVerificationStatus({
          email_verified: user.email_verified,
          phone_verified: user.phone_verified
        });
        setActiveTab("verify");
      } else {
        // ถ้ายืนยันแล้ว ไปที่หน้าแดชบอร์ด
        setLocation("/dashboard");
      }
    }
  }, [user, setLocation]);

  // นับเวลาถอยหลัง
  useEffect(() => {
    if (secondsLeft <= 0) return;
    
    const timer = setInterval(() => {
      setSecondsLeft(prev => prev - 1);
    }, 1000);
    
    return () => clearInterval(timer);
  }, [secondsLeft]);
  
  // ตรวจสอบรหัสยืนยัน
  const verifyCode = async () => {
    if (!verificationCode || verificationCode.length < 6) {
      toast({
        variant: "destructive",
        title: "รหัสยืนยันไม่ถูกต้อง",
        description: "กรุณากรอกรหัสยืนยัน 6 หลัก",
      });
      return;
    }
    
    try {
      setIsVerifying(true);
      const identifier = tempUserData.verificationMethod === "email" 
        ? tempUserData.email 
        : tempUserData.phoneNumber;
        
      // ส่ง API เพื่อตรวจสอบรหัสยืนยัน
      const verifyResponse = await axios.post("/api/verification/verify", {
        type: tempUserData.verificationMethod,
        identifier,
        code: verificationCode,
      });
      
      if (verifyResponse.data.success) {
        // ลงทะเบียนผู้ใช้หลังจากยืนยันสำเร็จ
        registerMutation.mutate(tempUserData);
      } else {
        toast({
          variant: "destructive",
          title: "รหัสยืนยันไม่ถูกต้อง",
          description: verifyResponse.data.message || "กรุณาตรวจสอบรหัสและลองใหม่อีกครั้ง",
        });
      }
    } catch (error: any) {
      console.error("Verification error:", error);
      toast({
        variant: "destructive",
        title: "ไม่สามารถยืนยันรหัสได้",
        description: error.response?.data?.message || "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง",
      });
    } finally {
      setIsVerifying(false);
    }
  };
  
  // ขอรหัสยืนยันใหม่
  const resendVerificationCode = async () => {
    if (secondsLeft > 0) return;
    
    try {
      // กรณีที่เป็นการสมัครใหม่
      if (tempUserData) {
        const identifier = tempUserData.verificationMethod === "email" 
          ? tempUserData.email 
          : tempUserData.phoneNumber;
          
        // ส่ง API เพื่อขอรหัสยืนยันใหม่
        const response = await axios.post("/api/verification/send", {
          type: tempUserData.verificationMethod,
          identifier,
          userId: 0,
        });
        
        if (response.data.success) {
          toast({
            title: "ส่งรหัสยืนยันใหม่สำเร็จ",
            description: response.data.message,
          });
          setSecondsLeft(120); // ตั้งเวลารอใหม่
        } else {
          toast({
            variant: "destructive",
            title: "ไม่สามารถส่งรหัสยืนยันได้",
            description: response.data.message || "กรุณาลองใหม่อีกครั้ง",
          });
        }
      } 
      // กรณีที่เป็นผู้ใช้เก่า
      else if (user) {
        const identifier = verificationMethod === "email" 
          ? user.email 
          : user.phoneNumber;
          
        // ส่ง API เพื่อขอรหัสยืนยันใหม่
        const response = await axios.post("/api/verification/send", {
          type: verificationMethod,
          identifier,
          userId: user.id,
        });
        
        if (response.data.success) {
          toast({
            title: "ส่งรหัสยืนยันใหม่สำเร็จ",
            description: response.data.message,
          });
          setSecondsLeft(120); // ตั้งเวลารอใหม่
        } else {
          toast({
            variant: "destructive",
            title: "ไม่สามารถส่งรหัสยืนยันได้",
            description: response.data.message || "กรุณาลองใหม่อีกครั้ง",
          });
        }
      }
    } catch (error: any) {
      console.error("Resend error:", error);
      toast({
        variant: "destructive",
        title: "ไม่สามารถส่งรหัสยืนยันได้",
        description: error.response?.data?.message || "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง",
      });
    }
  };
  
  // สำหรับการยืนยันบัญชีของผู้ใช้ที่มีอยู่แล้ว
  const verifyExistingAccount = async () => {
    if (!verificationCode || verificationCode.length < 6) {
      toast({
        variant: "destructive",
        title: "รหัสยืนยันไม่ถูกต้อง",
        description: "กรุณากรอกรหัสยืนยัน 6 หลัก",
      });
      return;
    }
    
    try {
      setIsVerifying(true);
      
      if (user) {
        const identifier = verificationMethod === "email" 
          ? user.email 
          : user.phoneNumber;
          
        // ส่ง API เพื่อตรวจสอบรหัสยืนยัน
        const verifyResponse = await axios.post("/api/verification/verify", {
          type: verificationMethod,
          identifier,
          code: verificationCode,
          userId: user.id
        });
        
        if (verifyResponse.data.success) {
          toast({
            title: "ยืนยันบัญชีสำเร็จ",
            description: "ขอบคุณที่ยืนยันข้อมูลของท่าน",
          });
          
          // อัพเดทสถานะการยืนยัน และเก็บในสเตทเพื่อแสดงผล
          if (verificationMethod === "email") {
            setUserVerificationStatus(prev => ({ ...prev, email_verified: true }));
          } else {
            setUserVerificationStatus(prev => ({ ...prev, phone_verified: true }));
          }
          
          // ถ้ายืนยันครบแล้ว ไปที่หน้าแดชบอร์ด
          if (
            (verificationMethod === "email" && userVerificationStatus.phone_verified) ||
            (verificationMethod === "phone" && userVerificationStatus.email_verified)
          ) {
            setTimeout(() => {
              setLocation("/dashboard");
            }, 1500);
          }
        } else {
          toast({
            variant: "destructive",
            title: "รหัสยืนยันไม่ถูกต้อง",
            description: verifyResponse.data.message || "กรุณาตรวจสอบรหัสและลองใหม่อีกครั้ง",
          });
        }
      }
    } catch (error: any) {
      console.error("Verification error:", error);
      toast({
        variant: "destructive",
        title: "ไม่สามารถยืนยันรหัสได้",
        description: error.response?.data?.message || "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง",
      });
    } finally {
      setIsVerifying(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-[#2e1d5b] bg-opacity-95 bg-[url('/images/stars-bg.png')] bg-repeat">
      <Navbar />

      <div className="flex-1 py-10">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="flex flex-col items-center">
              {/* ส่วนหัว */}
              <div className="mb-6 text-center">
                <h1 className="text-6xl font-bold text-yellow-400 mb-2 tracking-wide">SLIPKUY</h1>
                <p className="text-xl text-purple-200 mb-6">ระบบตรวจสอบสลิปแห่งเทพเจ้า</p>
                <p className="text-lg text-yellow-300 mb-4 flex items-center justify-center">
                  <span className="inline-block mr-2">☻</span>
                  <span>ท่านคือผู้ถูกเลือก... ให้ทรงพร้มนี้</span>
                </p>
                <div className="w-64 h-1 bg-gradient-to-r from-purple-300 to-indigo-600 mx-auto rounded-full"></div>
              </div>

              {/* ส่วนแบบฟอร์ม */}
              <div className="bg-gradient-to-b from-[#1c103b] to-[#2a1861] p-2 rounded-lg shadow-2xl w-full max-w-xl relative border border-purple-800 shadow-purple-900/50">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-lg z-0"></div>
                <div className="relative z-10 p-6">
                  <Tabs
                    defaultValue="login"
                    value={activeTab}
                    onValueChange={(value) => setActiveTab(value as "login" | "register" | "verify")}
                    className="w-full"
                  >
                    <TabsList className="grid grid-cols-3 mb-6 bg-slate-900/80 border border-purple-800">
                      <TabsTrigger value="login" className="data-[state=active]:bg-purple-800 data-[state=active]:text-white">
                        <div className="flex items-center">
                          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                          </svg>
                          <span className="whitespace-nowrap">เข้าสู่ระบบ</span>
                        </div>
                      </TabsTrigger>
                      <TabsTrigger value="register" className="data-[state=active]:bg-purple-800 data-[state=active]:text-white">
                        <div className="flex items-center">
                          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                          </svg>
                          <span className="whitespace-nowrap">สมัครใหม่</span>
                        </div>
                      </TabsTrigger>
                      <TabsTrigger value="verify" className="data-[state=active]:bg-purple-800 data-[state=active]:text-white">
                        <div className="flex items-center">
                          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                          </svg>
                          <span className="whitespace-nowrap">ยืนยันบัญชี</span>
                        </div>
                      </TabsTrigger>
                    </TabsList>

                  {/* แบบฟอร์มเข้าสู่ระบบ */}
                  <TabsContent value="login">
                    <Form {...loginForm}>
                      <form
                        onSubmit={loginForm.handleSubmit(onLoginSubmit)}
                        className="space-y-6 bg-slate-900 p-6 rounded-lg border border-purple-800 shadow-xl"
                      >
                        <div className="text-center mb-4">
                          <div className="w-20 h-20 bg-gradient-to-tr from-purple-500 to-indigo-600 rounded-full mx-auto flex items-center justify-center mb-3">
                            <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6c2.21 0 4 1.79 4 4s-1.79 4-4 4-4-1.79-4-4 1.79-4 4-4zm0 14c-5.52 0-10-4.48-10-10S6.48 2 12 2s10 4.48 10 10-4.48 10-10 10zm0-18c-4.42 0-8 3.58-8 8 0 1.65.5 3.18 1.35 ********** 1.26 1.64 2.05 2.19 1.35.95 2.98 1.36 4.6 1.36s3.25-.41 4.6-1.36c.79-.55 1.48-1.33 2.05-2.19.85-1.27 1.35-2.8 1.35-4.45 0-4.42-3.58-8-8-8z"></path>
                            </svg>
                          </div>
                          <h2 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-pink-300">เข้าสู่อาณาจักรเทพเจ้า</h2>
                          <p className="text-purple-200 text-sm mt-1">เข้าสู่ระบบเพื่อตรวจสอบสลิปแบบทิพย์</p>
                        </div>
                        
                        <FormField
                          control={loginForm.control}
                          name="username"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-white text-lg">ผู้ใช้เทพเจ้า</FormLabel>
                              <FormControl>
                                <div className="relative">
                                  <User className="absolute left-3 top-3 h-5 w-5 text-purple-400" />
                                  <Input
                                    placeholder="ชื่อผู้ใช้ อีเมล หรือเบอร์โทรทิพย์ของท่าน"
                                    className="pl-10 bg-slate-950 border-purple-700 text-white h-12"
                                    {...field}
                                  />
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={loginForm.control}
                          name="password"
                          render={({ field }) => (
                            <FormItem>
                              <div className="flex justify-between items-center">
                                <FormLabel className="text-white text-lg">รหัสผ่านศักดิ์สิทธิ์</FormLabel>
                                <a href="#forgot-password" className="text-xs text-purple-300 hover:text-purple-200">ลืมรหัสผ่าน?</a>
                              </div>
                              <FormControl>
                                <div className="relative">
                                  <Lock className="absolute left-3 top-3 h-5 w-5 text-purple-400" />
                                  <Input
                                    type="password"
                                    placeholder="รหัสลับของท่าน"
                                    className="pl-10 bg-slate-950 border-purple-700 text-white h-12"
                                    {...field}
                                  />
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <Button
                          type="submit"
                          className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white font-bold h-12 text-lg"
                          disabled={loginMutation.isPending}
                        >
                          {loginMutation.isPending ? (
                            <span className="flex items-center justify-center">
                              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              สู่แดนสวรรค์...
                            </span>
                          ) : (
                            <span className="flex items-center justify-center">
                              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                              </svg>
                              เข้าสู่สวรรค์ชั้นเจ็ด
                            </span>
                          )}
                        </Button>
                        
                        <div className="pt-3">
                          <div className="relative">
                            <div className="absolute inset-0 flex items-center">
                              <div className="w-full border-t border-purple-800"></div>
                            </div>
                            <div className="relative flex justify-center text-sm">
                              <span className="px-2 bg-slate-900 text-purple-300">หรือเข้าสู่ระบบด้วย</span>
                            </div>
                          </div>
                          
                          <div className="mt-4 grid grid-cols-3 gap-3">
                            <button type="button" className="bg-slate-800 hover:bg-slate-700 text-white p-2 rounded-md border border-slate-700 flex items-center justify-center">
                              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12.545 10.239v3.821h5.445c-.712 2.315-2.647 3.972-5.445 3.972-3.332 0-6.033-2.701-6.033-6.032s2.701-6.032 6.033-6.032c1.498 0 2.866.559 3.921 1.488l2.814-2.814A9.996 9.996 0 0 0 12.545 2C7.021 2 2.543 6.477 2.543 12s4.478 10 10.002 10c8.396 0 10.249-7.85 9.426-11.748l-9.426-.013z"></path>
                              </svg>
                            </button>
                            <button type="button" className="bg-slate-800 hover:bg-slate-700 text-white p-2 rounded-md border border-slate-700 flex items-center justify-center">
                              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"></path>
                              </svg>
                            </button>
                            <button type="button" className="bg-slate-800 hover:bg-slate-700 text-white p-2 rounded-md border border-slate-700 flex items-center justify-center">
                              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M22 10.651H2l10-8.5z M22 10.651v11.5H2v-11.5"></path>
                              </svg>
                            </button>
                          </div>
                        </div>
                      </form>
                    </Form>
                  </TabsContent>

                  {/* แท็บยืนยันบัญชี - สำหรับผู้ใช้ที่ล็อกอินแล้วแต่ยังไม่ได้ยืนยันตัวตน */}
                  <TabsContent value="verify">
                    <div className="space-y-6 bg-slate-900 p-6 rounded-lg border border-purple-800 shadow-xl">
                      <div className="text-center mb-4">
                        <div className="w-20 h-20 bg-gradient-to-tr from-purple-500 to-indigo-600 rounded-full mx-auto flex items-center justify-center mb-3">
                          <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                          </svg>
                        </div>
                        <h2 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-pink-300">ยืนยันความเป็นเทพเจ้า</h2>
                        <p className="text-purple-200 text-sm mt-1">กรุณายืนยันบัญชีเพื่อเข้าถึงพลังเทพอันไร้ขีดจำกัด</p>
                      </div>
                      
                      {user && (
                        <div className="space-y-4">
                          <div className="bg-purple-900/30 border border-purple-700 rounded-lg p-4">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center">
                                <div className="bg-purple-800 rounded-full p-2 mr-3">
                                  <Mail className="h-5 w-5 text-purple-200" />
                                </div>
                                <div>
                                  <div className="font-medium text-white">{user.email}</div>
                                  <div className="text-sm text-purple-300">อีเมล</div>
                                </div>
                              </div>
                              <div>
                                {userVerificationStatus.email_verified ? (
                                  <Badge className="bg-green-600">ยืนยันแล้ว</Badge>
                                ) : (
                                  <Button 
                                    size="sm" 
                                    variant="outline"
                                    className="border-purple-500 text-purple-200 hover:bg-purple-700"
                                    onClick={() => {
                                      setVerificationMethod("email");
                                      resendVerificationCode();
                                    }}
                                  >
                                    ยืนยันตอนนี้
                                  </Button>
                                )}
                              </div>
                            </div>
                          </div>
                          
                          {user.phoneNumber && (
                            <div className="bg-purple-900/30 border border-purple-700 rounded-lg p-4">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center">
                                  <div className="bg-purple-800 rounded-full p-2 mr-3">
                                    <Phone className="h-5 w-5 text-purple-200" />
                                  </div>
                                  <div>
                                    <div className="font-medium text-white">{user.phoneNumber}</div>
                                    <div className="text-sm text-purple-300">เบอร์โทรศัพท์</div>
                                  </div>
                                </div>
                                <div>
                                  {userVerificationStatus.phone_verified ? (
                                    <Badge className="bg-green-600">ยืนยันแล้ว</Badge>
                                  ) : (
                                    <Button 
                                      size="sm" 
                                      variant="outline"
                                      className="border-purple-500 text-purple-200 hover:bg-purple-700"
                                      onClick={() => {
                                        setVerificationMethod("phone");
                                        resendVerificationCode();
                                      }}
                                    >
                                      ยืนยันตอนนี้
                                    </Button>
                                  )}
                                </div>
                              </div>
                            </div>
                          )}
                          
                          {/* แสดงช่องกรอกรหัสยืนยันถ้าอยู่ในขั้นตอนการยืนยัน */}
                          {secondsLeft > 0 && (
                            <div className="mt-4 space-y-6 border-t border-purple-800 pt-4">
                              <div className="text-center">
                                <h3 className="text-xl font-semibold text-white">กรอกรหัสยืนยัน</h3>
                                <p className="text-sm text-purple-300 mt-1">
                                  ส่งรหัสยืนยันไปยัง{verificationMethod === "email" ? " อีเมล" : " SMS"} แล้ว ({secondsLeft}s)
                                </p>
                              </div>
                              
                              <div className="flex space-x-2">
                                <Input
                                  placeholder="รหัสยืนยัน 6 หลัก"
                                  className="bg-slate-950 border-purple-700 text-white"
                                  value={verificationCode}
                                  onChange={(e) => setVerificationCode(e.target.value)}
                                  maxLength={6}
                                />
                                <Button 
                                  className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700"
                                  onClick={verifyExistingAccount}
                                  disabled={isVerifying}
                                >
                                  {isVerifying ? (
                                    <div className="flex items-center">
                                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                      <span>กำลังยืนยัน...</span>
                                    </div>
                                  ) : "ยืนยัน"}
                                </Button>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </TabsContent>
                  
                  {/* แบบฟอร์มลงทะเบียน - แสดงตามขั้นตอน */}
                  <TabsContent value="register">
                    {registrationStep === "form" ? (
                      <Form {...registerForm}>
                        <form
                          onSubmit={registerForm.handleSubmit(onRegisterSubmit)}
                          className="space-y-4"
                        >
                          <FormField
                            control={registerForm.control}
                            name="username"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>ชื่อผู้ใช้</FormLabel>
                                <FormControl>
                                  <div className="relative">
                                    <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                                    <Input
                                      placeholder="ชื่อผู้ใช้ที่ต้องการ"
                                      className="pl-10"
                                      {...field}
                                    />
                                  </div>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          {/* เลือกวิธีการยืนยันตัวตน */}
                          <FormField
                            control={registerForm.control}
                            name="verificationMethod"
                            render={({ field }) => (
                              <FormItem className="space-y-3">
                                <FormLabel className="text-white text-lg font-medium">วิธีการยืนยันตัวตน</FormLabel>
                                <FormControl>
                                  <RadioGroup
                                    onValueChange={field.onChange}
                                    defaultValue={field.value}
                                    className="flex flex-col space-y-3"
                                  >
                                    <div className={`flex items-center space-x-2 p-3 rounded-lg border ${field.value === 'email' ? 'border-purple-500 bg-purple-900/50' : 'border-gray-700 bg-gray-900/30'} transition-colors duration-200`}>
                                      <RadioGroupItem value="email" id="email" className="text-purple-400 border-purple-500" />
                                      <label htmlFor="email" className="font-medium cursor-pointer flex items-center text-white">
                                        <Mail className="h-5 w-5 mr-2 text-purple-400" />
                                        อีเมลเทพเจ้า
                                      </label>
                                    </div>
                                    <div className={`flex items-center space-x-2 p-3 rounded-lg border ${field.value === 'phone' ? 'border-purple-500 bg-purple-900/50' : 'border-gray-700 bg-gray-900/30'} transition-colors duration-200`}>
                                      <RadioGroupItem value="phone" id="phone" className="text-purple-400 border-purple-500" />
                                      <label htmlFor="phone" className="font-medium cursor-pointer flex items-center text-white">
                                        <Phone className="h-5 w-5 mr-2 text-purple-400" />
                                        เบอร์โทรศัพท์ทิพย์
                                      </label>
                                    </div>
                                  </RadioGroup>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          {/* แสดงช่องกรอกข้อมูลตามวิธีการยืนยันที่เลือก */}
                          {verificationMethod === "email" && (
                            <FormField
                              control={registerForm.control}
                              name="email"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-white">อีเมลทิพย์</FormLabel>
                                  <FormControl>
                                    <div className="relative">
                                      <Mail className="absolute left-3 top-3 h-4 w-4 text-purple-400" />
                                      <Input
                                        type="email"
                                        placeholder="<EMAIL>"
                                        className="pl-10 bg-slate-950 border-purple-700 text-white"
                                        {...field}
                                      />
                                    </div>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          )}

                          {verificationMethod === "phone" && (
                            <FormField
                              control={registerForm.control}
                              name="phoneNumber"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-white">เบอร์โทรศัพท์เทพเจ้า</FormLabel>
                                  <FormControl>
                                    <div className="relative">
                                      <Phone className="absolute left-3 top-3 h-4 w-4 text-purple-400" />
                                      <Input
                                        type="tel"
                                        placeholder="เบอร์โทรทิพย์ของท่าน เช่น 0812345678"
                                        className="pl-10 bg-slate-950 border-purple-700 text-white"
                                        {...field}
                                      />
                                    </div>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          )}

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <FormField
                              control={registerForm.control}
                              name="firstName"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-white">ชื่อเทพเจ้า</FormLabel>
                                  <FormControl>
                                    <Input 
                                      placeholder="ชื่อแห่งเทพเจ้าของท่าน" 
                                      className="bg-slate-950 border-purple-700 text-white" 
                                      {...field} 
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={registerForm.control}
                              name="lastName"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-white">นามสกุลเทพเจ้า</FormLabel>
                                  <FormControl>
                                    <Input 
                                      placeholder="นามสกุลแห่งเทพเจ้า" 
                                      className="bg-slate-950 border-purple-700 text-white" 
                                      {...field} 
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>

                          <FormField
                            control={registerForm.control}
                            name="companyName"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-white">อาณาจักรของท่าน (ไม่บังคับ)</FormLabel>
                                <FormControl>
                                  <div className="relative">
                                    <Building className="absolute left-3 top-3 h-4 w-4 text-purple-400" />
                                    <Input
                                      placeholder="ชื่ออาณาจักร, บริษัท หรือองค์กรของท่าน"
                                      className="pl-10 bg-slate-950 border-purple-700 text-white"
                                      {...field}
                                    />
                                  </div>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={registerForm.control}
                            name="password"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-white">รหัสผ่านศักดิ์สิทธิ์</FormLabel>
                                <FormControl>
                                  <div className="relative">
                                    <Lock className="absolute left-3 top-3 h-4 w-4 text-purple-400" />
                                    <Input
                                      type="password"
                                      placeholder="รหัสลับเข้าอาณาจักรเทพเจ้า"
                                      className="pl-10 bg-slate-950 border-purple-700 text-white"
                                      {...field}
                                    />
                                  </div>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={registerForm.control}
                            name="confirmPassword"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-white">ยืนยันรหัสผ่านศักดิ์สิทธิ์</FormLabel>
                                <FormControl>
                                  <div className="relative">
                                    <Lock className="absolute left-3 top-3 h-4 w-4 text-purple-400" />
                                    <Input
                                      type="password"
                                      placeholder="ยืนยันรหัสลับอีกครั้ง"
                                      className="pl-10 bg-slate-950 border-purple-700 text-white"
                                      {...field}
                                    />
                                  </div>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <div className="flex items-start">
                            <div className="flex items-center h-5">
                              <input
                                id="terms"
                                type="checkbox"
                                className="h-4 w-4 text-purple-600 bg-slate-900 border-purple-500 rounded focus:ring-purple-500"
                                required
                              />
                            </div>
                            <div className="ml-3 text-sm">
                              <label htmlFor="terms" className="font-medium text-white">
                                ข้าพเจ้ายอมรับ{" "}
                                <a href="#terms" className="text-purple-400 hover:text-purple-300 underline">
                                  คำสาบานของเทพเจ้า
                                </a>{" "}
                                และ{" "}
                                <a href="#privacy" className="text-purple-400 hover:text-purple-300 underline">
                                  กฎแห่งสวรรค์
                                </a>
                              </label>
                            </div>
                          </div>

                          <Button
                            type="submit"
                            className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white font-bold py-3"
                          >
                            <span className="flex items-center justify-center">
                              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                              </svg>
                              สร้างตำนานเทพเจ้า
                            </span>
                          </Button>
                        </form>
                      </Form>
                    ) : (
                      /* หน้ายืนยันรหัส OTP หรืออีเมล */
                      <div className="space-y-5">
                        <Card>
                          <CardHeader>
                            <CardTitle className="text-center">ยืนยันตัวตน</CardTitle>
                            <CardDescription className="text-center">
                              {verificationMethod === "email" 
                                ? `เราได้ส่งรหัสยืนยันไปยังอีเมล ${tempUserData?.email}` 
                                : `เราได้ส่งรหัสยืนยัน OTP ไปยังเบอร์ ${tempUserData?.phoneNumber}`}
                            </CardDescription>
                          </CardHeader>
                          <CardContent className="space-y-4">
                            <div>
                              <label htmlFor="verification-code" className="block text-sm font-medium text-gray-700 mb-1">
                                รหัสยืนยัน
                              </label>
                              <Input
                                id="verification-code"
                                placeholder="กรอกรหัส 6 หลัก"
                                maxLength={6}
                                value={verificationCode}
                                onChange={(e) => setVerificationCode(e.target.value)}
                                className="text-center text-xl tracking-wider"
                              />
                            </div>
                            
                            {secondsLeft > 0 && (
                              <p className="text-sm text-gray-500 text-center">
                                สามารถขอรหัสใหม่ได้ในอีก {Math.floor(secondsLeft / 60)}:{(secondsLeft % 60).toString().padStart(2, "0")} นาที
                              </p>
                            )}
                          </CardContent>
                          <CardFooter className="flex flex-col space-y-3">
                            <Button 
                              className="w-full" 
                              onClick={verifyCode}
                              disabled={isVerifying || verificationCode.length < 6}
                            >
                              {isVerifying ? "กำลังตรวจสอบ..." : "ยืนยันรหัส"}
                            </Button>
                            
                            <Button
                              variant="outline"
                              className="w-full"
                              disabled={secondsLeft > 0}
                              onClick={resendVerificationCode}
                            >
                              ขอรหัสยืนยันใหม่
                            </Button>
                            
                            <Button
                              variant="link"
                              className="w-full"
                              onClick={() => setRegistrationStep("form")}
                            >
                              ย้อนกลับ
                            </Button>
                          </CardFooter>
                        </Card>
                        
                        <Alert>
                          <AlertTitle className="flex items-center">
                            <ExternalLink className="mr-2 h-4 w-4" />
                            คำแนะนำ
                          </AlertTitle>
                          <AlertDescription>
                            {verificationMethod === "email" 
                              ? "หากไม่พบอีเมลในกล่องจดหมาย โปรดตรวจสอบโฟลเดอร์สแปมหรือขยะ" 
                              : "หากไม่ได้รับ SMS กรุณาตรวจสอบว่าเบอร์โทรศัพท์ถูกต้อง"}
                          </AlertDescription>
                        </Alert>
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              </div>

              {/* ส่วนข้อมูลสินค้า */}
              <div className="hidden lg:block bg-primary-600 text-white p-10 rounded-lg">
                <h2 className="text-3xl font-bold mb-6">เริ่มต้นตรวจสอบสลิปธนาคารได้วันนี้</h2>
                <p className="text-lg mb-6">
                  บริการตรวจสอบสลิปธนาคารที่น่าเชื่อถือที่สุดสำหรับธุรกิจทุกขนาด ด้วยเทคโนโลยีและมาตรฐานความปลอดภัยระดับสากล
                </p>

                <div className="space-y-4 mb-8">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 bg-primary-500 p-2 rounded-full mr-3">
                      <svg className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-medium">รวดเร็วและแม่นยำ</h3>
                      <p className="text-primary-100">
                        ตรวจสอบข้อมูลสลิปได้ภายในไม่กี่วินาทีด้วยความแม่นยำสูง
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 bg-primary-500 p-2 rounded-full mr-3">
                      <svg className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-medium">รองรับทุกธนาคารในประเทศไทย</h3>
                      <p className="text-primary-100">
                        เชื่อมต่อกับธนาคารชั้นนำครอบคลุมทั่วประเทศ
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 bg-primary-500 p-2 rounded-full mr-3">
                      <svg className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-medium">API ที่ใช้งานง่าย</h3>
                      <p className="text-primary-100">
                        เชื่อมต่อเข้ากับระบบของคุณได้อย่างง่ายดายด้วย API ที่ออกแบบมาเพื่อนักพัฒนา
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-primary-500 p-4 rounded-lg">
                  <p className="italic text-primary-100">
                    "บริการตรวจสอบสลิปธนาคารจาก SLIPKUY ช่วยให้เราจัดการรายการชำระเงินได้อย่างมีประสิทธิภาพและป้องกันการโกงได้อย่างดีเยี่ยม ทีมงานให้บริการอย่างมืออาชีพ ประทับใจมาก"
                  </p>
                  <div className="mt-4 flex items-center">
                    <div className="flex-shrink-0">
                      <div className="rounded-full bg-primary-400 w-10 h-10 flex items-center justify-center font-bold text-white">
                        ส
                      </div>
                    </div>
                    <div className="ml-3">
                      <h4 className="font-medium">สมชาย ใจดี</h4>
                      <p className="text-sm text-primary-200">ผู้จัดการฝ่ายการเงิน, บริษัท ABC จำกัด</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
