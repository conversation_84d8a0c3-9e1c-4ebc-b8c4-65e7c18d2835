/**
 * เนื่องจากมีปัญหากับการติดตั้ง library canvas และ jsQR ในสภาพแวดล้อมนี้
 * เราจะใช้วิธีการอื่นในการตรวจสอบรูปภาพว่ามีลักษณะเป็นสลิป QR Code หรือไม่
 * โดยตรวจสอบจากลักษณะของไฟล์และ header ของข้อมูลที่มักพบในรูปภาพสลิป
 */

/**
 * ตรวจสอบว่ารูปภาพมีลักษณะที่น่าจะเป็น QR Code สลิปหรือไม่
 * 
 * @param imageBuffer Buffer ของรูปภาพที่ต้องการตรวจสอบ
 * @returns Promise<boolean> true ถ้าน่าจะเป็นสลิป QR Code, false ถ้าไม่น่าใช่
 */
export async function hasQRCode(imageBuffer: Buffer): Promise<boolean> {
  try {
    // ตรวจสอบว่าเป็นไฟล์รูปภาพที่ถูกต้องหรือไม่ (JPEG/PNG)
    const isJPEG = imageBuffer.slice(0, 3).toString('hex') === 'ffd8ff';
    const isPNG = imageBuffer.slice(0, 8).toString('hex') === '89504e470d0a1a0a';
    
    if (!isJPEG && !isPNG) {
      console.log('Not a valid image file (JPEG/PNG)');
      return false;
    }
    
    // ถ้าขนาดไฟล์ไม่เหมาะสม
    if (imageBuffer.length < 1000 || imageBuffer.length > 10 * 1024 * 1024) {
      console.log('File size invalid, too small or too large');
      return false;
    }
    
    // ตรวจสอบข้อความที่พบบ่อยในรูปสลิป ธนาคาร หรือ QR Code แบบไทย
    const bufferString = imageBuffer.toString('utf-8', 0, Math.min(imageBuffer.length, 4000));
    
    // ข้อความที่มักพบในรูปสลิป ธนาคาร หรือมีการเข้ารหัส QR code (เช้น THAI QR)
    const slipKeywords = [
      // QR Code markers
      'QRCode', 'QRCODE', 'qrcode',
      // Common text in banking slips
      'PAYMENT', 'payment', 'TRANSACTION', 'transaction',
      'PAY', 'pay', 'TRANSFER', 'transfer',
      'BANK', 'bank', 'RECEIPT', 'receipt',
      'SLIP', 'slip', 'BILL', 'bill',
      // Thai banking keywords
      'โอนเงิน', 'ชำระเงิน', 'รับเงิน', 'สลิป',
      'ธนาคาร', 'บัญชี', 'รายการ', 'ชำระ',
      // Thai bank names
      'กสิกร', 'ไทยพาณิชย์', 'กรุงเทพ', 'กรุงไทย',
      'กรุงศรี', 'ทหารไทย', 'ธนชาต', 'ออมสิน',
      'SCB', 'KBANK', 'KTB', 'BBL',
      'KRUNGSRI', 'TMB', 'THANACHART', 'GSB',
      // QR formats
      'QRC|', 'THAI QR', 'PromptPay', 'PROMPTPAY'
    ];
    
    // ตรวจสอบว่ามีข้อความที่มักพบในสลิปหรือไม่
    for (const keyword of slipKeywords) {
      if (bufferString.includes(keyword)) {
        console.log(`Found banking keyword in image: ${keyword}`);
        return true;
      }
    }
    
    // ตรวจสอบรูปแบบพิเศษของ QR Code ในข้อมูลไบนารี
    const binaryPatterns = [
      Buffer.from([0x51, 0x52, 0x20, 0x43, 0x4F, 0x44, 0x45]), // "QR CODE"
      Buffer.from([0x00, 0x26, 0x00, 0x42, 0x00, 0x51, 0x00, 0x52]), // UTF-16 encoded "QR"
      Buffer.from([0x50, 0x72, 0x6F, 0x6D, 0x70, 0x74, 0x50, 0x61, 0x79]) // "PromptPay"
    ];
    
    for (const pattern of binaryPatterns) {
      // ค้นหาแพทเทิร์นในไบนารี 10000 ไบต์แรก (หากไฟล์ใหญ่กว่านั้น)
      const searchLimit = Math.min(imageBuffer.length, 10000);
      for (let i = 0; i <= searchLimit - pattern.length; i++) {
        let match = true;
        for (let j = 0; j < pattern.length; j++) {
          if (imageBuffer[i + j] !== pattern[j]) {
            match = false;
            break;
          }
        }
        if (match) {
          console.log(`Found binary pattern for QR code at position ${i}`);
          return true;
        }
      }
    }
    
    // ถ้าไม่พบรูปแบบใดๆ ที่เกี่ยวข้องกับสลิป QR Code
    console.log('No QR code or banking information found in image');
    return false;
  } catch (error) {
    console.error('Error in hasQRCode:', error);
    return false;
  }
}

/**
 * ตรวจสอบและดึงข้อมูล QR Code จากรูปภาพ
 * (เนื่องจากไม่สามารถอ่าน QR code ได้จริงๆ จึงเป็นเพียงการจำลองการตรวจสอบ)
 * 
 * @param imageBuffer Buffer ของรูปภาพที่ต้องการตรวจสอบ
 * @returns Promise<{ hasQRCode: boolean; qrData?: string }> ผลการตรวจสอบ
 */
export async function detectQRCode(imageBuffer: Buffer): Promise<{ hasQRCode: boolean; qrData?: string }> {
  const result = await hasQRCode(imageBuffer);
  return {
    hasQRCode: result,
    qrData: result ? 'QR code likely present in image (exact data cannot be decoded)' : undefined
  };
}