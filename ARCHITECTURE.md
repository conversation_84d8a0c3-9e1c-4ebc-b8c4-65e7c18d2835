# สถาปัตยกรรมระบบ SLIPKUY

เอกสารนี้อธิบายสถาปัตยกรรมและโครงสร้างเชิงเทคนิคของระบบ SLIPKUY เพื่อให้เข้าใจถึงการทำงานภายในระบบ

## ภาพรวมสถาปัตยกรรม

SLIPKUY ใช้สถาปัตยกรรมแบบ **Three-tier Architecture** ที่แบ่งเป็น:

1. **Presentation Layer (Client)**: ส่วนติดต่อกับผู้ใช้งาน (UI/UX)
2. **Application Layer (Server)**: ส่วนประมวลผลข้อมูลและ API
3. **Data Layer (Database)**: ส่วนจัดเก็บข้อมูล

ระบบนี้ยังทำหน้าที่เป็น **Middleware Service** ที่อยู่ระหว่างผู้ใช้บริการและ API ของ slip2go หรือ Easy Slip เพื่อให้บริการและจัดการเพิ่มเติม:

```
Client (Mobile/Web) <--> SLIPKUY <--> Slip Verification Service (slip2go/Easy Slip)
```

## เทคโนโลยีการพัฒนา

### Frontend (Client)
- **React**: ไลบรารีหลักสำหรับพัฒนา UI
- **TypeScript**: ใช้ในการพัฒนาทั้งระบบ
- **TanStack Query**: จัดการ data fetching และ caching
- **Wouter**: จัดการเส้นทาง (routing)
- **Tailwind CSS** + **shadcn/ui**: จัดการการแสดงผลและ UI components
- **Zod**: ตรวจสอบข้อมูลฟอร์ม
- **Vite**: เครื่องมือในการพัฒนาและ build

### Backend (Server)
- **Node.js**: runtime environment
- **Express**: framework สำหรับสร้าง RESTful API
- **TypeScript**: ใช้ในการพัฒนาทั้งระบบ
- **Passport.js**: จัดการการพิสูจน์ตัวตน
- **Drizzle ORM**: ORM สำหรับจัดการฐานข้อมูล
- **Multer**: จัดการการอัปโหลดไฟล์
- **Zod**: ตรวจสอบข้อมูลนำเข้า

### Database (Data Layer)
- **PostgreSQL**: ระบบฐานข้อมูลหลัก
- **Drizzle ORM**: จัดการและติดต่อกับฐานข้อมูล
- **neon-serverless**: ไลบรารีเชื่อมต่อกับ PostgreSQL

### บริการภายนอก (External Services)
- **slip2go API**: บริการหลักในการตรวจสอบสลิป
- **Easy Slip API**: บริการสำรองในการตรวจสอบสลิป
- **SMTP/SendGrid**: บริการส่งอีเมล
- **Perplexity API**: บริการ AI (ใช้ในบางฟีเจอร์)
- **Stripe** (เตรียมรองรับในอนาคต): บริการชำระเงิน

## ภาพรวมการทำงานของระบบ

### กระบวนการตรวจสอบสลิป

```mermaid
sequenceDiagram
    Client->>+SLIPKUY: ส่งรูปภาพสลิป (formdata)
    SLIPKUY->>+SlipService: ส่งต่อรูปภาพ
    SlipService->>+APIProvider: ส่งรูปภาพไปตรวจสอบ (slip2go/Easy Slip)
    APIProvider-->>-SlipService: ส่งผลการตรวจสอบ
    SlipService->>SLIPKUY DB: บันทึกผลลัพธ์
    SlipService-->>-SLIPKUY: ส่งผลลัพธ์ที่แปลงแล้ว
    SLIPKUY->>SLIPKUY DB: อัพเดทเครดิต/แพ็คเกจของผู้ใช้
    SLIPKUY-->>-Client: ส่งผลลัพธ์
```

### กระบวนการยืนยันตัวตนและ API Key

```mermaid
sequenceDiagram
    Client->>+SLIPKUY: ส่งคำขอพร้อม API Key (X-API-KEY)
    SLIPKUY->>+SlipService: ตรวจสอบ API Key
    SlipService->>+SLIPKUY DB: ตรวจสอบความถูกต้องและสิทธิ์การใช้งาน
    SLIPKUY DB-->>-SlipService: ผลการตรวจสอบ
    SlipService-->>-SLIPKUY: ยืนยันหรือปฏิเสธการเข้าถึง
    alt API Key ถูกต้อง
        SLIPKUY->>+ServiceLogic: ดำเนินการตามคำขอ
        ServiceLogic-->>-SLIPKUY: ผลลัพธ์
        SLIPKUY-->>Client: ส่งผลลัพธ์
    else API Key ไม่ถูกต้อง
        SLIPKUY-->>-Client: ส่งข้อความแจ้งผิดพลาด (401/403)
    end
```

## ระบบฐานข้อมูล

### โครงสร้างข้อมูลหลัก (Main Entities)

```mermaid
erDiagram
    users ||--o{ userPackages : subscribes
    users ||--o{ apiKeys : creates
    users ||--o{ slipVerifications : requests
    users ||--o{ transactions : has
    packages ||--o{ userPackages : included_in
    apiKeys ||--o{ apiLogs : generates
    
    users {
        int id PK
        string username
        string password
        string email
        string role
        int credits
        json allowedPackages
        datetime createdAt
        datetime updatedAt
    }
    
    packages {
        int id PK
        string name
        string description
        int price
        int requestsLimit
        float creditPerVerification
        boolean isActive
        datetime createdAt
        datetime updatedAt
    }
    
    userPackages {
        int id PK
        int userId FK
        int packageId FK
        datetime startDate
        datetime endDate
        int requestsUsed
        int durationMonths
        boolean isActive
        datetime createdAt
        datetime updatedAt
    }
    
    apiKeys {
        int id PK
        int userId FK
        string apiKey
        string name
        string status
        json limitSettings
        datetime expiryDate
        datetime createdAt
        datetime updatedAt
    }
    
    slipVerifications {
        int id PK
        int userId FK
        string transactionRef
        string sourceType
        float amount
        string status
        string imagePath
        json responseData
        datetime createdAt
        datetime updatedAt
    }
    
    transactions {
        int id PK
        int userId FK
        int amount
        string type
        string source
        string referenceCode
        datetime createdAt
        datetime updatedAt
    }
    
    apiLogs {
        int id PK
        int apiKeyId FK
        string endpoint
        string method
        string ipAddress
        int statusCode
        json requestData
        json responseData
        datetime createdAt
    }
    
    systemSettings {
        int id PK
        string key
        json value
        datetime updatedAt
    }
    
    coupons {
        int id PK
        string code
        float discountPercent
        int discountAmount
        int usageCount
        int maxUsage
        boolean isActive
        datetime startDate
        datetime endDate
        datetime createdAt
        datetime updatedAt
    }
```

## API Response Format

### มาตรฐานการตอบกลับ API

ระบบ SLIPKUY ใช้รูปแบบการตอบกลับมาตรฐานเพื่อความสม่ำเสมอในการใช้งาน API:

```json
{
  "code": "200000",
  "message": "เสร็จสมบูรณ์",
  "data": { ... }
}
```

หรือกรณีเกิดข้อผิดพลาด:

```json
{
  "code": "400001",
  "message": "ข้อมูลไม่ถูกต้อง",
  "error": { ... }
}
```

### Response Codes

รหัสการตอบกลับถูกออกแบบเป็นรูปแบบต่อไปนี้:
- 3 ตัวแรก: HTTP status code
- 3 ตัวหลัง: รหัสเฉพาะของระบบ SLIPKUY

ตัวอย่างรหัสพื้นฐาน:
- `200000`: สำเร็จ
- `400001`: ข้อมูลไม่ถูกต้อง
- `401001`: ไม่ได้เข้าสู่ระบบ
- `403001`: ไม่มีสิทธิ์เข้าถึง
- `404001`: ไม่พบข้อมูล
- `500001`: เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์

## การจัดการข้อมูลและธุรกิจ

### ระบบเครดิตและแพ็คเกจ

1. **เครดิต** เป็นหน่วยสกุลเงินภายในระบบ สามารถ:
   - ซื้อแพ็คเกจด้วยเครดิต
   - เติมเครดิตผ่านระบบเติมเงิน
   - แอดมินสามารถเพิ่ม/ลดเครดิตผู้ใช้ได้

2. **แพ็คเกจ** มีหลายประเภท:
   - แพ็คเกจฟรี (ใช้ได้ครั้งเดียวต่อบัญชี)
   - แพ็คเกจรายเดือน
   - แพ็คเกจรายไตรมาส (ส่วนลด 3 เดือน)
   - แพ็คเกจรายครึ่งปี (ส่วนลด 6 เดือน)
   - แพ็คเกจรายปี (ส่วนลด 12 เดือน)

3. **การรีเซ็ทโควต้า**:
   - แพ็คเกจที่มากกว่า 1 เดือนจะรีเซ็ทโควต้าทุกเดือน
   - แอดมินสามารถรีเซ็ทโควต้าแพ็คเกจผู้ใช้ด้วยตนเองได้

### ระบบ API และการจำกัดการใช้งาน

1. **API Keys**:
   - ผู้ใช้สามารถสร้าง API Keys ได้หลายอัน
   - สามารถกำหนดชื่อ, วันหมดอายุ, และข้อจำกัดได้
   - มีการบันทึกประวัติการใช้งานละเอียด

2. **ข้อจำกัดการใช้งาน**:
   - จำกัดจำนวนครั้งต่อวัน
   - จำกัดจำนวนครั้งต่อเดือน
   - จำกัด IP address ที่เข้าถึงได้
   - จำกัดตามโควต้าในแพ็คเกจ

3. **การเก็บข้อมูลเชิงสถิติ**:
   - จำนวนการเรียกใช้ API
   - อัตราการใช้งานสำเร็จ/ล้มเหลว
   - เวลาตอบสนองเฉลี่ย
   - IP address และข้อมูลการใช้งาน

## ขั้นตอนการพัฒนาในอนาคต

### การปรับปรุงประสิทธิภาพ

1. **การแคช**: เพิ่มการแคชสำหรับคำขอที่ใช้บ่อย
   ```typescript
   // ตัวอย่างการเพิ่ม in-memory cache
   const cache = new Map();
   const CACHE_TTL = 60000; // 1 นาที
   
   app.get("/api/cached-data", (req, res) => {
     const cacheKey = "cached-data";
     if (cache.has(cacheKey)) {
       const { data, timestamp } = cache.get(cacheKey);
       if (Date.now() - timestamp < CACHE_TTL) {
         return res.json(data);
       }
     }
     
     // ดึงข้อมูลและเก็บในแคช
     const data = fetchData();
     cache.set(cacheKey, { data, timestamp: Date.now() });
     res.json(data);
   });
   ```

2. **การทำ Connection Pooling**: เพิ่มประสิทธิภาพการติดต่อกับฐานข้อมูล
   ```typescript
   // เพิ่มการกำหนดค่า pool
   const pool = new Pool({
     connectionString: process.env.DATABASE_URL,
     max: 20, // จำนวนการเชื่อมต่อสูงสุด
     idleTimeoutMillis: 30000 // เวลาหมดอายุเมื่อไม่ใช้งาน
   });
   ```

### ระบบเพิ่มเติมที่อาจพัฒนา

1. **ระบบชำระเงินออนไลน์**:
   - เชื่อมต่อกับ Stripe หรือ Payment Gateway อื่นๆ
   - เพิ่มระบบใบแจ้งหนี้และใบเสร็จ
   - ระบบสมัครสมาชิกแบบอัตโนมัติ

2. **ระบบแจ้งเตือนขั้นสูง**:
   - แจ้งเตือนผ่าน LINE Official
   - แจ้งเตือนผ่าน SMS
   - ระบบวิเคราะห์พฤติกรรมผิดปกติ

3. **ระบบรายงานขั้นสูง**:
   - รายงานการใช้งานรายวัน/สัปดาห์/เดือน
   - รายงานการวิเคราะห์แนวโน้ม
   - Export ข้อมูลในรูปแบบต่างๆ

## ข้อควรระวังในการพัฒนา

### การจัดการความปลอดภัย

1. **การจัดการข้อมูลที่อ่อนไหว**:
   - ไม่แสดง API key เต็มรูปแบบในหน้า UI (ยกเว้นตอนสร้างครั้งแรกหรือสร้างใหม่)
   - ไม่เก็บข้อมูลบัตรเครดิตในระบบโดยตรง
   - เข้ารหัสข้อมูลที่อ่อนไหวทั้งหมด

2. **การควบคุมการเข้าถึง**:
   - ตรวจสอบสิทธิ์การเข้าถึงทุกครั้งที่เรียกใช้ API
   - ใช้ middleware เพื่อตรวจสอบสิทธิ์ของผู้ใช้

3. **การป้องกันการโจมตี**:
   - ป้องกัน SQL Injection ด้วยการใช้ parameterized queries
   - ป้องกัน XSS ด้วยการทำ input sanitization
   - จำกัดจำนวนคำขอเพื่อป้องกัน DDoS

### การปรับปรุงประสิทธิภาพการพัฒนา

1. **การทดสอบ**:
   - เพิ่มการทดสอบหน่วย (Unit Tests)
   - เพิ่มการทดสอบการรวม (Integration Tests)
   - เพิ่มการทดสอบ end-to-end

2. **การติดตามและบันทึก**:
   - ใช้ระบบติดตามข้อผิดพลาด (Error Tracking)
   - เพิ่มการบันทึกการเข้าถึงและการใช้งาน (Access Logging)
   - ตรวจสอบประสิทธิภาพแบบเรียลไทม์ (Performance Monitoring)

---

เอกสารนี้อธิบายถึงสถาปัตยกรรมและโครงสร้างเชิงเทคนิคของระบบ SLIPKUY โดยละเอียด ควรใช้เอกสารนี้ควบคู่กับ AI_GUIDE.md และ README.md เพื่อความเข้าใจที่สมบูรณ์ในการพัฒนาระบบต่อไป