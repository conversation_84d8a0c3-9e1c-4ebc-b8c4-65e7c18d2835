import { Express, Request, Response, NextFunction } from "express";
import { storage } from "../storage";
import { randomBytes } from "crypto";
import { insertApiKeySchema, apiKeys } from "@shared/schema";
import { ZodError, z } from "zod";
import { fromZodError } from "zod-validation-error";
import { db } from "../db";

// Middleware สำหรับตรวจสอบสิทธิ์ผู้ดูแลระบบ
function isAdmin(req: Request, res: Response, next: NextFunction) {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ message: "ไม่ได้รับอนุญาต กรุณาเข้าสู่ระบบ" });
  }

  if (req.user?.isAdmin !== true) {
    return res.status(403).json({ message: "ไม่มีสิทธิ์เข้าถึง ต้องการสิทธิ์ผู้ดูแลระบบ" });
  }

  next();
}

// Middleware สำหรับตรวจสอบการเข้าสู่ระบบ
function isAuthenticated(req: Request, res: Response, next: NextFunction) {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ message: "ไม่ได้รับอนุญาต กรุณาเข้าสู่ระบบ" });
  }
  next();
}

// ฟังก์ชันสร้าง API key แบบสุ่ม
function generateApiKey(): string {
  return randomBytes(32).toString('hex');
}

export function setupApiKeyRoutes(app: Express) {
  // === Admin Routes ===

  // ดูรายการ API key ทั้งหมด (สำหรับ Admin)
  app.get("/api/admin/api-keys", isAdmin, async (req, res) => {
    try {
      const apiKeys = await storage.listAllApiKeys();
      res.json(apiKeys);
    } catch (error) {
      console.error("Error getting API keys:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  });

  // ดูรายการ API key ของผู้ใช้งานคนใดคนหนึ่ง (สำหรับ Admin)
  app.get("/api/admin/users/:userId/api-keys", isAdmin, async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);

      if (isNaN(userId)) {
        return res.status(400).json({ error: "รหัสผู้ใช้ไม่ถูกต้อง" });
      }

      // ตรวจสอบว่าผู้ใช้มีอยู่จริงหรือไม่
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ error: "ไม่พบผู้ใช้งาน" });
      }

      const apiKeys = await storage.listUserApiKeys(userId);
      res.json(apiKeys);
    } catch (error) {
      console.error("Error getting user API keys:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  });

  // สร้าง API key ใหม่สำหรับผู้ใช้ (สำหรับ Admin)
  app.post("/api/admin/users/:userId/api-keys", isAdmin, async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);

      if (isNaN(userId)) {
        return res.status(400).json({ error: "รหัสผู้ใช้ไม่ถูกต้อง" });
      }

      // ตรวจสอบว่าผู้ใช้มีอยู่จริงหรือไม่
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ error: "ไม่พบผู้ใช้งาน" });
      }

      // วิเคราะห์ข้อมูลที่ส่งมา
      const validatedData = insertApiKeySchema.parse(req.body);

      // สร้าง API key
      const apiKey = generateApiKey();

      // บันทึกลงฐานข้อมูล
      const newApiKey = await storage.createApiKey({
        ...validatedData,
        apiKey
      }, userId);

      // ส่งค่ากลับไป
      res.status(201).json({
        ...newApiKey,
        apiKey // ส่ง API key กลับไป - นี่เป็นครั้งเดียวที่จะแสดง API key เต็ม
      });
    } catch (error) {
      if (error instanceof ZodError) {
        const validationError = fromZodError(error);
        return res.status(400).json({ error: validationError.message });
      }
      console.error("Error creating API key for user:", error);
      res.status(500).json({ error: "เกิดข้อผิดพลาดในการสร้าง API key" });
    }
  });

  // สร้าง API Key ใหม่แบบ Admin (regenerate)
  app.post("/api/admin/users/:userId/api-keys/:id/regenerate", isAdmin, async (req, res) => {
    try {
      const apiKeyId = parseInt(req.params.id);
      const userId = parseInt(req.params.userId);

      if (isNaN(apiKeyId) || isNaN(userId)) {
        return res.status(400).json({ error: "Invalid API key ID or User ID" });
      }

      // ตรวจสอบว่า API key นี้เป็นของผู้ใช้ที่ระบุหรือไม่
      const apiKey = await storage.getApiKey(apiKeyId);
      if (!apiKey) {
        return res.status(404).json({ error: "API key not found" });
      }

      if (apiKey.userId !== userId) {
        return res.status(403).json({ error: "API key does not belong to this user" });
      }

      // สร้าง API key ใหม่
      const newApiKeyValue = generateApiKey();

      // อัพเดท API key ในฐานข้อมูล
      const updatedApiKey = await storage.updateApiKey(apiKeyId, {
        apiKey: newApiKeyValue,
        updatedAt: new Date()
      });

      if (!updatedApiKey) {
        return res.status(500).json({ error: "Failed to regenerate API key" });
      }

      // ส่งค่า API key กลับ (นี่เป็นครั้งเดียวที่จะแสดง API key เต็ม)
      res.json({
        ...updatedApiKey,
        apiKey: newApiKeyValue
      });
    } catch (error) {
      console.error("Error regenerating API key:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  });

  // === User Routes ===

  // ดูรายการ API key ของผู้ใช้
  app.get("/api/user/api-keys", isAuthenticated, async (req, res) => {
    try {
      const apiKeys = await storage.listUserApiKeys(req.user!.id);
      res.json(apiKeys);
    } catch (error) {
      console.error("Error getting user API keys:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  });

  // สร้าง API key ใหม่
  app.post("/api/user/api-keys", isAuthenticated, async (req, res) => {
    try {
      // ข้ามการตรวจสอบ schema และสร้างข้อมูลที่จำเป็นทั้งหมดสำหรับ API Key
      const { name, description = null, ipWhitelist = null, expiresAt = null } = req.body;
      const status = req.body.status || 'active';
      const limitEnabled = req.body.limitEnabled || false;
      const usageLimit = req.body.usageLimit || null;

      if (!name) {
        return res.status(400).json({ error: "กรุณาระบุชื่อ API Key" });
      }

      // สร้าง API key
      const apiKey = generateApiKey();

      console.log("Creating API key with data:", {
        name,
        description,
        status,
        userId: req.user!.id,
        apiKeyLength: apiKey.length
      });

      // สร้างข้อมูลโดยตรงและข้ามกระบวนการ validation
      const newApiKey = await db.insert(apiKeys).values({
        name,
        description,
        status,
        ipWhitelist: ipWhitelist ? ipWhitelist : null,
        expiresAt: expiresAt ? new Date(expiresAt) : null,
        limitEnabled,
        usageLimit,
        apiKey,
        userId: req.user!.id,
        requestCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      }).returning();

      if (!newApiKey || newApiKey.length === 0) {
        throw new Error("ไม่สามารถสร้าง API key ได้");
      }

      console.log("Created new API key:", {
        id: newApiKey[0].id,
        userId: req.user!.id,
        name: newApiKey[0].name,
        apiKeyLength: newApiKey[0].apiKey?.length || 0
      });

      res.status(201).json({
        ...newApiKey[0],
        apiKey // ส่ง API key กลับไปให้ผู้ใช้ - นี่เป็นครั้งเดียวที่จะแสดง API key เต็ม
      });
    } catch (error) {
      console.error("Error creating API key:", error);
      res.status(500).json({ error: "ไม่สามารถสร้าง API key ได้ กรุณาลองใหม่อีกครั้ง" });
    }
  });

  // ดูรายละเอียด API key (เฉพาะข้อมูลสำคัญ ไม่ใช่ key เต็ม)
  app.get("/api/user/api-keys/:id", isAuthenticated, async (req, res) => {
    try {
      const apiKeyId = parseInt(req.params.id);
      if (isNaN(apiKeyId)) {
        return res.status(400).json({ error: "Invalid API key ID" });
      }

      const apiKey = await storage.getApiKey(apiKeyId);
      if (!apiKey) {
        return res.status(404).json({ error: "API key not found" });
      }

      // ตรวจสอบว่าเป็น API key ของผู้ใช้นี้หรือไม่
      if (apiKey.userId !== req.user!.id && req.user!.role !== 'admin') {
        return res.status(403).json({ error: "Forbidden" });
      }

      // ปกปิด API key จริง
      const { apiKey: _, ...apiKeyInfo } = apiKey;

      res.json(apiKeyInfo);
    } catch (error) {
      console.error("Error getting API key:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  });

  // อัพเดทสถานะหรือข้อมูล API key
  app.patch("/api/user/api-keys/:id", isAuthenticated, async (req, res) => {
    try {
      const apiKeyId = parseInt(req.params.id);
      if (isNaN(apiKeyId)) {
        return res.status(400).json({ error: "Invalid API key ID" });
      }

      // ตรวจสอบว่า API key นี้เป็นของผู้ใช้คนนี้หรือไม่
      const apiKey = await storage.getApiKey(apiKeyId);
      if (!apiKey) {
        return res.status(404).json({ error: "API key not found" });
      }

      if (apiKey.userId !== req.user!.id && req.user!.role !== 'admin') {
        return res.status(403).json({ error: "Forbidden" });
      }

      // อัพเดทข้อมูล
      const updatedApiKey = await storage.updateApiKey(apiKeyId, req.body);

      // ปกปิด API key จริง
      const apiKeyInfo = updatedApiKey ?
        { ...updatedApiKey, apiKey: undefined } :
        { id: apiKeyId, updatedAt: new Date() };

      res.json(apiKeyInfo);
    } catch (error) {
      console.error("Error updating API key:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  });

  // ยกเลิก API key
  app.delete("/api/user/api-keys/:id", isAuthenticated, async (req, res) => {
    try {
      const apiKeyId = parseInt(req.params.id);
      if (isNaN(apiKeyId)) {
        return res.status(400).json({ error: "Invalid API key ID" });
      }

      // ตรวจสอบว่า API key นี้เป็นของผู้ใช้คนนี้หรือไม่
      const apiKey = await storage.getApiKey(apiKeyId);
      if (!apiKey) {
        return res.status(404).json({ error: "API key not found" });
      }

      if (apiKey.userId !== req.user!.id && req.user!.role !== 'admin') {
        return res.status(403).json({ error: "Forbidden" });
      }

      // ลบ API key ออกจากฐานข้อมูล
      await db.delete(apiKeys).where(eq(apiKeys.id, apiKeyId));

      res.status(204).end();
    } catch (error) {
      console.error("Error deleting API key:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  });

  // ระงับการใช้งาน API key
  app.patch("/api/user/api-keys/:id/suspend", isAuthenticated, async (req, res) => {
    try {
      const apiKeyId = parseInt(req.params.id);
      if (isNaN(apiKeyId)) {
        return res.status(400).json({ error: "Invalid API key ID" });
      }

      // ตรวจสอบว่า API key นี้เป็นของผู้ใช้คนนี้หรือไม่
      const apiKey = await storage.getApiKey(apiKeyId);
      if (!apiKey) {
        return res.status(404).json({ error: "API key not found" });
      }

      if (apiKey.userId !== req.user!.id && req.user!.role !== 'admin') {
        return res.status(403).json({ error: "Forbidden" });
      }

      // ระงับการใช้งาน API key
      const updatedApiKey = await storage.updateApiKey(apiKeyId, { status: 'revoked' });

      res.json(updatedApiKey);
    } catch (error) {
      console.error("Error suspending API key:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  });

  // เปิดใช้งาน API key
  app.patch("/api/user/api-keys/:id/activate", isAuthenticated, async (req, res) => {
    try {
      const apiKeyId = parseInt(req.params.id);
      if (isNaN(apiKeyId)) {
        return res.status(400).json({ error: "Invalid API key ID" });
      }

      // ตรวจสอบว่า API key นี้เป็นของผู้ใช้คนนี้หรือไม่
      const apiKey = await storage.getApiKey(apiKeyId);
      if (!apiKey) {
        return res.status(404).json({ error: "API key not found" });
      }

      if (apiKey.userId !== req.user!.id && req.user!.role !== 'admin') {
        return res.status(403).json({ error: "Forbidden" });
      }

      // เปิดใช้งาน API key
      const updatedApiKey = await storage.updateApiKey(apiKeyId, { status: 'active' });

      res.json(updatedApiKey);
    } catch (error) {
      console.error("Error activating API key:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  });

  // ดูประวัติการใช้งาน API
  app.get("/api/user/api-keys/:id/logs", isAuthenticated, async (req, res) => {
    try {
      const apiKeyId = parseInt(req.params.id);
      if (isNaN(apiKeyId)) {
        return res.status(400).json({ error: "Invalid API key ID" });
      }

      // ตรวจสอบว่า API key นี้เป็นของผู้ใช้คนนี้หรือไม่
      const apiKey = await storage.getApiKey(apiKeyId);
      if (!apiKey) {
        return res.status(404).json({ error: "API key not found" });
      }

      if (apiKey.userId !== req.user!.id && req.user!.role !== 'admin') {
        return res.status(403).json({ error: "Forbidden" });
      }

      // ดึงประวัติการใช้งาน
      const logs = await storage.getApiKeyLogs(apiKeyId);

      res.json(logs);
    } catch (error) {
      console.error("Error getting API key logs:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  });

  // สร้าง API Key ใหม่ (regenerate)
  app.post("/api/user/api-keys/:id/regenerate", isAuthenticated, async (req, res) => {
    try {
      const apiKeyId = parseInt(req.params.id);
      if (isNaN(apiKeyId)) {
        return res.status(400).json({ error: "Invalid API key ID" });
      }

      // ตรวจสอบว่า API key นี้เป็นของผู้ใช้คนนี้หรือไม่
      const apiKey = await storage.getApiKey(apiKeyId);
      if (!apiKey) {
        return res.status(404).json({ error: "API key not found" });
      }

      if (apiKey.userId !== req.user!.id && req.user!.role !== 'admin') {
        return res.status(403).json({ error: "Forbidden" });
      }

      // สร้าง API key ใหม่
      const newApiKeyValue = generateApiKey();

      // อัพเดท API key ในฐานข้อมูล
      const updatedApiKey = await storage.updateApiKey(apiKeyId, {
        apiKey: newApiKeyValue,
        updatedAt: new Date()
      });

      if (!updatedApiKey) {
        return res.status(500).json({ error: "Failed to regenerate API key" });
      }

      console.log("API key regenerated successfully:", {
        id: updatedApiKey.id,
        name: updatedApiKey.name,
        hasApiKey: !!updatedApiKey.apiKey
      });

      // ส่งค่า API key กลับ (นี่เป็นครั้งเดียวที่จะแสดง API key เต็ม)
      res.json({
        ...updatedApiKey,
        apiKey: newApiKeyValue
      });
    } catch (error) {
      console.error("Error regenerating API key:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  });
}