import { useQuery } from "@tanstack/react-query";
import { DashboardLayout } from "@/components/layouts/dashboard-responsive-new";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { PackageCard } from "@/components/packages/package-card";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Crown, Sparkles, Zap, Star, Shield, Percent, Ticket, RefreshCw } from "lucide-react";
import { Input } from "@/components/ui/input";
import { motion } from "framer-motion";
import { useAuth } from "@/hooks/use-auth";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { Progress } from "@/components/ui/progress";
import { calculatePriceWithDiscount } from "@/lib/price-utils";

// Interface สำหรับข้อมูลแพ็กเกจ
interface Package {
  id: number;
  name: string;
  description: string;
  price: number;
  requestsLimit: number;
  isActive: boolean;
  features: string[];
  tag?: string;
  duration_days?: number;
  discount3Months?: number;
  discount6Months?: number;
  discount12Months?: number;
  creditPerVerification?: number; // เครดิตที่ต้องใช้ต่อการตรวจสอบ 1 ครั้ง (กรณีโควต้าหมด)
}

// พื้นหลังสไตล์เทพเจ้า
const CelestialBackground = () => (
  <div className="absolute inset-0 overflow-hidden">
    <div className="absolute inset-0 bg-gradient-to-b from-indigo-900/20 via-transparent to-transparent"></div>
    <div className="absolute top-0 left-0 w-full h-96 bg-gradient-radial from-indigo-600/10 via-transparent to-transparent"></div>
    <div className="absolute -top-40 -right-40 w-96 h-96 bg-indigo-600/5 rounded-full blur-3xl"></div>
    <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-purple-600/5 rounded-full blur-3xl"></div>

    {/* เพิ่มแสงของดาวเทพเจ้าที่เคลื่อนไหว */}
    <div className="absolute top-1/4 right-1/4 w-40 h-40 bg-indigo-400/5 rounded-full blur-3xl cosmic-pulse"></div>
    <div className="absolute bottom-1/4 left-1/3 w-32 h-32 bg-purple-500/5 rounded-full blur-3xl cosmic-pulse-slow"></div>

    {/* เพิ่มประกายรัศมีที่สว่างวาบ */}
    <div className="absolute top-1/3 left-1/5 w-2 h-2 bg-yellow-300/30 rounded-full divine-flash"></div>
    <div className="absolute bottom-1/3 right-1/5 w-1 h-1 bg-blue-300/30 rounded-full divine-flash" style={{ animationDelay: '1.5s' }}></div>
    <div className="absolute top-2/3 left-1/2 w-1.5 h-1.5 bg-purple-300/30 rounded-full divine-flash" style={{ animationDelay: '0.7s' }}></div>

    <style dangerouslySetInnerHTML={{ __html: `
      @keyframes floatingStar {
        0% { transform: translate(0, 0) rotate(0deg); }
        50% { transform: translate(10px, -10px) rotate(5deg); }
        100% { transform: translate(0, 0) rotate(0deg); }
      }
      .floating-star {
        animation: floatingStar 8s ease-in-out infinite;
      }

      @keyframes cosmicPulse {
        0% { opacity: 0.3; transform: scale(0.9); }
        50% { opacity: 0.8; transform: scale(1.1); }
        100% { opacity: 0.3; transform: scale(0.9); }
      }
      .cosmic-pulse {
        animation: cosmicPulse 6s ease-in-out infinite;
      }
      .cosmic-pulse-slow {
        animation: cosmicPulse 9s ease-in-out infinite;
      }

      @keyframes divineFlash {
        0% { opacity: 0; transform: scale(0); }
        20% { opacity: 1; transform: scale(1.2); }
        40% { opacity: 0.5; transform: scale(0.8); }
        60% { opacity: 0.8; transform: scale(1.1); }
        80% { opacity: 0.2; transform: scale(0.9); }
        100% { opacity: 0; transform: scale(0); }
      }
      .divine-flash {
        animation: divineFlash 4s ease-out infinite;
      }

      @keyframes glowPulse {
        0% { box-shadow: 0 0 5px 1px rgba(139, 92, 246, 0.3); }
        50% { box-shadow: 0 0 15px 5px rgba(139, 92, 246, 0.6); }
        100% { box-shadow: 0 0 5px 1px rgba(139, 92, 246, 0.3); }
      }

      .tab-active-glow {
        animation: glowPulse 3s ease-in-out infinite;
      }
    `}} />
    <div className="absolute top-20 right-20 text-amber-300 opacity-20 floating-star">
      <Star className="h-16 w-16" />
    </div>
    <div className="absolute bottom-20 left-40 text-purple-300 opacity-10 floating-star" style={{ animationDelay: "-2s" }}>
      <Star className="h-24 w-24" />
    </div>
  </div>
);

export default function UserPackagesPage() {
  const { user } = useAuth();
  const { toast } = useToast();

  // สำหรับจัดการคูปอง
  const [couponCode, setCouponCode] = useState("");
  const [isApplyingCoupon, setIsApplyingCoupon] = useState(false);
  // เก็บข้อมูลคูปองที่ใช้งานสำหรับแต่ละแพ็กเกจ
  const [couponData, setCouponData] = useState<Record<number, {
    id: number;
    code: string;
    discountPercent: number;
    discountAmount: number;
  } | undefined>>({});

  // ดึงข้อมูลแพ็กเกจ
  const { data: packages, isLoading } = useQuery<Package[]>({
    queryKey: ["/api/packages"],
  });

  // คำนวณค่าเฉลี่ยของเปอร์เซ็นต์ส่วนลดจากแพ็กเกจทั้งหมด
  const getAverageDiscountPercent = (months: 3 | 6 | 12) => {
    if (!packages || packages.length === 0) return 0;

    let totalDiscount = 0;
    let count = 0;

    packages.forEach(pkg => {
      if (months === 3 && pkg.discount3Months) {
        totalDiscount += pkg.discount3Months;
        count++;
      } else if (months === 6 && pkg.discount6Months) {
        totalDiscount += pkg.discount6Months;
        count++;
      } else if (months === 12 && pkg.discount12Months) {
        totalDiscount += pkg.discount12Months;
        count++;
      }
    });

    return count > 0 ? Math.round(totalDiscount / count) : 0;
  };

  // ดึงข้อมูล active package ของผู้ใช้
  const { data: activePackage } = useQuery<any>({
    queryKey: ["/api/user/active-package"],
    enabled: !!user,
  });

  // ดึงข้อมูลสถิติผู้ใช้
  const { data: stats } = useQuery<{
    totalVerifications: number;
    successfulVerifications: number;
    failedVerifications: number;
  }>({
    queryKey: ["/api/stats"],
    enabled: !!user,
  });

  // ดึงข้อมูลเครดิตผู้ใช้
  const { data: creditData } = useQuery<{ credit: number }>({
    queryKey: ["/api/user/credit"],
    enabled: !!user,
  });

  // นำเอาข้อความคุณสมบัติมาเป็นออบเจ็กต์
  const parseFeatures = (
    features: string[] | undefined,
  ): { name: string; included: boolean }[] => {
    if (!features) return [];

    return features.map((feature) => {
      const included = !feature.startsWith("!");
      const name = included ? feature : feature.substring(1);
      return { name, included };
    });
  };

  // ตรวจสอบคูปอง
  const validateCoupon = async (code: string, packageId: number) => {
    if (!code.trim()) {
      toast({
        title: "กรุณาระบุรหัสคูปอง",
        description: "โปรดป้อนรหัสคูปองก่อนทำการตรวจสอบ",
        variant: "destructive",
      });
      return;
    }

    setIsApplyingCoupon(true);

    try {
      const res = await fetch("/api/coupons/verify", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ code })
      });

      const data = await res.json();

      if (res.ok && data.valid) {
        // บันทึกข้อมูลคูปอง
        setCouponData(prev => ({
          ...prev,
          [packageId]: data.coupon
        }));

        toast({
          title: "คูปองถูกต้อง",
          description: `ใช้คูปอง ${data.coupon.code} สำเร็จ ได้รับส่วนลด ${data.coupon.discountPercent > 0 ? `${data.coupon.discountPercent}%` : `${data.coupon.discountAmount} บาท`}`,
        });

        setCouponCode("");
      } else {
        toast({
          title: "คูปองไม่ถูกต้อง",
          description: data.message || "คูปองไม่ถูกต้องหรือหมดอายุ",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "ไม่สามารถตรวจสอบคูปองได้",
        description: "เกิดข้อผิดพลาดในการตรวจสอบคูปอง กรุณาลองใหม่อีกครั้ง",
        variant: "destructive",
      });
    } finally {
      setIsApplyingCoupon(false);
    }
  };

  // ไปยังหน้า Dashboard เมื่อสมัครสำเร็จ
  const handleSubscriptionSuccess = () => {
    // รีเซ็ตข้อมูลคูปอง
    setCouponData({});
    setCouponCode("");

    toast({
      title: "สมัครแพ็กเกจสำเร็จ",
      description: "แพ็กเกจของคุณได้ถูกอัปเดตแล้ว",
    });
  };

  return (
    <DashboardLayout>
      <div className="relative min-h-screen py-8">
        <CelestialBackground />

        {/* แสดงข้อมูลส่วนหัว */}
        <div className="relative z-10">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="mb-8"
          >
            <h1 className="text-3xl font-bold mb-2 flex items-center">
              <Crown className="h-8 w-8 text-amber-400 mr-3" />
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-white to-indigo-200">
                แพ็กเกจของท่านเทพเจ้า
              </span>
            </h1>
            <p className="text-indigo-300 ml-11">
              เลือกแพ็กเกจที่เหมาะสมกับการใช้งานของท่านเพื่อเพิ่มพลังในการตรวจสอบสลิป
            </p>
          </motion.div>

          {/* Card สรุปข้อมูลแพ็กเกจปัจจุบัน */}
          {activePackage && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12"
            >
              {/* แสดงข้อมูลแพ็กเกจปัจจุบัน */}
              <Card className="bg-gradient-to-br from-indigo-950/80 to-purple-950/80 border-indigo-800/40 shadow-xl">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-xl text-indigo-100">แพ็กเกจปัจจุบัน</CardTitle>
                      <CardDescription className="text-indigo-300">สถานะการใช้งานของท่าน</CardDescription>
                    </div>
                    <div className="bg-amber-400/10 rounded-full p-1.5">
                      <Crown className="h-5 w-5 text-amber-400" />
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-2 mb-4">
                    <span className="text-2xl font-bold text-indigo-100">
                      {packages?.find(p => p.id === activePackage.packageId)?.name || "กำลังโหลด..."}
                    </span>
                    <span className="bg-amber-400/20 text-amber-300 text-xs rounded-full px-2 py-0.5">
                      Active
                    </span>
                  </div>
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-indigo-300">จำนวนที่ใช้ไป</span>
                        <span className="text-indigo-200">
                          {activePackage.requestsUsed || 0} / {activePackage.package?.requestsLimit || activePackage.requestsLimit || 0}
                        </span>
                      </div>
                      <Progress
                        value={(activePackage.requestsUsed / (activePackage.package?.requestsLimit || activePackage.requestsLimit || 1)) * 100}
                        className="h-2 bg-indigo-950"
                      />
                    </div>
                    <div className="pt-2 space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-indigo-300">วันที่เริ่มต้น</span>
                        <span className="text-indigo-200">
                          {new Date(activePackage.startDate).toLocaleDateString('th-TH')}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-indigo-300">วันที่สิ้นสุด</span>
                        <span className="text-indigo-200">
                          {new Date(activePackage.endDate).toLocaleDateString('th-TH')}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-indigo-300">สถานะ</span>
                        <span className="text-green-400">ใช้งานได้</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* แสดงเครดิตและสถิติการใช้งาน */}
              <Card className="bg-gradient-to-br from-indigo-950/80 to-purple-950/80 border-indigo-800/40 shadow-xl">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-xl text-indigo-100">เครดิตของท่าน</CardTitle>
                      <CardDescription className="text-indigo-300">ยอดเงินสำหรับการซื้อแพ็กเกจ</CardDescription>
                    </div>
                    <div className="bg-purple-500/10 rounded-full p-1.5">
                      <Star className="h-5 w-5 text-purple-400" />
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="mb-4">
                    <div className="text-3xl font-bold text-indigo-100 mb-1">
                      ฿ {creditData?.credit.toFixed(2) || '0.00'}
                    </div>
                    <p className="text-indigo-300 text-sm">
                      ใช้เครดิตนี้สำหรับการซื้อแพ็กเกจใหม่หรืออัพเกรด
                    </p>
                  </div>
                  <Button variant="outline" className="w-full bg-purple-800/30 hover:bg-purple-700/30 border-purple-700/40 text-indigo-100" asChild>
                    <a href="/topup">
                      <Zap className="mr-2 h-4 w-4 text-amber-400" />
                      เติมเงิน
                    </a>
                  </Button>
                </CardContent>
              </Card>

              {/* แสดงสถิติการใช้งาน */}
              <Card className="bg-gradient-to-br from-indigo-950/80 to-purple-950/80 border-indigo-800/40 shadow-xl">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-xl text-indigo-100">สถิติการใช้งาน</CardTitle>
                      <CardDescription className="text-indigo-300">ประวัติการตรวจสอบทั้งหมด</CardDescription>
                    </div>
                    <div className="bg-blue-500/10 rounded-full p-1.5">
                      <Zap className="h-5 w-5 text-blue-400" />
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4 mb-2">
                    <div className="bg-indigo-900/30 rounded-lg p-3 border border-indigo-800/30">
                      <div className="text-xl font-bold text-indigo-100">
                        {stats?.totalVerifications || 0}
                      </div>
                      <div className="text-xs text-indigo-300">การตรวจสอบทั้งหมด</div>
                    </div>
                    <div className="bg-indigo-900/30 rounded-lg p-3 border border-indigo-800/30">
                      <div className="text-xl font-bold text-green-400">
                        {stats?.successfulVerifications || 0}
                      </div>
                      <div className="text-xs text-indigo-300">ตรวจสอบสำเร็จ</div>
                    </div>
                  </div>
                  <Button variant="outline" className="w-full bg-blue-800/30 hover:bg-blue-700/30 border-blue-700/40 text-indigo-100" asChild>
                    <a href="/history">
                      ดูประวัติทั้งหมด
                    </a>
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* ส่วนแสดงรายการแพ็กเกจ */}
          <div className="text-center mb-8">
            <div className="inline-block mb-3">
              <motion.div
                className="bg-indigo-600/10 rounded-full p-2 inline-block"
                whileHover={{ rotate: 15 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <Crown className="h-6 w-6 text-amber-400" />
              </motion.div>
            </div>
            <h2 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-purple-600 mb-2">
              อัพเกรดพลังสวรรค์
            </h2>
            <p className="text-indigo-200 max-w-2xl mx-auto">
              เพิ่มศักยภาพในการตรวจสอบสลิปของคุณด้วยแพ็กเกจที่เหมาะกับธุรกิจ
            </p>
          </div>

          <div className="flex justify-center mb-6 space-x-4">
            <motion.div
              whileHover={{ y: -2 }}
              className="flex items-center text-xs font-medium text-indigo-100 bg-indigo-800/30 rounded-full py-1 px-3 border border-indigo-700/40"
            >
              <Shield className="h-3 w-3 text-green-400 mr-1.5" />
              <span>การันตีความแม่นยำ</span>
            </motion.div>
            <motion.div
              whileHover={{ y: -2 }}
              className="flex items-center text-xs font-medium text-indigo-100 bg-indigo-800/30 rounded-full py-1 px-3 border border-indigo-700/40"
            >
              <Zap className="h-3 w-3 text-amber-400 mr-1.5" />
              <span>ใช้งานได้ทันที</span>
            </motion.div>
            <motion.div
              whileHover={{ y: -2 }}
              className="flex items-center text-xs font-medium text-indigo-100 bg-indigo-800/30 rounded-full py-1 px-3 border border-indigo-700/40"
            >
              <Star className="h-3 w-3 text-purple-400 mr-1.5" />
              <span>เปลี่ยนแพ็กเกจได้ตลอด</span>
            </motion.div>
          </div>

          {/* ส่วนป้อนรหัสคูปอง */}
          <div className="mx-auto max-w-md mb-6">
            <div className="bg-indigo-900/30 border border-indigo-800/40 rounded-lg p-4">
              <div className="flex items-center mb-3">
                <Ticket className="h-5 w-5 text-amber-400 mr-2" />
                <h3 className="text-indigo-100 font-medium">มีรหัสคูปองส่วนลด?</h3>
              </div>
              <div className="flex space-x-2">
                <Input
                  type="text"
                  placeholder="ป้อนรหัสคูปองที่นี่"
                  value={couponCode}
                  onChange={(e) => setCouponCode(e.target.value)}
                  className="bg-indigo-950/50 border-indigo-700/40 text-indigo-100 focus-visible:ring-amber-500/50"
                />
                <Button
                  onClick={() => {
                    if (packages && packages.length > 0) {
                      validateCoupon(couponCode, packages[0].id);
                    }
                  }}
                  disabled={isApplyingCoupon || !couponCode.trim()}
                  className="bg-amber-500 hover:bg-amber-600 text-gray-900 font-medium"
                >
                  {isApplyingCoupon ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    "ตรวจสอบ"
                  )}
                </Button>
              </div>
              <p className="text-xs text-indigo-300 mt-2">
                ใช้รหัสคูปองเพื่อรับส่วนลดสำหรับแพ็กเกจที่ท่านสนใจ
              </p>
            </div>
          </div>

          <Tabs defaultValue="monthly" className="mb-4">
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.3 }}
            >
              <TabsList className="relative w-[600px] max-w-full h-16 grid grid-cols-4 mx-auto bg-gradient-to-b from-indigo-900/90 to-indigo-950/90 backdrop-blur-sm border border-indigo-700/30 p-1 rounded-xl overflow-hidden shadow-xl">
                {/* พื้นหลังแสงและเงาแบบมืออาชีพ */}
                <div className="absolute inset-0 overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-br from-indigo-900/80 to-purple-950/90 z-0"></div>
                  <div className="absolute bottom-0 left-0 right-0 h-1/3 bg-gradient-to-t from-indigo-500/10 to-transparent"></div>

                  {/* เส้นแสงด้านบนสไตล์ไฮเทค */}
                  <div className="absolute top-0 left-0 right-0 h-[1px] bg-indigo-400/30"></div>

                  {/* แสงออร่าด้านล่าง */}
                  <div className="absolute -bottom-10 left-1/4 right-1/4 h-16 bg-violet-500/20 blur-2xl rounded-full"></div>
                </div>

                {/* แท็บทริกเกอร์ 1 เดือน */}
                <TabsTrigger
                  value="monthly"
                  className="relative z-10 mx-1 rounded-lg h-full flex items-center justify-center
                    transition-all duration-300
                    data-[state=active]:shadow-lg data-[state=active]:shadow-indigo-400/20
                    data-[state=active]:bg-gradient-to-b data-[state=active]:from-indigo-500 data-[state=active]:to-indigo-700
                    data-[state=active]:border data-[state=active]:border-indigo-400/40
                    data-[state=active]:text-white
                    data-[state=inactive]:text-indigo-200 data-[state=inactive]:hover:text-white
                    data-[state=inactive]:hover:bg-indigo-700/50"
                >
                  <div className="absolute inset-0 rounded-lg overflow-hidden">
                    {/* เอฟเฟกต์เส้นแสงเคลื่อนไหวเมื่อ active */}
                    <div className="absolute inset-0 opacity-0 data-[state=active]:opacity-100">
                      <div className="absolute top-0 left-0 right-0 h-[1px] bg-indigo-300/70"></div>
                      <div className="absolute bottom-0 left-0 right-0 h-[1px] bg-indigo-300/30"></div>
                      <div className="absolute top-0 bottom-0 left-0 w-[1px] bg-indigo-300/30"></div>
                      <div className="absolute top-0 bottom-0 right-0 w-[1px] bg-indigo-300/30"></div>
                    </div>
                  </div>

                  <div className="relative flex flex-col items-center px-3 z-10">
                    <span className="text-sm font-medium">1 เดือน</span>
                    <div className="text-xs text-indigo-300/90 mt-0.5 flex items-center">
                      <span>ราคาปกติ</span>
                    </div>
                  </div>
                </TabsTrigger>

                {/* แท็บทริกเกอร์ 3 เดือน */}
                <TabsTrigger
                  value="3months"
                  className="relative z-10 mx-1 rounded-lg h-full flex items-center justify-center
                    transition-all duration-300
                    data-[state=active]:shadow-lg data-[state=active]:shadow-blue-400/20
                    data-[state=active]:bg-gradient-to-b data-[state=active]:from-blue-500 data-[state=active]:to-blue-700
                    data-[state=active]:border data-[state=active]:border-blue-400/40
                    data-[state=active]:text-white
                    data-[state=inactive]:text-indigo-200 data-[state=inactive]:hover:text-white
                    data-[state=inactive]:hover:bg-indigo-700/50"
                >
                  <div className="absolute inset-0 rounded-lg overflow-hidden">
                    {/* เอฟเฟกต์เส้นแสงเคลื่อนไหวเมื่อ active */}
                    <div className="absolute inset-0 opacity-0 data-[state=active]:opacity-100">
                      <div className="absolute top-0 left-0 right-0 h-[1px] bg-blue-300/70"></div>
                      <div className="absolute bottom-0 left-0 right-0 h-[1px] bg-blue-300/30"></div>
                      <div className="absolute top-0 bottom-0 left-0 w-[1px] bg-blue-300/30"></div>
                      <div className="absolute top-0 bottom-0 right-0 w-[1px] bg-blue-300/30"></div>
                    </div>
                  </div>

                  <div className="relative flex flex-col items-center px-3 z-10">
                    <span className="text-sm font-medium">3 เดือน</span>
                    <div className="text-xs text-blue-300/90 mt-0.5 flex items-center">
                      <Percent className="h-3 w-3 mr-0.5" />
                      <span>ส่วนลด {getAverageDiscountPercent(3)}%</span>
                    </div>
                    <motion.div
                      initial={{ scale: 0.5, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      transition={{ duration: 0.3 }}
                      className="w-1 h-1 rounded-full bg-blue-400 opacity-0 data-[state=active]:opacity-100"
                    ></motion.div>
                  </div>
                </TabsTrigger>

                {/* แท็บทริกเกอร์ 6 เดือน */}
                <TabsTrigger
                  value="6months"
                  className="relative z-10 mx-1 rounded-lg h-full flex items-center justify-center
                    transition-all duration-300
                    data-[state=active]:shadow-lg data-[state=active]:shadow-purple-400/20
                    data-[state=active]:bg-gradient-to-b data-[state=active]:from-purple-500 data-[state=active]:to-purple-700
                    data-[state=active]:border data-[state=active]:border-purple-400/40
                    data-[state=active]:text-white
                    data-[state=inactive]:text-indigo-200 data-[state=inactive]:hover:text-white
                    data-[state=inactive]:hover:bg-indigo-700/50"
                >
                  <div className="absolute inset-0 rounded-lg overflow-hidden">
                    {/* เอฟเฟกต์เส้นแสงเคลื่อนไหวเมื่อ active */}
                    <div className="absolute inset-0 opacity-0 data-[state=active]:opacity-100">
                      <div className="absolute top-0 left-0 right-0 h-[1px] bg-purple-300/70"></div>
                      <div className="absolute bottom-0 left-0 right-0 h-[1px] bg-purple-300/30"></div>
                      <div className="absolute top-0 bottom-0 left-0 w-[1px] bg-purple-300/30"></div>
                      <div className="absolute top-0 bottom-0 right-0 w-[1px] bg-purple-300/30"></div>
                    </div>
                  </div>

                  <div className="relative flex flex-col items-center px-3 z-10">
                    <span className="text-sm font-medium">6 เดือน</span>
                    <div className="text-xs text-purple-300/90 mt-0.5 flex items-center">
                      <Percent className="h-3 w-3 mr-0.5" />
                      <span>ส่วนลด {getAverageDiscountPercent(6)}%</span>
                    </div>
                  </div>
                </TabsTrigger>

                {/* แท็บทริกเกอร์ 12 เดือน */}
                <TabsTrigger
                  value="12months"
                  className="relative z-10 mx-1 rounded-lg h-full flex items-center justify-center
                    transition-all duration-300
                    data-[state=active]:shadow-lg data-[state=active]:shadow-amber-400/20
                    data-[state=active]:bg-gradient-to-b data-[state=active]:from-amber-500 data-[state=active]:to-amber-700
                    data-[state=active]:border data-[state=active]:border-amber-400/40
                    data-[state=active]:text-white
                    data-[state=inactive]:text-indigo-200 data-[state=inactive]:hover:text-white
                    data-[state=inactive]:hover:bg-indigo-700/50"
                >
                  <div className="absolute inset-0 rounded-lg overflow-hidden">
                    {/* เอฟเฟกต์เส้นแสงเคลื่อนไหวเมื่อ active */}
                    <div className="absolute inset-0 opacity-0 data-[state=active]:opacity-100">
                      <div className="absolute top-0 left-0 right-0 h-[1px] bg-amber-300/70"></div>
                      <div className="absolute bottom-0 left-0 right-0 h-[1px] bg-amber-300/30"></div>
                      <div className="absolute top-0 bottom-0 left-0 w-[1px] bg-amber-300/30"></div>
                      <div className="absolute top-0 bottom-0 right-0 w-[1px] bg-amber-300/30"></div>

                      {/* ออร่าเรืองแสงเฉพาะแท็บ 12 เดือน */}
                      <motion.div
                        className="absolute inset-0 bg-amber-500/10"
                        animate={{ opacity: [0.05, 0.15, 0.05] }}
                        transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                      />
                    </div>
                  </div>

                  <div className="relative flex flex-col items-center px-3 z-10">
                    <span className="text-sm font-medium">12 เดือน</span>
                    <div className="text-xs text-amber-300/90 mt-0.5 flex items-center">
                      <Percent className="h-3 w-3 mr-0.5" />
                      <span>ส่วนลด {getAverageDiscountPercent(12)}%</span>
                    </div>
                    <div className="absolute -top-1 -right-1">
                      <motion.div
                        animate={{ scale: [0.9, 1.1, 0.9], rotate: [0, 5, 0] }}
                        transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
                        className="opacity-0 data-[state=active]:opacity-100"
                      >
                        <div className="text-amber-400 text-xs">✦</div>
                      </motion.div>
                    </div>
                  </div>
                </TabsTrigger>
              </TabsList>
            </motion.div>

            <TabsContent value="monthly" className="mt-6">
              {isLoading ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[...Array(3)].map((_, index) => (
                    <div
                      key={index}
                      className="bg-gradient-to-br from-indigo-900/40 to-purple-900/30 border border-indigo-800/30 rounded-xl p-5"
                    >
                      <Skeleton className="h-7 w-1/2 mb-1 bg-indigo-700/20" />
                      <Skeleton className="h-4 w-3/4 mb-4 bg-indigo-700/20" />
                      <Skeleton className="h-10 w-1/2 mb-6 bg-indigo-700/20" />
                      <div className="space-y-3 mb-6">
                        {[...Array(3)].map((_, i) => (
                          <div key={i} className="flex items-start">
                            <Skeleton className="h-4 w-4 mr-2 bg-indigo-700/20" />
                            <Skeleton className="h-4 w-full bg-indigo-700/20" />
                          </div>
                        ))}
                      </div>
                      <Skeleton className="h-9 w-full bg-indigo-700/20" />
                    </div>
                  ))}
                </div>
              ) : packages ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {packages.map((pkg) => {
                    const isCurrentPackage =
                      activePackage && activePackage.packageId === pkg.id;
                    return (
                      <motion.div
                        key={pkg.id}
                        whileHover={{ scale: 1.02 }}
                        transition={{ type: "spring", stiffness: 400, damping: 10 }}
                        className={`relative overflow-hidden rounded-xl ${isCurrentPackage ? "shadow-xl shadow-amber-500/40 animate-pulse-gentle" : ""}`}
                      >
                        <PackageCard
                          id={pkg.id}
                          name={pkg.name}
                          description={pkg.description}
                          price={pkg.price}
                          discount3Months={pkg.discount3Months}
                          discount6Months={pkg.discount6Months}
                          discount12Months={pkg.discount12Months}
                          requestsLimit={pkg.requestsLimit}
                          creditPerVerification={pkg.creditPerVerification}
                          isPopular={isCurrentPackage || pkg.name === "มืออาชีพ" || pkg.name === "ธรรดา"}
                          features={parseFeatures(pkg.features)}
                          tag={isCurrentPackage ? "แพ็กเกจที่ใช้งานอยู่" : pkg.tag}
                          onSubscribe={handleSubscriptionSuccess}
                          usageData={isCurrentPackage && activePackage ? {
                            used: activePackage.requestsUsed,
                            total: activePackage.package.requestsLimit,
                            percentage: Math.min(Math.round((activePackage.requestsUsed / activePackage.package.requestsLimit) * 100), 100)
                          } : undefined}
                          durationMonths={isCurrentPackage && activePackage ? activePackage.durationMonths || 1 : 1}
                        />
                        {isCurrentPackage && (
                          <div className="absolute inset-0 border-4 border-amber-500 rounded-xl glow-gold"></div>
                        )}
                      </motion.div>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-12 bg-indigo-900/20 rounded-lg border border-indigo-800/30">
                  <Sparkles className="h-8 w-8 mx-auto mb-3 text-amber-400/70" />
                  <p className="text-indigo-200">ไม่พบข้อมูลแพ็กเกจในขณะนี้</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="3months" className="mt-6">
              {isLoading ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[...Array(3)].map((_, index) => (
                    <div
                      key={index}
                      className="bg-gradient-to-br from-indigo-900/40 to-purple-900/30 border border-indigo-800/30 rounded-xl p-5"
                    >
                      <Skeleton className="h-7 w-1/2 mb-1 bg-indigo-700/20" />
                      <Skeleton className="h-4 w-3/4 mb-4 bg-indigo-700/20" />
                      <Skeleton className="h-10 w-1/2 mb-6 bg-indigo-700/20" />
                      <div className="space-y-3 mb-6">
                        {[...Array(3)].map((_, i) => (
                          <div key={i} className="flex items-start">
                            <Skeleton className="h-4 w-4 mr-2 bg-indigo-700/20" />
                            <Skeleton className="h-4 w-full bg-indigo-700/20" />
                          </div>
                        ))}
                      </div>
                      <Skeleton className="h-9 w-full bg-indigo-700/20" />
                    </div>
                  ))}
                </div>
              ) : packages ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {packages.map((pkg) => (
                    <PackageCard
                      key={pkg.id}
                      id={pkg.id}
                      name={pkg.name}
                      description={pkg.description}
                      price={pkg.price}
                      discount3Months={pkg.discount3Months}
                      discount6Months={pkg.discount6Months}
                      discount12Months={pkg.discount12Months}
                      requestsLimit={pkg.requestsLimit} // จำนวนการใช้งานเท่าเดิมแต่จะรีเซ็ตทุกเดือน
                      creditPerVerification={pkg.creditPerVerification} // เครดิตที่ต้องใช้ต่อการตรวจสอบ 1 ครั้ง
                      durationMonths={3} // ส่งค่าระยะเวลา 3 เดือน
                      isPopular={pkg.name === "มืออาชีพ" || pkg.name === "ธรรดา"}
                      features={parseFeatures(pkg.features)}
                      tag={pkg.tag} // ส่งค่า tag ไปแสดงผล
                      onSubscribe={handleSubscriptionSuccess}
                      usageData={activePackage && pkg.id === activePackage.packageId ? {
                        used: activePackage.requestsUsed,
                        total: pkg.requestsLimit,
                        percentage: Math.min(Math.round((activePackage.requestsUsed / pkg.requestsLimit) * 100), 100)
                      } : undefined}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12 bg-indigo-900/20 rounded-lg border border-indigo-800/30">
                  <Sparkles className="h-8 w-8 mx-auto mb-3 text-amber-400/70" />
                  <p className="text-indigo-200">ไม่พบข้อมูลแพ็กเกจในขณะนี้</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="6months" className="mt-6">
              {isLoading ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[...Array(3)].map((_, index) => (
                    <div
                      key={index}
                      className="bg-gradient-to-br from-indigo-900/40 to-purple-900/30 border border-indigo-800/30 rounded-xl p-5"
                    >
                      <Skeleton className="h-7 w-1/2 mb-1 bg-indigo-700/20" />
                      <Skeleton className="h-4 w-3/4 mb-4 bg-indigo-700/20" />
                      <Skeleton className="h-10 w-1/2 mb-6 bg-indigo-700/20" />
                      <div className="space-y-3 mb-6">
                        {[...Array(3)].map((_, i) => (
                          <div key={i} className="flex items-start">
                            <Skeleton className="h-4 w-4 mr-2 bg-indigo-700/20" />
                            <Skeleton className="h-4 w-full bg-indigo-700/20" />
                          </div>
                        ))}
                      </div>
                      <Skeleton className="h-9 w-full bg-indigo-700/20" />
                    </div>
                  ))}
                </div>
              ) : packages ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {packages.map((pkg) => (
                    <PackageCard
                      key={pkg.id}
                      id={pkg.id}
                      name={pkg.name}
                      description={pkg.description}
                      price={pkg.price}
                      discount3Months={pkg.discount3Months}
                      discount6Months={pkg.discount6Months}
                      discount12Months={pkg.discount12Months}
                      requestsLimit={pkg.requestsLimit} // จำนวนการใช้งานเท่าเดิมแต่จะรีเซ็ตทุกเดือน
                      creditPerVerification={pkg.creditPerVerification} // เครดิตที่ต้องใช้ต่อการตรวจสอบ 1 ครั้ง
                      durationMonths={6} // ส่งค่าระยะเวลา 6 เดือน
                      isPopular={pkg.name === "มืออาชีพ" || pkg.name === "ธรรดา"}
                      features={parseFeatures(pkg.features)}
                      tag={pkg.tag}
                      onSubscribe={handleSubscriptionSuccess}
                      usageData={activePackage && pkg.id === activePackage.packageId ? {
                        used: activePackage.requestsUsed,
                        total: pkg.requestsLimit,
                        percentage: Math.min(Math.round((activePackage.requestsUsed / pkg.requestsLimit) * 100), 100)
                      } : undefined}
                      appliedCoupon={couponData[pkg.id]}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12 bg-indigo-900/20 rounded-lg border border-indigo-800/30">
                  <Sparkles className="h-8 w-8 mx-auto mb-3 text-amber-400/70" />
                  <p className="text-indigo-200">ไม่พบข้อมูลแพ็กเกจในขณะนี้</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="12months" className="mt-6">
              {isLoading ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[...Array(3)].map((_, index) => (
                    <div
                      key={index}
                      className="bg-gradient-to-br from-indigo-900/40 to-purple-900/30 border border-indigo-800/30 rounded-xl p-5"
                    >
                      <Skeleton className="h-7 w-1/2 mb-1 bg-indigo-700/20" />
                      <Skeleton className="h-4 w-3/4 mb-4 bg-indigo-700/20" />
                      <Skeleton className="h-10 w-1/2 mb-6 bg-indigo-700/20" />
                      <div className="space-y-3 mb-6">
                        {[...Array(3)].map((_, i) => (
                          <div key={i} className="flex items-start">
                            <Skeleton className="h-4 w-4 mr-2 bg-indigo-700/20" />
                            <Skeleton className="h-4 w-full bg-indigo-700/20" />
                          </div>
                        ))}
                      </div>
                      <Skeleton className="h-9 w-full bg-indigo-700/20" />
                    </div>
                  ))}
                </div>
              ) : packages ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {packages.map((pkg) => (
                    <PackageCard
                      key={pkg.id}
                      id={pkg.id}
                      name={pkg.name}
                      description={pkg.description}
                      price={pkg.price}
                      discount3Months={pkg.discount3Months}
                      discount6Months={pkg.discount6Months}
                      discount12Months={pkg.discount12Months}
                      requestsLimit={pkg.requestsLimit} // จำนวนการใช้งานเท่าเดิมแต่จะรีเซ็ตทุกเดือน
                      creditPerVerification={pkg.creditPerVerification} // เครดิตที่ต้องใช้ต่อการตรวจสอบ 1 ครั้ง
                      durationMonths={12} // ส่งค่าระยะเวลา 12 เดือน
                      isPopular={pkg.name === "มืออาชีพ" || pkg.name === "ธรรดา"}
                      features={parseFeatures(pkg.features)}
                      tag={pkg.tag}
                      onSubscribe={handleSubscriptionSuccess}
                      usageData={activePackage && pkg.id === activePackage.packageId ? {
                        used: activePackage.requestsUsed,
                        total: pkg.requestsLimit,
                        percentage: Math.min(Math.round((activePackage.requestsUsed / pkg.requestsLimit) * 100), 100)
                      } : undefined}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12 bg-indigo-900/20 rounded-lg border border-indigo-800/30">
                  <Sparkles className="h-8 w-8 mx-auto mb-3 text-amber-400/70" />
                  <p className="text-indigo-200">ไม่พบข้อมูลแพ็กเกจในขณะนี้</p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </DashboardLayout>
  );
}