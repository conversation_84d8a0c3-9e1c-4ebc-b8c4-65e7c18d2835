# SLIPKUY - ระบบตรวจสอบและยืนยันสลิปธนาคารระดับเทพเจ้า 💫

ระบบตรวจสอบและยืนยันสลิปธนาคารอัตโนมัติพร้อม API สำหรับเชื่อมต่อกับระบบภายนอก รองรับหลายธนาคารในไทย ด้วยเทคโนโลยีการตรวจจับรหัส QR ขั้นสูง

## ฟีเจอร์หลัก

- ⚡️ **ตรวจจับรหัส QR อัตโนมัติ** - ตรวจจับและอ่าน QR Code บนสลิปธนาคารแบบอัจฉริยะ
- 🔒 **ตรวจสอบความถูกต้อง** - ยืนยันความถูกต้องของสลิปผ่าน EasySlip API
- 📊 **รายงานแบบเรียลไทม์** - แสดงผลการตรวจสอบพร้อมวิเคราะห์ข้อมูลการใช้งาน
- 🔄 **Webhook Support** - แจ้งเตือนระบบภายนอกเมื่อมีการตรวจสอบสลิปใหม่
- 👥 **ระบบผู้ใช้และสมาชิก** - จัดการสมาชิกพร้อมระบบเติมเงินและแพ็คเกจ
- 📱 **รองรับมือถือ** - ใช้งานได้ทั้งบนเดสก์ท็อปและมือถือ
- 📦 **ระบบสำรองและกู้คืนข้อมูล** - พร้อมติดตั้งบน Windows, Linux และ Docker

## การติดตั้งระบบ

### ไฟล์สำรองข้อมูล

ในโฟลเดอร์ `backup` มีไฟล์สำรองข้อมูลที่แยกตามระบบปฏิบัติการ:

- **backup_windows/** - สำหรับระบบปฏิบัติการ Windows
  - `slipkuy_windows.tar.gz` - ไฟล์บีบอัดที่มีทุกอย่างสำหรับ Windows

- **backup_linux/** - สำหรับระบบปฏิบัติการ Linux
  - `slipkuy_linux.tar.gz` - ไฟล์บีบอัดที่มีทุกอย่างสำหรับ Linux

- **backup_docker/** - สำหรับการติดตั้งด้วย Docker
  - `slipkuy_docker.tar.gz` - ไฟล์บีบอัดที่มีทุกอย่างสำหรับ Docker

- **backup_full/** - ไฟล์ทั้งหมดรวมกัน
  - `slipkuy_complete_installation.tar.gz` - ไฟล์บีบอัดที่มีทุกอย่างสำหรับทุกแพลตฟอร์ม

### คำแนะนำการติดตั้ง

ดูรายละเอียดการติดตั้งเพิ่มเติมได้ในไฟล์ README.md ของแต่ละโฟลเดอร์

## บัญชีทดสอบ

- **ผู้ดูแลระบบ**:
  - Username: `tmognot`
  - Password: `12345678`

- **ผู้ใช้ทั่วไป**:
  - Username: `test`
  - Password: `12345678`

## API Keys

ระบบใช้ API ต่อไปนี้:

- SLIP2GO_API_KEY: `RNWeLFE7aTalOXURHjYghkNG0JOFS7RrQXWf93ZBVFc=`
- EASYSLIP_API_KEY: `db34c04e-9465-4b60-8bb6-5cdb3faa1973`

## สงวนลิขสิทธิ์

© 2025 สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า