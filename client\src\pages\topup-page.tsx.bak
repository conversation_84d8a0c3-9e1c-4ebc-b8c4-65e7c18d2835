import { useState, useEffect, useMemo } from "react";
import { useAuth } from "@/hooks/use-auth";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { formatCurrency } from "@/lib/utils";
import { DashboardLayout } from "@/components/layouts/dashboard-layout";
import { AlertCircle, Check, Copy, Loader2, UploadCloud, Sparkles, Wallet, CreditCard, Banknote, CoinsIcon, Gem, Star, Zap } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { QRCodeSVG } from "qrcode.react";
import { motion, AnimatePresence } from "framer-motion";

// Define schema for top-up form
const topUpSchema = z.object({
  amount: z.coerce
    .number()
    .int()
    .min(100, { message: "จำนวนเงินขั้นต่ำคือ 100 บาท" })
    .max(100000, { message: "จำนวนเงินสูงสุดคือ 100,000 บาท" }),
});

// Define interface for top-up data
interface TopUpTransaction {
  id: number;
  amount: number;
  status: string;
  referenceCode: string;
  createdAt: string;
  updatedAt: string;
  verificationId?: number;
}

// Create a function to convert numbers to Thai baht text
function numberToThaiText(num: number): string {
  const units = ['', 'หนึ่ง', 'สอง', 'สาม', 'สี่', 'ห้า', 'หก', 'เจ็ด', 'แปด', 'เก้า', 'สิบ'];
  const positions = ['', 'สิบ', 'ร้อย', 'พัน', 'หมื่น', 'แสน', 'ล้าน'];
  
  if (num === 0) return 'ศูนย์บาทถ้วน';
  
  let text = '';
  const numStr = num.toString();
  
  for (let i = 0; i < numStr.length; i++) {
    const digit = parseInt(numStr[i]);
    const pos = numStr.length - i - 1;
    
    if (digit !== 0) {
      if (pos === 1 && digit === 1) {
        text += 'สิบ';
      } else if (pos === 1 && digit === 2) {
        text += 'ยี่สิบ';
      } else {
        text += units[digit] + positions[pos];
      }
    }
  }
  
  return text + 'บาทถ้วน';
}

export default function TopUpPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isVerifyDialogOpen, setIsVerifyDialogOpen] = useState(false);
  const [selectedTopUp, setSelectedTopUp] = useState<TopUpTransaction | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  
  // Create form
  const form = useForm<z.infer<typeof topUpSchema>>({
    resolver: zodResolver(topUpSchema),
    defaultValues: {
      amount: 1000,
    },
  });
  
  // Query for user credit
  const { data: creditData, isLoading: isLoadingCredit } = useQuery<{ credit: number }>({
    queryKey: ["/api/user/credit"],
    enabled: !!user,
  });
  
  // ดึงข้อมูลธนาคารจาก API
  const { data: paymentAccounts, isLoading: isLoadingPaymentAccounts } = useQuery<{
    bankAccounts: Array<{
      bankCode: string;
      accountNumber: string;
      accountName: string;
      accountNameEn: string;
      qrPaymentImage: string;
    }>;
    promptpays: string[];
  }>({
    queryKey: ["/api/payment-accounts"],
  });
  
  // Query for user's top-up transactions
  const { data: transactions, isLoading: isLoadingTransactions } = useQuery<TopUpTransaction[]>({
    queryKey: ["/api/topup"],
    enabled: !!user,
  });
  
  // Create topup mutation
  const createTopUpMutation = useMutation({
    mutationFn: async (amount: number) => {
      const res = await apiRequest("POST", "/api/topup/create", { amount });
      return await res.json();
    },
    onSuccess: (data: TopUpTransaction) => {
      toast({
        title: "สร้างรายการเติมเงินสำเร็จ",
        description: `รหัสอ้างอิง: ${data.referenceCode}`,
      });
      queryClient.invalidateQueries({ queryKey: ["/api/topup"] });
      setSelectedTopUp(data);
    },
    onError: (error: Error) => {
      toast({
        title: "ไม่สามารถสร้างรายการเติมเงินได้",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Verify topup mutation
  const verifyTopUpMutation = useMutation({
    mutationFn: async ({ id, formData }: { id: number; formData: FormData }) => {
      const res = await fetch(`/api/topup/verify/${id}`, {
        method: "POST",
        body: formData,
        credentials: "include",
      });
      
      if (!res.ok) {
        const error = await res.json();
        throw new Error(error.error || "เกิดข้อผิดพลาดในการตรวจสอบสลิป");
      }
      
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "ตรวจสอบสลิปสำเร็จ",
        description: "เติมเงินเข้าบัญชีเรียบร้อยแล้ว",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/topup"] });
      queryClient.invalidateQueries({ queryKey: ["/api/user/credit"] });
      setIsVerifyDialogOpen(false);
      setSelectedTopUp(null);
      setSelectedFile(null);
      setUploadProgress(0);
    },
    onError: (error: Error) => {
      toast({
        title: "ไม่สามารถตรวจสอบสลิปได้",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Handle form submission
  const onSubmit = (values: z.infer<typeof topUpSchema>) => {
    createTopUpMutation.mutate(values.amount);
  };
  
  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0]);
    }
  };
  
  // Handle file upload and verification
  const handleVerify = () => {
    if (!selectedFile || !selectedTopUp) return;
    
    const formData = new FormData();
    formData.append("slip", selectedFile);
    
    // Simulate upload progress
    let progress = 0;
    const interval = setInterval(() => {
      progress += 5;
      if (progress >= 100) {
        clearInterval(interval);
      }
      setUploadProgress(progress);
    }, 100);
    
    verifyTopUpMutation.mutate(
      { id: selectedTopUp.id, formData },
      {
        onSettled: () => {
          clearInterval(interval);
          setUploadProgress(100);
        },
      }
    );
  };
  
  // Open verify dialog
  const openVerifyDialog = (topUp: TopUpTransaction) => {
    setSelectedTopUp(topUp);
    setIsVerifyDialogOpen(true);
  };
  
  // Copy reference code to clipboard
  const copyReferenceCode = (code: string) => {
    navigator.clipboard.writeText(code);
    toast({
      title: "คัดลอกรหัสอ้างอิงแล้ว",
      description: code,
    });
  };
  
  // Get transaction status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-500">เสร็จสมบูรณ์</Badge>;
      case "pending":
        return <Badge className="bg-yellow-500">รอการยืนยัน</Badge>;
      case "failed":
        return <Badge className="bg-red-500">ล้มเหลว</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };
  

  
  // ข้อมูลธนาคารไทย สำหรับแสดงชื่อธนาคาร
  const THAI_BANKS = [
    { code: "004", name: "ธนาคารกสิกรไทย" },
    { code: "002", name: "ธนาคารกรุงเทพ" },
    { code: "006", name: "ธนาคารกรุงไทย" },
    { code: "011", name: "ธนาคารทหารไทยธนชาต" },
    { code: "014", name: "ธนาคารไทยพาณิชย์" },
    { code: "025", name: "ธนาคารกรุงศรีอยุธยา" },
    { code: "030", name: "ธนาคารออมสิน" },
    { code: "034", name: "ธนาคารเกียรตินาคิน" },
    { code: "067", name: "ธนาคารทิสโก้" },
    { code: "065", name: "ธนาคารธนชาต" },
    { code: "069", name: "ธนาคารเกียรตินาคินภัทร" },
    { code: "073", name: "ธนาคารแลนด์ แอนด์ เฮ้าส์" },
    { code: "022", name: "ธนาคารซีไอเอ็มบี ไทย" },
    { code: "018", name: "ธนาคารยูโอบี" },
    { code: "033", name: "ธนาคารอาคารสงเคราะห์" },
    { code: "070", name: "ธนาคารไอซีบีซี (ไทย)" },
    { code: "031", name: "ธนาคารเพื่อการเกษตรและสหกรณ์การเกษตร" },
  ];

  // ถ้ายังไม่มีข้อมูลบัญชีธนาคาร ให้ใช้ค่าเริ่มต้น
  const bankInfo = useMemo(() => {
    if (isLoadingPaymentAccounts || !paymentAccounts || !paymentAccounts.bankAccounts || !paymentAccounts.bankAccounts.length) {
      return {
        name: "กรุณารอสักครู่...",
        accountName: "กำลังโหลดข้อมูล",
        accountNameEn: "Loading...",
        accountNumber: "-",
        promptpayNumber: "-",
        qrPaymentImage: "",
      };
    }

    // เลือกบัญชีแรกเป็นค่าเริ่มต้น
    const firstBank = paymentAccounts.bankAccounts[0];
    const bankInfo = {
      code: firstBank.bankCode,
      name: THAI_BANKS.find(bank => bank.code === firstBank.bankCode)?.name || firstBank.bankCode,
      accountName: firstBank.accountName,
      accountNameEn: firstBank.accountNameEn,
      accountNumber: firstBank.accountNumber,
      promptpayNumber: paymentAccounts.promptpays && paymentAccounts.promptpays.length > 0 
        ? paymentAccounts.promptpays[0] 
        : "-",
      qrPaymentImage: firstBank.qrPaymentImage || "",
    };
    
    return bankInfo;
  }, [paymentAccounts, isLoadingPaymentAccounts]);
  
  return (
    <DashboardLayout>
      <div className="relative z-10">
        {/* Decorative Background Elements */}
        <div className="absolute -top-12 left-1/4 w-20 h-20 rounded-full bg-amber-600/10 filter blur-2xl"></div>
        <div className="absolute top-40 right-20 w-32 h-32 rounded-full bg-indigo-600/20 filter blur-3xl"></div>
        <div className="absolute bottom-40 left-10 w-24 h-24 rounded-full bg-purple-600/10 filter blur-2xl"></div>
        
        <motion.div 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="relative z-10 container mx-auto py-6 max-w-6xl"
        >
        
        <div className="flex items-center mb-6">
          <div className="mr-3 relative">
            <div className="absolute -inset-1 rounded-full bg-amber-600/30 animate-pulse blur-md"></div>
            <div className="relative bg-gradient-to-br from-amber-700 to-amber-900 h-10 w-10 rounded-full flex items-center justify-center">
              <Banknote className="h-5 w-5 text-amber-300" />
            </div>
          </div>
          <div>
            <h1 className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-100 to-white">การเติมพลังทิพย์</h1>
            <p className="text-indigo-300 text-sm">เพิ่มกำลังพลในการใช้บริการตรวจสอบของเทพเจ้า</p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Credit Card */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="md:col-span-1"
          >
            <Card className="overflow-hidden backdrop-blur-md bg-gradient-to-br from-indigo-950/80 to-purple-900/80 border-indigo-800/30 shadow-lg">
              <CardHeader className="relative">
                <div className="absolute inset-0 bg-gradient-to-br from-amber-800/30 to-amber-900/10"></div>
                <div className="relative z-10 flex items-center">
                  <div className="mr-3 h-8 w-8 rounded-full bg-gradient-to-br from-amber-600 to-amber-800 flex items-center justify-center">
                    <Gem className="h-4 w-4 text-amber-100" />
                  </div>
                  <div>
                    <CardTitle className="text-indigo-100">คลังสมบัติของเทพเจ้า</CardTitle>
                    <CardDescription className="text-indigo-300">พลังทิพย์ที่ท่านสะสมไว้ในอาณาจักร</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-4">
                <motion.div 
                  className="text-center relative"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  {isLoadingCredit ? (
                    <div className="flex items-center justify-center py-6">
                      <div className="h-10 w-10 rounded-full border-4 border-amber-600 border-t-transparent animate-spin"></div>
                    </div>
                  ) : (
                    <div className="relative py-4">
                      <div className="absolute -inset-2 rounded-full bg-amber-600/20 animate-pulse blur-md"></div>
                      <motion.div 
                        className="text-4xl font-bold bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent relative"
                        initial={{ scale: 0.8 }}
                        animate={{ scale: 1 }}
                        transition={{ 
                          type: "spring", 
                          bounce: 0.5,
                          duration: 0.8 
                        }}
                      >
                        {formatCurrency(creditData?.credit || 0)}
                        <motion.div
                          className="absolute -right-4 -top-2"
                          initial={{ opacity: 0, rotate: -45 }}
                          animate={{ opacity: 1, rotate: 0 }}
                          transition={{ delay: 0.5 }}
                        >
                          <Sparkles className="h-5 w-5 text-amber-400" />
                        </motion.div>
                      </motion.div>
                      <p className="text-xs text-indigo-400 mt-2">ใช้ในการอัปเกรดพลังทิพย์และความสามารถ</p>
                    </div>
                  )}
                </motion.div>
              </CardContent>
            </Card>
          </motion.div>
          
          {/* Top-up Form */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="md:col-span-2"
          >
            <Card className="backdrop-blur-md overflow-hidden bg-gradient-to-br from-indigo-950/80 to-purple-900/80 border-indigo-800/30 shadow-lg">
              <CardHeader className="relative">
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-800/30 to-indigo-900/10"></div>
                <div className="relative z-10 flex items-center">
                  <div className="mr-3 h-8 w-8 rounded-full bg-gradient-to-br from-indigo-600 to-indigo-800 flex items-center justify-center">
                    <Zap className="h-4 w-4 text-indigo-100" />
                  </div>
                  <div>
                    <CardTitle className="text-indigo-100">การเพิ่มพลังทิพย์</CardTitle>
                    <CardDescription className="text-indigo-300">ส่งพลังงานจากโลกมนุษย์สู่สรวงสวรรค์ของเทพเจ้า</CardDescription>
                  </div>
                </div>
              </CardHeader>
            <CardContent>
              <Tabs defaultValue="promptpay" className="w-full">
                <TabsList className="grid w-full grid-cols-2 mb-4">
                  <TabsTrigger value="promptpay">พร้อมเพย์</TabsTrigger>
                  <TabsTrigger value="bank">บัญชีธนาคาร</TabsTrigger>
                </TabsList>
                
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                    <FormField
                      control={form.control}
                      name="amount"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>จำนวนเงิน (บาท)</FormLabel>
                          <FormControl>
                            <Input type="number" placeholder="กรอกจำนวนเงิน" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    {form.watch("amount") > 0 && (
                      <div className="text-sm text-gray-500">
                        จำนวนเงิน: {numberToThaiText(form.watch("amount"))}
                      </div>
                    )}
                    
                    {selectedTopUp ? (
                      <div className="space-y-4">
                        <Alert className="bg-green-50 border-green-500">
                          <Check className="h-4 w-4 text-green-500" />
                          <AlertTitle>สร้างรายการเติมเงินสำเร็จ</AlertTitle>
                          <AlertDescription>
                            รหัสอ้างอิง: {selectedTopUp.referenceCode}
                            <div className="mt-1">
                              <Button variant="outline" size="sm" className="gap-1" onClick={() => copyReferenceCode(selectedTopUp.referenceCode)}>
                                <Copy className="h-3.5 w-3.5" />
                                คัดลอก
                              </Button>
                            </div>
                          </AlertDescription>
                        </Alert>
                        
                        <TabsContent value="promptpay">
                          <div className="flex flex-col items-center p-4 bg-white border rounded-md">
                            <div className="text-center mb-4">
                              <p className="font-semibold">สแกน QR Code เพื่อชำระเงิน</p>
                              <p className="text-sm text-gray-500">PromptPay: {bankInfo.promptpayNumber}</p>
                              <p className="text-sm text-gray-500">{bankInfo.accountName}</p>
                              <p className="text-sm text-gray-500 italic">{bankInfo.accountNameEn}</p>
                              <p className="font-medium mt-1">{formatCurrency(selectedTopUp.amount)}</p>
                            </div>
                            
                            <div className="bg-white p-3 rounded-lg border mb-4">
                              <QRCodeSVG 
                                value={`https://promptpay.io/${bankInfo.promptpayNumber}/${selectedTopUp.amount}`} 
                                size={200}
                                level="H"
                                includeMargin
                              />
                            </div>
                            
                            <Button 
                              type="button" 
                              onClick={() => openVerifyDialog(selectedTopUp)}
                              className="w-full"
                            >
                              อัปโหลดสลิปและยืนยันการชำระเงิน
                            </Button>
                          </div>
                        </TabsContent>
                        
                        <TabsContent value="bank">
                          <div className="p-4 bg-white border rounded-md">
                            <div className="space-y-3 mb-4">
                              <div>
                                <p className="text-sm text-gray-500">ธนาคาร</p>
                                <p className="font-medium">{bankInfo.name}</p>
                              </div>
                              <div>
                                <p className="text-sm text-gray-500">ชื่อบัญชี</p>
                                <p className="font-medium">{bankInfo.accountName}</p>
                                <p className="text-sm text-gray-500 italic">{bankInfo.accountNameEn}</p>
                              </div>
                              <div>
                                <p className="text-sm text-gray-500">เลขที่บัญชี</p>
                                <p className="font-medium flex items-center">
                                  {bankInfo.accountNumber}
                                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0 ml-1" onClick={() => copyReferenceCode(bankInfo.accountNumber)}>
                                    <Copy className="h-3.5 w-3.5" />
                                  </Button>
                                </p>
                              </div>
                              <div>
                                <p className="text-sm text-gray-500">จำนวนเงิน</p>
                                <p className="font-medium">{formatCurrency(selectedTopUp.amount)}</p>
                              </div>
                              <div>
                                <p className="text-sm text-gray-500">รหัสอ้างอิง (ใส่ในช่องหมายเหตุ)</p>
                                <p className="font-medium flex items-center">
                                  {selectedTopUp.referenceCode}
                                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0 ml-1" onClick={() => copyReferenceCode(selectedTopUp.referenceCode)}>
                                    <Copy className="h-3.5 w-3.5" />
                                  </Button>
                                </p>
                              </div>

                              {bankInfo.qrPaymentImage && (
                                <div className="mt-4">
                                  <p className="text-sm text-gray-500 mb-2">QR Code สำหรับชำระเงิน</p>
                                  <div className="flex justify-center">
                                    <div className="bg-white p-2 rounded-lg border mb-4 max-w-[250px]">
                                      <img 
                                        src={bankInfo.qrPaymentImage} 
                                        alt="QR Payment" 
                                        className="max-w-full h-auto"
                                      />
                                    </div>
                                  </div>
                                </div>
                              )}
                            </div>
                            
                            <Button 
                              type="button" 
                              onClick={() => openVerifyDialog(selectedTopUp)}
                              className="w-full"
                            >
                              อัปโหลดสลิปและยืนยันการชำระเงิน
                            </Button>
                          </div>
                        </TabsContent>
                      </div>
                    ) : (
                      <Button 
                        type="submit" 
                        className="w-full" 
                        disabled={createTopUpMutation.isPending}
                      >
                        {createTopUpMutation.isPending ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            กำลังดำเนินการ...
                          </>
                        ) : (
                          "สร้างรายการเติมเงิน"
                        )}
                      </Button>
                    )}
                  </form>
                </Form>
              </Tabs>
            </CardContent>
          </Card>
        </motion.div>
        
        {/* Transaction History */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="mt-8"
        >
          <div className="flex items-center mb-4">
            <div className="mr-3 relative">
              <div className="absolute -inset-1 rounded-full bg-indigo-600/30 animate-pulse blur-md"></div>
              <div className="relative bg-gradient-to-br from-indigo-700 to-indigo-900 h-8 w-8 rounded-full flex items-center justify-center">
                <Star className="h-4 w-4 text-indigo-300" />
              </div>
            </div>
            <h2 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-100 to-white">บันทึกการส่งพลังทิพย์</h2>
          </div>
          
          <Card className="backdrop-blur-md overflow-hidden bg-gradient-to-br from-indigo-950/80 to-purple-900/80 border-indigo-800/30 shadow-lg">
            <CardContent className="p-0">
              {isLoadingTransactions ? (
                <div className="p-6 text-center">
                  <div className="h-10 w-10 rounded-full border-4 border-indigo-600 border-t-transparent animate-spin mx-auto mb-2"></div>
                  <p className="text-indigo-300">กำลังเชื่อมต่อกับแดนสวรรค์...</p>
                </div>
              ) : !transactions || transactions.length === 0 ? (
                <div className="p-6 text-center text-indigo-300">
                  <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-indigo-900/50 mb-4">
                    <Sparkles className="h-8 w-8 text-indigo-400/70" />
                  </div>
                  <p>ยังไม่มีประวัติการส่งพลังทิพย์</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b bg-gray-50">
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">วันที่</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">รหัสอ้างอิง</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">จำนวนเงิน</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">สถานะ</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">การกระทำ</th>
                      </tr>
                    </thead>
                    <tbody>
                      {transactions.map((transaction) => (
                        <tr key={transaction.id} className="border-b">
                          <td className="px-4 py-3 text-sm">
                            {new Date(transaction.createdAt).toLocaleString("th-TH")}
                          </td>
                          <td className="px-4 py-3 text-sm font-medium">{transaction.referenceCode}</td>
                          <td className="px-4 py-3 text-sm">{formatCurrency(transaction.amount)}</td>
                          <td className="px-4 py-3 text-sm">{getStatusBadge(transaction.status)}</td>
                          <td className="px-4 py-3 text-sm">
                            {transaction.status === "pending" && (
                              <Button 
                                variant="outline" 
                                size="sm" 
                                onClick={() => openVerifyDialog(transaction)}
                              >
                                ยืนยันการชำระเงิน
                              </Button>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </div>
      
      {/* Verify Dialog */}
      <Dialog open={isVerifyDialogOpen} onOpenChange={setIsVerifyDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>ยืนยันการชำระเงิน</DialogTitle>
            <DialogDescription>
              อัปโหลดสลิปการโอนเงินเพื่อยืนยันการชำระเงิน
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            {selectedTopUp && (
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="text-gray-500">รหัสอ้างอิง:</div>
                <div>{selectedTopUp.referenceCode}</div>
                <div className="text-gray-500">จำนวนเงิน:</div>
                <div>{formatCurrency(selectedTopUp.amount)}</div>
              </div>
            )}
            
            <div className="border-2 border-dashed rounded-md p-6 text-center">
              <input
                type="file"
                id="slip-upload"
                className="hidden"
                accept="image/*"
                onChange={handleFileChange}
              />
              {!selectedFile ? (
                <label
                  htmlFor="slip-upload"
                  className="flex flex-col items-center cursor-pointer"
                >
                  <UploadCloud className="h-10 w-10 text-gray-400 mb-2" />
                  <span className="text-sm font-medium">คลิกเพื่ออัปโหลดสลิป</span>
                  <span className="text-xs text-gray-500 mt-1">PNG, JPG หรือ JPEG (สูงสุด 5MB)</span>
                </label>
              ) : (
                <div className="space-y-2">
                  <div className="flex items-center justify-center text-sm">
                    <Check className="h-5 w-5 text-green-500 mr-1" />
                    <span>{selectedFile.name}</span>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedFile(null)}
                    >
                      เปลี่ยนไฟล์
                    </Button>
                  </div>
                </div>
              )}
            </div>
            
            {verifyTopUpMutation.isPending && (
              <div className="space-y-2">
                <Progress value={uploadProgress} />
                <p className="text-sm text-center">กำลังตรวจสอบสลิป...</p>
              </div>
            )}
            
            {verifyTopUpMutation.isError && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>เกิดข้อผิดพลาด</AlertTitle>
                <AlertDescription>
                  {verifyTopUpMutation.error?.message || "ไม่สามารถตรวจสอบสลิปได้ กรุณาลองใหม่อีกครั้ง"}
                </AlertDescription>
              </Alert>
            )}
            
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setIsVerifyDialogOpen(false)}
                disabled={verifyTopUpMutation.isPending}
              >
                ยกเลิก
              </Button>
              <Button
                onClick={handleVerify}
                disabled={!selectedFile || verifyTopUpMutation.isPending}
              >
                {verifyTopUpMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    กำลังดำเนินการ...
                  </>
                ) : (
                  "ยืนยันการชำระเงิน"
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </DashboardLayout>
  );
}