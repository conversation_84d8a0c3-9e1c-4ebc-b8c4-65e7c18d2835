import { useState, useEffect } from "react";
import { useLoc<PERSON>, Link } from "wouter";
import { DashboardLayout } from "@/components/layouts/dashboard-layout";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { useQuery, useMutation } from "@tanstack/react-query";
import { queryClient, apiRequest } from "@/lib/queryClient";
import { Loader2, RefreshC<PERSON>, Key, AlertTriangle, Save, ChevronLeft, Calendar, CircleSlash, Network, Shield, Info, Ban } from "lucide-react";

// ประเภทข้อมูลของ API Key
interface ApiKey {
  id: number;
  name: string;
  status: "active" | "inactive" | "revoked";
  apiKey?: string;
  createdAt: string;
  lastUsed: string | null;
  requestCount: number;
  description: string | null;
  expiresAt: string | null;
  ipWhitelist?: string[] | null;
  limitEnabled?: boolean;
  usageLimit?: number;
  duplicateSlipCheck?: boolean;
}

export default function ApiKeySettingsPage() {
  const [location, setLocation] = useLocation();
  const apiKeyId = location.split("/").pop();
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [ipWhitelist, setIpWhitelist] = useState<string>("");
  const [expirationDays, setExpirationDays] = useState<number | null>(null);
  const [expirationDate, setExpirationDate] = useState<string | null>(null);
  const [limitEnabled, setLimitEnabled] = useState(false);
  const [usageLimit, setUsageLimit] = useState<number>(0);
  const [duplicateSlipCheck, setDuplicateSlipCheck] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  
  const { user } = useAuth();

  // โหลดข้อมูล API Keys
  const { data: apiKeys, isLoading: isLoadingKeys } = useQuery({
    queryKey: ["/api/user/api-keys"],
    enabled: !!user,
  });

  useEffect(() => {
    if (apiKeys && Array.isArray(apiKeys) && apiKeyId) {
      const apiKey = apiKeys.find((key: ApiKey) => key.id === parseInt(apiKeyId));
      if (apiKey) {
        setName(apiKey.name || "");
        setDescription(apiKey.description || "");
        setLimitEnabled(apiKey.limitEnabled || false);
        setUsageLimit(apiKey.usageLimit || 0);
        setDuplicateSlipCheck(apiKey.duplicateSlipCheck !== false);
        
        // ตั้งค่า IP Whitelist
        if (apiKey.ipWhitelist && Array.isArray(apiKey.ipWhitelist)) {
          setIpWhitelist(apiKey.ipWhitelist.join(", "));
        }
        
        // ตั้งค่าวันหมดอายุ
        if (apiKey.expiresAt) {
          const expiryDate = new Date(apiKey.expiresAt);
          const today = new Date();
          const diffTime = expiryDate.getTime() - today.getTime();
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          
          setExpirationDate(apiKey.expiresAt);
          if (diffDays > 0) {
            setExpirationDays(diffDays);
          }
        }
      } else {
        // ไม่พบ API Key ที่ระบุ
        toast({
          title: "ไม่พบ API Key",
          description: "ไม่พบ API Key ที่ระบุในระบบ",
          variant: "destructive",
        });
        setLocation("/api-keys");
      }
    }
  }, [apiKeys, apiKeyId, setLocation]);

  // อัพเดท API Key
  const updateApiKeyMutation = useMutation({
    mutationFn: async (data: { 
      name?: string; 
      description?: string; 
      limitEnabled?: boolean; 
      usageLimit?: number;
      ipWhitelist?: string[];
      expiresAt?: string | null;
      duplicateSlipCheck?: boolean;
    }) => {
      const res = await apiRequest("PATCH", `/api/user/api-keys/${apiKeyId}`, data);
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/user/api-keys"] });
      toast({
        title: "บันทึกการตั้งค่าสำเร็จ",
        description: "ข้อมูล API Key ถูกอัพเดทเรียบร้อยแล้ว",
      });
      setIsSaving(false);
      // กลับไปยังหน้า API Keys
      setLocation("/api-keys");
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive",
      });
      setIsSaving(false);
    },
  });

  // บันทึกการตั้งค่า
  const handleSaveSettings = () => {
    if (!name) {
      toast({
        title: "กรุณากรอกชื่อ API Key",
        variant: "destructive",
      });
      return;
    }

    setIsSaving(true);
    const data: any = {
      name,
      description,
      limitEnabled,
      duplicateSlipCheck,
    };

    // เพิ่มข้อมูลการจำกัดการใช้งาน
    if (limitEnabled) {
      data.usageLimit = usageLimit;
    }
    
    // เพิ่มข้อมูล IP Whitelist
    if (ipWhitelist) {
      data.ipWhitelist = ipWhitelist.split(",").map(ip => ip.trim()).filter(ip => ip);
    } else {
      data.ipWhitelist = null;
    }
    
    // เพิ่มข้อมูลวันหมดอายุ
    if (expirationDays && expirationDays > 0) {
      const expiryDate = new Date();
      expiryDate.setDate(expiryDate.getDate() + expirationDays);
      data.expiresAt = expiryDate.toISOString();
    } else {
      data.expiresAt = null; // ยกเลิกวันหมดอายุ (ส่งเป็น null)
    }

    updateApiKeyMutation.mutate(data);
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="space-y-1">
            <h2 className="text-3xl font-bold tracking-tight">ตั้งค่า API Key</h2>
            <p className="text-indigo-300">กำหนดค่าและข้อจำกัดสำหรับ API Key</p>
          </div>
          <Link href="/api-keys">
            <Button variant="outline" className="border-indigo-700 text-indigo-200 hover:text-white hover:bg-indigo-950">
              <ChevronLeft className="mr-2 h-4 w-4" />
              กลับ
            </Button>
          </Link>
        </div>

        {isLoadingKeys ? (
          <div className="flex justify-center py-20">
            <Loader2 className="h-10 w-10 animate-spin text-indigo-400" />
          </div>
        ) : (
          <Card className="bg-gradient-to-br from-indigo-950/80 to-purple-950/90 border-indigo-800/40">
            <CardHeader>
              <CardTitle className="text-white">ตั้งค่า API Key</CardTitle>
              <CardDescription className="text-indigo-300">
                กำหนดค่าพื้นฐานและกำหนดข้อจำกัดการใช้งาน API Key
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name" className="text-indigo-200">ชื่อ API Key</Label>
                  <Input
                    id="name"
                    placeholder="ระบุชื่อ API Key"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="bg-indigo-950/50 border-indigo-700"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description" className="text-indigo-200">คำอธิบาย (ไม่บังคับ)</Label>
                  <Input
                    id="description"
                    placeholder="ระบุคำอธิบายเพิ่มเติม"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    className="bg-indigo-950/50 border-indigo-700"
                  />
                </div>

                <div className="border-t border-indigo-800/40 pt-4 mt-6">
                  <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
                    <Shield className="mr-2 h-5 w-5 text-indigo-300" />
                    การรักษาความปลอดภัย
                  </h3>
                  
                  <div className="space-y-6">
                    {/* IP Whitelist */}
                    <div className="space-y-2">
                      <Label htmlFor="ip-whitelist" className="text-indigo-200 flex items-center">
                        <Network className="mr-2 h-4 w-4 text-indigo-300" />
                        IP Whitelist
                      </Label>
                      <Textarea
                        id="ip-whitelist"
                        placeholder="ระบุ IP ที่อนุญาต เช่น ***********, ******** (เว้นว่างหากไม่จำกัด IP)"
                        value={ipWhitelist}
                        onChange={(e) => setIpWhitelist(e.target.value)}
                        className="bg-indigo-950/50 border-indigo-700 h-24"
                      />
                      <p className="text-sm text-indigo-400">
                        จำกัดให้เฉพาะ IP ที่ระบุเท่านั้นที่สามารถใช้งาน API Key นี้ได้ (คั่นด้วยเครื่องหมายจุลภาค)
                      </p>
                    </div>
                    
                    {/* Expiration Date */}
                    <div className="space-y-2">
                      <Label htmlFor="expiration" className="text-indigo-200 flex items-center">
                        <Calendar className="mr-2 h-4 w-4 text-indigo-300" />
                        วันหมดอายุ
                      </Label>
                      <div className="flex items-start space-x-4">
                        <div className="flex-1">
                          <RadioGroup 
                            defaultValue={expirationDays ? "custom" : "never"}
                            className="space-y-3"
                            onValueChange={(val) => {
                              if (val === "never") {
                                setExpirationDays(null);
                              } else if (val === "30days") {
                                setExpirationDays(30);
                              } else if (val === "90days") {
                                setExpirationDays(90);
                              } else if (val === "365days") {
                                setExpirationDays(365);
                              }
                            }}
                          >
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="never" id="never" className="border-indigo-400 text-amber-500" />
                              <Label htmlFor="never" className="text-indigo-200">ไม่มีวันหมดอายุ</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="30days" id="30days" className="border-indigo-400 text-amber-500" />
                              <Label htmlFor="30days" className="text-indigo-200">30 วัน</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="90days" id="90days" className="border-indigo-400 text-amber-500" />
                              <Label htmlFor="90days" className="text-indigo-200">90 วัน</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="365days" id="365days" className="border-indigo-400 text-amber-500" />
                              <Label htmlFor="365days" className="text-indigo-200">1 ปี</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="custom" id="custom" className="border-indigo-400 text-amber-500" />
                              <Label htmlFor="custom" className="text-indigo-200">กำหนดเอง (วัน)</Label>
                              <Input
                                type="number"
                                placeholder="จำนวนวัน"
                                value={expirationDays || ""}
                                onChange={(e) => setExpirationDays(parseInt(e.target.value) || null)}
                                className="w-20 h-8 bg-indigo-950/50 border-indigo-700"
                                min={1}
                              />
                            </div>
                          </RadioGroup>
                        </div>

                        {expirationDate && (
                          <div className="bg-indigo-900/30 rounded-md p-3 text-sm border border-indigo-800/60">
                            <div className="font-medium text-indigo-200">วันหมดอายุปัจจุบัน</div>
                            <div className="text-amber-300 mt-1">
                              {new Date(expirationDate).toLocaleDateString('th-TH', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric'
                              })}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    <Separator className="border-indigo-800/40 my-4" />
                    
                    {/* Duplicate Slip Check */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="duplicate-slip-check" className="text-indigo-200 flex items-center">
                            <Ban className="mr-2 h-4 w-4 text-indigo-300" />
                            ตรวจสอบสลิปซ้ำ
                          </Label>
                          <p className="text-sm text-indigo-400">
                            เปิดใช้งานการตรวจสอบสลิปซ้ำเพื่อป้องกันการใช้สลิปเดียวกันหลายครั้ง
                          </p>
                        </div>
                        <Switch
                          id="duplicate-slip-check"
                          checked={duplicateSlipCheck}
                          onCheckedChange={setDuplicateSlipCheck}
                          className="data-[state=checked]:bg-amber-500"
                        />
                      </div>
                    </div>

                    <Separator className="border-indigo-800/40 my-4" />
                    
                    {/* Usage Limit */}
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                        <CircleSlash className="mr-2 h-5 w-5 text-indigo-300" />
                        ข้อจำกัดการใช้งาน
                      </h3>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label htmlFor="limit-enabled" className="text-indigo-200">
                              จำกัดจำนวนการใช้งาน API Key
                            </Label>
                            <p className="text-sm text-indigo-400">
                              จำกัดจำนวนครั้งที่สามารถใช้งาน API Key นี้ได้
                            </p>
                          </div>
                          <Switch
                            id="limit-enabled"
                            checked={limitEnabled}
                            onCheckedChange={setLimitEnabled}
                            className="data-[state=checked]:bg-amber-500"
                          />
                        </div>

                        {limitEnabled && (
                          <div className="space-y-2">
                            <Label htmlFor="usage-limit" className="text-indigo-200">จำนวนการใช้งานสูงสุด</Label>
                            <div className="flex items-center space-x-2">
                              <Input
                                id="usage-limit"
                                type="number"
                                placeholder="ระบุจำนวนการใช้งานสูงสุด"
                                value={usageLimit}
                                onChange={(e) => setUsageLimit(parseInt(e.target.value) || 0)}
                                min={0}
                                className="bg-indigo-950/50 border-indigo-700"
                              />
                              <span className="text-indigo-300">ครั้ง</span>
                            </div>
                            <p className="text-sm text-amber-300 mt-1">
                              หากเกินจำนวนที่กำหนด ระบบจะส่งข้อความแจ้งเตือนและปฏิเสธการใช้งาน API
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="border-t border-indigo-800/40 pt-6 flex justify-between">
              <div className="flex items-center">
                <AlertTriangle className="h-4 w-4 mr-2 text-amber-400" />
                <span className="text-sm text-indigo-300">
                  การเปลี่ยนแปลงจะมีผลทันทีหลังจากบันทึก
                </span>
              </div>
              <Button 
                onClick={handleSaveSettings} 
                disabled={isSaving}
                className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700"
              >
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    กำลังบันทึก...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    บันทึกการตั้งค่า
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
}