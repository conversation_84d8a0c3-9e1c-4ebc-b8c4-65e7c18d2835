import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { DashboardLayout } from "@/components/layouts/dashboard-layout";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { 
  AlertTriangle, ArrowUp, Bell, 
  MessageSquare, Plus, RefreshCw, 
  Save, Settings, Shield, Upload, Wallet, Zap,
  Key, Filter, Info, Link as LinkIcon, Trash
} from "lucide-react";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Checkbox } from "@/components/ui/checkbox";
import { Switch } from "@/components/ui/switch";
import { Link } from "wouter";
import { z } from "zod";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  Dialog as DialogPrimitive,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

// Schema for webhook form
const webhookSchema = z.object({
  url: z.string().url({ message: "URL ไม่ถูกต้อง" }),
  secretKey: z.string().optional(),
  eventType: z.string(),
  isActive: z.boolean().default(true),
  apiKeyId: z.number().nullable().optional(),
  conditions: z.object({
    minAmount: z.coerce.number().min(0).optional(),
    maxAmount: z.coerce.number().min(0).optional(),
    bankCodes: z.array(z.string()).optional(),
  }).optional(),
});

type WebhookFormValues = z.infer<typeof webhookSchema>;

// Bank codes for selection
const BANK_CODES = [
  { code: "002", name: "ธนาคารกรุงเทพ" },
  { code: "004", name: "ธนาคารกสิกรไทย" },
  { code: "006", name: "ธนาคารกรุงไทย" },
  { code: "011", name: "ธนาคารทหารไทยธนชาต" },
  { code: "014", name: "ธนาคารไทยพาณิชย์" },
  { code: "025", name: "ธนาคารกรุงศรีอยุธยา" },
  { code: "069", name: "ธนาคารเกียรตินาคินภัทร" },
  { code: "073", name: "ธนาคารออมสิน" },
];

interface Webhook {
  id: number;
  url: string;
  secretKey?: string;
  eventType: string;
  isActive: boolean;
  userId: number;
  apiKeyId?: number | null;
  conditions?: {
    minAmount?: number;
    maxAmount?: number;
    bankCodes?: string[];
  };
  createdAt: string;
  updatedAt: string;
  lastTriggeredAt?: string;
}

interface ApiKey {
  id: number;
  apiKey: string;
  name: string;
  description?: string;
  status: string;
}

export default function WebhookServicePage() {
  const { toast } = useToast();
  const [webhookToDelete, setWebhookToDelete] = useState<number | null>(null);
  const [webhookToEdit, setWebhookToEdit] = useState<Webhook | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("list");
  
  // Fetch webhooks
  const { data: webhooks, isLoading: isLoadingWebhooks, refetch: refetchWebhooks } = useQuery({
    queryKey: ["/api/webhooks"],
    queryFn: async () => {
      const res = await apiRequest("GET", "/api/webhooks");
      const data = await res.json();
      return data.data || [];
    },
  });
  
  // Fetch API Keys
  const { data: apiKeys, isLoading: isLoadingApiKeys } = useQuery({
    queryKey: ["/api/user/api-keys"],
    queryFn: async () => {
      const res = await apiRequest("GET", "/api/user/api-keys");
      const data = await res.json();
      return data || [];
    },
  });
  
  // Create webhook
  const addWebhookMutation = useMutation({
    mutationFn: async (data: WebhookFormValues) => {
      const res = await apiRequest("POST", "/api/webhooks", data);
      return res.json();
    },
    onSuccess: () => {
      toast({
        title: "สร้าง Webhook สำเร็จ",
        description: "การติดตั้ง Webhook ใหม่เสร็จสมบูรณ์",
      });
      refetchWebhooks();
      setIsDialogOpen(false);
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Update webhook
  const updateWebhookMutation = useMutation({
    mutationFn: async (data: { id: number } & WebhookFormValues) => {
      const { id, ...webhook } = data;
      const res = await apiRequest("PUT", `/api/webhooks/${id}`, webhook);
      return res.json();
    },
    onSuccess: () => {
      toast({
        title: "อัพเดท Webhook สำเร็จ",
        description: "Webhook ถูกอัพเดทเรียบร้อยแล้ว",
      });
      refetchWebhooks();
      setIsDialogOpen(false);
      setWebhookToEdit(null);
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Delete webhook
  const deleteWebhookMutation = useMutation({
    mutationFn: async (id: number) => {
      const res = await apiRequest("DELETE", `/api/webhooks/${id}`);
      return res.json();
    },
    onSuccess: () => {
      toast({
        title: "ลบ Webhook สำเร็จ",
        description: "Webhook ถูกลบเรียบร้อยแล้ว",
      });
      refetchWebhooks();
      setWebhookToDelete(null);
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Test webhook
  const testWebhookMutation = useMutation({
    mutationFn: async (id: number) => {
      const res = await apiRequest("POST", `/api/webhooks/${id}/test`);
      return res.json();
    },
    onSuccess: () => {
      toast({
        title: "ทดสอบ Webhook สำเร็จ",
        description: "ส่งข้อมูลทดสอบไปยัง webhook เรียบร้อยแล้ว",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Toggle webhook active state
  const toggleWebhookActiveMutation = useMutation({
    mutationFn: async ({ id, isActive }: { id: number; isActive: boolean }) => {
      const res = await apiRequest("PATCH", `/api/webhooks/${id}/active`, {
        isActive,
      });
      return res.json();
    },
    onSuccess: () => {
      toast({
        title: "อัพเดทสถานะสำเร็จ",
        description: "สถานะ Webhook ถูกเปลี่ยนเรียบร้อยแล้ว",
      });
      refetchWebhooks();
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Format date
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return new Intl.DateTimeFormat('th-TH', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  // Get event type display
  const getEventTypeDisplay = (type: string) => {
    const types: Record<string, string> = {
      "slip_verification": "การตรวจสอบสลิป",
      "quota_low": "โควต้าเหลือน้อย",
      "credit_low": "เครดิตเหลือน้อย",
      "api_key_expire": "API key ใกล้หมดอายุ",
      "package_expire": "แพ็กเกจใกล้หมดอายุ",
      "fraud_detected": "ตรวจพบการทุจริต",
      "system_update": "อัพเดทระบบ",
    };
    return types[type] || type;
  };

  // Get event type color
  const getEventTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      "slip_verification": "bg-blue-600",
      "quota_low": "bg-amber-600",
      "credit_low": "bg-orange-600",
      "api_key_expire": "bg-purple-600",
      "package_expire": "bg-pink-600",
      "fraud_detected": "bg-red-600",
      "system_update": "bg-emerald-600",
    };
    return colors[type] || "bg-gray-600";
  };

  // Form for create/edit webhook
  const WebhookForm = ({ webhook }: { webhook?: Webhook | null }) => {
    const [showConditions, setShowConditions] = useState(!!webhook?.conditions);
    const defaultValues: Partial<WebhookFormValues> = {
      url: webhook?.url || "",
      secretKey: webhook?.secretKey || "",
      eventType: webhook?.eventType || "slip_verification",
      isActive: webhook?.isActive ?? true,
      apiKeyId: webhook?.apiKeyId ?? null,
      conditions: webhook?.conditions || {
        minAmount: undefined,
        maxAmount: undefined,
        bankCodes: [],
      },
    };

    const { control, handleSubmit, formState: { errors } } = useForm<WebhookFormValues>({
      resolver: zodResolver(webhookSchema),
      defaultValues,
    });

    const onSubmit = (data: WebhookFormValues) => {
      if (!showConditions) {
        // Remove conditions if not shown
        data.conditions = undefined;
      }
      
      if (webhook) {
        updateWebhookMutation.mutate({ id: webhook.id, ...data });
      } else {
        addWebhookMutation.mutate(data);
      }
    };

    return (
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 text-left">
        <div className="space-y-2">
          <Label htmlFor="url">URL ปลายทาง <span className="text-red-500">*</span></Label>
          <Controller
            name="url"
            control={control}
            render={({ field }) => (
              <Input
                id="url"
                placeholder="https://example.com/webhook"
                className="bg-indigo-900/30 border-indigo-700/50"
                {...field}
              />
            )}
          />
          {errors.url && (
            <p className="text-sm text-red-500">{errors.url.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="secretKey">Secret Key (ไม่บังคับ)</Label>
          <Controller
            name="secretKey"
            control={control}
            render={({ field }) => (
              <Input
                id="secretKey"
                placeholder="รหัสลับสำหรับยืนยันตัวตน"
                className="bg-indigo-900/30 border-indigo-700/50"
                {...field}
              />
            )}
          />
          <p className="text-xs text-indigo-400">
            รหัสลับนี้จะถูกส่งในหัว header 'X-Webhook-Secret' เพื่อให้คุณยืนยันว่า webhook มาจากระบบของเรา
          </p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="eventType">ประเภทเหตุการณ์ <span className="text-red-500">*</span></Label>
          <Controller
            name="eventType"
            control={control}
            render={({ field }) => (
              <select
                id="eventType"
                className="w-full rounded-md bg-indigo-900/30 border-indigo-700/50 text-white px-3 py-2"
                {...field}
              >
                <option value="slip_verification">การตรวจสอบสลิป</option>
                <option value="quota_low">โควต้าเหลือน้อย</option>
                <option value="credit_low">เครดิตเหลือน้อย</option>
                <option value="api_key_expire">API key ใกล้หมดอายุ</option>
                <option value="package_expire">แพ็กเกจใกล้หมดอายุ</option>
                <option value="fraud_detected">ตรวจพบการทุจริต</option>
                <option value="system_update">อัพเดทระบบ</option>
              </select>
            )}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="apiKeyId">ใช้กับ API Key</Label>
          <Controller
            name="apiKeyId"
            control={control}
            render={({ field }) => (
              <select
                id="apiKeyId"
                className="w-full rounded-md bg-indigo-900/30 border-indigo-700/50 text-white px-3 py-2"
                value={field.value?.toString() || ""}
                onChange={(e) => {
                  const value = e.target.value;
                  field.onChange(value === "" ? null : parseInt(value));
                }}
              >
                <option value="">ทุก API Key</option>
                {apiKeys?.map((apiKey: ApiKey) => (
                  <option key={apiKey.id} value={apiKey.id}>{apiKey.name} - {apiKey.apiKey.substring(0, 8)}...</option>
                ))}
              </select>
            )}
          />
          <p className="text-xs text-indigo-400">
            เลือก "ทุก API Key" เพื่อให้ webhook ทำงานกับทุก API Key, หรือเลือกเฉพาะ API Key ที่ต้องการ
          </p>
        </div>

        <div className="flex items-center space-x-2">
          <Controller
            name="isActive"
            control={control}
            render={({ field }) => (
              <Switch
                id="isActive"
                checked={field.value}
                onCheckedChange={field.onChange}
              />
            )}
          />
          <Label htmlFor="isActive">เปิดใช้งาน webhook</Label>
        </div>

        <div className="mt-6">
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="showConditions" 
              checked={showConditions} 
              onCheckedChange={(checked) => setShowConditions(!!checked)} 
            />
            <Label htmlFor="showConditions">ตั้งค่าเงื่อนไขเพิ่มเติม</Label>
          </div>
          
          {showConditions && (
            <div className="mt-4 p-4 rounded-md bg-indigo-900/30 border border-indigo-700/50 space-y-4">
              <h4 className="text-sm font-medium text-white flex items-center">
                <Zap className="h-4 w-4 mr-2 text-amber-400" />
                เงื่อนไขการส่ง Webhook
              </h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="minAmount">ยอดเงินขั้นต่ำ</Label>
                  <Controller
                    name="conditions.minAmount"
                    control={control}
                    render={({ field }) => (
                      <Input
                        id="minAmount"
                        type="number"
                        min="0"
                        placeholder="0"
                        className="bg-indigo-900/40 border-indigo-700/50"
                        {...field}
                      />
                    )}
                  />
                  <p className="text-xs text-indigo-400">
                    ส่ง webhook เฉพาะเมื่อยอดเงินมากกว่าหรือเท่ากับค่านี้
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxAmount">ยอดเงินสูงสุด</Label>
                  <Controller
                    name="conditions.maxAmount"
                    control={control}
                    render={({ field }) => (
                      <Input
                        id="maxAmount"
                        type="number" 
                        min="0"
                        placeholder="ไม่จำกัด"
                        className="bg-indigo-900/40 border-indigo-700/50"
                        {...field}
                      />
                    )}
                  />
                  <p className="text-xs text-indigo-400">
                    ส่ง webhook เฉพาะเมื่อยอดเงินน้อยกว่าหรือเท่ากับค่านี้
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <Label>เฉพาะธนาคาร</Label>
                <div className="grid grid-cols-2 gap-2">
                  <Controller
                    name="conditions.bankCodes"
                    control={control}
                    render={({ field }) => (
                      <>
                        {BANK_CODES.map((bank) => (
                          <div key={bank.code} className="flex items-center space-x-2">
                            <Checkbox
                              id={`bank-${bank.code}`}
                              checked={field.value?.includes(bank.code)}
                              onCheckedChange={(checked) => {
                                const currentValue = field.value || [];
                                if (checked) {
                                  field.onChange([...currentValue, bank.code]);
                                } else {
                                  field.onChange(
                                    currentValue.filter((code) => code !== bank.code)
                                  );
                                }
                              }}
                            />
                            <Label htmlFor={`bank-${bank.code}`} className="text-sm">
                              {bank.name}
                            </Label>
                          </div>
                        ))}
                      </>
                    )}
                  />
                </div>
                <p className="text-xs text-indigo-400">
                  ไม่เลือกธนาคารใดๆ = ส่ง webhook ทุกธนาคาร
                </p>
              </div>
            </div>
          )}
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              setIsDialogOpen(false);
              setWebhookToEdit(null);
            }}
          >
            ยกเลิก
          </Button>
          <Button 
            type="submit"
            className="bg-gradient-to-r from-amber-600 to-amber-700"
          >
            <Save className="h-4 w-4 mr-2" />
            {webhook ? "อัพเดท" : "บันทึก"}
          </Button>
        </div>
      </form>
    );
  };

  return (
    <DashboardLayout>
      <div className="space-y-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight mb-1">Webhook Service</h1>
          <p className="text-indigo-300">
            จัดการและติดตามการแจ้งเตือนอัตโนมัติไปยังระบบของคุณ
          </p>
        </div>

        <Separator className="bg-indigo-800/40" />

        <div className="p-6 rounded-xl relative z-0 bg-gradient-to-b from-indigo-950/70 to-purple-950/60 border border-indigo-800/40">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="bg-indigo-900/40 border border-indigo-800/40 mb-6">
              <TabsTrigger value="list" className="data-[state=active]:bg-amber-600">
                <MessageSquare className="h-4 w-4 mr-2" />
                Webhooks ของฉัน
              </TabsTrigger>
              <TabsTrigger value="info" className="data-[state=active]:bg-amber-600">
                <Info className="h-4 w-4 mr-2" />
                ข้อมูลเพิ่มเติม
              </TabsTrigger>
            </TabsList>

            <TabsContent value="list" className="mt-0">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-white flex items-center">
                  <MessageSquare className="mr-2 h-5 w-5 text-amber-400" /> 
                  Webhooks ที่ตั้งค่าไว้
                </h2>
                <Button 
                  onClick={() => {
                    setWebhookToEdit(null);
                    setIsDialogOpen(true);
                  }}
                  className="bg-gradient-to-r from-amber-600 to-amber-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  สร้าง Webhook ใหม่
                </Button>
              </div>

              {isLoadingWebhooks ? (
                <div className="py-20 text-center">
                  <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-amber-400 border-t-transparent"></div>
                  <p className="mt-2 text-indigo-300">กำลังโหลดข้อมูล...</p>
                </div>
              ) : webhooks?.length === 0 ? (
                <div className="py-20 text-center bg-indigo-900/30 rounded-lg border border-indigo-800/40">
                  <MessageSquare className="mx-auto h-12 w-12 text-indigo-500 mb-3" />
                  <h3 className="text-lg font-medium text-white mb-1">ยังไม่มี Webhook</h3>
                  <p className="text-indigo-300 max-w-md mx-auto mb-6">
                    สร้าง webhook เพื่อรับการแจ้งเตือนอัตโนมัติเมื่อมีเหตุการณ์เกิดขึ้นในระบบ
                  </p>
                  <Button 
                    onClick={() => {
                      setWebhookToEdit(null);
                      setIsDialogOpen(true);
                    }}
                    className="bg-gradient-to-r from-amber-600 to-amber-700"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    สร้าง Webhook ใหม่
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {webhooks && webhooks.map((webhook: Webhook) => (
                    <Card key={webhook.id} className="bg-indigo-900/40 border-indigo-800/40">
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <CardTitle className="text-white text-lg">{webhook.url}</CardTitle>
                            {webhook.isActive ? (
                              <Badge className="bg-emerald-600">เปิดใช้งาน</Badge>
                            ) : (
                              <Badge variant="outline" className="text-indigo-400 border-indigo-400">ไม่ได้ใช้งาน</Badge>
                            )}
                          </div>
                          <div>
                            <Switch 
                              checked={webhook.isActive} 
                              onCheckedChange={(checked) => {
                                toggleWebhookActiveMutation.mutate({ 
                                  id: webhook.id, 
                                  isActive: checked 
                                });
                              }}
                            />
                          </div>
                        </div>
                        <CardDescription className="text-indigo-300">
                          สร้างเมื่อ {formatDate(webhook.createdAt)}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="pb-2">
                        <div className="flex flex-wrap gap-2 mb-3">
                          <Badge className={`${getEventTypeColor(webhook.eventType)}`}>
                            {getEventTypeDisplay(webhook.eventType)}
                          </Badge>
                          {webhook.secretKey && (
                            <Badge variant="outline" className="border-purple-600/50 text-purple-400">
                              <Shield className="h-3 w-3 mr-1" />
                              มี Secret Key
                            </Badge>
                          )}
                          {webhook.conditions && (
                            <Badge variant="outline" className="border-blue-600/50 text-blue-400">
                              <Filter className="h-3 w-3 mr-1" />
                              มีเงื่อนไข
                            </Badge>
                          )}
                          {webhook.apiKeyId ? (
                            <Badge variant="outline" className="border-amber-600/50 text-amber-400">
                              <Key className="h-3 w-3 mr-1" />
                              {apiKeys?.find((key: ApiKey) => key.id === webhook.apiKeyId)?.name || 'API Key'}
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="border-emerald-600/50 text-emerald-400">
                              <Key className="h-3 w-3 mr-1" />
                              ทุก API Key
                            </Badge>
                          )}
                        </div>
                        
                        {webhook.conditions && (
                          <div className="mb-3 text-xs bg-indigo-900/50 p-2 rounded-md">
                            <p className="text-indigo-300 font-medium mb-1">เงื่อนไข:</p>
                            <ul className="space-y-1 pl-2 text-indigo-400">
                              {webhook.conditions.minAmount && (
                                <li>- ยอดเงินขั้นต่ำ: {webhook.conditions.minAmount.toLocaleString()} บาท</li>
                              )}
                              {webhook.conditions.maxAmount && (
                                <li>- ยอดเงินสูงสุด: {webhook.conditions.maxAmount.toLocaleString()} บาท</li>
                              )}
                              {webhook.conditions.bankCodes && webhook.conditions.bankCodes.length > 0 && (
                                <li>
                                  - เฉพาะธนาคาร: {webhook.conditions.bankCodes.map(code => 
                                    BANK_CODES.find(bank => bank.code === code)?.name || code
                                  ).join(", ")}
                                </li>
                              )}
                            </ul>
                          </div>
                        )}
                        
                        {webhook.lastTriggeredAt && (
                          <p className="text-xs text-indigo-400">
                            <Bell className="h-3 w-3 inline mr-1" />
                            ทริกเกอร์ล่าสุด: {formatDate(webhook.lastTriggeredAt)}
                          </p>
                        )}
                      </CardContent>
                      <CardFooter className="pt-2">
                        <div className="flex space-x-2 ml-auto">
                          <Button 
                            variant="outline" 
                            size="sm"
                            className="border-indigo-700/50 hover:bg-indigo-800/50"
                            onClick={() => testWebhookMutation.mutate(webhook.id)}
                          >
                            <Zap className="h-3 w-3 mr-1" />
                            ทดสอบ
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            className="border-indigo-700/50 hover:bg-indigo-800/50"
                            onClick={() => {
                              setWebhookToEdit(webhook);
                              setIsDialogOpen(true);
                            }}
                          >
                            <Settings className="h-3 w-3 mr-1" />
                            แก้ไข
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            className="border-red-900/50 hover:bg-red-900/30 text-red-400"
                            onClick={() => setWebhookToDelete(webhook.id)}
                          >
                            <Trash className="h-3 w-3 mr-1" />
                            ลบ
                          </Button>
                        </div>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="info" className="mt-0">
              <Card className="bg-indigo-900/40 border-indigo-800/40">
                <CardHeader>
                  <CardTitle className="text-white text-xl flex items-center">
                    <div className="h-5 w-5 mr-2 text-amber-400">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                        <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
                      </svg>
                    </div>
                    เกี่ยวกับ Webhook Service
                  </CardTitle>
                  <CardDescription className="text-indigo-300">
                    วิธีใช้งาน Webhook สำหรับรับการแจ้งเตือนอัตโนมัติ
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-3">
                    <h3 className="text-lg font-medium text-white flex items-center">
                      <LinkIcon className="h-5 w-5 mr-2 text-amber-400" />
                      Webhook คืออะไร?
                    </h3>
                    <p className="text-indigo-300">
                      Webhook เป็นการแจ้งเตือนอัตโนมัติที่ส่งข้อมูลไปยัง URL ที่คุณกำหนดเมื่อเกิดเหตุการณ์บางอย่างในระบบ เช่น เมื่อมีการตรวจสอบสลิป หรือเมื่อโควต้าเหลือน้อย
                    </p>
                  </div>

                  <Accordion type="single" collapsible className="w-full">
                    <AccordionItem value="format" className="border-indigo-800/40">
                      <AccordionTrigger className="text-white hover:text-amber-400">
                        <div className="flex items-center">
                          <div className="h-5 w-5 mr-2 text-amber-400">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <polyline points="16 18 22 12 16 6"></polyline>
                              <polyline points="8 6 2 12 8 18"></polyline>
                            </svg>
                          </div>
                          <span>รูปแบบข้อมูล Webhook</span>
                        </div>
                      </AccordionTrigger>
                      <AccordionContent className="text-indigo-300">
                        <p className="mb-3">
                          ข้อมูลที่ส่งไปยัง webhook จะเป็นรูปแบบ JSON ดังนี้:
                        </p>
                        <pre className="bg-indigo-900/50 p-3 rounded-md text-xs overflow-auto text-indigo-200">
{`{
  "event_type": "slip_verification",
  "timestamp": "2025-04-20T14:30:25.000Z",
  "data": {
    "id": 123,
    "transactionRef": "015109160510CTF01567",
    "amount": 5000,
    "status": "success",
    "sender": {
      "bank": {
        "id": "004",
        "name": "ธนาคารกสิกรไทย"
      },
      "account": "xxx-x-x4936-x"
    },
    "receiver": {
      "bank": {
        "id": "014",
        "name": "ธนาคารไทยพาณิชย์"
      },
      "account": "xxx-x-x4800-x"
    }
  }
}`}
                        </pre>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="security" className="border-indigo-800/40">
                      <AccordionTrigger className="text-white hover:text-amber-400">
                        <div className="flex items-center">
                          <Shield className="h-5 w-5 mr-2 text-amber-400" />
                          <span>ความปลอดภัยของ Webhook</span>
                        </div>
                      </AccordionTrigger>
                      <AccordionContent className="text-indigo-300">
                        <p className="mb-3">
                          เพื่อความปลอดภัย เราแนะนำให้ตั้งค่า Secret Key และตรวจสอบ header 'X-Webhook-Secret' เพื่อยืนยันว่า webhook มาจากระบบของเราจริง
                        </p>
                        <pre className="bg-indigo-900/50 p-3 rounded-md text-xs overflow-auto text-indigo-200">
{`// ตัวอย่างการตรวจสอบ secret key ใน PHP
$secret = $_SERVER['HTTP_X_WEBHOOK_SECRET'] ?? '';
$expected_secret = 'your_webhook_secret_here';

if ($secret !== $expected_secret) {
  http_response_code(403);
  echo json_encode(['error' => 'Invalid secret']);
  exit;
}

// ประมวลผล webhook
$payload = file_get_contents('php://input');
$data = json_decode($payload, true);
// ดำเนินการต่อ...`}
                        </pre>
                        <div className="mt-3 p-3 bg-amber-900/30 rounded-md flex items-start">
                          <AlertTriangle className="h-5 w-5 text-amber-400 mr-2 flex-shrink-0" />
                          <div>
                            <p className="text-sm text-amber-200 font-medium">คำแนะนำเพื่อความปลอดภัย:</p>
                            <p className="text-sm text-amber-300/80">
                              1. ใช้ HTTPS สำหรับ URL ปลายทาง<br />
                              2. เก็บ Secret Key ไว้เป็นความลับ<br />
                              3. ตรวจสอบ Secret Key ทุกครั้งที่ได้รับ webhook<br />
                              4. ตั้งค่า timeout ที่เหมาะสมสำหรับการตอบกลับ webhook
                            </p>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="conditions" className="border-indigo-800/40">
                      <AccordionTrigger className="text-white hover:text-amber-400">
                        <div className="flex items-center">
                          <Filter className="h-5 w-5 mr-2 text-amber-400" />
                          <span>การใช้งานเงื่อนไข</span>
                        </div>
                      </AccordionTrigger>
                      <AccordionContent className="text-indigo-300">
                        <p className="mb-3">
                          คุณสามารถตั้งค่าเงื่อนไขเพื่อกำหนดว่าเมื่อไหร่ควรส่ง webhook:
                        </p>
                        <ul className="space-y-2 ml-5 list-disc">
                          <li>
                            <strong className="text-white">ยอดเงินขั้นต่ำ:</strong> ส่ง webhook เฉพาะเมื่อยอดเงินมากกว่าหรือเท่ากับค่าที่กำหนด
                          </li>
                          <li>
                            <strong className="text-white">ยอดเงินสูงสุด:</strong> ส่ง webhook เฉพาะเมื่อยอดเงินน้อยกว่าหรือเท่ากับค่าที่กำหนด
                          </li>
                          <li>
                            <strong className="text-white">เฉพาะธนาคาร:</strong> ส่ง webhook เฉพาะเมื่อธนาคารของผู้ส่งหรือผู้รับตรงกับรายการที่กำหนด
                          </li>
                        </ul>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>

                  <div className="p-4 bg-indigo-900/50 rounded-lg border border-indigo-800/40">
                    <h3 className="text-lg font-medium text-white flex items-center mb-3">
                      <Zap className="h-5 w-5 mr-2 text-amber-400" />
                      การใช้งานที่แนะนำ
                    </h3>
                    <div className="space-y-3">
                      <div className="flex items-start">
                        <Badge className="bg-blue-600 mt-0.5 mr-2">slip_verification</Badge>
                        <p className="text-indigo-300 text-sm">
                          เชื่อมต่อกับระบบการเงินของคุณเพื่ออัพเดทสถานะการชำระเงินโดยอัตโนมัติเมื่อมีการตรวจสอบสลิป
                        </p>
                      </div>
                      <div className="flex items-start">
                        <Badge className="bg-amber-600 mt-0.5 mr-2">quota_low</Badge>
                        <p className="text-indigo-300 text-sm">
                          รับการแจ้งเตือนเมื่อโควต้าเหลือน้อยเพื่อเตรียมแผนการใช้งานหรือเพิ่มแพ็กเกจ
                        </p>
                      </div>
                      <div className="flex items-start">
                        <Badge className="bg-red-600 mt-0.5 mr-2">fraud_detected</Badge>
                        <p className="text-indigo-300 text-sm">
                          รับการแจ้งเตือนทันทีเมื่อระบบตรวจพบความผิดปกติหรือการใช้งานที่น่าสงสัย
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Dialog for create/edit webhook */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="bg-gradient-to-b from-indigo-950 to-indigo-900 border-indigo-800/40 text-white max-w-2xl">
          <DialogHeader>
            <DialogTitle className="text-xl flex items-center">
              <MessageSquare className="h-5 w-5 mr-2 text-amber-400" />
              {webhookToEdit ? "แก้ไข Webhook" : "สร้าง Webhook ใหม่"}
            </DialogTitle>
            <DialogDescription className="text-indigo-300">
              {webhookToEdit 
                ? "แก้ไขการตั้งค่า webhook ที่มีอยู่" 
                : "สร้าง webhook เพื่อรับการแจ้งเตือนอัตโนมัติจากระบบ"
              }
            </DialogDescription>
          </DialogHeader>
          
          {/* แปลง null เป็น undefined เพื่อให้ตรงกับ type */}
          <WebhookForm webhook={webhookToEdit || undefined} />
        </DialogContent>
      </Dialog>

      {/* Alert dialog for delete confirmation */}
      <AlertDialog open={!!webhookToDelete} onOpenChange={() => setWebhookToDelete(null)}>
        <AlertDialogContent className="bg-gradient-to-b from-indigo-950 to-indigo-900 border-indigo-800/40 text-white">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-xl">ยืนยันการลบ Webhook</AlertDialogTitle>
            <AlertDialogDescription className="text-indigo-300">
              คุณแน่ใจหรือไม่ที่จะลบ webhook นี้? การกระทำนี้ไม่สามารถเปลี่ยนแปลงได้
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              className="bg-transparent border-indigo-700/50 text-white hover:bg-indigo-900/50"
              onClick={() => setWebhookToDelete(null)}
            >
              ยกเลิก
            </AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-600 hover:bg-red-700 text-white"
              onClick={() => webhookToDelete && deleteWebhookMutation.mutate(webhookToDelete)}
            >
              ลบ
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </DashboardLayout>
  );
}

// ใช้ Lucide icons แทนการสร้างไอคอนเอง