-- Add location column to api_logs table 
ALTER TABLE api_logs ADD COLUMN IF NOT EXISTS location text;

-- Add error column to store error details as json
ALTER TABLE api_logs ADD COLUMN IF NOT EXISTS error jsonb;

-- Add user_id column for easier filtering
ALTER TABLE api_logs ADD COLUMN IF NOT EXISTS user_id integer REFERENCES users(id);

-- Create index on user_id for faster filtering
CREATE INDEX IF NOT EXISTS api_logs_user_id_idx ON api_logs (user_id);

-- Create index on created_at for faster time-based queries
CREATE INDEX IF NOT EXISTS api_logs_created_at_idx ON api_logs (created_at);

-- <PERSON>reate index on response_status for faster status filtering
CREATE INDEX IF NOT EXISTS api_logs_response_status_idx ON api_logs (response_status);

-- Update user_id based on api_key relationship if it's NULL
UPDATE api_logs 
SET user_id = api_keys.user_id 
FROM api_keys
WHERE api_logs.api_key_id = api_keys.id AND api_logs.user_id IS NULL;