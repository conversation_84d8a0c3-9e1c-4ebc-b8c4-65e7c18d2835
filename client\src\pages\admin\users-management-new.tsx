import { useState, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Admin } from "@/components/layouts/admin-layout";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/hooks/use-toast";
import { getQueryFn, apiRequest, queryClient } from "@/lib/queryClient";
// ใช้ฟังก์ชัน formatCurrency จาก utils แทน
import { formatDate, formatCurrency } from "@/lib/utils";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  User,
  Search,
  MoreHorizontal,
  Edit,
  Trash,
  UserCog,
  Key,
  CreditCard,
  Package,
  PlusCircle,
  Minus,
  RefreshCw,
  Settings,
  Shield,
  Check,
  RefreshCcw,
  Building,
  Mail,
  Phone,
  AlarmClock,
  BarChart,
  Wallet,
  AlertCircle,
  ListFilter,
} from "lucide-react";

// ประเภทข้อมูลสำหรับผู้ใช้งาน
interface UserType {
  id: number;
  username: string;
  email: string;
  role: string;
  status: string;
  firstName?: string;
  lastName?: string;
  companyName?: string;
  phoneNumber?: string;
  email_verified?: boolean;
  phone_verified?: boolean;
  credit: number;
  createdAt: string;
}

// ประเภทข้อมูลแพ็คเกจ
interface PackageType {
  id: number;
  name: string;
  description: string;
  price: number;
  requestsLimit: number;
  features: string[];
  durationDays: number;
  tag?: string;
  discount3Months?: number;
  discount6Months?: number;
  discount12Months?: number;
  creditPerVerification?: number;
}

// ประเภทข้อมูลแพ็คเกจของผู้ใช้
interface UserPackageType {
  id: number;
  userId: number;
  packageId: number;
  startDate: string;
  endDate: string;
  isActive: boolean;
  requestsUsed: number;
  durationMonths: number;
  createdAt: string;
  updatedAt: string;
  package: PackageType;
}

// ประเภทข้อมูล API Key
interface ApiKeyType {
  id: number;
  userId: number;
  apiKey: string;
  name: string;
  description?: string;
  usageLimit?: number;
  usageCount: number;
  ipWhitelist?: string[];
  isActive: boolean;
  expiryDate?: string;
  createdAt: string;
  updatedAt: string;
}

// ประเภทข้อมูลธุรกรรม
interface TransactionType {
  id: number;
  userId: number;
  amount: number;
  status: string;
  referenceCode?: string;
  description?: string;
  verificationId?: number;
  createdAt: string;
}

// สคีมาสำหรับแก้ไขผู้ใช้งาน
const editUserSchema = z.object({
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  email: z.string().email("กรุณากรอกอีเมลที่ถูกต้อง").optional(),
  phoneNumber: z.string().optional(),
  companyName: z.string().optional(),
  status: z.string(),
  role: z.string(),
});

// สคีมาสำหรับเปลี่ยนรหัสผ่าน
const changePasswordSchema = z.object({
  newPassword: z.string().min(8, "รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร"),
  confirmPassword: z.string().min(8, "รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร"),
}).refine(data => data.newPassword === data.confirmPassword, {
  message: "รหัสผ่านไม่ตรงกัน",
  path: ["confirmPassword"]
});

// สคีมาสำหรับเพิ่ม/หักเครดิต
const creditActionSchema = z.object({
  amount: z.preprocess(
    (val) => {
      // แปลงค่าเป็น number และรองรับทศนิยม 2 ตำแหน่ง
      const num = parseFloat(val as string);
      // คืนค่าที่ปัดเศษเป็นทศนิยม 2 ตำแหน่ง
      return Math.round(num * 100) / 100;
    },
    z.number().positive("จำนวนเครดิตต้องมากกว่า 0")
  ),
  description: z.string().optional(),
});

// สคีมาสำหรับเพิ่ม API key
const addApiKeySchema = z.object({
  name: z.string().min(3, "ชื่อต้องมีอย่างน้อย 3 ตัวอักษร"),
  description: z.string().optional(),
  usageLimit: z.preprocess((val) => (val === "" ? undefined : Number(val)), z.number().optional()),
  ipWhitelist: z.string().optional(),
  expiryDate: z.preprocess((val) => (val === "" ? undefined : val), z.string().optional()),
});

// สคีมาสำหรับเพิ่มแพ็คเกจให้ผู้ใช้
const addPackageSchema = z.object({
  packageId: z.preprocess(
    (val) => Number(val),
    z.number().positive("กรุณาเลือกแพ็คเกจ")
  ),
  durationMonths: z.preprocess(
    (val) => Number(val),
    z.number().int().positive("ระยะเวลาต้องเป็นจำนวนเต็มบวก")
  ),
  isActive: z.boolean().default(true),
});

// กำหนดประเภทสำหรับฟอร์ม
type EditUserFormValues = z.infer<typeof editUserSchema>;
type ChangePasswordFormValues = z.infer<typeof changePasswordSchema>;
type CreditActionFormValues = z.infer<typeof creditActionSchema>;
type AddApiKeyFormValues = z.infer<typeof addApiKeySchema>;
type AddPackageFormValues = z.infer<typeof addPackageSchema>;

// แปลงสถานะเป็นภาษาไทย
const translateUserStatus = (status: string): string => {
  switch (status) {
    case "active": return "ใช้งาน";
    case "inactive": return "ไม่ใช้งาน";
    case "suspended": return "ระงับใช้งาน";
    default: return status;
  }
};

// แปลงบทบาทเป็นภาษาไทย
const translateUserRole = (role: string): string => {
  switch (role) {
    case "admin": return "ผู้ดูแลระบบ";
    case "user": return "ผู้ใช้งาน";
    default: return role;
  }
};

export default function UsersManagementNew() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [search, setSearch] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [roleFilter, setRoleFilter] = useState<string>("all");
  const [selectedUser, setSelectedUser] = useState<UserType | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isPasswordDialogOpen, setIsPasswordDialogOpen] = useState(false);
  const [isCreditDialogOpen, setIsCreditDialogOpen] = useState(false);
  const [isPackagesDialogOpen, setIsPackagesDialogOpen] = useState(false);
  const [isApiKeysDialogOpen, setIsApiKeysDialogOpen] = useState(false);
  const [creditAction, setCreditAction] = useState<'add' | 'deduct'>('add');

  // ข้อมูลแพ็คเกจและ API keys
  const [userPackages, setUserPackages] = useState<(UserPackageType & { package: PackageType })[]>([]);
  const [userApiKeys, setUserApiKeys] = useState<ApiKeyType[]>([]);
  const [userTransactions, setUserTransactions] = useState<TransactionType[]>([]);

  // ดึงข้อมูลผู้ใช้ทั้งหมด
  const {
    data: users,
    isLoading: isLoadingUsers,
    error: usersError,
    refetch: refetchUsers
  } = useQuery<UserType[]>({
    queryKey: ['/api/admin/users'],
    queryFn: getQueryFn({ on401: "returnNull" }),
  });

  // ดึงข้อมูลแพ็คเกจทั้งหมด
  const { data: packagesList } = useQuery<PackageType[]>({
    queryKey: ['/api/packages'],
    staleTime: 1000 * 60 * 5, // 5 นาที
  });

  // ฟอร์มสำหรับแก้ไขข้อมูลผู้ใช้
  const editUserForm = useForm<EditUserFormValues>({
    resolver: zodResolver(editUserSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      phoneNumber: "",
      companyName: "",
      status: "active",
      role: "user",
    }
  });

  // ฟอร์มสำหรับเปลี่ยนรหัสผ่าน
  const changePasswordForm = useForm<ChangePasswordFormValues>({
    resolver: zodResolver(changePasswordSchema),
    defaultValues: {
      newPassword: "",
      confirmPassword: "",
    }
  });

  // ฟอร์มสำหรับเพิ่ม/หักเครดิต
  const creditForm = useForm<CreditActionFormValues>({
    resolver: zodResolver(creditActionSchema),
    defaultValues: {
      amount: 0,
      description: ""
    }
  });

  // ฟอร์มสำหรับเพิ่ม API key
  const addApiKeyForm = useForm<AddApiKeyFormValues>({
    resolver: zodResolver(addApiKeySchema),
    defaultValues: {
      name: "",
      description: "",
      usageLimit: undefined,
      ipWhitelist: "",
      expiryDate: undefined
    }
  });

  // ฟอร์มสำหรับเพิ่มแพ็คเกจ
  const addPackageForm = useForm<AddPackageFormValues>({
    resolver: zodResolver(addPackageSchema),
    defaultValues: {
      packageId: 0,
      durationMonths: 1,
      isActive: true
    }
  });

  // กำหนดค่าเริ่มต้นของฟอร์มเมื่อเลือกผู้ใช้
  useEffect(() => {
    if (selectedUser) {
      editUserForm.reset({
        firstName: selectedUser.firstName || "",
        lastName: selectedUser.lastName || "",
        email: selectedUser.email || "",
        phoneNumber: selectedUser.phoneNumber || "",
        companyName: selectedUser.companyName || "",
        status: selectedUser.status || "active",
        role: selectedUser.role || "user",
      });
    }
  }, [selectedUser, editUserForm]);

  // Mutation สำหรับการอัพเดทข้อมูลผู้ใช้
  const updateUserMutation = useMutation({
    mutationFn: async (data: EditUserFormValues) => {
      const res = await apiRequest("PATCH", `/api/admin/users/${selectedUser?.id}`, data);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "อัพเดทข้อมูลสำเร็จ",
        description: "ข้อมูลผู้ใช้ถูกอัพเดทเรียบร้อยแล้ว",
      });
      setIsEditDialogOpen(false);
      refetchUsers();
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับการลบผู้ใช้
  const deleteUserMutation = useMutation({
    mutationFn: async () => {
      const res = await apiRequest("DELETE", `/api/admin/users/${selectedUser?.id}`);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "ลบผู้ใช้สำเร็จ",
        description: "ผู้ใช้ถูกลบออกจากระบบเรียบร้อยแล้ว",
      });
      setIsDeleteDialogOpen(false);
      refetchUsers();
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับเปลี่ยนรหัสผ่าน
  const changePasswordMutation = useMutation({
    mutationFn: async (data: { newPassword: string }) => {
      const res = await apiRequest("PATCH", `/api/admin/users/${selectedUser?.id}/password`, data);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "เปลี่ยนรหัสผ่านสำเร็จ",
        description: "รหัสผ่านถูกเปลี่ยนเรียบร้อยแล้ว",
      });
      setIsPasswordDialogOpen(false);
      changePasswordForm.reset();
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับเพิ่มเครดิต
  const addCreditMutation = useMutation({
    mutationFn: async (data: CreditActionFormValues) => {
      const res = await apiRequest("POST", `/api/admin/users/${selectedUser?.id}/credit`, data);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "เพิ่มเครดิตสำเร็จ",
        description: `เพิ่มเครดิต ${formatCurrency(creditForm.getValues().amount)} บาท ให้กับ ${selectedUser?.username} เรียบร้อยแล้ว`,
      });
      setIsCreditDialogOpen(false);
      creditForm.reset();
      refetchUsers();
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับหักเครดิต
  const deductCreditMutation = useMutation({
    mutationFn: async (data: CreditActionFormValues) => {
      const res = await apiRequest("POST", `/api/admin/users/${selectedUser?.id}/deduct-credit`, data);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "หักเครดิตสำเร็จ",
        description: `หักเครดิต ${formatCurrency(creditForm.getValues().amount)} บาท จาก ${selectedUser?.username} เรียบร้อยแล้ว`,
      });
      setIsCreditDialogOpen(false);
      creditForm.reset();
      refetchUsers();
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับเพิ่มแพ็คเกจให้ผู้ใช้
  const addPackageMutation = useMutation({
    mutationFn: async (data: AddPackageFormValues) => {
      const res = await apiRequest("POST", `/api/admin/users/${selectedUser?.id}/packages`, data);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "เพิ่มแพ็คเกจสำเร็จ",
        description: "เพิ่มแพ็คเกจให้ผู้ใช้งานเรียบร้อยแล้ว",
      });
      // รีเฟรชข้อมูลแพ็คเกจของผู้ใช้
      handleOpenPackagesDialog(selectedUser!);
      addPackageForm.reset();
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับเปิด/ปิดการใช้งานแพ็คเกจ
  const togglePackageStatusMutation = useMutation({
    mutationFn: async ({ packageId, isActive }: { packageId: number, isActive: boolean }) => {
      const res = await apiRequest("PATCH", `/api/admin/users/${selectedUser?.id}/packages/${packageId}`, {
        isActive
      });
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "อัพเดทแพ็คเกจสำเร็จ",
        description: "สถานะแพ็คเกจถูกอัพเดทเรียบร้อยแล้ว",
      });
      handleOpenPackagesDialog(selectedUser!);
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับรีเซ็ตการใช้งานแพ็คเกจ
  const resetPackageUsageMutation = useMutation({
    mutationFn: async (packageId: number) => {
      const res = await apiRequest("PATCH", `/api/admin/users/${selectedUser?.id}/packages/${packageId}`, {
        resetUsage: true
      });
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "รีเซ็ตการใช้งานสำเร็จ",
        description: "การใช้งานแพ็คเกจถูกรีเซ็ตเรียบร้อยแล้ว",
      });
      handleOpenPackagesDialog(selectedUser!);
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับขยายวันหมดอายุแพ็คเกจ
  const extendPackageExpiryMutation = useMutation({
    mutationFn: async ({ packageId, newEndDate }: { packageId: number, newEndDate: string }) => {
      const res = await apiRequest("PATCH", `/api/admin/users/${selectedUser?.id}/packages/${packageId}`, {
        endDate: newEndDate
      });
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "ขยายวันหมดอายุสำเร็จ",
        description: "วันหมดอายุแพ็คเกจถูกขยายเรียบร้อยแล้ว",
      });
      handleOpenPackagesDialog(selectedUser!);
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับการสร้าง API key ใหม่
  const addApiKeyMutation = useMutation({
    mutationFn: async (data: AddApiKeyFormValues) => {
      // แปลง ipWhitelist จาก string เป็น array ถ้ามีข้อมูล
      const ipWhitelistArray = data.ipWhitelist ? data.ipWhitelist.split(',').map(ip => ip.trim()) : undefined;

      const payload = {
        ...data,
        ipWhitelist: ipWhitelistArray
      };

      const res = await apiRequest("POST", `/api/admin/users/${selectedUser?.id}/api-keys`, payload);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "สร้าง API Key สำเร็จ",
        description: "API Key ใหม่ถูกสร้างขึ้นเรียบร้อยแล้ว",
      });
      handleOpenApiKeysDialog(selectedUser!);
      addApiKeyForm.reset();
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับการเปิด/ปิดการใช้งาน API key
  const toggleApiKeyStatusMutation = useMutation({
    mutationFn: async ({ apiKeyId, isActive }: { apiKeyId: number, isActive: boolean }) => {
      const res = await apiRequest("PATCH", `/api/admin/users/${selectedUser?.id}/api-keys/${apiKeyId}`, {
        isActive
      });
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "อัพเดทสถานะ API Key สำเร็จ",
        description: "สถานะ API Key ถูกอัพเดทเรียบร้อยแล้ว",
      });
      handleOpenApiKeysDialog(selectedUser!);
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับการลบ API key
  const deleteApiKeyMutation = useMutation({
    mutationFn: async (apiKeyId: number) => {
      const res = await apiRequest("DELETE", `/api/admin/users/${selectedUser?.id}/api-keys/${apiKeyId}`);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "ลบ API Key สำเร็จ",
        description: "API Key ถูกลบเรียบร้อยแล้ว",
      });
      handleOpenApiKeysDialog(selectedUser!);
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // เปิดไดอะล็อกแก้ไขผู้ใช้
  const handleEditUser = (user: UserType) => {
    setSelectedUser(user);
    setIsEditDialogOpen(true);
  };

  // เปิดไดอะล็อกลบผู้ใช้
  const handleDeleteUser = (user: UserType) => {
    setSelectedUser(user);
    setIsDeleteDialogOpen(true);
  };

  // เปิดไดอะล็อกเปลี่ยนรหัสผ่าน
  const handleChangePassword = (user: UserType) => {
    setSelectedUser(user);
    setIsPasswordDialogOpen(true);
  };

  // เปิดไดอะล็อกจัดการเครดิต
  const handleCreditDialog = (user: UserType, action: 'add' | 'deduct') => {
    setSelectedUser(user);
    setCreditAction(action);

    // กำหนดค่าเริ่มต้นของฟอร์ม
    const defaultDescription = action === 'add' ? 'เติมเครดิต' : 'หักเครดิต';
    creditForm.reset({
      amount: 0,
      description: defaultDescription
    });

    setIsCreditDialogOpen(true);
  };

  // เปิดไดอะล็อกจัดการแพ็คเกจ
  const handleOpenPackagesDialog = async (user: UserType) => {
    setSelectedUser(user);
    try {
      // โหลดข้อมูลแพ็คเกจของผู้ใช้
      const res = await apiRequest("GET", `/api/admin/users/${user.id}/packages`, null);
      const data = await res.json();
      setUserPackages(data);

      // โหลดข้อมูลธุรกรรมของผู้ใช้
      const transRes = await apiRequest("GET", `/api/admin/users/${user.id}/transactions`, null);
      const transData = await transRes.json();
      setUserTransactions(transData);

      // อัพเดทฟอร์มเพิ่มแพ็คเกจ
      if (packagesList && packagesList.length > 0) {
        addPackageForm.reset({
          packageId: packagesList[0].id,
          durationMonths: 1,
          isActive: true
        });
      }

      setIsPackagesDialogOpen(true);
    } catch (error) {
      console.error('Error loading user packages:', error);
      toast({
        title: "เกิดข้อผิดพลาด",
        description: "ไม่สามารถโหลดข้อมูลแพ็คเกจของผู้ใช้ได้",
        variant: "destructive"
      });
    }
  };

  // เปิดไดอะล็อกจัดการ API keys
  const handleOpenApiKeysDialog = async (user: UserType) => {
    setSelectedUser(user);
    try {
      // โหลดข้อมูล API keys ของผู้ใช้
      const res = await apiRequest("GET", `/api/admin/users/${user.id}/api-keys`, null);
      const data = await res.json();
      setUserApiKeys(data);

      // รีเซ็ตฟอร์มเพิ่ม API key
      addApiKeyForm.reset({
        name: "",
        description: "",
        usageLimit: undefined,
        ipWhitelist: "",
        expiryDate: undefined
      });

      setIsApiKeysDialogOpen(true);
    } catch (error) {
      console.error('Error loading user API keys:', error);
      toast({
        title: "เกิดข้อผิดพลาด",
        description: "ไม่สามารถโหลดข้อมูล API keys ของผู้ใช้ได้",
        variant: "destructive"
      });
    }
  };

  // บันทึกข้อมูลผู้ใช้ที่แก้ไข
  const onSubmitEditForm = (values: EditUserFormValues) => {
    updateUserMutation.mutate(values);
  };

  // บันทึกรหัสผ่านใหม่
  const onSubmitPasswordForm = (values: ChangePasswordFormValues) => {
    changePasswordMutation.mutate({ newPassword: values.newPassword });
  };

  // กรองข้อมูลผู้ใช้ตามการค้นหาและตัวกรอง
  const filteredUsers = users?.filter(user => {
    const matchesSearch = search === "" ||
      user.username.toLowerCase().includes(search.toLowerCase()) ||
      user.email.toLowerCase().includes(search.toLowerCase()) ||
      (user.firstName && user.firstName.toLowerCase().includes(search.toLowerCase())) ||
      (user.lastName && user.lastName.toLowerCase().includes(search.toLowerCase()));

    const matchesStatus = statusFilter === "all" || user.status === statusFilter;
    const matchesRole = roleFilter === "all" || user.role === roleFilter;

    return matchesSearch && matchesStatus && matchesRole;
  });

  // จัดการแสดงสถานะผู้ใช้งานด้วยสีและสัญลักษณ์
  const renderUserStatus = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-50 text-green-700 border-green-200">{translateUserStatus(status)}</Badge>;
      case "inactive":
        return <Badge className="bg-yellow-50 text-yellow-700 border-yellow-200">{translateUserStatus(status)}</Badge>;
      case "suspended":
        return <Badge className="bg-red-50 text-red-700 border-red-200">{translateUserStatus(status)}</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // จัดการแสดงบทบาทผู้ใช้งาน
  const renderUserRole = (role: string) => {
    switch (role) {
      case "admin":
        return <Badge className="bg-purple-50 text-purple-700 border-purple-200">{translateUserRole(role)}</Badge>;
      case "user":
        return <Badge className="bg-blue-50 text-blue-700 border-blue-200">{translateUserRole(role)}</Badge>;
      default:
        return <Badge variant="outline">{role}</Badge>;
    }
  };

  return (
    <Admin>
      <div className="space-y-8">
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 rounded-lg blur-xl -z-10 opacity-70"></div>
          <div className="relative z-10 p-4 rounded-lg border border-indigo-200/30">
            <h1 className="text-2xl font-bold tracking-tight divine-text">จัดการผู้ใช้งานระบบ</h1>
            <div className="lightning-bar w-32 my-2"></div>
            <p className="text-muted-foreground">
              ดูและจัดการข้อมูลผู้ใช้งานทั้งหมดในระบบ SLIPKUY
            </p>
          </div>
        </div>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle>ตารางผู้ใช้งานทั้งหมด</CardTitle>
            <CardDescription>
              จำนวนผู้ใช้งานทั้งหมด: {users?.length || 0} คน
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="ค้นหาผู้ใช้งาน..."
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <div className="w-40">
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger>
                      <div className="flex items-center">
                        <ListFilter className="h-4 w-4 mr-2" />
                        <span>สถานะ: {statusFilter === 'all' ? 'ทั้งหมด' : translateUserStatus(statusFilter)}</span>
                      </div>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">ทั้งหมด</SelectItem>
                      <SelectItem value="active">ใช้งาน</SelectItem>
                      <SelectItem value="inactive">ไม่ใช้งาน</SelectItem>
                      <SelectItem value="suspended">ระงับใช้งาน</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="w-40">
                  <Select value={roleFilter} onValueChange={setRoleFilter}>
                    <SelectTrigger>
                      <div className="flex items-center">
                        <ListFilter className="h-4 w-4 mr-2" />
                        <span>บทบาท: {roleFilter === 'all' ? 'ทั้งหมด' : translateUserRole(roleFilter)}</span>
                      </div>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">ทั้งหมด</SelectItem>
                      <SelectItem value="admin">ผู้ดูแลระบบ</SelectItem>
                      <SelectItem value="user">ผู้ใช้งาน</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {isLoadingUsers ? (
              <div className="py-10 flex justify-center">
                <div className="flex flex-col items-center">
                  <RefreshCw className="h-8 w-8 animate-spin text-primary" />
                  <p className="mt-2 text-muted-foreground">กำลังโหลดข้อมูล...</p>
                </div>
              </div>
            ) : usersError ? (
              <div className="py-10 flex justify-center">
                <div className="flex flex-col items-center">
                  <AlertCircle className="h-8 w-8 text-destructive" />
                  <p className="mt-2 text-muted-foreground">เกิดข้อผิดพลาดในการโหลดข้อมูล</p>
                  <Button
                    variant="outline"
                    className="mt-4"
                    onClick={() => refetchUsers()}
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    โหลดข้อมูลใหม่
                  </Button>
                </div>
              </div>
            ) : (
              <div className="rounded-md border overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-muted/50">
                      <TableHead>ชื่อผู้ใช้</TableHead>
                      <TableHead>อีเมล</TableHead>
                      <TableHead>ชื่อ-นามสกุล</TableHead>
                      <TableHead>บทบาท</TableHead>
                      <TableHead>สถานะ</TableHead>
                      <TableHead>เครดิต</TableHead>
                      <TableHead>วันที่สร้าง</TableHead>
                      <TableHead className="text-right">จัดการ</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredUsers && filteredUsers.length > 0 ? (
                      filteredUsers.map((user) => (
                        <TableRow key={user.id}>
                          <TableCell className="font-medium">{user.username}</TableCell>
                          <TableCell>{user.email}</TableCell>
                          <TableCell>
                            {user.firstName && user.lastName
                              ? `${user.firstName} ${user.lastName}`
                              : user.firstName || user.lastName || "-"}
                          </TableCell>
                          <TableCell>{renderUserRole(user.role)}</TableCell>
                          <TableCell>{renderUserStatus(user.status)}</TableCell>
                          <TableCell className="font-medium text-right">
                            <span className={`${user.credit > 0 ? 'text-green-500' : 'text-red-500'}`}>
                              {formatCurrency(user.credit ?? 0)}
                            </span>
                          </TableCell>
                          <TableCell>{formatDate(new Date(user.createdAt))}</TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end" className="w-56">
                                <DropdownMenuLabel>จัดการผู้ใช้</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={() => handleEditUser(user)}>
                                  <Edit className="h-4 w-4 mr-2" />
                                  <span>แก้ไขข้อมูล</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleChangePassword(user)}>
                                  <Key className="h-4 w-4 mr-2" />
                                  <span>เปลี่ยนรหัสผ่าน</span>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={() => handleCreditDialog(user, 'add')}>
                                  <PlusCircle className="h-4 w-4 mr-2 text-green-600" />
                                  <span>เพิ่มเครดิต</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleCreditDialog(user, 'deduct')}>
                                  <Minus className="h-4 w-4 mr-2 text-amber-600" />
                                  <span>หักเครดิต</span>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={() => handleOpenPackagesDialog(user)}>
                                  <Package className="h-4 w-4 mr-2 text-blue-600" />
                                  <span>จัดการแพ็คเกจ</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleOpenApiKeysDialog(user)}>
                                  <Key className="h-4 w-4 mr-2 text-purple-600" />
                                  <span>จัดการ API Keys</span>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={() => handleDeleteUser(user)}
                                  className="text-red-600 focus:text-red-700 focus:bg-red-50"
                                >
                                  <Trash className="h-4 w-4 mr-2" />
                                  <span>ลบผู้ใช้</span>
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={8} className="h-24 text-center">
                          ไม่พบข้อมูลผู้ใช้งาน
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* ไดอะล็อกแก้ไขข้อมูลผู้ใช้ */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center text-indigo-700">
              <UserCog className="h-5 w-5 mr-2" />
              แก้ไขข้อมูลผู้ใช้
            </DialogTitle>
            <DialogDescription>
              แก้ไขข้อมูลของผู้ใช้งาน {selectedUser?.username}
            </DialogDescription>
          </DialogHeader>
          <Form {...editUserForm}>
            <form onSubmit={editUserForm.handleSubmit(onSubmitEditForm)} className="space-y-4">
              <FormField
                control={editUserForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>อีเมล</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          type="email"
                          placeholder="อีเมล"
                          className="pl-10"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={editUserForm.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>ชื่อ</FormLabel>
                      <FormControl>
                        <Input placeholder="ชื่อ" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={editUserForm.control}
                  name="lastName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>นามสกุล</FormLabel>
                      <FormControl>
                        <Input placeholder="นามสกุล" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={editUserForm.control}
                name="phoneNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>เบอร์โทรศัพท์</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          placeholder="เบอร์โทรศัพท์"
                          className="pl-10"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editUserForm.control}
                name="companyName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>ชื่อบริษัท (ไม่บังคับ)</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Building className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          placeholder="ชื่อบริษัท"
                          className="pl-10"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={editUserForm.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>สถานะ</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="เลือกสถานะ" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="active">ใช้งาน</SelectItem>
                          <SelectItem value="inactive">ไม่ใช้งาน</SelectItem>
                          <SelectItem value="suspended">ระงับใช้งาน</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={editUserForm.control}
                  name="role"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>บทบาท</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="เลือกบทบาท" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="user">ผู้ใช้งาน</SelectItem>
                          <SelectItem value="admin">ผู้ดูแลระบบ</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsEditDialogOpen(false)}
                >
                  ยกเลิก
                </Button>
                <Button
                  type="submit"
                  disabled={updateUserMutation.isPending}
                  className="bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white"
                >
                  {updateUserMutation.isPending && (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  )}
                  บันทึกข้อมูล
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* ไดอะล็อกเปลี่ยนรหัสผ่าน */}
      <Dialog open={isPasswordDialogOpen} onOpenChange={setIsPasswordDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center text-indigo-700">
              <Key className="h-5 w-5 mr-2" />
              เปลี่ยนรหัสผ่าน
            </DialogTitle>
            <DialogDescription>
              เปลี่ยนรหัสผ่านให้กับผู้ใช้งาน {selectedUser?.username}
            </DialogDescription>
          </DialogHeader>
          <Form {...changePasswordForm}>
            <form onSubmit={changePasswordForm.handleSubmit(onSubmitPasswordForm)} className="space-y-4">
              <FormField
                control={changePasswordForm.control}
                name="newPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>รหัสผ่านใหม่</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="รหัสผ่านใหม่" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={changePasswordForm.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>ยืนยันรหัสผ่านใหม่</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="ยืนยันรหัสผ่านใหม่" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsPasswordDialogOpen(false)}
                >
                  ยกเลิก
                </Button>
                <Button
                  type="submit"
                  disabled={changePasswordMutation.isPending}
                  className="bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white"
                >
                  {changePasswordMutation.isPending && (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  )}
                  บันทึกรหัสผ่าน
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* ไดอะล็อกลบผู้ใช้ */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center text-red-700">
              <AlertCircle className="h-5 w-5 mr-2" />
              ยืนยันการลบผู้ใช้
            </DialogTitle>
            <DialogDescription>
              ต้องการลบผู้ใช้งาน {selectedUser?.username} หรือไม่? การกระทำนี้ไม่สามารถย้อนกลับได้
            </DialogDescription>
          </DialogHeader>
          <div className="bg-red-50 p-4 rounded-md border border-red-200 mb-4">
            <div className="flex items-start">
              <AlertCircle className="h-5 w-5 text-red-600 mt-0.5 mr-2" />
              <div>
                <h4 className="text-sm font-medium text-red-800">คำเตือน</h4>
                <ul className="mt-1 text-sm text-red-700 list-disc list-inside">
                  <li>ข้อมูลผู้ใช้งานทั้งหมดจะถูกลบออกจากระบบ</li>
                  <li>ข้อมูลประวัติการตรวจสอบสลิปจะถูกลบออกจากระบบ</li>
                  <li>แพ็คเกจทั้งหมดของผู้ใช้งานจะถูกลบออกจากระบบ</li>
                </ul>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              ยกเลิก
            </Button>
            <Button
              type="button"
              variant="destructive"
              disabled={deleteUserMutation.isPending}
              onClick={() => deleteUserMutation.mutate()}
            >
              {deleteUserMutation.isPending && (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              )}
              ลบผู้ใช้
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* ไดอะล็อกจัดการเครดิต */}
      <Dialog open={isCreditDialogOpen} onOpenChange={setIsCreditDialogOpen}>
        <DialogContent className={`sm:max-w-[425px] border ${creditAction === 'add' ? 'border-green-200 bg-gradient-to-b from-white to-green-50/20' : 'border-amber-200 bg-gradient-to-b from-white to-amber-50/20'}`}>
          <div className={`absolute top-0 left-0 right-0 h-1 ${creditAction === 'add' ? 'bg-gradient-to-r from-green-500/50 via-green-400/50 to-green-500/50' : 'bg-gradient-to-r from-amber-500/50 via-amber-400/50 to-amber-500/50'}`}></div>
          <DialogHeader>
            <DialogTitle className={`flex items-center ${creditAction === 'add' ? 'text-green-700' : 'text-amber-700'}`}>
              <div className={`p-1.5 rounded-full ${creditAction === 'add' ? 'bg-gradient-to-r from-green-100 to-green-200' : 'bg-gradient-to-r from-amber-100 to-amber-200'} mr-2 flex items-center justify-center`}>
                {creditAction === 'add' ? (
                  <PlusCircle className="h-5 w-5 text-green-600" />
                ) : (
                  <Minus className="h-5 w-5 text-amber-600" />
                )}
              </div>
              <span>{creditAction === 'add' ? 'เพิ่มเครดิต' : 'หักเครดิต'}</span>
            </DialogTitle>
            <DialogDescription className={`${creditAction === 'add' ? 'text-green-600/70' : 'text-amber-600/70'}`}>
              {creditAction === 'add' ? 'เพิ่มเครดิตให้ผู้ใช้งาน' : 'หักเครดิตจากผู้ใช้งาน'} <span className="font-semibold">{selectedUser?.username}</span>
            </DialogDescription>
          </DialogHeader>
          <Form {...creditForm}>
            <form onSubmit={creditForm.handleSubmit((data) => (creditAction === 'add' ? addCreditMutation.mutate(data) : deductCreditMutation.mutate(data)))} className="space-y-4">
              <FormField
                control={creditForm.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>จำนวนเครดิต</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Wallet className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          type="number"
                          placeholder="จำนวนเครดิต"
                          className="pl-10"
                          min="0.01"
                          step="0.01"
                          {...field}
                          onChange={(e) => {
                            // แปลงค่าเป็น number และปัดเศษเป็นทศนิยม 2 ตำแหน่ง
                            const value = e.target.value === "" ? 0 : parseFloat(e.target.value);
                            field.onChange(value);
                          }}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={creditForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>คำอธิบาย (ไม่บังคับ)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder={`คำอธิบายการ${creditAction === 'add' ? 'เพิ่ม' : 'หัก'}เครดิต`}
                        {...field}
                        className="resize-none"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsCreditDialogOpen(false)}
                  className={`border-${creditAction === 'add' ? 'green' : 'orange'}-200 hover:bg-${creditAction === 'add' ? 'green' : 'orange'}-50 hover:text-${creditAction === 'add' ? 'green' : 'orange'}-700`}
                >
                  ยกเลิก
                </Button>
                <Button
                  type="submit"
                  disabled={creditAction === 'add' ? addCreditMutation.isPending : deductCreditMutation.isPending}
                  className={`text-white ${
                    creditAction === 'add'
                      ? 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700'
                      : 'bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700'
                  }`}
                >
                  {(creditAction === 'add' ? addCreditMutation.isPending : deductCreditMutation.isPending) && (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  )}
                  {creditAction === 'add' ? 'เพิ่มเครดิต' : 'หักเครดิต'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* ไดอะล็อกจัดการแพ็คเกจ */}
      <Dialog open={isPackagesDialogOpen} onOpenChange={setIsPackagesDialogOpen} modal={false}>
        <DialogContent className="sm:max-w-[700px] border border-blue-200 bg-gradient-to-b from-white to-blue-50/20">
          <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500/50 via-indigo-500/50 to-blue-500/50"></div>
          <DialogHeader>
            <DialogTitle className="flex items-center text-blue-700">
              <div className="p-1.5 rounded-full bg-gradient-to-r from-blue-100 to-blue-200 mr-2 flex items-center justify-center">
                <Package className="h-5 w-5 text-blue-600" />
              </div>
              <span>จัดการแพ็คเกจของผู้ใช้</span>
            </DialogTitle>
            <div className="lightning-bar w-20 my-1 bg-blue-400"></div>
            <DialogDescription className="text-blue-600/70">
              จัดการแพ็คเกจของผู้ใช้งาน <span className="font-semibold text-blue-700">{selectedUser?.username}</span>
            </DialogDescription>
          </DialogHeader>
          <Tabs defaultValue="packages" className="w-full">
            <TabsList className="grid w-full grid-cols-2 bg-blue-50">
              <TabsTrigger value="packages" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-100 data-[state=active]:to-indigo-100 data-[state=active]:text-blue-700">
                แพ็คเกจปัจจุบัน
              </TabsTrigger>
              <TabsTrigger value="add-package" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-100 data-[state=active]:to-indigo-100 data-[state=active]:text-blue-700">
                เพิ่มแพ็คเกจใหม่
              </TabsTrigger>
            </TabsList>
            <TabsContent value="packages" className="mt-4">
              {userPackages && userPackages.length > 0 ? (
                <div className="space-y-4">
                  {userPackages.map((userPackage) => (
                    <div key={userPackage.id} className={`rounded-lg border p-4 ${userPackage.isActive ? 'border-blue-200 bg-blue-50/50' : 'border-gray-200'}`}>
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="flex items-center gap-2">
                            <h4 className="text-lg font-semibold text-blue-700">
                              {userPackage.package.name}
                            </h4>
                            {userPackage.isActive ? (
                              <Badge className="bg-green-100 text-green-700 hover:bg-green-200">Active</Badge>
                            ) : (
                              <Badge variant="outline" className="text-gray-500">Inactive</Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground">{userPackage.package.description}</p>
                          <div className="mt-2 grid grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm font-medium">วันที่เริ่มต้น</p>
                              <p className="text-sm text-muted-foreground">{formatDate(new Date(userPackage.startDate))}</p>
                            </div>
                            <div>
                              <p className="text-sm font-medium">วันที่หมดอายุ</p>
                              <p className="text-sm text-muted-foreground">{formatDate(new Date(userPackage.endDate))}</p>
                            </div>
                            <div>
                              <p className="text-sm font-medium">จำนวนที่ใช้ได้ / เดือน</p>
                              <p className="text-sm text-muted-foreground">
                                {userPackage.package.requestsLimit > 0
                                  ? `${userPackage.package.requestsLimit} ครั้ง`
                                  : "ไม่จำกัด"}
                              </p>
                            </div>
                            <div>
                              <p className="text-sm font-medium">ใช้ไปแล้ว</p>
                              <p className="text-sm text-muted-foreground">{userPackage.requestsUsed} ครั้ง</p>
                            </div>
                          </div>
                        </div>
                        <div className="flex flex-col gap-2">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="outline" size="sm" className="border-blue-200 hover:bg-blue-50">
                                <Settings className="h-4 w-4 mr-1" />
                                จัดการ
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="w-48 border-blue-200">
                              <DropdownMenuLabel className="text-blue-700">จัดการแพ็คเกจ</DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => togglePackageStatusMutation.mutate({
                                  packageId: userPackage.id,
                                  isActive: !userPackage.isActive
                                })}
                              >
                                {userPackage.isActive ? (
                                  <>
                                    <Shield className="h-4 w-4 mr-2 text-amber-600" />
                                    <span>ปิดการใช้งาน</span>
                                  </>
                                ) : (
                                  <>
                                    <Check className="h-4 w-4 mr-2 text-green-600" />
                                    <span>เปิดใช้งาน</span>
                                  </>
                                )}
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => resetPackageUsageMutation.mutate(userPackage.id)}>
                                <RefreshCcw className="h-4 w-4 mr-2 text-blue-600" />
                                <span>รีเซ็ตยอดใช้งาน</span>
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => {
                                const newDate = new Date(userPackage.endDate);
                                newDate.setMonth(newDate.getMonth() + 1);
                                extendPackageExpiryMutation.mutate({
                                  packageId: userPackage.id,
                                  newEndDate: newDate.toISOString().split('T')[0]
                                });
                              }}>
                                <AlarmClock className="h-4 w-4 mr-2 text-purple-600" />
                                <span>ต่ออายุ 1 เดือน</span>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 border rounded-lg border-blue-100 bg-blue-50/30">
                  <Package className="h-12 w-12 text-blue-300 mx-auto mb-2" />
                  <h3 className="text-lg font-medium text-blue-800">ไม่มีแพ็คเกจ</h3>
                  <p className="text-blue-600/70 mt-1">ผู้ใช้งานยังไม่มีแพ็คเกจที่ลงทะเบียนไว้</p>
                  <Button
                    variant="outline"
                    className="mt-4 border-blue-200 text-blue-700 hover:bg-blue-50"
                    onClick={() => document.querySelector('[data-value="add-package"]')?.click()}
                  >
                    <PlusCircle className="h-4 w-4 mr-2" />
                    เพิ่มแพ็คเกจใหม่
                  </Button>
                </div>
              )}
            </TabsContent>
            <TabsContent value="add-package" className="mt-4">
              <Form {...addPackageForm}>
                <form onSubmit={addPackageForm.handleSubmit((data) => addPackageMutation.mutate(data))} className="space-y-4">
                  <FormField
                    control={addPackageForm.control}
                    name="packageId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>เลือกแพ็คเกจ</FormLabel>
                        <Select
                          onValueChange={(value) => field.onChange(Number(value))}
                          defaultValue={field.value?.toString() || ""}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="เลือกแพ็คเกจ" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {packagesList && packagesList.map((pkg) => (
                              <SelectItem key={pkg.id} value={pkg.id.toString()}>
                                {pkg.name} - {pkg.price.toLocaleString('th-TH')} บาท
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={addPackageForm.control}
                    name="durationMonths"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>ระยะเวลา (เดือน)</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <AlarmClock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                            <Input
                              type="number"
                              placeholder="ระยะเวลา (เดือน)"
                              className="pl-10"
                              min={1}
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value))}
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={addPackageForm.control}
                    name="isActive"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>เปิดใช้งานทันที</FormLabel>
                          <FormDescription>
                            หากเลือก แพ็คเกจนี้จะถูกตั้งเป็นแพ็คเกจที่ใช้งานอยู่ และแพ็คเกจอื่นที่ใช้งานอยู่จะถูกปิดการใช้งาน
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />

                  <DialogFooter>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => {
                        const element = document.querySelector('[data-value="packages"]');
                        if (element instanceof HTMLElement) {
                          element.click();
                        }
                      }}
                      className="border-blue-200 hover:bg-blue-50 hover:text-blue-700"
                    >
                      ยกเลิก
                    </Button>
                    <Button
                      type="submit"
                      disabled={addPackageMutation.isPending}
                      className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white"
                    >
                      {addPackageMutation.isPending && (
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      )}
                      เพิ่มแพ็คเกจ
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>

      {/* ไดอะล็อกจัดการ API keys */}
      <Dialog open={isApiKeysDialogOpen} onOpenChange={setIsApiKeysDialogOpen} modal={false}>
        <DialogContent className="sm:max-w-[700px] border border-purple-200 bg-gradient-to-b from-white to-purple-50/20">
          <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-purple-500/50 via-indigo-500/50 to-purple-500/50"></div>
          <DialogHeader>
            <DialogTitle className="flex items-center text-purple-700">
              <div className="p-1.5 rounded-full bg-gradient-to-r from-purple-100 to-purple-200 mr-2 flex items-center justify-center">
                <Key className="h-5 w-5 text-purple-600" />
              </div>
              <span>จัดการ API Keys</span>
            </DialogTitle>
            <div className="lightning-bar w-20 my-1 bg-purple-400"></div>
            <DialogDescription className="text-purple-600/70">
              จัดการ API Keys ของผู้ใช้งาน <span className="font-semibold text-purple-700">{selectedUser?.username}</span>
            </DialogDescription>
          </DialogHeader>
          <Tabs defaultValue="keys" className="w-full">
            <TabsList className="grid w-full grid-cols-2 bg-purple-50">
              <TabsTrigger value="keys" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-100 data-[state=active]:to-indigo-100 data-[state=active]:text-purple-700">
                API Keys
              </TabsTrigger>
              <TabsTrigger value="add-key" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-100 data-[state=active]:to-indigo-100 data-[state=active]:text-purple-700">
                เพิ่ม API Key ใหม่
              </TabsTrigger>
            </TabsList>
            <TabsContent value="keys" className="mt-4">
              {userApiKeys && userApiKeys.length > 0 ? (
                <div className="space-y-4">
                  {userApiKeys.map((apiKey) => (
                    <div key={apiKey.id} className={`rounded-lg border p-4 ${apiKey.isActive ? 'border-purple-200 bg-purple-50/50' : 'border-gray-200'}`}>
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="flex items-center gap-2">
                            <h4 className="text-lg font-semibold text-purple-700">
                              {apiKey.name}
                            </h4>
                            {apiKey.isActive ? (
                              <Badge className="bg-green-100 text-green-700 hover:bg-green-200">Active</Badge>
                            ) : (
                              <Badge variant="outline" className="text-gray-500">Inactive</Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground">{apiKey.description || 'ไม่มีคำอธิบาย'}</p>
                          <div className="mt-2">
                            <p className="text-sm font-medium">API Key</p>
                            <p className="text-xs font-mono bg-gray-100 p-2 rounded border mt-1 overflow-x-auto">
                              {apiKey.apiKey}
                            </p>
                          </div>
                          <div className="mt-3 grid grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm font-medium">สร้างเมื่อ</p>
                              <p className="text-sm text-muted-foreground">{formatDate(new Date(apiKey.createdAt))}</p>
                            </div>
                            <div>
                              <p className="text-sm font-medium">ใช้งานแล้ว</p>
                              <p className="text-sm text-muted-foreground">{apiKey.usageCount} ครั้ง {apiKey.usageLimit ? `จากทั้งหมด ${apiKey.usageLimit} ครั้ง` : ''}</p>
                            </div>
                          </div>
                        </div>
                        <div className="flex flex-col gap-2">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="outline" size="sm" className="border-purple-200 hover:bg-purple-50">
                                <Settings className="h-4 w-4 mr-1" />
                                จัดการ
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="w-48 border-purple-200">
                              <DropdownMenuLabel className="text-purple-700">จัดการ API Key</DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => toggleApiKeyStatusMutation.mutate({
                                  apiKeyId: apiKey.id,
                                  isActive: !apiKey.isActive
                                })}
                              >
                                {apiKey.isActive ? (
                                  <>
                                    <Shield className="h-4 w-4 mr-2 text-amber-600" />
                                    <span>ปิดการใช้งาน</span>
                                  </>
                                ) : (
                                  <>
                                    <Check className="h-4 w-4 mr-2 text-green-600" />
                                    <span>เปิดใช้งาน</span>
                                  </>
                                )}
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => deleteApiKeyMutation.mutate(apiKey.id)}
                                className="text-red-600 focus:text-red-700 focus:bg-red-50"
                              >
                                <Trash className="h-4 w-4 mr-2" />
                                <span>ลบ API Key</span>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 border rounded-lg border-purple-100 bg-purple-50/30">
                  <Key className="h-12 w-12 text-purple-300 mx-auto mb-2" />
                  <h3 className="text-lg font-medium text-purple-800">ไม่มี API Keys</h3>
                  <p className="text-purple-600/70 mt-1">ผู้ใช้งานยังไม่มี API Keys ที่สร้างไว้</p>
                  <Button
                    variant="outline"
                    className="mt-4 border-purple-200 text-purple-700 hover:bg-purple-50"
                    onClick={() => document.querySelector('[data-value="add-key"]')?.click()}
                  >
                    <PlusCircle className="h-4 w-4 mr-2" />
                    เพิ่ม API Key ใหม่
                  </Button>
                </div>
              )}
            </TabsContent>
            <TabsContent value="add-key" className="mt-4">
              <Form {...addApiKeyForm}>
                <form onSubmit={addApiKeyForm.handleSubmit((data) => addApiKeyMutation.mutate(data))} className="space-y-4">
                  <FormField
                    control={addApiKeyForm.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>ชื่อ API Key</FormLabel>
                        <FormControl>
                          <Input placeholder="ชื่อ API Key" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={addApiKeyForm.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>คำอธิบาย (ไม่บังคับ)</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="คำอธิบาย API Key"
                            {...field}
                            className="resize-none"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={addApiKeyForm.control}
                    name="usageLimit"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>จำกัดการใช้งาน (ไม่บังคับ)</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <BarChart className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                            <Input
                              type="number"
                              placeholder="จำนวนครั้งสูงสุดที่สามารถใช้งานได้"
                              className="pl-10"
                              {...field}
                              onChange={(e) => field.onChange(e.target.value === "" ? undefined : Number(e.target.value))}
                            />
                          </div>
                        </FormControl>
                        <FormDescription>
                          จำนวนครั้งสูงสุดที่สามารถใช้งาน API Key นี้ได้ (เว้นว่างหากไม่ต้องการจำกัด)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={addApiKeyForm.control}
                    name="ipWhitelist"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>IP Whitelist (ไม่บังคับ)</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Settings className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                            <Input
                              placeholder="เช่น ***********,***********"
                              className="pl-10"
                              {...field}
                            />
                          </div>
                        </FormControl>
                        <FormDescription>
                          กรอก IP Address หลายตัวโดยคั่นด้วยเครื่องหมายจุลภาค (,)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <DialogFooter>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => addApiKeyForm.reset()}
                      className="border-purple-200 text-purple-700 hover:bg-purple-50 hover:text-purple-800"
                    >
                      รีเซ็ต
                    </Button>
                    <Button
                      type="submit"
                      disabled={addApiKeyMutation.isPending}
                      className="bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 text-white"
                    >
                      {addApiKeyMutation.isPending ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          กำลังสร้าง...
                        </>
                      ) : (
                        "สร้าง API Key"
                      )}
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>
    </Admin>
  );
}