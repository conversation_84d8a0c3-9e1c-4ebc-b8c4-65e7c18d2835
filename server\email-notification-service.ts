/**
 * บริการส่งการแจ้งเตือนอีเมลอัตโนมัติสำหรับเหตุการณ์สำคัญในระบบ
 * เช่น การตรวจสอบสลิปสำเร็จ, เครดิตเหลือน้อย, และกิจกรรมที่น่าสงสัย
 */

import { emailService } from './email-service';
import { db } from './db';
import { users, userPackages, packages, userAlertSettings } from '@shared/schema';
import { eq, and, lt } from 'drizzle-orm';

export class EmailNotificationService {
  /**
   * ส่งการแจ้งเตือนเมื่อตรวจสอบสลิปสำเร็จ
   * 
   * @param userId ID ของผู้ใช้ที่ตรวจสอบสลิป
   * @param verificationData ข้อมูลการตรวจสอบสลิป
   * @returns Promise<boolean> ผลลัพธ์การส่งอีเมล
   */
  public async sendVerificationSuccessNotification(
    userId: number,
    verificationData: {
      transactionRef: string;
      amount: number;
      date: string;
      receiver: string;
    }
  ): Promise<boolean> {
    try {
      // ตรวจสอบการตั้งค่าการแจ้งเตือนของผู้ใช้
      const alertSettings = await db.select()
        .from(userAlertSettings)
        .where(eq(userAlertSettings.userId, userId))
        .limit(1);
      
      // ถ้าไม่มีการตั้งค่า หรือปิดการแจ้งเตือนสลิป ให้ข้ามการส่งอีเมล
      if (alertSettings.length === 0 || !alertSettings[0].emailOnSuccessfulVerification) {
        console.log(`EmailNotificationService: ผู้ใช้ ${userId} ไม่ได้เปิดใช้งานการแจ้งเตือนสลิปทางอีเมล`);
        return true;
      }
      
      // ดึงข้อมูลผู้ใช้
      const [user] = await db.select().from(users).where(eq(users.id, userId));
      if (!user || !user.email) {
        console.log(`EmailNotificationService: ไม่พบอีเมลของผู้ใช้ ${userId}`);
        return false;
      }
      
      // ส่งอีเมลแจ้งเตือน
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const dashboardUrl = `${process.env.BASE_URL || 'https://slipkuy.com'}/dashboard`;
      
      return await emailService.sendTemplateEmail(
        user.email,
        'verification_success',
        {
          userName: user.username,
          transactionRef: verificationData.transactionRef,
          amount: verificationData.amount.toLocaleString('th-TH'),
          date: verificationData.date,
          receiver: verificationData.receiver,
          dashboardUrl,
          currentYear
        },
        userId
      );
    } catch (error) {
      console.error('EmailNotificationService: เกิดข้อผิดพลาดในการส่งการแจ้งเตือนการตรวจสอบสลิป:', error);
      return false;
    }
  }
  
  /**
   * ส่งการแจ้งเตือนเมื่อเครดิตเหลือน้อย
   * 
   * @param userId ID ของผู้ใช้ที่มีเครดิตเหลือน้อย
   * @param remainingCredit จำนวนเครดิตที่เหลือ
   * @returns Promise<boolean> ผลลัพธ์การส่งอีเมล
   */
  public async sendLowCreditAlert(userId: number, remainingCredit: number): Promise<boolean> {
    try {
      // ตรวจสอบการตั้งค่าการแจ้งเตือนของผู้ใช้
      const alertSettings = await db.select()
        .from(userAlertSettings)
        .where(eq(userAlertSettings.userId, userId))
        .limit(1);
      
      // ถ้าไม่มีการตั้งค่า หรือปิดการแจ้งเตือนเครดิต ให้ข้ามการส่งอีเมล
      if (alertSettings.length === 0 || !alertSettings[0].emailOnLowCredit) {
        console.log(`EmailNotificationService: ผู้ใช้ ${userId} ไม่ได้เปิดใช้งานการแจ้งเตือนเครดิตทางอีเมล`);
        return true;
      }
      
      // ดึงค่า threshold จากการตั้งค่า
      const creditThreshold = alertSettings[0].lowCreditThreshold || 10;
      
      // ถ้าเครดิตยังมากกว่า threshold ให้ข้ามการส่งอีเมล
      if (remainingCredit > creditThreshold) {
        return true;
      }
      
      // ดึงข้อมูลผู้ใช้
      const [user] = await db.select().from(users).where(eq(users.id, userId));
      if (!user || !user.email) {
        console.log(`EmailNotificationService: ไม่พบอีเมลของผู้ใช้ ${userId}`);
        return false;
      }
      
      // ดึงข้อมูลแพ็กเกจของผู้ใช้
      const [activePackage] = await db.select({
        package: packages
      })
      .from(userPackages)
      .where(and(
        eq(userPackages.userId, userId),
        eq(userPackages.status, 'active')
      ))
      .leftJoin(packages, eq(userPackages.packageId, packages.id))
      .limit(1);
      
      const packageName = activePackage ? activePackage.package.name : 'ไม่มีแพ็กเกจ';
      
      // ส่งอีเมลแจ้งเตือน
      const currentYear = new Date().getFullYear();
      const topupUrl = `${process.env.BASE_URL || 'https://slipkuy.com'}/topup`;
      
      return await emailService.sendTemplateEmail(
        user.email,
        'credit_low',
        {
          userName: user.username,
          remainingCredit: remainingCredit.toLocaleString('th-TH'),
          packageName,
          topupUrl,
          currentYear
        },
        userId
      );
    } catch (error) {
      console.error('EmailNotificationService: เกิดข้อผิดพลาดในการส่งการแจ้งเตือนเครดิตเหลือน้อย:', error);
      return false;
    }
  }
  
  /**
   * ส่งการแจ้งเตือนเมื่อพบกิจกรรมที่น่าสงสัย
   * 
   * @param userId ID ของผู้ใช้ที่พบกิจกรรมที่น่าสงสัย
   * @param activityData ข้อมูลกิจกรรมที่น่าสงสัย
   * @returns Promise<boolean> ผลลัพธ์การส่งอีเมล
   */
  public async sendSuspiciousActivityAlert(
    userId: number,
    activityData: {
      timestamp: string;
      ipAddress: string;
      location: string;
      device: string;
      activity: string;
    }
  ): Promise<boolean> {
    try {
      // ตรวจสอบการตั้งค่าการแจ้งเตือนของผู้ใช้
      const alertSettings = await db.select()
        .from(userAlertSettings)
        .where(eq(userAlertSettings.userId, userId))
        .limit(1);
      
      // ถ้าไม่มีการตั้งค่า หรือปิดการแจ้งเตือนความปลอดภัย ให้ข้ามการส่งอีเมล
      if (alertSettings.length === 0 || !alertSettings[0].emailOnSecurityEvents) {
        console.log(`EmailNotificationService: ผู้ใช้ ${userId} ไม่ได้เปิดใช้งานการแจ้งเตือนความปลอดภัยทางอีเมล`);
        return true;
      }
      
      // ดึงข้อมูลผู้ใช้
      const [user] = await db.select().from(users).where(eq(users.id, userId));
      if (!user || !user.email) {
        console.log(`EmailNotificationService: ไม่พบอีเมลของผู้ใช้ ${userId}`);
        return false;
      }
      
      // ส่งอีเมลแจ้งเตือน
      const currentYear = new Date().getFullYear();
      const changePasswordUrl = `${process.env.BASE_URL || 'https://slipkuy.com'}/change-password`;
      
      return await emailService.sendTemplateEmail(
        user.email,
        'suspicious_activity',
        {
          userName: user.username,
          timestamp: activityData.timestamp,
          ipAddress: activityData.ipAddress,
          location: activityData.location,
          device: activityData.device,
          activity: activityData.activity,
          changePasswordUrl,
          currentYear
        },
        userId
      );
    } catch (error) {
      console.error('EmailNotificationService: เกิดข้อผิดพลาดในการส่งการแจ้งเตือนกิจกรรมที่น่าสงสัย:', error);
      return false;
    }
  }
  
  /**
   * ตรวจสอบและส่งการแจ้งเตือนเครดิตเหลือน้อยให้กับผู้ใช้ทุกคน
   * ใช้สำหรับเรียกจาก cron job
   * 
   * @returns Promise<void>
   */
  public async checkAndSendLowCreditAlerts(): Promise<void> {
    try {
      // ดึงการตั้งค่าการแจ้งเตือนของผู้ใช้ที่เปิดใช้งานการแจ้งเตือนเครดิตเหลือน้อย
      const alertSettings = await db.select()
        .from(userAlertSettings)
        .where(eq(userAlertSettings.emailOnLowCredit, true));
      
      for (const setting of alertSettings) {
        try {
          const userId = setting.userId;
          const threshold = setting.lowCreditThreshold || 10;
          
          // ดึงข้อมูลเครดิตของผู้ใช้
          const [user] = await db.select().from(users).where(eq(users.id, userId));
          if (!user) continue;
          
          // ถ้าเครดิตน้อยกว่าหรือเท่ากับ threshold ให้ส่งการแจ้งเตือน
          if (user.credit <= threshold) {
            await this.sendLowCreditAlert(userId, user.credit);
          }
        } catch (userError) {
          console.error(`EmailNotificationService: เกิดข้อผิดพลาดในการตรวจสอบเครดิตของผู้ใช้ ${setting.userId}:`, userError);
          continue;
        }
      }
    } catch (error) {
      console.error('EmailNotificationService: เกิดข้อผิดพลาดในการตรวจสอบและส่งการแจ้งเตือนเครดิตเหลือน้อย:', error);
    }
  }
}

export const emailNotificationService = new EmailNotificationService();