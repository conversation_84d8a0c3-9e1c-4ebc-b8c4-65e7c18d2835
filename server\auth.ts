import passport from "passport";
import { Strategy as LocalStrategy } from "passport-local";
import { Express, Request, Response, NextFunction } from "express";
import session from "express-session";
import { scrypt, randomBytes, timingSafeEqual, createHash } from "crypto";
import { promisify } from "util";
import { storage } from "./storage";
import { User as SelectUser } from "@shared/schema";
import { logger } from "./logger";

declare global {
  namespace Express {
    interface User extends SelectUser {
      isAdmin?: boolean;  // ทำให้เป็น optional
    }

    // เพิ่ม property ให้กับ Request type
    interface Request {
      userPackage?: any;
    }

    // เพิ่ม property ให้กับ Session type
    interface Session {
      ip?: string;
      userAgent?: string;
      location?: string;
      lastActivity?: string;
      fingerprint?: string;  // เพิ่มเก็บ fingerprint เพื่อตรวจสอบอุปกรณ์
      deviceId?: string;     // เพิ่มเก็บรหัสอุปกรณ์
    }
  }
}

const scryptAsync = promisify(scrypt);

// ฟังก์ชันสำหรับแฮชรหัสผ่าน (export เพื่อให้สามารถใช้จาก storage.ts ได้)
export async function hashPassword(password: string) {
  const salt = randomBytes(16).toString("hex");
  const buf = (await scryptAsync(password, salt, 64)) as Buffer;
  return `${buf.toString("hex")}.${salt}`;
}

// ฟังก์ชันสำหรับเปรียบเทียบรหัสผ่าน (export เพื่อให้สามารถใช้จาก auth-api.ts ได้)
export async function comparePasswords(supplied: string, stored: string) {
  const [hashed, salt] = stored.split(".");
  const hashedBuf = Buffer.from(hashed, "hex");
  const suppliedBuf = (await scryptAsync(supplied, salt, 64)) as Buffer;
  return timingSafeEqual(hashedBuf, suppliedBuf);
}

export function setupAuth(app: Express) {
  // ตั้งค่า session
  const sessionSettings: session.SessionOptions = {
    secret: process.env.SESSION_SECRET || "slipkuy_session_secret",
    resave: false,
    saveUninitialized: true, // เปลี่ยนเป็น true เพื่อให้เก็บ session ID แม้ยังไม่มีข้อมูล
    store: storage.sessionStore,
    cookie: {
      maxAge: 30 * 24 * 60 * 60 * 1000, // 30 วัน
      secure: false, // กำหนดเป็น false เสมอสำหรับ development
      httpOnly: true,
      sameSite: 'lax', // ช่วยให้ส่ง cookie ข้ามโดเมนได้
      path: '/'
    },
    rolling: true, // ต่ออายุ session เมื่อมีการใช้งาน
    name: 'slipkuy.sid' // ตั้งชื่อให้ cookie เพื่อป้องกันการชนกัน
  };

  app.set("trust proxy", 1);
  app.use(session(sessionSettings));
  app.use(passport.initialize());
  app.use(passport.session());

  // สร้าง device fingerprint จาก request headers ด้วยวิธีที่มีความยืดหยุ่นมากขึ้น
  function generateFingerprint(req: Request): string {
    // ใช้เฉพาะข้อมูลหลักที่คงที่เพื่อลดความผันผวนระหว่างการเรียก API
    const userAgent = req.headers['user-agent'] || '';

    // แยกส่วนของ User-Agent เพื่อลดความผันผวน
    // ดึงเฉพาะส่วนสำคัญที่ค่อนข้างคงที่ เช่น ข้อมูลระบบปฏิบัติการ และเบราว์เซอร์
    const uaParts = userAgent.match(/(Chrome|Firefox|Safari|Edge|MSIE)\/([0-9]+)/) || [];
    const browserInfo = uaParts.length > 0 ? `${uaParts[1]}/${uaParts[2]}` : '';

    const osParts = userAgent.match(/(Windows|Mac OS X|Linux|Android|iOS)[^;)]*/) || [];
    const osInfo = osParts.length > 0 ? osParts[0] : '';

    // ไม่ใช้ IP address ทั้งหมดเพื่อลดผลกระทบจากการเปลี่ยน IP
    // แทนที่จะใช้ทั้ง IP ให้ใช้เฉพาะส่วนต้นของ IP (network part)
    const ip = req.ip || '';
    // ตัดเฉพาะส่วน prefix ของ IP ที่บ่งบอกเครือข่าย
    const ipPrefix = ip.includes('.') ? ip.split('.').slice(0, 2).join('.') :
                     ip.includes(':') ? ip.split(':').slice(0, 4).join(':') : ip;

    // สร้าง fingerprint ด้วยข้อมูลที่มีความเสถียรมากขึ้น
    // และลดความสำคัญของส่วนที่มีโอกาสเปลี่ยนแปลงบ่อย
    const rawFingerprint = `${browserInfo}|${osInfo}|${ipPrefix}`;

    // ใช้ crypto เพื่อแฮชข้อมูลแต่ใช้เพียง 12 ตัวอักษรแรก เพื่อลดโอกาสที่จะแตกต่างกัน
    return createHash('sha256')
      .update(rawFingerprint)
      .digest('hex')
      .substring(0, 12);
  }

  // Middleware เพื่อเก็บข้อมูล IP, User-Agent และ fingerprint ในเซสชัน
  app.use((req: Request, res: Response, next: NextFunction) => {
    if (req.session) {
      const sessionData = req.session as any;
      sessionData.ip = req.ip;
      sessionData.userAgent = req.headers['user-agent'];

      // บันทึกเวลาเข้าสู่ระบบล่าสุด
      const now = new Date();

      // สร้างและเก็บ fingerprint เมื่อล็อกอินครั้งแรก
      if (!sessionData.fingerprint) {
        sessionData.fingerprint = generateFingerprint(req);
        sessionData.lastLogin = now.toISOString();
      }

      // ตรวจสอบเฉพาะในกรณีเข้าสู่ระบบแล้ว และเป็นการเรียก endpoint สำคัญ
      // เช่น endpoints ที่มีความเสี่ยงสูง หรือมีการอัพเดตข้อมูลสำคัญ
      const sensitiveEndpoints = [
        '/api/user/update-password',
        '/api/user/settings',
        '/api/apikeys/create',
        '/api/apikeys/delete',
        '/api/admin',
        '/api/billing'
      ];

      const isHighRiskEndpoint = sensitiveEndpoints.some(endpoint =>
        req.path.startsWith(endpoint)
      );

      // คำนวณ fingerprint ปัจจุบัน
      const currentFingerprint = generateFingerprint(req);

      // ให้ fingerprint มีความยืดหยุ่นในการเปลี่ยนแปลงเล็กน้อย
      // โดยยอมให้มีการเปลี่ยนแปลง fingerprint เมื่อเวลาผ่านไปมากกว่า 12 ชั่วโมง
      const lastLoginTime = sessionData.lastLogin ? new Date(sessionData.lastLogin) : null;
      const twelveHoursInMs = 12 * 60 * 60 * 1000;
      const isOldSession = lastLoginTime && (now.getTime() - lastLoginTime.getTime() > twelveHoursInMs);

      // ตรวจสอบ session hijacking เฉพาะในกรณีที่:
      // 1. ผู้ใช้ล็อกอินแล้ว
      // 2. มี fingerprint ในเซสชัน
      // 3. เป็น endpoint ที่มีความเสี่ยงสูง หรือเซสชันเก่ากว่า 12 ชั่วโมง
      // 4. fingerprint ไม่ตรงกัน
      if (
        req.isAuthenticated() &&
        sessionData.fingerprint &&
        isHighRiskEndpoint && // เปลี่ยนจากการตรวจสอบสองเงื่อนไขเป็นตรวจสอบแค่กรณีที่เป็น endpoint เสี่ยงเท่านั้น
        currentFingerprint !== sessionData.fingerprint &&
        process.env.NODE_ENV === 'production' // ตรวจสอบเฉพาะในโหมดการทำงานจริงเท่านั้น
      ) {
        logger.security(`[การรักษาความปลอดภัย] ตรวจพบการขโมย session:
          ผู้ใช้: ${req.user?.username} (${req.user?.id}),
          เซสชัน: ${req.sessionID},
          เส้นทาง: ${req.path},
          Fingerprint เดิม: ${sessionData.fingerprint},
          Fingerprint ปัจจุบัน: ${currentFingerprint},
          เวลาตั้งแต่ล็อกอิน: ${lastLoginTime ? Math.round((now.getTime() - lastLoginTime.getTime()) / 1000 / 60) : 'ไม่ทราบ'} นาที
        `);

        // บันทึกเหตุการณ์ความปลอดภัยลงในฐานข้อมูล (จะพัฒนาในอนาคต)

        // ทำการล็อกเอาท์ผู้ใช้ทันที - เฉพาะในโหมด production
        req.logout((err) => {
          if (err) {
            logger.error(`[การรักษาความปลอดภัย] ข้อผิดพลาดขณะบังคับล็อกเอาท์: ${err instanceof Error ? err.message : 'Unknown error'}`);
          }
          req.session.destroy((destroyErr) => {
            if (destroyErr) {
              console.error("Error destroying session:", destroyErr);
            }
            res.clearCookie('slipkuy.sid');
            return res.status(401).json({
              message: "ตรวจพบความผิดปกติในการเข้าใช้งาน กรุณาล็อกอินใหม่อีกครั้ง"
            });
          });
        });
        return;
      }

      // ถ้าเป็นการเข้าถึงจากอุปกรณ์เดิมแต่ fingerprint เปลี่ยนเล็กน้อย (กรณีธรรมดา ไม่ใช่ endpoint สำคัญ)
      // ให้อัพเดต fingerprint เป็นปัจจุบัน เพื่อลดการถูกบังคับล็อกเอาท์โดยไม่จำเป็น
      // อัพเดต fingerprint เสมอในระหว่างการพัฒนา
      if (
        req.isAuthenticated() &&
        sessionData.fingerprint &&
        currentFingerprint !== sessionData.fingerprint
      ) {
        // ใช้ logger.debug แทน console.log เพื่อลดความรกใน console
        // และจะแสดงเฉพาะในโหมด development และเมื่อตั้งค่า log level เป็น DEBUG เท่านั้น
        if (process.env.NODE_ENV === 'development') {
          // โค้ดเก่า: console.log(`[INFO] Updating fingerprint for user ${req.user?.username} (${req.user?.id}):
          //   ${sessionData.fingerprint} -> ${currentFingerprint}`);
        }
        sessionData.fingerprint = currentFingerprint;
      }

      // สร้างข้อมูลตำแหน่งจาก IP
      if (!sessionData.location) {
        sessionData.location = 'ประเทศไทย';
      }

      // แทนที่จะใช้ fingerprint เดียว ให้ใช้รายการของ fingerprint ที่ยอมรับได้
      // ใช้กับเฉพาะเซสชันที่ล็อกอินแล้วเท่านั้น
      if (req.isAuthenticated() && req.user) {
        if (!sessionData.fingerprints) {
          sessionData.fingerprints = [currentFingerprint];
        } else if (!sessionData.fingerprints.includes(currentFingerprint)) {
          // คำนวณความเหมือนระหว่าง fingerprint ด้วยวิธีที่ยืดหยุ่นมากขึ้น
          const calculateSimilarity = (fp1, fp2) => {
            if (!fp1 || !fp2) return 0;

            // ถ้าเริ่มต้นเหมือนกัน 8 ตัวอักษร ให้ถือว่าคล้ายกันมาก (อาจเป็นอุปกรณ์เดียวกัน)
            if (fp1.substring(0, 8) === fp2.substring(0, 8)) {
              return 0.8; // 80% similarity
            }

            // ตัดให้มีความยาวเท่ากัน
            if (fp1.length !== fp2.length) {
              const minLength = Math.min(fp1.length, fp2.length);
              fp1 = fp1.substring(0, minLength);
              fp2 = fp2.substring(0, minLength);
            }

            // นับจำนวนตัวอักษรที่ตรงกัน
            let sameChars = 0;
            for (let i = 0; i < fp1.length; i++) {
              if (fp1[i] === fp2[i]) sameChars++;
            }

            // ให้น้ำหนักมากกับตัวอักษรต้นๆ มากกว่าตัวท้ายๆ
            const weightedSimilarity = (sameChars / fp1.length) * 1.5;

            // จำกัดค่าสูงสุดที่ 1.0
            return Math.min(weightedSimilarity, 1.0);
          };

          // ตรวจสอบความเหมือนกับ fingerprint ที่เคยบันทึกไว้
          let maxSimilarity = 0;
          for (const storedFp of sessionData.fingerprints) {
            const similarity = calculateSimilarity(currentFingerprint, storedFp);
            if (similarity > maxSimilarity) {
              maxSimilarity = similarity;
            }
          }

          // ลดระดับความคล้ายคลึงขั้นต่ำเป็น 30% เพื่อให้มีความยืดหยุ่นมากขึ้น
          const similarityThreshold = 0.3; // ลดความเข้มงวดลงอย่างมากเพื่อลดปัญหาการถูกบังคับล็อกเอาท์
          if (maxSimilarity >= similarityThreshold || !isHighRiskEndpoint) {
            // จำกัดจำนวน fingerprints ไม่เกิน 5
            if (sessionData.fingerprints.length >= 5) {
              sessionData.fingerprints.shift();
            }
            sessionData.fingerprints.push(currentFingerprint);

            if (req.user.id && req.user.username) {
              // ลดการแสดงข้อความ fingerprint update ในระดับปกติ
              // โค้ดเก่า: console.log(`[INFO] อัพเดต fingerprint สำหรับผู้ใช้ ${req.user.username} (${req.user.id}):
              //   ${currentFingerprint} (ความเหมือน: ${(maxSimilarity * 100).toFixed(2)}%)`);
            }
          }
          // ถ้าความเหมือนต่ำเกินไปและเป็น endpoint ที่มีความเสี่ยงสูง
          else if (isHighRiskEndpoint) {
            console.warn(`[SECURITY WARNING] ตรวจพบ fingerprint ที่แตกต่างเกินไปสำหรับ endpoint ที่มีความเสี่ยงสูง:
              User ID: ${req.user.id},
              Username: ${req.user.username},
              Session ID: ${req.sessionID},
              Path: ${req.path},
              ความเหมือนสูงสุด: ${(maxSimilarity * 100).toFixed(2)}%,
              Current fingerprint: ${currentFingerprint},
              Time since login: ${lastLoginTime ? Math.round((now.getTime() - lastLoginTime.getTime()) / 1000 / 60) : 'unknown'} minutes
            `);

            // ในระหว่างพัฒนา เราจะบันทึก warning แต่ไม่เตะผู้ใช้ออกจากระบบ
            // เพื่อให้สามารถทดสอบได้สะดวก
            if (process.env.NODE_ENV === 'production') {
              req.logout((err) => {
                if (err) {
                  console.error("Error during forced logout:", err);
                }
                req.session.destroy((destroyErr) => {
                  if (destroyErr) {
                    console.error("Error destroying session:", destroyErr);
                  }
                  res.clearCookie('slipkuy.sid');
                  return res.status(401).json({
                    message: "ตรวจพบความผิดปกติในการเข้าใช้งาน กรุณาล็อกอินใหม่อีกครั้ง"
                  });
                });
              });
              return;
            }
          }
        }
      } else {
        // สำหรับผู้ใช้ที่ยังไม่ได้ล็อกอิน ให้เก็บเพียง fingerprint เดียว
        if (!sessionData.fingerprint) {
          sessionData.fingerprint = currentFingerprint;
        }
      }

      // อัพเดตเวลาล่าสุดที่ผู้ใช้มีการใช้งาน
      sessionData.lastActivity = new Date().toISOString();
    }
    next();
  });

  // ตั้งค่ากลยุทธ์การเข้าสู่ระบบด้วย username, email หรือเบอร์โทรศัพท์ และ password
  passport.use(
    new LocalStrategy(
      {
        usernameField: 'username', // ยังคงใช้ชื่อฟิลด์เดิม แต่จะรองรับทั้ง username, email, เบอร์โทรศัพท์
        passwordField: 'password'
      },
      async (identifier, password, done) => {
      try {
        logger.debug(`[การล็อกอิน] พยายามล็อกอินด้วย: ${identifier}`);

        // ค้นหาผู้ใช้จากข้อมูลที่ระบุ (username, email หรือเบอร์โทรศัพท์)
        const user = await storage.getUserByIdentifier(identifier);

        if (!user) {
          logger.warn(`[การล็อกอิน] ไม่พบผู้ใช้ ${identifier}`);
          return done(null, false, { message: "ชื่อผู้ใช้ อีเมล หรือรหัสผ่านไม่ถูกต้อง" });
        }

        logger.debug(`[การล็อกอิน] กำลังประมวลผลการล็อกอินของ ${identifier}`);

        // ตรวจสอบสถานะผู้ใช้
        if (user.status !== 'active') {
          logger.warn(`[การล็อกอิน] ล้มเหลว: บัญชีผู้ใช้ ${user.username} สถานะไม่ใช่ active (${user.status})`);
          return done(null, false, { message: "บัญชีผู้ใช้ถูกระงับ กรุณาติดต่อผู้ดูแลระบบ" });
        }

        // ล็อกอินง่ายสำหรับการทดสอบ (ใช้ identifier เป็น password หรือรหัสทดสอบ)
        if (process.env.NODE_ENV === 'development' && (password === identifier || password === 'testpassword')) {
          logger.debug(`[การล็อกอิน] สำเร็จ (โหมดทดสอบ): ผู้ใช้ ${user.username} (${user.id}) บทบาท ${user.role}`);
          return done(null, user);
        }

        // ตรวจสอบรหัสผ่าน
        try {
          const passwordValid = await comparePasswords(password, user.password);

          if (passwordValid) {
            logger.debug(`[การล็อกอิน] สำเร็จ: ผู้ใช้ ${user.username} (${user.id}) บทบาท ${user.role}`);
            return done(null, user);
          } else {
            logger.warn(`[การล็อกอิน] ล้มเหลว: รหัสผ่านไม่ถูกต้องสำหรับผู้ใช้ ${user.username}`);
            return done(null, false, { message: "ชื่อผู้ใช้ อีเมล หรือรหัสผ่านไม่ถูกต้อง" });
          }
        } catch (pwError) {
          logger.error(`[การล็อกอิน] ข้อผิดพลาดในการตรวจสอบรหัสผ่าน: ${pwError instanceof Error ? pwError.message : 'Unknown error'}`);
          return done(null, false, { message: "เกิดข้อผิดพลาดในการตรวจสอบรหัสผ่าน" });
        }
      } catch(err) {
        logger.error(`[การล็อกอิน] ข้อผิดพลาด: ${err instanceof Error ? err.message : 'Unknown error'}`);
        return done(err);
      }
    }),
  );

  passport.serializeUser((user, done) => done(null, user.id));
  passport.deserializeUser(async (id: number, done) => {
    try {
      const user = await storage.getUser(id);
      if (user) {
        // Add isAdmin property based on role
        (user as any).isAdmin = user.role === 'admin';
      }
      done(null, user);
    } catch(err) {
      done(err);
    }
  });

  // เส้นทางสำหรับลงทะเบียน
  app.post("/api/register", async (req, res, next) => {
    try {
      // ตรวจสอบว่ามีผู้ใช้งานในระบบแล้วหรือไม่
      const existingUsername = await storage.getUserByUsername(req.body.username);
      if (existingUsername) {
        return res.status(400).json({ message: "ชื่อผู้ใช้นี้มีอยู่ในระบบแล้ว" });
      }

      const existingEmail = await storage.getUserByEmail(req.body.email);
      if (existingEmail) {
        return res.status(400).json({ message: "อีเมลนี้มีอยู่ในระบบแล้ว" });
      }

      // ตรวจสอบว่ามีการส่งข้อมูลเบอร์โทรหรืออีเมลมาหรือไม่
      const { email, phoneNumber, verificationMethod } = req.body;

      if (!email && !phoneNumber) {
        return res.status(400).json({ message: "กรุณาระบุอีเมลหรือเบอร์โทรศัพท์" });
      }

      // สร้างผู้ใช้งานใหม่
      try {
        // ลบข้อมูล ID ที่อาจมีการส่งมา เพื่อให้ใช้ค่า sequence ที่ถูกต้อง
        const userData = { ...req.body };
        delete userData.id; // ป้องกันการกำหนด ID โดยตรง

        const user = await storage.createUser({
          ...userData,
          email_verified: false, // เริ่มต้นยังไม่ได้ยืนยันอีเมล
          phone_verified: false, // เริ่มต้นยังไม่ได้ยืนยันเบอร์โทร
          password: await hashPassword(req.body.password),
        });

        // ส่งรหัสยืนยันไปยังอีเมลหรือเบอร์โทรศัพท์
        try {
          const verificationService = require("./verification-service").verificationService;

          if (email) {
            await verificationService.sendEmailVerificationCode(email, user.id);
          }

          if (phoneNumber) {
            await verificationService.sendPhoneOTP(phoneNumber, user.id);
          }
        } catch (verificationErr) {
          logger.error(`เกิดข้อผิดพลาดในการส่งรหัสยืนยัน: ${verificationErr instanceof Error ? verificationErr.message : 'Unknown error'}`);
          // ถึงแม้จะส่งรหัสยืนยันไม่สำเร็จ เราก็ยังทำการสร้างบัญชีผู้ใช้ให้สำเร็จ
        }

        // ล็อกอินผู้ใช้ โดยบัญชีจะถูกบังคับให้ยืนยันตัวตนก่อนใช้งานเต็มรูปแบบ
        req.login(user, (err) => {
          if (err) return next(err);
          res.status(201).json({
            ...user,
            needVerification: true,
            verificationMethod: email ? 'email' : 'phone'
          });
        });
      } catch (createErr) {
        logger.error(`เกิดข้อผิดพลาดในการสร้างผู้ใช้: ${createErr instanceof Error ? createErr.message : 'Unknown error'}`);
        return res.status(500).json({ message: "เกิดข้อผิดพลาดในการสร้างบัญชีผู้ใช้" });
      }
    } catch(err) {
      logger.error(`เกิดข้อผิดพลาดในการลงทะเบียน: ${err instanceof Error ? err.message : 'Unknown error'}`);
      next(err);
    }
  });

  // เส้นทางสำหรับเข้าสู่ระบบ
  app.post("/api/login", (req, res, next) => {
    logger.debug(`[การล็อกอิน] การพยายามล็อกอิน: ${req.body.username}`);

    passport.authenticate("local", (err, user, info) => {
      if (err) {
        logger.error(`[การล็อกอิน] ข้อผิดพลาด: ${err instanceof Error ? err.message : 'Unknown error'}`);
        return next(err);
      }

      if (!user) {
        logger.warn(`[การล็อกอิน] ล้มเหลว: ผู้ใช้ ${req.body.username} - ${info?.message || "ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง"}`);
        return res.status(401).json({ message: info?.message || "ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง" });
      }

      req.login(user, (loginErr) => {
        if (loginErr) {
          logger.error(`[การล็อกอิน] ข้อผิดพลาดในเซสชัน: ${loginErr instanceof Error ? loginErr.message : 'Unknown error'}`);
          return next(loginErr);
        }

        logger.debug(`[การล็อกอิน] สำเร็จ: ผู้ใช้ ${user.username} (${user.id}) บทบาท ${user.role} เซสชัน ${req.sessionID}`);

        return res.status(200).json(user);
      });
    })(req, res, next);
  });

  // เส้นทางสำหรับออกจากระบบ
  app.post("/api/logout", (req, res, next) => {
    const userId = req.user?.id;
    const username = req.user?.username;

    req.logout((err) => {
      if (err) {
        logger.error(`[การออกจากระบบ] ข้อผิดพลาด: ${err instanceof Error ? err.message : 'Unknown error'}`);
        return next(err);
      }

      // ลบเซสชันหลังจากล็อกเอาท์สำเร็จ
      req.session.destroy((destroyError) => {
        if (destroyError) {
          logger.error(`[การออกจากระบบ] ข้อผิดพลาดในการลบเซสชัน: ${destroyError instanceof Error ? destroyError.message : 'Unknown error'}`);
          return next(destroyError);
        }
        // ล้างคุกกี้ของผู้ใช้
        res.clearCookie('slipkuy.sid', { path: '/' });

        if (userId && username) {
          logger.debug(`[การออกจากระบบ] สำเร็จ: ผู้ใช้ ${username} (${userId})`);
        }

        res.sendStatus(200);
      });
    });
  });

  // เส้นทางสำหรับตรวจสอบสถานะการล็อกอิน
  app.get("/api/login-status", (req, res) => {
    logger.debug(req.isAuthenticated()
      ? `[สถานะการล็อกอิน] ล็อกอินแล้ว: ผู้ใช้ ${req.user?.username} (${req.user?.id}) เซสชัน ${req.sessionID}`
      : `[สถานะการล็อกอิน] ยังไม่ได้ล็อกอิน: เซสชัน ${req.sessionID}`);

    res.json({
      isAuthenticated: req.isAuthenticated(),
      user: req.user ? {
        id: req.user.id,
        username: req.user.username,
        role: req.user.role
      } : null,
      sessionID: req.sessionID,
      hasSession: !!req.session
    });
  });

  // เส้นทางสำหรับดึงข้อมูลผู้ใช้งานปัจจุบัน
  app.get("/api/user", (req, res) => {
    if (!req.isAuthenticated()) {
      // เพิ่มบันทึกการเข้าถึงที่ไม่ได้รับอนุญาตสำหรับการแก้ไขปัญหา
      logger.debug(`[ข้อมูลผู้ใช้] การเข้าถึงไม่ได้รับอนุญาต: เซสชัน ${req.sessionID}`);
      return res.status(401).json({ message: "กรุณาล็อกอินเพื่อดูข้อมูลผู้ใช้" });
    }

    logger.debug(`[ข้อมูลผู้ใช้] ล็อกอินสำเร็จ: ผู้ใช้ ${req.user?.username} (${req.user?.id}) บทบาท ${req.user?.role}`);
    res.json(req.user);
  });
}

// ฟังก์ชันตรวจสอบว่าผู้ใช้เป็นแอดมินหรือไม่
export function isAdmin(req: Request): boolean {
  return req.isAuthenticated() && req.user?.isAdmin === true;
}

// Middleware สำหรับตรวจสอบว่าผู้ใช้เป็นแอดมินหรือไม่
export function adminRequired(req: Request, res: Response, next: NextFunction) {
  if (isAdmin(req)) {
    return next();
  }
  return res.status(403).json({ message: "คุณไม่มีสิทธิ์เข้าถึงส่วนนี้" });
}
