@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    📊 สถานะระบบ SLIPKUY Docker
echo ========================================
echo.

echo 🔍 สถานะ Docker Containers:
docker-compose ps

echo.
echo 🌐 ทดสอบการเชื่อมต่อเว็บไซต์:
powershell -Command "try { $response = Invoke-WebRequest -Uri http://localhost:4000 -UseBasicParsing -TimeoutSec 5; Write-Host '✅ เว็บไซต์ทำงานปกติ (HTTP' $response.StatusCode ')' -ForegroundColor Green } catch { Write-Host '❌ เว็บไซต์ไม่ตอบสนอง' -ForegroundColor Red }"

echo.
echo 💾 สถานะฐานข้อมูล:
docker-compose exec -T postgres psql -U slipkuy_user -d slipkuy_db -c "SELECT 'Database OK' as status, now() as current_time;" 2>nul || echo ❌ ฐานข้อมูลไม่ตอบสนอง

echo.
echo 🔄 สถานะ Redis:
docker-compose exec -T redis redis-cli ping 2>nul || echo ❌ Redis ไม่ตอบสนอง

echo.
echo 📈 การใช้งาน Resources:
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"

echo.
echo 🌐 URL สำหรับเข้าใช้งาน:
echo    - เว็บไซต์หลัก: http://localhost:4000
echo    - หน้า Admin: http://localhost:4000/admin
echo    - pgAdmin: http://localhost:5050 (ถ้าเปิดใช้งาน)
echo.
pause
