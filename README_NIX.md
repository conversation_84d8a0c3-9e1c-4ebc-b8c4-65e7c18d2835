# การใช้งาน SLIPKUY ด้วย Nix บน Ubuntu

คู่มือนี้อธิบายวิธีการตั้งค่าและใช้งานโปรเจค SLIPKUY ด้วย Nix บนระบบปฏิบัติการ Ubuntu

## ข้อกำหนดเบื้องต้น

1. ติดตั้ง Nix Package Manager:
   ```bash
   sh <(curl -L https://nixos.org/nix/install) --daemon
   ```

2. (ทางเลือก) เปิดใช้งาน Nix Flakes:
   ```bash
   mkdir -p ~/.config/nix
   echo "experimental-features = nix-command flakes" >> ~/.config/nix/nix.conf
   ```

3. (ทางเลือก) ติดตั้ง direnv เพื่อโหลดสภาพแวดล้อมอัตโนมัติ:
   ```bash
   nix-env -iA nixpkgs.direnv
   ```

   เพิ่มบรรทัดต่อไปนี้ใน `~/.bashrc` หรือ `~/.zshrc`:
   ```bash
   eval "$(direnv hook bash)"  # สำหรับ bash
   # หรือ
   eval "$(direnv hook zsh)"   # สำหรับ zsh
   ```

## วิธีการใช้งาน

### วิธีที่ 1: ใช้ nix-shell (แนะนำ)

1. เข้าสู่โฟลเดอร์โปรเจค:
   ```bash
   cd /path/to/slipkuy
   ```

2. เข้าสู่ Nix shell:
   ```bash
   nix-shell
   ```

3. เริ่มต้นใช้งานโปรเจค:
   ```bash
   npm install  # ติดตั้งแพคเกจ (ทำครั้งแรกเท่านั้น)
   npm run dev  # เริ่มเซิร์ฟเวอร์ในโหมดพัฒนา
   ```

### วิธีที่ 2: ใช้ Nix Flakes (ถ้าเปิดใช้งาน Flakes)

1. เข้าสู่โฟลเดอร์โปรเจค:
   ```bash
   cd /path/to/slipkuy
   ```

2. เข้าสู่ shell โดยตรง:
   ```bash
   nix develop
   ```

   หรือรันคำสั่งโดยตรง:
   ```bash
   nix develop --command npm run dev
   ```

3. หรือใช้ direnv (ถ้าติดตั้งไว้):
   ```bash
   direnv allow
   npm run dev
   ```

## การตั้งค่าฐานข้อมูล

โปรเจคนี้ใช้ฐานข้อมูล PostgreSQL จาก NeonDB ซึ่งได้กำหนดค่าไว้ใน `.env` แล้ว:

```
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require
```

## คำสั่งที่ใช้บ่อย

- `npm run dev`: เริ่มเซิร์ฟเวอร์ในโหมดพัฒนา
- `npm run build`: สร้างไฟล์สำหรับการใช้งานจริง
- `npm run start`: เริ่มเซิร์ฟเวอร์ในโหมดการใช้งานจริง
- `npm run db:push`: อัปเดตโครงสร้างฐานข้อมูล

## การแก้ไขปัญหา

### ปัญหาการเชื่อมต่อกับ NeonDB

หากพบข้อผิดพลาด ETIMEDOUT เกี่ยวกับการเชื่อมต่อ WebSocket กับ NeonDB:
- ตรวจสอบการเชื่อมต่ออินเทอร์เน็ต
- ตรวจสอบว่า URL ของฐานข้อมูลถูกต้อง
- ตรวจสอบว่า NeonDB ไม่มีการบำรุงรักษาหรือปัญหาการให้บริการ

ข้อผิดพลาดนี้อาจเกิดขึ้นกับบริการ EmailService และ SMSService แต่ไม่ได้ทำให้เซิร์ฟเวอร์หยุดทำงาน

### ปัญหาเกี่ยวกับ .env

หากพบปัญหาเกี่ยวกับการโหลดไฟล์ .env:
- ตรวจสอบว่าไฟล์ .env มีรูปแบบที่ถูกต้อง
- ตรวจสอบว่าค่าที่มีเครื่องหมายพิเศษ (เช่น <, >, หรือช่องว่าง) ถูกครอบด้วยเครื่องหมายคำพูด

## หมายเหตุ

- ไฟล์ `.env` ควรถูกเพิ่มใน `.gitignore` เพื่อไม่ให้ข้อมูลที่ละเอียดอ่อนถูกเผยแพร่
- โฟลเดอร์ `public/uploads/slips` จะถูกสร้างโดยอัตโนมัติเมื่อเข้าสู่ Nix shell
