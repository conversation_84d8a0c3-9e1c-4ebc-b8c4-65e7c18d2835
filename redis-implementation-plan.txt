# แผนการใช้งาน Redis เพื่อเพิ่มประสิทธิภาพระบบ SLIPKUY

## ปัญหาที่พบในระบบปัจจุบัน

ระบบ SLIPKUY ปัจจุบันมีการทำงานที่ต้องเข้าถึงฐานข้อมูล PostgreSQL บ่อยครั้ง โดยเฉพาะในกรณีที่มีการส่ง request ซ้ำๆ เพื่อตรวจสอบสลิปธนาคาร ทำให้เกิดปัญหาดังนี้:

1. CPU ทำงานหนักเกินไปเนื่องจากต้องประมวลผล SQL queries จำนวนมาก
2. ฐานข้อมูลถูกเข้าถึงบ่อยครั้ง ทำให้ประสิทธิภาพโดยรวมของระบบลดลง
3. เมื่อมีการส่ง request ซ้ำๆ ระบบต้องค้นหาข้อมูลในฐานข้อมูลทุกครั้ง แม้ว่าจะเป็นข้อมูลเดิม

## แนวทางการแก้ไขโดยใช้ Redis

Redis เป็น in-memory database ที่มีความเร็วสูง สามารถใช้เป็น cache เพื่อเก็บข้อมูลที่ใช้บ่อยได้ โดยเฉพาะข้อมูลการตรวจสอบ QR code ที่เคยตรวจสอบไปแล้ว ซึ่งจะช่วยลดการเข้าถึงฐานข้อมูลหลักและเพิ่มประสิทธิภาพของระบบ

## ขั้นตอนการทำงานใหม่

1. เมื่อมีการส่งรูปภาพสลิปเข้ามา ระบบจะตรวจสอบ QR code ในรูปภาพ
2. ถ้าพบ QR code ระบบจะตรวจสอบใน Redis cache ก่อนว่ามีข้อมูลการตรวจสอบนี้หรือไม่
3. ถ้าพบข้อมูลใน Redis (Cache Hit) ระบบจะใช้ข้อมูลนั้นตอบกลับทันที โดยไม่ต้องเข้าถึงฐานข้อมูล PostgreSQL
4. ถ้าไม่พบข้อมูลใน Redis (Cache Miss) ระบบจะค้นหาในฐานข้อมูล PostgreSQL
5. ถ้าพบข้อมูลในฐานข้อมูล ระบบจะเก็บข้อมูลนั้นใน Redis เพื่อใช้ในครั้งต่อไป
6. ถ้าไม่พบข้อมูลในฐานข้อมูล ระบบจะส่งรูปไปตรวจสอบและบันทึกข้อมูลลงฐานข้อมูล พร้อมทั้งเก็บใน Redis

## โครงสร้างไฟล์ที่ต้องสร้างและแก้ไข

### 1. สร้างไฟล์ Redis Client (server/redis-client.ts)

ไฟล์นี้จะจัดการการเชื่อมต่อกับ Redis และมีฟังก์ชันสำหรับการเก็บและดึงข้อมูลจาก Redis

```typescript
import Redis from 'ioredis';
import { logger } from './logger';

// สร้าง Redis client
const redisClient = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  db: parseInt(process.env.REDIS_DB || '0'),
});

// ตรวจสอบการเชื่อมต่อ
redisClient.on('connect', () => {
  logger.info('Redis client connected');
});

redisClient.on('error', (err) => {
  logger.error('Redis client error:', err);
});

// ฟังก์ชันสำหรับเก็บข้อมูล QR code ใน Redis
export async function cacheQRCodeVerification(userId: number, qrData: string, verificationData: any, expirySeconds: number = 86400): Promise<void> {
  try {
    const key = `qrcode:${userId}:${qrData}`;
    await redisClient.set(key, JSON.stringify(verificationData), 'EX', expirySeconds);
    logger.debug(`Cached QR code verification data for user ${userId}, QR: ${qrData.substring(0, 20)}...`);
  } catch (error) {
    logger.error('Error caching QR code verification:', error);
  }
}

// ฟังก์ชันสำหรับดึงข้อมูล QR code จาก Redis
export async function getCachedQRCodeVerification(userId: number, qrData: string): Promise<any | null> {
  try {
    const key = `qrcode:${userId}:${qrData}`;
    const cachedData = await redisClient.get(key);
    
    if (cachedData) {
      logger.debug(`Cache hit for QR code verification: user ${userId}, QR: ${qrData.substring(0, 20)}...`);
      return JSON.parse(cachedData);
    }
    
    logger.debug(`Cache miss for QR code verification: user ${userId}, QR: ${qrData.substring(0, 20)}...`);
    return null;
  } catch (error) {
    logger.error('Error getting cached QR code verification:', error);
    return null;
  }
}

export default redisClient;
```

### 2. แก้ไขฟังก์ชัน findDuplicateSlipByQRData ใน storage.ts

ฟังก์ชันนี้จะถูกแก้ไขให้ตรวจสอบใน Redis ก่อน ถ้าไม่พบจึงค้นหาในฐานข้อมูล

```typescript
// ตรวจสอบสลิปซ้ำจากข้อมูล QR code
async findDuplicateSlipByQRData(userId: number, qrData: string): Promise<SlipVerification[]> {
  try {
    // ตรวจสอบใน Redis cache ก่อน
    const cachedVerifications = await getCachedQRCodeVerification(userId, qrData);
    if (cachedVerifications) {
      console.log(`[CACHE HIT] พบข้อมูลสลิปซ้ำใน Redis cache สำหรับ QR: ${qrData.substring(0, 20)}...`);
      return Array.isArray(cachedVerifications) ? cachedVerifications : [cachedVerifications];
    }
    
    // ถ้าไม่พบใน cache ให้ค้นหาในฐานข้อมูล
    console.log(`[CACHE MISS] ไม่พบข้อมูลใน Redis cache สำหรับ QR: ${qrData.substring(0, 20)}... ค้นหาในฐานข้อมูล`);
    
    // ดึงข้อมูลสลิปที่มี QR code เดียวกันของผู้ใช้เดียวกัน
    const results = await db
      .select()
      .from(slipVerifications)
      .where(
        and(
          eq(slipVerifications.userId, userId),
          eq(slipVerifications.qrData, qrData),
          // เลือกเฉพาะสลิปที่มีสถานะสำเร็จหรือกำลังประมวลผล
          or(
            eq(slipVerifications.status, 'success'),
            eq(slipVerifications.status, 'processing')
          )
        )
      )
      .orderBy(desc(slipVerifications.createdAt))
      .limit(5); // จำกัดจำนวนการค้นหา
    
    // ถ้าพบข้อมูลในฐานข้อมูล ให้เก็บใน Redis cache
    if (results.length > 0) {
      console.log(`[DB HIT] พบข้อมูลสลิปซ้ำในฐานข้อมูลสำหรับ QR: ${qrData.substring(0, 20)}... เก็บใน Redis cache`);
      await cacheQRCodeVerification(userId, qrData, results, 86400); // cache 24 ชั่วโมง
    } else {
      console.log(`[DB MISS] ไม่พบข้อมูลสลิปซ้ำในฐานข้อมูลสำหรับ QR: ${qrData.substring(0, 20)}...`);
    }
    
    return results;
  } catch (error) {
    console.error(`[ERROR] เกิดข้อผิดพลาดในการค้นหาสลิปซ้ำ:`, error);
    // กรณีเกิดข้อผิดพลาด ให้ค้นหาในฐานข้อมูลโดยตรง
    const results = await db
      .select()
      .from(slipVerifications)
      .where(
        and(
          eq(slipVerifications.userId, userId),
          eq(slipVerifications.qrData, qrData),
          or(
            eq(slipVerifications.status, 'success'),
            eq(slipVerifications.status, 'processing')
          )
        )
      )
      .orderBy(desc(slipVerifications.createdAt))
      .limit(5);
    
    return results;
  }
}
```

### 3. แก้ไขฟังก์ชัน createSlipVerificationWithQRData ใน storage.ts

ฟังก์ชันนี้จะถูกแก้ไขให้เก็บข้อมูลใน Redis เมื่อสร้างการตรวจสอบใหม่

```typescript
// สร้างการตรวจสอบสลิปใหม่พร้อมข้อมูล QR code
async createSlipVerificationWithQRData(verification: InsertSlipVerification, qrData: string): Promise<SlipVerification> {
  const verificationWithQR = {
    ...verification,
    qrData
  };
  const [slipVerification] = await db.insert(slipVerifications).values(verificationWithQR).returning();
  
  // เก็บข้อมูลใน Redis cache เมื่อสร้างการตรวจสอบใหม่
  if (slipVerification && slipVerification.userId && qrData) {
    await cacheQRCodeVerification(slipVerification.userId, qrData, [slipVerification], 86400);
  }
  
  return slipVerification;
}
```

### 4. แก้ไขไฟล์ server/api/slip-api.ts

ส่วนของการตรวจสอบสลิปซ้ำจะถูกแก้ไขให้ตรวจสอบใน Redis ก่อน

```typescript
// ในส่วนของการตรวจสอบสลิปซ้ำ
if (detectResult.hasQRCode && qrData) {
  // ตรวจสอบการตั้งค่าตรวจสอบสลิปซ้ำของ API key
  const apiKeyRecord = req.apiKey;
  if (apiKeyRecord?.duplicateSlipCheck) {
    console.log(`[API] API key ถูกตั้งค่าให้ตรวจสอบสลิปซ้ำ`);
    
    // ตรวจสอบใน Redis cache ก่อน
    const cachedVerifications = await getCachedQRCodeVerification(req.apiUser?.id || 0, qrData);
    if (cachedVerifications) {
      console.log(`[CACHE HIT] พบข้อมูลสลิปซ้ำใน Redis cache`);
      
      const existingVerifications = Array.isArray(cachedVerifications) ? cachedVerifications : [cachedVerifications];
      if (existingVerifications.length > 0) {
        console.log(`[API] พบสลิปซ้ำจากข้อมูล QR code (จาก Redis cache)`);
        
        const existingVerification = existingVerifications[0];
        const formattedDate = new Date(existingVerification.createdAt).toLocaleString('th-TH');
        
        const responseTime = Date.now() - startTime;
        await logApiUsage(req, res, {
          apiKeyId: req.apiKey?.id || 0,
          requestType: apiRequestTypeEnum.enumValues[0],
          requestData: req.path,
          responseStatus: 'error',
          responseStatusCode: 409,
          responseTime,
          errorMessage: 'Duplicate slip detected (from cache)'
        });
        
        return res.status(409).json({
          code: "409000",
          message: `สลิปฉบับนี้ถูกตรวจสอบไปแล้วเมื่อ ${formattedDate}`,
          data: {
            previousVerification: {
              id: existingVerification.id,
              verifiedAt: existingVerification.createdAt,
              amount: existingVerification.amount,
              status: existingVerification.status
            }
          }
        });
      }
    }
    
    // ถ้าไม่พบใน cache ให้ค้นหาในฐานข้อมูล
    console.log(`[CACHE MISS] ไม่พบข้อมูลใน Redis cache สำหรับ QR: ${qrData.substring(0, 20)}... ค้นหาในฐานข้อมูล`);
    const existingVerifications = await storage.findDuplicateSlipByQRData(
      req.apiUser?.id || 0, 
      qrData
    );
    
    if (existingVerifications && existingVerifications.length > 0) {
      console.log(`[API] พบสลิปซ้ำจากข้อมูล QR code (จากฐานข้อมูล)`);
      
      const existingVerification = existingVerifications[0];
      const formattedDate = new Date(existingVerification.createdAt).toLocaleString('th-TH');
      
      const responseTime = Date.now() - startTime;
      await logApiUsage(req, res, {
        apiKeyId: req.apiKey?.id || 0,
        requestType: apiRequestTypeEnum.enumValues[0],
        requestData: req.path,
        responseStatus: 'error',
        responseStatusCode: 409,
        responseTime,
        errorMessage: 'Duplicate slip detected'
      });
      
      return res.status(409).json({
        code: "409000",
        message: `สลิปฉบับนี้ถูกตรวจสอบไปแล้วเมื่อ ${formattedDate}`,
        data: {
          previousVerification: {
            id: existingVerification.id,
            verifiedAt: existingVerification.createdAt,
            amount: existingVerification.amount,
            status: existingVerification.status
          }
        }
      });
    }
  } else {
    console.log(`[API] ผู้ใช้ปิดการตรวจสอบสลิปซ้ำ`);
  }
}
```

### 5. แก้ไขไฟล์ package.json

เพิ่ม dependency ของ Redis

```json
{
  "dependencies": {
    // ... dependencies อื่นๆ
    "ioredis": "^5.3.2"
  }
}
```

### 6. แก้ไขไฟล์ .env

เพิ่มการตั้งค่า Redis

```
# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
```

## ประโยชน์ที่จะได้รับ

1. **ลดการเข้าถึงฐานข้อมูล**: เมื่อมีการส่ง request ซ้ำๆ ระบบจะดึงข้อมูลจาก Redis แทนการเข้าถึงฐานข้อมูล PostgreSQL ทุกครั้ง

2. **เพิ่มประสิทธิภาพ**: Redis เป็น in-memory database ที่มีความเร็วสูง ทำให้การตรวจสอบข้อมูลทำได้เร็วกว่าการเข้าถึงฐานข้อมูล PostgreSQL

3. **ลดการทำงานของ CPU**: ลดการประมวลผล SQL queries และการเข้าถึงดิสก์ ทำให้ CPU ทำงานน้อยลง

4. **รองรับการขยายตัว**: สามารถรองรับการขยายตัวของระบบได้ดีขึ้น เมื่อมีผู้ใช้งานมากขึ้น

## ข้อควรระวัง

1. **ความสอดคล้องของข้อมูล**: ต้องมั่นใจว่าข้อมูลใน Redis และฐานข้อมูลมีความสอดคล้องกัน โดยการตั้งค่าเวลาหมดอายุที่เหมาะสม

2. **การจัดการหน่วยความจำ**: ต้องมีการจัดการหน่วยความจำของ Redis อย่างเหมาะสม เพื่อไม่ให้ใช้หน่วยความจำมากเกินไป

3. **การสำรองข้อมูล**: Redis เป็น in-memory database ดังนั้นต้องมีการสำรองข้อมูลอย่างเหมาะสม

4. **การจัดการข้อผิดพลาด**: ต้องมีการจัดการข้อผิดพลาดที่อาจเกิดขึ้นกับ Redis เช่น การเชื่อมต่อล้มเหลว

## ขั้นตอนการติดตั้ง Redis

1. **ติดตั้ง Redis บนเซิร์ฟเวอร์**:
   ```bash
   sudo apt update
   sudo apt install redis-server
   ```

2. **ตั้งค่า Redis ให้เริ่มต้นอัตโนมัติ**:
   ```bash
   sudo systemctl enable redis-server
   ```

3. **ตรวจสอบสถานะ Redis**:
   ```bash
   sudo systemctl status redis-server
   ```

4. **ทดสอบการเชื่อมต่อ Redis**:
   ```bash
   redis-cli ping
   ```
   ถ้าตอบกลับ "PONG" แสดงว่า Redis ทำงานปกติ

5. **ติดตั้ง Redis client สำหรับ Node.js**:
   ```bash
   npm install ioredis
   ```

## การทดสอบประสิทธิภาพ

หลังจากติดตั้งและใช้งาน Redis แล้ว ควรทำการทดสอบประสิทธิภาพเพื่อเปรียบเทียบก่อนและหลังการใช้งาน Redis โดยทดสอบในสถานการณ์ต่อไปนี้:

1. **ทดสอบการส่ง request ซ้ำๆ**: ส่ง request เดียวกันซ้ำๆ หลายครั้ง และวัดเวลาตอบสนอง

2. **ทดสอบการทำงานของ CPU**: ตรวจสอบการใช้งาน CPU ก่อนและหลังการใช้งาน Redis

3. **ทดสอบการเข้าถึงฐานข้อมูล**: ตรวจสอบจำนวนการเข้าถึงฐานข้อมูลก่อนและหลังการใช้งาน Redis

## สรุป

การใช้ Redis เป็น cache สำหรับการตรวจสอบ QR code ที่เคยตรวจสอบไปแล้ว จะช่วยลดการทำงานของ CPU และการเข้าถึงฐานข้อมูล ทำให้ระบบมีประสิทธิภาพมากขึ้น โดยเฉพาะในกรณีที่มีการส่ง request ซ้ำๆ จำนวนมาก
