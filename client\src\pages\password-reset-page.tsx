import { useState } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import axios from "axios";
import { useLocation } from "wouter";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import { Loader2, Mail, Phone, User, Lock, ArrowLeft, Check } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { GodlyNavbar } from "@/components/layouts/godly-navbar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON>ertDescription, AlertTitle } from "@/components/ui/alert";

// กำหนด Schema สำหรับขอรีเซ็ตรหัสผ่าน
const requestResetSchema = z.object({
  identifier: z.string().min(3, "กรุณากรอกชื่อผู้ใช้ อีเมล หรือเบอร์โทรศัพท์"),
  method: z.enum(["email", "phone"])
});

// กำหนด Schema สำหรับยืนยันรหัส
const verifyCodeSchema = z.object({
  code: z.string().length(6, "รหัสยืนยันต้องมี 6 หลัก")
});

// กำหนด Schema สำหรับตั้งรหัสผ่านใหม่
const resetPasswordSchema = z.object({
  newPassword: z.string().min(8, "รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร"),
  confirmPassword: z.string()
}).refine(data => data.newPassword === data.confirmPassword, {
  message: "รหัสผ่านไม่ตรงกัน",
  path: ["confirmPassword"]
});

// กำหนด Types
type RequestResetFormValues = z.infer<typeof requestResetSchema>;
type VerifyCodeFormValues = z.infer<typeof verifyCodeSchema>;
type ResetPasswordFormValues = z.infer<typeof resetPasswordSchema>;

export default function PasswordResetPage() {
  const [, navigate] = useLocation();
  
  // สถานะการรีเซ็ตรหัสผ่าน (request -> verify -> reset)
  const [resetStep, setResetStep] = useState<"request" | "verify" | "reset">("request");
  
  // ข้อมูลผู้ใช้และการส่งรหัสยืนยัน
  const [userData, setUserData] = useState<{
    identifier: string;
    method: "email" | "phone";
  } | null>(null);
  
  // รหัสยืนยันที่ผู้ใช้กรอก
  const [verificationCode, setVerificationCode] = useState("");
  
  // สถานะการโหลด
  const [isRequestingReset, setIsRequestingReset] = useState(false);
  const [isVerifyingCode, setIsVerifyingCode] = useState(false);
  const [isResettingPassword, setIsResettingPassword] = useState(false);
  
  // สถานะการนับถอยหลัง (ป้องกันการส่งรหัสยืนยันบ่อยเกินไป)
  const [cooldown, setCooldown] = useState(0);
  
  // Forms
  const requestResetForm = useForm<RequestResetFormValues>({
    resolver: zodResolver(requestResetSchema),
    defaultValues: {
      identifier: "",
      method: "email"
    }
  });
  
  const verifyCodeForm = useForm<VerifyCodeFormValues>({
    resolver: zodResolver(verifyCodeSchema),
    defaultValues: {
      code: ""
    }
  });
  
  const resetPasswordForm = useForm<ResetPasswordFormValues>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      newPassword: "",
      confirmPassword: ""
    }
  });
  
  // ขอรีเซ็ตรหัสผ่าน (ส่งรหัสยืนยัน)
  const onRequestReset = async (values: RequestResetFormValues) => {
    try {
      setIsRequestingReset(true);
      
      // เรียก API เพื่อส่งรหัสยืนยัน
      const response = await axios.post("/api/password-reset/request", {
        identifier: values.identifier,
        method: values.method
      });
      
      if (response.data.success) {
        toast({
          title: "ส่งรหัสยืนยันแล้ว",
          description: response.data.message,
        });
        
        // เก็บข้อมูลสำหรับขั้นตอนต่อไป
        setUserData({
          identifier: values.identifier,
          method: values.method
        });
        
        // เริ่มตัวนับถอยหลัง
        setCooldown(60);
        const countdownInterval = setInterval(() => {
          setCooldown(prev => {
            if (prev <= 1) {
              clearInterval(countdownInterval);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
        
        // ไปยังขั้นตอนยืนยันรหัส
        setResetStep("verify");
      } else {
        toast({
          variant: "destructive",
          title: "ไม่สามารถส่งรหัสยืนยันได้",
          description: response.data.message || "โปรดลองอีกครั้งในภายหลัง",
        });
      }
    } catch (error: any) {
      console.error("Request reset error:", error);
      toast({
        variant: "destructive",
        title: "ไม่สามารถส่งรหัสยืนยันได้",
        description: error.response?.data?.message || "เกิดข้อผิดพลาด โปรดลองใหม่อีกครั้ง",
      });
    } finally {
      setIsRequestingReset(false);
    }
  };
  
  // ส่งรหัสยืนยันใหม่
  const resendVerificationCode = async () => {
    if (!userData || cooldown > 0) return;
    
    try {
      setIsRequestingReset(true);
      
      // เรียก API เพื่อส่งรหัสยืนยันอีกครั้ง
      const response = await axios.post("/api/password-reset/request", {
        identifier: userData.identifier,
        method: userData.method
      });
      
      if (response.data.success) {
        toast({
          title: "ส่งรหัสยืนยันใหม่แล้ว",
          description: response.data.message,
        });
        
        // เริ่มตัวนับถอยหลังใหม่
        setCooldown(60);
        const countdownInterval = setInterval(() => {
          setCooldown(prev => {
            if (prev <= 1) {
              clearInterval(countdownInterval);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        toast({
          variant: "destructive",
          title: "ไม่สามารถส่งรหัสยืนยันได้",
          description: response.data.message || "โปรดลองอีกครั้งในภายหลัง",
        });
      }
    } catch (error: any) {
      console.error("Resend verification code error:", error);
      toast({
        variant: "destructive",
        title: "ไม่สามารถส่งรหัสยืนยันได้",
        description: error.response?.data?.message || "เกิดข้อผิดพลาด โปรดลองใหม่อีกครั้ง",
      });
    } finally {
      setIsRequestingReset(false);
    }
  };
  
  // ยืนยันรหัส
  const onVerifyCode = async (values: VerifyCodeFormValues) => {
    if (!userData) return;
    
    try {
      setIsVerifyingCode(true);
      setVerificationCode(values.code);
      
      // เรียก API เพื่อตรวจสอบรหัสยืนยัน
      const response = await axios.post("/api/password-reset/verify-code", {
        identifier: userData.identifier,
        code: values.code
      });
      
      if (response.data.success) {
        toast({
          title: "ยืนยันรหัสสำเร็จ",
          description: "คุณสามารถตั้งรหัสผ่านใหม่ได้",
        });
        
        // ไปยังขั้นตอนตั้งรหัสผ่านใหม่
        setResetStep("reset");
      } else {
        toast({
          variant: "destructive",
          title: "ยืนยันรหัสไม่สำเร็จ",
          description: response.data.message || "รหัสยืนยันไม่ถูกต้องหรือหมดอายุแล้ว",
        });
      }
    } catch (error: any) {
      console.error("Verify code error:", error);
      toast({
        variant: "destructive",
        title: "ยืนยันรหัสไม่สำเร็จ",
        description: error.response?.data?.message || "เกิดข้อผิดพลาด โปรดลองใหม่อีกครั้ง",
      });
    } finally {
      setIsVerifyingCode(false);
    }
  };
  
  // ตั้งรหัสผ่านใหม่
  const onResetPassword = async (values: ResetPasswordFormValues) => {
    if (!userData || !verificationCode) return;
    
    try {
      setIsResettingPassword(true);
      
      // เรียก API เพื่อตั้งรหัสผ่านใหม่
      const response = await axios.post("/api/password-reset/reset", {
        identifier: userData.identifier,
        code: verificationCode,
        newPassword: values.newPassword,
        confirmPassword: values.confirmPassword
      });
      
      if (response.data.success) {
        toast({
          title: "รีเซ็ตรหัสผ่านสำเร็จ",
          description: "คุณสามารถใช้รหัสผ่านใหม่ในการเข้าสู่ระบบได้แล้ว",
        });
        
        // กลับไปหน้าเข้าสู่ระบบ
        setTimeout(() => {
          navigate("/auth");
        }, 1500);
      } else {
        toast({
          variant: "destructive",
          title: "ไม่สามารถรีเซ็ตรหัสผ่านได้",
          description: response.data.message || "กรุณาลองใหม่อีกครั้ง",
        });
      }
    } catch (error: any) {
      console.error("Reset password error:", error);
      toast({
        variant: "destructive",
        title: "ไม่สามารถรีเซ็ตรหัสผ่านได้",
        description: error.response?.data?.message || "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง",
      });
    } finally {
      setIsResettingPassword(false);
    }
  };
  
  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-purple-950 to-purple-900 bg-[url('/images/stars-bg.png')] bg-repeat">
      
      <div className="flex-1 py-10">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="max-w-md mx-auto">
            <Card className="bg-purple-950/50 border-purple-800 backdrop-blur-sm shadow-2xl">
              <CardHeader className="space-y-1">
                <CardTitle className="text-3xl font-bold bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 bg-clip-text text-transparent">
                  {resetStep === "request" && "ลืมรหัสผ่าน"}
                  {resetStep === "verify" && "ยืนยันรหัส"}
                  {resetStep === "reset" && "ตั้งรหัสผ่านใหม่"}
                </CardTitle>
                <CardDescription className="text-purple-300">
                  {resetStep === "request" && "กรอกข้อมูลเพื่อรีเซ็ตรหัสผ่านของคุณ"}
                  {resetStep === "verify" && "กรอกรหัสยืนยันที่ส่งไปยัง" + (userData?.method === "email" ? "อีเมล" : "เบอร์โทรศัพท์") + "ของคุณ"}
                  {resetStep === "reset" && "กรอกรหัสผ่านใหม่ของคุณ"}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* ขั้นตอนที่ 1: ขอรีเซ็ตรหัสผ่าน */}
                {resetStep === "request" && (
                  <Form {...requestResetForm}>
                    <form onSubmit={requestResetForm.handleSubmit(onRequestReset)} className="space-y-4">
                      <FormField
                        control={requestResetForm.control}
                        name="identifier"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-white">ชื่อผู้ใช้ อีเมล หรือเบอร์โทรศัพท์</FormLabel>
                            <FormControl>
                              <div className="relative">
                                <User className="absolute left-3 top-3 h-5 w-5 text-purple-400" />
                                <Input
                                  placeholder="กรอกชื่อผู้ใช้ อีเมล หรือเบอร์โทรศัพท์"
                                  {...field}
                                  className="pl-10 bg-purple-900/30 border-purple-700 text-white"
                                />
                              </div>
                            </FormControl>
                            <FormMessage className="text-red-300" />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={requestResetForm.control}
                        name="method"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-white">วิธีการยืนยัน</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger className="bg-purple-900/30 border-purple-700 text-white">
                                  <SelectValue placeholder="เลือกวิธีการยืนยัน" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent className="bg-purple-900 border-purple-700 text-white">
                                <SelectItem value="email">ยืนยันด้วยอีเมล</SelectItem>
                                <SelectItem value="phone">ยืนยันด้วย SMS</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormDescription className="text-purple-300">
                              เราจะส่งรหัสยืนยันไปยัง{field.value === "email" ? "อีเมล" : "เบอร์โทรศัพท์"}ของคุณ
                            </FormDescription>
                            <FormMessage className="text-red-300" />
                          </FormItem>
                        )}
                      />
                      
                      <Button 
                        type="submit"
                        className="w-full mt-4 bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 font-semibold"
                        disabled={isRequestingReset}
                      >
                        {isRequestingReset ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            กำลังส่งรหัสยืนยัน...
                          </>
                        ) : (
                          "ขอรหัสยืนยัน"
                        )}
                      </Button>
                      
                      <div className="text-sm text-center mt-4 text-purple-300">
                        <button 
                          type="button" 
                          onClick={() => navigate("/auth")}
                          className="text-pink-400 hover:underline"
                        >
                          <ArrowLeft className="h-4 w-4 inline mr-1" />
                          กลับไปหน้าเข้าสู่ระบบ
                        </button>
                      </div>
                    </form>
                  </Form>
                )}
                
                {/* ขั้นตอนที่ 2: ยืนยันรหัส */}
                {resetStep === "verify" && userData && (
                  <div className="space-y-4">
                    <div className="bg-purple-900/50 p-4 rounded-lg border border-purple-700 mb-6">
                      <p className="text-purple-200 text-center mb-2">
                        เราได้ส่งรหัสยืนยันไปยัง{userData.method === "email" ? "อีเมล" : "เบอร์โทรศัพท์"}ของคุณแล้ว
                      </p>
                      <p className="text-pink-300 text-center font-medium">
                        กรุณาตรวจสอบและกรอกรหัสยืนยัน 6 หลัก
                      </p>
                    </div>
                    
                    <Form {...verifyCodeForm}>
                      <form onSubmit={verifyCodeForm.handleSubmit(onVerifyCode)} className="space-y-4">
                        <FormField
                          control={verifyCodeForm.control}
                          name="code"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-white">รหัสยืนยัน</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="กรอกรหัสยืนยัน 6 หลัก"
                                  {...field}
                                  className="bg-purple-900/30 border-purple-700 text-white text-center text-xl tracking-widest"
                                  maxLength={6}
                                />
                              </FormControl>
                              <FormMessage className="text-red-300" />
                            </FormItem>
                          )}
                        />
                        
                        <Button 
                          type="submit"
                          className="w-full mt-4 bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 font-semibold"
                          disabled={isVerifyingCode}
                        >
                          {isVerifyingCode ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              กำลังตรวจสอบ...
                            </>
                          ) : (
                            "ยืนยันรหัส"
                          )}
                        </Button>
                        
                        <div className="text-sm text-center mt-4 text-purple-300">
                          <p>ไม่ได้รับรหัส? {
                            cooldown > 0 
                              ? `กรุณารอ ${cooldown} วินาที` 
                              : (
                                <button 
                                  type="button" 
                                  onClick={resendVerificationCode}
                                  className="text-pink-400 hover:underline"
                                  disabled={isRequestingReset}
                                >
                                  ส่งรหัสใหม่
                                </button>
                              )
                          }</p>
                        </div>
                        
                        <div className="text-sm text-center mt-4 text-purple-300">
                          <button 
                            type="button" 
                            onClick={() => setResetStep("request")}
                            className="text-pink-400 hover:underline"
                          >
                            <ArrowLeft className="h-4 w-4 inline mr-1" />
                            กลับไปขั้นตอนก่อนหน้า
                          </button>
                        </div>
                      </form>
                    </Form>
                  </div>
                )}
                
                {/* ขั้นตอนที่ 3: ตั้งรหัสผ่านใหม่ */}
                {resetStep === "reset" && userData && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="font-medium text-white">รหัสยืนยัน</div>
                        <Badge variant="outline" className="bg-green-900/50 text-green-300 border-green-500">
                          <Check className="h-3 w-3 mr-1" /> ยืนยันแล้ว
                        </Badge>
                      </div>
                      <div className="text-purple-300 text-sm">
                        รหัสยืนยันถูกต้อง ท่านสามารถตั้งรหัสผ่านใหม่ได้
                      </div>
                    </div>
                    
                    <Form {...resetPasswordForm}>
                      <form onSubmit={resetPasswordForm.handleSubmit(onResetPassword)} className="space-y-4">
                        <FormField
                          control={resetPasswordForm.control}
                          name="newPassword"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-white">รหัสผ่านใหม่ <span className="text-red-400">*</span></FormLabel>
                              <FormControl>
                                <div className="relative">
                                  <Lock className="absolute left-3 top-3 h-5 w-5 text-purple-400" />
                                  <Input
                                    type="password"
                                    placeholder="กรอกรหัสผ่านใหม่ (อย่างน้อย 8 ตัวอักษร)"
                                    {...field}
                                    className="pl-10 bg-purple-900/30 border-purple-700 text-white"
                                  />
                                </div>
                              </FormControl>
                              <FormMessage className="text-red-300" />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={resetPasswordForm.control}
                          name="confirmPassword"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-white">ยืนยันรหัสผ่านใหม่ <span className="text-red-400">*</span></FormLabel>
                              <FormControl>
                                <div className="relative">
                                  <Lock className="absolute left-3 top-3 h-5 w-5 text-purple-400" />
                                  <Input
                                    type="password"
                                    placeholder="กรอกรหัสผ่านใหม่อีกครั้ง"
                                    {...field}
                                    className="pl-10 bg-purple-900/30 border-purple-700 text-white"
                                  />
                                </div>
                              </FormControl>
                              <FormMessage className="text-red-300" />
                            </FormItem>
                          )}
                        />
                        
                        <Button 
                          type="submit"
                          className="w-full mt-4 bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 font-semibold"
                          disabled={isResettingPassword}
                        >
                          {isResettingPassword ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              กำลังบันทึกรหัสผ่านใหม่...
                            </>
                          ) : (
                            "บันทึกรหัสผ่านใหม่"
                          )}
                        </Button>
                        
                        <div className="text-sm text-center mt-4 text-purple-300">
                          <button 
                            type="button" 
                            onClick={() => setResetStep("verify")}
                            className="text-pink-400 hover:underline"
                          >
                            <ArrowLeft className="h-4 w-4 inline mr-1" />
                            กลับไปขั้นตอนยืนยันรหัส
                          </button>
                        </div>
                      </form>
                    </Form>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}