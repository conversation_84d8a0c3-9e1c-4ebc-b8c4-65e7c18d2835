import { Express, Request, Response } from "express";
import { storage } from "../storage";

export function setupAdminUserDetailsRoutes(app: Express) {
  // ฟังก์ชันตรวจสอบว่าผู้ใช้เป็นแอดมินหรือไม่
  function isAdmin(req: any, res: any, next: any) {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "ไม่ได้รับอนุญาต กรุณาเข้าสู่ระบบ" });
    }

    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: "ไม่มีสิทธิ์เข้าถึง เฉพาะผู้ดูแลระบบเท่านั้น" });
    }

    next();
  }
  
  // เพิ่มเครดิตให้ผู้ใช้
  app.post('/api/admin/users/:id/credit', isAdmin, async (req, res) => {
    try {
      const userId = parseInt(req.params.id);
      const { amount, description } = req.body;
      
      if (isNaN(userId)) {
        return res.status(400).json({ message: "รหัสผู้ใช้ไม่ถูกต้อง" });
      }
      
      // แปลงค่าเป็น float และปัดเศษเป็นทศนิยม 2 ตำแหน่ง
      const creditAmount = parseFloat(parseFloat(amount).toFixed(2));
      
      if (isNaN(creditAmount) || creditAmount <= 0) {
        return res.status(400).json({ message: "จำนวนเครดิตไม่ถูกต้อง" });
      }
      
      // ตรวจสอบว่ามีผู้ใช้คนนี้หรือไม่
      const user = await storage.getUser(userId);
      
      if (!user) {
        return res.status(404).json({ message: "ไม่พบผู้ใช้งาน" });
      }
      
      // เพิ่มเครดิต
      await storage.addCredits(userId, creditAmount, 'admin', description || 'เติมเครดิตโดยผู้ดูแลระบบ');
      
      // ส่งข้อมูลอัพเดตผ่าน WebSocket
      if (typeof global.broadcastWebsocketEvent === 'function') {
        try {
          const updatedUser = await storage.getUser(userId);
          global.broadcastWebsocketEvent('dashboard', 'credit_updated', {
            userId: userId,
            credit: updatedUser?.credit || 0,
            updateType: 'add',
            amount: creditAmount,
            description: description || 'เติมเครดิตโดยผู้ดูแลระบบ'
          });
          console.log(`ส่งข้อมูลอัพเดตเครดิตผ่าน WebSocket สำหรับผู้ใช้ ${userId}`);
          
          // ทดสอบส่ง package_updated event
          global.broadcastWebsocketEvent('dashboard', 'package_updated', {
            userId: userId,
            package: {
              id: 1,
              name: 'แพ็คเกจทดสอบ',
              dailyLimit: 100,
              usedToday: 28
            },
            action: 'update'
          });
          console.log(`ส่งข้อมูลอัพเดตแพ็คเกจผ่าน WebSocket สำหรับผู้ใช้ ${userId}`);
        } catch (error) {
          console.error('เกิดข้อผิดพลาดในการส่งข้อมูล WebSocket:', error);
        }
      }
      
      res.status(200).json({ message: "เพิ่มเครดิตสำเร็จ" });
    } catch (error) {
      console.error('Error adding credit:', error);
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการเพิ่มเครดิต" });
    }
  });
  
  // หักเครดิตจากผู้ใช้
  app.post('/api/admin/users/:id/deduct-credit', isAdmin, async (req, res) => {
    try {
      const userId = parseInt(req.params.id);
      const { amount, description } = req.body;
      
      if (isNaN(userId)) {
        return res.status(400).json({ message: "รหัสผู้ใช้ไม่ถูกต้อง" });
      }
      
      // แปลงค่าเป็น float และปัดเศษเป็นทศนิยม 2 ตำแหน่ง
      const creditAmount = parseFloat(parseFloat(amount).toFixed(2));
      
      if (isNaN(creditAmount) || creditAmount <= 0) {
        return res.status(400).json({ message: "จำนวนเครดิตไม่ถูกต้อง" });
      }
      
      // ตรวจสอบว่ามีผู้ใช้คนนี้หรือไม่
      const user = await storage.getUser(userId);
      
      if (!user) {
        return res.status(404).json({ message: "ไม่พบผู้ใช้งาน" });
      }
      
      // ตรวจสอบว่ามีเครดิตเพียงพอหรือไม่
      if (user.credit < creditAmount) {
        return res.status(400).json({ message: "เครดิตไม่เพียงพอ" });
      }
      
      // หักเครดิต
      await storage.deductCredits(userId, creditAmount, 'admin', description || 'หักเครดิตโดยผู้ดูแลระบบ');
      
      // ส่งข้อมูลอัพเดตผ่าน WebSocket
      if (typeof global.broadcastWebsocketEvent === 'function') {
        try {
          const updatedUser = await storage.getUser(userId);
          global.broadcastWebsocketEvent('dashboard', 'credit_updated', {
            userId: userId,
            credit: updatedUser?.credit || 0,
            updateType: 'deduct',
            amount: creditAmount,
            description: description || 'หักเครดิตโดยผู้ดูแลระบบ'
          });
          console.log(`ส่งข้อมูลหักเครดิตผ่าน WebSocket สำหรับผู้ใช้ ${userId}`);
        } catch (error) {
          console.error('เกิดข้อผิดพลาดในการส่งข้อมูล WebSocket:', error);
        }
      }
      
      res.status(200).json({ message: "หักเครดิตสำเร็จ" });
    } catch (error) {
      console.error('Error deducting credit:', error);
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการหักเครดิต" });
    }
  });
  
  // เปลี่ยนรหัสผ่านผู้ใช้
  app.patch('/api/admin/users/:id/password', isAdmin, async (req, res) => {
    try {
      const userId = parseInt(req.params.id);
      const { newPassword } = req.body;
      
      if (isNaN(userId)) {
        return res.status(400).json({ message: "รหัสผู้ใช้ไม่ถูกต้อง" });
      }
      
      if (!newPassword || newPassword.length < 6) {
        return res.status(400).json({ message: "รหัสผ่านไม่ถูกต้อง ต้องมีความยาวอย่างน้อย 6 ตัวอักษร" });
      }
      
      // ตรวจสอบว่ามีผู้ใช้คนนี้หรือไม่
      const user = await storage.getUser(userId);
      
      if (!user) {
        return res.status(404).json({ message: "ไม่พบผู้ใช้งาน" });
      }
      
      // เปลี่ยนรหัสผ่าน
      const updatedUser = await storage.changeUserPassword(userId, newPassword);
      
      if (!updatedUser) {
        return res.status(500).json({ message: "เกิดข้อผิดพลาดในการเปลี่ยนรหัสผ่าน" });
      }
      
      res.status(200).json({ message: "เปลี่ยนรหัสผ่านสำเร็จ" });
    } catch (error) {
      console.error('Error changing password:', error);
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการเปลี่ยนรหัสผ่าน" });
    }
  });

  // ดึงข้อมูลผู้ใช้
  app.get('/api/admin/users/:id', isAdmin, async (req, res) => {
    try {
      const userId = parseInt(req.params.id);
      const user = await storage.getUser(userId);
      
      if (!user) {
        return res.status(404).json({ message: "ไม่พบผู้ใช้งาน" });
      }
      
      res.json(user);
    } catch (error) {
      console.error('Error fetching user:', error);
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการดึงข้อมูลผู้ใช้" });
    }
  });

  // ดึงข้อมูลสถิติของผู้ใช้
  app.get('/api/admin/users/:id/stats', isAdmin, async (req, res) => {
    try {
      const userId = parseInt(req.params.id);
      
      // ตรวจสอบว่ามีผู้ใช้คนนี้หรือไม่
      const user = await storage.getUser(userId);
      
      if (!user) {
        return res.status(404).json({ message: "ไม่พบผู้ใช้งาน" });
      }
      
      // ดึงข้อมูลสถิติการใช้งานของผู้ใช้
      const userStats = await storage.getUserStats(userId);
      
      // ดึงข้อมูลสถิติการใช้งาน API
      const apiUsageStats = await storage.getApiUsageStats(userId);
      
      // ดึงข้อมูลสถิติเครดิต
      const creditBalance = await storage.getUserCredit(userId);
      
      res.json({
        ...userStats,
        creditBalance,
        apiUsage: apiUsageStats
      });
    } catch (error) {
      console.error('Error fetching user stats:', error);
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการดึงข้อมูลสถิติของผู้ใช้" });
    }
  });
  
  // ดึงข้อมูลการตรวจสอบสลิปของผู้ใช้
  app.get('/api/admin/users/:id/verifications', isAdmin, async (req, res) => {
    try {
      const userId = parseInt(req.params.id);
      
      // ตรวจสอบว่ามีผู้ใช้คนนี้หรือไม่
      const user = await storage.getUser(userId);
      
      if (!user) {
        return res.status(404).json({ message: "ไม่พบผู้ใช้งาน" });
      }
      
      // ดึงข้อมูลการตรวจสอบสลิปของผู้ใช้
      const verifications = await storage.listUserSlipVerifications(userId);
      
      res.json(verifications);
    } catch (error) {
      console.error('Error fetching user verifications:', error);
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการดึงข้อมูลการตรวจสอบสลิป" });
    }
  });
  
  // ดึงข้อมูลการเติมเงินของผู้ใช้
  app.get('/api/admin/users/:id/transactions', isAdmin, async (req, res) => {
    try {
      const userId = parseInt(req.params.id);
      
      // ตรวจสอบว่ามีผู้ใช้คนนี้หรือไม่
      const user = await storage.getUser(userId);
      
      if (!user) {
        return res.status(404).json({ message: "ไม่พบผู้ใช้งาน" });
      }
      
      // ดึงข้อมูลการเติมเงินของผู้ใช้
      const transactions = await storage.listUserTopUpTransactions(userId);
      
      res.json(transactions);
    } catch (error) {
      console.error('Error fetching user transactions:', error);
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการดึงข้อมูลการเติมเงิน" });
    }
  });
  
  // ดึงข้อมูล API Keys ของผู้ใช้
  app.get('/api/admin/users/:id/api-keys', isAdmin, async (req, res) => {
    try {
      const userId = parseInt(req.params.id);
      
      // ตรวจสอบว่ามีผู้ใช้คนนี้หรือไม่
      const user = await storage.getUser(userId);
      
      if (!user) {
        return res.status(404).json({ message: "ไม่พบผู้ใช้งาน" });
      }
      
      // ดึงข้อมูล API Keys ของผู้ใช้
      const apiKeys = await storage.listUserApiKeys(userId);
      
      res.json(apiKeys);
    } catch (error) {
      console.error('Error fetching user API keys:', error);
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการดึงข้อมูล API Keys" });
    }
  });
  
  // ดึงข้อมูลแพ็กเกจของผู้ใช้
  app.get('/api/admin/users/:id/packages', isAdmin, async (req, res) => {
    try {
      const userId = parseInt(req.params.id);
      
      // ตรวจสอบว่ามีผู้ใช้คนนี้หรือไม่
      const user = await storage.getUser(userId);
      
      if (!user) {
        return res.status(404).json({ message: "ไม่พบผู้ใช้งาน" });
      }
      
      // ดึงข้อมูลแพ็กเกจของผู้ใช้
      const userPackages = await storage.listUserPackages(userId);
      
      res.json(userPackages);
    } catch (error) {
      console.error('Error fetching user packages:', error);
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการดึงข้อมูลแพ็กเกจ" });
    }
  });
}