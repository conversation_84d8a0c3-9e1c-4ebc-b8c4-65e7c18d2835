<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Admin Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #results {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 SLIPKUY Admin Page Test</h1>
        
        <div class="test-section info">
            <h3>📊 Page Load Status</h3>
            <p>✅ HTML loaded successfully</p>
            <p>✅ CSS styles applied</p>
            <p id="js-status">⏳ Testing JavaScript...</p>
        </div>

        <div class="test-section">
            <h3>🌐 API Tests</h3>
            <button onclick="testHealthAPI()">Test Health API</button>
            <button onclick="testLoginStatus()">Test Login Status</button>
            <button onclick="testStaticFiles()">Test Static Files</button>
        </div>

        <div class="test-section">
            <h3>🔗 Navigation Tests</h3>
            <button onclick="window.location.href='/admin'">Go to Real Admin</button>
            <button onclick="window.location.href='/'">Go to Home</button>
            <button onclick="window.location.href='/api/health'">Test API Health</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        // Test JavaScript execution
        document.getElementById('js-status').innerHTML = '✅ JavaScript working!';
        document.getElementById('js-status').className = 'success';

        function addResult(title, content, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-section ${type}`;
            div.innerHTML = `<h4>${title}</h4><pre>${content}</pre>`;
            results.appendChild(div);
        }

        async function testHealthAPI() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                addResult('Health API Test', JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                addResult('Health API Test', `Error: ${error.message}`, 'error');
            }
        }

        async function testLoginStatus() {
            try {
                const response = await fetch('/api/login-status');
                const data = await response.json();
                addResult('Login Status Test', JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                addResult('Login Status Test', `Error: ${error.message}`, 'error');
            }
        }

        async function testStaticFiles() {
            const files = [
                '/assets/index-D9OQya04.js',
                '/assets/index-CsjMBqAh.css'
            ];
            
            for (const file of files) {
                try {
                    const response = await fetch(file, { method: 'HEAD' });
                    addResult(`Static File Test: ${file}`, 
                        `Status: ${response.status}\nContent-Type: ${response.headers.get('content-type')}`, 
                        response.ok ? 'success' : 'error');
                } catch (error) {
                    addResult(`Static File Test: ${file}`, `Error: ${error.message}`, 'error');
                }
            }
        }

        // Auto-run basic tests
        setTimeout(() => {
            testHealthAPI();
            testStaticFiles();
        }, 1000);
    </script>
</body>
</html>
