# API Keys
EASYSLIP_API_KEY=db34c04e-9465-4b60-8bb6-5cdb3faa1973
SLIP2GO_API_KEY=RNWeLFE7aTalOXURHjYghkNG0JOFS7RrQXWf93ZBVFc=

# Security
SESSION_SECRET=slipkuy_session_secret

# Email Settings
EMAIL_USER=<EMAIL>
EMAIL_PASS=kddsrlmxivizcjsz
EMAIL_FROM=SLIPKUY Alert <<EMAIL>>

# Database Settings (Neon PostgreSQL)
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require
PGUSER=neondb_owner
PGPASSWORD=npg_A2RLyF1EoZDi
PGDATABASE=neondb
PGHOST=ep-broad-lab-a11dv8em-pooler.ap-southeast-1.aws.neon.tech
PGPORT=5432
PGSSLMODE=require
