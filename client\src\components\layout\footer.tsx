import { <PERSON> } from "wouter";
import { ReceiptIcon } from "lucide-react";
import { FaFacebook, FaTwitter, FaLinkedin } from "react-icons/fa";

export default function Footer() {
  return (
    <footer className="bg-neutral-800 text-neutral-200 py-12">
      <div className="container mx-auto px-4 md:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="col-span-1 md:col-span-1">
            <div className="text-2xl font-bold mb-4">
              <ReceiptIcon className="inline-block mr-2 text-secondary-500 h-6 w-6" />
              <span className="text-white">Verify</span>
              <span className="text-secondary-400">Slip</span>
            </div>
            <p className="text-neutral-400 mb-4">
              บริการตรวจสอบสลิปธนาคารระดับมืออาชีพสำหรับธุรกิจทุกขนาด
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-neutral-400 hover:text-white transition-colors">
                <FaFacebook className="text-xl" />
              </a>
              <a href="#" className="text-neutral-400 hover:text-white transition-colors">
                <FaTwitter className="text-xl" />
              </a>
              <a href="#" className="text-neutral-400 hover:text-white transition-colors">
                <FaLinkedin className="text-xl" />
              </a>
            </div>
          </div>
          
          <div>
            <h4 className="text-white font-medium mb-4">บริษัท</h4>
            <ul className="space-y-2">
              <li>
                <Link href="#about" className="text-neutral-400 hover:text-white transition-colors">
                  เกี่ยวกับเรา
                </Link>
              </li>
              <li>
                <Link href="#careers" className="text-neutral-400 hover:text-white transition-colors">
                  ร่วมงานกับเรา
                </Link>
              </li>
              <li>
                <Link href="#contact" className="text-neutral-400 hover:text-white transition-colors">
                  ติดต่อเรา
                </Link>
              </li>
              <li>
                <Link href="#blog" className="text-neutral-400 hover:text-white transition-colors">
                  บล็อก
                </Link>
              </li>
            </ul>
          </div>
          
          <div>
            <h4 className="text-white font-medium mb-4">บริการ</h4>
            <ul className="space-y-2">
              <li>
                <Link href="/pricing" className="text-neutral-400 hover:text-white transition-colors">
                  แพ็กเกจและราคา
                </Link>
              </li>
              <li>
                <Link href="/docs" className="text-neutral-400 hover:text-white transition-colors">
                  เอกสารประกอบ API
                </Link>
              </li>
              <li>
                <Link href="/dashboard" className="text-neutral-400 hover:text-white transition-colors">
                  ฝ่ายสนับสนุน
                </Link>
              </li>
              <li>
                <Link href="#status" className="text-neutral-400 hover:text-white transition-colors">
                  สถานะบริการ
                </Link>
              </li>
            </ul>
          </div>
          
          <div>
            <h4 className="text-white font-medium mb-4">กฎหมาย</h4>
            <ul className="space-y-2">
              <li>
                <Link href="#terms" className="text-neutral-400 hover:text-white transition-colors">
                  ข้อกำหนดการให้บริการ
                </Link>
              </li>
              <li>
                <Link href="#privacy" className="text-neutral-400 hover:text-white transition-colors">
                  นโยบายความเป็นส่วนตัว
                </Link>
              </li>
              <li>
                <Link href="#security" className="text-neutral-400 hover:text-white transition-colors">
                  ความปลอดภัย
                </Link>
              </li>
              <li>
                <Link href="#compliance" className="text-neutral-400 hover:text-white transition-colors">
                  การปฏิบัติตามกฎหมาย
                </Link>
              </li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-neutral-700 mt-8 pt-8 text-center text-neutral-400 text-sm">
          <p>&copy; {new Date().getFullYear()} VerifySlip. สงวนลิขสิทธิ์ทั้งหมด.</p>
        </div>
      </div>
    </footer>
  );
}
