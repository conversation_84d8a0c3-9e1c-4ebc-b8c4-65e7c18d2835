# การตั้งค่าฐานข้อมูล PostgreSQL ในเครื่อง

เอกสารนี้อธิบายวิธีการตั้งค่าและใช้งานฐานข้อมูล PostgreSQL ในเครื่องสำหรับโปรเจค SLIPKUY โดยใช้ Nix

## ข้อดีของการใช้ฐานข้อมูลในเครื่อง

1. **ความเร็ว**: การเชื่อมต่อกับฐานข้อมูลในเครื่องเร็วกว่าการเชื่อมต่อผ่านอินเทอร์เน็ต
2. **ความเสถียร**: ไม่ต้องกังวลเรื่องปัญหาการเชื่อมต่ออินเทอร์เน็ตหรือการบำรุงรักษาของผู้ให้บริการ
3. **ความเป็นส่วนตัว**: ข้อมูลอยู่ในเครื่องของคุณเท่านั้น
4. **ไม่มีค่าใช้จ่าย**: ไม่ต้องจ่ายค่าบริการฐานข้อมูลบนคลาวด์

## การตั้งค่าฐานข้อมูล

โปรเจคนี้ใช้ Nix เพื่อสร้างสภาพแวดล้อม PostgreSQL แบบ ephemeral (ชั่วคราว) ซึ่งทำให้ไม่ต้องติดตั้ง PostgreSQL ในระบบ และสามารถนำเข้าข้อมูลจากไฟล์ backup.sql ได้อย่างง่ายดาย

### ข้อมูลการเชื่อมต่อ

```
Host: localhost
Port: 5432
Database: slipkuy_db
Username: slipkuy_user
Password: slipkuy_password
```

Connection string:
```
postgresql://slipkuy_user:slipkuy_password@localhost:5432/slipkuy_db
```

## วิธีการใช้งาน

### 1. เริ่มต้นฐานข้อมูล

```bash
nix-shell local-db.nix --run 'init-postgres'
```

คำสั่งนี้จะ:
- เริ่มต้น PostgreSQL
- สร้างฐานข้อมูลและผู้ใช้
- นำเข้าข้อมูลจากไฟล์ backup.sql
- เปิด psql shell เพื่อให้คุณสามารถโต้ตอบกับฐานข้อมูลได้

### 2. หยุดฐานข้อมูล

```bash
nix-shell local-db.nix --run 'stop-postgres'
```

### 3. รีเซ็ตฐานข้อมูล

หากต้องการลบข้อมูลทั้งหมดและเริ่มต้นใหม่:

```bash
nix-shell local-db.nix --run 'reset-postgres'
```

## การใช้งานกับแอพพลิเคชัน

แอพพลิเคชันได้ถูกตั้งค่าให้ใช้ฐานข้อมูลในเครื่องแล้ว โดยการแก้ไขไฟล์ต่อไปนี้:

1. **`.env`**: ตั้งค่า DATABASE_URL ให้ชี้ไปที่ฐานข้อมูลในเครื่อง
2. **`shell.nix`** และ **`flake.nix`**: ตั้งค่าตัวแปรสภาพแวดล้อมสำหรับการเชื่อมต่อฐานข้อมูล

### การเริ่มต้นใช้งานแอพพลิเคชัน

1. เริ่มต้นฐานข้อมูล:
   ```bash
   nix-shell local-db.nix --run 'init-postgres'
   ```

2. เปิดเทอร์มินัลอีกหน้าต่างและเริ่มต้นแอพพลิเคชัน:
   ```bash
   nix-shell --run 'npm run dev'
   ```

## การแก้ไขปัญหา

### ปัญหาพอร์ตถูกใช้งาน

หากพบข้อผิดพลาดว่าพอร์ต 5432 ถูกใช้งานแล้ว:

1. ตรวจสอบว่ามี PostgreSQL อื่นทำงานอยู่หรือไม่:
   ```bash
   ps aux | grep postgres
   ```

2. หยุดการทำงานของ PostgreSQL ที่ทำงานอยู่:
   ```bash
   sudo systemctl stop postgresql
   ```
   หรือ
   ```bash
   nix-shell local-db.nix --run 'stop-postgres'
   ```

### ปัญหาการนำเข้าข้อมูล

หากพบปัญหาในการนำเข้าข้อมูลจาก backup.sql:

1. ตรวจสอบว่าไฟล์ backup.sql อยู่ในโฟลเดอร์โปรเจค
2. ตรวจสอบว่าไฟล์ backup.sql มีรูปแบบที่ถูกต้อง
3. ลองรีเซ็ตฐานข้อมูลและเริ่มต้นใหม่:
   ```bash
   nix-shell local-db.nix --run 'reset-postgres'
   nix-shell local-db.nix --run 'init-postgres'
   ```

## การสำรองข้อมูล

หากต้องการสำรองข้อมูลจากฐานข้อมูลในเครื่อง:

```bash
nix-shell --run 'pg_dump -h localhost -p 5432 -U slipkuy_user -d slipkuy_db > backup_new.sql'
```

เมื่อถูกถามรหัสผ่าน ให้ป้อน: `slipkuy_password`
