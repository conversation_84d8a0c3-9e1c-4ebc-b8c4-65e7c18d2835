import { useQuery, useQ<PERSON>yClient } from "@tanstack/react-query";
import { <PERSON> } from "wouter";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";
import { motion, AnimatePresence } from "framer-motion";
import {
  AlertTriangle,
  CheckCircle2,
  XCircle,
  Upload,
  History,
  TrendingUp,
  Clock,
  Wallet,
  Crown,
  CrownIcon,
  Sparkles,
  Zap,
  ShieldCheck,
  Bolt,
  CloudLightning,
  Flame,
  Package,
  Shield,
  Star,
  ArrowUp,
  BadgeCheck,
  MessageSquare,
  Settings
} from "lucide-react";
import { DashboardLayout } from "@/components/layouts/dashboard-responsive-new";
// import { DashboardPackagesSection } from "@/components/packages/dashboard-packages-section";
import { useAuth } from "@/hooks/use-auth";
import DuplicateSlipToggle from '@/components/duplicate-slip-toggle';
import { formatNumber, formatDate } from "@/lib/utils";
import { useState, useEffect, useRef } from "react";
// SSE (Server-Sent Events) แทน Socket.IO
import { toast, useToast } from "@/hooks/use-toast";

// ประกาศ Interface สำหรับข้อมูลสถิติผู้ใช้
interface UserStats {
  totalVerifications: number;
  successfulVerifications: number;
  failedVerifications: number;
  activePackage: {
    id: number;
    userId: number;
    packageId: number;
    startDate: string;
    endDate: string;
    isActive: boolean;
    requestsUsed: number;
    package: {
      id: number;
      name: string;
      price: number;
      requestsLimit: number;
      creditPerVerification: number;
      features: string[];
    }
  } | null;
  requestsRemaining: number;
}

// ประกาศ Interface สำหรับข้อมูลการตรวจสอบล่าสุด
interface RecentVerification {
  id: number;
  status: string;
  transactionRef: string;
  amount: number;
  sender: string;
  receiver: string;
  bankName: string;
  transactionDate: string;
  createdAt: string;
}

// คอมโพเนนต์สำหรับสร้างลักษณะพื้นหลังที่มีดวงดาวกะพริบ
const CelestialBackground = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="relative p-6 overflow-hidden">
      {/* พื้นหลังแบบกระจายรัศมี */}
      <div className="absolute inset-0 bg-gradient-radial from-indigo-600/20 via-purple-800/10 to-transparent"></div>

      {/* เนื้อหาหลัก */}
      <div className="relative z-10">{children}</div>
    </div>
  );
};

// คอมโพเนนต์การ์ดเทพเจ้า - สำหรับแสดงข้อมูลในรูปแบบการ์ดที่มีเอฟเฟกต์สวยงาม
const DeityCard = ({
  title,
  icon: Icon,
  iconColorClass = "text-amber-300",
  gradientFrom = "from-indigo-900/60",
  gradientTo = "to-purple-900/60",
  borderColor = "border-indigo-500/20",
  glowColor = "shadow-indigo-500/20",
  isLoading = false,
  children
}: {
  title: string;
  icon: any;
  iconColorClass?: string;
  gradientFrom?: string;
  gradientTo?: string;
  borderColor?: string;
  glowColor?: string;
  isLoading?: boolean;
  children: React.ReactNode;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ type: "spring", damping: 12 }}
      className={`relative rounded-xl overflow-hidden backdrop-blur-md shadow-lg ${glowColor} border ${borderColor}`}
    >
      <div className={`absolute inset-0 bg-gradient-to-br ${gradientFrom} ${gradientTo}`}></div>
      <div className="relative z-10 p-5">
        <div className="flex items-center mb-4">
          <div className="relative h-8 w-8 flex items-center justify-center mr-3">
            <div className="absolute inset-0 bg-gradient-radial from-indigo-600/30 via-indigo-800/10 to-transparent rounded-full"></div>
            <div className="absolute h-8 w-8 rounded-full bg-gradient-to-br from-indigo-800 to-purple-900 flex items-center justify-center">
              <Icon className={`h-4 w-4 ${iconColorClass}`} />
            </div>
          </div>
          <h3 className="font-bold text-indigo-100">{title}</h3>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-300"></div>
          </div>
        ) : (
          children
        )}
      </div>
    </motion.div>
  );
};

// คอมโพเนนต์สำหรับการ์ดแพ็คเกจแบบเทพเจ้า
const PackageDeityCard = ({
  packageData,
  percentage,
  status,
  remaining,
  isLoading
}: {
  packageData: any;
  percentage: number;
  status: string;
  remaining: number;
  isLoading: boolean;
}) => {
  return (
    <DeityCard
      title={packageData?.name || "แพ็กเกจเทพ"}
      icon={Crown}
      gradientFrom="from-purple-900/80"
      gradientTo="to-indigo-900/80"
      borderColor="border-amber-500/30"
      glowColor="shadow-amber-500/20"
      isLoading={isLoading}
    >
      {!isLoading && (
        <div className="space-y-4">
          <div className="text-indigo-200 text-sm opacity-80 mb-2">
            <div className="flex justify-between text-xs">
              <span>วันที่เริ่มต้น: {formatDate(new Date(packageData.startDate))}</span>
              <span>วันที่สิ้นสุด: {formatDate(new Date(packageData.endDate))}</span>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-indigo-100">จำนวนครั้งที่ใช้งานได้</span>
              <span className="font-medium text-amber-200">
                {formatNumber(packageData.requestsUsed)} / {formatNumber(packageData.package.requestsLimit)}
              </span>
            </div>

            <div className="relative h-2 w-full bg-indigo-950/60 rounded-full overflow-hidden">
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: `${percentage}%` }}
                transition={{ duration: 1, ease: "easeOut" }}
                className={`absolute h-full left-0 rounded-full ${
                  status === "critical"
                    ? "bg-gradient-to-r from-red-600 to-red-500"
                    : status === "warning"
                      ? "bg-gradient-to-r from-amber-500 to-amber-400"
                      : "bg-gradient-to-r from-emerald-500 to-emerald-400"
                }`}
              />
              <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-white/10 via-transparent to-transparent opacity-50"></div>
            </div>

            <div className="flex justify-between items-center">
              <div className={`text-xs flex items-center ${
                status === "critical"
                  ? "text-red-400"
                  : status === "warning"
                    ? "text-amber-300"
                    : "text-emerald-300"
              }`}>
                {status === "critical" && (
                  <div className="flex items-center">
                    <AlertTriangle className="h-3 w-3 mr-1" />
                    <span>เหลือน้อย! คุณใช้งานไปแล้ว {percentage}%</span>
                  </div>
                )}
                {status === "warning" && (
                  <div className="flex items-center">
                    <AlertTriangle className="h-3 w-3 mr-1" />
                    <span>คุณใช้งานไปแล้ว {percentage}% ของแพ็กเกจ</span>
                  </div>
                )}
                {status === "good" && (
                  <div className="flex items-center">
                    <CheckCircle2 className="h-3 w-3 mr-1" />
                    <span>เหลืออีก {formatNumber(remaining)} ครั้ง</span>
                  </div>
                )}
              </div>
              <div className="text-xs text-indigo-300">{percentage}%</div>
            </div>

            {/* แสดงข้อมูลอัตราค่าใช้จ่ายเครดิต */}
            <div className="mt-3 pt-3 border-t border-indigo-800/30">
              <div className="flex items-center text-xs space-x-1 mb-1">
                <Zap className="h-3 w-3 text-amber-400" />
                <span className="text-purple-200 font-medium">อัตราค่าใช้จ่ายเครดิต:</span>
              </div>
              <div className="bg-indigo-900/40 border border-indigo-700/30 rounded-lg p-3">
                <div className="flex items-center justify-between">
                  <span className="text-xs text-indigo-300">เมื่อโควต้าหมด:</span>
                  <span className="text-xs font-medium text-amber-200">
                    {(packageData.package.creditPerVerification || 0).toFixed(2)} บาท/ครั้ง
                  </span>
                </div>
                <div className="mt-2 text-xs text-indigo-400 italic">
                  <div className="flex items-center">
                    <BadgeCheck className="h-3 w-3 text-emerald-400 mr-1 inline" />
                    <span>สามารถใช้เครดิตในการตรวจสอบสลิปเมื่อโควต้าหมด</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="flex space-x-3 pt-2">
            <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }}>
              <Button className="bg-gradient-to-r from-indigo-600 to-indigo-800 hover:from-indigo-700 hover:to-indigo-900 border-none shadow-md shadow-indigo-900/30 text-white" asChild>
                <Link href="/verify">
                  <Upload className="h-4 w-4 mr-2" />
                  ตรวจสอบสลิป
                </Link>
              </Button>
            </motion.div>
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.97 }}>
              <Button className="bg-gradient-to-r from-rose-500 via-amber-500 to-yellow-400 hover:from-rose-600 hover:via-amber-600 hover:to-yellow-500 text-white font-bold shadow-lg shadow-amber-500/30 border border-amber-400/30 h-11 rounded-xl relative overflow-hidden" asChild>
                <Link href="/user-packages">
                  <div className="absolute inset-0 bg-white/10 rounded-xl"></div>
                  <Sparkles className="h-4 w-4 mr-2 text-yellow-100 animate-pulse" />
                  <span className="relative z-10">อัปเกรดแพ็กเกจ</span>
                  <Star className="h-4 w-4 ml-2 text-yellow-100" />
                  <div className="absolute -top-1 -right-1 w-2 h-2 bg-yellow-300 rounded-full animate-ping"></div>
                </Link>
              </Button>
            </motion.div>
          </div>
        </div>
      )}
    </DeityCard>
  );
};

// คอมโพเนนต์การ์ดเครดิตแบบเทพเจ้า
const CreditDeityCard = ({ credit, isLoading }: { credit: number; isLoading: boolean }) => {
  return (
    <DeityCard
      title="คลังสมบัติ"
      icon={Wallet}
      gradientFrom="from-amber-900/40"
      gradientTo="to-amber-700/20"
      borderColor="border-amber-500/30"
      glowColor="shadow-amber-500/10"
      isLoading={isLoading}
    >
      {!isLoading && (
        <div className="space-y-4">
          <div className="relative">
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5 }}
              className="text-3xl font-bold text-amber-300 flex items-center"
            >
              {formatNumber(credit)} <span className="ml-1 text-xs text-amber-200">บาท</span>
            </motion.div>
            <p className="text-xs text-indigo-300 mt-1">
              ใช้เครดิตของคุณในการซื้อแพ็กเกจหรืออัปเกรดแพ็กเกจที่มีอยู่
            </p>
          </div>

          <div className="space-y-4">
            <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }}>
              <Button className="w-full bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-700 hover:to-amber-800 border-none text-white" asChild>
                <Link href="/topup">
                  <Zap className="h-4 w-4 mr-2" />
                  เติมคลังสมบัติ
                </Link>
              </Button>
            </motion.div>

            {/* DuplicateSlipToggle Component */}
            <div className="mt-4">
              <DuplicateSlipToggle />
            </div>
          </div>
        </div>
      )}
    </DeityCard>
  );
};

// คอมโพเนนต์การ์ดสถิติแบบเทพเจ้า
const StatsDeityCard = ({ title, value, icon: Icon, color, unit, isLoading }: {
  title: string;
  value: number;
  icon: any;
  color: string;
  unit?: string;
  isLoading: boolean;
}) => {
  // เลือกสีตามค่าที่รับมา
  const colorMap: Record<string, { bg: string; text: string; glow: string }> = {
    blue: {
      bg: "from-blue-600/40 to-blue-900/20",
      text: "text-blue-300",
      glow: "shadow-blue-500/10"
    },
    green: {
      bg: "from-emerald-600/40 to-emerald-900/20",
      text: "text-emerald-300",
      glow: "shadow-emerald-500/10"
    },
    red: {
      bg: "from-red-600/40 to-red-900/20",
      text: "text-red-300",
      glow: "shadow-red-500/10"
    },
    purple: {
      bg: "from-purple-600/40 to-purple-900/20",
      text: "text-purple-300",
      glow: "shadow-purple-500/10"
    },
    amber: {
      bg: "from-amber-600/40 to-amber-900/20",
      text: "text-amber-300",
      glow: "shadow-amber-500/10"
    }
  };

  const colorStyle = colorMap[color] || colorMap.blue;

  return (
    <DeityCard
      title={title}
      icon={Icon}
      iconColorClass={colorStyle.text}
      gradientFrom={colorStyle.bg.split(" ")[0]}
      gradientTo={colorStyle.bg.split(" ")[1]}
      borderColor={`border-${color}-500/30`}
      glowColor={colorStyle.glow}
      isLoading={isLoading}
    >
      {!isLoading && (
        <div className="pt-1">
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, type: "spring" }}
            className={`text-3xl font-bold ${colorStyle.text}`}
          >
            {formatNumber(value)}{unit && <span className="text-xs ml-1 opacity-70">{unit}</span>}
          </motion.div>
        </div>
      )}
    </DeityCard>
  );
};

// คอมโพเนนต์การ์ดประวัติการตรวจสอบแบบเทพเจ้า
const VerificationHistoryCard = ({ verifications, isLoading }: {
  verifications: RecentVerification[] | undefined;
  isLoading: boolean;
}) => {
  return (
    <DeityCard
      title="ประวัติการตรวจสอบล่าสุด"
      icon={History}
      gradientFrom="from-indigo-900/60"
      gradientTo="to-indigo-950/60"
      isLoading={isLoading}
    >
      {!isLoading && (
        <div className="space-y-4 pt-2">
          {verifications && verifications.length > 0 ? (
            <div className="space-y-3">
              {verifications.map((verification, index) => (
                <motion.div
                  key={verification.id}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-indigo-950/40 backdrop-blur-sm rounded-lg p-3 border border-indigo-900/60"
                >
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <div className="mr-3">
                        {verification.status === 'success' ? (
                          <div className="h-8 w-8 bg-emerald-900/30 rounded-full flex items-center justify-center">
                            <CheckCircle2 className="h-4 w-4 text-emerald-400" />
                          </div>
                        ) : verification.status === 'failed' ? (
                          <div className="h-8 w-8 bg-red-900/30 rounded-full flex items-center justify-center">
                            <XCircle className="h-4 w-4 text-red-400" />
                          </div>
                        ) : (
                          <div className="h-8 w-8 bg-amber-900/30 rounded-full flex items-center justify-center">
                            <Clock className="h-4 w-4 text-amber-400" />
                          </div>
                        )}
                      </div>
                      <div>
                        <p className="font-medium text-indigo-100 text-sm">
                          {verification.bankName || "ไม่ระบุธนาคาร"}
                          {verification.transactionRef && ` - ${verification.transactionRef}`}
                        </p>
                        <p className="text-xs text-indigo-300">
                          {verification.transactionDate ?
                            formatDate(new Date(verification.transactionDate)) :
                            formatDate(new Date(verification.createdAt))}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-amber-300 text-sm">
                        {verification.amount ? `${formatNumber(verification.amount)} บาท` : "ไม่ระบุจำนวน"}
                      </p>
                      <p className={`text-xs ${
                        verification.status === 'success'
                          ? "text-emerald-400"
                          : verification.status === 'failed'
                            ? "text-red-400"
                            : "text-amber-400"
                      }`}>
                        {verification.status === 'success' ? "สำเร็จ" :
                         verification.status === 'failed' ? "ล้มเหลว" : "กำลังดำเนินการ"}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}

              <div className="flex justify-center pt-2">
                <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }}>
                  <Button variant="ghost" className="text-indigo-300 hover:bg-indigo-800/40 hover:text-indigo-100" asChild>
                    <Link href="/history">
                      <span className="flex items-center">
                        <History className="h-4 w-4 mr-2" />
                        ดูประวัติทั้งหมด
                      </span>
                    </Link>
                  </Button>
                </motion.div>
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-6 space-y-4">
              <div className="bg-indigo-900/30 p-4 rounded-full">
                <History className="h-8 w-8 text-indigo-300" />
              </div>
              <p className="text-indigo-300 text-sm">ยังไม่มีประวัติการตรวจสอบ</p>
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button className="bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-700 hover:to-indigo-800 text-white" asChild>
                  <Link href="/verify">
                    <Upload className="h-4 w-4 mr-2" />
                    เริ่มตรวจสอบสลิป
                  </Link>
                </Button>
              </motion.div>
            </div>
          )}
        </div>
      )}
    </DeityCard>
  );
};

// คอมโพเนนต์แสดงเมื่อยังไม่มีแพ็กเกจ
const NoPackageDeityCard = ({ isLoading }: { isLoading: boolean }) => {
  return (
    <DeityCard
      title="ยังไม่มีแพ็กเกจที่ใช้งาน"
      icon={Crown}
      gradientFrom="from-purple-900/60"
      gradientTo="to-indigo-900/60"
      isLoading={isLoading}
    >
      {!isLoading && (
        <div className="space-y-6">
          <div className="flex justify-center py-4">
            <div className="relative">
              <div className="absolute inset-0 rounded-full bg-indigo-600/20 animate-ping"></div>
              <div className="relative h-20 w-20 bg-gradient-to-br from-indigo-800 to-purple-900 rounded-full flex items-center justify-center">
                <Package className="h-10 w-10 text-indigo-300" />
              </div>
            </div>
          </div>
          <div className="text-center space-y-2">
            <p className="text-indigo-200 text-sm">คุณยังไม่ได้สมัครแพ็กเกจใดๆ กรุณาเลือกแพ็กเกจเพื่อเริ่มใช้งาน</p>
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button className="mt-2 w-full bg-gradient-to-r from-indigo-600 to-indigo-800 hover:from-indigo-700 hover:to-indigo-900 text-white" asChild>
                <Link href="/user-packages">
                  <Crown className="h-4 w-4 mr-2 text-amber-300" />
                  เลือกแพ็กเกจเทพเจ้า
                </Link>
              </Button>
            </motion.div>
          </div>
        </div>
      )}
    </DeityCard>
  );
};

// รีแอคทีฟเอฟเฟกต์เมื่อคลิก
const clickEffect = (e: React.MouseEvent<HTMLDivElement>) => {
  const button = e.currentTarget;
  const circle = document.createElement("span");
  const diameter = Math.max(button.clientWidth, button.clientHeight);
  const radius = diameter / 2;

  circle.style.width = circle.style.height = `${diameter}px`;
  circle.style.left = `${e.clientX - button.offsetLeft - radius}px`;
  circle.style.top = `${e.clientY - button.offsetTop - radius}px`;
  circle.classList.add("ripple");

  const ripple = button.getElementsByClassName("ripple")[0];
  if (ripple) {
    ripple.remove();
  }

  button.appendChild(circle);
};

export default function DashboardPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [showWelcome, setShowWelcome] = useState(true);

  // ใช้ Server-Sent Events (SSE) แทน WebSocket และ Socket.IO

  // สถานะสำหรับเก็บค่า polling interval จากการตั้งค่าระบบ
  const [pollingInterval, setPollingInterval] = useState(3000); // ค่าเริ่มต้นคือ 3 วินาที
  const [useSSE, setUseSSE] = useState(true); // ใช้ SSE เป็นค่าเริ่มต้น

  // อ้างอิงถึง EventSource เพื่อให้สามารถปิดการเชื่อมต่อได้
  const eventSourceRef = useRef<EventSource | null>(null);

  // SSE สำหรับดึงข้อมูลแบบเรียลไทม์
  useEffect(() => {
    // ถ้าไม่ได้ใช้ SSE หรือไม่มีการล็อกอิน ให้ข้ามไป
    if (!useSSE || !user) return;

    try {
      // ปิดการเชื่อมต่อเดิม (ถ้ามี)
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }

      // เปิดการเชื่อมต่อใหม่
      const eventSource = new EventSource('/api/sse/dashboard-data');
      eventSourceRef.current = eventSource;

      // รับฟังเหตุการณ์ stats
      eventSource.addEventListener('stats', (event) => {
        try {
          const stats = JSON.parse(event.data);
          queryClient.setQueryData(['/api/stats'], stats);

          // อัพเดตข้อมูลในตัวแปร stats จะมีข้อมูล activePackage ด้วย
          // จึงไม่จำเป็นต้องเรียก API แยกอีก
          if (stats.activePackage) {
            queryClient.setQueryData(['/api/user/active-package'], stats.activePackage);
          }
        } catch (err) {
          console.error('ไม่สามารถแปลงข้อมูล stats จาก SSE:', err);
        }
      });

      // รับฟังเหตุการณ์ activePackage เพิ่มเติม
      eventSource.addEventListener('activePackage', (event) => {
        try {
          const activePackage = JSON.parse(event.data);
          queryClient.setQueryData(['/api/user/active-package'], activePackage);
        } catch (err) {
          console.error('ไม่สามารถแปลงข้อมูล activePackage จาก SSE:', err);
        }
      });

      // รับฟังเหตุการณ์ credit
      eventSource.addEventListener('credit', (event) => {
        try {
          const creditData = JSON.parse(event.data);
          queryClient.setQueryData(['/api/user/credit'], creditData);
        } catch (err) {
          console.error('ไม่สามารถแปลงข้อมูล credit จาก SSE:', err);
        }
      });

      // รับฟังเหตุการณ์ verifications
      eventSource.addEventListener('verifications', (event) => {
        try {
          const verifications = JSON.parse(event.data);
          queryClient.setQueryData(['/api/verifications'], verifications);
        } catch (err) {
          console.error('ไม่สามารถแปลงข้อมูล verifications จาก SSE:', err);
        }
      });

      // รับฟังเหตุการณ์ error
      eventSource.addEventListener('error', (event) => {
        console.error('เกิดข้อผิดพลาดจาก SSE:', event);
        // ถ้าเกิดข้อผิดพลาด ให้กลับไปใช้ polling แทน
        setUseSSE(false);
        toast({
          title: "เกิดข้อผิดพลาดในการเชื่อมต่อแบบเรียลไทม์",
          description: "กลับไปใช้การอัพเดทข้อมูลแบบตามรอบเวลาแทน",
          variant: "destructive",
        });
      });
    } catch (error) {
      console.error('เกิดข้อผิดพลาดในการสร้าง SSE:', error);
      setUseSSE(false);
    }

    // ทำความสะอาดเมื่อคอมโพเนนต์ถูกถอดออก
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
        eventSourceRef.current = null;
      }
    };
  }, [useSSE, user, queryClient, toast]);

  // ดึงการตั้งค่า polling interval จาก API (ยังใช้สำหรับกรณีที่ SSE ไม่ทำงาน)
  useEffect(() => {
    // ไม่ว่าจะใช้ SSE หรือไม่ ยังต้องดึงค่านี้เพื่อแสดงในหน้าเว็บ
    fetch('/api/admin/settings')
      .then(res => res.json())
      .then(data => {
        if (data && Array.isArray(data)) {
          const pollingIntervalSetting = data.find(setting => setting.key === 'polling_interval');
          if (pollingIntervalSetting && pollingIntervalSetting.value) {
            const interval = parseInt(pollingIntervalSetting.value);
            if (!isNaN(interval) && interval > 0) {
              setPollingInterval(interval);
              console.log(`ตั้งค่า polling interval เป็น ${interval} มิลลิวินาที`);
            }
          }
        }
      })
      .catch(err => console.error('ไม่สามารถดึงการตั้งค่า polling interval:', err));
  }, []);

  // ดึงข้อมูลสถิติผู้ใช้
  const { data: stats, isLoading: isLoadingStats, refetch: refetchStats } = useQuery<UserStats>({
    queryKey: ['/api/stats'],
    // ถ้าไม่ใช้ SSE ให้ใช้ polling แทน แต่ถ้าใช้ SSE แล้วไม่ต้อง refetch อัตโนมัติ
    refetchInterval: useSSE ? false : pollingInterval,
  });

  // ดึงข้อมูลเครดิตของผู้ใช้
  const { data: creditData, isLoading: isLoadingCredit, refetch: refetchCredit } = useQuery<{ credit: number }>({
    queryKey: ['/api/user/credit'],
    enabled: !!user,
    // ถ้าไม่ใช้ SSE ให้ใช้ polling แทน แต่ถ้าใช้ SSE แล้วไม่ต้อง refetch อัตโนมัติ
    refetchInterval: useSSE ? false : pollingInterval,
  });

  // ดึงข้อมูลประวัติการตรวจสอบล่าสุด 5 รายการ
  const { data: recentVerifications, isLoading: isLoadingVerifications, refetch: refetchVerifications } = useQuery<RecentVerification[]>({
    queryKey: ['/api/verifications'],
    select: (data) => data.slice(0, 5),
    // ถ้าไม่ใช้ SSE ให้ใช้ polling แทน แต่ถ้าใช้ SSE แล้วไม่ต้อง refetch อัตโนมัติ
    refetchInterval: useSSE ? false : pollingInterval,
  });

  // ฟังก์ชันสำหรับจัดรูปแบบจำนวนเป็นตัวเลขที่มีจุลภาคคั่น
  const formatNumber = (value: number) => {
    return value.toLocaleString('th-TH');
  };

  // ใช้ refetchInterval ในตัว useQuery แล้ว จึงไม่จำเป็นต้องใช้ polling ด้วย useEffect อีก

  // คำนวณจำนวนครั้งที่ใช้งานได้ และเปอร์เซ็นต์การใช้งาน
  const usagePercentage = stats?.activePackage
    ? Math.round((stats.activePackage.requestsUsed / stats.activePackage.package.requestsLimit) * 100)
    : 0;

  const usageStatus = () => {
    if (!stats?.activePackage) return "no-package";
    if (usagePercentage >= 90) return "critical";
    if (usagePercentage >= 70) return "warning";
    return "good";
  };

  // ซ่อน Welcome message หลังจาก 5 วินาที
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowWelcome(false);
    }, 5000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <DashboardLayout>
      {/* แบ็คกราวด์เอฟเฟกต์สวรรค์ */}
      <CelestialBackground>
        <div className="space-y-8">
          {/* หัวข้อหน้า - แอนิเมชั่นแบบพิเศษ */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="relative"
          >
            {/* Welcome Animation */}
            <AnimatePresence>
              {showWelcome && (
                <motion.div
                  initial={{ opacity: 0, y: -10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: -10, scale: 0.9 }}
                  transition={{ duration: 0.5 }}
                  className="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-indigo-900/80 to-purple-900/80 backdrop-blur-md px-5 py-3 rounded-xl border border-indigo-500/30 shadow-lg z-20"
                >
                  <div className="flex items-center space-x-3">
                    <Sparkles className="h-5 w-5 text-amber-300" />
                    <span className="text-indigo-100">ยินดีต้อนรับสู่สรวงสวรรค์แห่งการตรวจสอบ!</span>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            <div className="flex items-center mb-2">
              <div className="relative mr-3">
                <motion.div
                  animate={{ scale: [1, 1.1, 1], opacity: [0.5, 1, 0.5] }}
                  transition={{ duration: 3, repeat: Infinity }}
                  className="absolute -inset-1 bg-gradient-to-r from-amber-400 to-amber-600 rounded-full opacity-30 blur-md"
                ></motion.div>
                <div className="relative h-10 w-10 flex items-center justify-center bg-gradient-to-br from-indigo-800 to-purple-900 rounded-full">
                  <Crown className="h-5 w-5 text-amber-300" />
                </div>
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-indigo-100 to-white">
                  สรวงสวรรค์
                </h1>
                <p className="text-indigo-300 text-sm">
                  ยินดีต้อนรับเทพเจ้า {user?.firstName || user?.username}
                </p>
              </div>
            </div>
          </motion.div>

          {/* Content Grid - Cosmic Layout */}
          <div className="grid grid-cols-1 md:grid-cols-12 gap-6">
            {/* Main Package Card - Larger and Prominent */}
            <div className="md:col-span-8">
              {isLoadingStats ? (
                <PackageDeityCard
                  packageData={null}
                  percentage={0}
                  status="good"
                  remaining={0}
                  isLoading={true}
                />
              ) : stats?.activePackage ? (
                <PackageDeityCard
                  packageData={stats.activePackage}
                  percentage={usagePercentage}
                  status={usageStatus()}
                  remaining={stats.requestsRemaining}
                  isLoading={false}
                />
              ) : (
                <NoPackageDeityCard isLoading={false} />
              )}
            </div>

            {/* Credit Card - Divine Treasury */}
            <div className="md:col-span-4">
              <CreditDeityCard
                credit={creditData?.credit || 0}
                isLoading={isLoadingCredit}
              />
            </div>
          </div>

          {/* Stats Cards - Divine Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
            <StatsDeityCard
              title="การตรวจสอบทั้งหมด"
              value={stats?.totalVerifications || 0}
              icon={TrendingUp}
              color="blue"
              unit="ครั้ง"
              isLoading={isLoadingStats}
            />
            <StatsDeityCard
              title="การตรวจสอบสำเร็จ"
              value={stats?.successfulVerifications || 0}
              icon={BadgeCheck}
              color="green"
              unit="ครั้ง"
              isLoading={isLoadingStats}
            />
            <StatsDeityCard
              title="การตรวจสอบล้มเหลว"
              value={stats?.failedVerifications || 0}
              icon={Shield}
              color="red"
              unit="ครั้ง"
              isLoading={isLoadingStats}
            />
          </div>

          {/* Recent Verifications - Divine History */}
          <div className="mt-6">
            <div className="flex items-center justify-end mb-2">
              <div className="flex items-center space-x-2 text-xs text-amber-300">
                <div className="bg-amber-500 h-2 w-2 rounded-full animate-pulse"></div>
                <span>{useSSE ? "อัพเดทข้อมูลแบบเรียลไทม์" : `อัพเดทข้อมูลอัตโนมัติทุก ${pollingInterval / 1000} วินาที`}</span>
              </div>
            </div>

            <VerificationHistoryCard
              verifications={recentVerifications}
              isLoading={isLoadingVerifications}
            />
          </div>

          {/* User Settings Section - Link to Settings Page */}
          <div className="mt-6">
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="relative overflow-hidden bg-gradient-to-r from-indigo-800/60 via-violet-800/40 to-purple-800/60 rounded-xl border border-indigo-500/30 shadow-lg group cursor-pointer mb-6"
              onClick={(e) => clickEffect(e)}
            >
              <div className="p-5 flex items-center space-x-4">
                <div className="h-12 w-12 rounded-full bg-gradient-to-br from-violet-700 to-indigo-900 flex items-center justify-center">
                  <Settings className="h-6 w-6 text-indigo-200" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-indigo-100">การตั้งค่าส่วนตัว</h3>
                  <p className="text-indigo-300 text-sm">ปรับแต่งการทำงานของระบบตามความต้องการของคุณ</p>
                </div>
                <div className="ml-auto">
                  <ArrowUp className="h-6 w-6 text-indigo-300 group-hover:text-amber-300 transition-colors" />
                </div>
              </div>
              <Link href="/user-settings" className="absolute inset-0">
                <span className="sr-only">การตั้งค่าส่วนตัว</span>
              </Link>
              <span className="ripple absolute bg-white/20 rounded-full z-10 pointer-events-none"></span>
            </motion.div>
          </div>

          {/* Quick Actions - Divine Powers */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="relative overflow-hidden bg-gradient-to-r from-purple-800/40 to-purple-700/20 rounded-xl border border-purple-500/30 shadow-lg group cursor-pointer"
              onClick={(e) => clickEffect(e)}
            >
              <div className="p-5 flex items-center space-x-4">
                <div className="h-12 w-12 rounded-full bg-gradient-to-br from-purple-700 to-purple-900 flex items-center justify-center">
                  <MessageSquare className="h-6 w-6 text-purple-200" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-indigo-100">Webhook Service</h3>
                  <p className="text-indigo-300 text-sm">รับการแจ้งเตือนอัตโนมัติไปยังระบบของคุณ</p>
                </div>
                <div className="ml-auto">
                  <ArrowUp className="h-6 w-6 text-indigo-300 group-hover:text-amber-300 transition-colors" />
                </div>
              </div>
              <Link href="/webhook-service" className="absolute inset-0">
                <span className="sr-only">Webhook Service</span>
              </Link>
              <span className="ripple absolute bg-white/20 rounded-full z-10 pointer-events-none"></span>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="relative overflow-hidden bg-gradient-to-r from-indigo-800/60 to-purple-900/60 rounded-xl border border-indigo-500/30 shadow-lg group cursor-pointer"
              onClick={(e) => clickEffect(e)}
            >
              <div className="p-5 flex items-center space-x-4">
                <div className="h-12 w-12 rounded-full bg-gradient-to-br from-indigo-700 to-indigo-900 flex items-center justify-center">
                  <Upload className="h-6 w-6 text-indigo-200" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-indigo-100">ตรวจสอบสลิป</h3>
                  <p className="text-indigo-300 text-sm">อัพโหลดสลิปเพื่อตรวจสอบความถูกต้อง</p>
                </div>
                <div className="ml-auto">
                  <ArrowUp className="h-6 w-6 text-indigo-300 group-hover:text-amber-300 transition-colors" />
                </div>
              </div>
              <Link href="/verify" className="absolute inset-0">
                <span className="sr-only">ตรวจสอบสลิป</span>
              </Link>
              <span className="ripple absolute bg-white/20 rounded-full z-10 pointer-events-none"></span>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="relative overflow-hidden bg-gradient-to-r from-amber-800/40 to-amber-700/20 rounded-xl border border-amber-500/30 shadow-lg group cursor-pointer"
              onClick={(e) => clickEffect(e)}
            >
              <div className="p-5 flex items-center space-x-4">
                <div className="h-12 w-12 rounded-full bg-gradient-to-br from-amber-700 to-amber-900 flex items-center justify-center">
                  <Wallet className="h-6 w-6 text-amber-200" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-indigo-100">เติมเครดิต</h3>
                  <p className="text-indigo-300 text-sm">เพิ่มพลังทิพย์เพื่อการตรวจสอบที่ไร้ขีดจำกัด</p>
                </div>
                <div className="ml-auto">
                  <ArrowUp className="h-6 w-6 text-indigo-300 group-hover:text-amber-300 transition-colors" />
                </div>
              </div>
              <Link href="/topup" className="absolute inset-0">
                <span className="sr-only">เติมเครดิต</span>
              </Link>
              <span className="ripple absolute bg-white/20 rounded-full z-10 pointer-events-none"></span>
            </motion.div>
          </div>

          {/* แพคเกจถูกย้ายไปที่หน้า /user-packages แล้ว */}
        </div>

        {/* CSS สำหรับเอฟเฟกต์คลิกแบบสายฟ้า */}
        <style>{`
          .ripple {
            position: absolute;
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.7s linear;
            background-color: rgba(255, 255, 255, 0.1);
            pointer-events: none;
            z-index: 10;
          }

          @keyframes ripple {
            to {
              transform: scale(2);
              opacity: 0;
            }
          }
        `}</style>
      </CelestialBackground>
    </DashboardLayout>
  );
}
