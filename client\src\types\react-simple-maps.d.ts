// Type definitions for react-simple-maps
declare module 'react-simple-maps' {
  import React from 'react';

  export interface ComposableMapProps {
    projection?: string;
    projectionConfig?: {
      scale?: number;
      center?: [number, number];
      rotate?: [number, number, number];
      parallels?: [number, number];
    };
    width?: number;
    height?: number;
    children?: React.ReactNode;
  }

  export interface GeographiesProps {
    geography: any;
    children: (props: { geographies: any[] }) => React.ReactNode;
  }

  export interface GeographyProps {
    geography: any;
    key?: React.Key;
    fill?: string;
    stroke?: string;
    strokeWidth?: number;
    style?: React.CSSProperties;
    className?: string;
    onClick?: (event: React.MouseEvent<SVGElement, MouseEvent>) => void;
    onMouseEnter?: (event: React.MouseEvent<SVGElement, MouseEvent>) => void;
    onMouseLeave?: (event: React.MouseEvent<SVGElement, MouseEvent>) => void;
  }

  export interface MarkerProps {
    coordinates: [number, number];
    key?: React.Key;
    style?: React.CSSProperties;
    className?: string;
    onClick?: (event: React.MouseEvent<SVGElement, MouseEvent>) => void;
    onMouseEnter?: (event: React.MouseEvent<SVGElement, MouseEvent>) => void;
    onMouseLeave?: (event: React.MouseEvent<SVGElement, MouseEvent>) => void;
    children?: React.ReactNode;
  }

  export const ComposableMap: React.FC<ComposableMapProps>;
  export const Geographies: React.FC<GeographiesProps>;
  export const Geography: React.FC<GeographyProps>;
  export const Marker: React.FC<MarkerProps>;
}