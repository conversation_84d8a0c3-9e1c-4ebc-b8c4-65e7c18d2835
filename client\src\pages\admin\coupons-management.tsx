import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";
import {
  Search,
  RefreshCw,
  Edit,
  Trash,
  Plus,
  Percent,
  Calendar,
  Banknote,
  Ticket,
  Check,
  AlarmClock
} from "lucide-react";
import { Admin } from "@/components/layouts/admin-layout";
import { useAuth } from "@/hooks/use-auth";
import { apiRequest } from "@/lib/queryClient";
import { queryClient } from "@/lib/queryClient";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { formatCurrency, formatDate } from "@/lib/utils";
import { z } from "zod";
import { Coupon as CouponType } from "@shared/schema";

// สร้าง Schema สำหรับคูปอง
const couponSchema = z.object({
  code: z.string().min(3, "รหัสคูปองต้องมีอย่างน้อย 3 ตัวอักษร").max(20, "รหัสคูปองต้องไม่เกิน 20 ตัวอักษร"),
  discountPercent: z.coerce.number().min(0, "เปอร์เซ็นต์ส่วนลดต้องไม่ต่ำกว่า 0").max(100, "เปอร์เซ็นต์ส่วนลดต้องไม่เกิน 100%"),
  discountAmount: z.coerce.number().min(0, "จำนวนเงินส่วนลดต้องไม่ต่ำกว่า 0"),
  maxUsage: z.coerce.number().min(1, "จำนวนการใช้งานต้องไม่ต่ำกว่า 1"),
  startDate: z.string().min(1, "กรุณาระบุวันที่เริ่มต้น"),
  endDate: z.string().min(1, "กรุณาระบุวันที่สิ้นสุด"),
  isActive: z.boolean().default(true),
});

type CouponFormValues = z.infer<typeof couponSchema>;

export default function CouponsManagement() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [search, setSearch] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [selectedCoupon, setSelectedCoupon] = useState<CouponType | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // ฟอร์มสำหรับเพิ่ม/แก้ไขคูปอง
  const couponForm = useForm<CouponFormValues>({
    resolver: zodResolver(couponSchema),
    defaultValues: {
      code: "",
      discountPercent: 0,
      discountAmount: 0,
      maxUsage: 1,
      startDate: new Date().toISOString().split('T')[0],
      endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      isActive: true,
    }
  });

  // ดึงข้อมูลคูปองทั้งหมด
  const { data: coupons, isLoading, refetch } = useQuery<CouponType[]>({
    queryKey: ['/api/admin/coupons'],
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาดในการโหลดข้อมูล",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับเพิ่มคูปองใหม่
  const addCouponMutation = useMutation({
    mutationFn: async (couponData: CouponFormValues) => {
      const res = await apiRequest("POST", "/api/admin/coupons", couponData);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "เพิ่มคูปองสำเร็จ",
        description: "คูปองใหม่ถูกเพิ่มเข้าสู่ระบบเรียบร้อยแล้ว",
      });
      setIsAddDialogOpen(false);
      couponForm.reset();
      queryClient.invalidateQueries({ queryKey: ['/api/admin/coupons'] });
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับอัปเดตคูปอง
  const updateCouponMutation = useMutation({
    mutationFn: async (couponData: CouponFormValues & { id: number }) => {
      const { id, ...data } = couponData;
      const res = await apiRequest("PATCH", `/api/admin/coupons/${id}`, data);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "อัปเดตคูปองสำเร็จ",
        description: "ข้อมูลคูปองถูกอัปเดตเรียบร้อยแล้ว",
      });
      setIsEditDialogOpen(false);
      queryClient.invalidateQueries({ queryKey: ['/api/admin/coupons'] });
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับลบคูปอง
  const deleteCouponMutation = useMutation({
    mutationFn: async (id: number) => {
      const res = await apiRequest("DELETE", `/api/admin/coupons/${id}`, {});
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "ลบคูปองสำเร็จ",
        description: "คูปองถูกลบออกจากระบบเรียบร้อยแล้ว",
      });
      setIsDeleteDialogOpen(false);
      queryClient.invalidateQueries({ queryKey: ['/api/admin/coupons'] });
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // กรองข้อมูลคูปองตามเงื่อนไขค้นหาและสถานะ
  const filteredCoupons = coupons?.filter((coupon) => {
    // กรองตามข้อความค้นหา
    const searchMatch =
      search === "" ||
      coupon.code.toLowerCase().includes(search.toLowerCase());

    // กรองตามสถานะ
    const statusMatch = statusFilter === "all" ||
      (statusFilter === "active" && coupon.isActive) ||
      (statusFilter === "inactive" && !coupon.isActive);

    return searchMatch && statusMatch;
  });

  // เปิดไดอะล็อกเพิ่มคูปองใหม่
  const handleOpenAddDialog = () => {
    couponForm.reset({
      code: "",
      discountPercent: 0,
      discountAmount: 0,
      maxUsage: 1,
      startDate: new Date().toISOString().split('T')[0],
      endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      isActive: true,
    });
    setIsAddDialogOpen(true);
  };

  // เปิดไดอะล็อกแก้ไขคูปอง
  const handleOpenEditDialog = (coupon: CouponType) => {
    setSelectedCoupon(coupon);

    // แปลงวันที่เป็นรูปแบบ YYYY-MM-DD
    const startDate = new Date(coupon.startDate).toISOString().split('T')[0];
    const endDate = new Date(coupon.endDate).toISOString().split('T')[0];

    couponForm.reset({
      code: coupon.code,
      discountPercent: coupon.discountPercent,
      discountAmount: coupon.discountAmount,
      maxUsage: coupon.maxUsage,
      startDate,
      endDate,
      isActive: coupon.isActive,
    });
    setIsEditDialogOpen(true);
  };

  // เปิดไดอะล็อกลบคูปอง
  const handleOpenDeleteDialog = (coupon: CouponType) => {
    setSelectedCoupon(coupon);
    setIsDeleteDialogOpen(true);
  };

  // บันทึกคูปองใหม่
  const onSubmitAddForm = (values: CouponFormValues) => {
    addCouponMutation.mutate(values);
  };

  // บันทึกคูปองที่แก้ไข
  const onSubmitEditForm = (values: CouponFormValues) => {
    if (!selectedCoupon) return;
    updateCouponMutation.mutate({
      ...values,
      id: selectedCoupon.id
    });
  };

  // คำนวณว่าคูปองหมดอายุหรือยัง
  const isCouponExpired = (endDate: string) => {
    return new Date(endDate) < new Date();
  };

  return (
    <Admin>
      <div className="space-y-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-indigo-600">จัดการคูปองส่วนลด</h1>
            <div className="lightning-bar w-24 my-1"></div>
            <p className="text-purple-600/70">
              ดูและจัดการคูปองส่วนลดทั้งหมดในระบบ
            </p>
          </div>
          <Button
            onClick={handleOpenAddDialog}
            className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white"
          >
            <Plus className="h-4 w-4 mr-2" />
            เพิ่มคูปองใหม่
          </Button>
        </div>

        <Card className="border border-purple-200 shadow-md">
          <CardHeader className="border-b border-purple-100 bg-gradient-to-r from-purple-50 to-purple-100/50">
            <CardTitle className="text-purple-700 flex items-center">
              <div className="p-1.5 rounded-full bg-gradient-to-r from-purple-100 to-purple-200 mr-2 flex items-center justify-center">
                <Ticket className="h-5 w-5 text-purple-600" />
              </div>
              รายการคูปองส่วนลด
            </CardTitle>
            <div className="lightning-bar w-20 my-1"></div>
            <CardDescription className="text-purple-600/70">
              จัดการรายการคูปองทั้งหมดในระบบ แก้ไข เปลี่ยนสถานะ หรือลบคูปอง
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* ส่วนกรองและค้นหา */}
              <div className="flex flex-col gap-4 md:flex-row">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="ค้นหาคูปอง..."
                    className="pl-10"
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                  />
                </div>
                <Select
                  value={statusFilter}
                  onValueChange={setStatusFilter}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="สถานะ" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">ทั้งหมด</SelectItem>
                    <SelectItem value="active">ใช้งาน</SelectItem>
                    <SelectItem value="inactive">ไม่ได้ใช้งาน</SelectItem>
                  </SelectContent>
                </Select>
                <Button variant="outline" className="h-10 px-4 py-2" onClick={() => refetch()}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  รีเฟรช
                </Button>
              </div>

              {/* ตารางแสดงข้อมูล */}
              {isLoading ? (
                <div className="space-y-4">
                  {[...Array(3)].map((_, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <Skeleton className="h-6 w-1/4" />
                      <Skeleton className="h-6 w-1/5" />
                      <Skeleton className="h-6 w-1/6" />
                      <Skeleton className="h-6 w-1/6" />
                    </div>
                  ))}
                </div>
              ) : filteredCoupons && filteredCoupons.length > 0 ? (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>รหัสคูปอง</TableHead>
                        <TableHead>ส่วนลด</TableHead>
                        <TableHead>วันที่ใช้งาน</TableHead>
                        <TableHead>จำนวนการใช้</TableHead>
                        <TableHead>สถานะ</TableHead>
                        <TableHead className="text-right">การจัดการ</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredCoupons.map((coupon) => (
                        <TableRow key={coupon.id} className={isCouponExpired(coupon.endDate) ? "opacity-60" : ""}>
                          <TableCell>
                            <div className="font-medium">{coupon.code}</div>
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-col">
                              {coupon.discountPercent > 0 && (
                                <div className="flex items-center">
                                  <Percent className="h-3.5 w-3.5 mr-1 text-green-500" />
                                  <span>{coupon.discountPercent}%</span>
                                </div>
                              )}
                              {coupon.discountAmount > 0 && (
                                <div className="flex items-center">
                                  <Banknote className="h-3.5 w-3.5 mr-1 text-blue-500" />
                                  <span>{formatCurrency(coupon.discountAmount)}</span>
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-col text-xs space-y-1">
                              <div className="flex items-center">
                                <Calendar className="h-3.5 w-3.5 mr-1 text-gray-400" />
                                <span>{formatDate(new Date(coupon.startDate), "d MMM yyyy")}</span>
                              </div>
                              <div className="flex items-center">
                                <AlarmClock className="h-3.5 w-3.5 mr-1 text-gray-400" />
                                <span>{formatDate(new Date(coupon.endDate), "d MMM yyyy")}</span>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Check className="h-3.5 w-3.5 mr-1 text-gray-400" />
                              <span>{coupon.usageCount} / {coupon.maxUsage}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            {coupon.isActive ? (
                              isCouponExpired(coupon.endDate) ? (
                                <Badge className="bg-orange-50 text-orange-700 border-orange-200">หมดอายุ</Badge>
                              ) : (
                                <Badge className="bg-green-50 text-green-700 border-green-200">ใช้งาน</Badge>
                              )
                            ) : (
                              <Badge className="bg-red-50 text-red-700 border-red-200">ไม่ได้ใช้งาน</Badge>
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="flex justify-end gap-2">
                              <Button variant="ghost" size="icon" onClick={() => handleOpenEditDialog(coupon)}>
                                <Edit className="h-4 w-4" />
                                <span className="sr-only">แก้ไข</span>
                              </Button>
                              <Button variant="ghost" size="icon" onClick={() => handleOpenDeleteDialog(coupon)}>
                                <Trash className="h-4 w-4" />
                                <span className="sr-only">ลบ</span>
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="py-8 text-center">
                  <p className="text-muted-foreground">ไม่พบข้อมูลคูปอง</p>
                  <Button className="mt-4" onClick={handleOpenAddDialog}>
                    <Plus className="h-4 w-4 mr-2" />
                    เพิ่มคูปองแรก
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* ไดอะล็อกเพิ่มคูปองใหม่ */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[550px]">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Ticket className="mr-2 h-5 w-5" />
              เพิ่มคูปองส่วนลดใหม่
            </DialogTitle>
            <DialogDescription>
              กรอกข้อมูลเพื่อสร้างคูปองส่วนลดใหม่ในระบบ
            </DialogDescription>
          </DialogHeader>
          <Form {...couponForm}>
            <form onSubmit={couponForm.handleSubmit(onSubmitAddForm)} className="space-y-4">
              <FormField
                control={couponForm.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>รหัสคูปอง</FormLabel>
                    <FormControl>
                      <Input placeholder="รหัสคูปอง เช่น SUMMER2023" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={couponForm.control}
                  name="discountPercent"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>ส่วนลด (%)</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Percent className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                          <Input
                            type="number"
                            min="0"
                            max="100"
                            placeholder="ส่วนลด %"
                            className="pl-10"
                            {...field}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={couponForm.control}
                  name="discountAmount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>ส่วนลด (บาท)</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Banknote className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                          <Input
                            type="number"
                            min="0"
                            placeholder="ส่วนลด (บาท)"
                            className="pl-10"
                            {...field}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={couponForm.control}
                name="maxUsage"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>จำนวนการใช้งานสูงสุด</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        placeholder="จำนวนการใช้งานสูงสุด"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={couponForm.control}
                  name="startDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>วันที่เริ่มต้น</FormLabel>
                      <FormControl>
                        <Input
                          type="date"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={couponForm.control}
                  name="endDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>วันที่สิ้นสุด</FormLabel>
                      <FormControl>
                        <Input
                          type="date"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={couponForm.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">สถานะการใช้งาน</FormLabel>
                      <FormDescription>
                        กำหนดว่าจะเปิดใช้งานคูปองนี้ให้ลูกค้าสามารถใช้ได้หรือไม่
                      </FormDescription>
                    </div>
                    <FormControl>
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                        <span className={field.value ? "text-green-600" : "text-red-600"}>
                          {field.value ? "เปิดใช้งาน" : "ปิดใช้งาน"}
                        </span>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsAddDialogOpen(false)}
                >
                  ยกเลิก
                </Button>
                <Button
                  type="submit"
                  disabled={addCouponMutation.isPending}
                >
                  {addCouponMutation.isPending ? "กำลังบันทึก..." : "บันทึก"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* ไดอะล็อกแก้ไขคูปอง */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[550px]">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Edit className="mr-2 h-5 w-5" />
              แก้ไขคูปอง
            </DialogTitle>
            <DialogDescription>
              แก้ไขข้อมูลของคูปอง {selectedCoupon?.code}
            </DialogDescription>
          </DialogHeader>
          <Form {...couponForm}>
            <form onSubmit={couponForm.handleSubmit(onSubmitEditForm)} className="space-y-4">
              <FormField
                control={couponForm.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>รหัสคูปอง</FormLabel>
                    <FormControl>
                      <Input placeholder="รหัสคูปอง เช่น SUMMER2023" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={couponForm.control}
                  name="discountPercent"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>ส่วนลด (%)</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Percent className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                          <Input
                            type="number"
                            min="0"
                            max="100"
                            placeholder="ส่วนลด %"
                            className="pl-10"
                            {...field}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={couponForm.control}
                  name="discountAmount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>ส่วนลด (บาท)</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Banknote className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                          <Input
                            type="number"
                            min="0"
                            placeholder="ส่วนลด (บาท)"
                            className="pl-10"
                            {...field}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={couponForm.control}
                name="maxUsage"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>จำนวนการใช้งานสูงสุด</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        placeholder="จำนวนการใช้งานสูงสุด"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={couponForm.control}
                  name="startDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>วันที่เริ่มต้น</FormLabel>
                      <FormControl>
                        <Input
                          type="date"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={couponForm.control}
                  name="endDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>วันที่สิ้นสุด</FormLabel>
                      <FormControl>
                        <Input
                          type="date"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={couponForm.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">สถานะการใช้งาน</FormLabel>
                      <FormDescription>
                        กำหนดว่าจะเปิดใช้งานคูปองนี้ให้ลูกค้าสามารถใช้ได้หรือไม่
                      </FormDescription>
                    </div>
                    <FormControl>
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                        <span className={field.value ? "text-green-600" : "text-red-600"}>
                          {field.value ? "เปิดใช้งาน" : "ปิดใช้งาน"}
                        </span>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsEditDialogOpen(false)}
                >
                  ยกเลิก
                </Button>
                <Button
                  type="submit"
                  disabled={updateCouponMutation.isPending}
                >
                  {updateCouponMutation.isPending ? "กำลังบันทึก..." : "บันทึก"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* ไดอะล็อกยืนยันลบคูปอง */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>คุณต้องการลบคูปองนี้ใช่หรือไม่?</AlertDialogTitle>
            <AlertDialogDescription>
              คุณกำลังจะลบคูปอง "{selectedCoupon?.code}"
              การกระทำนี้ไม่สามารถย้อนกลับได้ และหากคูปองนี้เคยถูกใช้งานแล้ว อาจส่งผลกระทบต่อข้อมูลในระบบ
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>ยกเลิก</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (selectedCoupon) {
                  deleteCouponMutation.mutate(selectedCoupon.id);
                }
              }}
              className="bg-red-600 hover:bg-red-700"
            >
              {deleteCouponMutation.isPending ? "กำลังลบ..." : "ลบคูปอง"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Admin>
  );
}