// ต้องใช้ import เพราะเป็น ES modules
import jsQR from 'jsqr';
// @ts-ignore เนื่องจาก Jimp มีปัญหากับ TypeScript
import * as JimpModule from 'jimp';
const Jimp = JimpModule;

// แก้ไขปัญหา TypeScript โดยการประกาศ type
declare module 'jsqr';

/**
 * แปลง Buffer เป็น RGBA data array สำหรับใช้กับ jsQR
 */
async function bufferToImageData(buffer: Buffer): Promise<{ data: Uint8ClampedArray, width: number, height: number } | null> {
  try {
    const image = await Jimp.read(buffer);
    const width = image.getWidth();
    const height = image.getHeight();
    
    // ถ้าขนาดภาพใหญ่เกินไป จะย่อขนาดลงให้เหมาะสม
    if (width > 1000 || height > 1000) {
      image.resize(1000, Jimp.AUTO);
    }
    
    // แปลงภาพเป็น RGBA array
    const newWidth = image.getWidth();
    const newHeight = image.getHeight();
    const rgbaData = new Uint8ClampedArray(newWidth * newHeight * 4);
    
    // กรองข้อมูลภาพเป็น RGBA array
    let i = 0;
    // @ts-ignore เพื่อแก้ปัญหา TypeScript ใน Jimp scan
    image.scan(0, 0, newWidth, newHeight, function(x: any, y: any, idx: any) {
      // @ts-ignore จำเป็นต้องใช้ `this` ที่มีประเภทเป็น any
      const red = this.bitmap.data[idx + 0];
      // @ts-ignore จำเป็นต้องใช้ `this` ที่มีประเภทเป็น any
      const green = this.bitmap.data[idx + 1];
      // @ts-ignore จำเป็นต้องใช้ `this` ที่มีประเภทเป็น any
      const blue = this.bitmap.data[idx + 2];
      // @ts-ignore จำเป็นต้องใช้ `this` ที่มีประเภทเป็น any
      const alpha = this.bitmap.data[idx + 3];
      
      rgbaData[i++] = red;
      rgbaData[i++] = green;
      rgbaData[i++] = blue;
      rgbaData[i++] = alpha;
    });
    
    return {
      data: rgbaData,
      width: newWidth,
      height: newHeight
    };
  } catch (error) {
    console.error('Error converting buffer to image data:', error);
    return null;
  }
}

/**
 * ตรวจสอบและดึงข้อมูล QR Code จากรูปภาพ
 * 
 * @param imageBuffer Buffer ของรูปภาพที่ต้องการตรวจสอบ
 * @returns Promise<{ hasQRCode: boolean; qrData?: string }> ผลการตรวจสอบ QR Code
 */
export async function detectQRCode(imageBuffer: Buffer): Promise<{ hasQRCode: boolean; qrData?: string }> {
  try {
    // ตรวจสอบว่าเป็นไฟล์รูปภาพที่ถูกต้องหรือไม่
    const isJPEG = imageBuffer.slice(0, 3).toString('hex') === 'ffd8ff';
    const isPNG = imageBuffer.slice(0, 8).toString('hex') === '89504e470d0a1a0a';
    
    if (!isJPEG && !isPNG) {
      console.log('Not a valid image file (JPEG/PNG)');
      return { hasQRCode: false };
    }
    
    // แปลง buffer เป็นรูปแบบที่ jsQR สามารถใช้งานได้
    const imageData = await bufferToImageData(imageBuffer);
    
    if (!imageData) {
      console.log('Could not process image data');
      return { hasQRCode: false };
    }
    
    // ตรวจสอบ QR code ด้วย jsQR
    const code = jsQR(imageData.data, imageData.width, imageData.height);
    
    if (!code) {
      console.log('No QR code found in image');
      return { hasQRCode: false };
    }
    
    console.log('Found QR code in image with data:', code.data);
    return {
      hasQRCode: true,
      qrData: code.data
    };
  } catch (error) {
    console.error('Error in detectQRCode:', error);
    return { hasQRCode: false };
  }
}

/**
 * ตรวจสอบว่ารูปภาพมี QR Code หรือไม่
 * 
 * @param imageBuffer Buffer ของรูปภาพที่ต้องการตรวจสอบ
 * @returns Promise<boolean> true ถ้าพบ QR Code, false ถ้าไม่พบ
 */
export async function hasQRCode(imageBuffer: Buffer): Promise<boolean> {
  const result = await detectQRCode(imageBuffer);
  return result.hasQRCode;
}