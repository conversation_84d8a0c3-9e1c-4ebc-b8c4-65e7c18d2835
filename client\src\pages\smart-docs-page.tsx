import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { useMutation } from "@tanstack/react-query";

import {
  RefreshCw, Search, BookOpen, MessageSquare, Code, Check, Cpu, Zap,
  FileQuestion, HelpCircle, Sparkles, Copy, ExternalLink, ThumbsUp,
  ThumbsDown, SkipForward, Bookmark, Star, Send
} from "lucide-react";

import { DashboardLayout } from "@/components/layouts/dashboard-responsive-new";
import { CodeBlock, a11yDark } from "react-code-blocks";

// ตัวอย่างข้อมูลคำถามที่พบบ่อย
const faqData = [
  {
    id: 1,
    question: "การเริ่มต้นใช้งาน API ทำอย่างไร?",
    answer: "เพื่อเริ่มต้นใช้งาน API ทำตามขั้นตอนนี้:\n1. สร้าง API key ที่หน้า API Keys\n2. เลือกแพ็คเกจที่เหมาะสมกับความต้องการของคุณ\n3. ดูตัวอย่างการใช้งานในเอกสาร API\n4. ทดสอบ API key ของคุณก่อนนำไปใช้งานจริง",
    category: "การเริ่มต้น",
    rating: 4.8,
    views: 1250
  },
  {
    id: 2,
    question: "ทำไมรูปสลิปไม่ผ่านการตรวจสอบ?",
    answer: "รูปสลิปอาจไม่ผ่านการตรวจสอบด้วยเหตุผลต่อไปนี้:\n1. คุณภาพรูปภาพต่ำเกินไป (แนะนำความละเอียด 300 DPI ขึ้นไป)\n2. QR โค้ดหรือข้อมูลสำคัญถูกตัดออกไป\n3. รูปภาพมีการแก้ไขหรือดัดแปลง\n4. สลิปหมดอายุ (เกิน 180 วัน)\n5. รูปแบบไฟล์ไม่รองรับ (รองรับเฉพาะ JPG, PNG และ PDF)",
    category: "การแก้ไขปัญหา",
    rating: 4.5,
    views: 980
  },
  {
    id: 3,
    question: "จะเชื่อมต่อ API กับ LINE Messaging API ได้อย่างไร?",
    answer: "คุณสามารถเชื่อมต่อ API ของเรากับ LINE Messaging API ได้โดยใช้คุณสมบัติ Webhook\n1. ไปที่หน้า Webhook ในแดชบอร์ด\n2. สร้าง webhook ใหม่ โดยใส่ URL ของ LINE Messaging API webhook\n3. เลือกเหตุการณ์ที่ต้องการให้ส่งข้อมูลไปยัง LINE\n4. ทดสอบการเชื่อมต่อก่อนใช้งานจริง\n\nสำหรับตัวอย่างโค้ดการรับข้อมูลจาก webhook ในฝั่ง LINE ดูได้ที่ส่วนตัวอย่างโค้ด",
    category: "การเชื่อมต่อ",
    rating: 4.7,
    views: 750
  },
  {
    id: 4,
    question: "มีวิธีการอัพเกรดแพ็คเกจแบบไม่ต้องหยุดบริการหรือไม่?",
    answer: "ใช่ คุณสามารถอัพเกรดแพ็คเกจได้ทุกเมื่อโดยไม่ต้องหยุดบริการ\n1. ไปที่หน้าแพ็คเกจในแดชบอร์ด\n2. เลือกแพ็คเกจที่ต้องการอัพเกรด\n3. หากมีแพ็คเกจที่ใช้งานอยู่ ระบบจะคำนวณส่วนลดให้ตามระยะเวลาที่เหลือ\n4. หลังจากอัพเกรด คุณจะได้ใช้ความสามารถใหม่ทันที",
    category: "แพ็คเกจและการชำระเงิน",
    rating: 4.6,
    views: 540
  },
  {
    id: 5,
    question: "จะกรองเฉพาะธุรกรรมที่มียอดเงินมากกว่า 5,000 บาทได้อย่างไร?",
    answer: "คุณสามารถกรองธุรกรรมที่มียอดเงินมากกว่า 5,000 บาทได้โดย:\n1. ใช้ตัวกรองเงื่อนไขใน webhook โดยตั้งค่า minAmount เป็น 5000\n2. ใช้การค้นหาขั้นสูงในหน้าค้นหาธุรกรรมและระบุ 'จำนวนเงิน > 5000'\n3. หรือกรองผ่าน API โดยใช้พารามิเตอร์ minAmount=5000 ในการเรียก API",
    category: "การใช้งานขั้นสูง",
    rating: 4.9,
    views: 320
  }
];

// ตัวอย่างข้อมูลเอกสาร API
const apiDocsData = [
  {
    id: 1,
    title: "การตรวจสอบสลิป",
    endpoint: "/api/v1/verify-slip",
    method: "POST",
    description: "เป็น API หลักที่ใช้ในการตรวจสอบความถูกต้องของสลิปการโอนเงิน",
    params: [
      { name: "image", type: "File", description: "ไฟล์รูปภาพสลิป (รองรับ JPG, PNG, PDF)" },
      { name: "reference", type: "String", description: "รหัสอ้างอิงสำหรับติดตาม (Optional)" }
    ],
    returns: {
      success: true,
      code: "200000",
      message: "ตรวจสอบสลิปสำเร็จ",
      data: {
        transactionId: "TRX123456789",
        amount: 1500.00,
        date: "2023-04-15T14:30:25+07:00",
        sender: {
          bank: "KBANK",
          accountNumber: "xxx-x-x9876-x",
          name: "นายตัวอย่าง ข้อมูล"
        },
        receiver: {
          bank: "SCB",
          accountNumber: "xxx-x-x1234-x",
          name: "บริษัทตัวอย่าง จำกัด"
        },
        isVerified: true
      }
    },
    sampleCode: {
      js: `// ตัวอย่างการเรียกใช้ด้วย axios
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

async function verifySlip() {
  try {
    const form = new FormData();
    form.append('image', fs.createReadStream('./slip.jpg'));
    form.append('reference', 'REF123456');

    const response = await axios.post('https://api.slipkuy.com/api/v1/verify-slip', form, {
      headers: {
        ...form.getHeaders(),
        'Authorization': 'Bearer YOUR_API_KEY',
      },
    });

    console.log(response.data);
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

verifySlip();`,
      php: `<?php
// ตัวอย่างการเรียกใช้ด้วย cURL ใน PHP
$apiKey = 'YOUR_API_KEY';
$url = 'https://api.slipkuy.com/api/v1/verify-slip';

$curl = curl_init();
$cfile = curl_file_create('./slip.jpg', 'image/jpeg', 'slip.jpg');

curl_setopt_array($curl, [
  CURLOPT_URL => $url,
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_POST => true,
  CURLOPT_POSTFIELDS => [
    'image' => $cfile,
    'reference' => 'REF123456'
  ],
  CURLOPT_HTTPHEADER => [
    'Authorization: Bearer ' . $apiKey
  ],
]);

$response = curl_exec($curl);
$err = curl_error($curl);

curl_close($curl);

if ($err) {
  echo "Error: " . $err;
} else {
  echo $response;
}
?>`
    }
  },
  {
    id: 2,
    title: "ประวัติการตรวจสอบ",
    endpoint: "/api/verification-history",
    method: "GET",
    description: "ดึงประวัติการตรวจสอบสลิปทั้งหมด",
    params: [
      { name: "limit", type: "Number", description: "จำนวนรายการที่ต้องการดึง (ค่าเริ่มต้น: 20)" },
      { name: "offset", type: "Number", description: "จำนวนรายการที่ต้องการข้าม (ค่าเริ่มต้น: 0)" },
      { name: "startDate", type: "Date", description: "วันที่เริ่มต้น (format: YYYY-MM-DD)" },
      { name: "endDate", type: "Date", description: "วันที่สิ้นสุด (format: YYYY-MM-DD)" }
    ],
    returns: {
      success: true,
      code: "200000",
      message: "ดึงประวัติการตรวจสอบสำเร็จ",
      data: {
        total: 150,
        items: [
          {
            id: "V123456789",
            transactionId: "TRX123456789",
            date: "2023-04-15T14:30:25+07:00",
            amount: 1500.00,
            status: "verified",
            sender: "KBANK xxx-x-x9876-x",
            receiver: "SCB xxx-x-x1234-x"
          }
        ]
      }
    },
    sampleCode: {
      js: `// ตัวอย่างการเรียกใช้ด้วย axios
const axios = require('axios');

async function getVerificationHistory() {
  try {
    const response = await axios.get('https://api.slipkuy.com/api/verification-history', {
      params: {
        limit: 10,
        offset: 0,
        startDate: '2023-04-01',
        endDate: '2023-04-30'
      },
      headers: {
        'Authorization': 'Bearer YOUR_API_KEY',
      },
    });

    console.log(response.data);
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

getVerificationHistory();`,
      php: `<?php
// ตัวอย่างการเรียกใช้ด้วย cURL ใน PHP
$apiKey = 'YOUR_API_KEY';
$url = 'https://api.slipkuy.com/api/verification-history?limit=10&offset=0&startDate=2023-04-01&endDate=2023-04-30';

$curl = curl_init();

curl_setopt_array($curl, [
  CURLOPT_URL => $url,
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_HTTPHEADER => [
    'Authorization: Bearer ' . $apiKey
  ],
]);

$response = curl_exec($curl);
$err = curl_error($curl);

curl_close($curl);

if ($err) {
  echo "Error: " . $err;
} else {
  echo $response;
}
?>`
    }
  }
];

// ตัวอย่างลำดับการสนทนากับ AI
const initialConversation = [
  {
    role: "assistant",
    content: "สวัสดีครับ ผมคือผู้ช่วยอัจฉริยะของระบบ SLIPKUY ยินดีให้ความช่วยเหลือเกี่ยวกับการใช้งานระบบ มีคำถามอะไรเกี่ยวกับ API หรือการใช้งานไหมครับ?",
    timestamp: new Date().toISOString(),
  }
];

// ประเภทของ API
type CodeLanguage = "js" | "php";

export default function SmartDocsPage() {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("docs");
  const [searchQuery, setSearchQuery] = useState("");
  const [conversation, setConversation] = useState(initialConversation);
  const [newMessage, setNewMessage] = useState("");
  const [selectedLanguage, setSelectedLanguage] = useState<CodeLanguage>("js");
  const [isTyping, setIsTyping] = useState(false);
  const [detailDoc, setDetailDoc] = useState<any>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // กรองเอกสาร FAQ ตามคำค้นหา
  const filteredFaqs = faqData.filter(faq =>
    searchQuery === "" ||
    faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
    faq.answer.toLowerCase().includes(searchQuery.toLowerCase()) ||
    faq.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // กรองเอกสาร API ตามคำค้นหา
  const filteredApiDocs = apiDocsData.filter(doc =>
    searchQuery === "" ||
    doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    doc.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    doc.endpoint.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // คัดลอกโค้ดตัวอย่าง
  const copyCode = (code: string) => {
    navigator.clipboard.writeText(code).then(() => {
      toast({
        title: "คัดลอกโค้ดแล้ว",
        description: "โค้ดตัวอย่างถูกคัดลอกไปยังคลิปบอร์ดแล้ว",
      });
    });
  };

  // เปิดเอกสาร API ในโหมดรายละเอียด
  const openApiDetail = (doc: any) => {
    setDetailDoc(doc);
  };

  // ปิดเอกสาร API ในโหมดรายละเอียด
  const closeApiDetail = () => {
    setDetailDoc(null);
  };

  // ส่งคำถามไปยัง AI
  const sendMessage = async () => {
    if (!newMessage.trim()) return;

    setIsSubmitting(true);

    // เพิ่มข้อความของผู้ใช้เข้าไปในการสนทนา
    const userMessage = {
      role: "user",
      content: newMessage,
      timestamp: new Date().toISOString(),
    };

    setConversation(prev => [...prev, userMessage]);
    setNewMessage("");
    setIsTyping(true);

    try {
      // ส่งคำถามไปยัง API
      const response = await apiRequest('POST', '/api/analytics/ai/query', { query: newMessage });
      const data = await response.json();

      // หน่วงเวลาเล็กน้อยเพื่อให้ดูเป็นธรรมชาติ
      setTimeout(() => {
        // เพิ่มคำตอบจาก AI เข้าไปในการสนทนา
        const aiMessage = {
          role: "assistant",
          content: data.data.answer,
          timestamp: new Date().toISOString(),
        };

        setConversation(prev => [...prev, aiMessage]);
        setIsTyping(false);
        setIsSubmitting(false);
      }, 1000);
    } catch (error) {
      console.error('Error sending message:', error);

      // กรณีเกิดข้อผิดพลาด
      const errorMessage = {
        role: "assistant",
        content: "ขออภัย เกิดข้อผิดพลาดในการประมวลผลคำถามของคุณ กรุณาลองใหม่อีกครั้งในภายหลัง",
        timestamp: new Date().toISOString(),
      };

      setConversation(prev => [...prev, errorMessage]);
      setIsTyping(false);
      setIsSubmitting(false);
    }
  };

  // Mutation สำหรับส่งคำถามไปยัง AI
  const sendMessageMutation = useMutation({
    mutationFn: async (message: string) => {
      const response = await apiRequest('POST', '/api/analytics/ai/query', { query: message });
      return response.json();
    },
    onMutate: (message) => {
      // เพิ่มข้อความของผู้ใช้เข้าไปในการสนทนา
      const userMessage = {
        role: "user",
        content: message,
        timestamp: new Date().toISOString(),
      };

      setConversation(prev => [...prev, userMessage]);
      setNewMessage("");
      setIsTyping(true);
    },
    onSuccess: (data) => {
      // หน่วงเวลาเล็กน้อยเพื่อให้ดูเป็นธรรมชาติ
      setTimeout(() => {
        // เพิ่มคำตอบจาก AI เข้าไปในการสนทนา
        const aiMessage = {
          role: "assistant",
          content: data.data.answer,
          timestamp: new Date().toISOString(),
        };

        setConversation(prev => [...prev, aiMessage]);
        setIsTyping(false);
      }, 1000);
    },
    onError: (error) => {
      console.error('Error sending message:', error);

      // กรณีเกิดข้อผิดพลาด
      const errorMessage = {
        role: "assistant",
        content: "ขออภัย เกิดข้อผิดพลาดในการประมวลผลคำถามของคุณ กรุณาลองใหม่อีกครั้งในภายหลัง",
        timestamp: new Date().toISOString(),
      };

      setConversation(prev => [...prev, errorMessage]);
      setIsTyping(false);
    }
  });

  // ฟังก์ชันส่งข้อความใหม่
  const handleSendMessage = () => {
    if (!newMessage.trim()) return;
    sendMessageMutation.mutate(newMessage);
  };

  // เลื่อนไปที่ข้อความล่าสุดเมื่อมีการเพิ่มข้อความใหม่
  useEffect(() => {
    const chatContainer = document.getElementById('chat-container');
    if (chatContainer) {
      chatContainer.scrollTop = chatContainer.scrollHeight;
    }
  }, [conversation, isTyping]);

  return (
    <DashboardLayout>
      <div className="container py-6">
        <div className="flex flex-col space-y-2 mb-6">
          <h1 className="text-3xl font-bold tracking-tight text-white">เอกสารและความช่วยเหลือ</h1>
          <p className="text-slate-400">
            เอกสารอ้างอิง API, คำถามที่พบบ่อย และความช่วยเหลือแบบอัจฉริยะสำหรับระบบ SLIPKUY
          </p>
        </div>

        <div className="w-full mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
            <Input
              placeholder="ค้นหาในเอกสารและคำถามที่พบบ่อย..."
              className="pl-10 bg-indigo-950/40 border-indigo-800/40 text-white"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <Tabs defaultValue="docs" value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="bg-indigo-900/50 border border-indigo-800/40">
            <TabsTrigger value="docs" className="data-[state=active]:bg-indigo-700">
              <BookOpen className="h-4 w-4 mr-2" />
              เอกสาร API
            </TabsTrigger>
            <TabsTrigger value="faq" className="data-[state=active]:bg-indigo-700">
              <FileQuestion className="h-4 w-4 mr-2" />
              คำถามที่พบบ่อย
            </TabsTrigger>
            <TabsTrigger value="chat" className="data-[state=active]:bg-indigo-700">
              <MessageSquare className="h-4 w-4 mr-2" />
              ผู้ช่วยอัจฉริยะ
            </TabsTrigger>
          </TabsList>

          {/* แท็บเอกสาร API */}
          <TabsContent value="docs">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {filteredApiDocs.map((doc) => (
                <Card key={doc.id} className="bg-indigo-950/40 border-indigo-800/40 flex flex-col hover:border-indigo-600 transition-all duration-200">
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-white text-xl">{doc.title}</CardTitle>
                      <span className={`px-2 py-1 rounded-md text-xs font-bold
                        ${doc.method === 'GET' ? 'bg-emerald-900/70 text-emerald-300' :
                          doc.method === 'POST' ? 'bg-blue-900/70 text-blue-300' :
                          doc.method === 'PUT' ? 'bg-amber-900/70 text-amber-300' :
                          'bg-red-900/70 text-red-300'}`}>
                        {doc.method}
                      </span>
                    </div>
                    <CardDescription className="text-slate-400 mt-1 text-sm">
                      <code className="px-1.5 py-0.5 rounded bg-indigo-900/50 text-indigo-300 font-mono">
                        {doc.endpoint}
                      </code>
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="flex-grow">
                    <p className="text-slate-300 text-sm">{doc.description}</p>
                  </CardContent>
                  <CardFooter className="pt-0 pb-4">
                    <Button
                      variant="outline"
                      className="w-full border-indigo-700 text-indigo-300 hover:bg-indigo-900"
                      onClick={() => openApiDetail(doc)}
                    >
                      <Code className="h-4 w-4 mr-2" />
                      ดูรายละเอียด
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>

            {/* ถ้าไม่พบเอกสาร API */}
            {filteredApiDocs.length === 0 && (
              <div className="text-center py-10">
                <HelpCircle className="h-12 w-12 text-slate-500 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-white mb-2">ไม่พบเอกสาร API ที่ตรงกับการค้นหา</h3>
                <p className="text-slate-400 max-w-md mx-auto mb-6">
                  ลองใช้คำค้นหาอื่น หรือตรวจสอบการสะกดอีกครั้ง
                </p>
                <Button
                  variant="outline"
                  className="border-indigo-700 text-indigo-300 hover:bg-indigo-900"
                  onClick={() => setSearchQuery("")}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  ล้างการค้นหา
                </Button>
              </div>
            )}
          </TabsContent>

          {/* แท็บคำถามที่พบบ่อย */}
          <TabsContent value="faq">
            <div className="space-y-4">
              {filteredFaqs.map((faq) => (
                <Card key={faq.id} className="bg-indigo-950/40 border-indigo-800/40">
                  <CardHeader className="pb-2">
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-white text-xl">{faq.question}</CardTitle>
                        <div className="flex items-center mt-1 space-x-2">
                          <span className="px-2 py-0.5 rounded-md text-xs bg-indigo-900/70 text-indigo-300">
                            {faq.category}
                          </span>
                          <div className="flex items-center text-amber-400">
                            <Star className="h-3.5 w-3.5" />
                            <span className="ml-1 text-xs text-slate-300">{faq.rating.toFixed(1)}</span>
                          </div>
                          <span className="text-xs text-slate-400">{faq.views} ครั้ง</span>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-slate-300 whitespace-pre-line">{faq.answer}</div>
                  </CardContent>
                  <CardFooter className="flex justify-between pt-2">
                    <div className="flex space-x-2">
                      <Button variant="ghost" size="sm" className="text-slate-400 hover:text-green-400 hover:bg-green-900/20">
                        <ThumbsUp className="h-4 w-4 mr-1" />
                        มีประโยชน์
                      </Button>
                      <Button variant="ghost" size="sm" className="text-slate-400 hover:text-red-400 hover:bg-red-900/20">
                        <ThumbsDown className="h-4 w-4 mr-1" />
                        ไม่มีประโยชน์
                      </Button>
                    </div>
                    <Button variant="ghost" size="sm" className="text-slate-400 hover:text-amber-400">
                      <Bookmark className="h-4 w-4 mr-1" />
                      บันทึก
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>

            {/* ถ้าไม่พบคำถามที่พบบ่อย */}
            {filteredFaqs.length === 0 && (
              <div className="text-center py-10">
                <HelpCircle className="h-12 w-12 text-slate-500 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-white mb-2">ไม่พบคำถามที่ตรงกับการค้นหา</h3>
                <p className="text-slate-400 max-w-md mx-auto mb-6">
                  ลองใช้คำค้นหาอื่น หรือคุณสามารถถามคำถามโดยตรงกับผู้ช่วยอัจฉริยะของเรา
                </p>
                <div className="flex justify-center space-x-3">
                  <Button
                    variant="outline"
                    className="border-indigo-700 text-indigo-300 hover:bg-indigo-900"
                    onClick={() => setSearchQuery("")}
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    ล้างการค้นหา
                  </Button>
                  <Button
                    variant="default"
                    className="bg-indigo-700 hover:bg-indigo-600"
                    onClick={() => setActiveTab("chat")}
                  >
                    <MessageSquare className="h-4 w-4 mr-2" />
                    ถามผู้ช่วยอัจฉริยะ
                  </Button>
                </div>
              </div>
            )}
          </TabsContent>

          {/* แท็บผู้ช่วยอัจฉริยะ */}
          <TabsContent value="chat">
            <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
              {/* กล่องข้อความแนะนำ (แสดงในหน้าจอใหญ่) */}
              <div className="hidden lg:flex lg:col-span-2 flex-col space-y-4">
                <Card className="bg-indigo-950/40 border-indigo-800/40">
                  <CardHeader>
                    <CardTitle className="text-white">คำถามที่แนะนำ</CardTitle>
                    <CardDescription className="text-slate-400">
                      เลือกคำถามที่คุณสนใจหรือพิมพ์คำถามของคุณเอง
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {faqData.slice(0, 3).map((faq, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        className="w-full border-indigo-700 text-indigo-300 hover:bg-indigo-900 justify-start h-auto py-3"
                        onClick={() => {
                          setNewMessage(faq.question);
                          setTimeout(() => handleSendMessage(), 100);
                        }}
                      >
                        <Sparkles className="h-4 w-4 mr-2 flex-shrink-0" />
                        <span className="truncate text-left">{faq.question}</span>
                      </Button>
                    ))}
                  </CardContent>
                </Card>

                <Card className="bg-indigo-950/40 border-indigo-800/40">
                  <CardHeader>
                    <CardTitle className="text-white">ความสามารถของผู้ช่วยอัจฉริยะ</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex">
                      <div className="bg-indigo-900/70 rounded-full p-2 mr-3">
                        <MessageSquare className="h-5 w-5 text-indigo-300" />
                      </div>
                      <div>
                        <h4 className="font-medium text-white">ตอบคำถามเกี่ยวกับ API</h4>
                        <p className="text-sm text-slate-400">ให้ข้อมูลเกี่ยวกับการเรียกใช้ API และพารามิเตอร์ต่างๆ</p>
                      </div>
                    </div>

                    <div className="flex">
                      <div className="bg-indigo-900/70 rounded-full p-2 mr-3">
                        <Code className="h-5 w-5 text-indigo-300" />
                      </div>
                      <div>
                        <h4 className="font-medium text-white">ตัวอย่างโค้ด</h4>
                        <p className="text-sm text-slate-400">สร้างตัวอย่างโค้ดในภาษาที่คุณต้องการ</p>
                      </div>
                    </div>

                    <div className="flex">
                      <div className="bg-indigo-900/70 rounded-full p-2 mr-3">
                        <HelpCircle className="h-5 w-5 text-indigo-300" />
                      </div>
                      <div>
                        <h4 className="font-medium text-white">แก้ไขปัญหา</h4>
                        <p className="text-sm text-slate-400">ช่วยวิเคราะห์และแก้ไขปัญหาที่อาจเกิดขึ้น</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* กล่องแชท */}
              <Card className="bg-indigo-950/40 border-indigo-800/40 lg:col-span-3 flex flex-col" style={{ height: "calc(80vh - 100px)" }}>
                <CardHeader className="pb-3">
                  <div className="flex items-center">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-br from-indigo-500 to-purple-700 flex items-center justify-center mr-3">
                      <Cpu className="text-white h-5 w-5" />
                    </div>
                    <div>
                      <CardTitle className="text-white">ผู้ช่วยอัจฉริยะ SLIPKUY</CardTitle>
                      <CardDescription className="text-slate-400">
                        พร้อมช่วยเหลือเกี่ยวกับการใช้งาน API และระบบ
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <Separator className="bg-indigo-800/40" />
                <CardContent className="flex-grow overflow-auto p-0" id="chat-container">
                  <div className="p-4 space-y-4">
                    {conversation.map((message, index) => (
                      <div
                        key={index}
                        className={`flex ${message.role === 'assistant' ? 'justify-start' : 'justify-end'}`}
                      >
                        <div
                          className={`max-w-[90%] md:max-w-[70%] p-3 rounded-lg ${
                            message.role === 'assistant'
                              ? 'bg-indigo-900/40 border border-indigo-800/60 text-white'
                              : 'bg-indigo-700/80 text-white'
                          }`}
                        >
                          <div className="whitespace-pre-line">{message.content}</div>
                          <div className="text-xs mt-1 text-right text-slate-400">
                            {new Date(message.timestamp).toLocaleTimeString()}
                          </div>
                        </div>
                      </div>
                    ))}

                    {isTyping && (
                      <div className="flex justify-start">
                        <div className="max-w-[90%] md:max-w-[70%] p-3 rounded-lg bg-indigo-900/40 border border-indigo-800/60 text-white">
                          <div className="flex space-x-2">
                            <span className="w-3 h-3 bg-indigo-300 rounded-full animate-ping" style={{ animationDuration: '1s' }}></span>
                            <span className="w-3 h-3 bg-indigo-300 rounded-full animate-ping" style={{ animationDuration: '1s', animationDelay: '0.2s' }}></span>
                            <span className="w-3 h-3 bg-indigo-300 rounded-full animate-ping" style={{ animationDuration: '1s', animationDelay: '0.4s' }}></span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
                <Separator className="bg-indigo-800/40" />
                <CardFooter className="p-4">
                  <div className="flex w-full space-x-2">
                    <Input
                      placeholder="พิมพ์คำถามของคุณที่นี่..."
                      className="flex-grow bg-indigo-900/30 border-indigo-800/60 text-white"
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          handleSendMessage();
                        }
                      }}
                      disabled={sendMessageMutation.isPending}
                    />
                    <Button
                      variant="default"
                      className="bg-indigo-700 hover:bg-indigo-600"
                      onClick={handleSendMessage}
                      disabled={sendMessageMutation.isPending}
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                </CardFooter>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Dialog รายละเอียด API */}
      {detailDoc && (
        <Dialog open={!!detailDoc} onOpenChange={(open) => !open && closeApiDetail()}>
          <DialogContent className="bg-indigo-950 border-indigo-800 text-white max-w-4xl">
            <DialogHeader>
              <DialogTitle className="text-2xl font-bold flex items-center">
                <span className={`px-2 py-1 mr-2 rounded-md text-xs font-bold
                  ${detailDoc.method === 'GET' ? 'bg-emerald-900/70 text-emerald-300' :
                    detailDoc.method === 'POST' ? 'bg-blue-900/70 text-blue-300' :
                    detailDoc.method === 'PUT' ? 'bg-amber-900/70 text-amber-300' :
                    'bg-red-900/70 text-red-300'}`}>
                  {detailDoc.method}
                </span>
                {detailDoc.title}
              </DialogTitle>
              <DialogDescription className="text-slate-300 flex items-center mt-1">
                <code className="px-1.5 py-0.5 rounded bg-indigo-900/50 text-indigo-300 font-mono">
                  {detailDoc.endpoint}
                </code>
                <Button
                  variant="ghost"
                  size="sm"
                  className="ml-2 text-slate-400 hover:text-indigo-300"
                  onClick={() => copyCode(detailDoc.endpoint)}
                >
                  <Copy className="h-3.5 w-3.5" />
                </Button>
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4 my-2">
              <div>
                <h3 className="text-white font-semibold mb-2">คำอธิบาย</h3>
                <p className="text-slate-300">{detailDoc.description}</p>
              </div>

              <div>
                <h3 className="text-white font-semibold mb-2">พารามิเตอร์</h3>
                <div className="bg-indigo-900/40 rounded-md overflow-hidden">
                  <Table>
                    <TableHeader>
                      <TableRow className="hover:bg-indigo-900/60 border-indigo-800/40">
                        <TableHead className="text-slate-300">ชื่อ</TableHead>
                        <TableHead className="text-slate-300">ประเภท</TableHead>
                        <TableHead className="text-slate-300">คำอธิบาย</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {detailDoc.params.map((param: any, index: number) => (
                        <TableRow key={index} className="hover:bg-indigo-900/60 border-indigo-800/40">
                          <TableCell className="font-medium text-white">{param.name}</TableCell>
                          <TableCell>
                            <span className="px-1.5 py-0.5 rounded bg-indigo-900/50 text-indigo-300 font-mono text-xs">
                              {param.type}
                            </span>
                          </TableCell>
                          <TableCell className="text-slate-300">{param.description}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>

              <div>
                <h3 className="text-white font-semibold mb-2">ตัวอย่างการตอบกลับ</h3>
                <div className="bg-indigo-900/40 rounded-md p-4 relative">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute top-2 right-2 text-slate-400 hover:text-indigo-300"
                    onClick={() => copyCode(JSON.stringify(detailDoc.returns, null, 2))}
                  >
                    <Copy className="h-3.5 w-3.5" />
                  </Button>
                  <pre className="text-slate-300 overflow-auto max-h-60">
                    {JSON.stringify(detailDoc.returns, null, 2)}
                  </pre>
                </div>
              </div>

              <div>
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-white font-semibold">ตัวอย่างโค้ด</h3>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className={`border-indigo-800/40 ${selectedLanguage === 'js' ? 'bg-indigo-700 text-white' : 'text-slate-400'}`}
                      onClick={() => setSelectedLanguage('js')}
                    >
                      JavaScript
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className={`border-indigo-800/40 ${selectedLanguage === 'php' ? 'bg-indigo-700 text-white' : 'text-slate-400'}`}
                      onClick={() => setSelectedLanguage('php')}
                    >
                      PHP
                    </Button>
                  </div>
                </div>
                <div className="relative">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute top-2 right-2 z-10 text-slate-400 hover:text-indigo-300"
                    onClick={() => copyCode(detailDoc.sampleCode[selectedLanguage])}
                  >
                    <Copy className="h-3.5 w-3.5" />
                  </Button>
                  <div className="rounded-md overflow-hidden">
                    <CodeBlock
                      text={detailDoc.sampleCode[selectedLanguage]}
                      language={selectedLanguage === 'js' ? 'javascript' : 'php'}
                      showLineNumbers
                      theme={a11yDark}
                      wrapLongLines
                      customStyle={{
                        backgroundColor: 'rgba(30, 27, 75, 0.4)',
                        padding: '1rem',
                        borderRadius: '0.375rem',
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>

            <DialogFooter className="flex flex-col sm:flex-row gap-2 justify-start sm:justify-between items-start sm:items-center border-t border-indigo-800/40 pt-4">
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  className="border-indigo-800/40 text-indigo-300 hover:bg-indigo-900"
                  onClick={() => window.open('https://slipkuy.com/docs', '_blank')}
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  เอกสารเต็มรูปแบบ
                </Button>
                <Button
                  variant="outline"
                  className="border-indigo-800/40 text-indigo-300 hover:bg-indigo-900"
                  onClick={() => {
                    setActiveTab("chat");
                    closeApiDetail();
                    setNewMessage(`ช่วยอธิบาย ${detailDoc.title} API เพิ่มเติมหน่อยครับ`);
                    setTimeout(() => handleSendMessage(), 100);
                  }}
                >
                  <MessageSquare className="h-4 w-4 mr-2" />
                  ถามเพิ่มเติม
                </Button>
              </div>
              <Button
                variant="default"
                className="bg-indigo-700 hover:bg-indigo-600 sm:ml-auto"
                onClick={closeApiDetail}
              >
                ปิด
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </DashboardLayout>
  );
}