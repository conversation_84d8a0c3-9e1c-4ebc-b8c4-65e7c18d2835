# shell.nix - สำหรับความเข้ากันได้กับเครื่องมือที่ไม่รองรับ flakes
{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = with pkgs; [
    nodejs_20
    nodePackages.typescript
    postgresql_15
    libuuid
    jq

    # เพิ่มเครื่องมือสำหรับการพัฒนา
    nodePackages.npm
    nodePackages.yarn

    # เพิ่มไลบรารีที่จำเป็นสำหรับการเชื่อมต่อกับ PostgreSQL
    openssl
    postgresql_15.lib
  ];

  shellHook = ''
    echo "🚀 ยินดีต้อนรับสู่สภาพแวดล้อมการพัฒนา SLIPKUY!"
    echo "📦 Node.js: $(node --version)"
    echo "📦 npm: $(npm --version)"
    echo "📦 PostgreSQL: $(psql --version)"

    # ตั้งค่าตัวแปรสภาพแวดล้อมสำหรับฐานข้อมูลในเครื่อง
    export PGHOST=localhost
    export PGUSER=slipkuy_user
    export PGPASSWORD=slipkuy_password
    export PGDATABASE=slipkuy_db
    export PGPORT=5432
    export PGSSLMODE=disable

    # ตั้งค่า DATABASE_URL สำหรับแอพพลิเคชัน
    export DATABASE_URL="postgresql://slipkuy_user:slipkuy_password@localhost:5432/slipkuy_db"

    # ตั้งค่าตัวแปรสภาพแวดล้อมสำหรับ SSL (ไม่จำเป็นสำหรับฐานข้อมูลในเครื่อง)
    # export NODE_TLS_REJECT_UNAUTHORIZED=0
    # export PGSSLMODE=require

    # ตั้งค่าตัวแปรสภาพแวดล้อมสำหรับ pg-native (ถ้าใช้)
    export LD_LIBRARY_PATH=${pkgs.postgresql_15.lib}/lib:$LD_LIBRARY_PATH
    export PGCLIENTENCODING=UTF8

    # ไม่ต้องโหลด .env โดยตรง เพราะแอพพลิเคชันใช้ dotenv อยู่แล้ว
    echo "🔄 แอพพลิเคชันจะโหลดตัวแปรสภาพแวดล้อมจาก .env ด้วย dotenv"

    # สร้างโฟลเดอร์ที่จำเป็น
    mkdir -p public/uploads/slips

    # ติดตั้งแพคเกจที่จำเป็น (ถ้ายังไม่มี)
    if ! npm list pg >/dev/null 2>&1; then
      echo "🔄 กำลังติดตั้งแพคเกจ pg..."
      npm install pg
    fi

    echo "✅ สภาพแวดล้อมพร้อมใช้งานแล้ว!"
    echo ""
    echo "🐘 PostgreSQL Local Database"
    echo "  • เริ่มฐานข้อมูล: nix-shell local-db.nix --run 'init-postgres'"
    echo "  • หยุดฐานข้อมูล: nix-shell local-db.nix --run 'stop-postgres'"
    echo "  • รีเซ็ตฐานข้อมูล: nix-shell local-db.nix --run 'reset-postgres'"
    echo ""
    echo "🔧 คำสั่งที่ใช้บ่อย:"
    echo "   - npm run dev     : เริ่มเซิร์ฟเวอร์ในโหมดพัฒนา"
    echo "   - npm run build   : สร้างไฟล์สำหรับการใช้งานจริง"
    echo "   - npm run start   : เริ่มเซิร์ฟเวอร์ในโหมดการใช้งานจริง"
    echo "   - npm run db:push : อัปเดตโครงสร้างฐานข้อมูล"
    echo "   - node scripts/test-db-connection.js : ทดสอบการเชื่อมต่อกับฐานข้อมูล"
  '';
}
