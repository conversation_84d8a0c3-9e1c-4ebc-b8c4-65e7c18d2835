import { useState } from "react";
import { Link, useLocation } from "wouter";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useAuth } from "@/hooks/use-auth";
import { queryClient } from "@/lib/queryClient";
import { Package } from "@shared/schema";
import Header from "@/components/layout/header";
import Footer from "@/components/layout/footer";
import { Button } from "@/components/ui/button";
import { Loader2, CheckCircle2, X, ArrowRight, ChevronRight } from "lucide-react";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

export default function PricingPage() {
  const [location, navigate] = useLocation();
  const { user } = useAuth();
  const { toast } = useToast();
  const [selectedPackage, setSelectedPackage] = useState<number | null>(null);

  // Fetch packages
  const { data: packages, isLoading } = useQuery<Package[]>({
    queryKey: ["/api/packages"],
  });

  // Fetch user subscription
  const { data: userSubscription } = useQuery<any>({
    queryKey: ["/api/users/me"],
    enabled: !!user,
  });

  // Subscribe to package mutation
  const subscribeMutation = useMutation({
    mutationFn: async (packageId: number) => {
      const res = await apiRequest("POST", "/api/subscribe", { packageId });
      return res.json();
    },
    onSuccess: () => {
      toast({
        title: "สมัครแพ็กเกจสำเร็จ",
        description: "คุณได้สมัครแพ็กเกจใหม่เรียบร้อยแล้ว",
      });
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["/api/users/me"] });
      navigate("/dashboard");
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSubscribe = (packageId: number) => {
    setSelectedPackage(packageId);
    
    if (!user) {
      navigate("/auth");
      return;
    }
    
    subscribeMutation.mutate(packageId);
  };

  const getButtonText = (pkg: Package) => {
    if (!user) return "เริ่มต้นใช้งาน";
    
    if (userSubscription?.subscription?.package?.id === pkg.id) {
      return "แพ็กเกจปัจจุบัน";
    }
    
    if (pkg.type === 'free') {
      return "เริ่มต้นใช้งานฟรี";
    } else if (pkg.type === 'enterprise') {
      return "ติดต่อฝ่ายขาย";
    } else {
      return "เริ่มต้นใช้งาน";
    }
  };

  const isCurrentPackage = (pkg: Package) => {
    return userSubscription?.subscription?.package?.id === pkg.id;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <div className="flex-grow flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary-500" />
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <section className="py-16 bg-white flex-grow">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h1 className="text-3xl font-bold text-neutral-800 mb-4">แพ็กเกจและราคา</h1>
            <p className="text-lg text-neutral-600 max-w-3xl mx-auto">
              เลือกแพ็กเกจที่เหมาะกับความต้องการของธุรกิจคุณ เริ่มต้นใช้งานได้ทันทีโดยไม่มีค่าใช้จ่ายแอบแฝง
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {packages?.map((pkg) => (
              <div 
                key={pkg.id}
                className={`
                  bg-white border rounded-xl overflow-hidden transition-all duration-300 hover:shadow-lg
                  ${pkg.type === 'business' ? 'border-2 border-primary-500 shadow-lg relative' : 'border-neutral-200'}
                `}
              >
                {pkg.type === 'business' && (
                  <div className="absolute top-0 right-0 bg-primary-500 text-white text-xs px-3 py-1 rounded-bl-lg font-medium">
                    แนะนำ
                  </div>
                )}
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-1">
                    {pkg.name}
                  </h3>
                  <div className="text-neutral-500 mb-4 text-sm">
                    {pkg.type === 'free' && 'สำหรับผู้เริ่มต้นใช้งาน'}
                    {pkg.type === 'business' && 'สำหรับธุรกิจขนาดเล็กถึงกลาง'}
                    {pkg.type === 'enterprise' && 'สำหรับธุรกิจขนาดใหญ่'}
                  </div>
                  <div className="flex items-baseline mb-6">
                    <span className={`text-3xl font-bold ${pkg.type === 'business' ? 'text-primary-700' : 'text-neutral-800'}`}>
                      {pkg.price === 0 ? 'ฟรี' : `฿${pkg.price.toLocaleString()}`}
                    </span>
                    {pkg.price > 0 && <span className="text-neutral-500 ml-1">/เดือน</span>}
                  </div>
                  <ul className="space-y-3 mb-6">
                    <li className="flex items-start">
                      <CheckCircle2 className="h-5 w-5 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                      <span>{pkg.requestLimit.toLocaleString()} ครั้งต่อเดือน</span>
                    </li>
                    {pkg.features?.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <CheckCircle2 className="h-5 w-5 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="px-6 pb-6">
                  <Button
                    onClick={() => handleSubscribe(pkg.id)}
                    disabled={isCurrentPackage(pkg) || selectedPackage === pkg.id && subscribeMutation.isPending}
                    className={`
                      w-full
                      ${pkg.type === 'free' ? 'bg-neutral-100 hover:bg-neutral-200 text-neutral-800' : ''}
                      ${pkg.type === 'business' ? 'bg-primary-600 hover:bg-primary-700 text-white' : ''}
                      ${pkg.type === 'enterprise' ? 'bg-secondary-500 hover:bg-secondary-600 text-white' : ''}
                    `}
                  >
                    {selectedPackage === pkg.id && subscribeMutation.isPending ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        กำลังดำเนินการ...
                      </>
                    ) : (
                      getButtonText(pkg)
                    )}
                  </Button>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-12 bg-neutral-50 p-6 rounded-lg max-w-6xl mx-auto">
            <div className="text-center">
              <h3 className="text-xl font-semibold mb-2">ต้องการแพ็กเกจแบบกำหนดเอง?</h3>
              <p className="text-neutral-600 mb-4">
                เรามีตัวเลือกที่ยืดหยุ่นสำหรับองค์กรที่มีความต้องการเฉพาะ
              </p>
              <Link href="#contact">
                <Button variant="link" className="text-primary-600 hover:text-primary-700 font-medium">
                  ติดต่อทีมขายของเรา <ChevronRight className="ml-1 h-4 w-4" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      <section className="py-12 bg-primary-700 text-white">
        <div className="container mx-auto px-4 md:px-6 lg:px-8 text-center">
          <h2 className="text-2xl md:text-3xl font-bold mb-4">มั่นใจในคุณภาพด้วยการรับประกันความพึงพอใจ</h2>
          <p className="text-lg text-primary-100 mb-8 max-w-2xl mx-auto">
            เราเชื่อมั่นในบริการของเรา หากคุณไม่พอใจในบริการภายใน 14 วัน เราพร้อมคืนเงินเต็มจำนวน
          </p>
          <Button size="lg" variant="secondary" asChild>
            <Link href={user ? "/dashboard" : "/auth"}>
              {user ? "ไปที่ Dashboard" : "ทดลองใช้งานโดยไม่มีความเสี่ยง"}
            </Link>
          </Button>
        </div>
      </section>

      <Footer />
    </div>
  );
}
