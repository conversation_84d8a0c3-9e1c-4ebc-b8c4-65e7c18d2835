import { Router } from 'express';
import type { Request, Response } from 'express';

const router = Router();

// API Documentation in JSON format
const apiDocs = {
  title: "API เอกสารคู่มือสำหรับ Slip Verification API",
  version: "1.0.0",
  description: "API สำหรับการตรวจสอบสลิปธนาคารและอ่านข้อมูลธุรกรรม",
  baseUrl: "/api",
  security: [
    {
      apiKey: {
        type: "apiKey",
        name: "X-API-Key",
        in: "header"
      }
    }
  ],
  endpoints: [
    {
      path: "/v1/verify-slip",
      method: "POST",
      description: "ส่งรูปภาพสลิปเพื่อตรวจสอบและอ่านข้อมูลธุรกรรม (แนะนำให้ใช้ endpoint นี้)",
      headers: [
        {
          name: "X-API-Key",
          required: true,
          description: "API Key สำหรับการเข้าถึง API"
        },
        {
          name: "Accept",
          required: false,
          description: "ประเภทข้อมูลที่ต้องการรับ (เช่น application/json)"
        }
      ],
      requestBody: {
        contentType: "multipart/form-data",
        parameters: [
          {
            name: "slip_image",
            type: "file",
            required: true,
            description: "รูปภาพสลิปธนาคาร (JPEG, PNG, GIF)"
          }
        ]
      },
      responses: {
        "200": {
          description: "การตรวจสอบสลิปสำเร็จ",
          example: {
            code: "200000",
            message: "ตรวจสอบสลิปสำเร็จ",
            data: {
              referenceId: "*************",
              decode: "**************...",
              transRef: "015110025307AQR03730",
              dateTime: "2023-10-12T15:30:45+07:00",
              amount: 500,
              ref1: "INVOICE123456",
              ref2: "REF9876543210",
              ref3: null,
              receiver: {
                account: {
                  name: "บริษัท ตัวอย่าง จำกัด",
                  number: "**********",
                  bank: {
                    id: "025",
                    name: "กรุงศรีอยุธยา"
                  }
                }
              },
              sender: {
                account: {
                  name: "คุณ ทดสอบ ระบบ",
                  number: "**********",
                  bank: {
                    id: "004",
                    name: "กสิกรไทย"
                  }
                }
              }
            }
          }
        },
        "400": {
          description: "คำร้องขอไม่ถูกต้อง",
          examples: [
            {
              code: "400001",
              message: "กรุณาอัปโหลดรูปภาพสลิปธนาคาร",
              data: null
            },
            {
              code: "400002",
              message: "ไม่พบ API Key ในส่วนหัวของคำขอ",
              data: null
            },
            {
              code: "400004",
              message: "ไม่ใช่รูปสลิปธนาคาร: ไม่พบรูปแบบ QR Code ในรูปภาพ",
              data: null
            }
          ]
        },
        "401": {
          description: "ไม่มีสิทธิ์เข้าถึง",
          example: {
            code: "401002",
            message: "API Key ไม่ถูกต้อง",
            data: null
          }
        },
        "403": {
          description: "ไม่มีสิทธิ์ใช้งาน",
          example: {
            code: "403001",
            message: "ไม่มีเครดิตเพียงพอ",
            data: null
          }
        },
        "500": {
          description: "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์",
          example: {
            code: "500000",
            message: "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์",
            data: null
          }
        }
      }
    },
    {
      path: "/verify/slip",
      method: "POST",
      description: "ส่งรูปภาพสลิปเพื่อตรวจสอบและอ่านข้อมูลธุรกรรม (เส้นทางเก่า - แนะนำให้ใช้ /api/v1/verify-slip แทน)",
      headers: [
        {
          name: "X-API-Key",
          required: true,
          description: "API Key สำหรับการเข้าถึง API"
        },
        {
          name: "Accept",
          required: false,
          description: "ประเภทข้อมูลที่ต้องการรับ (เช่น application/json)"
        }
      ],
      requestBody: {
        contentType: "multipart/form-data",
        parameters: [
          {
            name: "slip_image",
            type: "file",
            required: true,
            description: "รูปภาพสลิปธนาคาร (JPEG, PNG, GIF)"
          }
        ]
      },
      responses: {
        "200": {
          description: "การตรวจสอบสลิปสำเร็จ",
          example: {
            code: "200000",
            message: "ตรวจสอบสลิปสำเร็จ",
            data: {
              referenceId: "*************",
              decode: "**************...",
              transRef: "015110025307AQR03730",
              dateTime: "2023-10-12T15:30:45+07:00",
              amount: 500,
              ref1: "INVOICE123456",
              ref2: "REF9876543210",
              ref3: null,
              note: "แนะนำให้ใช้ API เวอร์ชันใหม่ที่ /api/v1/verify-slip แทน เส้นทางนี้อาจถูกยกเลิกในอนาคต",
              receiver: {
                account: {
                  name: "บริษัท ตัวอย่าง จำกัด",
                  number: "**********",
                  bank: {
                    id: "025",
                    name: "กรุงศรีอยุธยา"
                  }
                }
              },
              sender: {
                account: {
                  name: "คุณ ทดสอบ ระบบ",
                  number: "**********",
                  bank: {
                    id: "004",
                    name: "กสิกรไทย"
                  }
                }
              }
            }
          }
        },
        "400": {
          description: "คำร้องขอไม่ถูกต้อง",
          examples: [
            {
              code: "400001",
              message: "กรุณาอัปโหลดรูปภาพสลิปธนาคาร",
              data: null
            },
            {
              code: "400002",
              message: "ไม่พบ API Key ในส่วนหัวของคำขอ",
              data: null
            },
            {
              code: "400004",
              message: "ไม่ใช่รูปสลิปธนาคาร: ไม่พบรูปแบบ QR Code ในรูปภาพ",
              data: null
            }
          ]
        }
      }
    },
    {
      path: "/health",
      method: "GET",
      description: "ตรวจสอบสถานะการทำงานของ API",
      responses: {
        "200": {
          description: "API ทำงานปกติ",
          example: {
            status: "ok"
          }
        }
      }
    }
  ],
  // ลบ testEndpoint ตามที่ผู้ใช้ร้องขอ
  errorCodes: {
    "200000": "ตรวจสอบสลิปสำเร็จ",
    "400001": "กรุณาอัปโหลดรูปภาพสลิปธนาคาร",
    "400002": "ไม่พบ API Key ในส่วนหัวของคำขอ",
    "400003": "รูปแบบคำขอไม่ถูกต้อง",
    "400004": "ไม่ใช่รูปสลิปธนาคาร",
    "400005": "ต้องใช้ POST method สำหรับการอัพโหลดรูปภาพเท่านั้น",
    "401001": "ไม่มีสิทธิ์เข้าถึง API",
    "401002": "API Key ไม่ถูกต้อง",
    "403001": "ไม่มีเครดิตเพียงพอ",
    "403002": "เกินโควต้าการเรียกใช้ของแพ็กเกจ",
    "500000": "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"
  },
  examples: {
    curlExample: `curl -X POST \\
  https://yourdomain.replit.app/api/v1/verify-slip \\
  -H 'X-API-Key: your_api_key_here' \\
  -H 'Accept: application/json' \\
  -F 'slip_image=@/path/to/your/slip_image.jpg'`,
    javascriptExample: `// ตัวอย่างการใช้งานด้วย Javascript (Node.js)
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

async function verifySlip() {
  const formData = new FormData();
  formData.append('slip_image', fs.createReadStream('./slip_image.jpg'));

  try {
    const response = await axios.post('https://yourdomain.replit.app/api/v1/verify-slip', formData, {
      headers: {
        'X-API-Key': 'your_api_key_here',
        'Accept': 'application/json',
        ...formData.getHeaders()
      }
    });

    console.log('Success:', response.data);
  } catch (error) {
    console.error('Error:', error.response ? error.response.data : error.message);
  }
}

verifySlip();`,
    pythonExample: `# ตัวอย่างการใช้งานด้วย Python
import requests

url = "https://yourdomain.replit.app/api/v1/verify-slip"
headers = {
    "X-API-Key": "your_api_key_here",
    "Accept": "application/json"
}

files = {
    "slip_image": open("slip_image.jpg", "rb")
}

response = requests.post(url, headers=headers, files=files)
print("Status Code:", response.status_code)
print("Response:", response.json())`,
    phpExample: `<?php
// ตัวอย่างการใช้งานด้วย PHP
$url = 'https://yourdomain.replit.app/api/v1/verify-slip';
$apiKey = 'your_api_key_here';

$file = new CURLFile('/path/to/your/slip_image.jpg', 'image/jpeg', 'slip_image');
$data = array('slip_image' => $file);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
    'X-API-Key: ' . $apiKey,
    'Accept: application/json'
));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
curl_close($ch);

$result = json_decode($response, true);
print_r($result);
?>`,
    javaExample: `// ตัวอย่างการใช้งานด้วย Java
import java.io.File;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.URI;
import java.nio.file.Path;

public class SlipVerification {
    public static void main(String[] args) throws Exception {
        String boundary = "----WebKitFormBoundary" + System.currentTimeMillis();
        HttpClient client = HttpClient.newBuilder().build();

        HttpRequest request = HttpRequest.newBuilder()
            .uri(URI.create("https://yourdomain.replit.app/api/v1/verify-slip"))
            .header("X-API-Key", "your_api_key_here")
            .header("Accept", "application/json")
            .header("Content-Type", "multipart/form-data; boundary=" + boundary)
            .POST(HttpRequest.BodyPublishers.ofFile(Path.of("/path/to/your/slip_image.jpg")))
            .build();

        HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
        System.out.println("Status Code: " + response.statusCode());
        System.out.println("Response: " + response.body());
    }
}`
  },
  guidelines: {
    title: "คำแนะนำในการใช้งาน API",
    content: [
      {
        title: "1. รูปแบบรูปภาพที่รองรับ",
        description: "API รองรับรูปภาพในรูปแบบ JPG, JPEG, PNG และ GIF เท่านั้น โดยขนาดไฟล์ต้องไม่เกิน 50MB"
      },
      {
        title: "2. การตรวจสอบความถูกต้องของรูปภาพ",
        description: "ระบบจะตรวจสอบว่ารูปภาพที่อัปโหลดเป็นสลิปธนาคารจริงหรือไม่ โดยจะตรวจสอบลักษณะของรูปภาพและรูปแบบ QR Code ที่ปรากฏในรูปภาพ"
      },
      {
        title: "3. ข้อจำกัดการเรียกใช้",
        description: "การเรียกใช้ API มีข้อจำกัดตามแพ็กเกจที่สมัคร โดยหากเกินโควต้าที่กำหนด จะไม่สามารถเรียกใช้ API ได้จนกว่าจะมีการต่ออายุโควต้า"
      },
      {
        title: "4. การตรวจสอบสถานะ API",
        description: "สามารถตรวจสอบสถานะการทำงานของ API ได้โดยการเรียกใช้ GET /api/health ซึ่งไม่จำเป็นต้องใช้ API Key"
      },
      {
        title: "5. การทดสอบ API",
        description: "ในขั้นตอนการพัฒนา แนะนำให้ทดสอบผ่าน API จริง (/api/v1/verify-slip) ซึ่งจะมีการหักเครดิตตามปกติ เพื่อให้การพัฒนาเป็นไปอย่างมีประสิทธิภาพ"
      }
    ]
  }
};

// GET /api/docs
router.get('/', (req: Request, res: Response) => {
  res.json(apiDocs);
});

// GET /api/docs/usage
router.get('/usage', (req: Request, res: Response) => {
  // ตัวอย่างเนื้อหาวิธีการใช้งาน
  const usageGuide = {
    title: "คู่มือการใช้งาน API ตรวจสอบสลิปธนาคาร",
    overview: "API ตรวจสอบสลิปธนาคารช่วยให้คุณสามารถตรวจสอบความถูกต้องของการโอนเงินและอ่านข้อมูลจากรูปภาพสลิปธนาคารได้อย่างอัตโนมัติ",
    apiKeyInstructions: "ก่อนเริ่มใช้งาน API คุณจำเป็นต้องมี API Key โดยสามารถสร้างและจัดการ API Key ได้ในหน้า API Keys ของเว็บไซต์",
    sections: [
      {
        title: "การเริ่มต้นใช้งาน",
        steps: [
          "1. ลงชื่อเข้าใช้ระบบและไปที่หน้า API Keys",
          "2. สร้าง API Key ใหม่และกำหนดชื่อที่ต้องการ",
          "3. คัดลอก API Key ที่ได้เพื่อนำไปใช้ในการเรียก API"
        ]
      },
      {
        title: "การตรวจสอบสลิปธนาคาร",
        description: "ในการตรวจสอบสลิปธนาคาร คุณต้องส่งคำขอแบบ POST ไปยัง endpoint /api/v1/verify-slip พร้อมรูปภาพสลิปและ API Key",
        steps: [
          "1. เตรียมรูปภาพสลิปธนาคารที่ต้องการตรวจสอบ",
          "2. ส่งคำขอ HTTP แบบ POST โดยใส่ API Key ในส่วนหัวของคำขอ",
          "3. แนบรูปภาพสลิปในรูปแบบ multipart/form-data",
          "4. รับและประมวลผลข้อมูลที่ได้รับกลับมา"
        ]
      },
      {
        title: "การอ่านผลลัพธ์",
        description: "ผลลัพธ์ที่ได้จากการเรียกใช้ API จะอยู่ในรูปแบบ JSON โดยมีโครงสร้างดังนี้",
        responseStructure: {
          code: "รหัสผลการทำงาน (เช่น 200000 สำหรับการทำงานสำเร็จ)",
          message: "ข้อความอธิบายผลการทำงาน",
          data: {
            referenceId: "รหัสอ้างอิงการทำรายการ",
            decode: "ข้อมูลดิบที่อ่านได้จาก QR Code",
            transRef: "รหัสอ้างอิงการโอนเงิน",
            dateTime: "วันและเวลาที่ทำรายการ",
            amount: "จำนวนเงินที่โอน",
            ref1: "ข้อมูลอ้างอิง 1 (ถ้ามี)",
            ref2: "ข้อมูลอ้างอิง 2 (ถ้ามี)",
            ref3: "ข้อมูลอ้างอิง 3 (ถ้ามี)",
            receiver: "ข้อมูลผู้รับเงิน",
            sender: "ข้อมูลผู้ส่งเงิน"
          }
        }
      },
      {
        title: "ข้อผิดพลาดที่อาจเกิดขึ้น",
        description: "กรณีที่เกิดข้อผิดพลาด ระบบจะตอบกลับด้วยรหัสข้อผิดพลาดและข้อความอธิบาย",
        commonErrors: [
          {
            code: "400001",
            message: "กรุณาอัปโหลดรูปภาพสลิปธนาคาร",
            solution: "ตรวจสอบว่าได้แนบรูปภาพในคำขอแล้ว"
          },
          {
            code: "400002",
            message: "ไม่พบ API Key ในส่วนหัวของคำขอ",
            solution: "ตรวจสอบว่าได้เพิ่ม X-API-Key ในส่วนหัวของคำขอแล้ว"
          },
          {
            code: "400004",
            message: "ไม่ใช่รูปสลิปธนาคาร",
            solution: "ตรวจสอบว่ารูปภาพที่อัปโหลดเป็นสลิปธนาคารที่มี QR Code"
          },
          {
            code: "401002",
            message: "API Key ไม่ถูกต้อง",
            solution: "ตรวจสอบว่า API Key ที่ใช้ถูกต้องและยังไม่หมดอายุ"
          },
          {
            code: "403001",
            message: "ไม่มีเครดิตเพียงพอ",
            solution: "ตรวจสอบยอดเครดิตคงเหลือและเติมเครดิตหากจำเป็น"
          }
        ]
      },
      {
        title: "คำแนะนำสำหรับการพัฒนา",
        tips: [
          "ใช้โปรแกรม Postman หรือเครื่องมือทดสอบ API อื่นๆ เพื่อทดสอบ API ก่อนนำไปใช้จริงในแอปพลิเคชัน",
          "จัดการข้อผิดพลาดในแอปพลิเคชันของคุณเพื่อรองรับกรณีที่ API ไม่สามารถตรวจสอบสลิปได้",
          "เก็บ API Key ไว้ในที่ปลอดภัยและไม่ควรเปิดเผยต่อสาธารณะ",
          "หากต้องการประสิทธิภาพสูงสุด ใช้รูปภาพสลิปที่มีความละเอียดชัดเจน",
          "กำหนดระยะเวลา timeout ที่เหมาะสมเนื่องจากการประมวลผลรูปภาพอาจใช้เวลา"
        ]
      },
      {
        title: "การทดสอบ API ด้วย Postman",
        description: "Postman เป็นเครื่องมือยอดนิยมสำหรับทดสอบ API ต่างๆ ซึ่งช่วยให้คุณสามารถทดสอบ API ได้อย่างมีประสิทธิภาพ",
        steps: [
          "1. ดาวน์โหลดและติดตั้ง Postman จาก https://www.postman.com/downloads/",
          "2. สร้าง Collection ใหม่สำหรับการทดสอบ Slip Verification API",
          "3. สร้างคำขอใหม่แบบ POST ไปยัง https://yourdomain.replit.app/api/v1/verify-slip",
          "4. ในแท็บ Headers เพิ่ม X-API-Key และกำหนดค่าเป็น API Key ของคุณ",
          "5. ในแท็บ Body เลือก form-data และเพิ่มฟิลด์ slip_image พร้อมเลือกไฟล์รูปภาพสลิปที่ต้องการทดสอบ",
          "6. คลิกปุ่ม Send เพื่อส่งคำขอและดูผลลัพธ์ที่ได้รับกลับมา"
        ],
        tips: [
          "บันทึกคำขอที่ใช้บ่อยเป็น Collection เพื่อใช้งานได้สะดวกในอนาคต",
          "สร้าง Environment สำหรับเก็บค่า API Key และ URL เพื่อให้สามารถสลับระหว่างสภาพแวดล้อมการทดสอบและการใช้งานจริงได้ง่าย",
          "ใช้ Pre-request Script สำหรับการสร้างข้อมูลทดสอบที่ซับซ้อน",
          "ใช้ Tests Script สำหรับการตรวจสอบผลลัพธ์อัตโนมัติ เช่นโค้ดด้านล่าง:"
        ],
        codeSnippet: `// ตัวอย่าง Tests Script สำหรับตรวจสอบผลลัพธ์
pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});

pm.test("Response has correct format", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData).to.have.property("code", "200000");
    pm.expect(jsonData).to.have.property("message");
    pm.expect(jsonData).to.have.property("data");
});

pm.test("Transaction data is valid", function () {
    var jsonData = pm.response.json();
    if (jsonData.code === "200000") {
        pm.expect(jsonData.data).to.have.property("transRef");
        pm.expect(jsonData.data).to.have.property("amount");
        pm.expect(jsonData.data).to.have.property("dateTime");
    }
});`
      }
    ]
  };

  res.json(usageGuide);
});

// ไม่มีเอนด์พอยต์ /api/docs/postman ตามที่ผู้ใช้ต้องการ
// ย้ายคู่มือ Postman ไปรวมใน /api/docs/usage แทน

export default router;