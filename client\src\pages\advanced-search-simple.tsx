import React, { useState } from 'react';
import { format } from 'date-fns';
import { th } from 'date-fns/locale';
import <PERSON> from 'papa<PERSON><PERSON>';
import { saveAs } from 'file-saver';
import {
  LineChart, Line, BarChart, Bar, PieChart, Pie, Cell,
  XAxis, YAxis, CartesianGrid, Tooltip as <PERSON><PERSON>rtsTooltip, Legend, ResponsiveContainer
} from 'recharts';

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { DatePickerWithRange } from "@/components/ui/date-range-picker";
import { Download, ArrowUpDown, Search } from "lucide-react";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue, 
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

export default function AdvancedSearchPage() {
  const { toast } = useToast();
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date }>({
    from: new Date(new Date().setDate(new Date().getDate() - 30)),
    to: new Date()
  });
  
  const [includeDetails, setIncludeDetails] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [searchResults, setSearchResults] = useState<any>(null);
  
  const handleSearch = async () => {
    if (!dateRange.from || !dateRange.to) {
      toast({
        title: "กรุณาเลือกช่วงวันที่",
        variant: "destructive"
      });
      return;
    }
    
    setIsLoading(true);
    
    try {
      const res = await apiRequest('POST', '/api/advanced-search/analyze-simple', {
        startDate: dateRange.from.toISOString(),
        endDate: dateRange.to.toISOString(),
        includeDetails: includeDetails
      });
      
      const data = await res.json();
      if (data.success) {
        setSearchResults(data);
      } else {
        toast({
          title: "ไม่สามารถวิเคราะห์ข้อมูลได้",
          description: data.message || "กรุณาลองใหม่อีกครั้ง",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: "ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  const exportToCSV = () => {
    if (!searchResults) return;
    
    const formattedDate = format(new Date(), 'yyyy-MM-dd_HH-mm');
    const filename = `slipkuy_advanced_report_${formattedDate}.csv`;
    
    let csvData = [];
    
    // Add header with date range
    csvData.push(['รายงานการค้นหาขั้นสูง SLIPKUY']);
    csvData.push([`ช่วงวันที่: ${format(dateRange.from, 'PPP', { locale: th })} ถึง ${format(dateRange.to, 'PPP', { locale: th })}`]);
    csvData.push([]);
    
    // Summary section
    csvData.push(['สรุปทั้งหมด']);
    csvData.push(['รายการทั้งหมด', searchResults.results.totalTransactions]);
    csvData.push(['จำนวนเงินทั้งหมด', searchResults.results.totalAmount?.toLocaleString(undefined, { minimumFractionDigits: 2 }) + ' บาท']);
    csvData.push(['จำนวนเงินเฉลี่ย', searchResults.results.avgAmount?.toLocaleString(undefined, { minimumFractionDigits: 2 }) + ' บาท']);
    csvData.push([]);
    
    // Bank Distribution
    if (Array.isArray(searchResults.results.bankDistribution)) {
      csvData.push(['การแจกแจงตามธนาคาร']);
      csvData.push(['ธนาคาร', 'จำนวนรายการ', 'เปอร์เซ็นต์', 'จำนวนเงินรวม']);
      
      searchResults.results.bankDistribution.forEach((bank: any) => {
        csvData.push([
          bank.name || 'ไม่ระบุ',
          bank.count,
          (bank.percentage?.toFixed(2) || '0') + '%',
          (bank.totalAmount?.toLocaleString(undefined, { minimumFractionDigits: 2 }) || '0') + ' บาท'
        ]);
      });
      csvData.push([]);
    }
    
    // Time Distribution
    if (Array.isArray(searchResults.results.timeDistribution)) {
      csvData.push(['การแจกแจงตามช่วงเวลา']);
      csvData.push(['ช่วงเวลา', 'จำนวนรายการ', 'เปอร์เซ็นต์', 'จำนวนเงินรวม']);
      
      searchResults.results.timeDistribution.forEach((time: any) => {
        csvData.push([
          time.time || 'ไม่ระบุ',
          time.count,
          (time.percentage?.toFixed(2) || '0') + '%',
          (time.totalAmount?.toLocaleString(undefined, { minimumFractionDigits: 2 }) || '0') + ' บาท'
        ]);
      });
      csvData.push([]);
    }
    
    // Top Receivers
    if (Array.isArray(searchResults.results.topReceivers)) {
      csvData.push(['ผู้รับโอนบ่อยที่สุด']);
      csvData.push(['ผู้รับ', 'จำนวนครั้ง', 'เปอร์เซ็นต์', 'จำนวนเงินรวม']);
      
      searchResults.results.topReceivers.forEach((receiver: any) => {
        csvData.push([
          receiver.name || 'ไม่ระบุ',
          receiver.count,
          (receiver.percentage?.toFixed(2) || '0') + '%',
          (receiver.totalAmount?.toLocaleString(undefined, { minimumFractionDigits: 2 }) || '0') + ' บาท'
        ]);
      });
      csvData.push([]);
    }
    
    // Timeline
    if (Array.isArray(searchResults.results.timeline)) {
      csvData.push(['แนวโน้มตามเวลา']);
      csvData.push(['วันที่', 'จำนวนรายการ', 'จำนวนเงินรวม']);
      
      searchResults.results.timeline.forEach((day: any) => {
        csvData.push([
          day.date,
          day.count,
          (day.amount?.toLocaleString(undefined, { minimumFractionDigits: 2 }) || '0') + ' บาท'
        ]);
      });
      csvData.push([]);
    }
    
    // Details of transactions
    if (includeDetails && Array.isArray(searchResults.transactions)) {
      csvData.push(['รายละเอียดรายการทั้งหมด']);
      csvData.push(['วันที่', 'เวลา', 'จำนวนเงิน', 'ธนาคารผู้รับ', 'ผู้รับ', 'สถานะ']);
      
      searchResults.transactions.forEach((tx: any) => {
        const date = tx.createdAt ? new Date(tx.createdAt) : null;
        
        csvData.push([
          date ? format(date, 'PPP', { locale: th }) : '-',
          date ? format(date, 'HH:mm', { locale: th }) : '-',
          (tx.amount?.toLocaleString(undefined, { minimumFractionDigits: 2 }) || '0') + ' บาท',
          tx.bankName || '-',
          tx.receiver || '-',
          tx.status || '-'
        ]);
      });
    }
    
    const csv = Papa.unparse(csvData);
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    saveAs(blob, filename);
  };
  
  return (
    <div className="container py-6 max-w-7xl mx-auto">
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">ค้นหาขั้นสูง</h1>
          <p className="text-muted-foreground mt-2">
            วิเคราะห์และแสดงข้อมูลเชิงลึกสำหรับการตรวจสอบรายการของคุณ
          </p>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>ตัวกรองการค้นหา</CardTitle>
            <CardDescription>
              กำหนดเงื่อนไขในการค้นหาและวิเคราะห์ข้อมูลของคุณ
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1 space-y-2">
                <Label htmlFor="date-range">ช่วงวันที่</Label>
                <DatePickerWithRange
                  className="w-full"
                  value={dateRange}
                  onChange={setDateRange}
                />
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <input 
                type="checkbox" 
                id="include-details" 
                className="mr-1"
                checked={includeDetails}
                onChange={(e) => setIncludeDetails(e.target.checked)}
              />
              <Label htmlFor="include-details">รวมรายการทั้งหมด</Label>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between items-center">
            <Button 
              variant="outline" 
              onClick={exportToCSV}
              disabled={!searchResults}
            >
              <Download className="h-4 w-4 mr-2" /> ดาวน์โหลด CSV
            </Button>
            
            <Button onClick={handleSearch} disabled={isLoading}>
              {isLoading ? 'กำลังค้นหา...' : 'ค้นหาและวิเคราะห์'}
              {!isLoading && <Search className="ml-2 h-4 w-4" />}
            </Button>
          </CardFooter>
        </Card>
        
        {searchResults && (
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>ผลการวิเคราะห์</CardTitle>
              <CardDescription>
                ข้อมูลระหว่าง {format(dateRange.from, 'PPP', { locale: th })} ถึง {format(dateRange.to, 'PPP', { locale: th })}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/50 dark:to-indigo-950/50 border-none">
                  <CardContent className="pt-6">
                    <div className="text-lg font-semibold mb-2">รายการทั้งหมด</div>
                    <div className="text-3xl font-bold text-gradient-blue-to-indigo">
                      {searchResults.results.totalTransactions || 0}
                    </div>
                  </CardContent>
                </Card>
                
                <Card className="bg-gradient-to-br from-emerald-50 to-green-50 dark:from-emerald-950/50 dark:to-green-950/50 border-none">
                  <CardContent className="pt-6">
                    <div className="text-lg font-semibold mb-2">จำนวนเงินทั้งหมด</div>
                    <div className="text-3xl font-bold text-gradient-emerald-to-green">
                      {searchResults.results.totalAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} ฿
                    </div>
                  </CardContent>
                </Card>
                
                <Card className="bg-gradient-to-br from-amber-50 to-orange-50 dark:from-amber-950/50 dark:to-orange-950/50 border-none">
                  <CardContent className="pt-6">
                    <div className="text-lg font-semibold mb-2">จำนวนเงินเฉลี่ย</div>
                    <div className="text-3xl font-bold text-gradient-amber-to-orange">
                      {searchResults.results.avgAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} ฿
                    </div>
                  </CardContent>
                </Card>
              </div>
              
              {/* Bank Distribution */}
              <div className="space-y-4">
                <h3 className="text-xl font-semibold">การแจกแจงตามธนาคาร</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="h-[300px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart 
                        data={Array.isArray(searchResults.results.bankDistribution) ? searchResults.results.bankDistribution : []}
                        margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis 
                          dataKey="name" 
                          angle={-45} 
                          textAnchor="end" 
                          height={80}
                          tick={{ fill: 'var(--color-foreground-muted)' }}
                        />
                        <YAxis 
                          tick={{ fill: 'var(--color-foreground-muted)' }}
                        />
                        <RechartsTooltip />
                        <Legend />
                        <Bar dataKey="count" name="จำนวนรายการ" fill="#0391ff" />
                        <Bar dataKey="percentage" name="เปอร์เซ็นต์" fill="#6466f1" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                  
                  <div className="h-[300px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart width={400} height={300}>
                        {Array.isArray(searchResults.results.bankDistribution) && (
                          <Pie
                            data={searchResults.results.bankDistribution}
                            nameKey="name"
                            dataKey="totalAmount"
                            cx="50%"
                            cy="50%"
                            outerRadius={100}
                            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                          >
                            {searchResults.results.bankDistribution.map((entry: any, index: number) => (
                              <Cell key={`cell-${index}`} fill={`hsl(${index * 40}, 70%, 60%)`} />
                            ))}
                          </Pie>
                        )}
                        <RechartsTooltip
                          formatter={(value: any) => [`${value.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })} บาท`, 'จำนวนเงิน']}
                        />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </div>
                
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>ธนาคาร</TableHead>
                      <TableHead>จำนวนรายการ</TableHead>
                      <TableHead>เปอร์เซ็นต์</TableHead>
                      <TableHead className="text-right">จำนวนเงินรวม</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {Array.isArray(searchResults.results.bankDistribution) ? (
                      searchResults.results.bankDistribution.map((bank: any, index: number) => (
                        <TableRow key={index}>
                          <TableCell className="font-medium">{bank.name || 'ไม่ระบุ'}</TableCell>
                          <TableCell>{bank.count}</TableCell>
                          <TableCell>{bank.percentage?.toFixed(2) || '0'}%</TableCell>
                          <TableCell className="text-right">{bank.totalAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} ฿</TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={4} className="text-center">ไม่มีข้อมูล</TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
              
              {/* Time Analysis Results */}
              {searchResults.results.timeDistribution && (
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold">การกระจายตามช่วงเวลา</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="h-[300px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart
                          data={Array.isArray(searchResults.results.timeDistribution) ? searchResults.results.timeDistribution : []}
                          margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis 
                            dataKey="time" 
                            angle={-45} 
                            textAnchor="end" 
                            height={80}
                            tick={{ fill: 'var(--color-foreground-muted)' }}
                          />
                          <YAxis 
                            yAxisId="left"
                            tick={{ fill: 'var(--color-foreground-muted)' }}
                          />
                          <YAxis 
                            yAxisId="right" 
                            orientation="right"
                            tick={{ fill: 'var(--color-foreground-muted)' }}
                          />
                          <RechartsTooltip />
                          <Legend />
                          <Line 
                            yAxisId="left"
                            type="monotone" 
                            dataKey="count" 
                            name="จำนวนรายการ" 
                            stroke="#4682b4"
                            strokeWidth={2}
                            dot={{ r: 5 }}
                            activeDot={{ r: 8 }}
                          />
                          <Line 
                            yAxisId="right"
                            type="monotone" 
                            dataKey="totalAmount" 
                            name="จำนวนเงินรวม" 
                            stroke="#9370db" 
                            strokeWidth={2}
                            dot={{ r: 5 }}
                            activeDot={{ r: 8 }}
                          />
                        </LineChart>
                      </ResponsiveContainer>
                    </div>
                    
                    <div className="h-[300px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={Array.isArray(searchResults.results.timeDistribution) ? searchResults.results.timeDistribution : []}
                          margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis 
                            dataKey="time" 
                            angle={-45} 
                            textAnchor="end" 
                            height={80}
                            tick={{ fill: 'var(--color-foreground-muted)' }}
                          />
                          <YAxis 
                            tick={{ fill: 'var(--color-foreground-muted)' }}
                          />
                          <RechartsTooltip />
                          <Legend />
                          <Bar dataKey="percentage" name="เปอร์เซ็นต์" fill="#20b2aa" />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </div>
                  
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>ช่วงเวลา</TableHead>
                        <TableHead>จำนวนรายการ</TableHead>
                        <TableHead>เปอร์เซ็นต์</TableHead>
                        <TableHead className="text-right">จำนวนเงินรวม</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {Array.isArray(searchResults.results.timeDistribution) ? (
                        searchResults.results.timeDistribution.map((time: any, index: number) => (
                          <TableRow key={index}>
                            <TableCell className="font-medium">{time.time || 'ไม่ระบุ'}</TableCell>
                            <TableCell>{time.count}</TableCell>
                            <TableCell>{time.percentage?.toFixed(2) || '0'}%</TableCell>
                            <TableCell className="text-right">{time.totalAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} ฿</TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={4} className="text-center">ไม่มีข้อมูล</TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}
              
              {/* Timeline Analysis */}
              <div className="space-y-4">
                <h3 className="text-xl font-semibold">แนวโน้มการทำรายการ</h3>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={Array.isArray(searchResults.results.timeline) ? searchResults.results.timeline : []}
                      margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="date" 
                        angle={-45} 
                        textAnchor="end" 
                        height={80}
                        tick={{ fill: 'var(--color-foreground-muted)' }}
                      />
                      <YAxis 
                        yAxisId="left"
                        tick={{ fill: 'var(--color-foreground-muted)' }}
                      />
                      <YAxis 
                        yAxisId="right" 
                        orientation="right"
                        tick={{ fill: 'var(--color-foreground-muted)' }}
                      />
                      <RechartsTooltip />
                      <Legend />
                      {Array.isArray(searchResults.results.timeline) && (
                        <>
                          <Line 
                            yAxisId="left"
                            type="monotone" 
                            dataKey="count" 
                            name="จำนวนรายการ" 
                            stroke="#1e88e5"
                            strokeWidth={2}
                            dot={{ r: 5 }}
                            activeDot={{ r: 8 }}
                          />
                          <Line 
                            yAxisId="right"
                            type="monotone" 
                            dataKey="amount" 
                            name="จำนวนเงินรวม" 
                            stroke="#43a047" 
                            strokeWidth={2}
                            dot={{ r: 5 }}
                            activeDot={{ r: 8 }}
                          />
                        </>
                      )}
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </div>
              
              <div className="space-y-3">
                <h3 className="text-xl font-semibold">ผู้รับโอนบ่อยที่สุด</h3>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>ผู้รับ</TableHead>
                      <TableHead>จำนวนครั้ง</TableHead>
                      <TableHead>เปอร์เซ็นต์</TableHead>
                      <TableHead className="text-right">จำนวนเงินรวม</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {Array.isArray(searchResults.results.topReceivers) ? (
                      searchResults.results.topReceivers.map((receiver: any, index: number) => (
                        <TableRow key={index}>
                          <TableCell className="font-medium">{receiver.name || 'ไม่ระบุ'}</TableCell>
                          <TableCell>{receiver.count}</TableCell>
                          <TableCell>{receiver.percentage?.toFixed(2) || '0'}%</TableCell>
                          <TableCell className="text-right">{receiver.totalAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} ฿</TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={4} className="text-center">ไม่มีข้อมูล</TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
              
              {/* List of All Transactions (if detailed view is enabled) */}
              {includeDetails && Array.isArray(searchResults.transactions) && searchResults.transactions.length > 0 && (
                <div className="space-y-3 mt-4 pt-4 border-t">
                  <h3 className="text-xl font-semibold">รายการทั้งหมด</h3>
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>วันที่</TableHead>
                          <TableHead>เวลา</TableHead>
                          <TableHead>จำนวนเงิน</TableHead>
                          <TableHead>ธนาคารผู้รับ</TableHead>
                          <TableHead>ผู้รับ</TableHead>
                          <TableHead>สถานะ</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {searchResults.transactions.map((tx: any, index: number) => {
                          const date = tx.createdAt ? new Date(tx.createdAt) : null;
                          return (
                            <TableRow key={index}>
                              <TableCell>{date ? format(date, 'PPP', { locale: th }) : '-'}</TableCell>
                              <TableCell>{date ? format(date, 'HH:mm', { locale: th }) : '-'}</TableCell>
                              <TableCell>{tx.amount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} ฿</TableCell>
                              <TableCell>{tx.bankName || '-'}</TableCell>
                              <TableCell>{tx.receiver || '-'}</TableCell>
                              <TableCell>{tx.status || '-'}</TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}