/**
 * API สำหรับจัดการการตั้งค่าต่างๆ ของผู้ใช้
 */

import { Router } from 'express';
import { body, validationResult } from 'express-validator';
import { storage } from '../storage';
import { logger } from '../logger';

const router = Router();

/**
 * ดึงข้อมูลการตั้งค่าผู้ใช้ทั้งหมด
 */
router.get('/', async (req, res) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ message: 'กรุณาล็อกอินเพื่อดูข้อมูลการตั้งค่า' });
  }

  const userId = req.user.id;

  try {
    // ดึงข้อมูลการตั้งค่าผู้ใช้จากฐานข้อมูล
    const userSettings = await storage.getUserSettings(userId);
    
    // ถ้าไม่มีข้อมูล ให้สร้างข้อมูลด้วยค่าเริ่มต้น
    if (!userSettings) {
      // ค่าเริ่มต้นสำหรับการตั้งค่าผู้ใช้
      const defaultSettings = {
        userId,
        emailNotifications: true,
        duplicateSlipCheck: true, // เปิดการตรวจสอบสลิปซ้ำตั้งแต่แรก
        theme: 'dark',
        language: 'th',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // สร้างข้อมูลการตั้งค่าใหม่ด้วยค่าเริ่มต้น
      await storage.createUserSettings(defaultSettings);
      
      logger.info(`[การตั้งค่าผู้ใช้] สร้างการตั้งค่าเริ่มต้นให้ผู้ใช้ ${userId} สำเร็จ`);
      return res.status(200).json(defaultSettings);
    }

    res.status(200).json(userSettings);
  } catch (error) {
    logger.error(`[การตั้งค่าผู้ใช้] เกิดข้อผิดพลาดในการดึงข้อมูลการตั้งค่าของผู้ใช้ ${userId}:`, error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการดึงข้อมูลการตั้งค่า' });
  }
});

/**
 * อัปเดตการตั้งค่าบางส่วนของผู้ใช้ (PATCH)
 */
router.patch('/', 
  body('duplicateSlipCheck').optional().isBoolean().withMessage('ค่า duplicateSlipCheck ต้องเป็น boolean'),
  body('emailNotifications').optional().isBoolean().withMessage('ค่า emailNotifications ต้องเป็น boolean'),
  body('theme').optional().isString().isIn(['dark', 'light']).withMessage('ค่า theme ต้องเป็น dark หรือ light'),
  body('language').optional().isString().isIn(['th', 'en']).withMessage('ค่า language ต้องเป็น th หรือ en'),
  async (req, res) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: 'กรุณาล็อกอินเพื่ออัปเดตการตั้งค่า' });
    }

    // ตรวจสอบความถูกต้องของข้อมูล
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const userId = req.user.id;
    const updates = req.body;

    // ตรวจสอบว่ามีข้อมูลที่ต้องการอัปเดตหรือไม่
    if (Object.keys(updates).length === 0) {
      return res.status(400).json({ message: 'ไม่มีข้อมูลที่ต้องการอัปเดต' });
    }

    try {
      // ดึงข้อมูลการตั้งค่าผู้ใช้จากฐานข้อมูล
      let userSettings = await storage.getUserSettings(userId);
      
      // เพิ่มเวลาอัปเดตล่าสุด
      updates.updatedAt = new Date();
      
      // ถ้าไม่มีข้อมูล ให้สร้างข้อมูลด้วยค่าเริ่มต้นและรวมกับการอัปเดต
      if (!userSettings) {
        // ค่าเริ่มต้นสำหรับการตั้งค่าผู้ใช้
        const defaultSettings = {
          userId,
          emailNotifications: true,
          duplicateSlipCheck: true,
          theme: 'dark',
          language: 'th',
          createdAt: new Date(),
          ...updates // รวมการอัปเดตกับค่าเริ่มต้น
        };

        // สร้างข้อมูลการตั้งค่าใหม่
        userSettings = await storage.createUserSettings(defaultSettings);
        logger.info(`[การตั้งค่าผู้ใช้] สร้างการตั้งค่าใหม่พร้อมอัปเดตการตั้งค่าสำหรับผู้ใช้ ${userId}`);
      } else {
        // อัปเดตการตั้งค่าตามที่ผู้ใช้ส่งมา
        userSettings = await storage.updateUserSettings(userId, updates);
        logger.info(`[การตั้งค่าผู้ใช้] อัปเดตการตั้งค่าสำหรับผู้ใช้ ${userId}: ${JSON.stringify(updates)}`);
      }

      res.status(200).json({ 
        message: 'อัปเดตการตั้งค่าเรียบร้อยแล้ว',
        userSettings
      });
    } catch (error) {
      logger.error(`[การตั้งค่าผู้ใช้] เกิดข้อผิดพลาดในการอัปเดตการตั้งค่าสำหรับผู้ใช้ ${userId}:`, error);
      res.status(500).json({ message: 'เกิดข้อผิดพลาดในการอัปเดตการตั้งค่า' });
    }
});

/**
 * อัปเดตการตั้งค่าการตรวจสอบสลิปซ้ำ (สนับสนุนเวอร์ชันเก่า)
 */
router.put('/duplicate-slip-check', 
  body('enabled').isBoolean().withMessage('ค่า enabled ต้องเป็น boolean'),
  async (req, res) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: 'กรุณาล็อกอินเพื่ออัปเดตการตั้งค่า' });
    }

    // ตรวจสอบความถูกต้องของข้อมูล
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const userId = req.user.id;
    const { enabled } = req.body;

    try {
      // ดึงข้อมูลการตั้งค่าผู้ใช้จากฐานข้อมูล
      let userSettings = await storage.getUserSettings(userId);
      
      // ถ้าไม่มีข้อมูล ให้สร้างข้อมูลด้วยค่าเริ่มต้นและอัปเดตค่า duplicateSlipCheck
      if (!userSettings) {
        // ค่าเริ่มต้นสำหรับการตั้งค่าผู้ใช้
        const defaultSettings = {
          userId,
          emailNotifications: true,
          duplicateSlipCheck: enabled, // ใช้ค่าที่รับมา
          theme: 'dark',
          language: 'th',
          createdAt: new Date(),
          updatedAt: new Date()
        };

        // สร้างข้อมูลการตั้งค่าใหม่
        userSettings = await storage.createUserSettings(defaultSettings);
        logger.info(`[การตั้งค่าผู้ใช้] สร้างการตั้งค่าใหม่พร้อมอัปเดตการตรวจสอบสลิปซ้ำเป็น ${enabled} สำหรับผู้ใช้ ${userId}`);
      } else {
        // อัปเดตการตั้งค่าเฉพาะส่วนการตรวจสอบสลิปซ้ำ
        userSettings = await storage.updateUserSettings(userId, {
          duplicateSlipCheck: enabled,
          updatedAt: new Date()
        });
        logger.info(`[การตั้งค่าผู้ใช้] อัปเดตการตรวจสอบสลิปซ้ำเป็น ${enabled} สำหรับผู้ใช้ ${userId}`);
      }

      res.status(200).json({ 
        message: `อัปเดตการตั้งค่าการตรวจสอบสลิปซ้ำเป็น ${enabled ? 'เปิดใช้งาน' : 'ปิดใช้งาน'} เรียบร้อยแล้ว`,
        userSettings
      });
    } catch (error) {
      logger.error(`[การตั้งค่าผู้ใช้] เกิดข้อผิดพลาดในการอัปเดตการตั้งค่าการตรวจสอบสลิปซ้ำสำหรับผู้ใช้ ${userId}:`, error);
      res.status(500).json({ message: 'เกิดข้อผิดพลาดในการอัปเดตการตั้งค่า' });
    }
});

/**
 * อัปเดตการตั้งค่าการแจ้งเตือนทางอีเมล
 */
router.put('/email-notifications', 
  body('enabled').isBoolean().withMessage('ค่า enabled ต้องเป็น boolean'),
  async (req, res) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: 'กรุณาล็อกอินเพื่ออัปเดตการตั้งค่า' });
    }

    // ตรวจสอบความถูกต้องของข้อมูล
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const userId = req.user.id;
    const { enabled } = req.body;

    try {
      // ดึงข้อมูลการตั้งค่าผู้ใช้จากฐานข้อมูล
      let userSettings = await storage.getUserSettings(userId);
      
      // ถ้าไม่มีข้อมูล ให้สร้างข้อมูลด้วยค่าเริ่มต้นและอัปเดตค่า emailNotifications
      if (!userSettings) {
        // ค่าเริ่มต้นสำหรับการตั้งค่าผู้ใช้
        const defaultSettings = {
          userId,
          emailNotifications: enabled, // ใช้ค่าที่รับมา
          duplicateSlipCheck: true, 
          theme: 'dark',
          language: 'th',
          createdAt: new Date(),
          updatedAt: new Date()
        };

        // สร้างข้อมูลการตั้งค่าใหม่
        userSettings = await storage.createUserSettings(defaultSettings);
        logger.info(`[การตั้งค่าผู้ใช้] สร้างการตั้งค่าใหม่พร้อมอัปเดตการแจ้งเตือนทางอีเมลเป็น ${enabled} สำหรับผู้ใช้ ${userId}`);
      } else {
        // อัปเดตการตั้งค่าเฉพาะส่วนการแจ้งเตือนทางอีเมล
        userSettings = await storage.updateUserSettings(userId, {
          emailNotifications: enabled,
          updatedAt: new Date()
        });
        logger.info(`[การตั้งค่าผู้ใช้] อัปเดตการแจ้งเตือนทางอีเมลเป็น ${enabled} สำหรับผู้ใช้ ${userId}`);
      }

      res.status(200).json({ 
        message: `อัปเดตการตั้งค่าการแจ้งเตือนทางอีเมลเป็น ${enabled ? 'เปิดใช้งาน' : 'ปิดใช้งาน'} เรียบร้อยแล้ว`,
        userSettings
      });
    } catch (error) {
      logger.error(`[การตั้งค่าผู้ใช้] เกิดข้อผิดพลาดในการอัปเดตการตั้งค่าการแจ้งเตือนทางอีเมลสำหรับผู้ใช้ ${userId}:`, error);
      res.status(500).json({ message: 'เกิดข้อผิดพลาดในการอัปเดตการตั้งค่า' });
    }
});

/**
 * อัปเดตการตั้งค่าธีม
 */
router.put('/theme', 
  body('theme').isString().isIn(['dark', 'light']).withMessage('ค่า theme ต้องเป็น dark หรือ light'),
  async (req, res) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: 'กรุณาล็อกอินเพื่ออัปเดตการตั้งค่า' });
    }

    // ตรวจสอบความถูกต้องของข้อมูล
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const userId = req.user.id;
    const { theme } = req.body;

    try {
      // ดึงข้อมูลการตั้งค่าผู้ใช้จากฐานข้อมูล
      let userSettings = await storage.getUserSettings(userId);
      
      // ถ้าไม่มีข้อมูล ให้สร้างข้อมูลด้วยค่าเริ่มต้นและอัปเดตค่า theme
      if (!userSettings) {
        // ค่าเริ่มต้นสำหรับการตั้งค่าผู้ใช้
        const defaultSettings = {
          userId,
          emailNotifications: true,
          duplicateSlipCheck: true,
          theme, // ใช้ค่าที่รับมา
          language: 'th',
          createdAt: new Date(),
          updatedAt: new Date()
        };

        // สร้างข้อมูลการตั้งค่าใหม่
        userSettings = await storage.createUserSettings(defaultSettings);
        logger.info(`[การตั้งค่าผู้ใช้] สร้างการตั้งค่าใหม่พร้อมอัปเดตธีมเป็น ${theme} สำหรับผู้ใช้ ${userId}`);
      } else {
        // อัปเดตการตั้งค่าเฉพาะส่วนธีม
        userSettings = await storage.updateUserSettings(userId, {
          theme,
          updatedAt: new Date()
        });
        logger.info(`[การตั้งค่าผู้ใช้] อัปเดตธีมเป็น ${theme} สำหรับผู้ใช้ ${userId}`);
      }

      res.status(200).json({ 
        message: `อัปเดตการตั้งค่าธีมเป็น ${theme === 'dark' ? 'โหมดมืด' : 'โหมดสว่าง'} เรียบร้อยแล้ว`,
        userSettings
      });
    } catch (error) {
      logger.error(`[การตั้งค่าผู้ใช้] เกิดข้อผิดพลาดในการอัปเดตการตั้งค่าธีมสำหรับผู้ใช้ ${userId}:`, error);
      res.status(500).json({ message: 'เกิดข้อผิดพลาดในการอัปเดตการตั้งค่า' });
    }
});

export default router;