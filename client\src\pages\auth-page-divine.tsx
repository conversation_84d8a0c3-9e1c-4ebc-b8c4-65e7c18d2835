import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { Crown, LockKeyhole, Lock, MailIcon, PhoneCall, Laugh, ArrowRight, CheckCircle, User, Phone } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { useAuth } from "@/hooks/use-auth";
import axios from "axios";
import { Navbar } from "@/components/layouts/navbar";
import { Foot<PERSON> } from "@/components/layouts/footer";
import { motion, AnimatePresence } from "framer-motion";

// Schema สำหรับฟอร์มเข้าสู่ระบบ
const loginSchema = z.object({
  username: z.string().min(1, "กรุณากรอกชื่อผู้ใช้"),
  password: z.string().min(1, "กรุณากรอกรหัสผ่าน"),
});

// Schema สำหรับฟอร์มลงทะเบียน
const registerSchema = z.object({
  username: z.string().min(3, "ชื่อผู้ใช้ต้องมีอย่างน้อย 3 ตัวอักษร").max(50, "ชื่อผู้ใช้ต้องไม่เกิน 50 ตัวอักษร"),
  email: z.string().email("อีเมลไม่ถูกต้อง"),
  phoneNumber: z.string().min(10, "เบอร์โทรศัพท์ไม่ถูกต้อง").max(15, "เบอร์โทรศัพท์ไม่ถูกต้อง").optional().or(z.literal("")),
  firstName: z.string().min(1, "กรุณากรอกชื่อจริง").max(100, "ชื่อต้องไม่เกิน 100 ตัวอักษร"),
  lastName: z.string().min(1, "กรุณากรอกนามสกุล").max(100, "นามสกุลต้องไม่เกิน 100 ตัวอักษร"),
  companyName: z.string().max(100, "ชื่อบริษัทต้องไม่เกิน 100 ตัวอักษร").optional().or(z.literal("")),
  password: z.string().min(8, "รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร"),
  confirmPassword: z.string().min(1, "กรุณายืนยันรหัสผ่าน"),
  verificationMethod: z.enum(["email", "phone"]).default("email"),
}).refine((data) => data.password === data.confirmPassword, {
  message: "รหัสผ่านไม่ตรงกัน",
  path: ["confirmPassword"],
});

// Schema สำหรับฟอร์มลืมรหัสผ่าน
const forgotPasswordSchema = z.object({
  identifier: z.string().min(1, "กรุณากรอกอีเมลหรือเบอร์โทรศัพท์"),
  verificationMethod: z.enum(["email", "phone"]).default("email"),
});

// Schema สำหรับฟอร์มรีเซ็ตรหัสผ่าน
const resetPasswordSchema = z.object({
  verificationCode: z.string().min(1, "กรุณากรอกรหัสยืนยัน"),
  newPassword: z.string().min(8, "รหัสผ่านใหม่ต้องมีอย่างน้อย 8 ตัวอักษร"),
  confirmPassword: z.string().min(1, "กรุณายืนยันรหัสผ่านใหม่"),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "รหัสผ่านไม่ตรงกัน",
  path: ["confirmPassword"],
});

// ประเภทข้อมูลสำหรับฟอร์ม
type LoginFormValues = z.infer<typeof loginSchema>;
type RegisterFormValues = z.infer<typeof registerSchema>;
type ForgotPasswordFormValues = z.infer<typeof forgotPasswordSchema>;
type ResetPasswordFormValues = z.infer<typeof resetPasswordSchema>;

export default function AuthPageDivine() {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState<"login" | "register" | "verify" | "forgot-password">("login");
  const [lightningEffect, setLightningEffect] = useState(false);

  // คำคมไทยแบบเทพๆ กวนๆ
  const epicQuotes = [
    "การยืนยันตัวตนเหมือนการได้พบกับเทพเจ้า ต้องมีความอดทน",
    "ล็อกอินวันนี้ รับพรจากเทพเจ้าฟรี ไม่มีค่าใช้จ่าย",
    "อย่าลืมรหัสผ่าน เพราะแม้แต่เทพเจ้าก็ช่วยคุณไม่ได้",
    "สมัครสมาชิกวันนี้ รับส่วนลด 10% จากพระอิศวร",
    "เทพเจ้าก็ต้องยืนยันอีเมล เราเป็นใครจะไม่ต้องยืนยัน?",
    "หน้าล็อกอินที่สวยที่สุดในสามโลก",
    "รหัสผ่านควรมีความซับซ้อนพอๆ กับการต่อสู้ของเทพเจ้า",
    "ความปลอดภัยมาก่อนเสมอ แม้แต่เมื่อคุณเป็นเทพเจ้า",
  ];
  const [quote, setQuote] = useState(epicQuotes[Math.floor(Math.random() * epicQuotes.length)]);
  const [location, setLocation] = useLocation();

  // เมื่อเปลี่ยน tab เป็น forgot-password ให้นำทางไปยังหน้า password-reset
  useEffect(() => {
    if (activeTab === "forgot-password") {
      setLocation("/password-reset");
      // กลับมาที่แท็บ login หลังจากเด้งไปที่หน้า password-reset
      setActiveTab("login");
    }
  }, [activeTab, setLocation]);

  const { user, loginMutation, registerMutation } = useAuth();
  const [registrationStep, setRegistrationStep] = useState<"form" | "verification">("form");
  const [forgotPasswordStep, setForgotPasswordStep] = useState<"request" | "verify" | "reset">("request");
  const [verificationMethod, setVerificationMethod] = useState<"email" | "phone">("email");
  const [verificationCode, setVerificationCode] = useState("");
  const [isVerifying, setIsVerifying] = useState(false);
  const [isResettingPassword, setIsResettingPassword] = useState(false);
  const [secondsLeft, setSecondsLeft] = useState(0);
  const [tempUserData, setTempUserData] = useState<any>(null);
  const [resetPasswordData, setResetPasswordData] = useState<{
    identifier: string;
    type: "email" | "phone";
    userId?: number;
  } | null>(null);
  const [userVerificationStatus, setUserVerificationStatus] = useState<{
    email_verified?: boolean;
    phone_verified?: boolean;
  }>({});

  // สร้าง Form สำหรับการเข้าสู่ระบบ
  const loginForm = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: "",
      password: "",
    },
  });

  // สร้าง Form สำหรับการลงทะเบียน
  const registerForm = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      username: "",
      email: "",
      phoneNumber: "",
      firstName: "",
      lastName: "",
      companyName: "",
      password: "",
      confirmPassword: "",
      verificationMethod: "email",
    },
  });

  // สร้าง Form สำหรับการขอรีเซ็ตรหัสผ่าน
  const forgotPasswordForm = useForm<ForgotPasswordFormValues>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      identifier: "",
      verificationMethod: "email",
    },
  });

  // สร้าง Form สำหรับการรีเซ็ตรหัสผ่านหลังจากยืนยันตัวตน
  const resetPasswordForm = useForm<ResetPasswordFormValues>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      verificationCode: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  // เมื่อเปลี่ยนวิธีการยืนยัน
  useEffect(() => {
    const subscription = registerForm.watch((value, { name }) => {
      if (name === "verificationMethod") {
        setVerificationMethod(value.verificationMethod as "email" | "phone");
      }
    });
    return () => subscription.unsubscribe();
  }, [registerForm.watch]);

  // เช็คสถานะการยืนยันบัญชีเมื่อเข้าสู่ระบบแล้ว
  useEffect(() => {
    if (user) {
      // ตรวจสอบว่าผู้ใช้ยืนยันอีเมลหรือเบอร์โทรแล้วหรือไม่
      const needVerification = !user.email_verified && !user.phone_verified;

      // ถ้ายังไม่ได้ยืนยัน ให้อยู่ที่หน้านี้และแสดงแท็บยืนยัน
      if (needVerification) {
        setUserVerificationStatus({
          email_verified: user.email_verified || false,
          phone_verified: user.phone_verified || false
        });
        setActiveTab("verify");
      }
      else {
        // ถ้ายืนยันแล้ว ไปที่หน้าแดชบอร์ด
        setLocation("/dashboard");
      }
    }
  }, [user]);

  // นับเวลาถอยหลัง
  useEffect(() => {
    if (secondsLeft <= 0) return;

    const timer = setInterval(() => {
      setSecondsLeft(prev => prev - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [secondsLeft]);

  // เมื่อเปลี่ยนวิธีการยืนยันสำหรับลืมรหัสผ่าน
  useEffect(() => {
    const subscription = forgotPasswordForm.watch((value, { name }) => {
      if (name === "verificationMethod") {
        setVerificationMethod(value.verificationMethod as "email" | "phone");
      }
    });
    return () => subscription.unsubscribe();
  }, [forgotPasswordForm.watch]);

  // ส่งคำขอเข้าสู่ระบบ
  const onLoginSubmit = (values: LoginFormValues) => {
    loginMutation.mutate(values);
  };

  // ส่งคำขอลงทะเบียนขั้นแรก - ส่งข้อมูลและรับรหัสยืนยัน
  const onRegisterSubmit = async (values: RegisterFormValues) => {
    try {
      // ลบ confirmPassword ออกเพราะไม่ต้องส่งไปยัง API
      const { confirmPassword, ...registerData } = values;

      // เก็บข้อมูลชั่วคราวสำหรับส่งเมื่อยืนยันเรียบร้อย
      setTempUserData(registerData);

      // ส่งคำขอรหัสยืนยัน
      const identifier = values.verificationMethod === "email"
        ? values.email
        : values.phoneNumber;

      // ส่ง API เพื่อขอรหัสยืนยัน
      const response = await axios.post("/api/verification/send", {
        type: values.verificationMethod,
        identifier,
        userId: 0, // ใช้ 0 สำหรับผู้ใช้ใหม่ที่ยังไม่มี ID
      });

      if (response.data.success) {
        toast({
          title: "ส่งรหัสยืนยันสำเร็จ",
          description: response.data.message,
        });

        // เปลี่ยนสถานะเป็นรอยืนยัน
        setRegistrationStep("verification");

        // ตั้งเวลาถอยหลังสำหรับการขอรหัสใหม่
        setSecondsLeft(60); // 1 นาที
      } else {
        toast({
          variant: "destructive",
          title: "ไม่สามารถส่งรหัสยืนยันได้",
          description: response.data.message || "กรุณาลองใหม่อีกครั้ง",
        });
      }
    } catch (error: any) {
      console.error("Registration error:", error);
      toast({
        variant: "destructive",
        title: "ไม่สามารถส่งรหัสยืนยันได้",
        description: error.response?.data?.message || "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง",
      });
    }
  };

  // ตรวจสอบรหัสยืนยัน
  const verifyCode = async () => {
    if (!verificationCode || verificationCode.length < 6) {
      toast({
        variant: "destructive",
        title: "รหัสยืนยันไม่ถูกต้อง",
        description: "กรุณากรอกรหัสยืนยัน 6 หลัก",
      });
      return;
    }

    try {
      setIsVerifying(true);
      const identifier = tempUserData?.verificationMethod === "email"
        ? tempUserData.email
        : tempUserData.phoneNumber;

      // ส่ง API เพื่อตรวจสอบรหัสยืนยัน
      const verifyResponse = await axios.post("/api/verification/verify", {
        type: tempUserData?.verificationMethod,
        identifier,
        code: verificationCode,
      });

      if (verifyResponse.data.success) {
        // ลงทะเบียนผู้ใช้หลังจากยืนยันสำเร็จ
        registerMutation.mutate(tempUserData);

        // รีเซ็ตสถานะกลับไปที่ฟอร์ม
        setRegistrationStep("form");
        setVerificationCode("");
        setTempUserData(null);
      } else {
        toast({
          variant: "destructive",
          title: "รหัสยืนยันไม่ถูกต้อง",
          description: verifyResponse.data.message || "กรุณาตรวจสอบรหัสและลองใหม่อีกครั้ง",
        });
      }
    } catch (error: any) {
      console.error("Verification error:", error);
      toast({
        variant: "destructive",
        title: "ไม่สามารถยืนยันรหัสได้",
        description: error.response?.data?.message || "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง",
      });
    } finally {
      setIsVerifying(false);
    }
  };

  // ขอรหัสยืนยันใหม่
  const resendVerificationCode = async () => {
    if (secondsLeft > 0) return;

    try {
      // กรณีที่เป็นการสมัครใหม่
      if (tempUserData) {
        const identifier = tempUserData.verificationMethod === "email"
          ? tempUserData.email
          : tempUserData.phoneNumber;

        // ส่ง API เพื่อขอรหัสยืนยันใหม่
        const response = await axios.post("/api/verification/send", {
          type: tempUserData.verificationMethod,
          identifier,
          userId: 0,
        });

        if (response.data.success) {
          toast({
            title: "ส่งรหัสยืนยันใหม่สำเร็จ",
            description: response.data.message,
          });
          setSecondsLeft(60); // ตั้งเวลารอใหม่
        } else {
          toast({
            variant: "destructive",
            title: "ไม่สามารถส่งรหัสยืนยันได้",
            description: response.data.message || "กรุณาลองใหม่อีกครั้ง",
          });
        }
      }
      // กรณีที่เป็นผู้ใช้เก่า
      else if (user) {
        const identifier = verificationMethod === "email"
          ? user.email
          : user.phoneNumber;

        if (!identifier) {
          toast({
            variant: "destructive",
            title: "ไม่สามารถส่งรหัสยืนยันได้",
            description: "ไม่พบข้อมูลสำหรับการยืนยัน กรุณาตรวจสอบข้อมูลส่วนตัวของคุณ",
          });
          return;
        }

        // ส่ง API เพื่อขอรหัสยืนยันใหม่
        const response = await axios.post("/api/verification/send", {
          type: verificationMethod,
          identifier,
          userId: user.id,
        });

        if (response.data.success) {
          toast({
            title: "ส่งรหัสยืนยันใหม่สำเร็จ",
            description: response.data.message,
          });
          setSecondsLeft(60); // ตั้งเวลารอใหม่
        } else {
          toast({
            variant: "destructive",
            title: "ไม่สามารถส่งรหัสยืนยันได้",
            description: response.data.message || "กรุณาลองใหม่อีกครั้ง",
          });
        }
      }
      // กรณีที่เป็นการลืมรหัสผ่าน
      else if (resetPasswordData) {
        const { identifier, type } = resetPasswordData;

        // ส่ง API เพื่อขอรหัสยืนยันใหม่
        const response = await axios.post("/api/password-reset/send", {
          type,
          identifier,
        });

        if (response.data.success) {
          toast({
            title: "ส่งรหัสยืนยันใหม่สำเร็จ",
            description: response.data.message,
          });
          setSecondsLeft(60); // ตั้งเวลารอใหม่
        } else {
          toast({
            variant: "destructive",
            title: "ไม่สามารถส่งรหัสยืนยันได้",
            description: response.data.message || "กรุณาลองใหม่อีกครั้ง",
          });
        }
      }
    } catch (error: any) {
      console.error("Resend verification code error:", error);
      toast({
        variant: "destructive",
        title: "ไม่สามารถส่งรหัสยืนยันใหม่ได้",
        description: error.response?.data?.message || "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง",
      });
    }
  };

  // ยืนยันบัญชีผู้ใช้ที่มีอยู่แล้ว
  const verifyExistingUser = async () => {
    if (!verificationCode || verificationCode.length < 6) {
      toast({
        variant: "destructive",
        title: "รหัสยืนยันไม่ถูกต้อง",
        description: "กรุณากรอกรหัสยืนยัน 6 หลัก",
      });
      return;
    }

    if (!user) {
      toast({
        variant: "destructive",
        title: "ไม่สามารถยืนยันบัญชีได้",
        description: "กรุณาเข้าสู่ระบบก่อนทำการยืนยัน",
      });
      return;
    }

    try {
      setIsVerifying(true);
      const identifier = verificationMethod === "email"
        ? user.email
        : user.phoneNumber;

      if (!identifier) {
        toast({
          variant: "destructive",
          title: "ไม่สามารถยืนยันรหัสได้",
          description: "ไม่พบข้อมูลสำหรับการยืนยัน กรุณาตรวจสอบข้อมูลส่วนตัวของคุณ",
        });
        return;
      }

      // ส่ง API เพื่อตรวจสอบรหัสยืนยัน
      const verifyResponse = await axios.post("/api/verification/verify", {
        type: verificationMethod,
        identifier,
        code: verificationCode,
        userId: user.id,
      });

      if (verifyResponse.data.success) {
        toast({
          title: "ยืนยันบัญชีสำเร็จ",
          description: "บัญชีของคุณได้รับการยืนยันแล้ว",
        });

        // ไปที่หน้าแดชบอร์ด
        setLocation("/dashboard");
      } else {
        toast({
          variant: "destructive",
          title: "รหัสยืนยันไม่ถูกต้อง",
          description: verifyResponse.data.message || "กรุณาตรวจสอบรหัสและลองใหม่อีกครั้ง",
        });
      }
    } catch (error: any) {
      console.error("Verification error:", error);
      toast({
        variant: "destructive",
        title: "ไม่สามารถยืนยันรหัสได้",
        description: error.response?.data?.message || "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง",
      });
    } finally {
      setIsVerifying(false);
    }
  };

  // ส่งคำขอรีเซ็ตรหัสผ่าน
  const onForgotPasswordSubmit = async (values: ForgotPasswordFormValues) => {
    try {
      // ส่ง API เพื่อขอรหัสยืนยันสำหรับรีเซ็ตรหัสผ่าน
      const response = await axios.post("/api/password-reset/send", {
        type: values.verificationMethod,
        identifier: values.identifier,
      });

      if (response.data.success) {
        toast({
          title: "ส่งคำขอรีเซ็ตรหัสผ่านสำเร็จ",
          description: response.data.message,
        });

        // เก็บข้อมูลสำหรับใช้ต่อ
        setResetPasswordData({
          type: values.verificationMethod,
          identifier: values.identifier,
          userId: response.data.userId,
        });

        // เปลี่ยนสถานะเป็นรอยืนยัน
        setForgotPasswordStep("verify");

        // ตั้งเวลาถอยหลังสำหรับการขอรหัสใหม่
        setSecondsLeft(60); // 1 นาที
      } else {
        toast({
          variant: "destructive",
          title: "ไม่สามารถส่งคำขอรีเซ็ตรหัสผ่านได้",
          description: response.data.message || "กรุณาลองใหม่อีกครั้ง",
        });
      }
    } catch (error: any) {
      console.error("Password reset request error:", error);
      toast({
        variant: "destructive",
        title: "ไม่สามารถส่งคำขอรีเซ็ตรหัสผ่านได้",
        description: error.response?.data?.message || "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง",
      });
    }
  };

  // ตรวจสอบรหัสยืนยันสำหรับรีเซ็ตรหัสผ่าน
  const verifyPasswordResetCode = async () => {
    if (!verificationCode || verificationCode.length < 6) {
      toast({
        variant: "destructive",
        title: "รหัสยืนยันไม่ถูกต้อง",
        description: "กรุณากรอกรหัสยืนยัน 6 หลัก",
      });
      return;
    }

    if (!resetPasswordData) {
      toast({
        variant: "destructive",
        title: "ไม่สามารถยืนยันรหัสได้",
        description: "ข้อมูลการรีเซ็ตรหัสผ่านไม่ถูกต้อง กรุณาลองใหม่อีกครั้ง",
      });
      return;
    }

    try {
      setIsVerifying(true);

      // ส่ง API เพื่อตรวจสอบรหัสยืนยัน
      const verifyResponse = await axios.post("/api/password-reset/verify", {
        type: resetPasswordData.type,
        identifier: resetPasswordData.identifier,
        code: verificationCode,
      });

      if (verifyResponse.data.success) {
        // เปลี่ยนสถานะเป็นรีเซ็ตรหัสผ่าน
        setForgotPasswordStep("reset");
      } else {
        toast({
          variant: "destructive",
          title: "รหัสยืนยันไม่ถูกต้อง",
          description: verifyResponse.data.message || "กรุณาตรวจสอบรหัสและลองใหม่อีกครั้ง",
        });
      }
    } catch (error: any) {
      console.error("Verification error:", error);
      toast({
        variant: "destructive",
        title: "ไม่สามารถยืนยันรหัสได้",
        description: error.response?.data?.message || "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง",
      });
    } finally {
      setIsVerifying(false);
    }
  };

  // ส่งคำขอรีเซ็ตรหัสผ่าน
  const onResetPasswordSubmit = async (values: ResetPasswordFormValues) => {
    if (!resetPasswordData) {
      toast({
        variant: "destructive",
        title: "ไม่สามารถรีเซ็ตรหัสผ่านได้",
        description: "ข้อมูลการรีเซ็ตรหัสผ่านไม่ถูกต้อง กรุณาลองใหม่อีกครั้ง",
      });
      return;
    }

    try {
      setIsResettingPassword(true);

      // ส่ง API เพื่อรีเซ็ตรหัสผ่าน
      const response = await axios.post("/api/password-reset/reset", {
        identifier: resetPasswordData.identifier,
        type: resetPasswordData.type,
        code: verificationCode,
        newPassword: values.newPassword,
      });

      if (response.data.success) {
        toast({
          title: "รีเซ็ตรหัสผ่านสำเร็จ",
          description: "รหัสผ่านของคุณได้รับการเปลี่ยนแปลงแล้ว กรุณาเข้าสู่ระบบด้วยรหัสผ่านใหม่",
        });

        // กลับไปที่หน้าเข้าสู่ระบบ
        setActiveTab("login");
        setForgotPasswordStep("request");
        resetPasswordForm.reset();
        setVerificationCode("");
        setResetPasswordData(null);
      } else {
        toast({
          variant: "destructive",
          title: "ไม่สามารถรีเซ็ตรหัสผ่านได้",
          description: response.data.message || "กรุณาลองใหม่อีกครั้ง",
        });
      }
    } catch (error: any) {
      console.error("Password reset error:", error);
      toast({
        variant: "destructive",
        title: "ไม่สามารถรีเซ็ตรหัสผ่านได้",
        description: error.response?.data?.message || "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง",
      });
    } finally {
      setIsResettingPassword(false);
    }
  };

  // ฟังก์ชันสำหรับเปลี่ยนแท็บพร้อมเอฟเฟกต์ฟ้าผ่า
  const changeTab = (tab: "login" | "register" | "verify" | "forgot-password") => {
    setLightningEffect(true);
    setTimeout(() => setLightningEffect(false), 300);
    setActiveTab(tab);

    // สุ่มคำคมใหม่เมื่อเปลี่ยนแท็บ
    setQuote(epicQuotes[Math.floor(Math.random() * epicQuotes.length)]);
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-purple-950 via-indigo-900 to-purple-950 relative">

      {/* เอฟเฟกต์พื้นหลังอลังการ */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* ดาวระยิบระยับในจักรวาล */}
        <div className="absolute inset-0" style={{
          backgroundImage: 'radial-gradient(white, rgba(255, 255, 255, 0.2) 2px, transparent 2px)',
          backgroundSize: '50px 50px'
        }}></div>

        {/* วงแหวนพลังเทพเจ้า */}
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[150vh] h-[150vh]">
          <div className="absolute inset-0 rounded-full border-8 border-amber-400/10 animate-[spin_120s_linear_infinite]"></div>
          <div className="absolute inset-10 rounded-full border-4 border-pink-500/10 animate-[spin_100s_linear_infinite_reverse]"></div>
          <div className="absolute inset-20 rounded-full border-2 border-blue-400/10 animate-[spin_80s_linear_infinite]"></div>
        </div>

        {/* คลื่นพลังงานเทพเจ้า */}
        <div className="absolute bottom-0 inset-x-0 h-48 bg-gradient-to-t from-pink-500/20 to-transparent"></div>

        {/* ฟ้าแลบที่มุมจอ */}
        <div className="absolute top-1/4 right-10 w-20 h-40">
          <motion.div
            className="absolute inset-0 bg-yellow-300"
            initial={{ opacity: 0 }}
            animate={{
              opacity: [0, 0.8, 0],
              rotate: [0, 5, 0],
              pathLength: [0, 1, 0]
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              repeatDelay: 7
            }}
            style={{ clipPath: "polygon(50% 0%, 15% 50%, 50% 50%, 0% 100%, 50% 70%, 50% 50%, 85% 100%)" }}
          />
        </div>

        {/* เฮลิกซ์พลังงานลึกลับ */}
        <div className="absolute top-[10%] left-[5%] w-20 h-80 origin-center">
          <motion.div
            className="absolute top-0 left-0 right-0 h-6 rounded-full bg-purple-500/30 blur-sm"
            animate={{ y: [0, 80, 0], opacity: [0.2, 0.8, 0.2] }}
            transition={{ duration: 5, repeat: Infinity, ease: "easeInOut" }}
          />
          <motion.div
            className="absolute top-20 left-0 right-0 h-6 rounded-full bg-indigo-500/30 blur-sm"
            animate={{ y: [80, 0, 80], opacity: [0.2, 0.8, 0.2] }}
            transition={{ duration: 5, repeat: Infinity, ease: "easeInOut", delay: 1 }}
          />
        </div>

        {/* ฟ้าแลบเมื่อเปลี่ยนแท็บ */}
        <AnimatePresence>
          {lightningEffect && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 0.7 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="absolute inset-0 bg-white pointer-events-none"
            />
          )}
        </AnimatePresence>
      </div>

      <Navbar />

      <main className="relative z-10 flex-1 flex flex-col items-center justify-center p-4 mt-14">
        {/* ส่วนหัว */}
        <motion.div
          className="text-center mb-8 relative"
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ type: "spring", stiffness: 200, damping: 20 }}
        >
          <div className="relative inline-block">
            <motion.div
              className="absolute -inset-4 rounded-full bg-gradient-to-r from-amber-600/30 via-pink-500/30 to-purple-600/30 blur-2xl"
              animate={{
                scale: [1, 1.1, 1],
                rotate: [0, 5, 0]
              }}
              transition={{
                duration: 5,
                repeat: Infinity,
                repeatType: "reverse"
              }}
            />
            <h1 className="text-6xl font-bold bg-gradient-to-r from-amber-300 via-yellow-200 to-amber-400 bg-clip-text text-transparent font-serif relative">
              SLIPKUY
              <motion.div
                className="absolute -right-8 -top-8 text-amber-300"
                animate={{ rotate: 360 }}
                transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
              >
                <Crown className="h-10 w-10" />
              </motion.div>
            </h1>
          </div>
          <p className="mt-2 text-xl text-fuchsia-200 font-semibold">ระบบตรวจสอบสลิปแห่งเทพเจ้า</p>

          {/* คำคมกวนๆ */}
          <motion.div
            className="mt-4 max-w-xl mx-auto bg-indigo-950/50 p-3 rounded-lg border border-indigo-800/50 relative overflow-hidden group"
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 via-fuchsia-600/5 to-amber-600/10 group-hover:opacity-80 transition-opacity" />
            <Laugh className="h-5 w-5 text-amber-300 inline-block mr-2 mb-1" />
            <span className="italic text-indigo-200">{quote}</span>
            <motion.div
              className="absolute bottom-0 left-0 h-[2px] bg-gradient-to-r from-amber-400 via-fuchsia-500 to-purple-500"
              initial={{ width: "0%" }}
              animate={{ width: "100%" }}
              transition={{ duration: 3 }}
            />
          </motion.div>
        </motion.div>

        {/* ส่วนหลักสำหรับล็อกอิน/ลงทะเบียน */}
        <div className="flex-1 w-full lg:w-3/5">
          <Card className="bg-purple-950/50 border-purple-800 backdrop-blur-sm shadow-2xl">
            <CardHeader className="space-y-1">
              <CardTitle className="text-3xl font-bold bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 bg-clip-text text-transparent">
                {activeTab === "login"
                  ? "เข้าสู่ระบบ SLIPKUY"
                  : activeTab === "register"
                    ? "สมัครสมาชิก SLIPKUY"
                    : activeTab === "verify"
                      ? "ยืนยันบัญชีของคุณ"
                      : "ลืมรหัสผ่าน"}
              </CardTitle>
              <CardDescription className="text-purple-300">
                {activeTab === "login"
                  ? "กรอกข้อมูลเพื่อเข้าสู่ระบบ"
                  : activeTab === "register"
                    ? "สร้างบัญชีใหม่เพื่อใช้งานระบบ"
                    : activeTab === "verify"
                      ? "ยืนยันบัญชีของคุณเพื่อเข้าใช้งานระบบเต็มรูปแบบ"
                      : "กรอกข้อมูลเพื่อรีเซ็ตรหัสผ่านของคุณ"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* แท็บสำหรับเลือกระหว่างล็อกอินและลงทะเบียน */}
              {!user && (
                <Tabs
                  defaultValue="login"
                  value={activeTab}
                  onValueChange={(value) => changeTab(value as any)}
                  className="w-full mb-6"
                >
                  <TabsList className="grid w-full grid-cols-2 bg-purple-900/50">
                    <TabsTrigger
                      value="login"
                      className="text-purple-300 data-[state=active]:bg-gradient-to-r data-[state=active]:from-pink-600/80 data-[state=active]:to-purple-600/80 data-[state=active]:text-white"
                    >
                      เข้าสู่ระบบ
                    </TabsTrigger>
                    <TabsTrigger
                      value="register"
                      className="text-purple-300 data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-600/80 data-[state=active]:to-indigo-600/80 data-[state=active]:text-white"
                    >
                      สมัครสมาชิก
                    </TabsTrigger>
                  </TabsList>

                  {/* แท็บเข้าสู่ระบบ */}
                  <TabsContent value="login" className="mt-4">
                    <Form {...loginForm}>
                      <form onSubmit={loginForm.handleSubmit(onLoginSubmit)} className="space-y-4">
                        <FormField
                          control={loginForm.control}
                          name="username"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-white">ชื่อผู้ใช้</FormLabel>
                              <FormControl>
                                <div className="relative">
                                  <User className="absolute left-3 top-3 h-5 w-5 text-purple-400" />
                                  <Input
                                    placeholder="กรอกชื่อผู้ใช้"
                                    {...field}
                                    className="pl-10 bg-purple-900/30 border-purple-700 text-white"
                                  />
                                </div>
                              </FormControl>
                              <FormMessage className="text-red-300" />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={loginForm.control}
                          name="password"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-white">รหัสผ่าน</FormLabel>
                              <FormControl>
                                <div className="relative">
                                  <Lock className="absolute left-3 top-3 h-5 w-5 text-purple-400" />
                                  <Input
                                    type="password"
                                    placeholder="กรอกรหัสผ่าน"
                                    {...field}
                                    className="pl-10 bg-purple-900/30 border-purple-700 text-white"
                                  />
                                </div>
                              </FormControl>
                              <FormMessage className="text-red-300" />
                            </FormItem>
                          )}
                        />
                        <Button
                          type="submit"
                          className="w-full mt-4 bg-gradient-to-r from-amber-500/90 to-amber-600/90 hover:from-amber-600/90 hover:to-amber-700/90 text-white font-semibold text-base shadow-lg backdrop-blur-sm"
                          disabled={loginMutation.isPending}
                        >
                          {loginMutation.isPending ? (
                            <>
                              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              กำลังเข้าสู่ระบบ...
                            </>
                          ) : (
                            "เข้าสู่ระบบ"
                          )}
                        </Button>

                        <div className="text-sm text-center mt-4 text-purple-300">
                          <span>ลืมรหัสผ่าน? </span>
                          <button
                            type="button"
                            onClick={() => setLocation("/password-reset")}
                            className="text-pink-400 hover:underline"
                          >
                            คลิกที่นี่
                          </button>
                        </div>
                      </form>
                    </Form>
                  </TabsContent>

                  {/* แท็บสมัครสมาชิก */}
                  <TabsContent value="register" className="mt-4">
                    {registrationStep === "form" ? (
                      <Form {...registerForm}>
                        <form onSubmit={registerForm.handleSubmit(onRegisterSubmit)} className="space-y-4">
                          {/* ชื่อผู้ใช้ */}
                          <FormField
                            control={registerForm.control}
                            name="username"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-white">ชื่อผู้ใช้ <span className="text-red-400">*</span></FormLabel>
                                <FormControl>
                                  <div className="relative">
                                    <User className="absolute left-3 top-3 h-5 w-5 text-purple-400" />
                                    <Input
                                      placeholder="กรอกชื่อผู้ใช้"
                                      {...field}
                                      className="pl-10 bg-purple-900/30 border-purple-700 text-white"
                                    />
                                  </div>
                                </FormControl>
                                <FormMessage className="text-red-300" />
                              </FormItem>
                            )}
                          />

                          {/* อีเมล */}
                          <FormField
                            control={registerForm.control}
                            name="email"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-white">อีเมล <span className="text-red-400">*</span></FormLabel>
                                <FormControl>
                                  <div className="relative">
                                    <MailIcon className="absolute left-3 top-3 h-5 w-5 text-purple-400" />
                                    <Input
                                      placeholder="กรอกอีเมล"
                                      {...field}
                                      className="pl-10 bg-purple-900/30 border-purple-700 text-white"
                                    />
                                  </div>
                                </FormControl>
                                <FormMessage className="text-red-300" />
                              </FormItem>
                            )}
                          />

                          {/* เบอร์โทรศัพท์ */}
                          <FormField
                            control={registerForm.control}
                            name="phoneNumber"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-white">เบอร์โทรศัพท์</FormLabel>
                                <FormControl>
                                  <div className="relative">
                                    <Phone className="absolute left-3 top-3 h-5 w-5 text-purple-400" />
                                    <Input
                                      type="tel"
                                      placeholder="กรอกเบอร์โทรศัพท์"
                                      {...field}
                                      className="pl-10 bg-purple-900/30 border-purple-700 text-white"
                                    />
                                  </div>
                                </FormControl>
                                <FormMessage className="text-red-300" />
                              </FormItem>
                            )}
                          />

                          {/* รหัสผ่าน */}
                          <FormField
                            control={registerForm.control}
                            name="password"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-white">รหัสผ่าน <span className="text-red-400">*</span></FormLabel>
                                <FormControl>
                                  <div className="relative">
                                    <LockKeyhole className="absolute left-3 top-3 h-5 w-5 text-purple-400" />
                                    <Input
                                      type="password"
                                      placeholder="กรอกรหัสผ่าน"
                                      {...field}
                                      className="pl-10 bg-purple-900/30 border-purple-700 text-white"
                                    />
                                  </div>
                                </FormControl>
                                <FormMessage className="text-red-300" />
                              </FormItem>
                            )}
                          />

                          {/* ยืนยันรหัสผ่าน */}
                          <FormField
                            control={registerForm.control}
                            name="confirmPassword"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-white">ยืนยันรหัสผ่าน <span className="text-red-400">*</span></FormLabel>
                                <FormControl>
                                  <div className="relative">
                                    <LockKeyhole className="absolute left-3 top-3 h-5 w-5 text-purple-400" />
                                    <Input
                                      type="password"
                                      placeholder="กรอกรหัสผ่านอีกครั้ง"
                                      {...field}
                                      className="pl-10 bg-purple-900/30 border-purple-700 text-white"
                                    />
                                  </div>
                                </FormControl>
                                <FormMessage className="text-red-300" />
                              </FormItem>
                            )}
                          />

                          {/* วิธีการยืนยันตัวตน */}
                          <FormField
                            control={registerForm.control}
                            name="verificationMethod"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-white">วิธีการยืนยันตัวตน <span className="text-red-400">*</span></FormLabel>
                                <div className="flex space-x-4">
                                  <Button
                                    type="button"
                                    variant={field.value === "email" ? "default" : "outline"}
                                    className={`flex-1 ${field.value === "email" ? "bg-gradient-to-r from-purple-600 to-indigo-600" : "bg-purple-900/30 text-white border-purple-700"}`}
                                    onClick={() => registerForm.setValue("verificationMethod", "email")}
                                  >
                                    <MailIcon className="mr-2 h-4 w-4" />
                                    ทางอีเมล
                                  </Button>
                                  <Button
                                    type="button"
                                    variant={field.value === "phone" ? "default" : "outline"}
                                    className={`flex-1 ${field.value === "phone" ? "bg-gradient-to-r from-purple-600 to-indigo-600" : "bg-purple-900/30 text-white border-purple-700"}`}
                                    onClick={() => registerForm.setValue("verificationMethod", "phone")}
                                  >
                                    <PhoneCall className="mr-2 h-4 w-4" />
                                    ทาง SMS
                                  </Button>
                                </div>
                                <FormMessage className="text-red-300" />
                              </FormItem>
                            )}
                          />

                          <Button
                            type="submit"
                            className="w-full mt-4 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 font-semibold"
                          >
                            สมัครสมาชิกและรับรหัสยืนยัน
                          </Button>
                        </form>
                      </Form>
                    ) : (
                      <div className="space-y-6 animate__animated animate__fadeIn">
                        <div className="text-center mb-6">
                          <div className="bg-indigo-900/50 p-4 rounded-lg border border-indigo-700/50 mb-4">
                            <p className="text-indigo-200 mb-2">
                              รหัสยืนยันได้ถูกส่งไปยัง {verificationMethod === "email" ? "อีเมล" : "เบอร์โทรศัพท์"} ของคุณแล้ว
                            </p>
                            <p className="text-amber-300 font-semibold">
                              {verificationMethod === "email" ? (tempUserData?.email || "") : (tempUserData?.phoneNumber || "")}
                            </p>
                          </div>

                          <div className="space-y-4">
                            <div className="relative">
                              <Input
                                type="text"
                                placeholder="กรอกรหัสยืนยัน 6 หลัก"
                                value={verificationCode}
                                onChange={(e) => setVerificationCode(e.target.value)}
                                className="pl-4 pr-10 py-6 text-xl tracking-widest text-center bg-purple-900/30 border-purple-700 text-white"
                                maxLength={6}
                              />
                            </div>

                            <Button
                              onClick={verifyCode}
                              className="w-full mt-4 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 font-semibold"
                              disabled={isVerifying}
                            >
                              {isVerifying ? (
                                <>
                                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                  </svg>
                                  กำลังตรวจสอบ...
                                </>
                              ) : (
                                "ยืนยันรหัส"
                              )}
                            </Button>

                            <div className="text-center mt-4">
                              <p className="text-gray-300 text-sm">
                                ไม่ได้รับรหัส?{" "}
                                <button
                                  type="button"
                                  onClick={resendVerificationCode}
                                  disabled={secondsLeft > 0}
                                  className={`text-pink-400 hover:underline ${secondsLeft > 0 ? "opacity-50 cursor-not-allowed" : ""}`}
                                >
                                  ส่งรหัสใหม่
                                </button>
                                {secondsLeft > 0 && <span className="text-gray-400 ml-2">({secondsLeft})</span>}
                              </p>
                            </div>

                            <div className="text-center mt-4">
                              <button
                                type="button"
                                onClick={() => setRegistrationStep("form")}
                                className="text-indigo-300 hover:text-indigo-200 text-sm"
                              >
                                &larr; กลับไปแก้ไขข้อมูล
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              )}

              {/* แท็บยืนยันบัญชี */}
              {user && activeTab === "verify" && (
                <div className="space-y-6 animate__animated animate__fadeIn">
                  <div className="text-center mb-6">
                    <div className="p-4 bg-amber-950/30 border border-amber-800/30 rounded-lg mb-4">
                      <p className="text-amber-200">กรุณายืนยันตัวตนเพื่อเข้าใช้งานระบบเต็มรูปแบบ</p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                      <div
                        className={`p-4 rounded-lg border ${userVerificationStatus.email_verified
                          ? "bg-green-900/20 border-green-700/30"
                          : "bg-purple-900/30 border-purple-700 cursor-pointer hover:bg-purple-900/40"}`}
                        onClick={() => !userVerificationStatus.email_verified && setVerificationMethod("email")}
                      >
                        <div className="flex items-center mb-2">
                          <MailIcon className={`h-5 w-5 mr-2 ${userVerificationStatus.email_verified ? "text-green-400" : "text-purple-400"}`} />
                          <span className={`font-medium ${userVerificationStatus.email_verified ? "text-green-300" : "text-purple-300"}`}>ยืนยันทางอีเมล</span>
                          {userVerificationStatus.email_verified && (
                            <CheckCircle className="h-5 w-5 text-green-400 ml-auto" />
                          )}
                        </div>
                        <p className={`text-sm ${userVerificationStatus.email_verified ? "text-green-200" : "text-purple-200"}`}>
                          {userVerificationStatus.email_verified
                            ? "คุณได้ยืนยันอีเมลแล้ว"
                            : verificationMethod === "email"
                              ? "วิธีการที่เลือกในขณะนี้"
                              : "คลิกเพื่อยืนยันทางอีเมล"}
                        </p>
                      </div>

                      <div
                        className={`p-4 rounded-lg border ${userVerificationStatus.phone_verified
                          ? "bg-green-900/20 border-green-700/30"
                          : "bg-purple-900/30 border-purple-700 cursor-pointer hover:bg-purple-900/40"}`}
                        onClick={() => !userVerificationStatus.phone_verified && setVerificationMethod("phone")}
                      >
                        <div className="flex items-center mb-2">
                          <PhoneCall className={`h-5 w-5 mr-2 ${userVerificationStatus.phone_verified ? "text-green-400" : "text-purple-400"}`} />
                          <span className={`font-medium ${userVerificationStatus.phone_verified ? "text-green-300" : "text-purple-300"}`}>ยืนยันทาง SMS</span>
                          {userVerificationStatus.phone_verified && (
                            <CheckCircle className="h-5 w-5 text-green-400 ml-auto" />
                          )}
                        </div>
                        <p className={`text-sm ${userVerificationStatus.phone_verified ? "text-green-200" : "text-purple-200"}`}>
                          {userVerificationStatus.phone_verified
                            ? "คุณได้ยืนยันเบอร์โทรศัพท์แล้ว"
                            : verificationMethod === "phone"
                              ? "วิธีการที่เลือกในขณะนี้"
                              : "คลิกเพื่อยืนยันทาง SMS"}
                        </p>
                      </div>
                    </div>

                    {(!userVerificationStatus.email_verified || !userVerificationStatus.phone_verified) && (
                      <div className="space-y-4">
                        <div className="text-center">
                          <p className="text-purple-200 mb-4">
                            กรุณายืนยันตัวตนด้วย {verificationMethod === "email" ? "อีเมล" : "เบอร์โทรศัพท์"}
                          </p>

                          {(verificationMethod === "email" && !user.email) || (verificationMethod === "phone" && !user.phoneNumber) ? (
                            <div className="bg-red-900/30 border border-red-800/50 p-4 rounded-lg mb-4">
                              <p className="text-red-300">
                                คุณยังไม่ได้กำหนด {verificationMethod === "email" ? "อีเมล" : "เบอร์โทรศัพท์"} กรุณาไปที่การตั้งค่าบัญชีเพื่อเพิ่มข้อมูล
                              </p>
                              <Button
                                onClick={() => setLocation("/profile")}
                                className="mt-2 bg-red-700 hover:bg-red-800"
                              >
                                ไปที่การตั้งค่าบัญชี
                              </Button>
                            </div>
                          ) : (
                            <>
                              <p className="text-amber-300 font-semibold mb-4">
                                {verificationMethod === "email" ? user.email : user.phoneNumber}
                              </p>

                              <div className="space-y-4">
                                {secondsLeft === 0 && (
                                  <Button
                                    onClick={resendVerificationCode}
                                    className="w-full bg-indigo-700 hover:bg-indigo-800"
                                  >
                                    <MailIcon className="mr-2 h-4 w-4" />
                                    ส่งรหัสยืนยัน
                                  </Button>
                                )}

                                {secondsLeft > 0 && (
                                  <div className="relative mt-4">
                                    <Input
                                      type="text"
                                      placeholder="กรอกรหัสยืนยัน 6 หลัก"
                                      value={verificationCode}
                                      onChange={(e) => setVerificationCode(e.target.value)}
                                      className="pl-4 pr-10 py-6 text-xl tracking-widest text-center bg-purple-900/30 border-purple-700 text-white"
                                      maxLength={6}
                                    />

                                    <Button
                                      onClick={verifyExistingUser}
                                      className="w-full mt-4 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 font-semibold"
                                      disabled={isVerifying}
                                    >
                                      {isVerifying ? (
                                        <>
                                          <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                          </svg>
                                          กำลังตรวจสอบ...
                                        </>
                                      ) : (
                                        "ยืนยันรหัส"
                                      )}
                                    </Button>

                                    <div className="text-center mt-4">
                                      <p className="text-gray-300 text-sm">
                                        ไม่ได้รับรหัส?{" "}
                                        <button
                                          type="button"
                                          onClick={resendVerificationCode}
                                          disabled={secondsLeft > 0}
                                          className={`text-pink-400 hover:underline ${secondsLeft > 0 ? "opacity-50 cursor-not-allowed" : ""}`}
                                        >
                                          ส่งรหัสใหม่
                                        </button>
                                        {secondsLeft > 0 && <span className="text-gray-400 ml-2">({secondsLeft})</span>}
                                      </p>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
            <CardFooter>
              <div className="text-xs text-purple-400 w-full text-center">
                <p>
                  สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า
                </p>
              </div>
            </CardFooter>
          </Card>
        </div>
      </main>

      <Footer />
    </div>
  );
}