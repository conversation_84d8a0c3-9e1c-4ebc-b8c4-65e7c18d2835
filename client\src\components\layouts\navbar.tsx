import { useState, useEffect } from "react";
import { Link, useLocation } from "wouter";
import { useAuth } from "@/hooks/use-auth";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import {
  ReceiptTextIcon,
  Menu,
  X,
  User,
  LogOut,
  Settings,
  SparklesIcon,
  Zap,
  Crown,
  Sun,
  CloudLightning,
  CircleUserRound,
  ScrollText,
  Package
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

export function Navbar() {
  const [location] = useLocation();
  const { user, logoutMutation } = useAuth();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [scrolled]);

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const handleLogout = () => {
    logoutMutation.mutate();
  };

  return (
    <header className="sticky top-0 z-50 transition-all duration-500 backdrop-blur-md bg-transparent border-b border-amber-300/20 shadow-lg shadow-amber-900/5">
      {/* เส้นคั่นเป็นลายสายฟ้าทอง - แบบโปร่งใสเหมือนกระจก */}
      <div className="h-0.5 w-full bg-gradient-to-r from-amber-500/30 via-amber-300/50 to-amber-500/30 relative overflow-hidden"></div>

      <div className="container mx-auto px-4 md:px-6 lg:px-8 relative">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center">
            <Link href="/">
              <motion.div
                className="flex items-center space-x-2 cursor-pointer group"
                whileHover={{ scale: 1.05 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <div className="relative">
                  {/* Symbol glow effect */}
                  <div className="absolute -inset-3 bg-gradient-to-r from-yellow-400 to-amber-300 opacity-40
                    rounded-full blur-md group-hover:opacity-70 transition-opacity duration-300"></div>

                  {/* Divine symbol */}
                  <div className="relative flex items-center justify-center h-10 w-10 bg-gradient-to-r
                    from-indigo-600 to-purple-600 rounded-full">
                    <Crown className="h-5 w-5 text-yellow-300 group-hover:text-yellow-200 transition-colors duration-300" />

                    {/* Animated rays */}
                    <motion.div
                      className="absolute -inset-2 border border-yellow-400/30 rounded-full"
                      animate={{ scale: [1, 1.2, 1], opacity: [0.5, 0.2, 0.5] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    />
                  </div>
                </div>
                <div className="text-2xl font-extrabold">
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-purple-500">SLIP</span>
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-amber-400 to-yellow-300">KUY</span>
                </div>

                {/* Mini lightning effect beside logo */}
                <motion.div
                  className="absolute -top-1 left-10 opacity-70"
                  initial={{ opacity: 0, rotate: -15, scale: 0.7 }}
                  animate={{ opacity: [0, 0.7, 0], rotate: [-15, -10, -15], scale: [0.7, 0.8, 0.7] }}
                  transition={{ duration: 1.5, repeat: Infinity, repeatDelay: 5 }}
                >
                  <CloudLightning className="h-3 w-3 text-yellow-300" />
                </motion.div>
              </motion.div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {[
              { href: "/", label: "หน้าหลัก", icon: <Sun className="h-4 w-4" /> },
              { href: "/docs", label: "API Docs", icon: <ScrollText className="h-4 w-4" /> },
              { href: "/packages", label: "แพ็กเกจ", icon: <Package className="h-4 w-4" /> },
              ...(user ? [{ href: "/dashboard", label: "แดชบอร์ด", icon: <Crown className="h-4 w-4" /> }] : [])
            ].map((item) => (
              <Link key={item.href} href={item.href}>
                <motion.div
                  className={`relative cursor-pointer py-2 text-base font-medium flex items-center gap-1.5 group
                  ${location === item.href
                    ? 'gold-text font-semibold'
                    : 'text-white hover:text-amber-200 transition-colors duration-300'}`}
                  whileHover={{ y: -2 }}
                  transition={{ type: "spring", stiffness: 400, damping: 17 }}
                >
                  {/* Animate icon on hover */}
                  <motion.span
                    className={`${location === item.href ? 'text-amber-400' : 'text-white'} group-hover:text-amber-300 transition-colors`}
                    whileHover={{ rotate: [0, -10, 10, -10, 0] }}
                    transition={{ duration: 0.5 }}
                  >
                    {item.icon}
                  </motion.span>

                  {/* Add gold shimmer effect to text */}
                  <span className={`relative ${location === item.href ? 'gold-text font-semibold' : 'celestial-text'}`}>
                    {item.label}
                  </span>

                  {/* Active indicator with divine glow */}
                  {location === item.href && (
                    <motion.span
                      className="absolute -bottom-1 left-0 w-full h-0.5 bg-gradient-to-r from-amber-400 to-yellow-300 rounded-full divine-glow"
                      layoutId="navbar-indicator"
                      transition={{ type: "spring", stiffness: 400, damping: 25 }}
                    />
                  )}
                </motion.div>
              </Link>
            ))}
          </nav>

          <div className="hidden md:flex items-center space-x-3">
            {user ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="relative px-4 py-2 rounded-full overflow-hidden group border-amber-400/30 bg-indigo-800/30 hover:bg-indigo-700/50">
                    {/* Celestial background effect with improved contrast */}
                    <div className="absolute inset-0 bg-gradient-to-r from-indigo-600/30 to-purple-600/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                    {/* Animated border - brighter and wider */}
                    <div className="absolute inset-0 rounded-full overflow-hidden">
                      <div className="absolute inset-[-2px] bg-gradient-to-r from-amber-300 via-yellow-200 to-amber-300
                        opacity-40 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>

                    {/* Pulsing glow effect */}
                    <div className="absolute inset-0 rounded-full bg-amber-400/10 animate-pulse-custom opacity-50"></div>

                    <div className="flex items-center gap-2 relative z-10">
                      {/* Vibrant user icon with colorful background */}
                      <div className="relative">
                        {/* Vibrant background glow */}
                        <div className="absolute -inset-1 bg-gradient-to-r from-amber-300 to-yellow-300 rounded-full opacity-60 blur-sm"></div>

                        {/* Main circle background with fun colors */}
                        <div className="h-7 w-7 rounded-full bg-gradient-to-br from-indigo-500 via-purple-500 to-fuchsia-500 flex items-center justify-center relative divine-glow border border-white/20">
                          <CircleUserRound className="h-4 w-4 text-white drop-shadow-md" />

                          {/* Small stars effect around the icon */}
                          <div className="absolute -top-1 -right-1 w-2 h-2 bg-yellow-300 rounded-full animate-pulse"></div>
                          <div className="absolute bottom-0 -left-1 w-1.5 h-1.5 bg-amber-300 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
                        </div>
                      </div>

                      <div>
                        <span className="font-bold text-sm gold-text drop-shadow-md">{user.username}</span>
                      </div>
                    </div>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56 bg-gradient-to-b from-indigo-900/95 to-purple-900/95 text-white border border-amber-400/30 shadow-lg">
                  <DropdownMenuLabel className="text-amber-300 font-bold text-center">
                    <div className="flex items-center justify-center">
                      <Crown className="h-4 w-4 mr-2 text-amber-300" />
                      <span>บัญชีของเทพเจ้า</span>
                    </div>
                  </DropdownMenuLabel>
                  <div className="lightning-bar mx-3 my-1"></div>

                  <div className="px-2 py-1">
                    <DropdownMenuItem asChild className="bg-indigo-800/50 hover:bg-indigo-700/70 rounded-md my-1 focus:bg-indigo-700/70">
                      <Link href="/dashboard" className="flex items-center px-2 py-1">
                        <Sun className="mr-2 h-4 w-4 text-amber-300" />
                        <span>แดชบอร์ด</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild className="bg-indigo-800/50 hover:bg-indigo-700/70 rounded-md my-1 focus:bg-indigo-700/70">
                      <Link href="/verify" className="flex items-center px-2 py-1">
                        <Zap className="mr-2 h-4 w-4 text-amber-300" />
                        <span>ตรวจสอบสลิป</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild className="bg-indigo-800/50 hover:bg-indigo-700/70 rounded-md my-1 focus:bg-indigo-700/70">
                      <Link href="/history" className="flex items-center px-2 py-1">
                        <ScrollText className="mr-2 h-4 w-4 text-amber-300" />
                        <span>ประวัติการตรวจสอบ</span>
                      </Link>
                    </DropdownMenuItem>
                  </div>

                  {user.role === 'admin' && (
                    <>
                      <div className="lightning-bar mx-3 my-1"></div>
                      <div className="px-2 py-1">
                        <DropdownMenuItem asChild className="bg-purple-800/50 hover:bg-purple-700/70 rounded-md my-1 focus:bg-purple-700/70">
                          <Link href="/admin" className="flex items-center px-2 py-1">
                            <Crown className="mr-2 h-4 w-4 text-amber-300" />
                            <span>แดชบอร์ดเทพเจ้า</span>
                          </Link>
                        </DropdownMenuItem>
                      </div>
                    </>
                  )}

                  <div className="lightning-bar mx-3 my-1"></div>
                  <div className="px-2 py-1">
                    <DropdownMenuItem onClick={handleLogout} className="bg-red-800/50 hover:bg-red-700/70 rounded-md my-1 focus:bg-red-700/70 text-red-200 flex items-center px-2 py-1">
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>ออกจากระบบ</span>
                    </DropdownMenuItem>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <>
                {/* ซ่อนปุ่มเข้าสู่ระบบและลงทะเบียนเมื่ออยู่ในหน้า /auth */}
                {location !== "/auth" && (
                  <>
                    <Link href="/auth">
                      <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.98 }}>
                        <Button variant="outline" className="text-white hover:text-amber-300 border-white/30 hover:border-amber-300/70 px-5 py-2 rounded-full font-medium relative overflow-hidden group">
                          <div className="absolute inset-0 bg-gradient-to-r from-indigo-600/50 to-purple-600/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                          <CloudLightning className="h-4 w-4 mr-2 text-amber-300" />
                          <span className="relative z-10 celestial-text">เข้าสู่ระบบ</span>

                          {/* เพิ่มแสงวาบรอบปุ่มเมื่อ hover */}
                          <div className="absolute -inset-0.5 rounded-full opacity-0 blur-md bg-amber-300/30 group-hover:opacity-100 transition-all duration-300"></div>
                        </Button>
                      </motion.div>
                    </Link>
                    <Link href="/auth">
                      <motion.div
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.98 }}
                        className="relative"
                      >
                        <div className="absolute -inset-1 bg-gradient-to-r from-amber-400 to-yellow-300 rounded-full opacity-80 blur-md animate-pulse-custom"></div>
                        <Button className="relative bg-gradient-to-r from-indigo-600 to-purple-700 hover:bg-gradient-to-r hover:from-indigo-500 hover:to-purple-600 text-white px-5 py-2 rounded-full font-medium shadow-lg">
                          <Crown className="h-4 w-4 mr-2 text-amber-300" />
                          <span className="celestial-text font-bold">ลงทะเบียน</span>
                        </Button>
                      </motion.div>
                    </Link>
                  </>
                )}
              </>
            )}
          </div>

          {/* Mobile menu button - เรียบง่ายและใช้งานง่าย */}
          <div className="md:hidden">
            <Button
              variant="outline"
              size="icon"
              onClick={toggleMobileMenu}
              aria-label={mobileMenuOpen ? "Close Menu" : "Open Menu"}
              className="relative w-10 h-10 text-white border-amber-400/30 bg-indigo-950/30 hover:bg-indigo-800/30 focus:bg-indigo-800/30 focus:outline-none rounded-full"
            >
              {/* Toggle icon with subtle animation */}
              <AnimatePresence mode="wait">
                {mobileMenuOpen ? (
                  <motion.div
                    key="close"
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0.8, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    className="absolute inset-0 flex items-center justify-center"
                  >
                    <X className="h-5 w-5 text-amber-300" />
                  </motion.div>
                ) : (
                  <motion.div
                    key="menu"
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0.8, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    className="absolute inset-0 flex items-center justify-center"
                  >
                    <Menu className="h-5 w-5 text-amber-300" />
                  </motion.div>
                )}
              </AnimatePresence>
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <AnimatePresence>
          {mobileMenuOpen && (
            <motion.div
              className="md:hidden pb-4 backdrop-blur-md bg-indigo-950/80 text-white rounded-xl shadow-lg mt-2 border border-amber-400/30"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >

              <div className="px-3 pt-3 pb-3 space-y-1">
                {/* Mobile menu items - เรียบง่ายและใช้งานง่าย */}
                <Link href="/">
                  <div className={`flex items-center px-3 py-2.5 rounded-lg text-base font-medium transition-colors
                    ${location === '/'
                      ? 'bg-indigo-700/50 text-amber-300'
                      : 'text-white hover:bg-indigo-800/50 hover:text-amber-300'}`}>
                    <Sun className="h-5 w-5 mr-3 text-amber-300" />
                    <span>หน้าหลัก</span>
                  </div>
                </Link>

                <Link href="/docs">
                  <div className={`flex items-center px-3 py-2.5 rounded-lg text-base font-medium transition-colors
                    ${location === '/docs'
                      ? 'bg-indigo-700/50 text-amber-300'
                      : 'text-white hover:bg-indigo-800/50 hover:text-amber-300'}`}>
                    <ScrollText className="h-5 w-5 mr-3 text-amber-300" />
                    <span>API Docs</span>
                  </div>
                </Link>

                <Link href="/packages">
                  <div className={`flex items-center px-3 py-2.5 rounded-lg text-base font-medium transition-colors
                    ${location === '/packages'
                      ? 'bg-indigo-700/50 text-amber-300'
                      : 'text-white hover:bg-indigo-800/50 hover:text-amber-300'}`}>
                    <Package className="h-5 w-5 mr-3 text-amber-300" />
                    <span>แพ็กเกจ</span>
                  </div>
                </Link>

                {user && (
                  <>
                    {/* เส้นคั่นเรียบง่าย */}
                    <div className="border-t border-indigo-700/50 my-2"></div>

                    <Link href="/dashboard">
                      <div className={`flex items-center px-3 py-2.5 rounded-lg text-base font-medium transition-colors
                        ${location.startsWith('/dashboard')
                          ? 'bg-indigo-700/50 text-amber-300'
                          : 'text-white hover:bg-indigo-800/50 hover:text-amber-300'}`}>
                        <Crown className="h-5 w-5 mr-3 text-amber-300" />
                        <span>แดชบอร์ด</span>
                      </div>
                    </Link>

                    <Link href="/verify">
                      <div className={`flex items-center px-3 py-2.5 rounded-lg text-base font-medium transition-colors
                        ${location === '/verify'
                          ? 'bg-indigo-700/50 text-amber-300'
                          : 'text-white hover:bg-indigo-800/50 hover:text-amber-300'}`}>
                        <Zap className="h-5 w-5 mr-3 text-amber-300" />
                        <span>ตรวจสอบสลิป</span>
                      </div>
                    </Link>

                    <Link href="/history">
                      <div className={`flex items-center px-3 py-2.5 rounded-lg text-base font-medium transition-colors
                        ${location === '/history'
                          ? 'bg-indigo-700/50 text-amber-300'
                          : 'text-white hover:bg-indigo-800/50 hover:text-amber-300'}`}>
                        <ScrollText className="h-5 w-5 mr-3 text-amber-300" />
                        <span>ประวัติการตรวจสอบ</span>
                      </div>
                    </Link>

                    {user.role === 'admin' && (
                      <Link href="/admin">
                        <div className={`flex items-center px-3 py-2.5 rounded-lg text-base font-medium transition-colors
                          ${location.startsWith('/admin')
                            ? 'bg-purple-700/50 text-amber-300'
                            : 'text-white hover:bg-purple-800/50 hover:text-amber-300'}`}>
                          <Crown className="h-5 w-5 mr-3 text-amber-300" />
                          <span>แดชบอร์ดแอดมิน</span>
                        </div>
                      </Link>
                    )}
                  </>
                )}

                <div className="border-t border-indigo-700/50 my-2"></div>

                <div className="pt-1 px-2 flex flex-col space-y-2">
                  {user ? (
                    <button
                      onClick={handleLogout}
                      className="w-full py-2.5 rounded-lg flex items-center justify-center bg-red-900/30 hover:bg-red-800/50 text-white border border-red-700/30"
                    >
                      <LogOut className="h-5 w-5 mr-2 text-red-300" />
                      <span>ออกจากระบบ</span>
                    </button>
                  ) : (
                    <>
                      {/* ซ่อนปุ่มเข้าสู่ระบบและลงทะเบียนเมื่ออยู่ในหน้า /auth */}
                      {location !== "/auth" && (
                        <>
                          <Link href="/auth" className="w-full">
                            <div className="py-2.5 rounded-lg text-center flex items-center justify-center bg-indigo-900/50 text-white hover:bg-indigo-800/50 border border-indigo-700/30">
                              <CloudLightning className="h-5 w-5 mr-2 text-amber-300" />
                              <span>เข้าสู่ระบบ</span>
                            </div>
                          </Link>

                          <Link href="/auth" className="w-full">
                            <div className="py-2.5 rounded-lg text-center flex items-center justify-center bg-indigo-700/60 text-amber-300 hover:bg-indigo-600/60 border border-amber-400/30">
                              <Crown className="h-5 w-5 mr-2 text-amber-300" />
                              <span>ลงทะเบียน</span>
                            </div>
                          </Link>
                        </>
                      )}
                    </>
                  )}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </header>
  );
}
