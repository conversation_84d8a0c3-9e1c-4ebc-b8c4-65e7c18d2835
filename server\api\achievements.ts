import { Router, Express } from 'express';
import { z } from 'zod';
import { storage } from '../storage';
import { logger } from '../logger';
import { achievements, achievementTypeEnum } from '@shared/schema';

// นำเข้า middleware สำหรับตรวจสอบสิทธิ์การเข้าถึง
import { requireVerified, requireAdmin } from '../middleware/verification-middleware';

const router = Router();

// Schema สำหรับตรวจสอบข้อมูลนำเข้า
const achievementSchema = z.object({
  name: z.string().min(1, 'ชื่อความสำเร็จไม่ควรว่าง'),
  description: z.string().min(1, 'คำอธิบายความสำเร็จไม่ควรว่าง'),
  type: z.enum(achievementTypeEnum.enumValues, {
    errorMap: (issue, ctx) => ({
      message: `ประเภทความสำเร็จไม่ถูกต้อง ต้องเป็นหนึ่งใน: ${achievementTypeEnum.enumValues.join(', ')}`
    })
  }),
  level: z.number().int().positive('ระดับความสำเร็จต้องเป็นจำนวนเต็มบวก'),
  requirement: z.number().int().positive('เงื่อนไขความสำเร็จต้องเป็นจำนวนเต็มบวก'),
  points: z.number().int().nonnegative('คะแนนต้องเป็นจำนวนเต็มไม่ติดลบ'),
  packageId: z.number().int().nonnegative().optional(),
  icon: z.string().default('award'),
});

// ดึงข้อมูลความสำเร็จทั้งหมดสำหรับผู้ใช้
router.get('/', requireVerified, async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: 'กรุณาล็อกอินเพื่อดูความสำเร็จของคุณ' });
    }

    const userAchievements = await storage.listUserAchievements(userId);
    res.json(userAchievements);
  } catch (error) {
    logger.error(`[API] ไม่สามารถดึงข้อมูลความสำเร็จ: ${error instanceof Error ? error.message : 'ข้อผิดพลาดที่ไม่ทราบสาเหตุ'}`);
    res.status(500).json({ error: 'ไม่สามารถดึงข้อมูลความสำเร็จได้' });
  }
});

// รับรางวัลจากความสำเร็จ
router.post('/:achievementId/complete', requireVerified, async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: 'กรุณาล็อกอินเพื่อรับรางวัลจากความสำเร็จ' });
    }

    const achievementId = parseInt(req.params.achievementId);
    if (isNaN(achievementId)) {
      return res.status(400).json({ error: 'รหัสความสำเร็จไม่ถูกต้อง' });
    }

    // ตรวจสอบว่าผู้ใช้มีสิทธิ์รับรางวัลหรือไม่
    const userAchievement = await storage.getUserAchievement(userId, achievementId);
    
    if (!userAchievement) {
      return res.status(404).json({ error: 'ไม่พบข้อมูลความสำเร็จนี้สำหรับผู้ใช้' });
    }

    if (userAchievement.completed) {
      return res.status(400).json({ error: 'คุณได้รับรางวัลจากความสำเร็จนี้ไปแล้ว' });
    }

    if (userAchievement.progress < userAchievement.achievement.requirement) {
      return res.status(400).json({ error: 'คุณยังไม่บรรลุเงื่อนไขเพื่อรับรางวัลนี้' });
    }

    // อัปเดตสถานะความสำเร็จเป็น "completed" และเพิ่มคะแนนให้ผู้ใช้
    const updatedAchievement = await storage.completeUserAchievement(userId, achievementId);
    
    // เพิ่มคะแนนให้ผู้ใช้
    await storage.addUserPoints(userId, userAchievement.achievement.points);

    res.json({
      success: true,
      achievement: updatedAchievement,
      pointsEarned: userAchievement.achievement.points
    });
  } catch (error) {
    logger.error(`[API] ไม่สามารถรับรางวัลความสำเร็จ: ${error instanceof Error ? error.message : 'ข้อผิดพลาดที่ไม่ทราบสาเหตุ'}`);
    res.status(500).json({ error: 'ไม่สามารถรับรางวัลความสำเร็จได้' });
  }
});

// === API สำหรับแอดมิน ===

// ดึงข้อมูลความสำเร็จทั้งหมด (สำหรับแอดมิน)
router.get('/admin/all', requireAdmin, async (req, res) => {
  try {
    const allAchievements = await storage.listAchievements();
    res.json(allAchievements);
  } catch (error) {
    logger.error(`[API] ไม่สามารถดึงข้อมูลความสำเร็จทั้งหมด: ${error instanceof Error ? error.message : 'ข้อผิดพลาดที่ไม่ทราบสาเหตุ'}`);
    res.status(500).json({ error: 'ไม่สามารถดึงข้อมูลความสำเร็จทั้งหมดได้' });
  }
});

// เพิ่มความสำเร็จใหม่ (สำหรับแอดมิน)
router.post('/admin/create', requireAdmin, async (req, res) => {
  try {
    const achievementData = achievementSchema.parse(req.body);
    
    const newAchievement = await storage.createAchievement(achievementData);
    
    res.status(201).json(newAchievement);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'ข้อมูลไม่ถูกต้อง', details: error.errors });
    }
    
    logger.error(`[API] ไม่สามารถเพิ่มความสำเร็จใหม่: ${error instanceof Error ? error.message : 'ข้อผิดพลาดที่ไม่ทราบสาเหตุ'}`);
    res.status(500).json({ error: 'ไม่สามารถเพิ่มความสำเร็จใหม่ได้' });
  }
});

// แก้ไขความสำเร็จ (สำหรับแอดมิน)
router.put('/admin/:achievementId', requireAdmin, async (req, res) => {
  try {
    const achievementId = parseInt(req.params.achievementId);
    if (isNaN(achievementId)) {
      return res.status(400).json({ error: 'รหัสความสำเร็จไม่ถูกต้อง' });
    }
    
    const achievementData = achievementSchema.parse(req.body);
    
    const updatedAchievement = await storage.updateAchievement(achievementId, achievementData);
    
    if (!updatedAchievement) {
      return res.status(404).json({ error: 'ไม่พบความสำเร็จที่ต้องการแก้ไข' });
    }
    
    res.json(updatedAchievement);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'ข้อมูลไม่ถูกต้อง', details: error.errors });
    }
    
    logger.error(`[API] ไม่สามารถแก้ไขความสำเร็จ: ${error instanceof Error ? error.message : 'ข้อผิดพลาดที่ไม่ทราบสาเหตุ'}`);
    res.status(500).json({ error: 'ไม่สามารถแก้ไขความสำเร็จได้' });
  }
});

// ลบความสำเร็จ (สำหรับแอดมิน)
router.delete('/admin/:achievementId', requireAdmin, async (req, res) => {
  try {
    const achievementId = parseInt(req.params.achievementId);
    if (isNaN(achievementId)) {
      return res.status(400).json({ error: 'รหัสความสำเร็จไม่ถูกต้อง' });
    }
    
    const deleted = await storage.deleteAchievement(achievementId);
    
    if (!deleted) {
      return res.status(404).json({ error: 'ไม่พบความสำเร็จที่ต้องการลบ' });
    }
    
    res.json({ success: true, message: 'ลบความสำเร็จเรียบร้อยแล้ว' });
  } catch (error) {
    logger.error(`[API] ไม่สามารถลบความสำเร็จ: ${error instanceof Error ? error.message : 'ข้อผิดพลาดที่ไม่ทราบสาเหตุ'}`);
    res.status(500).json({ error: 'ไม่สามารถลบความสำเร็จได้' });
  }
});

function registerAchievementRoutes(app: Express) {
  app.use('/api/achievements', router);
}

export default registerAchievementRoutes;