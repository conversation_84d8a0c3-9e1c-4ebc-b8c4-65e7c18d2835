import { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useLocation } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { 
  <PERSON>, 
  CardHeader, 
  CardContent, 
  CardFooter 
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, TabsContent } from "@/components/ui/tabs";
import { 
  Skeleton,
  Badge,
  Separator,
} from "@/components/ui";
import { 
  Check, 
  X, 
  CreditCard, 
  AlertTriangle,
  LucideIcon,
  CloudLightning,
  Zap,
  ShieldCheck,
  Bell,
  Gauge,
  LineChart,
  Activity,
  ArrowRight
} from "lucide-react";
import { motion } from "framer-motion";
import { apiRequest } from "@/lib/queryClient";
import { PackageCard } from "@/components/packages/package-card";
import { useAuth } from "@/hooks/use-auth";
import { BackgroundGradientAnimation } from "@/components/ui/background-gradient-animation";
import { calculatePriceWithDiscount } from "@/lib/price-utils";

// Define the Feature interface
interface Feature {
  name: string;
  included: boolean;
}

// Define the Package interface
interface Package {
  id: number;
  name: string;
  description: string;
  price: number;
  features: string;
  requestsLimit: number;
  tag?: string; // ป้ายกำกับ เช่น "ยอดนิยม", "แนะนำ", "คุ้มค่า" เป็นต้น
  discount3Months: number; // เปอร์เซ็นต์ส่วนลดเมื่อสมัคร 3 เดือน
  discount6Months: number; // เปอร์เซ็นต์ส่วนลดเมื่อสมัคร 6 เดือน
  discount12Months: number; // เปอร์เซ็นต์ส่วนลดเมื่อสมัคร 12 เดือน
  creditPerVerification: number; // จำนวนเครดิตที่ใช้ต่อ 1 การตรวจสอบ (ใช้เมื่อเครดิตในแพ็กเกจหมด)
}

export default function PackagesPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [location, navigate] = useLocation();
  const [selectedDuration, setSelectedDuration] = useState<number>(1);
  
  // Fetch packages from the API
  const { data: packages, isLoading, error } = useQuery<Package[]>({
    queryKey: ["/api/packages"],
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
  
  // กรณีที่ error ออกแจ้งเตือน
  useEffect(() => {
    if (error) {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: "ไม่สามารถโหลดข้อมูลแพ็กเกจได้ โปรดลองใหม่อีกครั้ง",
        variant: "destructive",
      });
    }
  }, [error, toast]);
  
  // Function to parse features string into an array of Feature objects
  const parseFeatures = (featuresString: string = ""): Feature[] => {
    try {
      return JSON.parse(featuresString);
    } catch {
      // If parsing fails, split by commas and mark all as included
      return featuresString.split(',').map(name => ({
        name: name.trim(),
        included: true
      }));
    }
  };
  
  // Handle subscription and show success message
  const handleSubscriptionSuccess = () => {
    toast({
      title: "สมัครแพ็กเกจสำเร็จ",
      description: "ขอบคุณที่สมัครใช้บริการของเรา อวยพรให้คุณโชคดีและร่ำรวย",
      variant: "success",
    });
    
    // รอสักครู่แล้วนำไปที่หน้า dashboard
    setTimeout(() => {
      navigate("/dashboard");
    }, 1500);
  };
  
  // Handle subscription for not logged in users
  const handleNotLoggedInSubscribe = () => {
    toast({
      title: "กรุณาเข้าสู่ระบบ",
      description: "คุณต้องเข้าสู่ระบบก่อนสมัครแพ็กเกจ",
      variant: "info",
    });
    
    // นำไปที่หน้า login
    navigate("/auth");
  };
  
  // คำนวณส่วนลดเฉลี่ยของแพ็กเกจทั้งหมด (ไม่รวมแพ็กเกจฟรี)
  const getAverageDiscountPercent = (durationMonths: number): number => {
    if (!packages || packages.length === 0) return 0;
    
    const paidPackages = packages.filter(pkg => pkg.price > 0);
    if (paidPackages.length === 0) return 0;
    
    let totalDiscount = 0;
    
    if (durationMonths === 3) {
      totalDiscount = paidPackages.reduce((sum, pkg) => sum + (pkg.discount3Months || 0), 0);
    } else if (durationMonths === 6) {
      totalDiscount = paidPackages.reduce((sum, pkg) => sum + (pkg.discount6Months || 0), 0);
    } else if (durationMonths === 12) {
      totalDiscount = paidPackages.reduce((sum, pkg) => sum + (pkg.discount12Months || 0), 0);
    }
    
    return Math.round(totalDiscount / paidPackages.length);
  };

  // ฟังก์ชันสำหรับเปลี่ยนค่า duration เมื่อเลือกแท็บใหม่
  const handleTabChange = (value: string) => {
    console.log(`เลือกแท็บ: ${value}`);
    
    // กำหนดระยะเวลาตามแท็บที่เลือก
    if (value === "monthly") {
      setSelectedDuration(1);
    } else if (value === "3months") {
      setSelectedDuration(3);
    } else if (value === "6months") {
      setSelectedDuration(6);
    } else if (value === "yearly") {
      setSelectedDuration(12);
    }
  };

  // ฟังก์ชันสร้าง PackageCards ตามระยะเวลาที่เลือก
  const renderPackageCards = (durationMonths: number) => {
    if (isLoading) {
      return (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {[...Array(3)].map((_, index) => (
            <div key={index} className="border border-neutral-200/20 rounded-xl p-6 bg-indigo-900/20">
              <Skeleton className="h-8 w-1/2 mb-1 bg-indigo-700/20" />
              <Skeleton className="h-4 w-3/4 mb-4 bg-indigo-700/20" />
              <Skeleton className="h-10 w-1/2 mb-6 bg-indigo-700/20" />
              <div className="space-y-3 mb-6">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="flex items-start">
                    <Skeleton className="h-5 w-5 mr-2 bg-indigo-700/20" />
                    <Skeleton className="h-5 w-full bg-indigo-700/20" />
                  </div>
                ))}
              </div>
              <Skeleton className="h-10 w-full bg-indigo-700/20" />
            </div>
          ))}
        </div>
      );
    }
    
    if (!packages || packages.length === 0) {
      return (
        <div className="text-center py-12">
          <p className="text-indigo-400">ไม่พบข้อมูลแพ็กเกจ</p>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {packages.map((pkg, index) => (
          <PackageCard
            key={pkg.id}
            id={pkg.id}
            name={pkg.name}
            description={pkg.description}
            price={pkg.price}
            requestsLimit={pkg.requestsLimit}
            isPopular={pkg.name === 'มืออาชีพ' || pkg.name === 'ธรรดา'} 
            features={parseFeatures(pkg.features)}
            tag={pkg.tag}
            durationMonths={durationMonths}
            discount3Months={pkg.discount3Months || 0}
            discount6Months={pkg.discount6Months || 0}
            discount12Months={pkg.discount12Months || 0}
            creditPerVerification={pkg.creditPerVerification}
            onSubscribe={() => {
              if (user) {
                const subscribeWithDuration = async () => {
                  try {
                    await apiRequest("POST", "/api/user/subscribe", { 
                      packageId: pkg.id,
                      durationMonths
                    });
                    handleSubscriptionSuccess();
                  } catch (error) {
                    console.error("Error subscribing:", error);
                    toast({
                      title: "เกิดข้อผิดพลาด",
                      description: "ไม่สามารถสมัครแพ็กเกจได้ โปรดลองใหม่อีกครั้ง",
                      variant: "destructive",
                    });
                  }
                };
                subscribeWithDuration();
              } else {
                handleNotLoggedInSubscribe();
              }
            }}
          />
        ))}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-950 via-blue-950 to-violet-950 py-12 px-4 sm:px-6 lg:px-8 relative">
      {/* พื้นหลังรูปดาวกระพริบ */}
      <div className="absolute inset-0 overflow-hidden z-0">
        <div className="absolute inset-0 bg-[url('/assets/stars-bg.svg')] bg-repeat opacity-30"></div>
        
        {/* ลูกบอลแสงสว่างเคลื่อนไหว */}
        {[...Array(5)].map((_, i) => (
          <motion.div 
            key={i}
            className="absolute rounded-full bg-indigo-600/20 blur-3xl"
            style={{
              width: `${Math.random() * 30 + 20}vw`,
              height: `${Math.random() * 30 + 20}vh`,
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              opacity: Math.random() * 0.3 + 0.1,
            }}
            animate={{
              x: [0, Math.random() * 40 - 20, 0],
              y: [0, Math.random() * 40 - 20, 0],
              opacity: [Math.random() * 0.3 + 0.1, Math.random() * 0.3 + 0.2, Math.random() * 0.3 + 0.1],
            }}
            transition={{
              duration: Math.random() * 30 + 20,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>
      
      <div className="relative z-10 max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <motion.h1 
            className="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-amber-300 to-yellow-200 mb-4 inline-block"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            แพ็กเกจ SLIPKUY
          </motion.h1>
          
          <motion.div
            className="max-w-3xl mx-auto mt-6 text-indigo-300 text-lg"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <p>เลือกแพ็กเกจที่เหมาะกับความต้องการของคุณ</p>
            <motion.div 
              className="text-sm text-indigo-400 mt-2 font-medium"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <span>คำอวยพรจากเทพเจ้าแห่งความมั่งคั่ง</span>
            </motion.div>
          </div>

          <Tabs defaultValue="monthly" onValueChange={handleTabChange} className="mb-8">
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              <TabsList className="grid w-[600px] max-w-full grid-cols-4 mx-auto backdrop-blur-md bg-indigo-900/30 p-1.5 shadow-xl rounded-full border border-amber-400/30">
                <TabsTrigger 
                  value="monthly" 
                  className="rounded-full font-medium text-white data-[state=active]:bg-gradient-to-r data-[state=active]:from-amber-400 data-[state=active]:to-yellow-300 data-[state=active]:text-indigo-900 data-[state=active]:font-bold data-[state=active]:shadow-lg hover:text-amber-300 transition-all py-2"
                >
                  1 เดือน
                </TabsTrigger>
                <TabsTrigger 
                  value="3months" 
                  className="rounded-full font-medium text-white data-[state=active]:bg-gradient-to-r data-[state=active]:from-amber-400 data-[state=active]:to-yellow-300 data-[state=active]:text-indigo-900 data-[state=active]:font-bold data-[state=active]:shadow-lg hover:text-amber-300 transition-all py-2"
                >
                  <div className="flex flex-col items-center justify-center">
                    <span>3 เดือน</span>
                    {packages && packages.some(p => p.discount3Months > 0) && (
                      <span className="text-xs text-amber-300 data-[state=active]:text-indigo-800">ลด {getAverageDiscountPercent(3)}%</span>
                    )}
                  </div>
                </TabsTrigger>
                <TabsTrigger 
                  value="6months" 
                  className="rounded-full font-medium text-white data-[state=active]:bg-gradient-to-r data-[state=active]:from-amber-400 data-[state=active]:to-yellow-300 data-[state=active]:text-indigo-900 data-[state=active]:font-bold data-[state=active]:shadow-lg hover:text-amber-300 transition-all py-2"
                >
                  <div className="flex flex-col items-center justify-center">
                    <span>6 เดือน</span>
                    {packages && packages.some(p => p.discount6Months > 0) && (
                      <span className="text-xs text-amber-300 data-[state=active]:text-indigo-800">ลด {getAverageDiscountPercent(6)}%</span>
                    )}
                  </div>
                </TabsTrigger>
                <TabsTrigger 
                  value="yearly" 
                  className="rounded-full font-medium text-white data-[state=active]:bg-gradient-to-r data-[state=active]:from-amber-400 data-[state=active]:to-yellow-300 data-[state=active]:text-indigo-900 data-[state=active]:font-bold data-[state=active]:shadow-lg hover:text-amber-300 transition-all py-2"
                >
                  <div className="flex flex-col items-center justify-center">
                    <span>1 ปี</span>
                    {packages && packages.some(p => p.discount12Months > 0) && (
                      <span className="text-xs text-amber-300 data-[state=active]:text-indigo-800">ลด {getAverageDiscountPercent(12)}%</span>
                    )}
                  </div>
                </TabsTrigger>
              </TabsList>
            </motion.div>

            <TabsContent value="monthly" className="mt-8">
              {renderPackageCards(1)}
            </TabsContent>

            <TabsContent value="3months" className="mt-8">
              {renderPackageCards(3)}
            </TabsContent>
            
            <TabsContent value="6months" className="mt-8">
              {renderPackageCards(6)}
            </TabsContent>
            
            <TabsContent value="yearly" className="mt-8">
              {renderPackageCards(12)}
            </TabsContent>
          </Tabs>
      
          <motion.div 
            className="mt-16 mb-8 text-lg text-indigo-200 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <h2 className="text-xl font-bold mb-4 text-transparent bg-clip-text bg-gradient-to-r from-indigo-300 to-purple-300">
              ทำไมต้องใช้งานบริการของ SLIPKUY?
            </h2>
            <p className="mb-4">
              บริการของเราช่วยให้ธุรกิจของคุณประหยัดเวลาในการตรวจสอบสลิปธนาคาร ด้วยความแม่นยำสูงสุดถึง 99.9%
            </p>
            <p>
              เราให้ความสำคัญกับความปลอดภัยและความเป็นส่วนตัวของข้อมูล พร้อมทั้งมีทีมสนับสนุนที่พร้อมช่วยเหลือตลอด 24 ชั่วโมง
            </p>
          </motion.div>
      
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto mt-12">
            <motion.div 
              className="backdrop-blur-sm bg-indigo-900/20 rounded-xl p-6 border border-indigo-800/50"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              whileHover={{ y: -5, boxShadow: "0 10px 30px -10px rgba(139, 92, 246, 0.3)" }}
            >
              <div className="h-12 w-12 rounded-lg bg-gradient-to-br from-purple-500 to-indigo-600 flex items-center justify-center mb-4">
                <ShieldCheck className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2">ความปลอดภัยสูงสุด</h3>
              <p className="text-indigo-300 text-sm">
                ระบบของเราใช้เทคโนโลยีการเข้ารหัสข้อมูลระดับสูง เพื่อปกป้องข้อมูลสำคัญของคุณ
              </p>
            </motion.div>
      
            <motion.div 
              className="backdrop-blur-sm bg-indigo-900/20 rounded-xl p-6 border border-indigo-800/50"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              whileHover={{ y: -5, boxShadow: "0 10px 30px -10px rgba(139, 92, 246, 0.3)" }}
            >
              <div className="h-12 w-12 rounded-lg bg-gradient-to-br from-amber-500 to-yellow-400 flex items-center justify-center mb-4">
                <Bell className="h-6 w-6 text-indigo-900" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2">แจ้งเตือนทันที</h3>
              <p className="text-indigo-300 text-sm">
                รับการแจ้งเตือนทันทีเมื่อมีการทำรายการใหม่ ช่วยให้คุณไม่พลาดทุกความเคลื่อนไหว
              </p>
            </motion.div>
      
            <motion.div 
              className="backdrop-blur-sm bg-indigo-900/20 rounded-xl p-6 border border-indigo-800/50"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.6 }}
              whileHover={{ y: -5, boxShadow: "0 10px 30px -10px rgba(139, 92, 246, 0.3)" }}
            >
              <div className="h-12 w-12 rounded-lg bg-gradient-to-br from-emerald-500 to-green-400 flex items-center justify-center mb-4">
                <Activity className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2">รายงานละเอียด</h3>
              <p className="text-indigo-300 text-sm">
                ดูรายงานและสถิติการใช้งานอย่างละเอียด ช่วยให้คุณเข้าใจและบริหารธุรกิจได้ดียิ่งขึ้น
              </p>
            </motion.div>
          </div>
        </div>
      </div>
      
      <div className="relative z-10 mt-24 pb-8 text-center">
        <p className="text-indigo-400 text-sm">สงวนลิขสิทธิ์ © 2025 สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>
      </div>
    </div>
  );
}