import { useState, useEffect } from "react";
import { DashboardLayout } from "@/components/layouts/dashboard-layout";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { th } from 'date-fns/locale';
import { CalendarIcon, BarChart3, TrendingUp, AlertCircle, AreaChart, History, Search, Download } from "lucide-react";
import { 
  LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, 
  Tooltip as RechartsTooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell 
} from 'recharts';
import Papa from 'papaparse';
import { saveAs } from 'file-saver';
import { useQuery } from "@tanstack/react-query";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

export default function AdvancedSearchPage() {
  // States for UI
  const [activeTab, setActiveTab] = useState("analyze");
  
  // State for analyze tab
  const [analysisType, setAnalysisType] = useState("usage_summary");
  const [timeframe, setTimeframe] = useState("30days");
  const [fromDate, setFromDate] = useState<Date | undefined>(undefined);
  const [toDate, setToDate] = useState<Date | undefined>(undefined);
  const [includeDetails, setIncludeDetails] = useState(false);
  const [analysisResults, setAnalysisResults] = useState<any>(null);
  const [isLoadingAnalysis, setIsLoadingAnalysis] = useState(false);
  
  // State for correlation tab
  const [correlationType, setCorrelationType] = useState("transaction_patterns");
  const [correlationTimeframe, setCorrelationTimeframe] = useState("90days");
  const [correlationResults, setCorrelationResults] = useState<any>(null);
  const [isLoadingCorrelation, setIsLoadingCorrelation] = useState(false);
  
  // State for prediction tab
  const [predictionType, setPredictionType] = useState("usage_forecast");
  const [predictionTimeframe, setPredictionTimeframe] = useState("90days");
  const [predictionResults, setPredictionResults] = useState<any>(null);
  const [isLoadingPrediction, setIsLoadingPrediction] = useState(false);
  
  const { toast } = useToast();
  
  // CSV Export Functions - รวมข้อมูลในไฟล์เดียว
  const exportToCSV = () => {
    // กำหนดชื่อไฟล์
    const filename = `SLIPKUY_PRM_รายงานการวิเคราะห์ขั้นสูง_${format(new Date(), 'yyyyMMdd_HHmmss')}.csv`;
    
    // เพิ่มข้อมูลบริษัทและหัวเอกสาร
    const documentInfo = [
      ['รายงานการวิเคราะห์ข้อมูลขั้นสูง - SLIPKUY PRM ANALYTICS SYSTEM'],
      ['บริษัท SLIPKUY จำกัด'],
      ['วันที่สร้าง:', format(new Date(), 'PPP', { locale: th })],
      ['เวลาที่สร้าง:', format(new Date(), 'HH:mm:ss', { locale: th }), 'น.'],
      ['ผู้สร้างรายงาน:', 'ระบบ SLIPKUY PRM Analytics'],
      ['เลขที่อ้างอิง:', `SLIPKUY-REPORT-${format(new Date(), 'yyyyMMdd-HHmmss')}`],
      [''],
    ];
    
    let csvContent: any[] = [];
    csvContent = [...documentInfo];
    
    // สร้างฟังก์ชันที่ใช้เก็บข้อมูลจากแต่ละแท็บเพื่อรวมในไฟล์เดียว
    const addAnalysisData = () => {
      if (!analysisResults) return [];
      
      const timeframeText = fromDate && toDate 
        ? `${format(fromDate, 'PPP', { locale: th })} ถึง ${format(toDate, 'PPP', { locale: th })}`
        : timeframe === '7days' ? '7 วันล่าสุด'
        : timeframe === '30days' ? '30 วันล่าสุด'
        : timeframe === '90days' ? '90 วันล่าสุด'
        : timeframe === '1year' ? '1 ปีล่าสุด'
        : 'ทั้งหมด';
      
      const analysisTitle = analysisType === 'usage_summary' ? 'สรุปการใช้งาน'
        : analysisType === 'bank_analysis' ? 'วิเคราะห์ตามธนาคาร'
        : analysisType === 'time_analysis' ? 'วิเคราะห์ตามเวลา'
        : analysisType === 'transaction_patterns' ? 'รูปแบบธุรกรรม'
        : 'การใช้งาน API';
      
      let analysisData = [
        ['ส่วนที่ 1: ผลการวิเคราะห์ข้อมูล'],
        ['รายงาน:', analysisTitle],
        ['ช่วงเวลา:', timeframeText],
        [''],
        ['สรุปผลการวิเคราะห์'],
        ['จำนวนรายการทั้งหมด', analysisResults.results.totalVerifications || 0],
        ['จำนวนเงินรวม', `${analysisResults.results.totalAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`],
        ['จำนวนเงินเฉลี่ยต่อรายการ', `${analysisResults.results.averageAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`],
        [''],
      ];
      
      // รายละเอียดตามประเภทการวิเคราะห์
      if (analysisType === 'bank_analysis' && analysisResults.results.bankDistribution) {
        analysisData.push(['การวิเคราะห์ตามธนาคาร'], ['ธนาคาร', 'จำนวนรายการ', 'เปอร์เซ็นต์', 'จำนวนเงินรวม'], ['']);
        
        analysisResults.results.bankDistribution.forEach((bank: any) => {
          analysisData.push([
            bank.name || 'ไม่ระบุ',
            bank.count,
            `${bank.percentage.toFixed(2)}%`,
            `${bank.totalAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`
          ]);
        });
      }
      
      // เพิ่มรายการต่างๆ ตามประเภทข้อมูล
      if (analysisType === 'time_analysis' && analysisResults.results.timeDistribution) {
        analysisData.push(['การวิเคราะห์ตามช่วงเวลา'], ['ช่วงเวลา', 'จำนวนรายการ', 'เปอร์เซ็นต์', 'จำนวนเงินรวม'], ['']);
        
        analysisResults.results.timeDistribution.forEach((time: any) => {
          analysisData.push([
            time.time || 'ไม่ระบุ',
            time.count,
            `${time.percentage.toFixed(2)}%`,
            `${time.totalAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`
          ]);
        });
      }
      
      // รายการทั้งหมด (ถ้ามี)
      if (includeDetails && analysisResults.transactions && analysisResults.transactions.length > 0) {
        analysisData.push([''], ['รายการทั้งหมด'], ['วันที่', 'เวลา', 'จำนวนเงิน', 'ธนาคารผู้รับ', 'ผู้รับ', 'สถานะ'], ['']);
        
        analysisResults.transactions.forEach((tx: any) => {
          const date = tx.createdAt ? new Date(tx.createdAt) : null;
          analysisData.push([
            date ? format(date, 'PPP', { locale: th }) : '-',
            date ? format(date, 'HH:mm', { locale: th }) : '-',
            `${tx.amount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`,
            tx.bankName || '-',
            tx.receiver || '-',
            tx.status || '-'
          ]);
        });
      }
      
      return analysisData;
    };
    
    const addCorrelationData = () => {
      if (!correlationResults) return [];
      
      let correlationData = [
        [''],
        ['ส่วนที่ 2: การวิเคราะห์ความสัมพันธ์'],
        ['ประเภท:', correlationType === 'transaction_patterns' ? 'รูปแบบการทำรายการ' : 'ตรวจจับความผิดปกติ'],
        ['ช่วงเวลา:', correlationTimeframe === '30days' ? '30 วันล่าสุด' : 
                    correlationTimeframe === '90days' ? '90 วันล่าสุด' : 
                    correlationTimeframe === '180days' ? '180 วันล่าสุด' : '1 ปีล่าสุด'],
        [''],
        ['สรุปผลการวิเคราะห์ความสัมพันธ์'],
      ];
      
      if (correlationType === 'transaction_patterns') {
        correlationData.push(
          ['รูปแบบการทำรายการซ้ำที่พบ', correlationResults.results.totalPatterns || 0],
          [''],
          ['รายละเอียดรูปแบบการทำรายการ'],
          ['ผู้รับ', 'จำนวนรายการ', 'ความถี่', 'จำนวนเงินเฉลี่ย'],
          ['']
        );
        
        if (correlationResults.results.patterns && correlationResults.results.patterns.length > 0) {
          correlationResults.results.patterns.forEach((pattern: any) => {
            correlationData.push([
              pattern.receiver || 'ไม่ระบุ',
              pattern.count,
              pattern.frequency || '-',
              `${pattern.averageAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`,
            ]);
            
            if (includeDetails && pattern.transactions && pattern.transactions.length > 0) {
              correlationData.push([''], ['รายการที่เกี่ยวข้อง'], ['วันที่', 'จำนวนเงิน', 'ธนาคาร'], ['']);
              
              pattern.transactions.forEach((tx: any) => {
                correlationData.push([
                  tx.date ? format(new Date(tx.date), 'PPP', { locale: th }) : '-',
                  `${tx.amount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`,
                  tx.bank || '-',
                ]);
              });
              
              correlationData.push(['']);
            }
          });
        }
      } else if (correlationType === 'anomaly_detection') {
        correlationData.push(
          ['จำนวนรายการผิดปกติที่พบ', correlationResults.results.totalAnomalies || 0],
          ['จำนวนเงินเฉลี่ย', `${correlationResults.results.amountStatistics?.average?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`],
          ['ช่วงปกติ', `${correlationResults.results.amountStatistics?.normalRange.min?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} - ${correlationResults.results.amountStatistics?.normalRange.max?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`],
          [''],
        );
        
        if (correlationResults.results.amountAnomalies && correlationResults.results.amountAnomalies.length > 0) {
          correlationData.push(['รายการที่มีจำนวนเงินผิดปกติ'], ['วันที่', 'จำนวนเงิน', 'ผู้รับ', 'ธนาคาร'], ['']);
          
          correlationResults.results.amountAnomalies.forEach((anomaly: any) => {
            correlationData.push([
              anomaly.date ? format(new Date(anomaly.date), 'PPP', { locale: th }) : '-',
              `${anomaly.amount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`,
              anomaly.receiver || '-',
              anomaly.bank || '-',
            ]);
          });
          
          correlationData.push(['']);
        }
        
        if (correlationResults.results.timeAnomalies && correlationResults.results.timeAnomalies.length > 0) {
          correlationData.push(['รายการที่เกิดขึ้นในเวลาผิดปกติ'], ['วันที่', 'เวลา', 'จำนวนเงิน', 'ผู้รับ'], ['']);
          
          correlationResults.results.timeAnomalies.forEach((anomaly: any) => {
            correlationData.push([
              anomaly.date ? format(new Date(anomaly.date), 'P', { locale: th }) : '-',
              anomaly.date ? format(new Date(anomaly.date), 'HH:mm', { locale: th }) + ' น.' : '-',
              `${anomaly.amount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`,
              anomaly.receiver || '-',
            ]);
          });
        }
      }
      
      return correlationData;
    };
    
    const addPredictionData = () => {
      if (!predictionResults) return [];
      
      let predictionData = [
        [''],
        ['ส่วนที่ 3: การคาดการณ์ข้อมูลในอนาคต'],
        ['ประเภท:', predictionType === 'usage_forecast' ? 'คาดการณ์การใช้งาน' : 
                  predictionType === 'amount_prediction' ? 'คาดการณ์จำนวนเงิน' : 'คาดการณ์รายการประจำ'],
        ['ข้อมูลที่ใช้วิเคราะห์:', predictionTimeframe === '30days' ? '30 วันล่าสุด' : 
                            predictionTimeframe === '90days' ? '90 วันล่าสุด' : 
                            predictionTimeframe === '180days' ? '180 วันล่าสุด' : '1 ปีล่าสุด'],
        [''],
        ['ผลการคาดการณ์'],
      ];
      
      if (predictionType === 'usage_forecast') {
        predictionData.push(
          ['คาดการณ์ 7 วันข้างหน้า', `${predictionResults.predictions.forecasts?.next7Days || 0} รายการ`],
          ['คาดการณ์ 30 วันข้างหน้า', `${predictionResults.predictions.forecasts?.next30Days || 0} รายการ`],
          ['คาดการณ์ 90 วันข้างหน้า', `${predictionResults.predictions.forecasts?.next90Days || 0} รายการ`],
          ['การใช้งานเฉลี่ย', `${predictionResults.predictions.historicalData?.averageDailyUsage?.toFixed(2) || 0} รายการ/วัน`],
          [''],
        );
        
        if (predictionResults.predictions.monthlyForecasts && predictionResults.predictions.monthlyForecasts.length > 0) {
          predictionData.push(['คาดการณ์รายเดือน'], ['เดือน', 'จำนวนคาดการณ์', 'การเปลี่ยนแปลง (%)'], ['']);
          
          predictionResults.predictions.monthlyForecasts.forEach((month: any) => {
            predictionData.push([
              month.month || '-',
              month.forecast,
              `${month.changePercentage > 0 ? '+' : ''}${month.changePercentage.toFixed(2)}%`,
            ]);
          });
          
          predictionData.push(['']);
        }
        
        if (includeDetails && predictionResults.predictions.historicalData && predictionResults.predictions.historicalData.weeklyUsage) {
          predictionData.push(['ข้อมูลการใช้งานรายสัปดาห์'], ['สัปดาห์', 'จำนวนรายการ'], ['']);
          
          Object.entries(predictionResults.predictions.historicalData.weeklyUsage).forEach(([week, count]: [string, any]) => {
            predictionData.push([`สัปดาห์ที่ ${week}`, count]);
          });
        }
      } else if (predictionType === 'amount_prediction') {
        predictionData.push(
          ['คาดการณ์จำนวนเงินเฉลี่ย 30 วันข้างหน้า', `${predictionResults.predictions.amountForecasts?.next30DaysAvg?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`],
          ['คาดการณ์จำนวนเงินรวม 30 วันข้างหน้า', `${predictionResults.predictions.amountForecasts?.next30DaysTotal?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`],
          ['แนวโน้มเทียบกับปัจจุบัน', `${predictionResults.predictions.amountForecasts?.trend > 0 ? '+' : ''}${predictionResults.predictions.amountForecasts?.trend?.toFixed(2) || 0}%`],
          [''],
        );
        
        if (predictionResults.predictions.monthlyAmountForecasts && predictionResults.predictions.monthlyAmountForecasts.length > 0) {
          predictionData.push(['คาดการณ์จำนวนเงินรายเดือน'], ['เดือน', 'จำนวนเงินเฉลี่ย', 'จำนวนเงินรวม', 'การเปลี่ยนแปลง (%)'], ['']);
          
          predictionResults.predictions.monthlyAmountForecasts.forEach((month: any) => {
            predictionData.push([
              month.month || '-',
              `${month.averageAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`,
              `${month.totalAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`,
              `${month.changePercentage > 0 ? '+' : ''}${month.changePercentage.toFixed(2)}%`,
            ]);
          });
        }
      } else if (predictionType === 'recurring_transactions') {
        predictionData.push(
          ['รายการที่คาดว่าจะเกิดซ้ำที่พบ', predictionResults.predictions.recurringTransactions?.count || 0],
          [''],
          ['รายละเอียดรายการที่คาดว่าจะเกิดซ้ำ'],
          ['ผู้รับ', 'จำนวนครั้งที่พบ', 'จำนวนเงินเฉลี่ย', 'ความถี่', 'วันที่คาดว่าจะเกิดครั้งถัดไป'],
          [''],
        );
        
        if (predictionResults.predictions.recurringTransactions && predictionResults.predictions.recurringTransactions.items) {
          predictionResults.predictions.recurringTransactions.items.forEach((item: any) => {
            predictionData.push([
              item.recipient || 'ไม่ระบุ',
              item.occurrences,
              `${item.averageAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} บาท`,
              item.frequency || '-',
              item.nextExpectedDate ? format(new Date(item.nextExpectedDate), 'PPP', { locale: th }) : '-'
            ]);
          });
        }
      }
      
      return predictionData;
    };
    
    // รวมข้อมูลจากทุกแท็บเข้าด้วยกัน ไม่ว่าจะอยู่ที่แท็บไหน
    const analysisData = analysisResults ? addAnalysisData() : [];
    const correlationData = correlationResults ? addCorrelationData() : [];
    const predictionData = predictionResults ? addPredictionData() : [];
    
    // รวมข้อมูลทั้งหมด
    csvContent = [
      ...csvContent,
      ...analysisData,
      ...correlationData,
      ...predictionData,
      [''],
      ['รายงานนี้ถูกสร้างโดยระบบ SLIPKUY PRM Analytics'],
      ['ข้อมูลในรายงานนี้เป็นกรรมสิทธิ์ของ บริษัท SLIPKUY จำกัด'],
      ['สงวนลิขสิทธิ์ © 2025 SLIPKUY']
    ];
    
    // ถ้าไม่มีข้อมูลในทุกแท็บให้แจ้งเตือน
    if (analysisData.length === 0 && correlationData.length === 0 && predictionData.length === 0) {
      toast({
        title: "ไม่พบข้อมูลสำหรับส่งออก",
        description: "กรุณาทำการวิเคราะห์ข้อมูลก่อนส่งออกไฟล์ CSV",
        variant: "destructive",
      });
      return;
    }
    
    const formattedCSV = Papa.unparse(csvContent, { delimiter: ',', header: false });
    const blob = new Blob(["\ufeff" + formattedCSV], {type: "text/csv;charset=utf-8"});
    saveAs(blob, filename);
  };
  
  // Analysis Functions
  const handleAnalyze = async () => {
    if (isLoadingAnalysis) return;
    
    setIsLoadingAnalysis(true);
    
    const params = new URLSearchParams();
    params.append('type', analysisType);
    
    if (fromDate && toDate) {
      params.append('from', fromDate.toISOString());
      params.append('to', toDate.toISOString());
    } else {
      params.append('timeframe', timeframe);
    }
    
    params.append('include_details', includeDetails ? 'true' : 'false');
    
    try {
      const response = await fetch(`/api/advanced-search/analyze?${params.toString()}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'การวิเคราะห์ข้อมูลล้มเหลว');
      }
      
      const data = await response.json();
      setAnalysisResults(data);
      
      toast({
        title: 'วิเคราะห์ข้อมูลสำเร็จ',
        description: 'โปรดตรวจสอบผลการวิเคราะห์ด้านล่าง',
      });
    } catch (error) {
      console.error('Error analyzing data:', error);
      toast({
        title: 'เกิดข้อผิดพลาดในการวิเคราะห์ข้อมูล',
        description: error instanceof Error ? error.message : 'กรุณาลองอีกครั้งในภายหลัง',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingAnalysis(false);
    }
  };
  
  // Correlation Analysis Functions
  const handleCorrelationAnalyze = async () => {
    if (isLoadingCorrelation) return;
    
    setIsLoadingCorrelation(true);
    
    const params = new URLSearchParams();
    params.append('type', correlationType);
    params.append('timeframe', correlationTimeframe);
    params.append('include_details', includeDetails ? 'true' : 'false');
    
    try {
      const response = await fetch(`/api/advanced-search/correlations?${params.toString()}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'การวิเคราะห์ความสัมพันธ์ล้มเหลว');
      }
      
      const data = await response.json();
      setCorrelationResults(data);
      
      toast({
        title: 'วิเคราะห์ความสัมพันธ์สำเร็จ',
        description: 'โปรดตรวจสอบผลการวิเคราะห์ด้านล่าง',
      });
    } catch (error) {
      console.error('Error analyzing correlations:', error);
      toast({
        title: 'เกิดข้อผิดพลาดในการวิเคราะห์ความสัมพันธ์',
        description: error instanceof Error ? error.message : 'กรุณาลองอีกครั้งในภายหลัง',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingCorrelation(false);
    }
  };
  
  // Prediction Functions
  const handlePredictionAnalyze = async () => {
    if (isLoadingPrediction) return;
    
    setIsLoadingPrediction(true);
    
    const params = new URLSearchParams();
    params.append('type', predictionType);
    params.append('timeframe', predictionTimeframe);
    params.append('include_details', includeDetails ? 'true' : 'false');
    
    try {
      const response = await fetch(`/api/advanced-search/predictions?${params.toString()}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'การคาดการณ์ข้อมูลล้มเหลว');
      }
      
      const data = await response.json();
      setPredictionResults(data);
      
      toast({
        title: 'คาดการณ์ข้อมูลสำเร็จ',
        description: 'โปรดตรวจสอบผลการคาดการณ์ด้านล่าง',
      });
    } catch (error) {
      console.error('Error predicting data:', error);
      toast({
        title: 'เกิดข้อผิดพลาดในการคาดการณ์ข้อมูล',
        description: error instanceof Error ? error.message : 'กรุณาลองอีกครั้งในภายหลัง',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingPrediction(false);
    }
  };
  
  // Reset when timeframe changes
  useEffect(() => {
    setFromDate(undefined);
    setToDate(undefined);
  }, [timeframe]);
  
  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center">
          <h1 className="mr-auto text-2xl font-semibold tracking-tight">การค้นหาขั้นสูง</h1>
        </div>
        
        <Tabs defaultValue="analyze" className="space-y-4" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="analyze" className="flex items-center gap-1.5">
              <Search className="h-4 w-4" />
              วิเคราะห์ข้อมูล
            </TabsTrigger>
            <TabsTrigger value="correlation" className="flex items-center gap-1.5">
              <BarChart3 className="h-4 w-4" />
              วิเคราะห์ความสัมพันธ์
            </TabsTrigger>
            <TabsTrigger value="prediction" className="flex items-center gap-1.5">
              <TrendingUp className="h-4 w-4" />
              คาดการณ์
            </TabsTrigger>
          </TabsList>
          
          {/* Analyze Tab */}
          <TabsContent value="analyze" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>ค้นหาและวิเคราะห์ข้อมูล</CardTitle>
                <CardDescription>
                  ระบุเงื่อนไขเพื่อวิเคราะห์ข้อมูลการใช้งานและรูปแบบการทำธุรกรรม
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="analysis-type">ประเภทการวิเคราะห์</Label>
                    <Select value={analysisType} onValueChange={setAnalysisType}>
                      <SelectTrigger id="analysis-type">
                        <SelectValue placeholder="เลือกประเภทการวิเคราะห์" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="usage_summary">สรุปการใช้งาน</SelectItem>
                        <SelectItem value="bank_analysis">วิเคราะห์ตามธนาคาร</SelectItem>
                        <SelectItem value="time_analysis">วิเคราะห์ตามเวลา</SelectItem>
                        <SelectItem value="transaction_patterns">รูปแบบธุรกรรม</SelectItem>
                        <SelectItem value="api_usage">การใช้งาน API</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="timeframe">ช่วงเวลา</Label>
                    <Select value={timeframe} onValueChange={setTimeframe}>
                      <SelectTrigger id="timeframe">
                        <SelectValue placeholder="เลือกช่วงเวลา" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="7days">7 วันล่าสุด</SelectItem>
                        <SelectItem value="30days">30 วันล่าสุด</SelectItem>
                        <SelectItem value="90days">90 วันล่าสุด</SelectItem>
                        <SelectItem value="1year">1 ปีล่าสุด</SelectItem>
                        <SelectItem value="custom">กำหนดเอง</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2 flex items-end">
                    <div className="flex items-center space-x-2 h-10">
                      <input 
                        type="checkbox" 
                        id="include-details" 
                        className="mr-1"
                        checked={includeDetails}
                        onChange={(e) => setIncludeDetails(e.target.checked)}
                      />
                      <Label htmlFor="include-details">รวมรายการทั้งหมด</Label>
                    </div>
                  </div>
                </div>
                
                {timeframe === 'custom' && (
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>วันที่เริ่มต้น</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-full justify-start text-left font-normal"
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {fromDate ? format(fromDate, 'PPP', { locale: th }) : <span>เลือกวันที่เริ่มต้น</span>}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={fromDate}
                            onSelect={setFromDate}
                            initialFocus
                            locale={th}
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                    
                    <div className="space-y-2">
                      <Label>วันที่สิ้นสุด</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-full justify-start text-left font-normal"
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {toDate ? format(toDate, 'PPP', { locale: th }) : <span>เลือกวันที่สิ้นสุด</span>}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={toDate}
                            onSelect={setToDate}
                            initialFocus
                            locale={th}
                            disabled={(date) => date < (fromDate || new Date(0))}
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>
                )}
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" className="w-1/3" onClick={exportToCSV}>
                  <Download className="h-4 w-4 mr-1" /> ดาวน์โหลด CSV รวม
                </Button>
                
                <Button onClick={handleAnalyze} disabled={isLoadingAnalysis}>
                  {isLoadingAnalysis ? 'กำลังวิเคราะห์...' : 'วิเคราะห์ข้อมูล'}
                </Button>
              </CardFooter>
            </Card>
            
            {analysisResults && (
              <Card className="bg-muted/20">
                <CardHeader>
                  <CardTitle>ผลการวิเคราะห์ข้อมูล</CardTitle>
                  <CardDescription>
                    {fromDate && toDate 
                      ? `ข้อมูลระหว่าง ${format(fromDate, 'PPP', { locale: th })} ถึง ${format(toDate, 'PPP', { locale: th })}`
                      : timeframe === '7days' ? 'ข้อมูล 7 วันล่าสุด'
                      : timeframe === '30days' ? 'ข้อมูล 30 วันล่าสุด'
                      : timeframe === '90days' ? 'ข้อมูล 90 วันล่าสุด'
                      : timeframe === '1year' ? 'ข้อมูล 1 ปีล่าสุด'
                      : 'ข้อมูลทั้งหมด'
                    }
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card className="bg-gradient-to-br from-blue-50 to-emerald-50 dark:from-blue-950/50 dark:to-emerald-950/50 border-none">
                      <CardContent className="pt-6">
                        <div className="text-xl font-bold mb-2">จำนวนรายการทั้งหมด</div>
                        <div className="text-4xl font-bold text-gradient-blue-to-emerald">
                          {analysisResults.results.totalVerifications?.toLocaleString() || '0'}
                        </div>
                      </CardContent>
                    </Card>
                    
                    <Card className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950/50 dark:to-pink-950/50 border-none">
                      <CardContent className="pt-6">
                        <div className="text-xl font-bold mb-2">จำนวนเงินรวม</div>
                        <div className="text-4xl font-bold text-gradient-purple-to-pink">
                          {analysisResults.results.totalAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} ฿
                        </div>
                      </CardContent>
                    </Card>
                    
                    <Card className="bg-gradient-to-br from-amber-50 to-orange-50 dark:from-amber-950/50 dark:to-orange-950/50 border-none">
                      <CardContent className="pt-6">
                        <div className="text-xl font-bold mb-2">จำนวนเงินเฉลี่ยต่อรายการ</div>
                        <div className="text-4xl font-bold text-gradient-amber-to-orange">
                          {analysisResults.results.averageAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} ฿
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                  
                  {/* Bank Analysis Results */}
                  {analysisType === 'bank_analysis' && analysisResults.results.bankDistribution && (
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">การกระจายตามธนาคาร</h3>
                      <div className="grid md:grid-cols-2 gap-6">
                        <div className="h-[300px]">
                          <ResponsiveContainer width="100%" height="100%">
                            <BarChart 
                              data={analysisResults.results.bankDistribution}
                              margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
                            >
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis 
                                dataKey="name" 
                                angle={-45} 
                                textAnchor="end" 
                                height={80}
                                tick={{ fill: 'var(--color-foreground-muted)' }}
                              />
                              <YAxis 
                                tick={{ fill: 'var(--color-foreground-muted)' }}
                              />
                              <RechartsTooltip 
                                formatter={(value: any, name: string) => {
                                  return name === 'count' 
                                    ? [`${value} รายการ`, 'จำนวนรายการ'] 
                                    : [`${value.toFixed(2)}%`, 'เปอร์เซ็นต์'];
                                }}
                              />
                              <Legend />
                              <Bar dataKey="count" name="จำนวนรายการ" fill="#0391ff" />
                              <Bar dataKey="percentage" name="เปอร์เซ็นต์" fill="#6466f1" />
                            </BarChart>
                          </ResponsiveContainer>
                        </div>
                        
                        <div className="h-[300px]">
                          <ResponsiveContainer width="100%" height="100%">
                            <PieChart width={400} height={300}>
                              {Array.isArray(analysisResults.results.bankDistribution) && (
                                <Pie
                                  data={analysisResults.results.bankDistribution}
                                  nameKey="name"
                                  dataKey="totalAmount"
                                  cx="50%"
                                  cy="50%"
                                  outerRadius={100}
                                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                                >
                                  {analysisResults.results.bankDistribution.map((entry: any, index: number) => (
                                    <Cell key={`cell-${index}`} fill={`hsl(${index * 40}, 70%, 60%)`} />
                                  ))}
                                </Pie>
                              )}
                              <RechartsTooltip
                                formatter={(value: any) => [`${value.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })} บาท`, 'จำนวนเงิน']}
                              />
                              <Legend />
                            </PieChart>
                          </ResponsiveContainer>
                        </div>
                      </div>
                      
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>ธนาคาร</TableHead>
                            <TableHead>จำนวนรายการ</TableHead>
                            <TableHead>เปอร์เซ็นต์</TableHead>
                            <TableHead className="text-right">จำนวนเงินรวม</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {Array.isArray(analysisResults.results.bankDistribution) ? (
                            analysisResults.results.bankDistribution.map((bank: any, index: number) => (
                              <TableRow key={index}>
                                <TableCell className="font-medium">{bank.name || 'ไม่ระบุ'}</TableCell>
                                <TableCell>{bank.count}</TableCell>
                                <TableCell>{bank.percentage?.toFixed(2) || '0'}%</TableCell>
                                <TableCell className="text-right">{bank.totalAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} ฿</TableCell>
                              </TableRow>
                            ))
                          ) : (
                            <TableRow>
                              <TableCell colSpan={4} className="text-center">ไม่มีข้อมูล</TableCell>
                            </TableRow>
                          )}
                        </TableBody>
                      </Table>
                    </div>
                  )}
                  
                  {/* Time Analysis Results */}
                  {analysisType === 'time_analysis' && analysisResults.results.timeDistribution && (
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">การกระจายตามช่วงเวลา</h3>
                      <div className="grid md:grid-cols-2 gap-6">
                        <div className="h-[300px]">
                          <ResponsiveContainer width="100%" height="100%">
                            <LineChart
                              data={analysisResults.results.timeDistribution}
                              margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
                            >
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis 
                                dataKey="time" 
                                angle={-45} 
                                textAnchor="end" 
                                height={80}
                                tick={{ fill: 'var(--color-foreground-muted)' }}
                              />
                              <YAxis 
                                yAxisId="left"
                                tick={{ fill: 'var(--color-foreground-muted)' }}
                              />
                              <YAxis 
                                yAxisId="right" 
                                orientation="right"
                                tick={{ fill: 'var(--color-foreground-muted)' }}
                              />
                              <RechartsTooltip />
                              <Legend />
                              <Line 
                                yAxisId="left"
                                type="monotone" 
                                dataKey="count" 
                                name="จำนวนรายการ" 
                                stroke="#4682b4"
                                strokeWidth={2}
                                dot={{ r: 5 }}
                                activeDot={{ r: 8 }}
                              />
                              <Line 
                                yAxisId="right"
                                type="monotone" 
                                dataKey="totalAmount" 
                                name="จำนวนเงินรวม" 
                                stroke="#9370db" 
                                strokeWidth={2}
                                dot={{ r: 5 }}
                                activeDot={{ r: 8 }}
                              />
                            </LineChart>
                          </ResponsiveContainer>
                        </div>
                        
                        <div className="h-[300px]">
                          <ResponsiveContainer width="100%" height="100%">
                            <BarChart
                              data={analysisResults.results.timeDistribution}
                              margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
                            >
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis 
                                dataKey="time" 
                                angle={-45} 
                                textAnchor="end" 
                                height={80}
                                tick={{ fill: 'var(--color-foreground-muted)' }}
                              />
                              <YAxis 
                                tick={{ fill: 'var(--color-foreground-muted)' }}
                              />
                              <RechartsTooltip />
                              <Legend />
                              <Bar dataKey="percentage" name="เปอร์เซ็นต์" fill="#20b2aa" />
                            </BarChart>
                          </ResponsiveContainer>
                        </div>
                      </div>
                      
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>ช่วงเวลา</TableHead>
                            <TableHead>จำนวนรายการ</TableHead>
                            <TableHead>เปอร์เซ็นต์</TableHead>
                            <TableHead className="text-right">จำนวนเงินรวม</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {analysisResults.results.timeDistribution.map((time: any, index: number) => (
                            <TableRow key={index}>
                              <TableCell className="font-medium">{time.time || 'ไม่ระบุ'}</TableCell>
                              <TableCell>{time.count}</TableCell>
                              <TableCell>{time.percentage?.toFixed(2) || '0'}%</TableCell>
                              <TableCell className="text-right">{time.totalAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} ฿</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  )}
                  
                  {/* API Usage Analysis */}
                  {analysisType === 'api_usage' && analysisResults.results.apiKeyUsage && (
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">การใช้งาน API Keys</h3>
                      <div className="h-[300px]">
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart 
                            data={Object.entries(analysisResults.results.apiKeyUsage).map(([key, data]: [string, any]) => ({
                              name: key,
                              count: data.count,
                              successRate: data.successRate,
                              avgResponseTime: data.avgResponseTime
                            }))}
                            margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis 
                              dataKey="name" 
                              angle={-45} 
                              textAnchor="end" 
                              height={80}
                              tick={{ fill: 'var(--color-foreground-muted)' }}
                            />
                            <YAxis 
                              yAxisId="left"
                              tick={{ fill: 'var(--color-foreground-muted)' }}
                            />
                            <YAxis 
                              yAxisId="right" 
                              orientation="right"
                              tick={{ fill: 'var(--color-foreground-muted)' }}
                            />
                            <RechartsTooltip />
                            <Legend />
                            <Bar yAxisId="left" dataKey="count" name="จำนวนการใช้งาน" fill="#2196f3" />
                            <Bar yAxisId="right" dataKey="successRate" name="อัตราความสำเร็จ (%)" fill="#4caf50" />
                          </BarChart>
                        </ResponsiveContainer>
                      </div>
                      
                      <div>
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>API Key</TableHead>
                              <TableHead>จำนวนการใช้งาน</TableHead>
                              <TableHead>อัตราความสำเร็จ</TableHead>
                              <TableHead>เวลาตอบสนองเฉลี่ย</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {analysisResults.results.apiKeyUsage && Object.entries(analysisResults.results.apiKeyUsage).map(([keyName, data]: [string, any]) => (
                              <TableRow key={keyName}>
                                <TableCell className="font-medium">{keyName}</TableCell>
                                <TableCell>{data.count}</TableCell>
                                <TableCell>
                                  <div className="flex items-center gap-2">
                                    <Progress value={data.successRate} className="h-2 w-20" />
                                    <span>{data.successRate?.toFixed(1) || '0'}%</span>
                                  </div>
                                </TableCell>
                                <TableCell>{data.avgResponseTime?.toFixed(0) || '0'} ms</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                      
                      <h3 className="text-lg font-semibold">IP Address ที่ใช้งานมากที่สุด</h3>
                      <div className="grid gap-4 md:grid-cols-2">
                        {analysisResults.results.topIpAddresses && analysisResults.results.topIpAddresses.map((ipData: any, index: number) => (
                          <Card key={index} className="bg-muted/20">
                            <CardContent className="py-4">
                              <div className="flex justify-between items-center">
                                <span className="font-mono">{ipData.ip}</span>
                                <span className="font-bold">{ipData.count} ครั้ง</span>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {/* Transaction Patterns Analysis */}
                  {analysisType === 'transaction_patterns' && analysisResults.results.patterns && (
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">รูปแบบการทำธุรกรรม</h3>
                      <div className="grid md:grid-cols-2 gap-6">
                        <div className="h-[300px]">
                          <ResponsiveContainer width="100%" height="100%">
                            <BarChart 
                              data={analysisResults.results.patterns}
                              margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
                            >
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis 
                                dataKey="name" 
                                angle={-45} 
                                textAnchor="end" 
                                height={80}
                                tick={{ fill: 'var(--color-foreground-muted)' }}
                              />
                              <YAxis 
                                tick={{ fill: 'var(--color-foreground-muted)' }}
                              />
                              <RechartsTooltip />
                              <Legend />
                              <Bar dataKey="count" name="ความถี่" fill="#ff7043" />
                            </BarChart>
                          </ResponsiveContainer>
                        </div>
                        
                        <div className="h-[300px]">
                          <ResponsiveContainer width="100%" height="100%">
                            <PieChart width={400} height={300}>
                              <Pie
                                data={analysisResults.results.patterns}
                                nameKey="type"
                                dataKey="percentage"
                                cx="50%"
                                cy="50%"
                                outerRadius={100}
                                label={({ name, percent }) => `${name || 'ไม่ระบุ'}: ${(percent * 100).toFixed(1)}%`}
                              >
                                {analysisResults.results.patterns.map((entry: any, index: number) => (
                                  <Cell key={`cell-${index}`} fill={`hsl(${index * 40}, 70%, 60%)`} />
                                ))}
                              </Pie>
                              <RechartsTooltip />
                              <Legend />
                            </PieChart>
                          </ResponsiveContainer>
                        </div>
                      </div>
                      
                      <div>
                        <Accordion type="multiple" className="w-full">
                          {analysisResults.results.patterns.map((pattern: any, index: number) => (
                            <AccordionItem key={index} value={`pattern-${index}`}>
                              <AccordionTrigger className="hover:no-underline">
                                <div className="flex w-full items-center justify-between pr-4">
                                  <div>{pattern.name || 'รูปแบบที่ ' + (index + 1)}</div>
                                  <div className="text-muted-foreground">{pattern.count} ครั้ง ({pattern.percentage?.toFixed(1) || '0'}%)</div>
                                </div>
                              </AccordionTrigger>
                              <AccordionContent>
                                <div className="pl-4 space-y-2">
                                  <div><strong>ประเภท:</strong> {pattern.type || 'ไม่ระบุ'}</div>
                                  <div><strong>จำนวนครั้ง:</strong> {pattern.count}</div>
                                  <div><strong>ความถี่:</strong> {pattern.frequency || 'ไม่ระบุ'}</div>
                                  <div><strong>จำนวนเงินเฉลี่ย:</strong> {pattern.avgAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} ฿</div>
                                  
                                  {pattern.examples && pattern.examples.length > 0 && (
                                    <div className="pt-2">
                                      <div className="font-semibold mb-2">ตัวอย่างธุรกรรม</div>
                                      <Table>
                                        <TableHeader>
                                          <TableRow>
                                            <TableHead>วันที่</TableHead>
                                            <TableHead>จำนวนเงิน</TableHead>
                                            <TableHead>ผู้รับ</TableHead>
                                            <TableHead>ธนาคาร</TableHead>
                                          </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                          {pattern.examples.map((example: any, eidx: number) => (
                                            <TableRow key={eidx}>
                                              <TableCell>{example.date || 'ไม่ระบุ'}</TableCell>
                                              <TableCell>{example.amount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} ฿</TableCell>
                                              <TableCell>{example.receiver || 'ไม่ระบุ'}</TableCell>
                                              <TableCell>{example.bank || 'ไม่ระบุ'}</TableCell>
                                            </TableRow>
                                          ))}
                                        </TableBody>
                                      </Table>
                                    </div>
                                  )}
                                </div>
                              </AccordionContent>
                            </AccordionItem>
                          ))}
                        </Accordion>
                      </div>
                    </div>
                  )}
                  
                  {/* General Usage Summary */}
                  {analysisType === 'usage_summary' && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <Card className="bg-muted/20">
                          <CardContent className="p-4">
                            <div className="font-medium mb-1">จำนวนธุรกรรมเฉลี่ยต่อวัน</div>
                            <div className="text-2xl font-bold">{analysisResults.results.dailyAverage?.toFixed(1) || '0'}</div>
                          </CardContent>
                        </Card>
                        
                        <Card className="bg-muted/20">
                          <CardContent className="p-4">
                            <div className="font-medium mb-1">ธนาคารที่ใช้บ่อยที่สุด</div>
                            <div className="text-2xl font-bold">{analysisResults.results.topBank || 'ไม่มีข้อมูล'}</div>
                          </CardContent>
                        </Card>
                        
                        <Card className="bg-muted/20">
                          <CardContent className="p-4">
                            <div className="font-medium mb-1">จำนวนเงินสูงสุด</div>
                            <div className="text-2xl font-bold">{analysisResults.results.maxAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} ฿</div>
                          </CardContent>
                        </Card>
                        
                        <Card className="bg-muted/20">
                          <CardContent className="p-4">
                            <div className="font-medium mb-1">ช่วงเวลาที่ใช้บ่อยที่สุด</div>
                            <div className="text-2xl font-bold">{analysisResults.results.peakHour || 'ไม่มีข้อมูล'}</div>
                          </CardContent>
                        </Card>
                      </div>
                      
                      <div className="grid md:grid-cols-2 gap-6">
                        <div>
                          <h3 className="text-lg font-semibold mb-3">การกระจายตามธนาคาร</h3>
                          <div className="h-[300px]">
                            <ResponsiveContainer width="100%" height="100%">
                              <PieChart width={400} height={300}>
                                {analysisResults.results.bankBreakdown && (
                                  <Pie
                                    data={analysisResults.results.bankBreakdown}
                                    nameKey="name"
                                    dataKey="count"
                                    cx="50%"
                                    cy="50%"
                                    outerRadius={100}
                                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                                  >
                                    {analysisResults.results.bankBreakdown.map((entry: any, index: number) => (
                                      <Cell key={`cell-${index}`} fill={`hsl(${index * 40}, 70%, 60%)`} />
                                    ))}
                                  </Pie>
                                )}
                                <RechartsTooltip />
                                <Legend />
                              </PieChart>
                            </ResponsiveContainer>
                          </div>
                        </div>
                        
                        <div>
                          <h3 className="text-lg font-semibold mb-3">แนวโน้มตามเวลา</h3>
                          <div className="h-[300px]">
                            <ResponsiveContainer width="100%" height="100%">
                              <LineChart
                                data={analysisResults.results.timeline || []}
                                margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
                              >
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis 
                                  dataKey="date" 
                                  angle={-45} 
                                  textAnchor="end" 
                                  height={80}
                                  tick={{ fill: 'var(--color-foreground-muted)' }}
                                />
                                <YAxis 
                                  yAxisId="left"
                                  tick={{ fill: 'var(--color-foreground-muted)' }}
                                />
                                <YAxis 
                                  yAxisId="right" 
                                  orientation="right"
                                  tick={{ fill: 'var(--color-foreground-muted)' }}
                                />
                                <RechartsTooltip />
                                <Legend />
                                {analysisResults.results.timeline && (
                                  <>
                                    <Line 
                                      yAxisId="left"
                                      type="monotone" 
                                      dataKey="count" 
                                      name="จำนวนรายการ" 
                                      stroke="#1e88e5"
                                      strokeWidth={2}
                                      dot={{ r: 5 }}
                                      activeDot={{ r: 8 }}
                                    />
                                    <Line 
                                      yAxisId="right"
                                      type="monotone" 
                                      dataKey="amount" 
                                      name="จำนวนเงินรวม" 
                                      stroke="#43a047" 
                                      strokeWidth={2}
                                      dot={{ r: 5 }}
                                      activeDot={{ r: 8 }}
                                    />
                                  </>
                                )}
                              </LineChart>
                            </ResponsiveContainer>
                          </div>
                        </div>
                      </div>
                      
                      <div className="space-y-3">
                        <h3 className="text-lg font-semibold">ผู้รับโอนบ่อยที่สุด</h3>
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>ผู้รับ</TableHead>
                              <TableHead>จำนวนครั้ง</TableHead>
                              <TableHead>เปอร์เซ็นต์</TableHead>
                              <TableHead className="text-right">จำนวนเงินรวม</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {analysisResults.results.topReceivers && analysisResults.results.topReceivers.map((receiver: any, index: number) => (
                              <TableRow key={index}>
                                <TableCell className="font-medium">{receiver.name || 'ไม่ระบุ'}</TableCell>
                                <TableCell>{receiver.count}</TableCell>
                                <TableCell>{receiver.percentage?.toFixed(2) || '0'}%</TableCell>
                                <TableCell className="text-right">{receiver.totalAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} ฿</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    </div>
                  )}
                  
                  {/* List of All Transactions (if detailed view is enabled) */}
                  {includeDetails && analysisResults.transactions && analysisResults.transactions.length > 0 && (
                    <div className="space-y-3 mt-4 pt-4 border-t">
                      <h3 className="text-lg font-semibold">รายการทั้งหมด</h3>
                      <div className="overflow-x-auto">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>วันที่</TableHead>
                              <TableHead>เวลา</TableHead>
                              <TableHead>จำนวนเงิน</TableHead>
                              <TableHead>ธนาคารผู้รับ</TableHead>
                              <TableHead>ผู้รับ</TableHead>
                              <TableHead>สถานะ</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {analysisResults.transactions.map((tx: any, index: number) => {
                              const date = tx.createdAt ? new Date(tx.createdAt) : null;
                              return (
                                <TableRow key={index}>
                                  <TableCell>{date ? format(date, 'PPP', { locale: th }) : '-'}</TableCell>
                                  <TableCell>{date ? format(date, 'HH:mm', { locale: th }) : '-'}</TableCell>
                                  <TableCell>{tx.amount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} ฿</TableCell>
                                  <TableCell>{tx.bankName || '-'}</TableCell>
                                  <TableCell>{tx.receiver || '-'}</TableCell>
                                  <TableCell>{tx.status || '-'}</TableCell>
                                </TableRow>
                              );
                            })}
                          </TableBody>
                        </Table>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </TabsContent>
          
          {/* Correlation Tab */}
          <TabsContent value="correlation" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>วิเคราะห์ความสัมพันธ์ของข้อมูล</CardTitle>
                <CardDescription>
                  วิเคราะห์รูปแบบและความสัมพันธ์ของการใช้งานหรือธุรกรรม
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="correlation-type">ประเภทการวิเคราะห์</Label>
                    <Select value={correlationType} onValueChange={setCorrelationType}>
                      <SelectTrigger id="correlation-type">
                        <SelectValue placeholder="เลือกประเภทการวิเคราะห์" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="transaction_patterns">รูปแบบการทำรายการ</SelectItem>
                        <SelectItem value="anomaly_detection">ตรวจจับความผิดปกติ</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="correlation-timeframe">ช่วงเวลา</Label>
                    <Select value={correlationTimeframe} onValueChange={setCorrelationTimeframe}>
                      <SelectTrigger id="correlation-timeframe">
                        <SelectValue placeholder="เลือกช่วงเวลา" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="30days">30 วันล่าสุด</SelectItem>
                        <SelectItem value="90days">90 วันล่าสุด</SelectItem>
                        <SelectItem value="180days">180 วันล่าสุด</SelectItem>
                        <SelectItem value="1year">1 ปีล่าสุด</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="space-y-2 flex items-end">
                  <div className="flex items-center space-x-2 h-10">
                    <input 
                      type="checkbox" 
                      id="include-details-correlation" 
                      className="mr-1"
                      checked={includeDetails}
                      onChange={(e) => setIncludeDetails(e.target.checked)}
                    />
                    <Label htmlFor="include-details-correlation">รวมรายการทั้งหมด</Label>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" className="w-1/3" onClick={exportToCSV}>
                  <Download className="h-4 w-4 mr-1" /> ดาวน์โหลด CSV รวม
                </Button>
                
                <Button onClick={handleCorrelationAnalyze} disabled={isLoadingCorrelation}>
                  {isLoadingCorrelation ? 'กำลังวิเคราะห์...' : 'วิเคราะห์ความสัมพันธ์'}
                </Button>
              </CardFooter>
            </Card>
            
            {correlationResults && (
              <Card className="bg-muted/20">
                <CardHeader>
                  <CardTitle>ผลการวิเคราะห์ความสัมพันธ์</CardTitle>
                  <CardDescription>
                    ข้อมูล {correlationTimeframe === '30days' ? '30 วันล่าสุด' : 
                          correlationTimeframe === '90days' ? '90 วันล่าสุด' : 
                          correlationTimeframe === '180days' ? '180 วันล่าสุด' : '1 ปีล่าสุด'}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Transaction Patterns Results */}
                  {correlationType === 'transaction_patterns' && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Card className="bg-gradient-to-br from-green-50 to-teal-50 dark:from-green-950/50 dark:to-teal-950/50 border-none">
                          <CardContent className="pt-6">
                            <div className="text-xl font-bold mb-2">รูปแบบการทำรายการซ้ำที่พบ</div>
                            <div className="text-4xl font-bold text-gradient-green-to-teal">
                              {correlationResults.results.totalPatterns || 0}
                            </div>
                          </CardContent>
                        </Card>
                        
                        <Card className="bg-gradient-to-br from-indigo-50 to-blue-50 dark:from-indigo-950/50 dark:to-blue-950/50 border-none">
                          <CardContent className="pt-6">
                            <div className="text-xl font-bold mb-2">ผู้รับที่ซ้ำกันบ่อยที่สุด</div>
                            <div className="text-4xl font-bold text-gradient-indigo-to-blue">
                              {correlationResults.results.mostFrequentReceiver?.name || 'ไม่มีข้อมูล'}
                            </div>
                          </CardContent>
                        </Card>
                        
                        <Card className="bg-gradient-to-br from-rose-50 to-pink-50 dark:from-rose-950/50 dark:to-pink-950/50 border-none">
                          <CardContent className="pt-6">
                            <div className="text-xl font-bold mb-2">มูลค่าของรูปแบบซ้ำ</div>
                            <div className="text-4xl font-bold text-gradient-rose-to-pink">
                              {correlationResults.results.patternValue?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} ฿
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                      
                      <div className="space-y-3">
                        <h3 className="text-lg font-semibold">รูปแบบการทำรายการที่พบ</h3>
                        
                        <div className="grid md:grid-cols-2 gap-6">
                          <div className="h-[350px]">
                            <ResponsiveContainer width="100%" height="100%">
                              <BarChart 
                                data={correlationResults.results.patterns || []}
                                margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
                              >
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis 
                                  dataKey="receiver" 
                                  angle={-45} 
                                  textAnchor="end" 
                                  height={80}
                                  tick={{ fill: 'var(--color-foreground-muted)' }}
                                />
                                <YAxis 
                                  tick={{ fill: 'var(--color-foreground-muted)' }}
                                />
                                <RechartsTooltip />
                                <Legend />
                                {correlationResults.results.patterns && (
                                  <>
                                    <Bar dataKey="count" name="จำนวนครั้ง" fill="#3f51b5" />
                                    <Bar dataKey="averageAmount" name="จำนวนเงินเฉลี่ย" fill="#e91e63" />
                                  </>
                                )}
                              </BarChart>
                            </ResponsiveContainer>
                          </div>
                          
                          <div className="h-[350px]">
                            <ResponsiveContainer width="100%" height="100%">
                              <PieChart width={400} height={350}>
                                {correlationResults.results.patterns && correlationResults.results.patterns.length > 0 && (
                                  <Pie
                                    data={correlationResults.results.patterns}
                                    nameKey="receiver"
                                    dataKey="count"
                                    cx="50%"
                                    cy="50%"
                                    outerRadius={130}
                                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                                  >
                                    {correlationResults.results.patterns.map((entry: any, index: number) => (
                                      <Cell key={`cell-${index}`} fill={`hsl(${index * 40}, 70%, 60%)`} />
                                    ))}
                                  </Pie>
                                )}
                                <RechartsTooltip />
                                <Legend />
                              </PieChart>
                            </ResponsiveContainer>
                          </div>
                        </div>
                        
                        <Accordion type="multiple" className="w-full">
                          {correlationResults.results.patterns.map((pattern: any, index: number) => (
                            <AccordionItem key={index} value={`pattern-${index}`}>
                              <AccordionTrigger className="hover:no-underline">
                                <div className="flex w-full items-center justify-between pr-4">
                                  <div>{pattern.receiver || 'ไม่ระบุ'}</div>
                                  <div className="text-muted-foreground">{pattern.count} ครั้ง</div>
                                </div>
                              </AccordionTrigger>
                              <AccordionContent>
                                <div className="pl-4 space-y-2">
                                  <div><strong>ความถี่:</strong> {pattern.frequency || 'ไม่ระบุ'}</div>
                                  <div><strong>จำนวนเงินเฉลี่ย:</strong> {pattern.averageAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} ฿</div>
                                  
                                  {includeDetails && pattern.transactions && pattern.transactions.length > 0 && (
                                    <div className="pt-2">
                                      <div className="font-semibold mb-2">รายการทั้งหมด</div>
                                      <Table>
                                        <TableHeader>
                                          <TableRow>
                                            <TableHead>วันที่</TableHead>
                                            <TableHead>จำนวนเงิน</TableHead>
                                            <TableHead>ธนาคาร</TableHead>
                                          </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                          {pattern.transactions.map((tx: any, tidx: number) => (
                                            <TableRow key={tidx}>
                                              <TableCell>{tx.date ? format(new Date(tx.date), 'PPP', { locale: th }) : '-'}</TableCell>
                                              <TableCell>{tx.amount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} ฿</TableCell>
                                              <TableCell>{tx.bank || 'ไม่ระบุ'}</TableCell>
                                            </TableRow>
                                          ))}
                                        </TableBody>
                                      </Table>
                                    </div>
                                  )}
                                </div>
                              </AccordionContent>
                            </AccordionItem>
                          ))}
                        </Accordion>
                      </div>
                    </div>
                  )}
                  
                  {/* Anomaly Detection Results */}
                  {correlationType === 'anomaly_detection' && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Card className="bg-gradient-to-br from-red-50 to-orange-50 dark:from-red-950/50 dark:to-orange-950/50 border-none">
                          <CardContent className="pt-6">
                            <div className="text-xl font-bold mb-2">รายการผิดปกติที่พบ</div>
                            <div className="text-4xl font-bold text-gradient-red-to-orange">
                              {correlationResults.results.totalAnomalies || 0}
                            </div>
                          </CardContent>
                        </Card>
                        
                        <Card className="bg-gradient-to-br from-cyan-50 to-blue-50 dark:from-cyan-950/50 dark:to-blue-950/50 border-none">
                          <CardContent className="pt-6">
                            <div className="text-xl font-bold mb-2">จำนวนเงินเฉลี่ย</div>
                            <div className="text-4xl font-bold text-gradient-cyan-to-blue">
                              {correlationResults.results.amountStatistics?.average?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} ฿
                            </div>
                          </CardContent>
                        </Card>
                        
                        <Card className="bg-gradient-to-br from-violet-50 to-purple-50 dark:from-violet-950/50 dark:to-purple-950/50 border-none">
                          <CardContent className="pt-6">
                            <div className="text-xl font-bold mb-2">ช่วงปกติ</div>
                            <div className="text-4xl font-bold text-gradient-violet-to-purple">
                              {correlationResults.results.amountStatistics?.normalRange.min?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} - {correlationResults.results.amountStatistics?.normalRange.max?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} ฿
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                      
                      <div className="space-y-3">
                        <h3 className="text-lg font-semibold">การแจกแจงจำนวนเงิน</h3>
                        <div className="h-[300px]">
                          <ResponsiveContainer width="100%" height="100%">
                            <LineChart
                              data={correlationResults.results.amountDistribution}
                              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                            >
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis 
                                dataKey="range" 
                                tick={{ fill: 'var(--color-foreground-muted)' }}
                              />
                              <YAxis 
                                tick={{ fill: 'var(--color-foreground-muted)' }}
                              />
                              <RechartsTooltip />
                              <Legend />
                              <Line 
                                type="monotone" 
                                dataKey="count" 
                                name="จำนวนรายการ" 
                                stroke="#8884d8"
                                strokeWidth={2}
                                dot={{ r: 5 }}
                                activeDot={{ r: 8 }}
                              />
                            </LineChart>
                          </ResponsiveContainer>
                        </div>
                      </div>
                      
                      {correlationResults.results.amountAnomalies && correlationResults.results.amountAnomalies.length > 0 && (
                        <div className="space-y-3">
                          <h3 className="text-lg font-semibold">รายการที่มีจำนวนเงินผิดปกติ</h3>
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>วันที่</TableHead>
                                <TableHead>จำนวนเงิน</TableHead>
                                <TableHead>ผู้รับ</TableHead>
                                <TableHead>ธนาคาร</TableHead>
                                <TableHead>Z-Score</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {correlationResults.results.amountAnomalies.map((anomaly: any, index: number) => (
                                <TableRow key={index}>
                                  <TableCell>{anomaly.date ? format(new Date(anomaly.date), 'PPP', { locale: th }) : '-'}</TableCell>
                                  <TableCell>{anomaly.amount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} ฿</TableCell>
                                  <TableCell>{anomaly.receiver || '-'}</TableCell>
                                  <TableCell>{anomaly.bank || '-'}</TableCell>
                                  <TableCell>{anomaly.zScore?.toFixed(2) || '-'}</TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </div>
                      )}
                      
                      {correlationResults.results.timeAnomalies && correlationResults.results.timeAnomalies.length > 0 && (
                        <div className="space-y-3">
                          <h3 className="text-lg font-semibold">รายการที่เกิดขึ้นในเวลาผิดปกติ</h3>
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>วันที่</TableHead>
                                <TableHead>เวลา</TableHead>
                                <TableHead>จำนวนเงิน</TableHead>
                                <TableHead>ผู้รับ</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {correlationResults.results.timeAnomalies.map((anomaly: any, index: number) => (
                                <TableRow key={index}>
                                  <TableCell>{anomaly.date ? format(new Date(anomaly.date), 'P', { locale: th }) : '-'}</TableCell>
                                  <TableCell>{anomaly.date ? format(new Date(anomaly.date), 'HH:mm', { locale: th }) + ' น.' : '-'}</TableCell>
                                  <TableCell>{anomaly.amount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} ฿</TableCell>
                                  <TableCell>{anomaly.receiver || '-'}</TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </TabsContent>
          
          {/* Prediction Tab */}
          <TabsContent value="prediction" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>คาดการณ์ข้อมูลในอนาคต</CardTitle>
                <CardDescription>
                  วิเคราะห์แนวโน้มและคาดการณ์ข้อมูลการใช้งานในอนาคต
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="prediction-type">ประเภทการคาดการณ์</Label>
                    <Select value={predictionType} onValueChange={setPredictionType}>
                      <SelectTrigger id="prediction-type">
                        <SelectValue placeholder="เลือกประเภทการคาดการณ์" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="usage_forecast">คาดการณ์การใช้งาน</SelectItem>
                        <SelectItem value="amount_prediction">คาดการณ์จำนวนเงิน</SelectItem>
                        <SelectItem value="recurring_transactions">คาดการณ์รายการประจำ</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="prediction-timeframe">ข้อมูลที่ใช้วิเคราะห์</Label>
                    <Select value={predictionTimeframe} onValueChange={setPredictionTimeframe}>
                      <SelectTrigger id="prediction-timeframe">
                        <SelectValue placeholder="เลือกช่วงเวลา" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="30days">30 วันล่าสุด</SelectItem>
                        <SelectItem value="90days">90 วันล่าสุด</SelectItem>
                        <SelectItem value="180days">180 วันล่าสุด</SelectItem>
                        <SelectItem value="1year">1 ปีล่าสุด</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="space-y-2 flex items-end">
                  <div className="flex items-center space-x-2 h-10">
                    <input 
                      type="checkbox" 
                      id="include-details-prediction" 
                      className="mr-1"
                      checked={includeDetails}
                      onChange={(e) => setIncludeDetails(e.target.checked)}
                    />
                    <Label htmlFor="include-details-prediction">รวมรายการทั้งหมด</Label>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" className="w-1/3" onClick={exportToCSV}>
                  <Download className="h-4 w-4 mr-1" /> ดาวน์โหลด CSV รวม
                </Button>
                
                <Button onClick={handlePredictionAnalyze} disabled={isLoadingPrediction}>
                  {isLoadingPrediction ? 'กำลังวิเคราะห์...' : 'คาดการณ์ข้อมูล'}
                </Button>
              </CardFooter>
            </Card>
            
            {predictionResults && (
              <Card className="bg-muted/20">
                <CardHeader>
                  <CardTitle>ผลการคาดการณ์ข้อมูล</CardTitle>
                  <CardDescription>
                    ใช้ข้อมูล {predictionTimeframe === '30days' ? '30 วันล่าสุด' : 
                           predictionTimeframe === '90days' ? '90 วันล่าสุด' : 
                           predictionTimeframe === '180days' ? '180 วันล่าสุด' : '1 ปีล่าสุด'} ในการคาดการณ์
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Usage Forecast Results */}
                  {predictionType === 'usage_forecast' && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Card className="bg-gradient-to-br from-blue-50 to-teal-50 dark:from-blue-950/50 dark:to-teal-950/50 border-none">
                          <CardContent className="pt-6">
                            <div className="text-xl font-bold mb-2">คาดการณ์ 7 วันข้างหน้า</div>
                            <div className="text-4xl font-bold text-gradient-blue-to-teal">
                              {predictionResults.predictions.forecasts?.next7Days || 0} รายการ
                            </div>
                          </CardContent>
                        </Card>
                        
                        <Card className="bg-gradient-to-br from-indigo-50 to-violet-50 dark:from-indigo-950/50 dark:to-violet-950/50 border-none">
                          <CardContent className="pt-6">
                            <div className="text-xl font-bold mb-2">คาดการณ์ 30 วันข้างหน้า</div>
                            <div className="text-4xl font-bold text-gradient-indigo-to-violet">
                              {predictionResults.predictions.forecasts?.next30Days || 0} รายการ
                            </div>
                          </CardContent>
                        </Card>
                        
                        <Card className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/50 dark:to-emerald-950/50 border-none">
                          <CardContent className="pt-6">
                            <div className="text-xl font-bold mb-2">การใช้งานเฉลี่ย</div>
                            <div className="text-4xl font-bold text-gradient-green-to-emerald">
                              {predictionResults.predictions.historicalData?.averageDailyUsage.toFixed(2) || 0} ต่อวัน
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                      
                      <div className="space-y-3">
                        <h3 className="text-lg font-semibold">แนวโน้มการใช้งาน</h3>
                        <div className="h-[300px]">
                          <ResponsiveContainer width="100%" height="100%">
                            <LineChart
                              data={[
                                ...predictionResults.predictions.historicalData?.dailyUsage || [],
                                ...predictionResults.predictions.forecasts?.dailyForecasts || []
                              ]}
                              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                            >
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis 
                                dataKey="date" 
                                tick={{ fill: 'var(--color-foreground-muted)' }}
                              />
                              <YAxis 
                                tick={{ fill: 'var(--color-foreground-muted)' }}
                              />
                              <RechartsTooltip />
                              <Legend />
                              <Line 
                                type="monotone" 
                                dataKey="actual" 
                                name="จำนวนจริง" 
                                stroke="#4caf50"
                                strokeWidth={2}
                                dot={{ r: 5 }}
                                activeDot={{ r: 8 }}
                              />
                              <Line 
                                type="monotone" 
                                dataKey="forecast" 
                                name="คาดการณ์" 
                                stroke="#f44336" 
                                strokeWidth={2}
                                strokeDasharray="5 5"
                                dot={{ r: 5 }}
                                activeDot={{ r: 8 }}
                              />
                            </LineChart>
                          </ResponsiveContainer>
                        </div>
                      </div>
                      
                      <div className="space-y-3">
                        <h3 className="text-lg font-semibold">คาดการณ์รายเดือน</h3>
                        <div className="h-[300px]">
                          <ResponsiveContainer width="100%" height="100%">
                            <BarChart
                              data={predictionResults.predictions.monthlyForecasts}
                              margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
                            >
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis 
                                dataKey="month" 
                                angle={-45} 
                                textAnchor="end" 
                                height={80}
                                tick={{ fill: 'var(--color-foreground-muted)' }}
                              />
                              <YAxis 
                                yAxisId="left"
                                tick={{ fill: 'var(--color-foreground-muted)' }}
                              />
                              <YAxis 
                                yAxisId="right" 
                                orientation="right"
                                tick={{ fill: 'var(--color-foreground-muted)' }}
                              />
                              <RechartsTooltip />
                              <Legend />
                              <Bar 
                                yAxisId="left"
                                dataKey="forecast" 
                                name="จำนวนคาดการณ์" 
                                fill="#3f51b5" 
                              />
                              <Line 
                                yAxisId="right"
                                type="monotone" 
                                dataKey="changePercentage" 
                                name="การเปลี่ยนแปลง (%)" 
                                stroke="#ff9800" 
                                strokeWidth={2}
                              />
                            </BarChart>
                          </ResponsiveContainer>
                        </div>
                        
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>เดือน</TableHead>
                              <TableHead>จำนวนคาดการณ์</TableHead>
                              <TableHead>การเปลี่ยนแปลง (%)</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {predictionResults.predictions.monthlyForecasts && predictionResults.predictions.monthlyForecasts.map((month: any, index: number) => (
                              <TableRow key={index}>
                                <TableCell className="font-medium">{month.month}</TableCell>
                                <TableCell>{month.forecast}</TableCell>
                                <TableCell>
                                  <div className="flex items-center gap-2">
                                    {month.changePercentage > 0 ? (
                                      <TrendingUp className="h-4 w-4 text-green-500" />
                                    ) : month.changePercentage < 0 ? (
                                      <TrendingUp className="h-4 w-4 text-red-500 rotate-180" />
                                    ) : (
                                      <div className="h-4 w-4" />
                                    )}
                                    <span>{month.changePercentage > 0 ? '+' : ''}{month.changePercentage.toFixed(2)}%</span>
                                  </div>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                      
                      {includeDetails && predictionResults.predictions.historicalData && predictionResults.predictions.historicalData.weeklyUsage && (
                        <div className="space-y-3">
                          <h3 className="text-lg font-semibold">ข้อมูลการใช้งานรายสัปดาห์</h3>
                          <div className="h-[300px]">
                            <ResponsiveContainer width="100%" height="100%">
                              <BarChart
                                data={Object.entries(predictionResults.predictions.historicalData.weeklyUsage).map(([week, count]) => ({
                                  week: `สัปดาห์ที่ ${week}`,
                                  count
                                }))}
                                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                              >
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis 
                                  dataKey="week" 
                                  tick={{ fill: 'var(--color-foreground-muted)' }}
                                />
                                <YAxis 
                                  tick={{ fill: 'var(--color-foreground-muted)' }}
                                />
                                <RechartsTooltip />
                                <Legend />
                                <Bar dataKey="count" name="จำนวนรายการ" fill="#009688" />
                              </BarChart>
                            </ResponsiveContainer>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                  
                  {/* Amount Prediction Results */}
                  {predictionType === 'amount_prediction' && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Card className="bg-gradient-to-br from-purple-50 to-indigo-50 dark:from-purple-950/50 dark:to-indigo-950/50 border-none">
                          <CardContent className="pt-6">
                            <div className="text-xl font-bold mb-2">คาดการณ์จำนวนเงินเฉลี่ย 30 วันข้างหน้า</div>
                            <div className="text-3xl font-bold text-gradient-purple-to-indigo">
                              {predictionResults.predictions.amountForecasts?.next30DaysAvg?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} ฿
                            </div>
                          </CardContent>
                        </Card>
                        
                        <Card className="bg-gradient-to-br from-green-50 to-teal-50 dark:from-green-950/50 dark:to-teal-950/50 border-none">
                          <CardContent className="pt-6">
                            <div className="text-xl font-bold mb-2">คาดการณ์จำนวนเงินรวม 30 วันข้างหน้า</div>
                            <div className="text-3xl font-bold text-gradient-green-to-teal">
                              {predictionResults.predictions.amountForecasts?.next30DaysTotal?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} ฿
                            </div>
                          </CardContent>
                        </Card>
                        
                        <Card className="bg-gradient-to-br from-amber-50 to-orange-50 dark:from-amber-950/50 dark:to-orange-950/50 border-none">
                          <CardContent className="pt-6">
                            <div className="text-xl font-bold mb-2">แนวโน้มเทียบกับปัจจุบัน</div>
                            <div className="text-3xl font-bold text-gradient-amber-to-orange flex items-center">
                              {predictionResults.predictions.amountForecasts?.trend > 0 ? (
                                <TrendingUp className="h-6 w-6 mr-2" />
                              ) : predictionResults.predictions.amountForecasts?.trend < 0 ? (
                                <TrendingUp className="h-6 w-6 mr-2 rotate-180" />
                              ) : null}
                              {predictionResults.predictions.amountForecasts?.trend > 0 ? '+' : ''}
                              {predictionResults.predictions.amountForecasts?.trend.toFixed(2) || 0}%
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                      
                      <div className="space-y-3">
                        <h3 className="text-lg font-semibold">แนวโน้มจำนวนเงิน</h3>
                        <div className="h-[300px]">
                          <ResponsiveContainer width="100%" height="100%">
                            <LineChart
                              data={predictionResults.predictions.trendData}
                              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                            >
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis 
                                dataKey="date" 
                                tick={{ fill: 'var(--color-foreground-muted)' }}
                              />
                              <YAxis 
                                tick={{ fill: 'var(--color-foreground-muted)' }}
                              />
                              <RechartsTooltip />
                              <Legend />
                              <Line 
                                type="monotone" 
                                dataKey="actual" 
                                name="จำนวนเงินจริง" 
                                stroke="#5c6bc0"
                                strokeWidth={2}
                                dot={{ r: 5 }}
                                activeDot={{ r: 8 }}
                              />
                              <Line 
                                type="monotone" 
                                dataKey="forecast" 
                                name="คาดการณ์" 
                                stroke="#ec407a" 
                                strokeWidth={2}
                                strokeDasharray="5 5"
                                dot={{ r: 5 }}
                                activeDot={{ r: 8 }}
                              />
                            </LineChart>
                          </ResponsiveContainer>
                        </div>
                      </div>
                      
                      <div className="space-y-3">
                        <h3 className="text-lg font-semibold">คาดการณ์จำนวนเงินรายเดือน</h3>
                        <div className="h-[300px]">
                          <ResponsiveContainer width="100%" height="100%">
                            <BarChart
                              data={predictionResults.predictions.monthlyAmountForecasts}
                              margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
                            >
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis 
                                dataKey="month" 
                                angle={-45} 
                                textAnchor="end" 
                                height={80}
                                tick={{ fill: 'var(--color-foreground-muted)' }}
                              />
                              <YAxis 
                                yAxisId="left"
                                tick={{ fill: 'var(--color-foreground-muted)' }}
                              />
                              <YAxis 
                                yAxisId="right" 
                                orientation="right"
                                tick={{ fill: 'var(--color-foreground-muted)' }}
                              />
                              <RechartsTooltip />
                              <Legend />
                              <Bar 
                                yAxisId="left"
                                dataKey="averageAmount" 
                                name="จำนวนเงินเฉลี่ย" 
                                fill="#00bcd4" 
                              />
                              <Bar 
                                yAxisId="left"
                                dataKey="totalAmount" 
                                name="จำนวนเงินรวม" 
                                fill="#7e57c2" 
                              />
                              <Line 
                                yAxisId="right"
                                type="monotone" 
                                dataKey="changePercentage" 
                                name="การเปลี่ยนแปลง (%)" 
                                stroke="#ff5722" 
                                strokeWidth={2}
                              />
                            </BarChart>
                          </ResponsiveContainer>
                        </div>
                        
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>เดือน</TableHead>
                              <TableHead>จำนวนเงินเฉลี่ย</TableHead>
                              <TableHead>จำนวนเงินรวม</TableHead>
                              <TableHead>การเปลี่ยนแปลง (%)</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {predictionResults.predictions.monthlyAmountForecasts && predictionResults.predictions.monthlyAmountForecasts.map((month: any, index: number) => (
                              <TableRow key={index}>
                                <TableCell className="font-medium">{month.month}</TableCell>
                                <TableCell>{month.averageAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} ฿</TableCell>
                                <TableCell>{month.totalAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} ฿</TableCell>
                                <TableCell>
                                  <div className="flex items-center gap-2">
                                    {month.changePercentage > 0 ? (
                                      <TrendingUp className="h-4 w-4 text-green-500" />
                                    ) : month.changePercentage < 0 ? (
                                      <TrendingUp className="h-4 w-4 text-red-500 rotate-180" />
                                    ) : (
                                      <div className="h-4 w-4" />
                                    )}
                                    <span>{month.changePercentage > 0 ? '+' : ''}{month.changePercentage.toFixed(2)}%</span>
                                  </div>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    </div>
                  )}
                  
                  {/* Recurring Transactions Results */}
                  {predictionType === 'recurring_transactions' && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Card className="bg-gradient-to-br from-lime-50 to-green-50 dark:from-lime-950/50 dark:to-green-950/50 border-none col-span-1 md:col-span-3">
                          <CardContent className="pt-6">
                            <div className="text-xl font-bold mb-2">รายการที่คาดว่าจะเกิดซ้ำที่พบ</div>
                            <div className="text-4xl font-bold text-gradient-lime-to-green">
                              {predictionResults.predictions.recurringTransactions?.count || 0} รายการ
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                      
                      <div className="space-y-3">
                        <h3 className="text-lg font-semibold">รายการที่คาดว่าจะเกิดซ้ำ</h3>
                        <div className="grid md:grid-cols-2 gap-6">
                          <div className="h-[350px]">
                            <ResponsiveContainer width="100%" height="100%">
                              <BarChart 
                                data={predictionResults.predictions.recurringTransactions?.items}
                                margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
                              >
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis 
                                  dataKey="recipient" 
                                  angle={-45} 
                                  textAnchor="end" 
                                  height={80}
                                  tick={{ fill: 'var(--color-foreground-muted)' }}
                                />
                                <YAxis 
                                  tick={{ fill: 'var(--color-foreground-muted)' }}
                                />
                                <RechartsTooltip />
                                <Legend />
                                <Bar dataKey="occurrences" name="จำนวนครั้ง" fill="#ff9800" />
                                <Bar dataKey="averageAmount" name="จำนวนเงินเฉลี่ย" fill="#673ab7" />
                              </BarChart>
                            </ResponsiveContainer>
                          </div>
                          
                          <div className="h-[350px]">
                            <ResponsiveContainer width="100%" height="100%">
                              <PieChart width={400} height={350}>
                                <Pie
                                  data={predictionResults.predictions.recurringTransactions?.items}
                                  nameKey="recipient"
                                  dataKey="averageAmount"
                                  cx="50%"
                                  cy="50%"
                                  outerRadius={130}
                                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                                >
                                  {predictionResults.predictions.recurringTransactions?.items?.map((entry: any, index: number) => (
                                    <Cell key={`cell-${index}`} fill={`hsl(${index * 40}, 70%, 60%)`} />
                                  ))}
                                </Pie>
                                <RechartsTooltip />
                                <Legend />
                              </PieChart>
                            </ResponsiveContainer>
                          </div>
                        </div>
                        
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>ผู้รับ</TableHead>
                              <TableHead>จำนวนครั้งที่พบ</TableHead>
                              <TableHead>จำนวนเงินเฉลี่ย</TableHead>
                              <TableHead>ความถี่</TableHead>
                              <TableHead>วันที่คาดว่าจะเกิดครั้งถัดไป</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {predictionResults.predictions.recurringTransactions?.items && predictionResults.predictions.recurringTransactions.items.map((item: any, index: number) => (
                              <TableRow key={index}>
                                <TableCell className="font-medium">{item.recipient || 'ไม่ระบุ'}</TableCell>
                                <TableCell>{item.occurrences}</TableCell>
                                <TableCell>{item.averageAmount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} ฿</TableCell>
                                <TableCell>{item.frequency || '-'}</TableCell>
                                <TableCell>{item.nextExpectedDate ? format(new Date(item.nextExpectedDate), 'PPP', { locale: th }) : '-'}</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                        
                        {includeDetails && predictionResults.predictions.recurringTransactions?.items?.flatMap(item => item.historicalTransactions || []).length > 0 && (
                          <div className="space-y-3 mt-4 pt-4 border-t">
                            <h3 className="text-lg font-semibold">ประวัติรายการประจำ</h3>
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead>วันที่</TableHead>
                                  <TableHead>ผู้รับ</TableHead>
                                  <TableHead>จำนวนเงิน</TableHead>
                                  <TableHead>ธนาคาร</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {predictionResults.predictions.recurringTransactions?.items?.flatMap((item, itemIndex) => 
                                  (item.historicalTransactions || []).map((tx: any, txIndex: number) => (
                                    <TableRow key={`${itemIndex}-${txIndex}`}>
                                      <TableCell>{tx.date ? format(new Date(tx.date), 'PPP', { locale: th }) : '-'}</TableCell>
                                      <TableCell>{item.recipient || 'ไม่ระบุ'}</TableCell>
                                      <TableCell>{tx.amount?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0} ฿</TableCell>
                                      <TableCell>{tx.bank || 'ไม่ระบุ'}</TableCell>
                                    </TableRow>
                                  ))
                                )}
                              </TableBody>
                            </Table>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}