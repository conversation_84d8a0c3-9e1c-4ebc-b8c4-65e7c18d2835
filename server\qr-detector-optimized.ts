import jsQR from 'jsqr';
import { processImage } from './image-processor';
import { logger } from './logger';

/**
 * ตรวจสอบและอ่านข้อมูล QR code จากรูปภาพ
 * @param imageBuffer Buffer ของรูปภาพที่ต้องการตรวจสอบ
 * @returns Promise<{ hasQRCode: boolean; qrData?: string }> ผลการตรวจสอบ
 */
export async function detectQRCode(imageBuffer: Buffer): Promise<{ hasQRCode: boolean; qrData?: string }> {
  try {
    // ตรวจสอบขนาดไฟล์
    const fileSizeKB = Math.round(imageBuffer.length / 1024);
    if (imageBuffer.length < 1000 || imageBuffer.length > 10 * 1024 * 1024) {
      logger.debug(`❌ QR: ขนาดไฟล์ไม่อยู่ในเกณฑ์ที่กำหนด (${fileSizeKB} KB)`);
      return { hasQRCode: false };
    }

    // ประมวลผลรูปภาพให้เหมาะสมสำหรับการอ่าน QR code
    const processedImageBuffer = await processImage(imageBuffer, {
      resize: { width: 800, height: 800 },
      grayscale: true,
      sharpen: true,
      normalize: true,
    });

    // แปลงรูปภาพเป็นข้อมูลที่ jsQR สามารถใช้งานได้
    const { data, info } = await convertImageForQRReader(processedImageBuffer);
    
    if (!data) {
      logger.debug('❌ QR: ไม่สามารถแปลงรูปภาพเพื่ออ่าน QR code ได้');
      return { hasQRCode: false };
    }

    // อ่าน QR code จากรูปภาพ
    logger.debug(`🔍 QR: เริ่มอ่าน QR code จากรูปภาพขนาด ${info.width}x${info.height}`);
    
    try {
      const code = jsQR(data, info.width, info.height, {
        inversionAttempts: 'attemptBoth'  // ลองทั้งแบบปกติและกลับขาวดำ
      });
      
      if (!code) {
        logger.debug('❌ QR: ไม่พบ QR code ในรูปภาพ');
        return { hasQRCode: false };
      }
      
      // ตรวจสอบว่าข้อมูลใน QR code มีเฉพาะตัวเลขและตัวอักษรที่ยอมรับสำหรับสลิปธนาคารหรือไม่
      const containsMainlyNumbers = /^\d{2,}|[0-9A-Z]{10,}/i.test(code.data);
      
      if (!containsMainlyNumbers) {
        logger.debug('❌ QR: ข้อมูลใน QR code ไม่เข้าข่ายรูปแบบของสลิปธนาคาร');
        return { hasQRCode: false };
      }
      
      return {
        hasQRCode: true,
        qrData: code.data  // ข้อมูลจริงจาก QR code
      };
    } catch (qrError) {
      logger.error(`❌ QR: เกิดข้อผิดพลาดเมื่ออ่าน QR code: ${qrError instanceof Error ? qrError.message : 'ไม่ทราบสาเหตุ'}`);
      return { hasQRCode: false };
    }
  } catch (error) {
    logger.error('Error in QR detection:', error);
    return { hasQRCode: false };
  }
}

/**
 * แปลงรูปภาพให้เหมาะสมสำหรับการอ่าน QR code
 * @param imageBuffer Buffer ของรูปภาพที่ต้องการแปลง
 * @returns Promise<{ data: Uint8ClampedArray; info: { width: number; height: number; channels: number } }> ข้อมูลรูปภาพที่แปลงแล้ว
 */
async function convertImageForQRReader(imageBuffer: Buffer): Promise<{ 
  data: Uint8ClampedArray; 
  info: { width: number; height: number; channels: number } 
}> {
  try {
    const sharp = (await import('sharp')).default;
    
    // แปลงรูปภาพเป็น raw data
    const { data, info } = await sharp(imageBuffer)
      .raw()
      .toBuffer({ resolveWithObject: true });
    
    // ตรวจสอบว่าข้อมูลภาพถูกต้องหรือไม่
    if (!info.width || !info.height || !info.channels) {
      throw new Error('ข้อมูลรูปภาพไม่สมบูรณ์');
    }
    
    // แปลง Buffer เป็น Uint8ClampedArray
    const pixelArray = new Uint8ClampedArray(data.buffer);
    
    // ถ้าเป็นภาพ RGB หรือ RGBA ให้แปลงเป็น RGBA
    if (info.channels === 3) {
      // แปลง RGB เป็น RGBA
      const rgba = new Uint8ClampedArray(info.width * info.height * 4);
      for (let i = 0; i < pixelArray.length; i += 3) {
        const j = (i / 3) * 4;
        rgba[j] = pixelArray[i];
        rgba[j + 1] = pixelArray[i + 1];
        rgba[j + 2] = pixelArray[i + 2];
        rgba[j + 3] = 255; // Alpha = 255 (ทึบแสง)
      }
      return { data: rgba, info: { ...info, channels: 4 } };
    } else if (info.channels === 4) {
      // ถ้าเป็น RGBA อยู่แล้ว ให้ใช้ค่าเดิม
      return { data: pixelArray, info };
    } else if (info.channels === 1) {
      // แปลง Grayscale เป็น RGBA
      const rgba = new Uint8ClampedArray(info.width * info.height * 4);
      for (let i = 0; i < pixelArray.length; i++) {
        const j = i * 4;
        rgba[j] = pixelArray[i];
        rgba[j + 1] = pixelArray[i];
        rgba[j + 2] = pixelArray[i];
        rgba[j + 3] = 255; // Alpha = 255 (ทึบแสง)
      }
      return { data: rgba, info: { ...info, channels: 4 } };
    }
    
    throw new Error(`ไม่รองรับรูปภาพที่มี ${info.channels} channels`);
  } catch (error) {
    logger.error('Error converting image for QR reader:', error);
    throw error;
  }
}
