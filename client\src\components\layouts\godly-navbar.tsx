import { useState, useEffect } from "react";
import { Link, useLocation } from "wouter";
import { useAuth } from "@/hooks/use-auth";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import {
  ReceiptTextIcon,
  Menu,
  X,
  User,
  LogOut,
  Settings,
  Sparkles,
  Zap,
  Crown,
  Sun,
  CloudLightning,
  CircleUserRound,
  ScrollText,
  Package,
  Home,
  FileText,
  History,
  CreditCard,
  Shield,
  Key,
  MonitorPlay,
  FlameKindling,
  Search,
  Filter,
  Award,
  Medal
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

export function GodlyNavbar() {
  const [location] = useLocation();
  const { user, logoutMutation } = useAuth();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [hoverIndex, setHoverIndex] = useState<number | null>(null);

  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [scrolled]);

  useEffect(() => {
    // Close mobile menu when location changes
    setMobileMenuOpen(false);
  }, [location]);

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const handleLogout = () => {
    logoutMutation.mutate();
  };

  // การกำหนดเมนูหลัก
  const mainMenuItems = [
    { href: "/", label: "หน้าหลัก", icon: <Home className="h-5 w-5" />, divineClass: "from-sky-400 to-blue-500" },
    { href: "/docs", label: "API Docs", icon: <FileText className="h-5 w-5" />, divineClass: "from-emerald-400 to-teal-500" },
    { href: "/packages", label: "แพ็กเกจ", icon: <Package className="h-5 w-5" />, divineClass: "from-amber-400 to-orange-500" },
    ...(user ? [{ href: "/dashboard", label: "แดชบอร์ด", icon: <Crown className="h-5 w-5" />, divineClass: "from-purple-400 to-fuchsia-500" }] : [])
  ];

  // การกำหนดเมนูสำหรับผู้ใช้ที่เข้าสู่ระบบแล้ว
  const userMenuItems = [
    { href: "/dashboard", label: "แดชบอร์ด", icon: <Crown />, divineClass: "from-amber-400 to-yellow-500" },
    { href: "/verify", label: "ตรวจสอบสลิป", icon: <Zap />, divineClass: "from-purple-400 to-violet-500" },
    { href: "/history", label: "ประวัติการตรวจสอบ", icon: <History />, divineClass: "from-blue-400 to-indigo-500" },
    { href: "/advanced-search", label: "ค้นหาขั้นสูง", icon: <Search />, divineClass: "from-violet-400 to-fuchsia-500" },
    { href: "/achievements", label: "ความสำเร็จ", icon: <Award className="h-5 w-5" />, divineClass: "from-yellow-400 to-orange-500" },
    { href: "/topup", label: "เติมเงิน", icon: <CreditCard />, divineClass: "from-green-400 to-emerald-500" },
    { href: "/user-packages", label: "แพ็กเกจของฉัน", icon: <Package />, divineClass: "from-pink-400 to-rose-500" },
    { href: "/api-keys", label: "API Keys", icon: <Key />, divineClass: "from-cyan-400 to-teal-500" },
    { href: "/api-tester", label: "ทดสอบ API", icon: <MonitorPlay />, divineClass: "from-amber-400 to-orange-500" },
  ];

  const adminMenuItems = [
    { href: "/admin", label: "แผงควบคุมจักรวาล", icon: <Shield />, divineClass: "from-yellow-400 to-amber-500" },
    { href: "/admin/users", label: "จัดการมนุษย์ทั้งหลาย", icon: <User />, divineClass: "from-pink-400 to-rose-500" },
    { href: "/admin/packages", label: "จัดการแพ็กเกจศักดิ์สิทธิ์", icon: <Package />, divineClass: "from-blue-400 to-indigo-500" },
    { href: "/admin/stats", label: "สถิติแห่งจักรวาล", icon: <Sparkles />, divineClass: "from-violet-400 to-purple-500" },
  ];

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${scrolled ? 'backdrop-blur-md bg-black/20' : 'bg-transparent'}`}>
      {/* เส้นแบ่งเรืองแสงด้านบน */}
      <div className="h-0.5 w-full bg-gradient-to-r from-fuchsia-500/30 via-amber-300/50 to-purple-500/30 relative overflow-hidden">
        <motion.div
          className="absolute inset-0 opacity-75 bg-gradient-to-r from-purple-500 via-amber-300 to-fuchsia-500"
          animate={{ x: ['0%', '100%', '0%'] }}
          transition={{ duration: 15, repeat: Infinity, ease: "linear" }}
        />
      </div>

      <div className="container mx-auto px-4 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo และชื่อแบรนด์ */}
          <Link href="/">
            <motion.div
              className="flex items-center space-x-2 cursor-pointer group"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <div className="relative">
                {/* คลื่นพลังเทพเจ้ารอบโลโก้ */}
                <div className="absolute -inset-3 bg-gradient-to-r from-amber-400 to-purple-400 opacity-40
                  rounded-full blur-md group-hover:opacity-70 transition-opacity duration-300"></div>

                {/* วงกลมเทพเจ้า */}
                <div className="relative flex items-center justify-center h-10 w-10 bg-gradient-to-r
                  from-indigo-600 to-purple-600 rounded-full border border-white/20">
                  {/* Icon บนโลโก้ */}
                  <Crown className="h-5 w-5 text-amber-300 group-hover:text-amber-200 transition-colors duration-300" />

                  {/* รังสีเทพเจ้า */}
                  <motion.div
                    className="absolute -inset-2 border border-amber-400/30 rounded-full"
                    animate={{ scale: [1, 1.2, 1], opacity: [0.5, 0.2, 0.5] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  />
                </div>
              </div>

              {/* ชื่อแบรนด์ */}
              <div className="text-2xl font-extrabold">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-purple-500">SLIP</span>
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-amber-400 to-yellow-300">KUY</span>
              </div>

              {/* เอฟเฟกต์ฟ้าผ่า */}
              <motion.div
                className="absolute -top-1 left-10 opacity-70"
                initial={{ opacity: 0, rotate: -15, scale: 0.7 }}
                animate={{ opacity: [0, 0.7, 0], rotate: [-15, -10, -15], scale: [0.7, 0.8, 0.7] }}
                transition={{ duration: 1.5, repeat: Infinity, repeatDelay: 5 }}
              >
                <CloudLightning className="h-3 w-3 text-yellow-300" />
              </motion.div>
            </motion.div>
          </Link>

          {/* Desktop Navigation - สำหรับหน้าจอขนาดใหญ่ */}
          <nav className="hidden lg:flex items-center space-x-1">
            {mainMenuItems.map((item, index) => (
              <Link key={item.href} href={item.href}>
                <motion.div
                  className={`relative cursor-pointer px-3 py-2 text-base font-medium flex items-center gap-1.5 rounded-lg group
                  ${location === item.href
                    ? 'text-white'
                    : 'text-white/80 hover:text-white'}`}
                  onHoverStart={() => setHoverIndex(index)}
                  onHoverEnd={() => setHoverIndex(null)}
                  whileHover={{ y: -2 }}
                  transition={{ type: "spring", stiffness: 400, damping: 17 }}
                >
                  {/* พื้นหลังเมื่อ Hover */}
                  <div className={`absolute inset-0 bg-gradient-to-r ${item.divineClass} opacity-0 rounded-lg -z-10
                    ${location === item.href ? 'opacity-20' : ''} group-hover:opacity-20 transition-opacity duration-300`} />

                  {/* ไอคอนที่เคลื่อนไหวเมื่อ Hover */}
                  <motion.span
                    className={`${location === item.href ? 'text-white' : 'text-white/70'} group-hover:text-white transition-colors`}
                    animate={hoverIndex === index ? { rotate: [0, -10, 10, -10, 0] } : {}}
                    transition={{ duration: 0.5 }}
                  >
                    {item.icon}
                  </motion.span>

                  <span className="relative">
                    {item.label}

                    {/* ตัวบ่งชี้เมนูที่เลือก */}
                    {location === item.href && (
                      <motion.span
                        className={`absolute -bottom-1 left-0 w-full h-0.5 bg-gradient-to-r ${item.divineClass} rounded-full`}
                        layoutId="main-navbar-indicator"
                        transition={{ type: "spring", stiffness: 400, damping: 25 }}
                      />
                    )}
                  </span>
                </motion.div>
              </Link>
            ))}
          </nav>

          {/* สำหรับผู้ใช้ที่เข้าสู่ระบบแล้ว หรือปุ่มลงทะเบียน/เข้าสู่ระบบ */}
          <div className="hidden md:flex items-center space-x-3">
            {user ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="relative px-4 py-2 rounded-full overflow-hidden group border-0 bg-white/10 hover:bg-white/20">
                    {/* พื้นหลังเรืองแสง */}
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-600/30 to-pink-600/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                    {/* ขอบเรืองแสง */}
                    <div className="absolute inset-0 rounded-full overflow-hidden">
                      <div className="absolute inset-[-1px] bg-gradient-to-r from-amber-300/50 via-purple-300/50 to-fuchsia-300/50
                        opacity-60 group-hover:opacity-100 transition-opacity duration-300 blur-[1px]"></div>
                    </div>

                    {/* เอฟเฟกต์เพิ่มเติม */}
                    <div className="absolute inset-0 rounded-full bg-amber-400/10 animate-pulse opacity-50"></div>

                    <div className="flex items-center gap-2 relative z-10">
                      {/* ไอคอนผู้ใช้ */}
                      <div className="relative">
                        {/* แสงเรืองรอบไอคอน */}
                        <div className="absolute -inset-1 bg-gradient-to-r from-amber-300 to-fuchsia-400 rounded-full opacity-60 blur-sm"></div>

                        {/* วงกลมหลัก */}
                        <div className="h-7 w-7 rounded-full bg-gradient-to-br from-indigo-500 via-purple-500 to-fuchsia-500 flex items-center justify-center relative border border-white/20">
                          <CircleUserRound className="h-4 w-4 text-white drop-shadow-md" />

                          {/* ดาวระยิบระยับ */}
                          <div className="absolute -top-1 -right-1 w-2 h-2 bg-yellow-300 rounded-full animate-pulse"></div>
                          <div className="absolute bottom-0 -left-1 w-1.5 h-1.5 bg-amber-300 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
                        </div>
                      </div>

                      <div>
                        <span className="font-bold text-sm bg-clip-text text-transparent bg-gradient-to-r from-amber-200 to-amber-100 drop-shadow-md">
                          {user.username}
                        </span>
                      </div>
                    </div>
                  </Button>
                </DropdownMenuTrigger>

                <DropdownMenuContent align="end" className="w-60 bg-gradient-to-b from-gray-900/95 to-indigo-950/95 text-white border border-purple-400/20 shadow-[0_0_15px_rgba(139,92,246,0.3)]">
                  <DropdownMenuLabel className="text-amber-300 font-bold text-center border-b border-gray-700/50 pb-2">
                    <div className="flex items-center justify-center">
                      <Crown className="h-4 w-4 mr-2 text-amber-300" />
                      <span>บัญชีของเทพเจ้า</span>
                    </div>
                  </DropdownMenuLabel>

                  <div className="px-1 py-1">
                    {userMenuItems.map((item) => (
                      <DropdownMenuItem key={item.href} asChild className="px-1 py-0.5 rounded-md my-1 focus:bg-white/10 hover:bg-white/10">
                        <Link href={item.href} className="flex items-center px-2 py-1.5">
                          <div className={`mr-2 h-6 w-6 rounded-full flex items-center justify-center bg-gradient-to-r ${item.divineClass} bg-opacity-20`}>
                            <motion.div whileHover={{ rotate: 360 }} transition={{ duration: 0.5 }}>
                              {item.icon}
                            </motion.div>
                          </div>
                          <span className="text-white/90 hover:text-white">{item.label}</span>

                          {/* เอฟเฟกต์แสงวาบเมื่อ hover */}
                          <div className={`absolute inset-0 rounded-md bg-gradient-to-r ${item.divineClass} opacity-0 hover:opacity-10 transition-opacity duration-300`}></div>
                        </Link>
                      </DropdownMenuItem>
                    ))}
                  </div>

                  {user.role === 'admin' && (
                    <>
                      <div className="px-2 my-1">
                        <div className="flex items-center py-1">
                          <div className="grow h-[1px] bg-gradient-to-r from-transparent via-amber-500/30 to-transparent"></div>
                          <span className="px-2 text-xs text-amber-400/80 font-semibold">พลังเทพเจ้าชั้นสูง</span>
                          <div className="grow h-[1px] bg-gradient-to-r from-transparent via-amber-500/30 to-transparent"></div>
                        </div>
                      </div>

                      <div className="px-1 py-1">
                        {adminMenuItems.map((item) => (
                          <DropdownMenuItem key={item.href} asChild className="px-1 py-0.5 rounded-md my-1 focus:bg-white/10 hover:bg-white/10">
                            <Link href={item.href} className="flex items-center px-2 py-1.5">
                              <div className={`mr-2 h-6 w-6 rounded-full flex items-center justify-center bg-gradient-to-r ${item.divineClass} bg-opacity-20`}>
                                <motion.div whileHover={{ rotate: 360 }} transition={{ duration: 0.5 }}>
                                  {item.icon}
                                </motion.div>
                              </div>
                              <span className="text-white/90 hover:text-white">{item.label}</span>

                              {/* เอฟเฟกต์แสงวาบเมื่อ hover */}
                              <div className={`absolute inset-0 rounded-md bg-gradient-to-r ${item.divineClass} opacity-0 hover:opacity-10 transition-opacity duration-300`}></div>
                            </Link>
                          </DropdownMenuItem>
                        ))}
                      </div>
                    </>
                  )}

                  <div className="px-2 my-1">
                    <div className="flex items-center py-1">
                      <div className="grow h-[1px] bg-gradient-to-r from-transparent via-red-500/30 to-transparent"></div>
                    </div>
                  </div>

                  <div className="px-1 py-1">
                    <DropdownMenuItem
                      onClick={handleLogout}
                      className="px-1 py-0.5 rounded-md my-1 focus:bg-red-500/20 hover:bg-red-500/20 text-red-300 flex items-center"
                    >
                      <div className="mr-2 h-6 w-6 rounded-full flex items-center justify-center bg-gradient-to-r from-red-400 to-pink-500 bg-opacity-20">
                        <LogOut className="h-4 w-4" />
                      </div>
                      <span>ออกจากดินแดนศักดิ์สิทธิ์</span>
                    </DropdownMenuItem>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <>
                <Link href="/auth">
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.98 }}>
                    <Button variant="ghost" className="text-white hover:text-white border-white/20 px-5 py-2 rounded-full font-medium relative overflow-hidden group bg-white/5">
                      <div className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full"></div>
                      <CloudLightning className="h-4 w-4 mr-2 text-amber-300" />
                      <span className="relative z-10">เข้าสู่ระบบ</span>
                    </Button>
                  </motion.div>
                </Link>
                <Link href="/auth">
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.98 }}
                    className="relative"
                  >
                    <div className="absolute -inset-1 bg-gradient-to-r from-amber-400 to-fuchsia-400 rounded-full opacity-70 blur-md"></div>
                    <Button className="relative bg-gradient-to-r from-indigo-600 to-purple-700 hover:from-indigo-500 hover:to-purple-600 text-white border-0 px-5 py-5 h-10 rounded-full font-medium shadow-lg">
                      <Crown className="h-4 w-4 mr-2 text-amber-300" />
                      <span className="font-bold text-white">ลงทะเบียน</span>
                    </Button>
                  </motion.div>
                </Link>
              </>
            )}
          </div>

          {/* ปุ่มเมนูสำหรับมือถือ */}
          <div className="lg:hidden flex items-center">
            {user && (
              <Link href="/dashboard" className="mr-2">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="relative w-10 h-10"
                >
                  <div className="absolute -inset-1 bg-gradient-to-r from-amber-300 to-fuchsia-400 rounded-full opacity-60 blur-sm"></div>
                  <div className="relative h-full w-full rounded-full bg-gradient-to-br from-indigo-500 via-purple-500 to-fuchsia-500 flex items-center justify-center border border-white/20">
                    <CircleUserRound className="h-5 w-5 text-white drop-shadow-md" />
                  </div>
                </motion.div>
              </Link>
            )}

            <Button
              variant="outline"
              size="icon"
              onClick={toggleMobileMenu}
              aria-label={mobileMenuOpen ? "Close Menu" : "Open Menu"}
              className="relative w-10 h-10 text-white border-0 bg-white/10 hover:bg-white/20 rounded-full"
            >
              <AnimatePresence mode="wait">
                {mobileMenuOpen ? (
                  <motion.div
                    key="close"
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0.8, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    className="absolute inset-0 flex items-center justify-center"
                  >
                    <X className="h-5 w-5 text-amber-300" />
                  </motion.div>
                ) : (
                  <motion.div
                    key="menu"
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0.8, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    className="absolute inset-0 flex items-center justify-center"
                  >
                    <Menu className="h-5 w-5 text-amber-300" />
                  </motion.div>
                )}
              </AnimatePresence>
            </Button>
          </div>
        </div>
      </div>

      {/* เมนูมือถือ */}
      <AnimatePresence>
        {mobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="lg:hidden border-t border-white/10 backdrop-blur-md bg-black/40"
          >
            <nav className="container mx-auto px-4 pt-2 pb-4">
              <div className="space-y-1">
                {/* เมนูหลัก */}
                {mainMenuItems.map((item) => (
                  <Link key={item.href} href={item.href}>
                    <motion.div
                      className={`flex items-center px-3 py-2 text-base font-medium rounded-lg ${
                        location === item.href ? 'bg-white/10 text-white' : 'text-white/80 hover:bg-white/5 hover:text-white'
                      }`}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className={`mr-3 h-7 w-7 rounded-full flex items-center justify-center bg-gradient-to-r ${item.divineClass}`}>
                        {item.icon}
                      </div>
                      {item.label}
                    </motion.div>
                  </Link>
                ))}

                {/* เมนูสำหรับผู้ใช้ที่เข้าสู่ระบบแล้ว */}
                {user && (
                  <>
                    <div className="mt-4 pt-2 border-t border-white/10">
                      <p className="px-3 pb-2 text-sm font-semibold text-amber-400">บัญชีของเทพเจ้า</p>
                      {userMenuItems.map((item) => (
                        <Link key={item.href} href={item.href}>
                          <motion.div
                            className={`flex items-center px-3 py-2 text-base font-medium rounded-lg ${
                              location === item.href ? 'bg-white/10 text-white' : 'text-white/80 hover:bg-white/5 hover:text-white'
                            }`}
                            whileTap={{ scale: 0.98 }}
                          >
                            <div className={`mr-3 h-7 w-7 rounded-full flex items-center justify-center bg-gradient-to-r ${item.divineClass}`}>
                              {item.icon}
                            </div>
                            {item.label}
                          </motion.div>
                        </Link>
                      ))}
                    </div>

                    {/* เมนูแอดมิน */}
                    {user.role === 'admin' && (
                      <div className="mt-4 pt-2 border-t border-white/10">
                        <p className="px-3 pb-2 text-sm font-semibold text-amber-400">พลังเทพเจ้าชั้นสูง</p>
                        {adminMenuItems.map((item) => (
                          <Link key={item.href} href={item.href}>
                            <motion.div
                              className={`flex items-center px-3 py-2 text-base font-medium rounded-lg ${
                                location === item.href ? 'bg-white/10 text-white' : 'text-white/80 hover:bg-white/5 hover:text-white'
                              }`}
                              whileTap={{ scale: 0.98 }}
                            >
                              <div className={`mr-3 h-7 w-7 rounded-full flex items-center justify-center bg-gradient-to-r ${item.divineClass}`}>
                                {item.icon}
                              </div>
                              {item.label}
                            </motion.div>
                          </Link>
                        ))}
                      </div>
                    )}

                    {/* ปุ่มออกจากระบบ */}
                    <div className="mt-4 pt-2 border-t border-white/10">
                      <motion.div
                        className="flex items-center px-3 py-2 text-base font-medium text-red-300 hover:bg-red-500/10 rounded-lg"
                        onClick={handleLogout}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="mr-3 h-7 w-7 rounded-full flex items-center justify-center bg-gradient-to-r from-red-500 to-pink-500">
                          <LogOut className="h-4 w-4 text-white" />
                        </div>
                        ออกจากดินแดนศักดิ์สิทธิ์
                      </motion.div>
                    </div>
                  </>
                )}

                {/* ปุ่มเข้าสู่ระบบสำหรับผู้ที่ยังไม่ได้เข้าสู่ระบบบน Mobile */}
                {!user && (
                  <div className="mt-4 pt-4 flex flex-col space-y-3 border-t border-white/10">
                    <Link href="/auth">
                      <Button variant="outline" className="w-full bg-white/5 border border-white/20 text-white hover:bg-white/10 hover:text-white">
                        <CloudLightning className="h-4 w-4 mr-2 text-amber-300" />
                        เข้าสู่ระบบ
                      </Button>
                    </Link>
                    <Link href="/auth">
                      <Button className="w-full bg-gradient-to-r from-indigo-600 to-purple-700 hover:from-indigo-500 hover:to-purple-600 text-white">
                        <Crown className="h-4 w-4 mr-2 text-amber-300" />
                        <span className="text-white">ลงทะเบียน</span>
                      </Button>
                    </Link>
                  </div>
                )}
              </div>
            </nav>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  );
}