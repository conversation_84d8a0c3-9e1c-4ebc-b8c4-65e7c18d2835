import { Card, CardContent } from "@/components/ui/card";
import { AlertCircle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Link } from "wouter";

export default function NotFound() {
  return (
    <div className="min-h-screen w-full flex items-center justify-center bg-gradient-to-b from-gray-900 to-slate-900">
      <Card className="w-full max-w-md mx-4 border-purple-500 border-2 shadow-lg shadow-purple-500/20 bg-black bg-opacity-70 backdrop-blur-sm">
        <CardContent className="pt-6">
          <div className="flex flex-col items-center mb-4 gap-2">
            <AlertCircle className="h-16 w-16 text-red-500 animate-pulse" />
            <h1 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-pink-500 to-red-500">
              404 ไม่พบหน้าที่คุณค้นหา
            </h1>
          </div>

          <p className="mt-4 text-sm text-center text-gray-300">
            ไม่พบหน้าที่คุณต้องการเข้าถึง หน้านี้อาจถูกลบหรือย้ายไปแล้ว
          </p>
          
          <div className="flex justify-center mt-6">
            <Link href="/">
              <Button className="bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600 transition-all duration-300">
                กลับไปหน้าหลัก
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
