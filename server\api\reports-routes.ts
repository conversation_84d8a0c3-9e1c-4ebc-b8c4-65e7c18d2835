import { Router } from 'express';
import * as reportsController from './reports';

const router = Router();

// สำหรับผู้ใช้ทั่วไป
router.post('/', reportsController.createUserReport);
router.get('/', reportsController.getUserReports);
router.get('/:id', reportsController.getReportById);
router.delete('/:id', reportsController.deleteReport);
router.post('/:id/email', reportsController.sendReportEmail);

// สำหรับผู้ดูแลระบบ
router.get('/admin/stats', reportsController.getReportStats);

export default router;