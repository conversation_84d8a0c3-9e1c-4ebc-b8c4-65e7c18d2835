import React from 'react';
import { motion } from 'framer-motion';
import { Bell, AlertTriangle, CheckCircle, Info, Star, Clock, Zap, XCircle } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { formatThaiDateTime } from '@/lib/utils';

type NotificationPriority = 'low' | 'medium' | 'high' | 'critical';
type NotificationType = 'unusual_transaction' | 'quota_low' | 'api_key_expiring' | 'fraud_detection' | 'daily_report' | 'weekly_report' | 'monthly_report' | 'system_update';

interface GodNotificationProps {
  id: number;
  title: string;
  message: string;
  type: NotificationType;
  priority: NotificationPriority;
  isRead: boolean;
  createdAt: string;
  onMarkAsRead?: (id: number) => void;
  onDelete?: (id: number) => void;
}

export function GodNotification({ 
  id,
  title,
  message,
  type,
  priority,
  isRead,
  createdAt,
  onMarkAsRead,
  onDelete
}: GodNotificationProps) {
  // เลือกไอคอนตามประเภทการแจ้งเตือน
  const getIcon = () => {
    switch (type) {
      case 'unusual_transaction':
        return <AlertTriangle className="h-5 w-5 text-yellow-400" />;
      case 'quota_low':
        return <Star className="h-5 w-5 text-indigo-400" />;
      case 'api_key_expiring':
        return <Clock className="h-5 w-5 text-purple-400" />;
      case 'fraud_detection':
        return <AlertTriangle className="h-5 w-5 text-red-400" />;
      case 'daily_report':
      case 'weekly_report':
      case 'monthly_report':
        return <CheckCircle className="h-5 w-5 text-emerald-400" />;
      case 'system_update':
        return <Zap className="h-5 w-5 text-blue-400" />;
      default:
        return <Bell className="h-5 w-5 text-gray-400" />;
    }
  };

  // สีสำหรับความสำคัญต่างๆ
  const getPriorityColor = () => {
    switch (priority) {
      case 'critical':
        return 'bg-red-500 text-white';
      case 'high':
        return 'bg-orange-500 text-white';
      case 'medium':
        return 'bg-yellow-500 text-black';
      case 'low':
      default:
        return 'bg-blue-500 text-white';
    }
  };

  // ข้อความแสดงความสำคัญ
  const getPriorityText = () => {
    switch (priority) {
      case 'critical':
        return 'วิกฤติ';
      case 'high':
        return 'สูง';
      case 'medium':
        return 'ปานกลาง';
      case 'low':
      default:
        return 'ต่ำ';
    }
  };

  // ข้อความแสดงประเภทการแจ้งเตือน
  const getTypeText = () => {
    switch (type) {
      case 'unusual_transaction':
        return 'ธุรกรรมผิดปกติ';
      case 'quota_low':
        return 'โควตาใกล้หมด';
      case 'api_key_expiring':
        return 'API Key ใกล้หมดอายุ';
      case 'fraud_detection':
        return 'ตรวจพบการฉ้อโกง';
      case 'daily_report':
        return 'รายงานประจำวัน';
      case 'weekly_report':
        return 'รายงานประจำสัปดาห์';
      case 'monthly_report':
        return 'รายงานประจำเดือน';
      case 'system_update':
        return 'อัปเดตระบบ';
      default:
        return 'ทั่วไป';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, scale: 0.95 }}
      transition={{ duration: 0.2 }}
    >
      <Card 
        className={`overflow-hidden ${isRead ? 'bg-gray-800/80' : 'bg-gray-900/90'} border-0 shadow-lg`}
      >
        {/* เอฟเฟกต์แสงพื้นหลังแบบเทพเจ้า */}
        <div className="absolute inset-0 z-0">
          <div className={`absolute inset-0 opacity-${isRead ? '20' : '30'} bg-gradient-to-br from-indigo-900/40 to-purple-900/40`}></div>
          
          {!isRead && (
            <>
              <div className="absolute top-0 right-0 w-20 h-20 bg-indigo-500/10 rounded-full blur-xl"></div>
              <div className="absolute bottom-0 left-0 w-20 h-20 bg-purple-500/10 rounded-full blur-xl"></div>
            </>
          )}
          
          {/* เส้นแสงเรืองรองด้านบนและด้านล่าง */}
          <div className="absolute top-0 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-indigo-500/30 to-transparent"></div>
          <div className="absolute bottom-0 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-purple-500/30 to-transparent"></div>
        </div>
        
        <CardContent className="p-4 relative z-10">
          <div className="flex items-start gap-3">
            <div className={`p-2 rounded-full ${isRead ? 'bg-gray-700/80' : 'bg-gray-800/80'} mt-1`}>
              {getIcon()}
            </div>
            
            <div className="flex-1">
              <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2 mb-1">
                <h3 className={`font-semibold text-base ${isRead ? 'text-gray-300' : 'text-white'}`}>
                  {title}
                </h3>
                <div className="flex items-center gap-2 flex-wrap">
                  <Badge className={getPriorityColor()}>
                    {getPriorityText()}
                  </Badge>
                  <Badge className="bg-indigo-700/80 text-white">
                    {getTypeText()}
                  </Badge>
                </div>
              </div>
              
              <p className={`text-sm mb-3 ${isRead ? 'text-gray-400' : 'text-gray-200'}`}>
                {message}
              </p>
              
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-400">
                  {formatThaiDateTime(createdAt)}
                </span>
                
                <div className="flex items-center gap-2">
                  {!isRead && onMarkAsRead && (
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="h-8 px-2 text-xs hover:bg-indigo-500/20 text-indigo-300"
                      onClick={() => onMarkAsRead(id)}
                    >
                      อ่านแล้ว
                    </Button>
                  )}
                  
                  {onDelete && (
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="h-8 px-2 text-xs hover:bg-red-500/20 text-red-300"
                      onClick={() => onDelete(id)}
                    >
                      <XCircle className="h-4 w-4 mr-1" />
                      ลบ
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

// คอมโพเนนต์สำหรับการแสดงรายการการแจ้งเตือน
export function GodNotificationList({ 
  notifications, 
  onMarkAsRead, 
  onDelete,
  emptyMessage = "ไม่มีการแจ้งเตือน"
}: { 
  notifications: GodNotificationProps[]; 
  onMarkAsRead?: (id: number) => void;
  onDelete?: (id: number) => void;
  emptyMessage?: string;
}) {
  if (notifications.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-gray-500 bg-gray-800/50 rounded-lg">
        <Bell className="h-12 w-12 mb-2 text-gray-400 opacity-50" />
        <p>{emptyMessage}</p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {notifications.map((notification) => (
        <GodNotification 
          key={notification.id} 
          {...notification} 
          onMarkAsRead={onMarkAsRead} 
          onDelete={onDelete} 
        />
      ))}
    </div>
  );
}

// คอมโพเนนต์สำหรับแสดงจำนวนการแจ้งเตือนที่ยังไม่ได้อ่าน
export function GodNotificationBadge({ count }: { count: number }) {
  if (count <= 0) return null;

  return (
    <motion.div
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      className="relative"
    >
      <span className="absolute -top-2 -right-2 flex h-5 w-5 items-center justify-center rounded-full bg-gradient-to-r from-red-500 to-pink-500 text-[10px] font-bold text-white ring-2 ring-gray-900">
        {count > 99 ? '99+' : count}
      </span>
      
      {/* เอฟเฟกต์เรืองแสงสำหรับการแจ้งเตือนความสำคัญสูง */}
      {count > 5 && (
        <motion.span
          animate={{ 
            scale: [1, 1.2, 1],
            opacity: [0.7, 0.2, 0.7]
          }}
          transition={{ duration: 2, repeat: Infinity }}
          className="absolute -top-2 -right-2 h-5 w-5 rounded-full bg-red-500 opacity-50 blur-sm"
        />
      )}
    </motion.div>
  );
}