services:
  # ฐานข้อมูล PostgreSQL
  postgres:
    image: postgres:16.9
    container_name: slipkuy_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: slipkuy_db
      POSTGRES_USER: slipkuy_user
      POSTGRES_PASSWORD: slipkuy_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
      LC_ALL: C.UTF-8
      LANG: C.UTF-8
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./DB_backup/slipkuy_db_backup.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U slipkuy_user -d slipkuy_db"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - slipkuy-network

  # Redis (สำหรับ session และ cache)
  redis:
    image: redis:7-alpine
    container_name: slipkuy_redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - slipkuy-network

  # แอปพลิเคชัน SLIPKUY
  slipkuy:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: slipkuy_app
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      NODE_ENV: production
      PORT: 4000
      DATABASE_URL: ********************************************************/slipkuy_db
      
      # Redis settings
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ""
      REDIS_DB: 0
      
      # Session settings
      SESSION_SECRET: slipkuy_session_secret_docker_2025
      
      # API Keys (ใช้ค่าเริ่มต้นจาก README)
      EASYSLIP_API_KEY: db34c04e-9465-4b60-8bb6-5cdb3faa1973
      SLIP2GO_API_KEY: RNWeLFE7aTalOXURHjYghkNG0JOFS7RrQXWf93ZBVFc=
      
      # Email settings (สามารถแก้ไขได้ในหน้า Admin)
      EMAIL_USER: ${EMAIL_USER:-<EMAIL>}
      EMAIL_PASS: ${EMAIL_PASS:-yzjecbqmurjyyxsu}
      EMAIL_FROM: ${EMAIL_FROM:-SLIPKUY Alert <<EMAIL>>}
      
      # SMS settings (optional)
      SMSMKT_API_KEY: ${SMSMKT_API_KEY:-}
      SMSMKT_SECRET: ${SMSMKT_SECRET:-}
      SMSMKT_SENDER: ${SMSMKT_SENDER:-SLIPKUY}
      
      # Encoding settings
      LC_ALL: C.UTF-8
      LANG: C.UTF-8
      
    ports:
      - "4000:4000"
    volumes:
      - slipkuy_uploads:/app/public/uploads
      - slipkuy_logs:/app/logs
      - slipkuy_tmp:/app/tmp
      - slipkuy_backups:/app/backups
    networks:
      - slipkuy-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://127.0.0.1:4000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Cloudflare Tunnel (สำหรับเข้าถึงจากภายนอก)
  cloudflared:
    image: cloudflare/cloudflared:latest
    container_name: slipkuy_cloudflared
    restart: unless-stopped
    command: tunnel --no-autoupdate run --token eyJhIjoiZjcxMDZlZmQ4MmZlNjNmNmZjODA3NDVkMWU4MGI1N2QiLCJ0IjoiNDk4YjYxZTUtY2YzYS00OWJhLThjODgtNDUyMDFiOTQ4MmU2IiwicyI6Ik5USXpOR1ppTTJJdE9EbG1PUzAwTmpRMkxUZzJZVEF0WVdReU9HTXhPR1psWkRVNSJ9
    depends_on:
      slipkuy:
        condition: service_healthy
    networks:
      - slipkuy-network

  # pgAdmin (ตัวจัดการฐานข้อมูล - เลือกใช้หรือไม่ก็ได้)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: slipkuy_pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: slipkuy123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    depends_on:
      - postgres
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - slipkuy-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  slipkuy_uploads:
    driver: local
  slipkuy_logs:
    driver: local
  slipkuy_tmp:
    driver: local
  slipkuy_backups:
    driver: local
  pgadmin_data:
    driver: local

networks:
  slipkuy-network:
    driver: bridge
