import React, { useState, useEffect } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AlertCircle, Check, Loader2, Mail, Save, Send, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger,
  DialogFooter
} from '@/components/ui/dialog';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

export interface EmailSettingsProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  onSave?: () => void;
}

export function EmailSettingsDialog({ open, setOpen, onSave }: EmailSettingsProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState('settings');
  const [testEmail, setTestEmail] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [isTesting, setIsTesting] = useState(false);

  // ดึงข้อมูลการตั้งค่าอีเมล
  const {
    data: emailSettings,
    isLoading: isLoadingSettings,
    isError: isErrorSettings,
    error: errorSettings,
    refetch: refetchSettings,
  } = useQuery({
    queryKey: ['/api/email/settings'],
    queryFn: async () => {
      const response = await fetch('/api/email/settings', {
        credentials: 'include' // ส่ง cookies เพื่อการยืนยันตัวตน
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'ไม่สามารถดึงข้อมูลการตั้งค่าอีเมลได้');
      }
      return response.json();
    },
    retry: false
  });

  // เมื่อกดปุ่มบันทึกการตั้งค่าอีเมล
  const updateEmailSettings = async (settings: any) => {
    setIsSaving(true);
    try {
      // ส่ง keepPassword เป็น header
      const headers: HeadersInit = {};
      if (!settings.auth.pass) {
        headers['X-Keep-Password'] = 'true';
      }
      
      const response = await apiRequest('PUT', '/api/email/settings', settings, headers);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'ไม่สามารถบันทึกการตั้งค่าอีเมลได้');
      }
      
      await queryClient.invalidateQueries({ queryKey: ['/api/email/settings'] });
      toast({
        title: "บันทึกสำเร็จ",
        description: "บันทึกการตั้งค่าอีเมลเรียบร้อยแล้ว",
        variant: "default",
      });
      
      if (onSave) {
        onSave();
      }
      
      return await response.json();
    } catch (error: any) {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message || "ไม่สามารถบันทึกการตั้งค่าอีเมลได้",
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsSaving(false);
    }
  };

  // เมื่อกดปุ่มทดสอบส่งอีเมล
  const testEmailSend = async (email: string) => {
    setIsTesting(true);
    try {
      const response = await apiRequest('POST', '/api/email/settings/test', { to: email });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'ไม่สามารถทดสอบส่งอีเมลได้');
      }
      
      toast({
        title: "ส่งอีเมลทดสอบสำเร็จ",
        description: `ส่งอีเมลทดสอบไปยัง ${email} เรียบร้อยแล้ว`,
        variant: "default",
      });
      
      return await response.json();
    } catch (error: any) {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message || "ไม่สามารถส่งอีเมลทดสอบได้",
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsTesting(false);
    }
  };

  // การจัดการฟอร์ม
  const [formData, setFormData] = useState({
    service: '',
    host: '',
    port: '',
    secure: false,
    auth: {
      user: '',
      pass: '',
    },
    defaultName: '',
    from: '',
  });

  // อัพเดทข้อมูลฟอร์มเมื่อมีการโหลดข้อมูล
  useEffect(() => {
    if (emailSettings) {
      setFormData({
        service: emailSettings.service || '',
        host: emailSettings.host || '',
        port: emailSettings.port || '',
        secure: emailSettings.secure || false,
        auth: {
          user: emailSettings.auth?.user || '',
          pass: '', // ไม่ดึงรหัสผ่านมาแสดง
        },
        defaultName: emailSettings.defaultName || '',
        from: emailSettings.from || '',
      });
    }
  }, [emailSettings]);

  // ฟังก์ชันจัดการการเปลี่ยนแปลงข้อมูลในฟอร์ม
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    if (name.startsWith('auth.')) {
      // สำหรับข้อมูลใน auth object
      const authField = name.split('.')[1];
      setFormData({
        ...formData,
        auth: {
          ...formData.auth,
          [authField]: value,
        },
      });
    } else {
      // สำหรับข้อมูลอื่นๆ
      setFormData({
        ...formData,
        [name]: name === 'port' ? parseInt(value) : value,
      });
    }
  };

  // ฟังก์ชันจัดการการเปลี่ยนแปลงค่า secure (เป็น boolean)
  const handleSecureChange = (value: string) => {
    setFormData({
      ...formData,
      secure: value === 'true',
    });
  };

  // ฟังก์ชันตอนกดปุ่มบันทึก
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // ตรวจสอบว่าไม่ต้องส่งรหัสผ่านไปหากไม่ได้เปลี่ยนแปลง
    let dataToSend = { ...formData };
    const keepPasswordFlag = !dataToSend.auth.pass;  // ใช้ flag แทนหากไม่มีรหัสผ่านใหม่
    
    updateEmailSettings(dataToSend)
      .then(() => {
        // เคลียร์ฟอร์ม password หลังจากบันทึก
        setFormData({
          ...formData,
          auth: {
            ...formData.auth,
            pass: '',
          },
        });
      })
      .catch(() => {
        // จัดการข้อผิดพลาดถูกจัดการใน updateEmailSettings แล้ว
      });
  };

  // ฟังก์ชันตอนกดปุ่มทดสอบส่งอีเมล
  const handleTestEmail = (e: React.FormEvent) => {
    e.preventDefault();
    if (!testEmail) {
      toast({
        title: "กรุณาระบุอีเมล",
        description: "กรุณาระบุอีเมลที่ต้องการทดสอบ",
        variant: "destructive",
      });
      return;
    }
    
    testEmailSend(testEmail).catch(() => {
      // จัดการข้อผิดพลาดถูกจัดการใน testEmailSend แล้ว
    });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="max-w-3xl bg-gray-900/95 border-gray-800 text-white backdrop-blur-sm">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-blue-300 flex items-center gap-2">
            <Mail className="h-5 w-5" />
            ตั้งค่าการส่งอีเมล
          </DialogTitle>
          <DialogDescription className="text-gray-400">
            จัดการการตั้งค่าอีเมลสำหรับการส่งการแจ้งเตือนและรายงาน
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 my-2 max-h-[70vh] overflow-y-auto pr-2">
          <Tabs defaultValue="settings" value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="w-full bg-gray-800/50 border border-gray-800">
              <TabsTrigger value="settings" className="flex-1 data-[state=active]:bg-blue-900/40">
                ตั้งค่า SMTP
              </TabsTrigger>
              <TabsTrigger value="test" className="flex-1 data-[state=active]:bg-blue-900/40">
                ทดสอบส่งอีเมล
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="settings" className="mt-4">
              {isErrorSettings ? (
                <Alert variant="destructive" className="bg-red-900/20 border-red-800">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>เกิดข้อผิดพลาด</AlertTitle>
                  <AlertDescription>
                    {errorSettings instanceof Error ? errorSettings.message : 'ไม่สามารถดึงข้อมูลการตั้งค่าอีเมลได้'}
                  </AlertDescription>
                </Alert>
              ) : isLoadingSettings ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-6">
                  <Card className="bg-gray-900/60 border-gray-800">
                    <CardHeader className="bg-gradient-to-r from-blue-950/50 to-gray-900/50 border-b border-gray-800">
                      <CardTitle className="text-lg text-blue-300">ตั้งค่าการเชื่อมต่อ SMTP</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4 pt-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="service">บริการอีเมล</Label>
                          <Select 
                            value={formData.service}
                            onValueChange={(value) => setFormData({ ...formData, service: value })}
                          >
                            <SelectTrigger className="bg-gray-800 border-gray-700">
                              <SelectValue placeholder="เลือกบริการอีเมล" />
                            </SelectTrigger>
                            <SelectContent className="bg-gray-800 border-gray-700">
                              <SelectItem value="gmail">Gmail</SelectItem>
                              <SelectItem value="outlook">Outlook / Office 365</SelectItem>
                              <SelectItem value="yahoo">Yahoo Mail</SelectItem>
                              <SelectItem value="other">อื่นๆ</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="host">SMTP Host</Label>
                          <Input
                            id="host"
                            name="host"
                            value={formData.host}
                            onChange={handleChange}
                            placeholder="smtp.example.com"
                            className="bg-gray-800 border-gray-700"
                          />
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="port">SMTP Port</Label>
                          <Input
                            id="port"
                            name="port"
                            type="number"
                            value={formData.port.toString()}
                            onChange={handleChange}
                            placeholder="587"
                            className="bg-gray-800 border-gray-700"
                          />
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="secure">การเชื่อมต่อแบบปลอดภัย (SSL/TLS)</Label>
                          <Select 
                            value={formData.secure.toString()}
                            onValueChange={handleSecureChange}
                          >
                            <SelectTrigger className="bg-gray-800 border-gray-700">
                              <SelectValue placeholder="เลือกการตั้งค่าความปลอดภัย" />
                            </SelectTrigger>
                            <SelectContent className="bg-gray-800 border-gray-700">
                              <SelectItem value="true">เปิดใช้งาน (SSL/TLS)</SelectItem>
                              <SelectItem value="false">ไม่เปิดใช้งาน</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card className="bg-gray-900/60 border-gray-800">
                    <CardHeader className="bg-gradient-to-r from-blue-950/50 to-gray-900/50 border-b border-gray-800">
                      <CardTitle className="text-lg text-blue-300">ตั้งค่าการยืนยันตัวตน</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4 pt-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="auth.user">อีเมลผู้ใช้</Label>
                          <Input
                            id="auth.user"
                            name="auth.user"
                            value={formData.auth.user}
                            onChange={handleChange}
                            placeholder="<EMAIL>"
                            className="bg-gray-800 border-gray-700"
                          />
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="auth.pass">รหัสผ่าน / App Password</Label>
                          <Input
                            id="auth.pass"
                            name="auth.pass"
                            type="password"
                            value={formData.auth.pass}
                            onChange={handleChange}
                            placeholder={emailSettings?.auth?.hasPassword ? "••••••••• (ไม่เปลี่ยนแปลง)" : "กรอกรหัสผ่าน"}
                            className="bg-gray-800 border-gray-700"
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card className="bg-gray-900/60 border-gray-800">
                    <CardHeader className="bg-gradient-to-r from-blue-950/50 to-gray-900/50 border-b border-gray-800">
                      <CardTitle className="text-lg text-blue-300">ตั้งค่าผู้ส่ง</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4 pt-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="defaultName">ชื่อผู้ส่ง</Label>
                          <Input
                            id="defaultName"
                            name="defaultName"
                            value={formData.defaultName}
                            onChange={handleChange}
                            placeholder="SLIPKUY Notification"
                            className="bg-gray-800 border-gray-700"
                          />
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="from">อีเมลผู้ส่ง</Label>
                          <Input
                            id="from"
                            name="from"
                            value={formData.from}
                            onChange={handleChange}
                            placeholder="<EMAIL>"
                            className="bg-gray-800 border-gray-700"
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <div className="flex justify-end gap-3">
                    <Button 
                      variant="destructive" 
                      type="button"
                      onClick={() => setOpen(false)}
                    >
                      <X className="h-4 w-4 mr-1" />
                      ยกเลิก
                    </Button>
                    <Button 
                      type="submit" 
                      disabled={isSaving} 
                      className="bg-blue-600 hover:bg-blue-700 text-white"
                    >
                      {isSaving ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                          กำลังบันทึก...
                        </>
                      ) : (
                        <>
                          <Save className="h-4 w-4 mr-1" />
                          บันทึกการตั้งค่า
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              )}
            </TabsContent>
            
            <TabsContent value="test" className="mt-4">
              <Card className="bg-gray-900/60 border-gray-800">
                <CardHeader className="bg-gradient-to-r from-blue-950/50 to-gray-900/50 border-b border-gray-800">
                  <CardTitle className="text-lg text-blue-300">ทดสอบการส่งอีเมล</CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                  <div className="space-y-4">
                    <p className="text-gray-400 mb-4">
                      ทดสอบการตั้งค่า SMTP โดยส่งอีเมลทดสอบไปยังอีเมลที่ระบุ
                    </p>
                    
                    {!emailSettings?.auth?.user && (
                      <Alert variant="destructive" className="bg-red-900/20 border-red-800 mb-4">
                        <AlertCircle className="h-4 w-4" />
                        <AlertTitle>การตั้งค่าไม่สมบูรณ์</AlertTitle>
                        <AlertDescription>
                          กรุณาตั้งค่า SMTP และบันทึกก่อนทดสอบการส่งอีเมล
                        </AlertDescription>
                      </Alert>
                    )}
                    
                    <form onSubmit={handleTestEmail} className="space-y-4">
                      <div className="grid grid-cols-1 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="testEmail">อีเมลสำหรับทดสอบ</Label>
                          <div className="flex gap-2">
                            <Input
                              id="testEmail"
                              value={testEmail}
                              onChange={(e) => setTestEmail(e.target.value)}
                              placeholder="<EMAIL>"
                              className="bg-gray-800 border-gray-700 flex-grow"
                            />
                            <Button 
                              type="submit" 
                              disabled={isTesting || !emailSettings?.auth?.user} 
                              className="bg-blue-600 hover:bg-blue-700 text-white"
                            >
                              {isTesting ? (
                                <>
                                  <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                                  กำลังส่ง...
                                </>
                              ) : (
                                <>
                                  <Send className="h-4 w-4 mr-1" />
                                  ส่งอีเมลทดสอบ
                                </>
                              )}
                            </Button>
                          </div>
                        </div>
                      </div>
                    </form>
                    
                    {emailSettings?.isConfigured && (
                      <Alert className="bg-green-900/20 border-green-800 mt-4">
                        <Check className="h-4 w-4" />
                        <AlertTitle>การตั้งค่า SMTP สำเร็จ</AlertTitle>
                        <AlertDescription>
                          การตั้งค่า SMTP ของคุณถูกกำหนดค่าแล้วและพร้อมใช้งาน
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
        
        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={() => setOpen(false)}
            className="border-gray-700 hover:bg-gray-800"
          >
            ปิด
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}