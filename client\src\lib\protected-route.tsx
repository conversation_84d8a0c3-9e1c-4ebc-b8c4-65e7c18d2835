import { useAuth } from "@/hooks/use-auth";
import { Loader2 } from "lucide-react";
import { Route } from "wouter";
import { useEffect } from "react";

export function ProtectedRoute({
  path,
  component: Component,
}: {
  path: string;
  component: () => React.JSX.Element;
}) {
  const { user, isLoading } = useAuth();
  
  console.log(`ProtectedRoute for path ${path}:`, { user, isLoading });

  useEffect(() => {
    // ถ้าไม่กำลังโหลดและไม่มีผู้ใช้ ให้เปลี่ยนเส้นทางไปหน้าล็อกอิน
    if (!isLoading && !user) {
      console.log(`Redirecting from ${path} to /auth due to no user`);
      window.location.href = "/auth";
    }
    
    // ถ้าไม่กำลังโหลดและผู้ใช้ไม่ใช่แอดมินแต่พยายามเข้าถึงหน้าแอดมิน
    if (!isLoading && user && path.startsWith('/admin') && user.role !== 'admin') {
      console.log(`User is not admin, redirecting from ${path} to /dashboard`);
      window.location.href = "/dashboard";
    }
  }, [user, isLoading, path]);

  if (isLoading) {
    return (
      <Route path={path}>
        <div className="flex items-center justify-center min-h-screen">
          <Loader2 className="h-8 w-8 animate-spin text-border" />
        </div>
      </Route>
    );
  }

  // ถ้าไม่มีผู้ใช้ แสดงหน้าโหลด รอการเปลี่ยนเส้นทางใน useEffect
  if (!user) {
    return (
      <Route path={path}>
        <div className="flex items-center justify-center min-h-screen">
          <Loader2 className="h-8 w-8 animate-spin text-border" />
        </div>
      </Route>
    );
  }

  // สำหรับหน้าแอดมิน รอการเปลี่ยนเส้นทางใน useEffect
  if (path.startsWith('/admin') && user.role !== 'admin') {
    return (
      <Route path={path}>
        <div className="flex items-center justify-center min-h-screen">
          <Loader2 className="h-8 w-8 animate-spin text-border" />
        </div>
      </Route>
    );
  }

  return <Route path={path} component={Component} />;
}
