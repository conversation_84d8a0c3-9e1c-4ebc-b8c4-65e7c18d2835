import { ReactNode, useEffect, useState } from "react";
import { GodlyNavbar } from "./godly-navbar";
import { Footer } from "./footer";
import { Link, useLocation } from "wouter";
import { useAuth } from "@/hooks/use-auth";
import { motion, AnimatePresence } from "framer-motion";
import { 
  LayoutDashboard, 
  FileCheck, 
  History, 
  Package, 
  Settings, 
  Users,
  FileBarChart2,
  Ticket,
  Wallet,
  Shield,
  Zap,
  Sun,
  Moon,
  Cloud,
  Star,
  Sparkles,
  Crown,
  CrownIcon,
  MountainSnow,
  CloudLightning,
  CloudRainWind,
  Code,
  Terminal
} from "lucide-react";

interface DashboardLayoutProps {
  children: ReactNode;
}

// พาร์ติเคิลสำหรับสร้างจุดทองคำที่ลอยอยู่ในอากาศ
const GoldenParticles = ({ count = 30 }) => {
  const [particles, setParticles] = useState<Array<{
    id: number,
    x: number,
    y: number,
    size: number,
    speed: number,
    opacity: number
  }>>([]);
  
  useEffect(() => {
    const newParticles = Array.from({ length: count }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: Math.random() * 4 + 1,
      speed: Math.random() * 3 + 1,
      opacity: Math.random() * 0.5 + 0.3
    }));
    
    setParticles(newParticles);
  }, [count]);
  
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {particles.map((particle) => (
        <div
          key={particle.id}
          className="absolute rounded-full bg-amber-300"
          style={{
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            opacity: particle.opacity,
            boxShadow: `0 0 ${particle.size * 3}px ${particle.size}px rgba(251, 191, 36, 0.3)`,
            animation: `floatParticle ${particle.speed + 3}s ease-in-out infinite alternate, 
                        glowPulse ${particle.speed * 2}s ease-in-out infinite alternate`
          }}
        />
      ))}
    </div>
  );
};

// เมฆที่ลอยช้าๆ
const HeavenlyClouds = () => {
  return (
    <div className="absolute inset-0 pointer-events-none overflow-hidden">
      <div className="cloud cloud-1">
        <Cloud className="text-purple-100/20" size={180} />
      </div>
      <div className="cloud cloud-2">
        <Cloud className="text-indigo-100/10" size={240} />
      </div>
      <div className="cloud cloud-3">
        <CloudLightning className="text-amber-100/10" size={160} />
      </div>
      <div className="cloud cloud-4">
        <CloudRainWind className="text-blue-100/10" size={200} />
      </div>
    </div>
  );
};

// ไอคอนแบบเทพเจ้า
const DeityIcon = ({ icon: Icon, active, pulse = false }: { 
  icon: any, 
  active: boolean,
  pulse?: boolean
}) => {
  return (
    <div className={`relative flex items-center justify-center ${pulse ? 'animate-pulse' : ''}`}>
      <div className={`absolute h-10 w-10 rounded-full ${active ? 'bg-gradient-to-tr from-amber-300 to-amber-500' : 'bg-gradient-to-tr from-purple-900/50 to-indigo-800/70'} blur-[2px]`}></div>
      <div className={`absolute h-9 w-9 rounded-full ${active ? 'bg-gradient-to-br from-amber-200 to-orange-300 border border-amber-100/20' : 'bg-gradient-to-br from-indigo-900 to-purple-900 border border-indigo-500/20'} flex items-center justify-center overflow-hidden`}>
        <div className="absolute inset-0 w-full h-full">
          <div className={`w-full h-full ${active ? 'bg-gradient-radial from-yellow-200/70 via-amber-300/20 to-transparent' : 'bg-gradient-radial from-indigo-600/30 via-purple-700/20 to-transparent'}`}></div>
        </div>
      </div>
      <Icon className={`h-4 w-4 z-10 relative ${active ? 'text-white' : 'text-indigo-200'}`} />
      {active && (
        <div className="absolute h-12 w-12 rounded-full animate-ping-slow opacity-50 bg-amber-400/20"></div>
      )}
    </div>
  );
};

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const [location] = useLocation();
  const { user } = useAuth();
  const [hoveredLink, setHoveredLink] = useState<string | null>(null);
  
  const isAdmin = user?.role === 'admin';
  
  // ลิงก์เมนูทั่วไปสำหรับผู้ใช้งาน
  const userLinks = [
    { href: "/dashboard", label: "แดชบอร์ด", icon: LayoutDashboard, description: "ภาพรวมการใช้งานและสถิติ" },
    { href: "/verify", label: "ตรวจสอบสลิป", icon: FileCheck, description: "อัปโหลดและยืนยันสลิปธนาคาร" },
    { href: "/history", label: "ประวัติการตรวจสอบ", icon: History, description: "ดูประวัติการตรวจสอบทั้งหมด" },
    { href: "/user-packages", label: "แพ็กเกจ", icon: Package, description: "เลือกซื้อและจัดการแพ็กเกจ" },
    { href: "/topup", label: "เติมเงิน", icon: Wallet, description: "เติมเครดิตเข้าระบบ" },
    { href: "/api-keys", label: "API Keys", icon: Shield, description: "จัดการและดูประวัติการใช้งาน API" },
    { href: "/api-tester", label: "ทดสอบ API", icon: Terminal, description: "ทดสอบการใช้งาน API แบบรวดเร็ว" },
    { href: "/api-docs", label: "คู่มือการใช้งาน API", icon: Zap, description: "คำแนะนำและเอกสารการใช้งาน API" },
  ];
  
  // ลิงก์เมนูสำหรับแอดมิน
  const adminLinks = [
    { href: "/admin", label: "แดชบอร์ดแอดมิน", icon: LayoutDashboard, description: "ภาพรวมการใช้งานทั้งระบบ" },
    { href: "/admin/users", label: "จัดการผู้ใช้งาน", icon: Users, description: "จัดการบัญชีผู้ใช้ทั้งหมด" },
    { href: "/admin/packages", label: "จัดการแพ็กเกจ", icon: Package, description: "จัดการแพ็กเกจทั้งหมด" },
    { href: "/admin/coupons", label: "จัดการคูปอง", icon: Ticket, description: "จัดการคูปองส่วนลด" },
    { href: "/admin/stats", label: "รายงานและสถิติ", icon: FileBarChart2, description: "ดูรายงานสถิติเชิงลึก" },
    { href: "/admin/settings", label: "ตั้งค่าระบบ", icon: Settings, description: "ตั้งค่าการทำงานของระบบ" },
  ];
  
  // เลือกลิงก์ที่จะแสดงตามสิทธิ์การใช้งาน
  const links = location.startsWith('/admin') ? adminLinks : userLinks;
  
  // ไอคอนประจำตำแหน่งใน sidebar แบบทพเจ้า
  const deityIcons = {
    user: <Sun className="h-7 w-7 text-amber-300" />,
    admin: <Crown className="h-7 w-7 text-amber-300" />
  };

  // สร้าง Aura บนหัวข้อ
  const [isAuraActive, setIsAuraActive] = useState(true);
  
  // เอฟเฟกต์เพื่อทำให้ Aura เคลื่อนไหว
  useEffect(() => {
    const interval = setInterval(() => {
      setIsAuraActive(prev => !prev);
    }, 5000);
    
    return () => clearInterval(interval);
  }, []);
  
  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-indigo-950 via-indigo-900 to-purple-950 text-white">

      <div className="relative flex-1 overflow-hidden flex">
        {/* เอฟเฟกต์พื้นหลังแบบเทพเจ้า */}
        <div className="absolute inset-0 bg-gradient-radial from-indigo-800/10 via-purple-900/5 to-transparent z-0"></div>
        <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiMzMTJlODEiIGZpbGwtb3BhY2l0eT0iMC4wNSI+PHBhdGggZD0iTTM2IDM0aDR2MWgtNHYtMXptMC0xaDR2LTFoLTR2MXptMC00aDR2LTFoLTR2MXptLTYgOGg0di0xaC00djF6bTAtMWg0di0xaC00djF6bTAtNGg0di0xaC00djF6bS02IDhoNHYtMWgtNHYxem0wLTFoNHYtMWgtNHYxem0wLTRoNHYtMWgtNHYxem0wLTRoNHYtMWgtNHYxem0yNC00aDR2LTFoLTR2MXptLTQgOGg0di0xaC00djF6bTAtMWg0di0xaC00djF6bTAtNGg0di0xaC00djF6bTAtNGg0di0xaC00djF6Ii8+PC9nPjwvZz48L3N2Zz4=')] opacity-10 z-0"></div>
        
        {/* ทำให้มีอนุภาคทองคำลอยอยู่ - แสดงเอฟเฟกต์ด้านหน้า */}
        <div className="absolute inset-0 pointer-events-none z-20">
          <GoldenParticles count={40} />
          <HeavenlyClouds />
        </div>

        {/* Sidebar */}
        <motion.aside
          initial={{ x: -50, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ type: "spring", stiffness: 100, duration: 0.5 }}
          className="w-80 relative bg-gradient-to-br from-indigo-950/90 to-purple-950/90 backdrop-blur-md p-6 border-r border-indigo-800/30 h-full z-30"
        >
              <div className="flex items-center space-x-3 mb-8">
                <div className={`relative h-12 w-12 rounded-full flex items-center justify-center ${isAuraActive ? 'animate-pulse' : ''}`}>
                  {/* Aura effect */}
                  <div className="absolute inset-0 bg-gradient-radial from-amber-300/30 via-amber-500/10 to-transparent rounded-full blur-md"></div>
                  <div className="absolute inset-0 bg-gradient-to-br from-indigo-800 to-purple-800 rounded-full"></div>
                  {isAdmin ? deityIcons.admin : deityIcons.user}
                </div>
                <div>
                  <h2 className="text-xl font-bold tracking-tight">
                    <span className="text-white">{isAdmin ? 'แดชบอร์ดเทพเจ้า' : 'สรวงสวรรค์'}</span>
                  </h2>
                  <p className="text-indigo-300 text-sm">
                    {isAdmin ? 'พระผู้สร้างระบบทั้งมวล' : 'ผู้ใช้งานแห่งระบบ SlipKuy'}
                  </p>
                </div>
              </div>

              <div className="space-y-1.5">
                <AnimatePresence>
                  {links.map((link) => (
                    <motion.div 
                      key={link.href}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      transition={{ type: "spring", stiffness: 300, damping: 25 }}
                      className="relative"
                      onMouseEnter={() => setHoveredLink(link.href)}
                      onMouseLeave={() => setHoveredLink(null)}
                    >
                      <Link href={link.href}>
                        <a className="w-full block">
                          <div 
                            className={`flex items-center px-4 py-3 relative rounded-xl transition-all duration-300 z-10
                              ${location === link.href
                                ? "bg-gradient-to-r from-amber-700/40 to-amber-500/20 text-amber-300 shadow-lg shadow-amber-700/20"
                                : "hover:bg-indigo-800/20"
                            }`}
                          >
                            {/* ฉากหลังเมื่อ hover */}
                            {hoveredLink === link.href && location !== link.href && (
                              <motion.div 
                                layoutId="hoverBackground"
                                className="absolute inset-0 bg-gradient-to-r from-indigo-800/40 to-indigo-700/20 rounded-xl z-0"
                                animate={{ opacity: 1 }}
                                initial={{ opacity: 0 }}
                                exit={{ opacity: 0 }}
                                transition={{ duration: 0.2 }}
                              />
                            )}

                            {/* Icon */}
                            <div className="mr-3">
                              <DeityIcon 
                                icon={link.icon} 
                                active={location === link.href}
                                pulse={location === link.href}
                              />
                            </div>

                            {/* Label */}
                            <span className={`font-medium text-[15px] ${location === link.href ? 'text-amber-200' : 'text-indigo-100'}`}>
                              {link.label}
                            </span>

                            {/* Glow effect on active */}
                            {location === link.href && (
                              <motion.div 
                                className="absolute right-3 top-1/2 transform -translate-y-1/2"
                                animate={{ opacity: [0.6, 1, 0.6] }}
                                transition={{ repeat: Infinity, duration: 2 }}
                              >
                                <Sparkles className="h-4 w-4 text-amber-300" />
                              </motion.div>
                            )}
                          </div>
                        </a>
                      </Link>

                      {/* คำอธิบายเมื่อ hover */}
                      <AnimatePresence>
                        {hoveredLink === link.href && (
                          <motion.div
                            className="absolute left-full ml-3 top-0 z-50 bg-indigo-900/95 backdrop-blur-sm text-white p-3 rounded-xl shadow-xl border border-indigo-500/20 w-60"
                            initial={{ opacity: 0, x: -5 }}
                            animate={{ opacity: 1, x: 0 }}
                            exit={{ opacity: 0, x: -5 }}
                            transition={{ duration: 0.2 }}
                          >
                            <div className="absolute -left-2 top-5 w-0 h-0 border-t-[8px] border-t-transparent border-r-[8px] border-r-indigo-900/95 border-b-[8px] border-b-transparent"></div>
                            <div className="font-semibold text-amber-200 mb-1 flex items-center">
                              <link.icon className="h-4 w-4 mr-2 text-amber-400" />
                              {link.label}
                            </div>
                            <p className="text-xs text-indigo-200">{link.description}</p>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </motion.div>
                  ))}
                </AnimatePresence>

                {/* แสดงลิงก์ไปยังแดชบอร์ดแอดมินเมื่อผู้ใช้เป็นแอดมิน */}
                {isAdmin && !location.startsWith('/admin') && (
                  <>
                    <div className="relative py-2 my-2">
                      <div className="absolute inset-0 h-px w-full bg-gradient-to-r from-transparent via-indigo-500/30 to-transparent"></div>
                    </div>
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                      onMouseEnter={() => setHoveredLink('/admin')}
                      onMouseLeave={() => setHoveredLink(null)}
                      className="relative"
                    >
                      <Link href="/admin">
                        <a className="flex items-center px-4 py-3 rounded-xl hover:bg-indigo-800/20 transition-all duration-300">
                          <div className="mr-3">
                            <DeityIcon 
                              icon={Crown} 
                              active={false}
                            />
                          </div>
                          <span className="font-medium text-indigo-100">เทพพระเจ้า</span>
                        </a>
                      </Link>
                      {hoveredLink === '/admin' && (
                        <motion.div
                          className="absolute left-full ml-3 top-0 z-50 bg-indigo-900/95 backdrop-blur-sm text-white p-3 rounded-xl shadow-xl border border-indigo-500/20 w-60"
                          initial={{ opacity: 0, x: -5 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: -5 }}
                          transition={{ duration: 0.2 }}
                        >
                          <div className="absolute -left-2 top-5 w-0 h-0 border-t-[8px] border-t-transparent border-r-[8px] border-r-indigo-900/95 border-b-[8px] border-b-transparent"></div>
                          <div className="font-semibold text-amber-200 mb-1 flex items-center">
                            <Crown className="h-4 w-4 mr-2 text-amber-400" />
                            แดชบอร์ดเทพเจ้า
                          </div>
                          <p className="text-xs text-indigo-200">เข้าสู่ดินแดนแห่งการควบคุมระบบทั้งมวล</p>
                        </motion.div>
                      )}
                    </motion.div>
                  </>
                )}

                {/* แสดงลิงก์ไปยังแดชบอร์ดผู้ใช้เมื่อผู้ใช้อยู่ในหน้าแอดมิน */}
                {isAdmin && location.startsWith('/admin') && (
                  <>
                    <div className="relative py-2 my-2">
                      <div className="absolute inset-0 h-px w-full bg-gradient-to-r from-transparent via-indigo-500/30 to-transparent"></div>
                    </div>
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                      onMouseEnter={() => setHoveredLink('/dashboard')}
                      onMouseLeave={() => setHoveredLink(null)}
                      className="relative"
                    >
                      <Link href="/dashboard">
                        <a className="flex items-center px-4 py-3 rounded-xl hover:bg-indigo-800/20 transition-all duration-300">
                          <div className="mr-3">
                            <DeityIcon 
                              icon={LayoutDashboard} 
                              active={false}
                            />
                          </div>
                          <span className="font-medium text-indigo-100">โลกแห่งมนุษย์</span>
                        </a>
                      </Link>
                      {hoveredLink === '/dashboard' && (
                        <motion.div
                          className="absolute left-full ml-3 top-0 z-50 bg-indigo-900/95 backdrop-blur-sm text-white p-3 rounded-xl shadow-xl border border-indigo-500/20 w-60"
                          initial={{ opacity: 0, x: -5 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: -5 }}
                          transition={{ duration: 0.2 }}
                        >
                          <div className="absolute -left-2 top-5 w-0 h-0 border-t-[8px] border-t-transparent border-r-[8px] border-r-indigo-900/95 border-b-[8px] border-b-transparent"></div>
                          <div className="font-semibold text-amber-200 mb-1 flex items-center">
                            <LayoutDashboard className="h-4 w-4 mr-2 text-amber-400" />
                            แดชบอร์ดผู้ใช้
                          </div>
                          <p className="text-xs text-indigo-200">กลับไปยังมุมมองของผู้ใช้ทั่วไป</p>
                        </motion.div>
                      )}
                    </motion.div>
                  </>
                )}
              </div>
            </motion.aside>
            
            {/* Main Content */}
            <motion.main 
              className="flex-1 relative z-30 p-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ type: "spring", stiffness: 100, damping: 20, delay: 0.2 }}
            >
              <div className="bg-gradient-to-br from-indigo-950/70 to-purple-950/80 backdrop-blur-md p-6 h-full rounded-2xl border border-indigo-800/30 shadow-xl">
                {children}
              </div>
            </motion.main>
          </div>
        </div>
      </div>
      
      <Footer />

      {/* กำหนด CSS สำหรับแอนิเมชันและเอฟเฟกต์อื่นๆ */}
      <style>{`
        @keyframes floatParticle {
          0% { transform: translateY(0) rotate(0deg); }
          100% { transform: translateY(-10px) rotate(5deg); }
        }
        
        @keyframes glowPulse {
          0% { opacity: 0.2; }
          100% { opacity: 0.6; }
        }
        
        @keyframes ping-slow {
          0% {
            transform: scale(1);
            opacity: 0.8;
          }
          100% {
            transform: scale(1.5);
            opacity: 0;
          }
        }
        
        .cloud {
          position: absolute;
          opacity: 0.5;
          animation-name: float;
          animation-timing-function: ease-in-out;
          animation-iteration-count: infinite;
          animation-direction: alternate;
        }
        
        .cloud-1 {
          top: 15%;
          left: 10%;
          animation-duration: 45s;
        }
        
        .cloud-2 {
          top: 35%;
          right: 15%;
          animation-duration: 60s;
        }
        
        .cloud-3 {
          bottom: 20%;
          left: 25%;
          animation-duration: 30s;
        }
        
        .cloud-4 {
          bottom: 40%;
          right: 30%;
          animation-duration: 52s;
        }
        
        @keyframes float {
          0% { transform: translateX(0) translateY(0); }
          50% { transform: translateX(30px) translateY(-10px); }
          100% { transform: translateX(60px) translateY(0); }
        }
      `}</style>
    </div>
  );
}
