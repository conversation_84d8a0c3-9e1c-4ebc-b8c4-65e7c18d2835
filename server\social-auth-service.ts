import { 
  externalAuth, 
  authProviderEnum,
  systemSettings,
  users 
} from "@shared/schema";
import { db } from "./db";
import { eq, desc, sql } from "drizzle-orm";
import { logger } from "./logger";
import { storage } from "./storage";

/**
 * บริการจัดการการล็อกอินผ่านโซเชียลมีเดีย
 */
export class SocialAuthService {
  /**
   * ดึงการตั้งค่าการล็อกอินผ่านโซเชียลมีเดีย
   * @returns Promise<object> การตั้งค่าการล็อกอินผ่านโซเชียลมีเดีย
   */
  async getSocialLoginSettings(): Promise<{
    enableLineLogin: boolean;
    enableFacebookLogin: boolean;
    enableGoogleLogin: boolean;
    lineClientId?: string;
    lineClientSecret?: string;
    facebookAppId?: string;
    facebookAppSecret?: string;
    googleClientId?: string;
    googleClientSecret?: string;
    lineCallbackUrl?: string;
    facebookCallbackUrl?: string;
    googleCallbackUrl?: string;
  }> {
    try {
      const [settingsRecord] = await db
        .select()
        .from(systemSettings)
        .where(eq(systemSettings.key, 'auth_settings'));

      // ถ้าไม่มีค่าในฐานข้อมูล ให้ส่งค่าเริ่มต้นกลับไป
      if (!settingsRecord || !settingsRecord.value) {
        return {
          enableLineLogin: false,
          enableFacebookLogin: false,
          enableGoogleLogin: false
        };
      }

      const settings = JSON.parse(settingsRecord.value as string);
      
      return {
        enableLineLogin: settings.socialLogin?.enableLineLogin === true,
        enableFacebookLogin: settings.socialLogin?.enableFacebookLogin === true,
        enableGoogleLogin: settings.socialLogin?.enableGoogleLogin === true,
        
        // ส่งค่า credentials กลับไปเฉพาะกรณีขอจาก API ภายใน
        lineClientId: settings.socialLogin?.lineClientId,
        lineClientSecret: settings.socialLogin?.lineClientSecret,
        facebookAppId: settings.socialLogin?.facebookAppId,
        facebookAppSecret: settings.socialLogin?.facebookAppSecret,
        googleClientId: settings.socialLogin?.googleClientId,
        googleClientSecret: settings.socialLogin?.googleClientSecret,
        
        // URLs
        lineCallbackUrl: settings.socialLogin?.lineCallbackUrl,
        facebookCallbackUrl: settings.socialLogin?.facebookCallbackUrl,
        googleCallbackUrl: settings.socialLogin?.googleCallbackUrl
      };
    } catch (error) {
      logger.error('เกิดข้อผิดพลาดในการดึงการตั้งค่า Social Login', error);
      return {
        enableLineLogin: false,
        enableFacebookLogin: false,
        enableGoogleLogin: false
      };
    }
  }

  /**
   * บันทึกข้อมูลการล็อกอินผ่านโซเชียลมีเดียสำหรับผู้ใช้
   * @param userId รหัสผู้ใช้
   * @param provider ผู้ให้บริการ (line, facebook, google)
   * @param providerId รหัสผู้ใช้จากผู้ให้บริการ
   * @param profileData ข้อมูลโปรไฟล์จากผู้ให้บริการ
   * @returns Promise<object> ข้อมูลการล็อกอินที่บันทึกแล้ว
   */
  async saveExternalAuth(
    userId: number,
    provider: string,
    providerId: string,
    profileData: any
  ): Promise<any> {
    try {
      // ตรวจสอบว่ามีข้อมูลอยู่แล้วหรือไม่
      const [existingAuth] = await db
        .select()
        .from(externalAuth)
        .where(
          sql`${externalAuth.userId} = ${userId} AND ${externalAuth.provider} = ${provider}`
        );

      // ถ้ามีข้อมูลอยู่แล้ว ให้อัปเดต
      if (existingAuth) {
        const [updatedAuth] = await db
          .update(externalAuth)
          .set({
            lastLogin: new Date(),
            profileData: profileData,
            providerId: providerId,
            updatedAt: new Date()
          })
          .where(
            sql`${externalAuth.userId} = ${userId} AND ${externalAuth.provider} = ${provider}`
          )
          .returning();
        
        return updatedAuth;
      } 
      
      // ถ้าไม่มีข้อมูล ให้เพิ่มข้อมูลใหม่
      const [newAuth] = await db
        .insert(externalAuth)
        .values({
          userId: userId,
          provider: provider as any, // Type assertion
          providerId: providerId,
          profileData: profileData,
          createdAt: new Date(),
          updatedAt: new Date(),
          lastLogin: new Date()
        })
        .returning();
      
      return newAuth;
    } catch (error) {
      logger.error('เกิดข้อผิดพลาดในการบันทึกข้อมูลการล็อกอินโซเชียลมีเดีย', error);
      throw error;
    }
  }

  /**
   * ค้นหาผู้ใช้จากข้อมูลการล็อกอินโซเชียลมีเดีย
   * @param provider ผู้ให้บริการ (line, facebook, google)
   * @param providerId รหัสผู้ใช้จากผู้ให้บริการ
   * @returns Promise<object | null> ข้อมูลผู้ใช้หรือ null ถ้าไม่พบ
   */
  async findUserByExternalAuth(
    provider: string,
    providerId: string
  ): Promise<any> {
    try {
      // ค้นหา external auth record
      const [authRecord] = await db
        .select()
        .from(externalAuth)
        .where(
          sql`${externalAuth.provider} = ${provider} AND ${externalAuth.providerId} = ${providerId}`
        );

      if (!authRecord) {
        return null;
      }

      // ค้นหาข้อมูลผู้ใช้
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, authRecord.userId));

      return user;
    } catch (error) {
      logger.error('เกิดข้อผิดพลาดในการค้นหาผู้ใช้จากข้อมูลการล็อกอินโซเชียลมีเดีย', error);
      return null;
    }
  }
}

export const socialAuthService = new SocialAuthService();