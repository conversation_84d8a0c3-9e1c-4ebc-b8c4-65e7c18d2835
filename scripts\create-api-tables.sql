-- สร้าง enum types
CREATE TYPE api_key_status AS ENUM ('active', 'inactive', 'revoked');
CREATE TYPE api_request_type AS ENUM ('verify_slip', 'get_usage', 'get_history');
CREATE TYPE api_response_status AS ENUM ('success', 'error', 'invalid_request', 'quota_exceeded', 'unauthorized');

-- สร้างตาราง api_keys
CREATE TABLE IF NOT EXISTS api_keys (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL REFERENCES users(id),
  api_key VARCHAR(64) NOT NULL UNIQUE,
  name TEXT NOT NULL,
  description TEXT,
  status api_key_status NOT NULL DEFAULT 'active',
  last_used TIMESTAMP,
  request_count INTEGER NOT NULL DEFAULT 0,
  ip_whitelist TEXT[],
  expires_at TIMESTAMP,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- สร้างตาราง api_logs
CREATE TABLE IF NOT EXISTS api_logs (
  id SERIAL PRIMARY KEY,
  api_key_id INTEGER NOT NULL REFERENCES api_keys(id),
  request_type api_request_type NOT NULL,
  request_data JSONB,
  response_status api_response_status NOT NULL,
  response_data JSONB,
  slip_verification_id INTEGER REFERENCES slip_verifications(id),
  ip_address TEXT NOT NULL,
  user_agent TEXT,
  processing_time INTEGER NOT NULL,
  error_message TEXT,
  created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- สร้างตาราง api_response_templates
CREATE TABLE IF NOT EXISTS api_response_templates (
  id SERIAL PRIMARY KEY,
  status_code INTEGER NOT NULL,
  status_type TEXT NOT NULL,
  message TEXT NOT NULL,
  description TEXT,
  template JSONB NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- สร้าง indexes เพื่อเพิ่มประสิทธิภาพการค้นหา
CREATE INDEX IF NOT EXISTS idx_api_keys_user_id ON api_keys(user_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_api_key ON api_keys(api_key);
CREATE INDEX IF NOT EXISTS idx_api_logs_api_key_id ON api_logs(api_key_id);
CREATE INDEX IF NOT EXISTS idx_api_logs_slip_verification_id ON api_logs(slip_verification_id);
CREATE INDEX IF NOT EXISTS idx_api_logs_created_at ON api_logs(created_at);