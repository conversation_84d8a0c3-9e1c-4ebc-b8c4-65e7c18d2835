import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { format } from "date-fns";
import { th } from "date-fns/locale";
import { Admin } from "@/components/layouts/admin-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { apiRequest } from "@/lib/queryClient";
import { formatDate } from "@/lib/utils";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Package,
  Key,
  Search,
  RefreshCw,
  Plus,
  Calendar,
  Clock,
  BarChart,
  History,
  Shield,
  Check,
  X,
  Settings,
  Trash2,
  AlarmClock,
  User,
  Filter,
  Sparkles,
  Ban,
  CheckCircle,
  AlertTriangle,
} from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
// import { DatePicker } from "@/components/ui/date-picker";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";

// ประเภทข้อมูลผู้ใช้
type UserType = {
  id: number;
  username: string;
  email: string;
  firstName?: string;
  lastName?: string;
  companyName?: string;
  role: string;
  status: string;
  credit: number;
  createdAt: string;
  updatedAt: string;
};

// ประเภทข้อมูลแพ็คเกจ
type PackageType = {
  id: number;
  name: string;
  description: string;
  price: number;
  durationDays: number;
  requestsLimit: number;
  isActive: boolean;
  features: string[];
  createdAt: string;
  updatedAt: string;
};

// ประเภทข้อมูลแพ็คเกจของผู้ใช้
type UserPackageType = {
  id: number;
  userId: number;
  packageId: number;
  startDate: string;
  endDate: string;
  isActive: boolean;
  requestsUsed: number;
  createdAt: string;
  updatedAt: string;
  package: PackageType;
};

// ประเภทข้อมูล API Key
type ApiKeyType = {
  id: number;
  userId: number;
  name: string;
  description?: string;
  apiKey?: string;
  status: string;
  usageLimit?: number;
  usageCount?: number;
  ipWhitelist?: string[];
  expiresAt?: string;
  createdAt: string;
  updatedAt: string;
};

// สร้าง Schema สำหรับเพิ่มแพ็คเกจ
const addPackageSchema = z.object({
  packageId: z.number().min(1, "กรุณาเลือกแพ็คเกจ"),
  durationMonths: z.number().min(1, "ระยะเวลาต้องไม่น้อยกว่า 1 เดือน"),
  isActive: z.boolean().default(true),
});

type AddPackageFormValues = z.infer<typeof addPackageSchema>;

// สร้าง Schema สำหรับเพิ่ม API Key
const addApiKeySchema = z.object({
  name: z.string().min(1, "กรุณาระบุชื่อ API Key"),
  description: z.string().optional(),
  usageLimit: z.preprocess(
    (val) => (val === "" ? undefined : Number(val)),
    z.number().min(0, "จำนวนการใช้งานต้องไม่น้อยกว่า 0").optional()
  ),
  ipWhitelist: z.string().optional(),
  expiryDate: z.date().optional().nullable(),
});

type AddApiKeyFormValues = z.infer<typeof addApiKeySchema>;

// หน้าจัดการแพคเกจและ API Keys ของผู้ใช้
export default function UserPackagesApiManagement() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState("packages");
  const [search, setSearch] = useState("");
  const [selectedUser, setSelectedUser] = useState<UserType | null>(null);
  const [userPackages, setUserPackages] = useState<UserPackageType[]>([]);
  const [userApiKeys, setUserApiKeys] = useState<ApiKeyType[]>([]);
  const [statusFilter, setStatusFilter] = useState("all");
  const [roleFilter, setRoleFilter] = useState("all");
  const [isResetDialogOpen, setIsResetDialogOpen] = useState(false);
  const [packageToReset, setPackageToReset] = useState<number | null>(null);
  const [resetConfirmText, setResetConfirmText] = useState("");

  // ฟอร์มสำหรับเพิ่ม API key
  const addApiKeyForm = useForm<AddApiKeyFormValues>({
    resolver: zodResolver(addApiKeySchema),
    defaultValues: {
      name: "",
      description: "",
      usageLimit: undefined,
      ipWhitelist: "",
      expiryDate: null
    }
  });

  // ฟอร์มสำหรับเพิ่มแพ็คเกจ
  const addPackageForm = useForm<AddPackageFormValues>({
    resolver: zodResolver(addPackageSchema),
    defaultValues: {
      packageId: 0,
      durationMonths: 1,
      isActive: true
    }
  });

  // ดึงข้อมูลผู้ใช้ทั้งหมด
  const { data: users, isLoading, refetch } = useQuery<UserType[]>({
    queryKey: ['/api/admin/users'],
  });

  // ดึงข้อมูลแพ็คเกจทั้งหมด
  const { data: packagesList } = useQuery<PackageType[]>({
    queryKey: ['/api/packages'],
  });

  // กรองข้อมูลผู้ใช้งานตามเงื่อนไขค้นหาและสถานะ
  const filteredUsers = users?.filter((user) => {
    // กรองตามข้อความค้นหา
    const searchMatch =
      search === "" ||
      user.username.toLowerCase().includes(search.toLowerCase()) ||
      user.email.toLowerCase().includes(search.toLowerCase()) ||
      user.firstName?.toLowerCase().includes(search.toLowerCase()) ||
      user.lastName?.toLowerCase().includes(search.toLowerCase()) ||
      user.companyName?.toLowerCase().includes(search.toLowerCase());

    // กรองตามสถานะ
    const statusMatch = statusFilter === "all" || user.status === statusFilter;

    // กรองตามบทบาท
    const roleMatch = roleFilter === "all" || user.role === roleFilter;

    return searchMatch && statusMatch && roleMatch;
  });

  // Mutation สำหรับเพิ่มแพ็คเกจให้ผู้ใช้
  const addPackageMutation = useMutation({
    mutationFn: async (data: AddPackageFormValues) => {
      const res = await apiRequest("POST", `/api/admin/users/${selectedUser?.id}/packages`, data);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "เพิ่มแพ็คเกจสำเร็จ",
        description: "เพิ่มแพ็คเกจให้ผู้ใช้งานเรียบร้อยแล้ว",
      });
      // รีเฟรชข้อมูลแพ็คเกจของผู้ใช้
      fetchUserPackages(selectedUser!.id);
      addPackageForm.reset();
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับสร้าง API key ใหม่
  const addApiKeyMutation = useMutation({
    mutationFn: async (data: AddApiKeyFormValues) => {
      // แปลง ipWhitelist จาก string เป็น array ถ้ามีข้อมูล
      const ipWhitelistArray = data.ipWhitelist ? data.ipWhitelist.split(',').map(ip => ip.trim()) : undefined;

      // แปลง expiryDate เป็น string หรือ null
      const expiresAt = data.expiryDate ? data.expiryDate.toISOString() : null;

      // สร้าง payload ที่ถูกต้องตามที่ API ต้องการ
      const payload = {
        name: data.name,
        description: data.description || null,
        status: 'active',
        ipWhitelist: ipWhitelistArray,
        expiresAt: expiresAt,
        limitEnabled: data.usageLimit ? true : false,
        usageLimit: data.usageLimit || null,
        duplicateSlipCheck: true
      };

      // ใช้ endpoint สำหรับ admin เพื่อสร้าง API key ให้ผู้ใช้ที่เลือก
      const res = await apiRequest("POST", `/api/admin/users/${selectedUser?.id}/api-keys`, payload);
      return await res.json();
    },
    onSuccess: (data) => {
      toast({
        title: "สร้าง API Key สำเร็จ",
        description: "API Key ใหม่ถูกสร้างขึ้นเรียบร้อยแล้ว",
      });
      // แสดง API Key ที่สร้างใหม่
      toast({
        title: "API Key ใหม่ของคุณ",
        description: (
          <div className="mt-2 p-2 bg-black/5 rounded font-mono text-xs break-all">
            {data.apiKey}
          </div>
        ),
        duration: 10000,
      });
      // รีเฟรชข้อมูล API keys ของผู้ใช้
      fetchUserApiKeys(selectedUser!.id);
      addApiKeyForm.reset();
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับลบ API key
  const deleteApiKeyMutation = useMutation({
    mutationFn: async (apiKeyId: number) => {
      const res = await apiRequest("DELETE", `/api/admin/users/${selectedUser?.id}/api-keys/${apiKeyId}`, null);
      return res;
    },
    onSuccess: () => {
      toast({
        title: "ลบ API Key สำเร็จ",
        description: "API Key ถูกลบเรียบร้อยแล้ว",
      });
      // รีเฟรชข้อมูล API keys ของผู้ใช้
      fetchUserApiKeys(selectedUser!.id);
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับระงับการใช้งาน API key
  const suspendApiKeyMutation = useMutation({
    mutationFn: async (apiKeyId: number) => {
      const res = await apiRequest("PATCH", `/api/admin/users/${selectedUser?.id}/api-keys/${apiKeyId}`, { status: 'revoked' });
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "ระงับการใช้งาน API Key สำเร็จ",
        description: "API Key ถูกระงับการใช้งานเรียบร้อยแล้ว",
      });
      // รีเฟรชข้อมูล API keys ของผู้ใช้
      fetchUserApiKeys(selectedUser!.id);
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับเปิดใช้งาน API key
  const activateApiKeyMutation = useMutation({
    mutationFn: async (apiKeyId: number) => {
      const res = await apiRequest("PATCH", `/api/admin/users/${selectedUser?.id}/api-keys/${apiKeyId}`, { status: 'active' });
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "เปิดใช้งาน API Key สำเร็จ",
        description: "API Key ถูกเปิดใช้งานเรียบร้อยแล้ว",
      });
      // รีเฟรชข้อมูล API keys ของผู้ใช้
      fetchUserApiKeys(selectedUser!.id);
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับรีเจนเนอเรท API key
  const regenerateApiKeyMutation = useMutation({
    mutationFn: async (apiKeyId: number) => {
      const res = await apiRequest("POST", `/api/admin/users/${selectedUser?.id}/api-keys/${apiKeyId}/regenerate`, {});
      return await res.json();
    },
    onSuccess: (data) => {
      toast({
        title: "รีเจนเนอเรท API Key สำเร็จ",
        description: "API Key ถูกสร้างใหม่เรียบร้อยแล้ว",
      });
      // แสดง API Key ที่สร้างใหม่
      toast({
        title: "API Key ใหม่ของคุณ",
        description: (
          <div className="mt-2 p-2 bg-black/5 rounded font-mono text-xs break-all">
            {data.apiKey}
          </div>
        ),
        duration: 10000,
      });
      // รีเฟรชข้อมูล API keys ของผู้ใช้
      fetchUserApiKeys(selectedUser!.id);
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับรีเซ็ตยอดการใช้งานแพ็คเกจ
  const resetPackageUsageMutation = useMutation({
    mutationFn: async (packageId: number) => {
      const res = await apiRequest("POST", `/api/admin/users/${selectedUser?.id}/packages/${packageId}/reset-quota`);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "รีเซ็ตยอดการใช้งานสำเร็จ",
        description: "รีเซ็ตยอดการใช้งานแพ็คเกจเรียบร้อยแล้ว",
      });
      // รีเฟรชข้อมูลแพ็คเกจของผู้ใช้
      fetchUserPackages(selectedUser!.id);
      // ปิด Dialog และรีเซ็ตค่า
      setIsResetDialogOpen(false);
      setPackageToReset(null);
      setResetConfirmText("");
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // เปิด Dialog ยืนยันการรีเซ็ตยอดการใช้งาน
  const handleOpenResetDialog = (packageId: number) => {
    setPackageToReset(packageId);
    setResetConfirmText("");
    setIsResetDialogOpen(true);
  };

  // ยืนยันการรีเซ็ตยอดการใช้งาน
  const handleConfirmReset = () => {
    if (packageToReset !== null && resetConfirmText === "รีเซ็ทการใช้งาน") {
      resetPackageUsageMutation.mutate(packageToReset);
    } else {
      toast({
        title: "ไม่สามารถรีเซ็ตได้",
        description: "กรุณาพิมพ์คำยืนยัน 'รีเซ็ทการใช้งาน' ให้ถูกต้อง",
        variant: "destructive"
      });
    }
  };

  // Mutation สำหรับเปลี่ยนสถานะแพ็คเกจ (active/inactive)
  const togglePackageStatusMutation = useMutation({
    mutationFn: async ({ packageId, isActive }: { packageId: number; isActive: boolean }) => {
      const res = await apiRequest("PATCH", `/api/admin/users/${selectedUser?.id}/packages/${packageId}`, { isActive });
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "อัปเดตสถานะแพ็คเกจสำเร็จ",
        description: "อัปเดตสถานะแพ็คเกจเรียบร้อยแล้ว",
      });
      // รีเฟรชข้อมูลแพ็คเกจของผู้ใช้
      fetchUserPackages(selectedUser!.id);
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับขยายวันหมดอายุแพ็คเกจ
  const extendPackageExpiryMutation = useMutation({
    mutationFn: async ({ packageId, expiryDate }: { packageId: number; expiryDate: string }) => {
      const res = await apiRequest("PATCH", `/api/admin/users/${selectedUser?.id}/packages/${packageId}`, { expiryDate });
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "ขยายวันหมดอายุสำเร็จ",
        description: "ขยายวันหมดอายุแพ็คเกจเรียบร้อยแล้ว",
      });
      // รีเฟรชข้อมูลแพ็คเกจของผู้ใช้
      fetchUserPackages(selectedUser!.id);
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // ฟังก์ชันดึงข้อมูลแพ็คเกจของผู้ใช้
  const fetchUserPackages = async (userId: number) => {
    try {
      const res = await apiRequest("GET", `/api/admin/users/${userId}/packages`, null);
      const data = await res.json();
      setUserPackages(data);
    } catch (error) {
      console.error("Error fetching user packages:", error);
    }
  };

  // ฟังก์ชันดึงข้อมูล API keys ของผู้ใช้
  const fetchUserApiKeys = async (userId: number) => {
    try {
      // ใช้ userId ในการดึงข้อมูล API keys ของผู้ใช้ที่เลือก
      const res = await apiRequest("GET", `/api/admin/users/${userId}/api-keys`, null);
      const data = await res.json();
      setUserApiKeys(data);
      console.log(`Fetched API keys for user ${userId}:`, data);
    } catch (error) {
      console.error("Error fetching user API keys:", error);
    }
  };

  // เลือกผู้ใช้และดึงข้อมูลแพ็คเกจและ API keys
  const handleSelectUser = async (user: UserType) => {
    setSelectedUser(user);
    await fetchUserPackages(user.id);
    await fetchUserApiKeys(user.id);
  };

  // จัดการแสดงสถานะผู้ใช้งานด้วยสีและสัญลักษณ์
  const renderUserStatus = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-50 text-green-700 border-green-200">ใช้งาน</Badge>;
      case "inactive":
        return <Badge className="bg-yellow-50 text-yellow-700 border-yellow-200">ไม่ได้ใช้งาน</Badge>;
      case "suspended":
        return <Badge className="bg-red-50 text-red-700 border-red-200">ระงับการใช้งาน</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // จัดการแสดงบทบาทผู้ใช้งาน
  const renderUserRole = (role: string) => {
    switch (role) {
      case "admin":
        return <Badge className="bg-purple-50 text-purple-700 border-purple-200">ผู้ดูแลระบบ</Badge>;
      case "user":
        return <Badge className="bg-blue-50 text-blue-700 border-blue-200">ผู้ใช้งานทั่วไป</Badge>;
      default:
        return <Badge variant="outline">{role}</Badge>;
    }
  };

  return (
    <Admin>
      <div className="space-y-8">
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 rounded-lg blur-xl -z-10 opacity-70"></div>
          <div className="relative z-10 p-4 rounded-lg border border-indigo-200/30">
            <h1 className="text-2xl font-bold tracking-tight divine-text">จัดการแพคเกจและ API Keys</h1>
            <div className="lightning-bar w-32 my-2"></div>
            <p className="text-muted-foreground">
              จัดการแพคเกจและ API Keys ของผู้ใช้งานทั้งหมดในระบบ
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
          {/* ส่วนแสดงรายชื่อผู้ใช้ */}
          <Card className="border border-indigo-200/30 lg:col-span-4">
            <CardHeader className="bg-gradient-to-r from-white to-indigo-50/30">
              <CardTitle className="text-indigo-700 flex items-center">
                <User className="h-5 w-5 mr-2 text-amber-500" />
                <span>รายชื่อผู้ใช้งาน</span>
              </CardTitle>
              <CardDescription className="text-indigo-600/70">
                เลือกผู้ใช้งานเพื่อจัดการแพคเกจและ API Keys
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* ส่วนค้นหาและกรอง */}
                <div className="flex flex-col gap-4 p-4 rounded-lg bg-gradient-to-r from-indigo-500/80 to-purple-600/80 border border-indigo-300/50">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-white" />
                    <Input
                      placeholder="ค้นหาผู้ใช้งาน..."
                      className="pl-10 border-indigo-200/50 focus-visible:ring-indigo-400"
                      value={search}
                      onChange={(e) => setSearch(e.target.value)}
                    />
                  </div>
                  <div className="flex flex-wrap gap-2 text-white">
                    <Select
                      value={statusFilter}
                      onValueChange={setStatusFilter}
                    >
                      <SelectTrigger className="w-[140px] border-indigo-200/50 focus-visible:ring-indigo-400 bg-indigo-500/80 text-white">
                        <SelectValue placeholder="สถานะ" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">ทั้งหมด</SelectItem>
                        <SelectItem value="active">ใช้งาน</SelectItem>
                        <SelectItem value="inactive">ไม่ได้ใช้งาน</SelectItem>
                        <SelectItem value="suspended">ระงับการใช้งาน</SelectItem>
                      </SelectContent>
                    </Select>
                    <Select
                      value={roleFilter}
                      onValueChange={setRoleFilter}
                    >
                      <SelectTrigger className="w-[140px] border-indigo-200/50 focus-visible:ring-indigo-400 bg-indigo-500/80 text-white">
                        <SelectValue placeholder="บทบาท" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">ทั้งหมด</SelectItem>
                        <SelectItem value="user">ผู้ใช้งานทั่วไป</SelectItem>
                        <SelectItem value="admin">ผู้ดูแลระบบ</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button
                      variant="outline"
                      className="h-10 px-4 py-2 bg-gradient-to-r from-indigo-500/80 to-purple-600/80 text-white hover:from-indigo-600/80 hover:to-purple-700/80 border-indigo-300/50"
                      onClick={() => refetch()}
                    >
                      <RefreshCw className="h-4 w-4 mr-2" />
                      รีเฟรช
                    </Button>
                  </div>
                </div>

                {/* รายการผู้ใช้งาน */}
                {isLoading ? (
                  <div className="space-y-4">
                    {[...Array(5)].map((_, index) => (
                      <div key={index} className="flex justify-between items-center">
                        <Skeleton className="h-6 w-1/2 bg-indigo-100" />
                        <Skeleton className="h-6 w-1/4 bg-indigo-100" />
                      </div>
                    ))}
                  </div>
                ) : filteredUsers && filteredUsers.length > 0 ? (
                  <div className="space-y-2 max-h-[600px] overflow-y-auto pr-2">
                    {filteredUsers.map((user) => (
                      <div
                        key={user.id}
                        className={`p-3 rounded-lg border transition-colors cursor-pointer ${
                          selectedUser?.id === user.id
                            ? "bg-indigo-100 border-indigo-300"
                            : "bg-white hover:bg-indigo-50/50 border-indigo-100/50"
                        }`}
                        onClick={() => handleSelectUser(user)}
                      >
                        <div className="font-medium text-indigo-700">{user.username}</div>
                        <div className="text-sm text-indigo-600/70">{user.email}</div>
                        <div className="flex items-center justify-between mt-2">
                          {renderUserRole(user.role || 'user')}
                          {renderUserStatus(user.status || 'active')}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="py-8 text-center">
                    <p className="text-muted-foreground">ไม่พบข้อมูลผู้ใช้งาน</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* ส่วนแสดงรายละเอียดแพคเกจและ API Keys */}
          <Card className="border border-indigo-200/30 lg:col-span-8">
            <CardHeader className="bg-gradient-to-r from-white to-indigo-50/30">
              <CardTitle className="text-indigo-700 flex items-center">
                {selectedUser ? (
                  <>
                    <Sparkles className="h-5 w-5 mr-2 text-amber-500" />
                    <span>จัดการข้อมูลของ {selectedUser.username}</span>
                  </>
                ) : (
                  <>
                    <Sparkles className="h-5 w-5 mr-2 text-amber-500" />
                    <span>รายละเอียดแพคเกจและ API Keys</span>
                  </>
                )}
              </CardTitle>
              <CardDescription className="text-indigo-600/70">
                {selectedUser
                  ? `จัดการแพคเกจและ API Keys ของ ${selectedUser.username} (${selectedUser.email})`
                  : "กรุณาเลือกผู้ใช้งานจากรายการด้านซ้าย"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {!selectedUser ? (
                <div className="py-12 text-center">
                  <User className="h-16 w-16 mx-auto text-indigo-200" />
                  <h3 className="mt-4 text-lg font-medium text-indigo-700">กรุณาเลือกผู้ใช้งาน</h3>
                  <p className="mt-2 text-muted-foreground">เลือกผู้ใช้งานจากรายการด้านซ้ายเพื่อจัดการแพคเกจและ API Keys</p>
                </div>
              ) : (
                <Tabs defaultValue="packages" value={activeTab} onValueChange={setActiveTab}>
                  <TabsList className="grid grid-cols-2 w-full">
                    <TabsTrigger value="packages" className="flex items-center">
                      <Package className="h-4 w-4 mr-2" />
                      <span>แพคเกจ</span>
                    </TabsTrigger>
                    <TabsTrigger value="apikeys" className="flex items-center">
                      <Key className="h-4 w-4 mr-2" />
                      <span>API Keys</span>
                    </TabsTrigger>
                  </TabsList>

                  {/* เนื้อหาแท็บแพคเกจ */}
                  <TabsContent value="packages" className="mt-4">
                    <div className="space-y-6">
                      {/* ส่วนเพิ่มแพคเกจใหม่ */}
                      <Card className="border border-indigo-200/30">
                        <CardHeader className="bg-gradient-to-r from-white to-indigo-50/30">
                          <CardTitle className="text-indigo-700 flex items-center text-base">
                            <Plus className="h-4 w-4 mr-2 text-amber-500" />
                            <span>เพิ่มแพคเกจใหม่</span>
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="pt-4">
                          <Form {...addPackageForm}>
                            <form onSubmit={addPackageForm.handleSubmit((data) => addPackageMutation.mutate(data))} className="space-y-4">
                              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <FormField
                                  control={addPackageForm.control}
                                  name="packageId"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>แพคเกจ</FormLabel>
                                      <Select
                                        value={field.value.toString()}
                                        onValueChange={(value) => field.onChange(parseInt(value))}
                                      >
                                        <FormControl>
                                          <SelectTrigger className="border-indigo-200/50 focus-visible:ring-indigo-400">
                                            <SelectValue placeholder="เลือกแพคเกจ" />
                                          </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                          {packagesList && packagesList.map((pkg) => (
                                            <SelectItem key={pkg.id} value={pkg.id.toString()}>
                                              {pkg.name}
                                            </SelectItem>
                                          ))}
                                        </SelectContent>
                                      </Select>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                                <FormField
                                  control={addPackageForm.control}
                                  name="durationMonths"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>ระยะเวลา (เดือน)</FormLabel>
                                      <FormControl>
                                        <Input
                                          type="number"
                                          min={1}
                                          className="border-indigo-200/50 focus-visible:ring-indigo-400"
                                          {...field}
                                          onChange={(e) => field.onChange(parseInt(e.target.value))}
                                        />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                                <FormField
                                  control={addPackageForm.control}
                                  name="isActive"
                                  render={({ field }) => (
                                    <FormItem className="flex flex-row items-end space-x-3 space-y-0 rounded-md">
                                      <FormControl>
                                        <Checkbox
                                          checked={field.value}
                                          onCheckedChange={field.onChange}
                                          className="data-[state=checked]:bg-indigo-600 data-[state=checked]:border-indigo-600"
                                        />
                                      </FormControl>
                                      <div className="space-y-1 leading-none">
                                        <FormLabel>เปิดใช้งานทันที</FormLabel>
                                      </div>
                                    </FormItem>
                                  )}
                                />
                              </div>
                              <div className="flex justify-end">
                                <Button
                                  type="submit"
                                  className="bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700"
                                  disabled={addPackageMutation.isPending}
                                >
                                  {addPackageMutation.isPending && (
                                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                                  )}
                                  เพิ่มแพคเกจ
                                </Button>
                              </div>
                            </form>
                          </Form>
                        </CardContent>
                      </Card>

                      {/* ส่วนแสดงรายการแพคเกจของผู้ใช้ */}
                      <Card className="border border-indigo-200/30">
                        <CardHeader className="bg-gradient-to-r from-white to-indigo-50/30">
                          <CardTitle className="text-indigo-700 flex items-center text-base">
                            <Package className="h-4 w-4 mr-2 text-amber-500" />
                            <span>แพคเกจปัจจุบัน</span>
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="pt-4">
                          {userPackages.length === 0 ? (
                            <div className="py-8 text-center">
                              <Package className="h-12 w-12 mx-auto text-indigo-200" />
                              <h3 className="mt-4 text-lg font-medium text-indigo-700">ไม่พบแพคเกจ</h3>
                              <p className="mt-2 text-muted-foreground">ผู้ใช้งานนี้ยังไม่มีแพคเกจ</p>
                            </div>
                          ) : (
                            <div className="space-y-4">
                              {userPackages.map((userPackage) => (
                                <div
                                  key={userPackage.id}
                                  className="p-4 rounded-lg border border-indigo-200/50 bg-gradient-to-r from-white to-indigo-50/30"
                                >
                                  <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                                    <div>
                                      <div className="flex items-center">
                                        <h3 className="font-medium text-indigo-700">{userPackage.package.name}</h3>
                                        {userPackage.isActive ? (
                                          <Badge className="ml-2 bg-green-50 text-green-700 border-green-200">ใช้งาน</Badge>
                                        ) : (
                                          <Badge className="ml-2 bg-yellow-50 text-yellow-700 border-yellow-200">ไม่ได้ใช้งาน</Badge>
                                        )}
                                      </div>
                                      <p className="text-sm text-indigo-600/70 mt-1">{userPackage.package.description}</p>
                                    </div>
                                    <div className="flex flex-wrap gap-2">
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="border-indigo-200 hover:bg-indigo-50 text-indigo-600 hover:text-indigo-700"
                                        onClick={() => togglePackageStatusMutation.mutate({
                                          packageId: userPackage.id,
                                          isActive: !userPackage.isActive
                                        })}
                                      >
                                        {userPackage.isActive ? (
                                          <>
                                            <X className="h-4 w-4 mr-1" />
                                            ปิดใช้งาน
                                          </>
                                        ) : (
                                          <>
                                            <Check className="h-4 w-4 mr-1" />
                                            เปิดใช้งาน
                                          </>
                                        )}
                                      </Button>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="border-blue-200 hover:bg-blue-50 text-blue-600 hover:text-blue-700"
                                        onClick={() => handleOpenResetDialog(userPackage.id)}
                                      >
                                        <RefreshCw className="h-4 w-4 mr-1" />
                                        รีเซ็ตยอดใช้งาน
                                      </Button>
                                    </div>
                                  </div>

                                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                                    <div className="p-3 rounded-md bg-indigo-50/50 border border-indigo-100/50">
                                      <div className="text-xs text-indigo-600/70">วันที่เริ่มต้น</div>
                                      <div className="font-medium text-indigo-700 flex items-center">
                                        <Calendar className="h-3 w-3 mr-1 text-indigo-400" />
                                        {formatDate(new Date(userPackage.startDate))}
                                      </div>
                                    </div>
                                    <div className="p-3 rounded-md bg-indigo-50/50 border border-indigo-100/50">
                                      <div className="text-xs text-indigo-600/70">วันที่สิ้นสุด</div>
                                      <div className="font-medium text-indigo-700 flex items-center">
                                        <Calendar className="h-3 w-3 mr-1 text-indigo-400" />
                                        {formatDate(new Date(userPackage.endDate))}
                                      </div>
                                    </div>
                                    <div className="p-3 rounded-md bg-indigo-50/50 border border-indigo-100/50">
                                      <div className="text-xs text-indigo-600/70">การใช้งาน</div>
                                      <div className="font-medium text-indigo-700 flex items-center">
                                        <BarChart className="h-3 w-3 mr-1 text-indigo-400" />
                                        {userPackage.requestsUsed} / {userPackage.package.requestsLimit} ครั้ง
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    </div>
                  </TabsContent>

                  {/* เนื้อหาแท็บ API Keys */}
                  <TabsContent value="apikeys" className="mt-4">
                    <div className="space-y-6">
                      {/* ส่วนเพิ่ม API Key ใหม่ */}
                      <Card className="border border-indigo-200/30">
                        <CardHeader className="bg-gradient-to-r from-white to-indigo-50/30">
                          <CardTitle className="text-indigo-700 flex items-center text-base">
                            <Plus className="h-4 w-4 mr-2 text-amber-500" />
                            <span>สร้าง API Key ใหม่</span>
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="pt-4">
                          <Form {...addApiKeyForm}>
                            <form onSubmit={addApiKeyForm.handleSubmit((data) => addApiKeyMutation.mutate(data))} className="space-y-4">
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <FormField
                                  control={addApiKeyForm.control}
                                  name="name"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>ชื่อ API Key</FormLabel>
                                      <FormControl>
                                        <Input
                                          className="border-indigo-200/50 focus-visible:ring-indigo-400"
                                          placeholder="ชื่อสำหรับระบุ API Key"
                                          {...field}
                                        />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                                <FormField
                                  control={addApiKeyForm.control}
                                  name="usageLimit"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>จำกัดการใช้งาน (ครั้ง)</FormLabel>
                                      <FormControl>
                                        <Input
                                          type="number"
                                          min={0}
                                          className="border-indigo-200/50 focus-visible:ring-indigo-400"
                                          placeholder="ไม่จำกัด"
                                          {...field}
                                          value={field.value === undefined ? "" : field.value}
                                          onChange={(e) => field.onChange(e.target.value === "" ? undefined : parseInt(e.target.value))}
                                        />
                                      </FormControl>
                                      <FormDescription>
                                        เว้นว่างไว้หากไม่ต้องการจำกัด
                                      </FormDescription>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                                <FormField
                                  control={addApiKeyForm.control}
                                  name="description"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>คำอธิบาย</FormLabel>
                                      <FormControl>
                                        <Textarea
                                          className="border-indigo-200/50 focus-visible:ring-indigo-400"
                                          placeholder="รายละเอียดเพิ่มเติม (ไม่บังคับ)"
                                          {...field}
                                        />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                                <FormField
                                  control={addApiKeyForm.control}
                                  name="ipWhitelist"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>IP Whitelist</FormLabel>
                                      <FormControl>
                                        <Textarea
                                          className="border-indigo-200/50 focus-visible:ring-indigo-400"
                                          placeholder="***********, ******** (คั่นด้วยเครื่องหมายคอมม่า)"
                                          {...field}
                                        />
                                      </FormControl>
                                      <FormDescription>
                                        เว้นว่างไว้หากไม่ต้องการจำกัด IP
                                      </FormDescription>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                                <FormField
                                  control={addApiKeyForm.control}
                                  name="expiryDate"
                                  render={({ field }) => (
                                    <FormItem className="flex flex-col">
                                      <FormLabel>วันหมดอายุ</FormLabel>
                                      <div className="space-y-4">
                                        <div className="flex items-center space-x-2">
                                          <Checkbox
                                            id="noExpiry"
                                            checked={field.value === null}
                                            onCheckedChange={(checked) => {
                                              if (checked) {
                                                field.onChange(null);
                                              } else {
                                                // ถ้ายกเลิกการติ๊ก ให้กำหนดวันหมดอายุเป็น 30 วันจากวันนี้
                                                const date = new Date();
                                                date.setDate(date.getDate() + 30);
                                                field.onChange(date);
                                              }
                                            }}
                                          />
                                          <label
                                            htmlFor="noExpiry"
                                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                          >
                                            ไม่มีวันหมดอายุ
                                          </label>
                                        </div>

                                        {field.value === null ? (
                                          <div className="text-white font-medium">ไม่จำกัดวันหมดอายุ</div>
                                        ) : (
                                          <>
                                            <div className="grid grid-cols-2 gap-4">
                                              <div>
                                                <label className="text-xs text-white mb-1 block">กำหนดจำนวนวัน</label>
                                                <div className="flex items-center space-x-2">
                                                  <Input
                                                    type="number"
                                                    placeholder="จำนวนวัน (เช่น 30)"
                                                    min={1}
                                                    onChange={(e) => {
                                                      const days = parseInt(e.target.value);
                                                      if (!isNaN(days) && days > 0) {
                                                        const date = new Date();
                                                        date.setDate(date.getDate() + days);
                                                        field.onChange(date);
                                                      }
                                                    }}
                                                    className="text-white font-medium"
                                                  />
                                                  <span className="text-white">วัน</span>
                                                </div>
                                              </div>

                                              <div>
                                                <label className="text-xs text-white mb-1 block">หรือระบุวันที่</label>
                                                <Input
                                                  type="text"
                                                  placeholder="DD/MM/YYYY (เช่น 01/05/2568)"
                                                  value={field.value ? format(field.value, 'dd/MM/yyyy') : ''}
                                                  onChange={(e) => {
                                                    const value = e.target.value;
                                                    if (value === '') {
                                                      return;
                                                    }

                                                    const datePattern = /^(\d{2})\/(\d{2})\/(\d{4})$/;
                                                    const match = value.match(datePattern);

                                                    if (match) {
                                                      const day = parseInt(match[1], 10);
                                                      const month = parseInt(match[2], 10) - 1; // เดือนใน JavaScript เริ่มจาก 0
                                                      const year = parseInt(match[3], 10);

                                                      const date = new Date(year, month, day);

                                                      // ตรวจสอบว่าวันที่ถูกต้องหรือไม่
                                                      if (!isNaN(date.getTime()) && date >= new Date()) {
                                                        field.onChange(date);
                                                      }
                                                    }
                                                  }}
                                                  className="text-white font-medium"
                                                />
                                              </div>
                                            </div>

                                            <div className="p-3 rounded-md bg-indigo-50/50 border border-indigo-100/50">
                                              <div className="text-xs text-indigo-600/70">วันที่หมดอายุ</div>
                                              <div className="font-medium text-indigo-700 flex items-center">
                                                <Calendar className="h-3 w-3 mr-1 text-indigo-400" />
                                                {formatDate(field.value)}
                                              </div>
                                            </div>
                                          </>
                                        )}
                                      </div>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              </div>
                              <div className="flex justify-end">
                                <Button
                                  type="submit"
                                  className="bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700"
                                  disabled={addApiKeyMutation.isPending}
                                >
                                  {addApiKeyMutation.isPending && (
                                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                                  )}
                                  สร้าง API Key
                                </Button>
                              </div>
                            </form>
                          </Form>
                        </CardContent>
                      </Card>

                      {/* ส่วนแสดงรายการ API Keys ของผู้ใช้ */}
                      <Card className="border border-indigo-200/30">
                        <CardHeader className="bg-gradient-to-r from-white to-indigo-50/30">
                          <CardTitle className="text-indigo-700 flex items-center text-base">
                            <Key className="h-4 w-4 mr-2 text-amber-500" />
                            <span>API Keys ปัจจุบัน</span>
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="pt-4">
                          {userApiKeys.length === 0 ? (
                            <div className="py-8 text-center">
                              <Key className="h-12 w-12 mx-auto text-indigo-200" />
                              <h3 className="mt-4 text-lg font-medium text-indigo-700">ไม่พบ API Keys</h3>
                              <p className="mt-2 text-muted-foreground">ผู้ใช้งานนี้ยังไม่มี API Keys</p>
                            </div>
                          ) : (
                            <div className="space-y-4">
                              {userApiKeys.map((apiKey) => (
                                <div
                                  key={apiKey.id}
                                  className="p-4 rounded-lg border border-indigo-200/50 bg-gradient-to-r from-white to-indigo-50/30"
                                >
                                  <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                                    <div>
                                      <div className="flex items-center">
                                        <h3 className="font-medium text-indigo-700">{apiKey.name}</h3>
                                        {apiKey.status === "active" ? (
                                          <Badge className="ml-2 bg-green-50 text-green-700 border-green-200">ใช้งาน</Badge>
                                        ) : (
                                          <Badge className="ml-2 bg-red-50 text-red-700 border-red-200">ระงับการใช้งาน</Badge>
                                        )}
                                      </div>
                                      {apiKey.description && (
                                        <p className="text-sm text-indigo-600/70 mt-1">{apiKey.description}</p>
                                      )}
                                    </div>
                                    <div className="flex flex-wrap gap-2">
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="border-indigo-200 hover:bg-indigo-50 text-indigo-600 hover:text-indigo-700"
                                        onClick={() => {
                                          // ทำการรีเจนเนอเรท API Key
                                          regenerateApiKeyMutation.mutate(apiKey.id);
                                        }}
                                      >
                                        <RefreshCw className="h-4 w-4 mr-1" />
                                        รีเจนเนอเรท
                                      </Button>
                                      {apiKey.status === "active" ? (
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          className="border-yellow-200 hover:bg-yellow-50 text-yellow-600 hover:text-yellow-700"
                                          onClick={() => {
                                            // ทำการระงับการใช้งาน API Key
                                            suspendApiKeyMutation.mutate(apiKey.id);
                                          }}
                                        >
                                          <Ban className="h-4 w-4 mr-1" />
                                          ระงับการใช้งาน
                                        </Button>
                                      ) : (
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          className="border-green-200 hover:bg-green-50 text-green-600 hover:text-green-700"
                                          onClick={() => {
                                            // ทำการเปิดใช้งาน API Key
                                            activateApiKeyMutation.mutate(apiKey.id);
                                          }}
                                        >
                                          <CheckCircle className="h-4 w-4 mr-1" />
                                          เปิดใช้งาน
                                        </Button>
                                      )}
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="border-red-200 hover:bg-red-50 text-red-600 hover:text-red-700"
                                        onClick={() => {
                                          // ทำการลบ API Key
                                          deleteApiKeyMutation.mutate(apiKey.id);
                                        }}
                                      >
                                        <Trash2 className="h-4 w-4 mr-1" />
                                        ลบ
                                      </Button>
                                    </div>
                                  </div>

                                  <div className="mt-4 p-3 rounded-md bg-indigo-50/50 border border-indigo-100/50 font-mono text-sm break-all">
                                    {apiKey.apiKey || "••••••••••••••••••••••••••••••••••••••••••••••••••"}
                                  </div>

                                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                                    <div className="p-3 rounded-md bg-indigo-50/50 border border-indigo-100/50">
                                      <div className="text-xs text-indigo-600/70">การใช้งาน</div>
                                      <div className="font-medium text-indigo-700 flex items-center">
                                        <BarChart className="h-3 w-3 mr-1 text-indigo-400" />
                                        {apiKey.usageCount || 0} {apiKey.usageLimit ? `/ ${apiKey.usageLimit}` : ""} ครั้ง
                                      </div>
                                    </div>
                                    <div className="p-3 rounded-md bg-indigo-50/50 border border-indigo-100/50">
                                      <div className="text-xs text-indigo-600/70">วันที่สร้าง</div>
                                      <div className="font-medium text-indigo-700 flex items-center">
                                        <History className="h-3 w-3 mr-1 text-indigo-400" />
                                        {formatDate(new Date(apiKey.createdAt))}
                                      </div>
                                    </div>
                                    <div className="p-3 rounded-md bg-indigo-50/50 border border-indigo-100/50">
                                      <div className="text-xs text-indigo-600/70">วันหมดอายุ</div>
                                      <div className="font-medium text-indigo-700 flex items-center">
                                        <AlarmClock className="h-3 w-3 mr-1 text-indigo-400" />
                                        {apiKey.expiresAt ? formatDate(new Date(apiKey.expiresAt)) : "ไม่มีวันหมดอายุ"}
                                      </div>
                                    </div>
                                  </div>

                                  {apiKey.ipWhitelist && apiKey.ipWhitelist.length > 0 && (
                                    <div className="mt-4 p-3 rounded-md bg-indigo-50/50 border border-indigo-100/50">
                                      <div className="text-xs text-indigo-600/70 mb-1">IP Whitelist</div>
                                      <div className="flex flex-wrap gap-2">
                                        {apiKey.ipWhitelist.map((ip, index) => (
                                          <Badge key={index} className="bg-white text-indigo-700 border-indigo-200">
                                            <Shield className="h-3 w-3 mr-1 text-indigo-400" />
                                            {ip}
                                          </Badge>
                                        ))}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    </div>
                  </TabsContent>
                </Tabs>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
      {/* Dialog ยืนยันการรีเซ็ตยอดการใช้งาน */}
      <Dialog open={isResetDialogOpen} onOpenChange={setIsResetDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2 text-amber-500" />
              ยืนยันการรีเซ็ตยอดการใช้งาน
            </DialogTitle>
            <DialogDescription>
              การรีเซ็ตยอดการใช้งานจะทำให้จำนวนการใช้งานของแพคเกจกลับไปเป็น 0 ครั้ง
              <br />
              กรุณาพิมพ์ <span className="font-bold text-amber-600">รีเซ็ทการใช้งาน</span> เพื่อยืนยัน
            </DialogDescription>
          </DialogHeader>
          <div className="p-4 border rounded-md bg-amber-50/50 border-amber-200 my-2">
            <Input
              placeholder="พิมพ์ 'รีเซ็ทการใช้งาน' เพื่อยืนยัน"
              value={resetConfirmText}
              onChange={(e) => setResetConfirmText(e.target.value)}
              className="border-amber-200 focus-visible:ring-amber-400"
            />
          </div>
          <DialogFooter className="flex flex-row justify-end gap-2 sm:justify-end">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsResetDialogOpen(false)}
            >
              ยกเลิก
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleConfirmReset}
              disabled={resetConfirmText !== "รีเซ็ทการใช้งาน" || resetPackageUsageMutation.isPending}
            >
              {resetPackageUsageMutation.isPending && (
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              )}
              ยืนยันการรีเซ็ต
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Admin>
  );
}
