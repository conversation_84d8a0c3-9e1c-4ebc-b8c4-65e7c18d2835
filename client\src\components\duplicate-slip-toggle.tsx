import { useState, useEffect } from 'react';
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Shield, AlertCircle } from 'lucide-react';

interface DuplicateSlipToggleProps {
  // เพิ่ม prop ถ้ามีความจำเป็น
}

const DuplicateSlipToggle = (props: DuplicateSlipToggleProps) => {
  const [enabled, setEnabled] = useState(true);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  // โหลดค่าการตั้งค่าเมื่อโหลดคอมโพเนนต์
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setLoading(true);
        const response = await apiRequest('GET', '/api/user-settings');
        const data = await response.json();
        
        if (data && data.duplicateSlipCheck !== undefined) {
          setEnabled(data.duplicateSlipCheck);
        }
      } catch (error) {
        console.error('Error fetching duplicate slip setting:', error);
        toast({
          title: 'เกิดข้อผิดพลาด',
          description: 'ไม่สามารถโหลดการตั้งค่าการตรวจสอบสลิปซ้ำได้',
          variant: 'destructive'
        });
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
  }, [toast]);

  // บันทึกการเปลี่ยนแปลงการตั้งค่า
  const handleToggleChange = async (checked: boolean) => {
    try {
      setLoading(true);
      const response = await apiRequest('PATCH', '/api/user-settings', {
        duplicateSlipCheck: checked
      });
      
      if (response.ok) {
        setEnabled(checked);
        toast({
          title: 'บันทึกการตั้งค่าสำเร็จ',
          description: checked 
            ? 'เปิดใช้งานการตรวจสอบสลิปซ้ำแล้ว ระบบจะตรวจสอบและแจ้งเตือนเมื่อมีการส่งสลิปที่เคยตรวจสอบแล้ว' 
            : 'ปิดการตรวจสอบสลิปซ้ำแล้ว ระบบจะไม่ตรวจสอบว่าสลิปเคยถูกตรวจสอบมาก่อนหรือไม่',
        });
      } else {
        throw new Error('Failed to update settings');
      }
    } catch (error) {
      console.error('Error updating duplicate slip setting:', error);
      toast({
        title: 'เกิดข้อผิดพลาด',
        description: 'ไม่สามารถบันทึกการตั้งค่าการตรวจสอบสลิปซ้ำได้',
        variant: 'destructive'
      });
      setEnabled(!checked); // กลับไปยังค่าเดิม
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="border border-indigo-800/30 bg-indigo-950/20 backdrop-blur-md">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center gap-2">
          <Shield className="h-5 w-5 text-amber-500" />
          <span className="bg-gradient-to-r from-purple-200 to-purple-100 text-transparent bg-clip-text">
            การตรวจสอบสลิปซ้ำ
          </span>
        </CardTitle>
        <CardDescription>
          {enabled 
            ? 'ระบบช่วยป้องกันไม่ให้ตรวจสอบสลิปเดิมซ้ำ' 
            : 'ระบบจะอนุญาตให้ตรวจสอบสลิปซ้ำได้'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Switch
              checked={enabled}
              onCheckedChange={handleToggleChange}
              disabled={loading}
              className="data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-amber-600 data-[state=checked]:to-amber-400"
            />
            <Label htmlFor="duplicate-check" className="text-sm flex gap-2 items-center">
              {enabled ? (
                <span className="text-green-400 flex items-center gap-1">
                  <Shield className="h-4 w-4" /> เปิดใช้งาน
                </span>
              ) : (
                <span className="text-amber-400 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" /> ปิดใช้งาน
                </span>
              )}
            </Label>
          </div>
        </div>
        
        <div className="mt-3 text-xs text-indigo-300 opacity-80">
          {enabled ? (
            <p>
              เมื่อเปิดใช้งาน ระบบจะตรวจสอบว่าสลิปที่อัปโหลดเคยถูกตรวจสอบมาก่อนหรือไม่
              เพื่อป้องกันการตรวจสอบสลิปซ้ำซ้อนและถูกหักเครดิตซ้ำ
            </p>
          ) : (
            <p>
              เมื่อปิดใช้งาน ระบบจะอนุญาตให้ตรวจสอบสลิปซ้ำได้ ซึ่งอาจทำให้ถูกหักเครดิตซ้ำ
              แนะนำให้เปิดใช้งานการตรวจสอบสลิปซ้ำเพื่อประหยัดเครดิต
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default DuplicateSlipToggle;