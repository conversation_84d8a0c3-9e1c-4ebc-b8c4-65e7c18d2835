import { useEffect, useRef } from "react";
import { useAuth } from "@/hooks/use-auth";
import { useLocation } from "wouter";
import axios from "axios";

// รายการเส้นทางที่ไม่ต้องการล็อกอิน
const PUBLIC_ROUTES = ["/", "/auth", "/docs", "/packages", "/qr-reader", "/password-reset", "/verification"];

export function SessionCheck() {
  const { user, isLoading } = useAuth();
  const [location, setLocation] = useLocation();
  const redirectedRef = useRef(false);
  const isCheckingRef = useRef(false);

  // ตรวจสอบ session ทันทีเมื่อโหลดหน้า (สำหรับหน้าต้องล็อกอินเท่านั้น)
  useEffect(() => {
    // ตรวจสอบว่าเป็นเส้นทางสาธารณะหรือไม่
    const isPublicRoute = PUBLIC_ROUTES.some(route => 
      location === route || 
      (route === "/" && location === "")
    );

    // ถ้าเป็นเส้นทางสาธารณะหรือกำลังตรวจสอบ หรือเคยเปลี่ยนเส้นทางแล้ว ให้ข้าม
    if (isPublicRoute || isCheckingRef.current || redirectedRef.current) {
      return;
    }

    // ทำการตรวจสอบ session
    const checkSession = async () => {
      try {
        isCheckingRef.current = true;

        // ตรวจสอบสถานะการล็อกอินจาก API โดยตรง
        const response = await axios.get('/api/login-status');
        const { isAuthenticated } = response.data;

        if (!isAuthenticated) {
          console.log(`Session check: ไม่พบ session ที่ถูกต้อง, เปลี่ยนเส้นทางจาก ${location} ไปที่ /auth`);
          redirectedRef.current = true;
          window.location.href = "/auth"; // ใช้การเปลี่ยน URL โดยตรงเพื่อหลีกเลี่ยงการวนลูป
        }

        isCheckingRef.current = false;
      } catch (error) {
        console.error("ไม่สามารถตรวจสอบสถานะการล็อกอินได้:", error);
        redirectedRef.current = true;
        window.location.href = "/auth";
        isCheckingRef.current = false;
      }
    };

    checkSession();

    // ล้างค่า redirected เมื่อ unmount
    return () => {
      redirectedRef.current = false;
      isCheckingRef.current = false;
    };
  }, [location]);

  // ตรวจสอบสิทธิ์หลังจากรู้ว่าผู้ใช้คือใคร (เฉพาะหน้าแอดมิน)
  useEffect(() => {
    // ข้ามการตรวจสอบหากกำลังโหลดข้อมูล หรือเคยเปลี่ยนเส้นทางแล้ว
    if (isLoading || redirectedRef.current) return;

    // ถ้าเข้าหน้าแอดมินแต่ไม่ใช่แอดมิน
    if (location.startsWith("/admin") && user?.role !== "admin") {
      console.log(`ผู้ใช้ไม่มีสิทธิ์เข้าถึงหน้าแอดมิน, เปลี่ยนเส้นทางไปที่ /dashboard`);
      redirectedRef.current = true;
      window.location.href = "/dashboard";
    }
  }, [user, isLoading, location]);

  // ไม่ต้องแสดงผลอะไรในหน้าจอ
  return null;
}