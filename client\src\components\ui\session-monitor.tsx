import { useState, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { Eye, EyeOff, UserX, Clock, RefreshCw, Shield, Zap, Globe, Users } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { th } from "date-fns/locale";

interface OnlineUser {
  userId: number;
  username: string;
  sessionId: string;
  ip: string;
  userAgent: string;
  lastActivity: Date;
  location: string;
  role: string;
  loginTime: Date;
}

interface SessionStats {
  activeUsers: number;
  totalSessions: number;
  adminUsers: number;
  regularUsers: number;
  blacklistedSessions: number;
}

export function SessionMonitor() {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("online");

  // ดึงข้อมูลผู้ใช้ที่กำลังออนไลน์
  const { 
    data: onlineUsers = [], 
    isLoading,
    error,
    refetch: refetchOnlineUsers 
  } = useQuery<OnlineUser[]>({
    queryKey: ["/api/admin/online-users"],
    refetchInterval: 5000, // รีเฟรชทุก 5 วินาที (เร็วขึ้นจาก 10 วินาทีเพื่อติดตามสถานะอย่างต่อเนื่อง)
    retry: 3,
    retryDelay: 1000,
    refetchOnWindowFocus: true,
  });

  // ดึงข้อมูลสถิติของ session
  const { 
    data: sessionStats = { activeUsers: 0, totalSessions: 0, adminUsers: 0, regularUsers: 0, blacklistedSessions: 0 },
    refetch: refetchSessionStats 
  } = useQuery<SessionStats>({
    queryKey: ["/api/admin/session-stats"],
    refetchInterval: 10000,
    retry: 3,
    retryDelay: 1000,
    refetchOnWindowFocus: true,
  });

  // ดึงข้อมูล session ทั้งหมด
  const { 
    data: allSessions = [], 
    refetch: refetchAllSessions 
  } = useQuery<OnlineUser[]>({
    queryKey: ["/api/admin/all-sessions"],
    refetchInterval: 15000,
    enabled: activeTab === "all",
    retry: 3,
    retryDelay: 1000,
    refetchOnWindowFocus: true,
  });

  // เตะผู้ใช้ออกจากระบบโดยใช้ userId
  const kickUserMutation = useMutation({
    mutationFn: async (userId: number) => {
      const res = await apiRequest("POST", "/api/admin/kick-user", { userId });
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "เตะผู้ใช้สำเร็จ",
        description: "ผู้ใช้ถูกเตะออกจากระบบเรียบร้อยแล้ว",
      });
      refetchOnlineUsers();
      refetchSessionStats();
      refetchAllSessions();
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message || "ไม่สามารถเตะผู้ใช้ออกจากระบบได้",
        variant: "destructive",
      });
    },
  });

  // เตะผู้ใช้ออกจากระบบโดยใช้ sessionId
  const kickSessionMutation = useMutation({
    mutationFn: async (sessionId: string) => {
      const res = await apiRequest("POST", "/api/admin/kick-session", { sessionId });
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "เตะ Session สำเร็จ",
        description: "Session ถูกเตะออกจากระบบเรียบร้อยแล้ว",
      });
      refetchOnlineUsers();
      refetchSessionStats();
      refetchAllSessions();
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message || "ไม่สามารถเตะ Session ออกจากระบบได้",
        variant: "destructive",
      });
    },
  });

  // เคลียร์ session ที่ไม่ได้ใช้งานเป็นเวลานาน
  const cleanupSessionsMutation = useMutation({
    mutationFn: async () => {
      const res = await apiRequest("POST", "/api/admin/cleanup-inactive-sessions", {});
      return await res.json();
    },
    onSuccess: (data) => {
      toast({
        title: "เคลียร์ Session สำเร็จ",
        description: `เคลียร์ ${data.cleanedCount || 0} sessions ที่ไม่ได้ใช้งานเรียบร้อยแล้ว`,
      });
      refetchOnlineUsers();
      refetchSessionStats();
      refetchAllSessions();
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message || "ไม่สามารถเคลียร์ Session ได้",
        variant: "destructive",
      });
    },
  });

  // รีเฟรชข้อมูลเมื่อเปลี่ยนแท็บ
  useEffect(() => {
    if (activeTab === "online") {
      refetchOnlineUsers();
    } else if (activeTab === "all") {
      refetchAllSessions();
    }
    refetchSessionStats();
  }, [activeTab, refetchOnlineUsers, refetchAllSessions, refetchSessionStats]);

  // แปลงเวลาให้อยู่ในรูปแบบ relative time (เช่น "5 นาทีที่แล้ว")
  const formatTime = (dateString: string | Date) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, { addSuffix: true, locale: th });
    } catch (error) {
      return "ไม่ทราบ";
    }
  };

  // แปลง User Agent เป็นชื่อเบราว์เซอร์อย่างง่าย
  const simplifyUserAgent = (ua: string) => {
    if (!ua) return "ไม่ทราบ";
    
    if (ua.includes("Chrome")) return "Chrome";
    if (ua.includes("Firefox")) return "Firefox";
    if (ua.includes("Safari") && !ua.includes("Chrome")) return "Safari";
    if (ua.includes("Edge")) return "Edge";
    if (ua.includes("Opera")) return "Opera";
    if (ua.includes("MSIE") || ua.includes("Trident")) return "Internet Explorer";
    if (ua.includes("Mobile")) return "Mobile Browser";
    
    return "เว็บเบราว์เซอร์";
  };

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>การติดตาม Session</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center h-40">
            <p className="text-destructive">เกิดข้อผิดพลาดในการโหลดข้อมูล</p>
            <Button onClick={() => refetchOnlineUsers()} className="mt-4">
              <RefreshCw className="w-4 h-4 mr-2" /> ลองใหม่
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const displaySessions = activeTab === "online" ? onlineUsers : allSessions;

  return (
    <Card className="shadow-lg border-t-4 border-t-primary">
      <CardHeader className="pb-3">
        <CardTitle className="text-2xl font-bold flex items-center">
          <Users className="w-6 h-6 mr-2 text-primary" />
          การติดตาม Session แบบเรียลไทม์
        </CardTitle>
        <CardDescription>
          ดูและจัดการผู้ใช้ที่กำลังออนไลน์และ session ทั้งหมดในระบบ
        </CardDescription>
      </CardHeader>

      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 px-6 py-2">
        <Card className="bg-primary/5 border-primary/20">
          <CardHeader className="p-3 pb-1">
            <CardTitle className="text-sm font-medium text-muted-foreground">ผู้ใช้ออนไลน์</CardTitle>
          </CardHeader>
          <CardContent className="p-3 pt-0">
            <p className="text-2xl font-bold">{sessionStats.activeUsers}</p>
          </CardContent>
        </Card>
        
        <Card className="bg-primary/5 border-primary/20">
          <CardHeader className="p-3 pb-1">
            <CardTitle className="text-sm font-medium text-muted-foreground">เซสชั่นทั้งหมด</CardTitle>
          </CardHeader>
          <CardContent className="p-3 pt-0">
            <p className="text-2xl font-bold">{sessionStats.totalSessions}</p>
          </CardContent>
        </Card>
        
        <Card className="bg-primary/5 border-primary/20">
          <CardHeader className="p-3 pb-1">
            <CardTitle className="text-sm font-medium text-muted-foreground">ผู้ดูแลระบบ</CardTitle>
          </CardHeader>
          <CardContent className="p-3 pt-0">
            <p className="text-2xl font-bold">{sessionStats.adminUsers}</p>
          </CardContent>
        </Card>
        
        <Card className="bg-primary/5 border-primary/20">
          <CardHeader className="p-3 pb-1">
            <CardTitle className="text-sm font-medium text-muted-foreground">ผู้ใช้ทั่วไป</CardTitle>
          </CardHeader>
          <CardContent className="p-3 pt-0">
            <p className="text-2xl font-bold">{sessionStats.regularUsers}</p>
          </CardContent>
        </Card>
        
        <Card className="bg-primary/5 border-primary/20">
          <CardHeader className="p-3 pb-1">
            <CardTitle className="text-sm font-medium text-muted-foreground">แบล็คลิสต์</CardTitle>
          </CardHeader>
          <CardContent className="p-3 pt-0">
            <p className="text-2xl font-bold">{sessionStats.blacklistedSessions}</p>
          </CardContent>
        </Card>
      </div>

      <CardContent className="p-6 pt-2">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <div className="flex justify-between items-center mb-4">
            <TabsList>
              <TabsTrigger value="online" className="flex items-center">
                <Eye className="w-4 h-4 mr-2" /> ผู้ใช้ออนไลน์
              </TabsTrigger>
              <TabsTrigger value="all" className="flex items-center">
                <Users className="w-4 h-4 mr-2" /> เซสชั่นทั้งหมด
              </TabsTrigger>
            </TabsList>
            
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => {
                  refetchOnlineUsers();
                  refetchSessionStats();
                  refetchAllSessions();
                }}
                disabled={isLoading}
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                รีเฟรช
              </Button>
              
              <Button 
                variant="destructive" 
                size="sm"
                onClick={() => cleanupSessionsMutation.mutate()}
                disabled={cleanupSessionsMutation.isPending}
              >
                <Clock className="w-4 h-4 mr-2" />
                เคลียร์ session ที่ไม่ใช้งาน
              </Button>
            </div>
          </div>

          <TabsContent value="online" className="m-0">
            <Table>
              <TableCaption>ข้อมูลผู้ใช้ที่กำลังออนไลน์ (อัพเดตอัตโนมัติทุก 5 วินาที)</TableCaption>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[150px]">ผู้ใช้</TableHead>
                  <TableHead>IP Address</TableHead>
                  <TableHead className="hidden md:table-cell">เบราว์เซอร์</TableHead>
                  <TableHead className="hidden md:table-cell">ตำแหน่ง</TableHead>
                  <TableHead className="hidden md:table-cell">กิจกรรมล่าสุด</TableHead>
                  <TableHead className="hidden md:table-cell">สถานะ</TableHead>
                  <TableHead className="text-right">จัดการ</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading && (
                  <TableRow>
                    <TableCell colSpan={7} className="h-24 text-center">
                      กำลังโหลดข้อมูล...
                    </TableCell>
                  </TableRow>
                )}
                
                {!isLoading && displaySessions.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={7} className="h-24 text-center">
                      ไม่พบผู้ใช้ที่กำลังออนไลน์
                    </TableCell>
                  </TableRow>
                )}
                
                {!isLoading && displaySessions.map((user) => (
                  <TableRow key={user.sessionId}>
                    <TableCell className="font-medium">
                      <div className="flex flex-col">
                        <span>{user.username}</span>
                        <span className="text-xs text-muted-foreground">ID: {user.userId}</span>
                      </div>
                    </TableCell>
                    <TableCell>{user.ip}</TableCell>
                    <TableCell className="hidden md:table-cell">{simplifyUserAgent(user.userAgent)}</TableCell>
                    <TableCell className="hidden md:table-cell">{user.location || "ไม่ทราบ"}</TableCell>
                    <TableCell className="hidden md:table-cell">{formatTime(user.lastActivity)}</TableCell>
                    <TableCell className="hidden md:table-cell">
                      {user.role === "admin" ? (
                        <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20">
                          <Shield className="w-3 h-3 mr-1" /> แอดมิน
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="bg-muted text-muted-foreground">
                          ผู้ใช้ทั่วไป
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => kickSessionMutation.mutate(user.sessionId)}
                        disabled={kickSessionMutation.isPending || user.role === "admin"}
                        className={user.role === "admin" ? "opacity-50 cursor-not-allowed" : ""}
                        title={user.role === "admin" ? "ไม่สามารถเตะแอดมินได้" : "เตะผู้ใช้ออกจากระบบ"}
                      >
                        <UserX className="w-4 h-4 text-destructive" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TabsContent>

          <TabsContent value="all" className="m-0">
            <Table>
              <TableCaption>ข้อมูล Session ทั้งหมดในระบบ (ทั้งออนไลน์และไม่ได้ออนไลน์)</TableCaption>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[150px]">ผู้ใช้</TableHead>
                  <TableHead>IP Address</TableHead>
                  <TableHead className="hidden md:table-cell">Session ID</TableHead>
                  <TableHead className="hidden md:table-cell">เวลาเข้าสู่ระบบ</TableHead>
                  <TableHead className="hidden md:table-cell">กิจกรรมล่าสุด</TableHead>
                  <TableHead className="hidden md:table-cell">สถานะ</TableHead>
                  <TableHead className="text-right">จัดการ</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {!displaySessions.length && (
                  <TableRow>
                    <TableCell colSpan={7} className="h-24 text-center">
                      ไม่พบข้อมูล Session
                    </TableCell>
                  </TableRow>
                )}
                
                {displaySessions.map((session) => {
                  // ตรวจสอบว่า session ยังออนไลน์อยู่หรือไม่
                  const isActive = onlineUsers.some(u => u.sessionId === session.sessionId);
                  
                  return (
                    <TableRow key={session.sessionId} className={!isActive ? "opacity-60" : ""}>
                      <TableCell className="font-medium">
                        <div className="flex flex-col">
                          <span>{session.username}</span>
                          <span className="text-xs text-muted-foreground">ID: {session.userId}</span>
                        </div>
                      </TableCell>
                      <TableCell>{session.ip}</TableCell>
                      <TableCell className="hidden md:table-cell text-xs font-mono">
                        {session.sessionId.substring(0, 10)}...
                      </TableCell>
                      <TableCell className="hidden md:table-cell">{formatTime(session.loginTime)}</TableCell>
                      <TableCell className="hidden md:table-cell">{formatTime(session.lastActivity)}</TableCell>
                      <TableCell className="hidden md:table-cell">
                        {isActive ? (
                          <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
                            <Zap className="w-3 h-3 mr-1" /> ออนไลน์
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="bg-muted text-muted-foreground">
                            <EyeOff className="w-3 h-3 mr-1" /> ไม่ได้ใช้งาน
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => kickSessionMutation.mutate(session.sessionId)}
                          disabled={kickSessionMutation.isPending || session.role === "admin"}
                          className={session.role === "admin" ? "opacity-50 cursor-not-allowed" : ""}
                          title={session.role === "admin" ? "ไม่สามารถเตะแอดมินได้" : "เตะผู้ใช้ออกจากระบบ"}
                        >
                          <UserX className="w-4 h-4 text-destructive" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TabsContent>
        </Tabs>
      </CardContent>

      <CardFooter className="flex justify-between border-t px-6 py-4">
        <div className="text-xs text-muted-foreground">
          อัพเดตล่าสุด: {new Date().toLocaleTimeString("th-TH", { hour: "2-digit", minute: "2-digit", second: "2-digit" })}
        </div>
        <div className="flex items-center">
          <Globe className="w-4 h-4 mr-2 text-primary animate-pulse" />
          <span className="text-xs text-muted-foreground">กำลังติดตามข้อมูลแบบเรียลไทม์</span>
        </div>
      </CardFooter>
    </Card>
  );
}