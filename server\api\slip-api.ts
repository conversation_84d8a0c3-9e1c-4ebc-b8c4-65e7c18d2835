import { Express, Request, Response, NextFunction } from 'express';
import { storage } from '../storage';
import { slipService } from '../slip-service';
import multer from 'multer';
import { apiRequestTypeEnum } from '@shared/schema';
import fs from 'fs';
import path from 'path';
import { apiResponse } from './api-response';
// นำเข้าฟังก์ชันตรวจสอบ QR Code
import { hasQRCode, detectQRCode } from '../qr-detector-new';
// นำเข้าฟังก์ชันตรวจสอบรูปภาพ
import { analyzeBankSlip } from '../analyzeQRCode';
// นำเข้า sharp สำหรับการวิเคราะห์รูปภาพ
import sharp from 'sharp';
// นำเข้า logger และฟังก์ชัน Redis
import { logger } from '../logger';
import {
  getCachedQRCodeVerification,
  cacheQRCodeVerification,
  getCachedSlipByFilename,
  cacheSlipFilename,
  isSlipFilenameExists
} from '../redis-client';

// กำหนดโฟลเดอร์สำหรับเก็บไฟล์สลิปที่อัปโหลดชั่วคราว
const uploadDir = path.join('tmp', 'uploads');

// กำหนดโฟลเดอร์สำหรับเก็บไฟล์สลิปถาวร (ต้องอยู่ใน public เพื่อให้เข้าถึงได้จากเว็บ)
const permanentUploadDir = path.join('public', 'uploads', 'slips');

// สร้างโฟลเดอร์ถ้ายังไม่มี
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// สร้างโฟลเดอร์สำหรับเก็บไฟล์ถาวร
if (!fs.existsSync(permanentUploadDir)) {
  fs.mkdirSync(permanentUploadDir, { recursive: true });
}

// ตั้งค่า multer สำหรับการอัปโหลดไฟล์
const storage_multer = multer.diskStorage({
  destination: (_req, _file, cb) => {
    cb(null, uploadDir);
  },
  filename: (_req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
    const extension = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + extension);
  }
});

const upload = multer({
  storage: storage_multer,
  limits: {
    fileSize: 5 * 1024 * 1024, // จำกัดขนาดไฟล์ไม่เกิน 5MB
  },
  fileFilter: (_req, file, cb) => {
    // รองรับเฉพาะไฟล์รูปภาพ
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('ไฟล์ที่อัปโหลดต้องเป็นรูปภาพเท่านั้น'));
    }
  }
});

// Middleware สำหรับตรวจสอบ API key
async function validateApiKey(req: Request, res: Response, next: NextFunction) {
  const apiKey = req.header('X-API-Key');

  if (!apiKey) {
    return res.status(401).json({
      code: "401001",
      message: 'API key is required',
      data: null
    });
  }

  const apiKeyRecord = await storage.getApiKeyByValue(apiKey);

  if (!apiKeyRecord) {
    return res.status(401).json({
      code: "401002",
      message: 'Invalid API key',
      data: null
    });
  }

  if (apiKeyRecord.status !== 'active') {
    return res.status(403).json({
      code: "403001",
      message: `API key is ${apiKeyRecord.status}`,
      data: null
    });
  }

  // ตรวจสอบการจำกัดการใช้งาน API Key
  if (apiKeyRecord.limitEnabled && apiKeyRecord.usageLimit !== null && apiKeyRecord.usageLimit !== undefined) {
    if (apiKeyRecord.requestCount >= apiKeyRecord.usageLimit) {
      return res.status(403).json({
        code: "403003",
        message: `API key usage limit exceeded (${apiKeyRecord.requestCount}/${apiKeyRecord.usageLimit})`,
        data: {
          currentUsage: apiKeyRecord.requestCount,
          usageLimit: apiKeyRecord.usageLimit
        }
      });
    }
  }

  // เช็คข้อจำกัดการใช้งาน API
  const user = await storage.getUser(apiKeyRecord.userId);

  if (!user) {
    return res.status(500).json({
      code: "500002",
      message: 'User not found',
      data: null
    });
  }

  if (user.status !== 'active') {
    return res.status(403).json({
      code: "403002",
      message: 'User account is not active',
      data: null
    });
  }

  // ตรวจสอบแพ็กเกจที่ใช้งานอยู่
  const activePackage = await storage.getUserActivePackage(user.id);

  if (!activePackage) {
    return res.status(402).json({
      code: "402001",
      message: 'No active package found for this user',
      data: null
    });
  }

  // ตรวจสอบโควต้าการใช้งาน
  console.log('Checking quota:', activePackage.requestsUsed, 'of', activePackage.package.requestsLimit);
  if (activePackage.requestsUsed >= activePackage.package.requestsLimit) {
    console.log('Quota exceeded, checking credit for user:', user.id);

    // คำนวณราคาต่อการใช้งาน 1 ครั้ง
    let pricePerRequest = 0;

    if (activePackage.package.creditPerVerification) {
      pricePerRequest = activePackage.package.creditPerVerification;
    } else {
      // คำนวณราคาต่อครั้ง = ราคาแพ็กเกจ / จำนวนครั้งที่ใช้งานได้
      pricePerRequest = activePackage.package.price / activePackage.package.requestsLimit;

      // บันทึกค่านี้กลับเข้าฐานข้อมูล
      try {
        await storage.updatePackage(activePackage.package.id, {
          creditPerVerification: pricePerRequest,
          updatedAt: new Date()
        });
      } catch (error) {
        console.error('Error updating package credit per verification:', error);
      }
    }

    // ดึงข้อมูลเครดิตของผู้ใช้
    const userCredit = await storage.getUserCredit(user.id);
    console.log(`User ${user.id} credit: ${userCredit}, needed: ${pricePerRequest}`);

    // ถ้าเครดิตไม่พอ
    if (userCredit < pricePerRequest) {
      return res.status(402).json({
        code: "402002",
        message: `API request quota exceeded and insufficient credit (${pricePerRequest.toFixed(2)} per request needed)`,
        data: {
          creditNeeded: pricePerRequest,
          currentCredit: userCredit
        }
      });
    }

    // ถ้าเครดิตพอ ให้หักเครดิตและดำเนินการต่อ
    try {
      await storage.addUserCredit(user.id, -pricePerRequest);
      console.log(`User ${user.id} credit deducted: ${pricePerRequest} for over-quota API usage`);

      // เก็บข้อมูลไว้สำหรับบันทึกลงใน slip_verifications ภายหลัง
      req.usedCredit = true;
      req.creditUsed = pricePerRequest;
    } catch (error) {
      console.error('Error deducting user credit:', error);
      return res.status(500).json({
        code: "500003",
        message: 'Error occurred while processing payment',
        data: null
      });
    }
  }

  // ผ่านการตรวจสอบทั้งหมด
  // เก็บข้อมูลสำหรับใช้ในเส้นทาง API
  req.apiKey = apiKeyRecord;
  req.apiUser = user;
  req.apiPackage = activePackage;

  // อัปเดตข้อมูลการใช้งาน API key
  await storage.updateApiKeyUsage(apiKeyRecord.id);

  next();
}

// บันทึกการใช้งาน API
async function logApiUsage(req: Request, res: Response, apiCall: {
  apiKeyId: number;
  requestType: any;
  requestData: string;
  responseStatus: any;
  responseStatusCode: number;
  responseTime: number;
  errorMessage?: string;
}) {
  try {
    await storage.createApiLog({
      apiKeyId: apiCall.apiKeyId,
      requestType: apiCall.requestType,
      requestData: JSON.stringify({ path: apiCall.requestData }),
      responseStatus: apiCall.responseStatus,
      responseData: JSON.stringify({ statusCode: apiCall.responseStatusCode }),
      processingTime: apiCall.responseTime,
      ipAddress: req.ip || '0.0.0.0',
      errorMessage: apiCall.errorMessage
    });

    // อัปเดตจำนวนการใช้งานของแพ็กเกจเฉพาะเมื่อสำเร็จเท่านั้น
    if (req.apiPackage && apiCall.responseStatus === 'success') {
      await storage.incrementRequestsUsed(req.apiPackage.id);
      console.log(`หักเครดิตจากแพ็กเกจ ${req.apiPackage.id} เนื่องจากการตรวจสอบสำเร็จ`);
    } else if (apiCall.responseStatus !== 'success') {
      console.log(`ไม่หักเครดิตเนื่องจากสถานะ: ${apiCall.responseStatus}`);
    }
  } catch (error) {
    console.error('Error logging API usage:', error);
  }
}

export function setupSlipApiRoutes(app: Express) {
  // ตรวจสอบสลิปด้วยรูปภาพ
  app.post('/api/v1/verify-slip', validateApiKey, upload.single('slip_image'), async (req: Request, res: Response) => {
    const startTime = Date.now();
    // ตัวแปรเก็บข้อมูล QR code ที่จะใช้ในทุกส่วนของโค้ด
    let qrData: string | null = null;

    try {
      if (!req.file) {
        const responseTime = Date.now() - startTime;
        await logApiUsage(req, res, {
          apiKeyId: req.apiKey?.id || 0,
          requestType: apiRequestTypeEnum.enumValues[0], // verify_slip
          requestData: req.path,
          responseStatus: 'error',
          responseStatusCode: 400,
          responseTime,
          errorMessage: 'No slip image provided'
        });

        return res.status(400).json({
          code: "400001",
          message: 'No slip image provided',
          data: null
        });
      }

      // ตรวจสอบชื่อไฟล์ซ้ำใน Redis cache ก่อนการประมวลผลรูปภาพ
      const userId = req.apiUser?.id || 0;
      const filename = req.file.originalname;

      // ตรวจสอบการตั้งค่าตรวจสอบสลิปซ้ำของ API key
      const apiKeyRecord = req.apiKey;
      if (apiKeyRecord?.duplicateSlipCheck) {
        logger.info(`[API] ตรวจสอบชื่อไฟล์ซ้ำสำหรับ: ${filename}`);

        // ตรวจสอบชื่อไฟล์ใน Redis cache
        const cachedSlipData = await getCachedSlipByFilename(userId, filename);

        if (cachedSlipData) {
          logger.info(`[FILENAME CACHE HIT] พบข้อมูลสลิปซ้ำใน Redis cache สำหรับไฟล์: ${filename}`);

          // ดึงข้อมูลการตรวจสอบจาก cache
          const existingVerification = cachedSlipData.verificationData;
          const formattedDate = new Date(existingVerification.createdAt).toLocaleString('th-TH');

          const responseTime = Date.now() - startTime;
          await logApiUsage(req, res, {
            apiKeyId: req.apiKey?.id || 0,
            requestType: apiRequestTypeEnum.enumValues[0],
            requestData: req.path,
            responseStatus: 'error',
            responseStatusCode: 409,
            responseTime,
            errorMessage: 'Duplicate slip detected (from filename cache)'
          });

          return res.status(409).json({
            code: "409000",
            message: `สลิปฉบับนี้ถูกตรวจสอบไปแล้วเมื่อ ${formattedDate}`,
            data: {
              previousVerification: {
                id: existingVerification.id,
                verifiedAt: existingVerification.createdAt,
                amount: existingVerification.amount,
                status: existingVerification.status
              }
            }
          });
        }

        logger.debug(`[FILENAME CACHE MISS] ไม่พบข้อมูลสลิปซ้ำใน Redis cache สำหรับไฟล์: ${filename}`);
      }

      // อ่านไฟล์
      const fileBuffer = fs.readFileSync(req.file.path);

      // ตรวจสอบว่ามี QR Code ในรูปภาพหรือไม่
      try {
        console.log('กำลังตรวจสอบ QR Code ในรูปภาพก่อนส่งไปที่ API');

        // ตรวจสอบว่าเป็นรูปสลิปธนาคารจริงหรือไม่ด้วยฟังก์ชัน analyzeBankSlip
        const validationResult = await analyzeBankSlip(fileBuffer);

        if (!validationResult.isValid) {
          console.log('ตรวจพบว่าไม่ใช่รูปสลิปธนาคาร:', validationResult.reason);

          const responseTime = Date.now() - startTime;
          await logApiUsage(req, res, {
            apiKeyId: req.apiKey?.id || 0,
            requestType: apiRequestTypeEnum.enumValues[0],
            requestData: req.path,
            responseStatus: 'error',
            responseStatusCode: 400,
            responseTime,
            errorMessage: `Invalid bank slip image: ${validationResult.reason}`
          });

          return res.status(400).json({
            code: "400002",
            message: `ไม่ใช่รูปสลิปธนาคาร: ${validationResult.reason}`,
            data: null
          });
        }

        // ตรวจสอบคุณภาพรูปภาพและขนาดอีกครั้ง
        const metadata = await sharp(fileBuffer).metadata();
        if (!metadata.width || !metadata.height || metadata.width < 200 || metadata.height < 200) {
          console.log('ขนาดภาพไม่ถูกต้อง ไม่น่าจะมี QR Code ที่อ่านได้');

          const responseTime = Date.now() - startTime;
          await logApiUsage(req, res, {
            apiKeyId: req.apiKey?.id || 0,
            requestType: apiRequestTypeEnum.enumValues[0],
            requestData: req.path,
            responseStatus: 'error',
            responseStatusCode: 400,
            responseTime,
            errorMessage: 'Image dimensions incorrect, unlikely to contain valid QR code'
          });

          return res.status(400).json({
            code: "400002",
            message: 'ไม่พบ QR Code ในรูปภาพ กรุณาตรวจสอบว่าเป็นรูปสลิปธนาคารที่ถูกต้อง',
            data: null
          });
        }

        // ข้ามการตรวจสอบความคมชัดและคุณภาพของรูปภาพ
        // เพื่อให้ทำงานเหมือนกับ api/verify/slip ที่ไม่มีการตรวจสอบคุณภาพรูปภาพก่อน
        // และเพิ่มโอกาสในการตรวจพบ QR code

        // ตรวจสอบ QR Code ด้วยการตั้งค่าที่เหมือนกับใน routes.ts
        console.log('🔍 ตรวจสอบ QR code ในรูปภาพก่อนส่งไปยัง API');
        const qrResult = await detectQRCode(fileBuffer);

        if (!qrResult.hasQRCode) {
          console.log('❌ ไม่พบ QR Code ในรูปภาพ - ปฏิเสธการตรวจสอบ');

          const responseTime = Date.now() - startTime;
          await logApiUsage(req, res, {
            apiKeyId: req.apiKey?.id || 0,
            requestType: apiRequestTypeEnum.enumValues[0], // verify_slip
            requestData: req.path,
            responseStatus: 'error',
            responseStatusCode: 400,
            responseTime,
            errorMessage: 'No QR code found in image'
          });

          return res.status(400).json({
            code: "400002",
            message: 'ไม่พบ QR Code ในรูปภาพ กรุณาตรวจสอบว่าเป็นรูปสลิปธนาคารที่ถูกต้อง',
            data: null
          });
        }

        console.log(`✅ พบ QR code ในรูปภาพ: ${qrResult.qrData ? qrResult.qrData.substring(0, 30) + '...' : 'พบแต่อ่านข้อมูลไม่ได้'}`);

        // ใช้ผลลัพธ์จากการตรวจสอบ QR code ครั้งแรกเลย ไม่ต้องตรวจสอบซ้ำ
        const detectResult = qrResult;

        // เก็บข้อมูล QR code ไว้ใช้ต่อในส่วนอื่น
        qrData = detectResult.hasQRCode && detectResult.qrData ? detectResult.qrData : null;

        console.log('พบ QR Code ในรูปภาพ:', qrData || 'พบ QR code แต่ไม่สามารถอ่านข้อมูลได้');

        // ตรวจสอบสลิปซ้ำถ้ามีข้อมูล QR code
        if (detectResult.hasQRCode && qrData) {
          // ตรวจสอบการตั้งค่าตรวจสอบสลิปซ้ำของ API key
          // เปลี่ยนเป็นใช้การตั้งค่าของ API key แทนการตั้งค่าของผู้ใช้
          const apiKeyRecord = req.apiKey;
          if (apiKeyRecord?.duplicateSlipCheck) {
            logger.info(`[API] API key ถูกตั้งค่าให้ตรวจสอบสลิปซ้ำ`);

            // ตรวจสอบใน Redis cache ก่อน
            const cachedVerifications = await getCachedQRCodeVerification(req.apiUser?.id || 0, qrData);
            if (cachedVerifications) {
              logger.info(`[CACHE HIT] พบข้อมูลสลิปซ้ำใน Redis cache สำหรับ QR: ${qrData.substring(0, 20)}...`);

              const existingVerifications = Array.isArray(cachedVerifications) ? cachedVerifications : [cachedVerifications];
              if (existingVerifications.length > 0) {
                logger.info(`[API] พบสลิปซ้ำจากข้อมูล QR code (จาก Redis cache)`);

                const existingVerification = existingVerifications[0];
                const formattedDate = new Date(existingVerification.createdAt).toLocaleString('th-TH');

                const responseTime = Date.now() - startTime;
                await logApiUsage(req, res, {
                  apiKeyId: req.apiKey?.id || 0,
                  requestType: apiRequestTypeEnum.enumValues[0],
                  requestData: req.path,
                  responseStatus: 'error',
                  responseStatusCode: 409,
                  responseTime,
                  errorMessage: 'Duplicate slip detected (from cache)'
                });

                return res.status(409).json({
                  code: "409000",
                  message: `สลิปฉบับนี้ถูกตรวจสอบไปแล้วเมื่อ ${formattedDate}`,
                  data: {
                    previousVerification: {
                      id: existingVerification.id,
                      verifiedAt: existingVerification.createdAt,
                      amount: existingVerification.amount,
                      status: existingVerification.status
                    }
                  }
                });
              }
            }

            // ถ้าไม่พบใน cache ให้ค้นหาในฐานข้อมูล
            logger.debug(`[CACHE MISS] ไม่พบข้อมูลใน Redis cache สำหรับ QR: ${qrData.substring(0, 20)}... ค้นหาในฐานข้อมูล`);
            const existingVerifications = await storage.findDuplicateSlipByQRData(
              req.apiUser?.id || 0,
              qrData
            );

            if (existingVerifications && existingVerifications.length > 0) {
              logger.info(`[API] พบสลิปซ้ำจากข้อมูล QR code (จากฐานข้อมูล)`);

              const existingVerification = existingVerifications[0];
              const formattedDate = new Date(existingVerification.createdAt).toLocaleString('th-TH');

              const responseTime = Date.now() - startTime;
              await logApiUsage(req, res, {
                apiKeyId: req.apiKey?.id || 0,
                requestType: apiRequestTypeEnum.enumValues[0],
                requestData: req.path,
                responseStatus: 'error',
                responseStatusCode: 409,
                responseTime,
                errorMessage: 'Duplicate slip detected'
              });

              return res.status(409).json({
                code: "409000",
                message: `สลิปฉบับนี้ถูกตรวจสอบไปแล้วเมื่อ ${formattedDate}`,
                data: {
                  previousVerification: {
                    id: existingVerification.id,
                    verifiedAt: existingVerification.createdAt,
                    amount: existingVerification.amount,
                    status: existingVerification.status
                  }
                }
              });
            }
          } else {
            logger.info(`[API] ผู้ใช้ปิดการตรวจสอบสลิปซ้ำ`);
          }
        }
      } catch (error) {
        console.error('เกิดข้อผิดพลาดในการตรวจสอบ QR Code:', error);

        const responseTime = Date.now() - startTime;
        await logApiUsage(req, res, {
          apiKeyId: req.apiKey?.id || 0,
          requestType: apiRequestTypeEnum.enumValues[0],
          requestData: req.path,
          responseStatus: 'error',
          responseStatusCode: 400,
          responseTime,
          errorMessage: 'Error processing QR code detection'
        });

        return res.status(400).json({
          code: "400003",
          message: 'เกิดข้อผิดพลาดในการตรวจสอบ QR Code กรุณาลองใหม่อีกครั้ง',
          data: null
        });
      }

      // ตรวจสอบสลิป
      // ข้ามการตรวจสอบสลิปซ้ำเมื่อ API Key ปิดการตรวจสอบ (duplicateSlipCheck เป็น false)
      const skipDuplicateCheck = req.apiKey?.duplicateSlipCheck === false;
      if (skipDuplicateCheck) {
        console.log(`[API] ข้ามการตรวจสอบสลิปซ้ำเนื่องจาก API Key ตั้งค่า duplicateSlipCheck เป็น false`);
      }

      const result = await slipService.verifySlip(
        req.apiUser?.id || 0,
        fileBuffer,
        req.file.originalname,
        skipDuplicateCheck  // ส่งค่าให้ verifySlip ข้ามการตรวจสอบสลิปซ้ำ
      );

      // ย้ายไฟล์สลิปจากโฟลเดอร์ชั่วคราวไปยังโฟลเดอร์ถาวร
      let imagePath = null;

      try {
        // ตรวจสอบว่าโฟลเดอร์ uploads/slips มีอยู่หรือไม่
        const uploadsDir = path.join(process.cwd(), 'public', 'uploads', 'slips');
        if (!fs.existsSync(uploadsDir)) {
          fs.mkdirSync(uploadsDir, { recursive: true });
        }

        // อ่านไฟล์จากพาธชั่วคราว
        const fileBuffer = fs.readFileSync(req.file.path);

        // สร้างชื่อไฟล์แบบไม่ซ้ำกัน
        const timestamp = Date.now();
        const filename = `slip_image-${userId}-${timestamp}.jpg`;
        const filepath = path.join(uploadsDir, filename);

        // บันทึกไฟล์ในโฟลเดอร์ถาวร (ต้องใช้พาธที่ถูกต้อง)
        const permanentFilePath = path.join(permanentUploadDir, filename);
        fs.writeFileSync(permanentFilePath, fileBuffer);

        // กำหนดพาธสัมพัทธ์สำหรับบันทึกในฐานข้อมูล
        imagePath = `/uploads/slips/${filename}`;

        console.log('Slip image saved permanently at:', imagePath);
        console.log('Absolute path:', permanentFilePath);

        // ตรวจสอบว่าไฟล์ถูกบันทึกจริงๆ
        if (fs.existsSync(filepath)) {
          console.log('Verified file exists on disk after saving');
        } else {
          console.error('File was not saved successfully!');
        }
      } catch (error) {
        console.error('Error saving slip image to permanent location:', error);
      }

      // ตรวจสอบว่ามีข้อมูล QR code จากการตรวจสอบด้านบนหรือไม่
      console.log(`[API] บันทึกข้อมูล QR Code: ${qrData ? `${qrData.substring(0, 40)}${qrData.length > 40 ? '...' : ''}` : 'ไม่พบข้อมูล QR code'}`)

      // บันทึกประวัติการตรวจสอบสลิป
      const slipVerification = await storage.createSlipVerification({
        userId: req.apiUser?.id || 0,
        apiKeyId: req.apiKey?.id, // เก็บ ID ของ API key ที่ใช้
        verificationSource: 'api', // ระบุแหล่งที่มาเป็น API
        status: result.status === 200 ? 'success' : 'error',
        // บันทึกข้อมูลสำคัญเพิ่มเติมเพื่อการแสดงผลในหน้าประวัติ
        transactionRef: result.data?.transRef,
        bankName: result.data?.sender?.bank?.name || '',
        amount: result.data?.amount?.amount || 0,
        sender: result.data?.sender?.account?.name?.th || result.data?.sender?.account?.name?.en || '',
        receiver: result.data?.receiver?.account?.name?.th || result.data?.receiver?.account?.name?.en || '',
        transactionDate: result.data?.date ? new Date(result.data.date) : null,
        // บันทึกพาธของรูปภาพถาวร
        imagePath: imagePath,
        // บันทึกข้อมูล QR code ถ้ามี
        qrData: qrData,
        // บันทึกข้อมูลการใช้เครดิต หากใช้งานเกินโควต้า
        usedCredit: req.usedCredit || false,
        creditUsed: req.creditUsed || 0,
        responseData: JSON.stringify({
          originalFilename: req.file.originalname,
          savedFilename: req.file.filename,
          filePath: req.file.path,
          fileSize: req.file.size,
          mimeType: req.file.mimetype,
          processingTime: Date.now() - startTime,
          providerUsed: result.apiProvider || 'unknown',
          overQuotaRequest: req.usedCredit || false,
          creditDeducted: req.creditUsed || 0,
          qrData: qrData, // เพิ่มข้อมูล QR code ในข้อมูลการตอบกลับด้วย
          ...result
        })
      });

      // บันทึกข้อมูลลง Redis cache สำหรับชื่อไฟล์
      if (result.status === 200 && req.file && qrData) {
        try {
          // บันทึกข้อมูลชื่อไฟล์ลง Redis cache
          await cacheSlipFilename(
            req.apiUser?.id || 0,
            req.file.originalname,
            qrData,
            slipVerification,
            86400 // 24 ชั่วโมง
          );
          logger.debug(`[CACHE SAVE] บันทึกข้อมูลชื่อไฟล์ลง Redis cache: ${req.file.originalname}`);
        } catch (error) {
          logger.error(`[CACHE ERROR] ไม่สามารถบันทึกข้อมูลชื่อไฟล์ลง Redis cache:`, error);
        }
      }

      // ส่งข้อมูลการตรวจสอบใหม่ผ่าน WebSocket (ถ้ามีฟังก์ชัน)
      if (typeof global.broadcastWebsocketEvent === 'function') {
        try {
          global.broadcastWebsocketEvent('dashboard', 'new_verification', {
            id: slipVerification.id,
            transactionRef: slipVerification.transactionRef,
            bankName: slipVerification.bankName,
            amount: slipVerification.amount,
            sender: slipVerification.sender,
            receiver: slipVerification.receiver,
            transactionDate: slipVerification.transactionDate,
            status: slipVerification.status,
            createdAt: new Date(),
            source: 'api'
          });
          console.log('Broadcast API verification update via WebSocket');
        } catch (err) {
          console.error('Error broadcasting WebSocket event:', err);
        }
      }

      // บันทึกการใช้งาน API
      const responseTime = Date.now() - startTime;
      await logApiUsage(req, res, {
        apiKeyId: req.apiKey?.id || 0,
        requestType: apiRequestTypeEnum.enumValues[0], // verify_slip
        requestData: req.path,
        responseStatus: result.status === 200 ? 'success' : 'error',
        responseStatusCode: result.status,
        responseTime
      });

      // ส่งผลลัพธ์กลับ
      if (result.status === 200 && result.data) {
        // แปลงข้อมูลให้อยู่ในรูปแบบที่ต้องการ
        const responseData = {
          referenceId: slipVerification.id.toString(),
          decode: result.data.payload || "",
          transRef: result.data.transRef || "",
          dateTime: result.data.date || new Date().toISOString(),
          amount: result.data.amount?.amount || 0,
          ref1: result.data.ref1 || null,
          ref2: result.data.ref2 || null,
          ref3: result.data.ref3 || null,
          receiver: {
            account: {
              name: result.data.receiver?.account?.name?.th || result.data.receiver?.account?.name?.en || "",
              bank: {
                account: result.data.receiver?.account?.bank?.account || ""
              },
              proxy: result.data.receiver?.account?.proxy || null
            },
            bank: {
              id: result.data.receiver?.bank?.id || "",
              name: result.data.receiver?.bank?.name || ""
            }
          },
          sender: {
            account: {
              name: result.data.sender?.account?.name?.th || result.data.sender?.account?.name?.en || "",
              bank: {
                account: result.data.sender?.account?.bank?.account || ""
              }
            },
            bank: {
              id: result.data.sender?.bank?.id || "",
              name: result.data.sender?.bank?.name || ""
            }
          }
        };

        return res.status(200).json({
          code: "200000",
          message: "Slip found",
          data: responseData
        });
      } else {
        return res.status(result.status).json({
          code: `${result.status}001`,
          message: result.message || 'Failed to verify slip',
          data: null
        });
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;

      // บันทึกการใช้งาน API
      await logApiUsage(req, res, {
        apiKeyId: req.apiKey?.id || 0,
        requestType: apiRequestTypeEnum.enumValues[0], // verify_slip
        requestData: req.path,
        responseStatus: 'error',
        responseStatusCode: 500,
        responseTime,
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      });

      console.error('Error verifying slip:', error);

      return res.status(500).json({
        code: "500001",
        message: 'Internal server error occurred while verifying slip',
        data: null
      });
    } finally {
      // ลบไฟล์ชั่วคราวหลังจากประมวลผลเสร็จ
      if (req.file) {
        try {
          fs.unlinkSync(req.file.path);
          console.log(`ลบไฟล์ชั่วคราว ${req.file.path} เรียบร้อยแล้ว`);
        } catch (error) {
          console.error(`ไม่สามารถลบไฟล์ชั่วคราว ${req.file.path}:`, error);
        }
      }
    }
  });

  // API ดูประวัติการใช้งาน
  app.get('/api/v1/usage', validateApiKey, async (req: Request, res: Response) => {
    const startTime = Date.now();

    try {
      // ดึงสถิติการใช้งาน API
      const stats = await storage.getApiUsageStats(req.apiUser?.id || 0);

      // ดึงข้อมูลแพ็กเกจปัจจุบัน
      const packageInfo = req.apiPackage ? {
        name: req.apiPackage.package?.name || 'Unknown',
        requestsUsed: req.apiPackage.requestsUsed || 0,
        requestsLimit: req.apiPackage.package?.requestsLimit || 0,
        startDate: req.apiPackage.startDate,
        endDate: req.apiPackage.endDate
      } : {
        name: 'No Package',
        requestsUsed: 0,
        requestsLimit: 0,
        startDate: new Date(),
        endDate: new Date()
      };

      // บันทึกการใช้งาน API
      const responseTime = Date.now() - startTime;
      await logApiUsage(req, res, {
        apiKeyId: req.apiKey?.id || 0,
        requestType: apiRequestTypeEnum.enumValues[1], // get_usage
        requestData: req.path,
        responseStatus: 'success',
        responseStatusCode: 200,
        responseTime
      });

      return res.status(200).json({
        code: "200000",
        message: "Success",
        data: {
          current_package: packageInfo,
          usage_stats: stats
        }
      });
    } catch (error) {
      const responseTime = Date.now() - startTime;

      // บันทึกการใช้งาน API
      await logApiUsage(req, res, {
        apiKeyId: req.apiKey?.id || 0,
        requestType: apiRequestTypeEnum.enumValues[1], // get_usage
        requestData: req.path,
        responseStatus: 'error',
        responseStatusCode: 500,
        responseTime,
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      });

      console.error('Error fetching usage stats:', error);

      return res.status(500).json({
        code: "500001",
        message: 'Internal server error occurred while fetching usage statistics',
        data: null
      });
    }
  });

  // API ดูประวัติการตรวจสอบสลิป
  app.get('/api/v1/history', validateApiKey, async (req: Request, res: Response) => {
    const startTime = Date.now();

    try {
      // พารามิเตอร์สำหรับการกรองข้อมูล
      const limit = Number(req.query.limit) || 10;

      // ดึงประวัติการตรวจสอบสลิป
      let verifications = await storage.listUserSlipVerifications(req.apiUser?.id || 0);

      // จำกัดจำนวนรายการ
      verifications = verifications.slice(0, limit);

      // บันทึกการใช้งาน API
      const responseTime = Date.now() - startTime;
      await logApiUsage(req, res, {
        apiKeyId: req.apiKey?.id || 0,
        requestType: apiRequestTypeEnum.enumValues[2], // get_history
        requestData: req.path,
        responseStatus: 'success',
        responseStatusCode: 200,
        responseTime
      });

      return res.status(200).json({
        code: "200000",
        message: "Success",
        data: {
          total: verifications.length,
          verifications: verifications.map(v => ({
            id: v.id,
            status: v.status,
            created_at: v.createdAt,
            // Extract additional data from the JSON in responseData
            ...JSON.parse(v.responseData || '{}')?.providerData
          }))
        }
      });
    } catch (error) {
      const responseTime = Date.now() - startTime;

      // บันทึกการใช้งาน API
      await logApiUsage(req, res, {
        apiKeyId: req.apiKey?.id || 0,
        requestType: apiRequestTypeEnum.enumValues[2], // get_history
        requestData: req.path,
        responseStatus: 'error',
        responseStatusCode: 500,
        responseTime,
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      });

      console.error('Error fetching history:', error);

      return res.status(500).json({
        code: "500001",
        message: 'Internal server error occurred while fetching verification history',
        data: null
      });
    }
  });
}