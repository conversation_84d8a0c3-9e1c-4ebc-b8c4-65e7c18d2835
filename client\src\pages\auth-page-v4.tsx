import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/hooks/use-auth";
import { Navbar } from "@/components/layouts/navbar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { insertUserSchema } from "@shared/schema";
import { CircleUserRound, KeyRound, AtSign, Building, User, Lock } from "lucide-react";

// กำหนด Schema สำหรับการเข้าสู่ระบบ
const loginSchema = z.object({
  username: z.string().min(1, "กรุณากรอกชื่อผู้ใช้"),
  password: z.string().min(1, "กรุณากรอกรหัสผ่าน"),
});

// กำหนด Schema สำหรับการลงทะเบียน (โดยใช้ Schema ที่มีอยู่แล้ว)
const registerSchema = insertUserSchema.extend({
  password: z.string().min(8, "รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร"),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "รหัสผ่านไม่ตรงกัน",
  path: ["confirmPassword"],
});

type LoginFormValues = z.infer<typeof loginSchema>;
type RegisterFormValues = z.infer<typeof registerSchema>;

export default function AuthPage() {
  const [activeTab, setActiveTab] = useState<string>("login");
  const [location, setLocation] = useLocation();
  const { user, loginMutation, registerMutation } = useAuth();

  // สร้าง Form สำหรับการเข้าสู่ระบบ
  const loginForm = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: "",
      password: "",
    },
  });

  // สร้าง Form สำหรับการลงทะเบียน
  const registerForm = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      username: "",
      email: "",
      firstName: "",
      lastName: "",
      companyName: "",
      password: "",
      confirmPassword: "",
    },
  });

  // ส่งคำขอเข้าสู่ระบบ
  const onLoginSubmit = (values: LoginFormValues) => {
    loginMutation.mutate(values);
  };

  // ส่งคำขอลงทะเบียน
  const onRegisterSubmit = (values: RegisterFormValues) => {
    // ลบ confirmPassword ออกเพราะไม่ต้องส่งไปยัง API
    const { confirmPassword, ...registerData } = values;
    registerMutation.mutate(registerData);
  };

  // เมื่อมีผู้ใช้งานเข้าสู่ระบบแล้ว ให้ไปยังหน้าแดชบอร์ด
  useEffect(() => {
    if (user) {
      setLocation("/dashboard");
    }
  }, [user, setLocation]);

  return (
    <div className="min-h-screen flex flex-col bg-white">
      <Navbar />
      
      <main className="flex-1 flex flex-col items-center justify-center px-4 py-12">
        <div className="w-full max-w-md mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-blue-700">SLIPKUY</h1>
            <p className="mt-2 text-slate-600">บริการตรวจสอบสลิป API ที่ใช้งานง่าย เชื่อถือได้</p>
          </div>
          
          <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-slate-200">
            <Tabs defaultValue="login" value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-2 h-14 rounded-none bg-slate-50">
                <TabsTrigger 
                  value="login" 
                  className="font-medium text-base data-[state=active]:bg-white data-[state=active]:text-blue-700 data-[state=active]:shadow-none rounded-none"
                >
                  เข้าสู่ระบบ
                </TabsTrigger>
                <TabsTrigger 
                  value="register" 
                  className="font-medium text-base data-[state=active]:bg-white data-[state=active]:text-blue-700 data-[state=active]:shadow-none rounded-none"
                >
                  ลงทะเบียน
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="login" className="p-6">
                <Form {...loginForm}>
                  <form onSubmit={loginForm.handleSubmit(onLoginSubmit)} className="space-y-5">
                    <FormField
                      control={loginForm.control}
                      name="username"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-slate-700 font-medium">ชื่อผู้ใช้</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <span className="absolute left-3 top-2.5 text-slate-400">
                                <User className="h-5 w-5" />
                              </span>
                              <Input
                                placeholder="ชื่อผู้ใช้ของคุณ"
                                className="pl-10 bg-white border-slate-300 focus:border-blue-400 text-slate-800"
                                value={field.value || ""}
                                onChange={field.onChange}
                                onBlur={field.onBlur}
                                name={field.name}
                                ref={field.ref}
                              />
                            </div>
                          </FormControl>
                          <FormMessage className="text-red-500" />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={loginForm.control}
                      name="password"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-slate-700 font-medium">รหัสผ่าน</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <span className="absolute left-3 top-2.5 text-slate-400">
                                <Lock className="h-5 w-5" />
                              </span>
                              <Input
                                type="password"
                                placeholder="รหัสผ่านของคุณ"
                                className="pl-10 bg-white border-slate-300 focus:border-blue-400 text-slate-800"
                                value={field.value || ""}
                                onChange={field.onChange}
                                onBlur={field.onBlur}
                                name={field.name}
                                ref={field.ref}
                              />
                            </div>
                          </FormControl>
                          <FormMessage className="text-red-500" />
                        </FormItem>
                      )}
                    />

                    <Button
                      type="submit"
                      className="w-full h-11 bg-blue-600 hover:bg-blue-700 text-white text-base font-medium"
                      disabled={loginMutation.isPending}
                    >
                      {loginMutation.isPending ? "กำลังเข้าสู่ระบบ..." : "เข้าสู่ระบบ"}
                    </Button>
                  </form>
                </Form>
              </TabsContent>
              
              <TabsContent value="register" className="p-6">
                <Form {...registerForm}>
                  <form onSubmit={registerForm.handleSubmit(onRegisterSubmit)} className="space-y-4">
                    <FormField
                      control={registerForm.control}
                      name="username"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-slate-700 font-medium">ชื่อผู้ใช้</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <span className="absolute left-3 top-2.5 text-slate-400">
                                <CircleUserRound className="h-5 w-5" />
                              </span>
                              <Input
                                placeholder="ชื่อผู้ใช้ที่ต้องการ"
                                className="pl-10 bg-white border-slate-300 focus:border-blue-400 text-slate-800"
                                value={field.value || ""}
                                onChange={field.onChange}
                                onBlur={field.onBlur}
                                name={field.name}
                                ref={field.ref}
                              />
                            </div>
                          </FormControl>
                          <FormMessage className="text-red-500" />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={registerForm.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-slate-700 font-medium">อีเมล</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <span className="absolute left-3 top-2.5 text-slate-400">
                                <AtSign className="h-5 w-5" />
                              </span>
                              <Input
                                type="email"
                                placeholder="<EMAIL>"
                                className="pl-10 bg-white border-slate-300 focus:border-blue-400 text-slate-800"
                                value={field.value || ""}
                                onChange={field.onChange}
                                onBlur={field.onBlur}
                                name={field.name}
                                ref={field.ref}
                              />
                            </div>
                          </FormControl>
                          <FormMessage className="text-red-500" />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={registerForm.control}
                        name="firstName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-slate-700 font-medium">ชื่อ</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="ชื่อของคุณ"
                                className="bg-white border-slate-300 focus:border-blue-400 text-slate-800"
                                value={field.value || ""}
                                onChange={field.onChange}
                                onBlur={field.onBlur}
                                name={field.name}
                                ref={field.ref}
                              />
                            </FormControl>
                            <FormMessage className="text-red-500" />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={registerForm.control}
                        name="lastName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-slate-700 font-medium">นามสกุล</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="นามสกุลของคุณ"
                                className="bg-white border-slate-300 focus:border-blue-400 text-slate-800"
                                value={field.value || ""}
                                onChange={field.onChange}
                                onBlur={field.onBlur}
                                name={field.name}
                                ref={field.ref}
                              />
                            </FormControl>
                            <FormMessage className="text-red-500" />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={registerForm.control}
                      name="companyName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-slate-700 font-medium">ชื่อบริษัท (ไม่บังคับ)</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <span className="absolute left-3 top-2.5 text-slate-400">
                                <Building className="h-5 w-5" />
                              </span>
                              <Input
                                placeholder="ชื่อบริษัทของคุณ"
                                className="pl-10 bg-white border-slate-300 focus:border-blue-400 text-slate-800"
                                value={field.value || ""}
                                onChange={field.onChange}
                                onBlur={field.onBlur}
                                name={field.name}
                                ref={field.ref}
                              />
                            </div>
                          </FormControl>
                          <FormMessage className="text-red-500" />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={registerForm.control}
                        name="password"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-slate-700 font-medium">รหัสผ่าน</FormLabel>
                            <FormControl>
                              <div className="relative">
                                <span className="absolute left-3 top-2.5 text-slate-400">
                                  <KeyRound className="h-5 w-5" />
                                </span>
                                <Input
                                  type="password"
                                  placeholder="รหัสผ่าน"
                                  className="pl-10 bg-white border-slate-300 focus:border-blue-400 text-slate-800"
                                  value={field.value || ""}
                                  onChange={field.onChange}
                                  onBlur={field.onBlur}
                                  name={field.name}
                                  ref={field.ref}
                                />
                              </div>
                            </FormControl>
                            <FormMessage className="text-red-500" />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={registerForm.control}
                        name="confirmPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-slate-700 font-medium">ยืนยันรหัสผ่าน</FormLabel>
                            <FormControl>
                              <div className="relative">
                                <span className="absolute left-3 top-2.5 text-slate-400">
                                  <KeyRound className="h-5 w-5" />
                                </span>
                                <Input
                                  type="password"
                                  placeholder="ยืนยันรหัสผ่าน"
                                  className="pl-10 bg-white border-slate-300 focus:border-blue-400 text-slate-800"
                                  value={field.value || ""}
                                  onChange={field.onChange}
                                  onBlur={field.onBlur}
                                  name={field.name}
                                  ref={field.ref}
                                />
                              </div>
                            </FormControl>
                            <FormMessage className="text-red-500" />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="flex items-start mt-2">
                      <div className="flex items-center h-5">
                        <input
                          id="terms"
                          type="checkbox"
                          className="h-4 w-4 border-slate-300 rounded text-blue-600 focus:ring-blue-500"
                          required
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <label htmlFor="terms" className="font-medium text-slate-700">
                          ฉันยอมรับข้อกำหนดและเงื่อนไขการใช้งาน
                        </label>
                      </div>
                    </div>

                    <Button
                      type="submit"
                      className="w-full h-11 bg-green-600 hover:bg-green-700 text-white text-base font-medium"
                      disabled={registerMutation.isPending}
                    >
                      {registerMutation.isPending ? "กำลังลงทะเบียน..." : "ลงทะเบียน"}
                    </Button>
                  </form>
                </Form>
              </TabsContent>
            </Tabs>
          </div>
          
          <div className="mt-6 text-center text-slate-500 text-sm">
            &copy; {new Date().getFullYear()} SLIPKUY. All rights reserved.
          </div>
        </div>
      </main>
    </div>
  );
}