#!/usr/bin/env node

/**
 * ไฟล์นี้ใช้สำหรับทดสอบการเชื่อมต่อกับฐานข้อมูล NeonDB
 * วิธีใช้: node scripts/test-db-connection.js
 */

// โหลด dotenv เพื่ออ่านค่าจากไฟล์ .env
require('dotenv').config();

const { Pool } = require('pg');

// ตรวจสอบว่ามีการตั้งค่า DATABASE_URL หรือไม่
const DATABASE_URL = process.env.DATABASE_URL;
if (!DATABASE_URL) {
  console.error('❌ ไม่พบ DATABASE_URL ในตัวแปรสภาพแวดล้อม');
  console.error('กรุณาตั้งค่า DATABASE_URL ในไฟล์ .env หรือตัวแปรสภาพแวดล้อม');
  process.exit(1);
}

console.log('🔍 กำลังทดสอบการเชื่อมต่อกับฐานข้อมูล...');
console.log(`🔗 DATABASE_URL: ${DATABASE_URL.replace(/:[^:]*@/, ':****@')}`);

// สร้าง connection pool
const pool = new Pool({
  connectionString: DATABASE_URL,
  ssl: {
    rejectUnauthorized: true,
  },
  connectionTimeoutMillis: 30000, // 30 วินาที
});

// ทดสอบการเชื่อมต่อ
async function testConnection() {
  let client;
  try {
    console.log('🔄 กำลังเชื่อมต่อกับฐานข้อมูล...');
    client = await pool.connect();
    console.log('✅ เชื่อมต่อกับฐานข้อมูลสำเร็จ');

    console.log('🔄 กำลังทดสอบคำสั่ง SQL...');
    const result = await client.query('SELECT NOW() as current_time');
    console.log(`✅ คำสั่ง SQL ทำงานสำเร็จ: เวลาปัจจุบันของฐานข้อมูลคือ ${result.rows[0].current_time}`);

    // ทดสอบการเข้าถึงตาราง
    console.log('🔄 กำลังตรวจสอบตารางในฐานข้อมูล...');
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `);
    
    if (tablesResult.rows.length === 0) {
      console.log('⚠️ ไม่พบตารางในฐานข้อมูล');
    } else {
      console.log(`✅ พบตารางในฐานข้อมูลทั้งหมด ${tablesResult.rows.length} ตาราง:`);
      tablesResult.rows.forEach((row, index) => {
        console.log(`   ${index + 1}. ${row.table_name}`);
      });
    }

    return true;
  } catch (err) {
    console.error('❌ เกิดข้อผิดพลาดในการเชื่อมต่อกับฐานข้อมูล:');
    console.error(err);
    return false;
  } finally {
    if (client) {
      console.log('🔄 กำลังปิดการเชื่อมต่อ...');
      client.release();
    }
    await pool.end();
    console.log('✅ ปิดการเชื่อมต่อเรียบร้อย');
  }
}

// รันฟังก์ชันทดสอบ
testConnection()
  .then(success => {
    if (success) {
      console.log('✅ การทดสอบการเชื่อมต่อกับฐานข้อมูลสำเร็จ');
      process.exit(0);
    } else {
      console.error('❌ การทดสอบการเชื่อมต่อกับฐานข้อมูลล้มเหลว');
      process.exit(1);
    }
  })
  .catch(err => {
    console.error('❌ เกิดข้อผิดพลาดที่ไม่คาดคิด:');
    console.error(err);
    process.exit(1);
  });
