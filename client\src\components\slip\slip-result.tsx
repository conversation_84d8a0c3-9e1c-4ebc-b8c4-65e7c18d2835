import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { CircleCheck, Circle, CircleX, CheckCircle2, Sparkles, Gem, BadgeCheck, <PERSON><PERSON><PERSON><PERSON><PERSON>, Clock, CalendarDays, ArrowRight } from "lucide-react";
import { formatDate } from "@/lib/utils";
import { motion, AnimatePresence } from "framer-motion";

// ประกาศ Type ของข้อมูลผลลัพธ์การตรวจสอบสลิป
interface SlipResultProps {
  result: {
    status: number;
    data?: {
      payload: string;
      transRef: string;
      date: string;
      countryCode: string;
      amount: {
        amount: number;
        local: {
          amount?: number;
          currency?: string;
        }
      };
      fee?: number;
      ref1?: string;
      ref2?: string;
      ref3?: string;
      sender: {
        bank: {
          id: string;
          name?: string;
          short?: string;
        };
        account: {
          name: {
            th?: string;
            en?: string;
          };
          bank?: {
            type: 'BANKAC' | 'TOKEN' | 'DUMMY';
            account: string;
          };
          proxy?: {
            type: 'NATID' | 'MSISDN' | 'EWALLETID' | 'EMAIL' | 'BILLERID';
            account: string;
          };
        };
      };
      receiver: {
        bank: {
          id: string;
          name?: string;
          short?: string;
        };
        account: {
          name: {
            th?: string;
            en?: string;
          };
          bank?: {
            type: 'BANKAC' | 'TOKEN' | 'DUMMY';
            account: string;
          };
          proxy?: {
            type: 'NATID' | 'MSISDN' | 'EWALLETID' | 'EMAIL' | 'BILLERID';
            account: string;
          };
        };
        merchantId?: string;
      };
    };
    message?: string;
  };
}

// ฟังก์ชันแสดงผลการตรวจสอบสลิป
export function SlipResult({ result }: SlipResultProps) {
  // ตรวจสอบว่ามีข้อมูลหรือไม่
  if (!result || (result.status !== 200 && !result.message)) {
    return null;
  }
  
  // กรณีมีข้อผิดพลาด แสดงข้อความแจ้งเตือน
  if (result.status !== 200) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.5 }}
      >
        <Card className="overflow-hidden backdrop-blur-md bg-gradient-to-br from-red-950/80 to-red-900/80 border-red-800/30 shadow-lg">
          <CardHeader className="relative">
            <div className="absolute inset-0 bg-gradient-to-br from-red-800/30 to-red-900/10"></div>
            <div className="relative z-10 flex items-center">
              <div className="mr-3 h-8 w-8 rounded-full bg-gradient-to-br from-red-600 to-red-800 flex items-center justify-center">
                <AlertTriangle className="h-4 w-4 text-red-100" />
              </div>
              <div>
                <CardTitle className="text-red-100">พลังเทพเจ้าพบข้อผิดพลาด</CardTitle>
                <CardDescription className="text-red-300">
                  รหัสข้อผิดพลาด: {result.status}
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-red-300">{result.message}</p>
            
            {result.message === 'slip_not_found' && (
              <motion.div 
                className="mt-4 text-sm text-red-300 bg-red-900/30 rounded-md p-3 border border-red-700/30"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2, duration: 0.4 }}
              >
                <p className="font-medium mb-2 flex items-center">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  สาเหตุที่เป็นไปได้:
                </p>
                <ul className="list-disc pl-5 mt-1 space-y-1">
                  <li>ไม่พบข้อมูลสลิปในคลังความรู้ของเทพเจ้า</li>
                  <li>รูปภาพสลิปอาจไม่ชัดเจนสำหรับดวงตาทิพย์</li>
                  <li>สลิปอาจเป็นของปลอมหรือรูปแบบถูกดัดแปลง</li>
                </ul>
                <p className="mt-2">โปรดตรวจสอบรูปภาพสลิปและขอพรเทพเจ้าอีกครั้ง</p>
              </motion.div>
            )}
          </CardContent>
        </Card>
      </motion.div>
    );
  }
  
  // กรณีตรวจสอบสำเร็จ แสดงข้อมูลสลิป
  const { data } = result;
  
  if (!data) {
    return null;
  }
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="overflow-hidden backdrop-blur-md bg-gradient-to-br from-green-950/80 to-emerald-900/80 border-green-800/30 shadow-lg relative">
        {/* เอฟเฟกต์แสงด้านหลัง */}
        <div className="absolute inset-0 bg-gradient-radial from-green-900/50 via-green-950 to-emerald-950"></div>
        
        {/* พาร์ติเคิลเล็กๆ */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {Array.from({ length: 15 }).map((_, i) => (
            <motion.div 
              key={i}
              className="absolute h-1 w-1 rounded-full bg-green-300"
              animate={{ 
                opacity: [Math.random() * 0.3 + 0.2, Math.random() * 0.7 + 0.3, Math.random() * 0.3 + 0.2],
                scale: [1, 1.5, 1]
              }}
              transition={{ 
                duration: Math.random() * 3 + 2,
                repeat: Infinity,
                repeatType: "reverse"
              }}
              style={{
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
                boxShadow: `0 0 ${Math.random() * 5 + 2}px ${Math.random() * 3 + 1}px rgba(134, 239, 172, 0.3)`
              }}
            />
          ))}
        </div>
        
        <CardHeader className="pb-3 relative z-10">
          <div className="flex justify-between items-start">
            <div className="flex items-center">
              <motion.div 
                className="relative mr-3"
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ 
                  type: "spring", 
                  stiffness: 300, 
                  damping: 20 
                }}
              >
                <div className="absolute -inset-1 rounded-full bg-green-600/30 animate-pulse blur-md"></div>
                <div className="relative bg-gradient-to-tr from-green-600 to-emerald-800 h-10 w-10 rounded-full flex items-center justify-center overflow-hidden">
                  <CheckCircle2 className="h-5 w-5 text-white" />
                  <div className="absolute inset-0 bg-gradient-to-br from-green-400/20 to-green-600/10"></div>
                </div>
              </motion.div>
              <div>
                <CardTitle className="text-transparent bg-clip-text bg-gradient-to-r from-green-300 to-green-500">
                  พลังเทพเจ้ายืนยันความถูกต้อง
                </CardTitle>
                <CardDescription className="text-green-300">
                  รหัสอ้างอิง: {data.transRef}
                </CardDescription>
              </div>
            </div>
            <Badge className="bg-green-800/70 text-green-100 hover:bg-green-800/80 border-green-700/50 flex items-center gap-1">
              <BadgeCheck className="h-3.5 w-3.5" />
              ยืนยันโดยเทพเจ้า
            </Badge>
          </div>
        </CardHeader>
        
        <CardContent className="relative z-10">
          <div className="space-y-4">
            <motion.div
              className="bg-gradient-to-br from-green-900/50 to-emerald-900/50 rounded-lg p-4 border border-green-800/30"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <div className="flex items-center mb-2">
                <div className="h-8 w-8 rounded-full bg-green-900/70 flex items-center justify-center mr-2">
                  <Gem className="h-4 w-4 text-green-400" />
                </div>
                <h3 className="text-green-200 font-medium">ธนาคารผู้ส่ง</h3>
              </div>
              <p className="text-lg font-semibold text-green-100">{data.sender.bank.name || `ธนาคารรหัส ${data.sender.bank.id}`}</p>
            </motion.div>
            
            <div className="grid grid-cols-2 gap-4">
              <motion.div
                className="bg-gradient-to-br from-green-900/50 to-emerald-900/50 rounded-lg p-4 border border-green-800/30"
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }}
              >
                <div className="flex items-center mb-2">
                  <div className="h-7 w-7 rounded-full bg-green-900/70 flex items-center justify-center mr-2">
                    <ArrowRight className="h-3.5 w-3.5 text-green-400" />
                  </div>
                  <h3 className="text-green-300 text-sm">ผู้ส่ง</h3>
                </div>
                <p className="text-green-100 font-semibold">{data.sender.account.name.th || data.sender.account.name.en || "ไม่ระบุ"}</p>
                {data.sender.account.bank && (
                  <p className="text-sm text-green-400">{data.sender.account.bank.account}</p>
                )}
              </motion.div>
              
              <motion.div
                className="bg-gradient-to-br from-green-900/50 to-emerald-900/50 rounded-lg p-4 border border-green-800/30"
                initial={{ opacity: 0, x: 10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }}
              >
                <div className="flex items-center mb-2">
                  <div className="h-7 w-7 rounded-full bg-green-900/70 flex items-center justify-center mr-2">
                    <ArrowRight className="h-3.5 w-3.5 text-green-400" />
                  </div>
                  <h3 className="text-green-300 text-sm">ผู้รับ</h3>
                </div>
                <p className="text-green-100 font-semibold">{data.receiver.account.name.th || data.receiver.account.name.en || "ไม่ระบุ"}</p>
                {data.receiver.account.bank && (
                  <p className="text-sm text-green-400">{data.receiver.account.bank.account}</p>
                )}
              </motion.div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <motion.div
                className="bg-gradient-to-br from-green-900/50 to-emerald-900/50 rounded-lg p-4 border border-green-800/30"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <div className="flex items-center mb-2">
                  <div className="h-7 w-7 rounded-full bg-green-900/70 flex items-center justify-center mr-2">
                    <CalendarDays className="h-3.5 w-3.5 text-green-400" />
                  </div>
                  <h3 className="text-green-300 text-sm">วันที่และเวลา</h3>
                </div>
                <p className="text-green-100 font-medium">{formatDate(new Date(data.date))}</p>
              </motion.div>
              
              <motion.div
                className="bg-gradient-to-br from-green-900/50 to-emerald-900/50 rounded-lg p-4 border border-green-800/30 relative overflow-hidden"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <div className="absolute -inset-2 bg-gradient-radial from-green-500/20 via-transparent to-transparent blur-md"></div>
                <div className="relative">
                  <div className="flex items-center mb-2">
                    <div className="h-7 w-7 rounded-full bg-green-900/70 flex items-center justify-center mr-2">
                      <Sparkles className="h-3.5 w-3.5 text-green-400" />
                    </div>
                    <h3 className="text-green-300 text-sm">จำนวนเงิน</h3>
                  </div>
                  <motion.p 
                    className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-300 to-green-500"
                    initial={{ scale: 0.8 }}
                    animate={{ scale: 1 }}
                    transition={{ 
                      type: "spring", 
                      stiffness: 300, 
                      damping: 20,
                      delay: 0.4
                    }}
                  >
                    {data.amount.amount.toLocaleString('th-TH')} บาท
                  </motion.p>
                </div>
              </motion.div>
            </div>
            
            {(data.ref1 || data.ref2 || data.ref3) && (
              <motion.div
                className="bg-gradient-to-br from-green-900/50 to-emerald-900/50 rounded-lg p-4 border border-green-800/30"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <div className="flex items-center mb-2">
                  <div className="h-7 w-7 rounded-full bg-green-900/70 flex items-center justify-center mr-2">
                    <Clock className="h-3.5 w-3.5 text-green-400" />
                  </div>
                  <h3 className="text-green-300 text-sm">ข้อมูลอ้างอิง</h3>
                </div>
                <div className="space-y-1">
                  {data.ref1 && <p className="text-sm text-green-200">Ref1: <span className="text-green-100">{data.ref1}</span></p>}
                  {data.ref2 && <p className="text-sm text-green-200">Ref2: <span className="text-green-100">{data.ref2}</span></p>}
                  {data.ref3 && <p className="text-sm text-green-200">Ref3: <span className="text-green-100">{data.ref3}</span></p>}
                </div>
              </motion.div>
            )}
            
            <div className="text-center mt-2">
              <motion.div
                className="inline-flex items-center text-xs text-green-400"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5 }}
              >
                <div className="relative mr-1">
                  <div className="absolute -inset-1 rounded-full bg-green-600/20 animate-pulse blur-sm"></div>
                  <Gem className="h-3 w-3 text-green-400 relative" />
                </div>
                ตรวจสอบโดยเทพเจ้าแห่ง SLIPKUY
              </motion.div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}