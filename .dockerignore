# Node.js
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
.next
.nuxt
.vuepress/dist

# IDE files
.vscode
.idea
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Backup files
backup/
backup_*/
backups/
*.sql
*.tar.gz

# Temporary files
tmp/
temp/
.tmp

# Nix files
*.nix
.direnv
.nix-profile
.nix-defexpr
.nix-channels
result
result-*

# Development files
attached_assets/
cookies.txt
mycookie.txt

# Documentation
*.md
!README.md

# Test files
test/
tests/
__tests__/
*.test.js
*.spec.js

# Config files
config.yml
