import { Express, Request, Response } from 'express';
import { storage } from '../storage';
import { InsertApiResponseTemplate } from '@shared/schema';
import { z } from 'zod';

// สคีมาสำหรับตรวจสอบข้อมูลที่รับเข้ามา
const createTemplateSchema = z.object({
  statusCode: z.number(),
  statusType: z.string(),
  message: z.string(),
  description: z.string().optional(),
  template: z.object({}).passthrough()
});

const updateTemplateSchema = z.object({
  statusCode: z.number().optional(),
  statusType: z.string().optional(),
  message: z.string().optional(),
  description: z.string().optional(),
  template: z.object({}).passthrough().optional()
});

// ฟังก์ชันสำหรับเรียกใช้ template API response
export async function getApiResponseTemplate(statusCode: number) {
  try {
    // ดึง template จากฐานข้อมูล
    const template = await storage.getApiResponseTemplateByStatusCode(statusCode);
    
    if (!template) {
      // ถ้าไม่มี template กำหนดค่าเริ่มต้น
      return {
        code: statusCode >= 200 && statusCode < 300 ? `${statusCode}000` : `${statusCode}001`,
        message: statusCode >= 200 && statusCode < 300 
          ? 'Success' 
          : 'Error',
        time: new Date().toISOString()
      };
    }
    
    // คืนค่า template ที่กำหนดไว้ในฐานข้อมูล (template.template เป็น jsonb)
    const templateObj = template.template as Record<string, any>;
    return {
      ...templateObj,
      time: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error getting API response template:', error);
    // คืนค่าเริ่มต้นในกรณีเกิดข้อผิดพลาด
    return {
      code: statusCode >= 200 && statusCode < 300 ? `${statusCode}000` : `${statusCode}001`,
      message: statusCode >= 200 && statusCode < 300 
        ? 'Success' 
        : 'Error',
      time: new Date().toISOString()
    };
  }
}

// ฟังก์ชันสำหรับสร้าง API response
export function createApiResponse(res: Response, statusCode: number, data?: any) {
  return getApiResponseTemplate(statusCode).then(template => {
    const response = {
      ...template,
      data
    };
    return res.status(statusCode).json(response);
  });
}

// เส้นทางสำหรับจัดการกับ API response templates
export function setupResponseTemplatesRoutes(app: Express) {
  // สร้าง response template ใหม่
  app.post('/api/admin/response-templates', async (req: Request, res: Response) => {
    try {
      // ตรวจสอบสิทธิ์
      if (!req.isAuthenticated() || !req.user?.isAdmin) {
        return res.status(403).json({
          status: 'error',
          message: 'ไม่มีสิทธิ์ในการเข้าถึง'
        });
      }
      
      // ตรวจสอบข้อมูล
      const validationResult = createTemplateSchema.safeParse(req.body);
      if (!validationResult.success) {
        return res.status(400).json({
          status: 'error',
          message: 'ข้อมูลไม่ถูกต้อง',
          errors: validationResult.error.errors
        });
      }
      
      // ตรวจสอบว่ามี template สำหรับ status code นี้อยู่แล้วหรือไม่
      const existingTemplate = await storage.getApiResponseTemplateByStatusCode(validationResult.data.statusCode);
      if (existingTemplate) {
        return res.status(409).json({
          status: 'error',
          message: `มี template สำหรับรหัสสถานะ ${validationResult.data.statusCode} อยู่แล้ว`
        });
      }
      
      // สร้าง template ใหม่
      const newTemplate = await storage.createApiResponseTemplate(validationResult.data as InsertApiResponseTemplate);
      
      return res.status(201).json({
        status: 'success',
        message: 'สร้าง API response template เรียบร้อยแล้ว',
        data: newTemplate
      });
    } catch (error) {
      console.error('Error creating API response template:', error);
      return res.status(500).json({
        status: 'error',
        message: 'เกิดข้อผิดพลาดในการสร้าง API response template',
        time: new Date().toISOString()
      });
    }
  });
  
  // ดึงรายการ response templates ทั้งหมด
  app.get('/api/admin/response-templates', async (req: Request, res: Response) => {
    try {
      // ตรวจสอบสิทธิ์
      if (!req.isAuthenticated() || !req.user?.isAdmin) {
        return res.status(403).json({
          status: 'error',
          message: 'ไม่มีสิทธิ์ในการเข้าถึง'
        });
      }
      
      // ดึงรายการ templates
      const templates = await storage.listApiResponseTemplates();
      
      return res.status(200).json({
        status: 'success',
        message: 'ดึงรายการ API response templates เรียบร้อยแล้ว',
        data: templates
      });
    } catch (error) {
      console.error('Error getting API response templates:', error);
      return res.status(500).json({
        status: 'error',
        message: 'เกิดข้อผิดพลาดในการดึงรายการ API response templates',
        time: new Date().toISOString()
      });
    }
  });
  
  // ดึง response template ตาม ID
  app.get('/api/admin/response-templates/:id', async (req: Request, res: Response) => {
    try {
      // ตรวจสอบสิทธิ์
      if (!req.isAuthenticated() || !req.user?.isAdmin) {
        return res.status(403).json({
          status: 'error',
          message: 'ไม่มีสิทธิ์ในการเข้าถึง'
        });
      }
      
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({
          status: 'error',
          message: 'ID ไม่ถูกต้อง'
        });
      }
      
      // ดึง template
      const template = await storage.getApiResponseTemplate(id);
      if (!template) {
        return res.status(404).json({
          status: 'error',
          message: 'ไม่พบ API response template'
        });
      }
      
      return res.status(200).json({
        status: 'success',
        message: 'ดึง API response template เรียบร้อยแล้ว',
        data: template
      });
    } catch (error) {
      console.error('Error getting API response template:', error);
      return res.status(500).json({
        status: 'error',
        message: 'เกิดข้อผิดพลาดในการดึง API response template',
        time: new Date().toISOString()
      });
    }
  });
  
  // อัปเดต response template
  app.put('/api/admin/response-templates/:id', async (req: Request, res: Response) => {
    try {
      // ตรวจสอบสิทธิ์
      if (!req.isAuthenticated() || !req.user?.isAdmin) {
        return res.status(403).json({
          status: 'error',
          message: 'ไม่มีสิทธิ์ในการเข้าถึง'
        });
      }
      
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({
          status: 'error',
          message: 'ID ไม่ถูกต้อง'
        });
      }
      
      // ตรวจสอบว่ามี template นี้อยู่หรือไม่
      const existingTemplate = await storage.getApiResponseTemplate(id);
      if (!existingTemplate) {
        return res.status(404).json({
          status: 'error',
          message: 'ไม่พบ API response template'
        });
      }
      
      // ตรวจสอบข้อมูล
      const validationResult = updateTemplateSchema.safeParse(req.body);
      if (!validationResult.success) {
        return res.status(400).json({
          status: 'error',
          message: 'ข้อมูลไม่ถูกต้อง',
          errors: validationResult.error.errors
        });
      }
      
      // อัปเดต template
      const updatedTemplate = await storage.updateApiResponseTemplate(id, validationResult.data);
      
      return res.status(200).json({
        status: 'success',
        message: 'อัปเดต API response template เรียบร้อยแล้ว',
        data: updatedTemplate
      });
    } catch (error) {
      console.error('Error updating API response template:', error);
      return res.status(500).json({
        status: 'error',
        message: 'เกิดข้อผิดพลาดในการอัปเดต API response template',
        time: new Date().toISOString()
      });
    }
  });
  
  // ลบ response template
  app.delete('/api/admin/response-templates/:id', async (req: Request, res: Response) => {
    try {
      // ตรวจสอบสิทธิ์
      if (!req.isAuthenticated() || !req.user?.isAdmin) {
        return res.status(403).json({
          status: 'error',
          message: 'ไม่มีสิทธิ์ในการเข้าถึง'
        });
      }
      
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({
          status: 'error',
          message: 'ID ไม่ถูกต้อง'
        });
      }
      
      // ตรวจสอบว่ามี template นี้อยู่หรือไม่
      const existingTemplate = await storage.getApiResponseTemplate(id);
      if (!existingTemplate) {
        return res.status(404).json({
          status: 'error',
          message: 'ไม่พบ API response template'
        });
      }
      
      // ลบ template
      await storage.deleteApiResponseTemplate(id);
      
      return res.status(200).json({
        status: 'success',
        message: 'ลบ API response template เรียบร้อยแล้ว'
      });
    } catch (error) {
      console.error('Error deleting API response template:', error);
      return res.status(500).json({
        status: 'error',
        message: 'เกิดข้อผิดพลาดในการลบ API response template',
        time: new Date().toISOString()
      });
    }
  });
}