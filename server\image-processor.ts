import sharp from 'sharp';
import { Worker, isMainThread, parentPort, workerData } from 'worker_threads';
import path from 'path';
import { logger } from './logger';

/**
 * ประมวลผลรูปภาพโดยใช้ worker thread
 * @param imageBuffer Buffer ของรูปภาพที่ต้องการประมวลผล
 * @param options ตัวเลือกในการประมวลผลรูปภาพ
 * @returns Promise<Buffer> Buffer ของรูปภาพที่ผ่านการประมวลผลแล้ว
 */
export async function processImage(
  imageBuffer: Buffer,
  options: {
    resize?: { width: number; height: number };
    grayscale?: boolean;
    sharpen?: boolean;
    normalize?: boolean;
  }
): Promise<Buffer> {
  // ถ้าไม่ได้อยู่ในโหมด production ให้ประมวลผลในเธรดหลัก
  if (process.env.NODE_ENV !== 'production') {
    return processImageInMainThread(imageBuffer, options);
  }

  return new Promise((resolve, reject) => {
    // สร้าง worker thread สำหรับประมวลผลรูปภาพ
    const worker = new Worker(__filename, {
      workerData: { imageBuffer, options },
    });

    // รับผลลัพธ์จาก worker thread
    worker.on('message', (result) => {
      resolve(Buffer.from(result.processedImageBuffer));
    });

    // จัดการกับข้อผิดพลาด
    worker.on('error', (err) => {
      logger.error('เกิดข้อผิดพลาดใน worker thread:', err);
      reject(err);
    });

    // เมื่อ worker thread ทำงานเสร็จ
    worker.on('exit', (code) => {
      if (code !== 0) {
        reject(new Error(`Worker thread exited with code ${code}`));
      }
    });
  });
}

/**
 * ประมวลผลรูปภาพในเธรดหลัก
 * @param imageBuffer Buffer ของรูปภาพที่ต้องการประมวลผล
 * @param options ตัวเลือกในการประมวลผลรูปภาพ
 * @returns Promise<Buffer> Buffer ของรูปภาพที่ผ่านการประมวลผลแล้ว
 */
async function processImageInMainThread(
  imageBuffer: Buffer,
  options: {
    resize?: { width: number; height: number };
    grayscale?: boolean;
    sharpen?: boolean;
    normalize?: boolean;
  }
): Promise<Buffer> {
  try {
    // สร้าง sharp instance
    let image = sharp(imageBuffer);

    // ปรับขนาดรูปภาพ
    if (options.resize) {
      image = image.resize(options.resize.width, options.resize.height, {
        fit: 'inside',
        withoutEnlargement: true,
      });
    }

    // แปลงเป็นภาพขาวดำ
    if (options.grayscale) {
      image = image.grayscale();
    }

    // เพิ่มความคมชัด
    if (options.sharpen) {
      image = image.sharpen();
    }

    // ปรับปรุงความเข้มของสี
    if (options.normalize) {
      image = image.normalize();
    }

    // แปลงเป็น buffer
    return await image.toBuffer();
  } catch (error) {
    logger.error('เกิดข้อผิดพลาดในการประมวลผลรูปภาพ:', error);
    throw error;
  }
}

// ถ้าเป็น worker thread ให้ประมวลผลรูปภาพและส่งผลลัพธ์กลับไปยังเธรดหลัก
if (!isMainThread && parentPort) {
  const { imageBuffer, options } = workerData;
  
  processImageInMainThread(imageBuffer, options)
    .then((processedImageBuffer) => {
      parentPort!.postMessage({ processedImageBuffer });
    })
    .catch((error) => {
      parentPort!.postMessage({ error: error.message });
    });
}
