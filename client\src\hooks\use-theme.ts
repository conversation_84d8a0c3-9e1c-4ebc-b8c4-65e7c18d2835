import { useState, useEffect } from 'react';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';

interface ThemeSettings {
  theme: 'dark' | 'light';
}

export function useTheme() {
  const [theme, setTheme] = useState<'dark' | 'light'>('dark'); // เนื่องจากธีมเริ่มต้นคือ dark
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  
  const isDarkMode = theme === 'dark';

  useEffect(() => {
    // โหลดธีมจากการตั้งค่าของผู้ใช้เมื่อคอมโพเนนต์ถูกโหลด
    const loadThemeSettings = async () => {
      try {
        setIsLoading(true);
        const response = await apiRequest('GET', '/api/user-settings');
        const data = await response.json();
        
        if (data && data.theme) {
          setTheme(data.theme);
          
          // อัปเดต HTML class
          document.documentElement.classList.toggle('dark', data.theme === 'dark');
          document.documentElement.classList.toggle('light', data.theme === 'light');
        }
      } catch (error) {
        console.error('Error loading theme settings:', error);
        // ถ้าเกิดข้อผิดพลาด ใช้ธีมเริ่มต้น (ไม่ต้องแสดง toast)
        document.documentElement.classList.add('dark');
      } finally {
        setIsLoading(false);
      }
    };

    loadThemeSettings();
  }, []);

  // ฟังก์ชันสำหรับสลับธีม
  const toggleTheme = async () => {
    const newTheme = theme === 'dark' ? 'light' : 'dark';
    
    try {
      const response = await apiRequest('PATCH', '/api/user-settings', {
        theme: newTheme
      });
      
      if (response.ok) {
        setTheme(newTheme);
        document.documentElement.classList.toggle('dark', newTheme === 'dark');
        document.documentElement.classList.toggle('light', newTheme === 'light');
        
        toast({
          title: 'เปลี่ยนธีมสำเร็จ',
          description: newTheme === 'dark' ? 'เปลี่ยนเป็นธีมมืดเรียบร้อยแล้ว' : 'เปลี่ยนเป็นธีมสว่างเรียบร้อยแล้ว',
        });
      }
    } catch (error) {
      console.error('Error updating theme:', error);
      toast({
        title: 'เกิดข้อผิดพลาด',
        description: 'ไม่สามารถเปลี่ยนธีมได้',
        variant: 'destructive'
      });
    }
  };

  // ฟังก์ชันสำหรับตั้งค่าธีมโดยตรง
  const setThemeDirectly = async (newTheme: 'dark' | 'light') => {
    if (newTheme === theme) return; // ไม่ต้องทำอะไรถ้าธีมไม่เปลี่ยน
    
    try {
      const response = await apiRequest('PATCH', '/api/user-settings', {
        theme: newTheme
      });
      
      if (response.ok) {
        setTheme(newTheme);
        document.documentElement.classList.toggle('dark', newTheme === 'dark');
        document.documentElement.classList.toggle('light', newTheme === 'light');
        
        toast({
          title: 'เปลี่ยนธีมสำเร็จ',
          description: newTheme === 'dark' ? 'เปลี่ยนเป็นธีมมืดเรียบร้อยแล้ว' : 'เปลี่ยนเป็นธีมสว่างเรียบร้อยแล้ว',
        });
      }
    } catch (error) {
      console.error('Error updating theme:', error);
      toast({
        title: 'เกิดข้อผิดพลาด',
        description: 'ไม่สามารถเปลี่ยนธีมได้',
        variant: 'destructive'
      });
    }
  };

  return {
    theme,
    isDarkMode,
    isLoading,
    toggleTheme,
    setTheme: setThemeDirectly
  };
}