import { useState, useEffect, useRef, useCallback } from 'react';

// Define message interface
interface WebSocketMessage {
  type: string;
  channel?: string;
  data?: any;
  message?: string;
  timestamp?: string;
}

/**
 * Custom hook สำหรับการใช้งาน WebSocket เพื่อรับข้อมูลแบบเรียลไทม์
 * 
 * @param channels รายการช่องที่ต้องการรับข้อมูล
 * @returns WebSocket state และฟังก์ชันที่เกี่ยวข้อง
 */
export function useWebSocket(channels: string[] = []) {
  const [isConnected, setIsConnected] = useState(false);
  const [messages, setMessages] = useState<WebSocketMessage[]>([]);
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const socketRef = useRef<WebSocket | null>(null);

  // สร้าง URL สำหรับ WebSocket
  const getWebSocketUrl = useCallback(() => {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    return `${protocol}//${host}/ws`;
  }, []);

  // สมัครรับข้อมูลจากช่องที่ระบุ
  const subscribe = useCallback((channel: string) => {
    if (socketRef.current?.readyState === WebSocket.OPEN) {
      console.log(`กำลังลงทะเบียนรับข้อมูลจากช่อง ${channel}`);
      socketRef.current.send(JSON.stringify({
        type: 'subscribe',
        channel
      }));
    } else {
      console.log(`ไม่สามารถลงทะเบียนช่อง ${channel} ได้ เนื่องจาก WebSocket ยังไม่เชื่อมต่อ`);
    }
  }, []);

  // ยกเลิกการรับข้อมูลจากช่องที่ระบุ
  const unsubscribe = useCallback((channel: string) => {
    if (socketRef.current?.readyState === WebSocket.OPEN) {
      socketRef.current.send(JSON.stringify({
        type: 'unsubscribe',
        channel
      }));
    }
  }, []);

  // ส่งข้อมูลไปยัง WebSocket server
  const sendMessage = useCallback((data: any) => {
    if (socketRef.current?.readyState === WebSocket.OPEN) {
      socketRef.current.send(JSON.stringify(data));
    } else {
      setError(new Error('WebSocket is not connected'));
    }
  }, []);

  // เชื่อมต่อกับ WebSocket server
  const connect = useCallback(() => {
    try {
      const wsUrl = getWebSocketUrl();
      const socket = new WebSocket(wsUrl);
      socketRef.current = socket;

      socket.onopen = () => {
        console.log('WebSocket connected');
        setIsConnected(true);
        setError(null);
        
        // สมัครรับข้อมูลจากช่องที่ระบุ
        channels.forEach(channel => {
          subscribe(channel);
        });
      };

      socket.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data) as WebSocketMessage;
          setLastMessage(message);
          setMessages(prev => [...prev, message]);
        } catch (err) {
          console.error('Error parsing WebSocket message:', err);
        }
      };

      socket.onerror = (event) => {
        console.error('WebSocket error:', event);
        setError(new Error('WebSocket connection error'));
      };

      socket.onclose = () => {
        console.log('WebSocket connection closed');
        setIsConnected(false);
        
        // ลองเชื่อมต่อใหม่อีกครั้งหลังจาก 5 วินาที (เพิ่มเวลารอเพื่อลดการเชื่อมต่อบ่อยเกินไป)
        setTimeout(() => {
          if (socketRef.current?.readyState !== WebSocket.OPEN) {
            console.log('Reconnecting to WebSocket...');
            connect();
          }
        }, 5000);
      };
    } catch (err) {
      console.error('Error creating WebSocket:', err);
      setError(err instanceof Error ? err : new Error('Unknown WebSocket error'));
    }
  }, [channels, getWebSocketUrl, subscribe]);

  // เชื่อมต่อเมื่อคอมโพเนนต์ถูกโหลด
  useEffect(() => {
    connect();

    // ทำความสะอาดเมื่อคอมโพเนนต์ถูกทำลาย
    return () => {
      if (socketRef.current) {
        socketRef.current.close();
      }
    };
  }, [connect]);

  // เมื่อมีการเปลี่ยนแปลงช่องที่ต้องการรับข้อมูล
  useEffect(() => {
    if (isConnected && socketRef.current) {
      channels.forEach(channel => {
        subscribe(channel);
      });
    }
  }, [channels, isConnected, subscribe]);

  return {
    isConnected,
    lastMessage,
    messages,
    error,
    sendMessage,
    subscribe,
    unsubscribe
  };
}