import { Switch, Route, useLocation } from "wouter";
import { useEffect } from "react";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { HelmetProvider } from "react-helmet-async";
import NotFound from "@/pages/not-found";
import HomePage from "@/pages/home-page";
import AuthPage from "@/pages/auth-page-epic"; // เปลี่ยนไปใช้หน้า auth แบบอลังการธีมเทพเจ้า
import AuthPageFixed from "@/pages/auth-page-fixed"; // หน้าล็อกอินและสมัครสมาชิกแบบใหม่ที่แก้ไขแล้ว
import AuthPageDivine from "@/pages/auth-page-divine"; // หน้าล็อกอินและสมัครสมาชิกแบบใหม่ล่าสุดมีเอฟเฟกต์เทพ
import PasswordResetPage from "@/pages/password-reset-page"; // หน้ารีเซ็ตรหัสผ่านแบบใหม่
import DashboardPage from "@/pages/dashboard-page";
import SlipVerifyPage from "@/pages/slip-verify-page";
import QRReaderPage from "@/pages/qr-reader-page";
import HistoryPage from "@/pages/history-page";
import AdvancedSearchPage from "@/pages/advanced-search-page"; // การอ้างอิงใหม่ไปยังหน้าค้นหาขั้นสูงที่เพิ่มเข้ามา
import AdvancedSearchSimplePage from "@/pages/advanced-search-simple"; // คงไว้เพื่อความเข้ากันได้กับโค้ดเก่า
import AdvancedSearchDivinePage from "@/pages/advanced-search-divine-new"; // หน้าค้นหาขั้นสูงระดับเทพเจ้า
import VerificationPage from "@/pages/verification-page"; // หน้ายืนยันตัวตน
import PackagesPage from "@/pages/packages-page";
import UserPackagesPage from "@/pages/user-packages-page";
import UserSettingsPage from "@/pages/user-settings-page"; // หน้าการตั้งค่าผู้ใช้
import ApiDocsPage from "@/pages/api-docs-page";
import PublicApiDocsPage from "@/pages/public-api-docs-page";
import ApiKeysPage from "@/pages/api-keys-page";
import ApiKeySettingsPage from "@/pages/api-key-settings-page";
import ApiTesterPage from "@/pages/api-tester-page-fix";
import TopUpPage from "@/pages/topup-page";
import ProfilePage from "@/pages/profile-page";
import ChangePasswordPage from "@/pages/change-password-page";
import NotificationsPage from "@/pages/notifications-page";
import FraudDetectionPage from "@/pages/fraud-detection-page";
import EmailSettingsPage from "@/pages/email-settings-page";
import WebhookServicePage from "@/pages/webhook-service-page";
import ApiAnalyticsPage from "@/pages/api-analytics-new";
import SmartDocsPage from "@/pages/smart-docs-page";
// ไม่ใช้หน้าทดสอบ Socket.IO อีกต่อไป
import SocketTestPage from "@/pages/socket-test-page"; // ยังคงต้องนำเข้ามาใช้ชั่วคราว
import AchievementsPage from "@/pages/achievements-page"; // หน้าความสำเร็จแบบ Gamified
import AdminDashboard from "@/pages/admin/admin-dashboard";
import UsersManagement from "@/pages/admin/users-management";
import UsersSimple from "@/pages/admin/users-simple";
import NewUsersManagement from "@/pages/admin/users-management-new";
import PackagesManagement from "@/pages/admin/packages-management";
import CouponsManagement from "@/pages/admin/coupons-management";
import SystemSettings from "@/pages/admin/system-settings";
import AuthSettingsPage from "@/pages/admin/auth-settings-page";
import Stats from "@/pages/admin/stats";
import AdminAnalyticsPage from "@/pages/admin/admin-analytics-page";
import UserPackageManagement from "@/pages/admin/user-package-management";
import UserPackagesApiManagement from "@/pages/admin/user-packages-api-management";
// ลบการนำเข้าที่เกี่ยวข้องกับหน้าค้นหาขั้นสูงระดับเทพเจ้า
import { AuthProvider } from "@/hooks/use-auth";
import { ProtectedRoute } from "./lib/protected-route";
import { GodlyNavbar } from "@/components/layouts/godly-navbar";
import { SessionCheck } from "@/components/session-check";
import { setupFetchInterceptor } from "./lib/api-error-handler";

function Router() {
  return (
    <Switch>
      {/* หน้าสาธารณะ */}
      <Route path="/" component={HomePage} />
      <Route path="/auth" component={AuthPageDivine} />
      <Route path="/auth-epic" component={AuthPage} />
      <Route path="/auth-fixed" component={AuthPageFixed} />
      <Route path="/password-reset" component={PasswordResetPage} />
      <Route path="/docs" component={PublicApiDocsPage} />
      <Route path="/packages" component={PackagesPage} />
      <Route path="/qr-reader" component={QRReaderPage} />
      {/* ถอดหน้าทดสอบ socket-test ออกแล้ว */}

      {/* หน้ายืนยันตัวตน */}
      <Route path="/verification" component={VerificationPage} />

      {/* หน้าที่ต้องเข้าสู่ระบบ */}
      <ProtectedRoute path="/dashboard" component={DashboardPage} />
      <ProtectedRoute path="/verify" component={SlipVerifyPage} />
      <ProtectedRoute path="/history" component={HistoryPage} />
      <ProtectedRoute path="/advanced-search" component={AdvancedSearchPage} />
      <ProtectedRoute path="/advanced-search-simple" component={AdvancedSearchSimplePage} />
      <ProtectedRoute path="/advanced-search-divine" component={AdvancedSearchDivinePage} />
      <ProtectedRoute path="/topup" component={TopUpPage} />
      <ProtectedRoute path="/user-packages" component={UserPackagesPage} />
      <ProtectedRoute path="/api-keys" component={ApiKeysPage} />
      <ProtectedRoute path="/api-key-settings/:id" component={ApiKeySettingsPage} />
      <ProtectedRoute path="/api-docs" component={ApiDocsPage} />
      <ProtectedRoute path="/api-tester" component={ApiTesterPage} />
      <ProtectedRoute path="/api-analytics" component={ApiAnalyticsPage} />
      <ProtectedRoute path="/smart-docs" component={SmartDocsPage} />
      <ProtectedRoute path="/webhook-service" component={WebhookServicePage} />
      <ProtectedRoute path="/profile" component={ProfilePage} />
      <ProtectedRoute path="/change-password" component={ChangePasswordPage} />
      <ProtectedRoute path="/notifications" component={NotificationsPage} />
      <ProtectedRoute path="/fraud-detection" component={FraudDetectionPage} />
      <ProtectedRoute path="/user-settings" component={UserSettingsPage} />
      <ProtectedRoute path="/achievements" component={AchievementsPage} />

      {/* หน้าแอดมิน */}
      <ProtectedRoute path="/admin" component={AdminDashboard} />
      <ProtectedRoute path="/admin/users" component={NewUsersManagement} />
      <ProtectedRoute path="/admin/users-old" component={UsersManagement} />
      <ProtectedRoute path="/admin/users-simple" component={UsersSimple} />
      <ProtectedRoute path="/admin/packages" component={PackagesManagement} />
      <ProtectedRoute path="/admin/coupons" component={CouponsManagement} />
      <ProtectedRoute path="/admin/user-package-management" component={UserPackageManagement} />
      <ProtectedRoute path="/admin/user-packages-api-management" component={UserPackagesApiManagement} />
      <ProtectedRoute path="/admin/settings" component={SystemSettings} />
      <ProtectedRoute path="/admin/auth-settings" component={AuthSettingsPage} />
      <ProtectedRoute path="/admin/email-settings" component={EmailSettingsPage} />
      <ProtectedRoute path="/admin/stats" component={Stats} />
      <ProtectedRoute path="/admin/analytics" component={AdminAnalyticsPage} />
      {/* ลบหน้าค้นหาขั้นสูงระดับเทพเจ้าเนื่องจากมีปัญหาเข้าถึงไฟล์ */}

      {/* หน้า 404 */}
      <Route component={NotFound} />
    </Switch>
  );
}

function MainLayout({ children }: { children: React.ReactNode }) {
  const [location, navigate] = useLocation();

  // ตั้งค่า Fetch Interceptor เพื่อจัดการข้อผิดพลาดและเปลี่ยนเส้นทางอัตโนมัติ
  useEffect(() => {
    // ใช้ setupFetchInterceptor จาก lib/api-error-handler
    setupFetchInterceptor(navigate);
  }, [navigate]);

  // ไม่แสดง Navbar เฉพาะหน้า dashboard, หน้าแอดมิน และหน้า auth
  const isDashboardPage = location === "/dashboard" ||
                          location.startsWith("/dashboard/") ||
                          location.startsWith("/verify") ||
                          location.startsWith("/history") ||
                          location.startsWith("/advanced-search") ||
                          location.startsWith("/topup") ||
                          location.startsWith("/user-packages") ||
                          location.startsWith("/api-keys") ||
                          location.startsWith("/api-key-settings") ||
                          location.startsWith("/api-tester") ||
                          location.startsWith("/api-analytics") ||
                          location.startsWith("/smart-docs") ||
                          location.startsWith("/webhook-service") ||
                          location.startsWith("/profile") ||
                          location.startsWith("/change-password") ||
                          location.startsWith("/notifications") ||
                          location.startsWith("/fraud-detection") ||
                          location.startsWith("/email-settings") ||
                          location.startsWith("/api-docs") ||
                          location.startsWith("/admin") ||
                          location.startsWith("/verification") ||
                          location.startsWith("/user-settings") ||
                          location.startsWith("/achievements") ||
                          location.startsWith("/password-reset") ||
                          location === "/auth";

  return (
    <>
      {!isDashboardPage && <GodlyNavbar />}
      {children}
      <Toaster />
    </>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <HelmetProvider>
          <MainLayout>
            <SessionCheck />
            <Router />
          </MainLayout>
        </HelmetProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;
