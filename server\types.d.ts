import { User, Api<PERSON>ey, UserPackage, Package } from "@shared/schema";

declare global {
  namespace Express {
    interface Request {
      apiKey?: ApiKey;
      apiUser?: User;
      apiPackage?: UserPackage & { package: Package };
      usedCredit?: boolean;
      creditUsed?: number;
    }
    
    interface User extends User {
      isAdmin?: boolean;
    }

    interface Session {
      ip?: string;
      userAgent?: string;
      location?: string;
      lastActivity?: string;
    }
  }
  
  // WebSocket broadcast function
  var broadcastWebsocketEvent: (channel: string, eventType: string, data: any) => void;
}

// Add to WebSocket for TypeScript
declare module 'ws' {
  // นิยามเพิ่มเติมสำหรับ WebSocket
  interface WebSocket {
    subscriptions?: string[];
  }
}