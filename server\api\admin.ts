import { Express, Request, Response, NextFunction } from "express";
import { storage } from "../storage";
import { isAdmin } from "../auth";

// ตรวจสอบว่า request มาจากผู้ดูแลระบบหรือไม่
function adminRequired(req: Request, res: Response, next: NextFunction) {
  if (req.isAuthenticated() && isAdmin(req)) {
    return next();
  }
  return res.status(403).json({ message: 'ไม่มีสิทธิ์เข้าถึง กรุณาเข้าสู่ระบบด้วยบัญชีผู้ดูแลระบบ' });
}

export function setupAdminRoutes(app: Express) {
  // API สำหรับการตั้งค่าระบบ (สำหรับผู้ดูแลระบบ)
  app.get("/api/admin/settings", adminRequired, async (req, res) => {
    try {
      // ดึงการตั้งค่าทั้งหมดจากฐานข้อมูล
      const settings = await storage.listSettings();
      res.status(200).json(settings);
    } catch (error) {
      console.error('Error fetching settings:', error);
      res.status(500).json({ message: 'เกิดข้อผิดพลาดในการดึงข้อมูลการตั้งค่า' });
    }
  });

  // API สำหรับบันทึกการตั้งค่าระบบ (สำหรับผู้ดูแลระบบ)
  app.post("/api/admin/settings", adminRequired, async (req, res) => {
    try {
      const { key, value } = req.body;

      if (!key) {
        return res.status(400).json({ message: 'กรุณาระบุคีย์ของการตั้งค่า' });
      }

      // บันทึกการตั้งค่าลงในฐานข้อมูล
      await storage.setSetting(key, value);

      res.status(200).json({ message: 'บันทึกการตั้งค่าเรียบร้อยแล้ว' });
    } catch (error) {
      console.error('Error saving setting:', error);
      res.status(500).json({ message: 'เกิดข้อผิดพลาดในการบันทึกการตั้งค่า' });
    }
  });

  // API สำหรับดึงการตั้งค่า polling interval (สำหรับ dashboard)
  app.get("/api/admin/settings/polling_interval", async (req, res) => {
    try {
      const setting = await storage.getSystemSetting('polling_interval');
      if (setting) {
        res.status(200).json(setting);
      } else {
        // ส่งค่าเริ่มต้น
        res.status(200).json({ key: 'polling_interval', value: '3000' });
      }
    } catch (error) {
      console.error('Error fetching polling interval:', error);
      res.status(500).json({ message: 'เกิดข้อผิดพลาดในการดึงข้อมูลการตั้งค่า' });
    }
  });
  // ดึงข้อมูลสถิติระบบสำหรับหน้า admin/stats
  app.get("/api/admin/stats", adminRequired, async (req, res) => {
    try {
      console.log("API: /api/admin/stats called by user ID:", (req.user as any)?.id);

      // สร้างข้อมูลสำหรับแสดงบนหน้าภาพรวม (dashboard)
      const mockStats = {
        users: {
          total: 5,
          active: 3,
          inactive: 1,
          suspended: 1
        },
        slips: {
          total: 10,
          success: 8,
          failed: 2
        },
        signupsByDay: [
          { date: new Date(Date.now() - 6 * 86400000).toISOString(), count: 1 },
          { date: new Date(Date.now() - 5 * 86400000).toISOString(), count: 0 },
          { date: new Date(Date.now() - 4 * 86400000).toISOString(), count: 2 },
          { date: new Date(Date.now() - 3 * 86400000).toISOString(), count: 1 },
          { date: new Date(Date.now() - 2 * 86400000).toISOString(), count: 0 },
          { date: new Date(Date.now() - 1 * 86400000).toISOString(), count: 1 },
          { date: new Date().toISOString(), count: 0 }
        ],
        apiUsageByDay: [
          { date: new Date(Date.now() - 6 * 86400000).toISOString(), count: 3 },
          { date: new Date(Date.now() - 5 * 86400000).toISOString(), count: 5 },
          { date: new Date(Date.now() - 4 * 86400000).toISOString(), count: 8 },
          { date: new Date(Date.now() - 3 * 86400000).toISOString(), count: 12 },
          { date: new Date(Date.now() - 2 * 86400000).toISOString(), count: 7 },
          { date: new Date(Date.now() - 1 * 86400000).toISOString(), count: 10 },
          { date: new Date().toISOString(), count: 5 }
        ],
        topUsers: [
          { userId: 1, username: "tmognot", email: "<EMAIL>", count: 15 },
          { userId: 2, username: "test", email: "<EMAIL>", count: 8 },
          { userId: 3, username: "asdasd", email: "<EMAIL>", count: 5 }
        ],
        topPackages: [
          { packageId: 1, packageName: "แพ็กเกจพรีเมียม", count: 2 },
          { packageId: 2, packageName: "แพ็กเกจรายเดือน", count: 1 },
          { packageId: 3, packageName: "แพ็กเกจฟรี", count: 3 }
        ]
      };

      // ส่งข้อมูลกลับไปยังไคลเอนต์
      return res.json(mockStats);
    } catch (error) {
      console.error("Error in /api/admin/stats:", error);
      return res.status(500).json({ message: "เกิดข้อผิดพลาดในการดึงข้อมูลสถิติ" });
    }
  });

  // ดึงข้อมูลผู้ใช้ทั้งหมด (สำหรับผู้ดูแลระบบเท่านั้น)
  app.get("/api/admin/users", adminRequired, async (req, res) => {
    try {
      const users = await storage.listUsers();
      res.status(200).json(users);
    } catch (error) {
      console.error('Error fetching users:', error);
      res.status(500).json({ message: 'เกิดข้อผิดพลาดในการดึงข้อมูลผู้ใช้' });
    }
  });

  // สร้างผู้ใช้งานใหม่ (สำหรับผู้ดูแลระบบเท่านั้น)
  app.post("/api/admin/users", adminRequired, async (req, res) => {
    try {
      const userData = req.body;

      // ตรวจสอบข้อมูลผู้ใช้ขั้นต่ำ
      if (!userData.username || !userData.password || !userData.email) {
        return res.status(400).json({ message: 'กรุณาระบุข้อมูลผู้ใช้ให้ครบถ้วน (username, password, email)' });
      }

      // ตรวจสอบว่าชื่อผู้ใช้ซ้ำหรือไม่
      const existingUser = await storage.getUserByUsername(userData.username);
      if (existingUser) {
        return res.status(400).json({ message: 'ชื่อผู้ใช้นี้มีอยู่ในระบบแล้ว' });
      }

      // สร้างผู้ใช้งานใหม่
      const newUser = await storage.createUser({
        ...userData,
        role: userData.role || 'user',
        status: userData.status || 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      });

      res.status(201).json(newUser);
    } catch (error) {
      console.error('Error creating user:', error);
      res.status(500).json({ message: 'เกิดข้อผิดพลาดในการสร้างผู้ใช้งานใหม่' });
    }
  });

  // ดึงข้อมูลผู้ใช้รายคน (สำหรับผู้ดูแลระบบเท่านั้น)
  app.get("/api/admin/users/:id", adminRequired, async (req, res) => {
    try {
      const userId = parseInt(req.params.id);
      if (isNaN(userId)) {
        return res.status(400).json({ message: 'รหัสผู้ใช้ไม่ถูกต้อง' });
      }

      const user = await storage.getUser(userId);

      if (!user) {
        return res.status(404).json({ message: 'ไม่พบผู้ใช้ที่ระบุ' });
      }

      res.status(200).json(user);
    } catch (error) {
      console.error('Error fetching user:', error);
      res.status(500).json({ message: 'เกิดข้อผิดพลาดในการดึงข้อมูลผู้ใช้' });
    }
  });

  // อัพเดทข้อมูลผู้ใช้ (สำหรับผู้ดูแลระบบเท่านั้น)
  app.patch("/api/admin/users/:id", adminRequired, async (req, res) => {
    try {
      const userId = parseInt(req.params.id);
      if (isNaN(userId)) {
        return res.status(400).json({ message: 'รหัสผู้ใช้ไม่ถูกต้อง' });
      }

      const user = await storage.getUser(userId);

      if (!user) {
        return res.status(404).json({ message: 'ไม่พบผู้ใช้ที่ระบุ' });
      }

      // อัพเดทข้อมูลผู้ใช้
      const updatedUser = await storage.updateUser(userId, req.body);

      if (!updatedUser) {
        return res.status(500).json({ message: 'เกิดข้อผิดพลาดในการอัพเดทข้อมูลผู้ใช้' });
      }

      res.status(200).json(updatedUser);
    } catch (error) {
      console.error('Error updating user:', error);
      res.status(500).json({ message: 'เกิดข้อผิดพลาดในการอัพเดทข้อมูลผู้ใช้' });
    }
  });

  // อัพเดทสิทธิ์การใช้แพ็กเกจของผู้ใช้ (สำหรับผู้ดูแลระบบเท่านั้น)
  app.patch("/api/admin/users/:id/allowed-packages", adminRequired, async (req, res) => {
    try {
      const userId = parseInt(req.params.id);
      if (isNaN(userId)) {
        return res.status(400).json({ message: 'รหัสผู้ใช้ไม่ถูกต้อง' });
      }

      const user = await storage.getUser(userId);

      if (!user) {
        return res.status(404).json({ message: 'ไม่พบผู้ใช้ที่ระบุ' });
      }

      // ตรวจสอบว่ามีการส่ง allowedPackages มาหรือไม่
      const { allowedPackages } = req.body;

      // อัพเดทสิทธิ์การใช้แพ็กเกจ
      const updatedUser = await storage.updateUserAllowedPackages(userId, allowedPackages);

      if (!updatedUser) {
        return res.status(500).json({ message: 'เกิดข้อผิดพลาดในการอัพเดทสิทธิ์การใช้แพ็กเกจ' });
      }

      res.status(200).json(updatedUser);
    } catch (error) {
      console.error('Error updating user allowed packages:', error);
      res.status(500).json({ message: 'เกิดข้อผิดพลาดในการอัพเดทสิทธิ์การใช้แพ็กเกจ' });
    }
  });

  // API สำหรับทดสอบการเชื่อมต่อกับ API Provider
  app.post("/api/admin/test-api-connection", adminRequired, async (req, res) => {
    try {
      const { provider } = req.body;

      if (!provider) {
        return res.status(400).json({ message: 'กรุณาระบุ API Provider ที่ต้องการทดสอบ' });
      }

      // สร้างรูปภาพทดสอบขนาดเล็ก (1x1 pixel)
      const testImageBuffer = Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64');

      // ทดสอบการเชื่อมต่อกับ API Provider
      const { slipService } = await import('../slip-service');

      try {
        if (provider === 'easyslip') {
          // ทดสอบการเชื่อมต่อกับ EasySlip API
          await slipService.verifyWithEasySlip(testImageBuffer, 'test.jpg');
        } else if (provider === 'slip2go') {
          // ทดสอบการเชื่อมต่อกับ Slip2Go API
          await slipService.verifyWithSlip2Go(testImageBuffer, 'test.jpg');
        } else {
          return res.status(400).json({ message: 'API Provider ไม่ถูกต้อง' });
        }

        // ถ้าไม่มีข้อผิดพลาด แสดงว่าเชื่อมต่อได้
        res.status(200).json({ message: 'เชื่อมต่อกับ API Provider สำเร็จ' });
      } catch (error) {
        // กรณีเกิดข้อผิดพลาดในการเชื่อมต่อ
        console.error(`Error testing API connection to ${provider}:`, error);
        res.status(500).json({ message: `ไม่สามารถเชื่อมต่อกับ ${provider} API ได้: ${error.message || 'ไม่ทราบสาเหตุ'}` });
      }
    } catch (error) {
      console.error('Error in test API connection:', error);
      res.status(500).json({ message: 'เกิดข้อผิดพลาดในการทดสอบการเชื่อมต่อ API' });
    }
  });

  // ดึงสถิติของระบบทั้งหมด (สำหรับผู้ดูแลระบบเท่านั้น)
  app.get("/api/admin/statistics", adminRequired, async (req, res) => {
    try {
      // ดึงจำนวนผู้ใช้ทั้งหมด
      const users = await storage.listUsers();
      const totalUsers = users.length;

      // ดึงจำนวนการตรวจสอบทั้งหมด
      const allVerifications = await storage.listAllSlipVerifications();
      const totalVerifications = allVerifications.length;

      // จำนวนการตรวจสอบที่สำเร็จ
      const successfulVerifications = allVerifications.filter(v => v.status === 'success').length;

      // จำนวนการตรวจสอบที่ไม่สำเร็จ
      const failedVerifications = allVerifications.filter(v => v.status === 'failed' || v.status === 'error').length;

      // จำนวนการตรวจสอบผ่านเว็บไซต์
      const webVerifications = allVerifications.filter(v => v.verificationSource === 'web').length;

      // จำนวนการตรวจสอบผ่าน API
      const apiVerifications = allVerifications.filter(v => v.verificationSource === 'api').length;

      // จำนวนยอดเงินที่ตรวจสอบทั้งหมด
      const totalAmount = allVerifications
        .filter(v => v.amount !== null && v.amount !== undefined)
        .reduce((sum, v) => sum + (v.amount || 0), 0);

      res.status(200).json({
        totalUsers,
        totalVerifications,
        successfulVerifications,
        failedVerifications,
        webVerifications,
        apiVerifications,
        totalAmount: Math.round(totalAmount * 100) / 100, // ปัดเศษทศนิยม 2 ตำแหน่ง
        successRate: totalVerifications > 0 ? (successfulVerifications / totalVerifications) * 100 : 0
      });
    } catch (error) {
      console.error('Error fetching admin statistics:', error);
      res.status(500).json({ message: 'เกิดข้อผิดพลาดในการดึงข้อมูลสถิติ' });
    }
  });
}