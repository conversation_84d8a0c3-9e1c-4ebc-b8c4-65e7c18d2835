import { useQuery } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { PackageCard } from "@/components/packages/package-card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Check,
  X,
  HelpCircle,
  Sparkles,
  Zap,
  Shield,
  Star,
  Crown,
  Sun,
  Gem,
  CloudLightning,
  Flame,
  DollarSign,
  Rocket,
  Heart,
  Diamond,
  ThumbsUp,
  Gift,
  Lightbulb,
  PalmtreeIcon,
  FlameKindling,
  ScrollText,
  Wand2,
  SparklesIcon,
  ShieldAlert,
} from "lucide-react";
import { useAuth } from "@/hooks/use-auth";
import { apiRequest } from "@/lib/queryClient";
import { motion, AnimatePresence } from "framer-motion";
import { useEffect, useState } from "react";


// คำฮาๆ เกี่ยวกับแพ็คเกจและโปรแกรมเมอร์
const epicQuotes = [
  // เกี่ยวกับแพ็คเกจและการใช้งาน
  "แพ็คเกจเรียกได้ว่าเหมือนสมาร์ทโฟนรุ่นใหม่ - ราคาเท่าเดิมแต่ทุกปีจะอัพเดทให้ช้าลงเรื่อยๆ",
  "แพ็คเกจของเราทำงานได้ 24/7 ยกเว้นช่วงที่คุณรีบจริงๆ",
  "แพ็คเกจฟรีของเรามาพร้อมกับฟีเจอร์ที่พัฒนาแล้วแต่ว่าไม่ค่อยได้เทสต์",
  "แพ็คเกจแต่ละระดับถูกออกแบบโดยโปรแกรมเมอร์แต่ละคน คุณสามารถทายได้เลยว่าใครทำงานดึกสุด",
  "แพ็คเกจเทพเจ้า เพราะมีแต่เทพเท่านั้นที่เข้าใจคู่มือการใช้งานได้ครบถ้วน",
  "แพ็คเกจของเราดีที่สุดในตลาด... หลังจากที่เรา fork คู่แข่งทั้งหมด แล้วลบ credit ออก",

  // เกี่ยวกับวิธีการพัฒนาและโปรแกรมเมอร์
  "โค้ดเราสะอาดมาก เพราะเราลบคอมเมนต์ออกทั้งหมดก่อนส่งให้ลูกค้า",
  "ระบบของเราเร็วเพราะเราติด if(slow) return; ไว้ทุกที่",
  "เราพยายามเขียนระบบแบบโอเพนซอร์ส แต่ลืมอัพโหลดขึ้น GitHub ซะแล้ว (อย่างน้อยก็ 5 ปีที่แล้ว)",
  "ระบบเรามีบั๊กน้อยมาก ที่น้อยเพราะเราเรียกมันว่า 'unexpected features' ต่างหาก",

  // เกี่ยวกับโปรแกรมเมอร์
  "ใช้แพ็คเกจเราแล้วจะเข้าใจว่าทำไมโปรแกรมเมอร์ถึงชอบกินพิซซ่า - เพราะมันส่งเร็วกว่าเวลา deploy แต่ละครั้ง",
  "เราลดบั๊กในระบบได้ 50% หลังจากที่ API Gateway ของเราล่ม และลูกค้าไม่สามารถส่งรีเควสได้",
  "แพ็คเกจนี้ถูกทดสอบโดยโปรแกรมเมอร์จริง และแก้บั๊กโดยอินเทิร์นที่เพิ่งเรียนจบ",
  "คนที่ออกแบบ UI ของเราคือคนเดียวกับคนที่เขียนระบบแบ็คเอนด์ จึงรับประกันได้ว่ามันใช้งานได้จริง... บนเครื่องเขา",

  // เกี่ยวกับการสนับสนุนแพ็คเกจต่างๆ
  "แพ็คเกจพื้นฐานของเรามีซัพพอร์ต 24/7... รับประกันว่าจะตอบอีเมลภายใน 24-168 ชั่วโมง",
  "ยิ่งคุณจ่ายมากเท่าไหร่ เราจะแกล้งทำเป็นเข้าใจคำถามของคุณมากขึ้นเท่านั้น",
  "ปัญหา: 'แพ็คเกจทำงานช้ามาก' คำตอบ: 'ลองปิดแล้วเปิดใหม่ดูหรือยัง?'",
  "แพ็คเกจตัวแพงสุดเราจะให้เบอร์โทรศัพท์ไดเร็คของ CEO เลย (แต่ทุกครั้งที่คุณโทรไป เขามีประชุมสำคัญพอดี)",
  "ซัพพอร์ตออนไลน์ของเราตอบไว เพราะทุกคำถามจะได้รับคำตอบว่า 'นี่เป็นฟีเจอร์ ไม่ใช่บั๊ก'",

  // เกี่ยวกับการอัพเดตและความเสถียร
  "อัพเดตล่าสุดของแพ็คเกจเรามีฟีเจอร์ใหม่ 0 อย่าง, แก้บั๊ก 0 ตัว และเพิ่มบั๊กอีก 12 ตัว",
  "เวอร์ชั่น 3.0 ของเราจะปฏิวัติวงการ! (โดยการเปลี่ยนไอคอนและทำให้ทุกอย่างช้าลง 30%)",
  "เราอัพเดตระบบทุกวันศุกร์ หลัง 18:00 น. เพราะโปรแกรมเมอร์คิดว่าไม่มีใครอยากใช้งานช่วงนั้น",
  "ความเสถียรคือสิ่งสำคัญ เราจึงไม่อัพเดตโค้ดตั้งแต่ปี 2019 (แม้จะมีช่องโหว่ความปลอดภัย 37 จุดก็ตาม)",
  "แพ็คเกจของเรารับประกันความเสถียร 99.9% - ที่เหลือ 0.1% มักจะเป็นตอนที่ลูกค้ามีการเดดไลน์สำคัญ",

  // เกี่ยวกับฟีเจอร์พิเศษ
  "แพ็คเกจเราพิเศษตรงที่มี 'ปุ่มตีกลับ' กรณีที่คุณเจอบั๊ก (ที่จริงมันแค่รีโหลดหน้าเว็บ)",
  "ไม่มีใครรู้ว่าเรามีฟีเจอร์ลับที่ซ่อนไว้ในเมนูลับ (เพราะเราก็ลืมมันไปแล้วเหมือนกัน)",
  "แพ็คเกจพรีเมียมของเรามีฟีเจอร์ 'โหมดเทพ' ที่แก้บั๊กได้เอง (จริงๆ คือมันแค่เขียนทับล็อกไฟล์ทุก 5 นาที)",
  "เราภูมิใจนำเสนอฟีเจอร์ 'แมชชีนเลิร์นนิ่ง' ในแพ็คเกจใหม่ล่าสุด (if-else ซ้อนกัน 500 บรรทัด ก็เรียกแมชชีนเลิร์นนิ่งได้แล้ว)",

  // เกี่ยวกับคุณภาพและการทดสอบ
  "เรารับประกันว่าโค้ดของเราผ่านการทดสอบทั้งหมด - โดยทดสอบด้วยวิธีรันแล้วดูว่าล่มมั้ย",
  "เรามี QA ทีมที่แข็งแกร่ง เพราะทุกคนที่เคยบ่นเรื่องบั๊กเราจะจ้างเขาทันที",
  "โค้ดของเราสะอาดมากจนกระทั่ง Linter ยังต้องร้องไห้",
  "ไม่มีบั๊กในแพ็คเกจของเรา มีแต่เส้นทางสู่ความสำเร็จที่ไม่มีใครคาดคิด",

  // เกี่ยวกับความคุ้มค่า
  "แพ็คเกจของเราคุ้มค่ากว่าเดิม - ราคาเพิ่มขึ้นแค่ 200% แต่มีฟีเจอร์เพิ่มขึ้น 2%",
  "สมัครวันนี้ รับส่วนลด 50%! (จากราคาที่เราเพิ่งขึ้นไป 100% เมื่อวาน)",
  "เราคำนวณความคุ้มค่าจากสูตร: จำนวนฟีเจอร์ × ราคา ÷ จำนวนบั๊กที่เจอ = คุ้มมากมาย!",
  "ลงทะเบียนเลย! รับฟรี! คอร์สสอนแก้บั๊กใน Stack Overflow มูลค่า 0 บาท!",

  // ล้อเลียนชีวิตโปรแกรมเมอร์
  "โค้ดของเราทำงานเหมือนโปรแกรมเมอร์ - ทำงานได้ดีเมื่อมีกาแฟ และล่มเมื่อต้องอธิบายว่าทำงานยังไง",
  "แพ็คเกจของเราเขียนด้วยความรักและกาแฟจำนวนมาก (ส่วนใหญ่เป็นกาแฟ)",
  "โปรแกรมเมอร์เราบอกว่าระบบเสร็จ 90% แล้ว... มา 3 เดือนแล้ว",
  "เราเข้าใจความรู้สึกคุณ เพราะโปรแกรมเมอร์เราร้องไห้เมื่อต้อง refactor โค้ดเก่าเหมือนกัน",
  "ระบบอัพเดตอัตโนมัติของเรามีประสิทธิภาพเหมือนโปรแกรมเมอร์ตอนวันจันทร์ - อาจทำงานหรือไม่ก็ได้",

  // เกี่ยวกับความปลอดภัย
  "ระบบรักษาความปลอดภัยของเราทำงานได้ดีมาก - แม้แต่โปรแกรมเมอร์ของเรายังเข้าระบบไม่ได้บางครั้ง",
  "เราใช้ระบบความปลอดภัยระดับสูงมาก รหัสผ่านจะถูกเข้ารหัสด้วย... เอ่อ... rot13 รอบที่สอง!",
  "ระบบของเราปลอดจากการโจมตี SQL injection เพราะแท้จริงแล้วเราใช้แค่ไฟล์ CSV",
  "ระบบเรามีหลายชั้นของความปลอดภัย - ชั้นแรกคือความสับสนของยูสเซอร์อินเตอร์เฟซที่ไม่มีใครเข้าใจ",

  // เกี่ยวกับเรื่องขำขัน IT
  "วิธีแก้ปัญหาที่ยอดเยี่ยมที่สุดของเรา คือการลบไฟล์ล็อก - ไม่เห็นไม่มีปัญหา",
  "แพ็คเกจของเราเถียงกับแพ็คเกจอื่นเสมอว่าใครใช้เมมโมรี่มากกว่ากัน (เราชนะประจำ)",
  "คำถาม: อะไรคือความแตกต่างระหว่างโปรแกรมเมอร์กับแมว? คำตอบ: แมวไม่คิดว่าตัวเองเป็นโปรแกรมเมอร์",
  "แพ็คเกจเราเขียนด้วยภาษาโปรแกรมมิ่งล่าสุด - เดิมทีเขียนด้วยภาษาเก่า แต่เรา find & replace ชื่อให้ทันสมัย",
  "โมดูล AI ของเราชาญฉลาดมาก - มันรู้ว่าเมื่อไรควรขัดข้องเพื่อให้ดูเหมือนว่ายังเป็นโปรแกรมที่มนุษย์เขียน",

  // คำขวัญและคำโฆษณาเกินจริง
  "SLIPKUY - เพราะการตรวจสอบสลิปด้วยตาเปล่าก็เหมือนการเขียนโค้ดบน Notepad",
  "เลือกเราสิ! เพราะแพ็คเกจอื่นแย่กว่านี้ (อาจจะ...)",
  "เว็บไซต์ไม่ตอบสนอง? แอพช้า? อย่าโทษแพ็คเกจเรา โทษอินเทอร์เน็ตคุณก่อน",
  "แพ็คเกจนี้ทำให้ชีวิตคุณง่ายขึ้น! (จากระดับ 'แทบเป็นไปไม่ได้' เป็น 'ยากมากๆ')",
  "สมัครแพ็คเกจวันนี้ แล้วคุณจะได้รู้ว่า API Key คืออะไร!",

  // การแซะระบบตัวเอง
  "API โค้ดของเราอ่านง่ายมาก เพราะตัดส่วนคอมเมนต์และเอกสารออกไปให้หมดแล้ว",
  "เราสนับสนุน serverless เพราะเซิร์ฟเวอร์เราล่มบ่อยเกินกว่าจะเรียกว่ามีเซิร์ฟเวอร์",
  "เวลาอัพเดตทุกอย่างอาจใช้เวลานานกว่าที่คาด ในระหว่างรอ คุณสามารถอ่านนิยายได้จบหนึ่งเล่ม",
  "เราภูมิใจในการแก้บั๊กให้ลูกค้า เพราะมันเป็นโอกาสให้เรารู้ว่าระบบเรามีฟีเจอร์อะไรบ้าง",
  "ระบบ API ของเราเป็น RESTful API จริงๆ - มันพักผ่อนเป็นส่วนใหญ่",

  // การล้อเลียนคำโฆษณาไอที
  "แพ็คเกจของเราใช้เทคโนโลยี cloud-native blockchain AI-driven IoT microservices! (หรือเราแค่อยากใส่คำฮิตเท่านั้น?)",
  "เราใช้ Machine Learning จริงๆ นะ - โค้ดเราจำได้ว่าควรล่มตอนไหน",
  "แพ็คเกจของเราช่วยให้ธุรกิจคุณเติบโตเร็วขึ้น 500%! (ตัวเลขนี้คิดมาเอง ไม่ได้มีการวิจัยรองรับ)",
  "มีคนบอกว่าแพ็คเกจเราเจ๋งกว่า ChatGPT! (คนคนนั้นคือแม่ของโปรแกรมเมอร์เรา)",
  "เราใช้ Big Data Analytics! (หมายถึงไฟล์ล็อกขนาด 2GB ที่ไม่มีใครกล้าเปิดดู)",
];

// ประกาศ Interface สำหรับข้อมูลแพ็กเกจ
interface Package {
  id: number;
  name: string;
  description: string;
  price: number;
  requestsLimit: number;
  isActive: boolean;
  features: string[];
  tag?: string; // เพิ่ม tag เพื่อแสดงป้าย
  duration_days?: number; // ระยะเวลาใช้งาน (วัน)
}

export default function PackagesPage() {
  const { user } = useAuth();
  const [_, navigate] = useLocation();
  const [randomQuote, setRandomQuote] = useState<string>("");
  const [floatingIcons, setFloatingIcons] = useState<
    { icon: JSX.Element; left: string; delay: number; duration: number }[]
  >([]);

  // ดึงข้อมูลแพ็กเกจ
  const { data: packages, isLoading } = useQuery<Package[]>({
    queryKey: ["/api/packages"],
  });

  // สร้างคำกวนๆ สุ่มเมื่อโหลดหน้า
  useEffect(() => {
    const randomIndex = Math.floor(Math.random() * epicQuotes.length);
    setRandomQuote(epicQuotes[randomIndex]);

    // สร้างไอคอนลอยๆ
    const icons = [
      {
        icon: <Crown className="text-amber-300" size={18} />,
        left: "10%",
        delay: 0,
        duration: 15,
      },
      {
        icon: <Sparkles className="text-purple-300" size={14} />,
        left: "25%",
        delay: 2,
        duration: 12,
      },
      {
        icon: <Star className="text-yellow-300" size={16} />,
        left: "40%",
        delay: 4,
        duration: 18,
      },
      {
        icon: <CloudLightning className="text-blue-300" size={16} />,
        left: "60%",
        delay: 1,
        duration: 14,
      },
      {
        icon: <Flame className="text-orange-400" size={16} />,
        left: "75%",
        delay: 3,
        duration: 16,
      },
      {
        icon: <Diamond className="text-cyan-300" size={14} />,
        left: "85%",
        delay: 5,
        duration: 20,
      },
      {
        icon: <Heart className="text-pink-400" size={12} />,
        left: "15%",
        delay: 6,
        duration: 17,
      },
      {
        icon: <Gift className="text-rose-300" size={14} />,
        left: "65%",
        delay: 2.5,
        duration: 13,
      },
      {
        icon: <ThumbsUp className="text-teal-300" size={15} />,
        left: "30%",
        delay: 4.5,
        duration: 19,
      },
      {
        icon: <Rocket className="text-purple-400" size={16} />,
        left: "50%",
        delay: 1.5,
        duration: 11,
      },
    ];
    setFloatingIcons(icons);
  }, []);

  // นำเอาข้อความคุณสมบัติมาเป็นออบเจ็กต์
  const parseFeatures = (
    features: string[] | undefined,
  ): { name: string; included: boolean }[] => {
    if (!features) return [];

    return features.map((feature) => {
      const included = !feature.startsWith("!");
      const name = included ? feature : feature.substring(1);
      return { name, included };
    });
  };

  // ไปยังหน้าเข้าสู่ระบบเมื่อคลิกสมัครแพ็กเกจโดยยังไม่ได้เข้าสู่ระบบ
  const handleNotLoggedInSubscribe = () => {
    navigate("/auth");
  };

  // หลังจากสมัครแพ็กเกจสำเร็จ
  const handleSubscriptionSuccess = () => {
    navigate("/dashboard");
  };

  return (
    <div className="min-h-screen flex flex-col font-['Kanit']">

      <div className="flex-1 py-10 relative overflow-hidden">
        {/* พื้นหลังกาแล็กซี่ */}
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-950 via-purple-950 to-black z-0"></div>

        {/* ลายกาแล็กซีและดวงดาว */}
        <div className="absolute inset-0 overflow-hidden z-0">
          {/* ดาวระยิบระยับ */}
          <div
            className="absolute inset-0"
            style={{
              backgroundImage:
                "radial-gradient(white, rgba(255, 255, 255, 0.2) 2px, transparent 2px)",
              backgroundSize: "100px 100px",
            }}
          ></div>

          {/* เนบิวลาสีม่วง */}
          <div className="absolute top-[20%] right-[10%] w-[60%] h-[40%] rounded-full bg-gradient-to-r from-purple-800/20 via-fuchsia-700/30 to-purple-800/20 blur-[130px] animate-float"></div>

          {/* เนบิวลาสีน้ำเงิน */}
          <div className="absolute -bottom-[10%] left-[10%] right-[10%] h-[40%] rounded-full bg-gradient-to-r from-blue-900/30 via-cyan-800/20 to-indigo-900/30 blur-[150px] animate-float"></div>

          {/* เนบิวลาสีทอง */}
          <div className="absolute top-[10%] left-[5%] w-[40%] h-[30%] rounded-full bg-gradient-to-r from-amber-700/30 via-yellow-600/30 to-amber-700/30 blur-[130px] animate-float-slow"></div>
        </div>

        {/* ไอคอนลอยได้ */}
        <div className="absolute inset-0 overflow-hidden z-10 pointer-events-none">
          {floatingIcons.map((item, index) => (
            <motion.div
              key={index}
              className="absolute"
              style={{ left: item.left }}
              initial={{ y: "120vh", opacity: 0 }}
              animate={{
                y: "-20vh",
                opacity: [0, 1, 1, 0],
                x: ["-10px", "10px", "-5px", "5px", "-10px"],
              }}
              transition={{
                duration: item.duration,
                delay: item.delay,
                repeat: Infinity,
                repeatDelay: 2,
                ease: "linear",
              }}
            >
              {item.icon}
            </motion.div>
          ))}
        </div>

        {/* เอฟเฟกต์สายฟ้าที่มุมจอ */}
        <motion.div
          className="absolute top-20 right-10 h-40 w-2 bg-amber-300 z-10 origin-top"
          initial={{ scaleY: 0, opacity: 0 }}
          animate={{
            scaleY: [0, 1, 0.5, 0.8, 0],
            opacity: [0, 0.8, 0.4, 0.6, 0],
          }}
          transition={{
            duration: 1,
            times: [0, 0.2, 0.3, 0.4, 0.5],
            repeat: Infinity,
            repeatDelay: 10,
          }}
          style={{
            clipPath: "polygon(0 0, 100% 0, 100% 100%, 0 100%)",
            filter: "blur(1px)",
          }}
        />

        <div className="container relative mx-auto px-4 md:px-6 lg:px-8 z-20">
          {/* หัวข้อหลัก */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            className="text-center mb-10 md:mb-16 max-w-3xl mx-auto mt-10"
          >
            <div className="relative mb-6 inline-block">
              <motion.div
                className="absolute -inset-4 rounded-full bg-gradient-to-r from-amber-600/30 via-pink-500/30 to-purple-600/30 blur-xl"
                animate={{
                  scale: [1, 1.1, 1],
                  rotate: [0, 5, 0],
                }}
                transition={{
                  duration: 5,
                  repeat: Infinity,
                  repeatType: "reverse",
                }}
              />
              <motion.div
                className="relative bg-gradient-to-r from-amber-400 to-yellow-300 rounded-full p-4 inline-block shadow-lg shadow-amber-500/20"
                whileHover={{ rotate: 15, scale: 1.1 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <SparklesIcon className="h-10 w-10 text-indigo-900" />
              </motion.div>
            </div>

            <h1 className="text-5xl md:text-6xl font-extrabold mb-4 relative inline-block">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-amber-300 via-yellow-200 to-amber-400">
                แพ็กเกจเทพเจ้า
              </span>
              <motion.div
                className="absolute -top-5 -right-10 text-amber-300"
                animate={{
                  y: [0, -5, 0],
                  rotate: [0, 10, 0],
                }}
                transition={{ duration: 4, repeat: Infinity }}
              >
                <Crown className="h-9 w-9" />
              </motion.div>
            </h1>

            <p className="text-xl md:text-2xl text-white/80 relative z-10 mb-6">
              เพิ่มอำนาจเทพให้ธุรกิจคุณ{" "}
              <span className="text-amber-300 font-semibold">
                เริ่มได้ทันที
              </span>{" "}
              ไม่มีข้อผูกมัด
            </p>

            {/* คำคมกวนๆ แสดงตรงกลาง */}
            <motion.div
              className="bg-gradient-to-r from-indigo-900/60 via-purple-900/60 to-indigo-900/60 p-4 rounded-lg border border-indigo-700/40 max-w-3xl mx-auto text-center backdrop-blur-sm relative overflow-hidden"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.5 }}
              whileHover={{ scale: 1.02 }}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-purple-600/5 via-fuchsia-600/5 to-amber-600/5 opacity-80"></div>
              <div className="relative z-10">
                <Wand2 className="h-5 w-5 text-amber-300 inline-block mr-2 mb-1" />
                <span className="italic text-lg md:text-xl text-indigo-100 font-light">
                  {randomQuote}
                </span>
              </div>
              <motion.div
                className="absolute bottom-0 left-0 h-[2px] bg-gradient-to-r from-amber-400 via-fuchsia-500 to-purple-500"
                initial={{ width: "0%" }}
                animate={{ width: "100%" }}
                transition={{ duration: 3 }}
              />
            </motion.div>
          </motion.div>

          {/* คุณสมบัติเด่นของแพ็กเกจ - แถบด้านบน */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            <motion.div
              whileHover={{ y: -5, scale: 1.03 }}
              className="flex items-center text-sm sm:text-base font-medium text-white bg-gradient-to-r from-indigo-900/70 to-purple-900/70 backdrop-blur-md rounded-full py-2.5 px-4 shadow-lg border border-indigo-400/20"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <ShieldAlert className="h-5 w-5 text-amber-300 mr-2" />
              <span>ปกป้องโดยเทพเจ้าแห่งความปลอดภัย</span>
            </motion.div>

            <motion.div
              whileHover={{ y: -5, scale: 1.03 }}
              className="flex items-center text-sm sm:text-base font-medium text-white bg-gradient-to-r from-indigo-900/70 to-purple-900/70 backdrop-blur-md rounded-full py-2.5 px-4 shadow-lg border border-indigo-400/20"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <Zap className="h-5 w-5 text-amber-300 mr-2" />
              <span>ใช้งานได้ทันทีหลังจากได้รับพร</span>
            </motion.div>

            <motion.div
              whileHover={{ y: -5, scale: 1.03 }}
              className="flex items-center text-sm sm:text-base font-medium text-white bg-gradient-to-r from-indigo-900/70 to-purple-900/70 backdrop-blur-md rounded-full py-2.5 px-4 shadow-lg border border-indigo-400/20"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <FlameKindling className="h-5 w-5 text-amber-300 mr-2" />
              <span>เพิ่มพลังแห่งความสำเร็จให้ธุรกิจคุณ</span>
            </motion.div>

            <motion.div
              whileHover={{ y: -5, scale: 1.03 }}
              className="flex items-center text-sm sm:text-base font-medium text-white bg-gradient-to-r from-indigo-900/70 to-purple-900/70 backdrop-blur-md rounded-full py-2.5 px-4 shadow-lg border border-indigo-400/20"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              <DollarSign className="h-5 w-5 text-amber-300 mr-2" />
              <span>คำอวยพรจากเทพเจ้าแห่งความมั่งคั่ง</span>
            </motion.div>
          </div>

          {/* แท็บเลือกระยะเวลา */}
          <Tabs defaultValue="monthly" className="mb-8">
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
              className="flex justify-center"
            >
              <TabsList className="grid w-full max-w-2xl grid-cols-4 mx-auto backdrop-blur-lg bg-indigo-900/40 p-1.5 shadow-xl rounded-full border border-indigo-400/30">
                <TabsTrigger
                  value="monthly"
                  className="rounded-full font-medium text-white data-[state=active]:bg-gradient-to-r data-[state=active]:from-amber-400 data-[state=active]:to-yellow-300 data-[state=active]:text-indigo-900 data-[state=active]:font-bold data-[state=active]:shadow-lg hover:text-amber-300 transition-all py-2"
                >
                  1 เดือน
                </TabsTrigger>
                <TabsTrigger
                  value="3months"
                  className="rounded-full font-medium text-white data-[state=active]:bg-gradient-to-r data-[state=active]:from-amber-400 data-[state=active]:to-yellow-300 data-[state=active]:text-indigo-900 data-[state=active]:font-bold data-[state=active]:shadow-lg hover:text-amber-300 transition-all py-2"
                >
                  3 เดือน
                </TabsTrigger>
                <TabsTrigger
                  value="6months"
                  className="rounded-full font-medium text-white data-[state=active]:bg-gradient-to-r data-[state=active]:from-amber-400 data-[state=active]:to-yellow-300 data-[state=active]:text-indigo-900 data-[state=active]:font-bold data-[state=active]:shadow-lg hover:text-amber-300 transition-all py-2"
                >
                  6 เดือน
                </TabsTrigger>
                <TabsTrigger
                  value="yearly"
                  className="rounded-full font-medium text-white data-[state=active]:bg-gradient-to-r data-[state=active]:from-amber-400 data-[state=active]:to-yellow-300 data-[state=active]:text-indigo-900 data-[state=active]:font-bold data-[state=active]:shadow-lg hover:text-amber-300 transition-all py-2"
                >
                  1 ปี
                </TabsTrigger>
              </TabsList>
            </motion.div>

            {/* เนื้อหาแท็บรายเดือน */}
            <TabsContent value="monthly" className="mt-8">
              {isLoading ? (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  {[...Array(3)].map((_, index) => (
                    <div
                      key={index}
                      className="border border-indigo-500/20 rounded-xl p-6 bg-indigo-900/30 backdrop-blur-md"
                    >
                      <Skeleton className="h-8 w-1/2 mb-1 bg-indigo-700/30" />
                      <Skeleton className="h-4 w-3/4 mb-4 bg-indigo-700/30" />
                      <Skeleton className="h-10 w-1/2 mb-6 bg-indigo-700/30" />
                      <div className="space-y-3 mb-6">
                        {[...Array(5)].map((_, i) => (
                          <div key={i} className="flex items-start">
                            <Skeleton className="h-5 w-5 mr-2 bg-indigo-700/30" />
                            <Skeleton className="h-5 w-full bg-indigo-700/30" />
                          </div>
                        ))}
                      </div>
                      <Skeleton className="h-10 w-full bg-indigo-700/30" />
                    </div>
                  ))}
                </div>
              ) : packages ? (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  {packages.map((pkg, index) => (
                    <PackageCard
                      key={pkg.id}
                      id={pkg.id}
                      name={pkg.name}
                      description={pkg.description}
                      price={pkg.price}
                      requestsLimit={pkg.requestsLimit}
                      isPopular={
                        pkg.name === "มืออาชีพ" || pkg.name === "ธรรมดา"
                      }
                      features={parseFeatures(pkg.features)}
                      tag={pkg.tag}
                      onSubscribe={() => {
                        if (user) {
                          const subscribeWithDuration = async () => {
                            try {
                              await apiRequest("POST", "/api/user/subscribe", {
                                packageId: pkg.id,
                                durationMonths: 1,
                              });
                              handleSubscriptionSuccess();
                            } catch (error) {
                              console.error("Error subscribing:", error);
                            }
                          };
                          subscribeWithDuration();
                        } else {
                          handleNotLoggedInSubscribe();
                        }
                      }}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-indigo-200">ไม่พบข้อมูลแพ็กเกจ</p>
                </div>
              )}
            </TabsContent>

            {/* เนื้อหาแท็บ 3 เดือน */}
            <TabsContent value="3months" className="mt-8">
              {isLoading ? (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  {[...Array(3)].map((_, index) => (
                    <div
                      key={index}
                      className="border border-indigo-500/20 rounded-xl p-6 bg-indigo-900/30 backdrop-blur-md"
                    >
                      <Skeleton className="h-8 w-1/2 mb-1 bg-indigo-700/30" />
                      <Skeleton className="h-4 w-3/4 mb-4 bg-indigo-700/30" />
                      <Skeleton className="h-10 w-1/2 mb-6 bg-indigo-700/30" />
                      <div className="space-y-3 mb-6">
                        {[...Array(5)].map((_, i) => (
                          <div key={i} className="flex items-start">
                            <Skeleton className="h-5 w-5 mr-2 bg-indigo-700/30" />
                            <Skeleton className="h-5 w-full bg-indigo-700/30" />
                          </div>
                        ))}
                      </div>
                      <Skeleton className="h-10 w-full bg-indigo-700/30" />
                    </div>
                  ))}
                </div>
              ) : packages ? (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  {packages.map((pkg, index) => (
                    <PackageCard
                      key={pkg.id}
                      id={pkg.id}
                      name={pkg.name}
                      description={pkg.description}
                      price={Math.round(pkg.price * 3 * 0.95)} // ลด 5%
                      requestsLimit={pkg.requestsLimit}
                      durationMonths={3}
                      isPopular={
                        pkg.name === "มืออาชีพ" || pkg.name === "ธรรมดา"
                      }
                      features={parseFeatures(pkg.features)}
                      tag={pkg.name === "มืออาชีพ" ? "ประหยัด 5%" : pkg.tag}
                      onSubscribe={() => {
                        if (user) {
                          const subscribeWithDuration = async () => {
                            try {
                              await apiRequest("POST", "/api/user/subscribe", {
                                packageId: pkg.id,
                                durationMonths: 3,
                              });
                              handleSubscriptionSuccess();
                            } catch (error) {
                              console.error("Error subscribing:", error);
                            }
                          };
                          subscribeWithDuration();
                        } else {
                          handleNotLoggedInSubscribe();
                        }
                      }}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-indigo-200">ไม่พบข้อมูลแพ็กเกจ</p>
                </div>
              )}
            </TabsContent>

            {/* เนื้อหาแท็บ 6 เดือน */}
            <TabsContent value="6months" className="mt-8">
              {isLoading ? (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  {[...Array(3)].map((_, index) => (
                    <div
                      key={index}
                      className="border border-indigo-500/20 rounded-xl p-6 bg-indigo-900/30 backdrop-blur-md"
                    >
                      <Skeleton className="h-8 w-1/2 mb-1 bg-indigo-700/30" />
                      <Skeleton className="h-4 w-3/4 mb-4 bg-indigo-700/30" />
                      <Skeleton className="h-10 w-1/2 mb-6 bg-indigo-700/30" />
                      <div className="space-y-3 mb-6">
                        {[...Array(5)].map((_, i) => (
                          <div key={i} className="flex items-start">
                            <Skeleton className="h-5 w-5 mr-2 bg-indigo-700/30" />
                            <Skeleton className="h-5 w-full bg-indigo-700/30" />
                          </div>
                        ))}
                      </div>
                      <Skeleton className="h-10 w-full bg-indigo-700/30" />
                    </div>
                  ))}
                </div>
              ) : packages ? (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  {packages.map((pkg, index) => (
                    <PackageCard
                      key={pkg.id}
                      id={pkg.id}
                      name={pkg.name}
                      description={pkg.description}
                      price={Math.round(pkg.price * 6 * 0.9)} // ลด 10%
                      requestsLimit={pkg.requestsLimit}
                      durationMonths={6}
                      isPopular={
                        pkg.name === "มืออาชีพ" || pkg.name === "ธรรมดา"
                      }
                      features={parseFeatures(pkg.features)}
                      tag={pkg.name === "มืออาชีพ" ? "ประหยัด 10%" : pkg.tag}
                      onSubscribe={() => {
                        if (user) {
                          const subscribeWithDuration = async () => {
                            try {
                              await apiRequest("POST", "/api/user/subscribe", {
                                packageId: pkg.id,
                                durationMonths: 6,
                              });
                              handleSubscriptionSuccess();
                            } catch (error) {
                              console.error("Error subscribing:", error);
                            }
                          };
                          subscribeWithDuration();
                        } else {
                          handleNotLoggedInSubscribe();
                        }
                      }}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-indigo-200">ไม่พบข้อมูลแพ็กเกจ</p>
                </div>
              )}
            </TabsContent>

            {/* เนื้อหาแท็บ 1 ปี */}
            <TabsContent value="yearly" className="mt-8">
              {isLoading ? (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  {[...Array(3)].map((_, index) => (
                    <div
                      key={index}
                      className="border border-indigo-500/20 rounded-xl p-6 bg-indigo-900/30 backdrop-blur-md"
                    >
                      <Skeleton className="h-8 w-1/2 mb-1 bg-indigo-700/30" />
                      <Skeleton className="h-4 w-3/4 mb-4 bg-indigo-700/30" />
                      <Skeleton className="h-10 w-1/2 mb-6 bg-indigo-700/30" />
                      <div className="space-y-3 mb-6">
                        {[...Array(5)].map((_, i) => (
                          <div key={i} className="flex items-start">
                            <Skeleton className="h-5 w-5 mr-2 bg-indigo-700/30" />
                            <Skeleton className="h-5 w-full bg-indigo-700/30" />
                          </div>
                        ))}
                      </div>
                      <Skeleton className="h-10 w-full bg-indigo-700/30" />
                    </div>
                  ))}
                </div>
              ) : packages ? (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  {packages.map((pkg, index) => (
                    <PackageCard
                      key={pkg.id}
                      id={pkg.id}
                      name={pkg.name}
                      description={pkg.description}
                      price={Math.round(pkg.price * 12 * 0.8)} // ลด 20%
                      requestsLimit={pkg.requestsLimit}
                      durationMonths={12}
                      isPopular={
                        pkg.name === "มืออาชีพ" || pkg.name === "ธรรมดา"
                      }
                      features={parseFeatures(pkg.features)}
                      tag={pkg.name === "มืออาชีพ" ? "ประหยัด 20%" : pkg.tag}
                      onSubscribe={() => {
                        if (user) {
                          const subscribeWithDuration = async () => {
                            try {
                              await apiRequest("POST", "/api/user/subscribe", {
                                packageId: pkg.id,
                                durationMonths: 12,
                              });
                              handleSubscriptionSuccess();
                            } catch (error) {
                              console.error("Error subscribing:", error);
                            }
                          };
                          subscribeWithDuration();
                        } else {
                          handleNotLoggedInSubscribe();
                        }
                      }}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-indigo-200">ไม่พบข้อมูลแพ็กเกจ</p>
                </div>
              )}
            </TabsContent>
          </Tabs>

          {/* ส่วนข้อมูลเพิ่มเติม */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.5 }}
            className="mt-16 mb-8 max-w-4xl mx-auto"
          >
            <div className="bg-gradient-to-r from-indigo-900/60 via-purple-900/60 to-indigo-900/60 rounded-xl backdrop-blur-md border border-indigo-700/30 p-6 md:p-8 shadow-lg">
              <h2 className="text-2xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-amber-300 to-yellow-300">
                คำถามที่พบบ่อย
              </h2>

              <div className="space-y-6">
                <div className="space-y-2">
                  <h3 className="text-xl font-semibold text-amber-300 flex items-center">
                    <HelpCircle className="h-5 w-5 mr-2 text-purple-400" />
                    พลังเทพเจ้าแห่ง API ทำงานอย่างไร?
                  </h3>
                  <p className="text-indigo-100 ml-7">
                    เทคโนโลยีแห่งเทพเจ้าของเราทำงานผ่าน API ที่ทรงพลัง
                    ตรวจสอบสลิปและข้อมูลทางการเงินโดยอัตโนมัติ
                    เพื่อให้คุณสามารถมุ่งเน้นไปที่การเติบโตของธุรกิจได้อย่างเต็มที่
                  </p>
                </div>

                <div className="space-y-2">
                  <h3 className="text-xl font-semibold text-amber-300 flex items-center">
                    <HelpCircle className="h-5 w-5 mr-2 text-purple-400" />
                    ราคาแพ็กเกจรวมภาษีหรือไม่?
                  </h3>
                  <p className="text-indigo-100 ml-7">
                    ราคาที่แสดงเป็นราคาที่รวมภาษีมูลค่าเพิ่มแล้ว
                    ไม่มีค่าใช้จ่ายแอบแฝงใดๆ ทั้งสิ้น
                    แม้แต่เทพเจ้ายังต้องยอมรับในความโปร่งใสของเรา
                  </p>
                </div>

                <div className="space-y-2">
                  <h3 className="text-xl font-semibold text-amber-300 flex items-center">
                    <HelpCircle className="h-5 w-5 mr-2 text-purple-400" />
                    เมื่อสมัครแล้ว สามารถยกเลิกได้หรือไม่?
                  </h3>
                  <p className="text-indigo-100 ml-7">
                    ท่านสามารถยกเลิกได้ทุกเมื่อ ไม่มีข้อผูกมัดใดๆ
                    เราเชื่อในเสรีภาพของการเลือก
                    แม้แต่เทพเจ้าก็ไม่บังคับให้คุณต้องอยู่กับเรา
                    (แต่เรารู้ว่าคุณจะอยู่กับเราแน่นอน)
                  </p>
                </div>

                <div className="space-y-2">
                  <h3 className="text-xl font-semibold text-amber-300 flex items-center">
                    <HelpCircle className="h-5 w-5 mr-2 text-purple-400" />
                    หากใช้งานเกินโควต้าในเดือนจะเกิดอะไรขึ้น?
                  </h3>
                  <p className="text-indigo-100 ml-7">
                    ระบบจะหักเครดิตโดยอัตโนมัติ คุณสามารถเติมเครดิตได้ตลอดเวลา
                    ไม่มีการปฏิเสธการให้บริการแม้โควต้าจะหมด
                    เพราะเทพเจ้าไม่เคยทอดทิ้งผู้ศรัทธา
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* ส่วนท้าย */}
      <div className="bg-gradient-to-t from-indigo-950 to-transparent py-8 relative z-20">
        <div className="container mx-auto px-4 text-center">
          <p className="text-indigo-300 text-sm">
            &copy; {new Date().getFullYear()} SLIPKUY - ทุกจักรวาลต้องก้มกราบ!
          </p>
          <p className="mt-2 text-xs text-indigo-400">
            ไม่มีเทพเจ้าตนใดถูกทำร้ายระหว่างการสร้างหน้านี้
          </p>
        </div>
      </div>
    </div>
  );
}
