import { useState, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { 
  Dialog, 
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { apiRequest } from "@/lib/queryClient";
import { queryClient } from "@/lib/queryClient";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useToast } from "@/hooks/use-toast";
import { User as UserType, Package as PackageType, UserPackage as UserPackageType, ApiKey as ApiKeyType } from "@shared/schema";
import { formatDate, formatCurrency, translateUserStatus, translateUserRole } from "@/lib/utils";
import {
  User,
  Mail,
  Building,
  Shield,
  UserCog,
  Package,
  PackagePlus,
  CreditCard,
  Key,
  BarChart4,
  History,
  RefreshCw,
  Calendar as CalendarIcon,
  Check,
  Plus,
  Minus,
  RefreshCcw,
  AlertCircle,
  PlusCircle,
  RotateCw,
  X,
  Clock,
  Settings,
  Trash
} from "lucide-react";

// Schema สำหรับแก้ไขข้อมูลผู้ใช้
const editUserSchema = z.object({
  username: z.string().min(3, "ชื่อผู้ใช้ต้องมีอย่างน้อย 3 ตัวอักษร"),
  email: z.string().email("รูปแบบอีเมลไม่ถูกต้อง"),
  firstName: z.string().min(1, "กรุณากรอกชื่อ"),
  lastName: z.string().min(1, "กรุณากรอกนามสกุล"),
  companyName: z.string().optional(),
  status: z.enum(["active", "inactive", "suspended"]),
  role: z.enum(["user", "admin"])
});

type EditUserFormValues = z.infer<typeof editUserSchema>;

// Schema สำหรับเปลี่ยนรหัสผ่าน
const changePasswordSchema = z.object({
  newPassword: z.string().min(8, "รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร"),
  confirmPassword: z.string().min(8, "รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร")
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "รหัสผ่านไม่ตรงกัน",
  path: ["confirmPassword"]
});

type ChangePasswordFormValues = z.infer<typeof changePasswordSchema>;

// Schema สำหรับการเพิ่ม/หักเครดิต
const creditActionSchema = z.object({
  amount: z.preprocess(
    (val) => Number(val),
    z.number().positive("จำนวนเครดิตต้องมากกว่า 0")
  ),
  description: z.string().optional(),
});

type CreditActionFormValues = z.infer<typeof creditActionSchema>;

// Schema สำหรับเพิ่มแพ็คเกจให้ผู้ใช้
const addPackageSchema = z.object({
  packageId: z.preprocess(
    (val) => Number(val),
    z.number().positive("กรุณาเลือกแพ็คเกจ")
  ),
  durationMonths: z.preprocess(
    (val) => Number(val),
    z.number().int().positive("ระยะเวลาต้องเป็นจำนวนเต็มบวก")
  ),
  isActive: z.boolean().default(true),
});

type AddPackageFormValues = z.infer<typeof addPackageSchema>;

// Schema สำหรับเพิ่ม API Key ใหม่
const addApiKeySchema = z.object({
  name: z.string().min(1, "กรุณาระบุชื่อ API Key"),
  description: z.string().optional(),
  usageLimit: z.preprocess(
    (val) => val === "" ? 0 : Number(val),
    z.number().min(0, "จำนวนการใช้งานต้องไม่น้อยกว่า 0")
  ).optional(),
  ipWhitelist: z.string().optional().transform(val => 
    val ? val.split(",").map(ip => ip.trim()).filter(ip => ip) : undefined
  ),
  expiryDate: z.date().optional().nullable(),
});

type AddApiKeyFormValues = z.infer<typeof addApiKeySchema>;

interface UserDetailDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  userId: number | null;
}

export function UserDetailDialog({ open, onOpenChange, userId }: UserDetailDialogProps) {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("profile");
  
  // เมื่อมีการเปลี่ยนแท็บ
  const handleTabChange = (tab: string) => {
    console.log(`เปลี่ยนแท็บเป็น: ${tab}`);
    console.log('ข้อมูลแพคเกจที่มี:', packages);
    setActiveTab(tab);
  };
  const [creditAction, setCreditAction] = useState<'add' | 'deduct'>('add');
  const [userPackages, setUserPackages] = useState<(UserPackageType & { package: PackageType })[]>([]);
  const [userApiKeys, setUserApiKeys] = useState<ApiKeyType[]>([]);

  // ดึงข้อมูลผู้ใช้
  const { data: user, isLoading } = useQuery<UserType>({
    queryKey: ['/api/admin/users', userId],
    enabled: open && userId !== null,
    queryFn: async () => {
      const res = await apiRequest("GET", `/api/admin/users/${userId}`);
      return await res.json();
    }
  });

  // ดึงข้อมูลแพ็คเกจทั้งหมดในระบบ
  const { data: packages } = useQuery<PackageType[]>({
    queryKey: ['/api/packages'],
    enabled: open,
    queryFn: async () => {
      try {
        const res = await fetch('/api/packages', {
          credentials: 'include'
        });
        
        if (res.status === 401) {
          console.log('ไม่ได้รับอนุญาตให้เข้าถึงข้อมูลแพ็คเกจ, กำลังใช้ข้อมูลว่าง');
          return [];
        }
        
        if (!res.ok) {
          throw new Error(`Error fetching packages: ${res.status}`);
        }
        
        return await res.json();
      } catch (error) {
        console.error('เกิดข้อผิดพลาดในการดึงข้อมูลแพ็คเกจ:', error);
        return [];
      }
    }
  });
  
  // เมื่อมีการเปลี่ยนแท็บให้แสดงข้อมูลในคอนโซล
  useEffect(() => {
    if (activeTab === "packages") {
      console.log("เข้าแท็บแพ็คเกจ:", packages);
    }
  }, [activeTab, packages]);

  // ดึงข้อมูลประวัติการตรวจสอบสลิป
  const { data: slipVerifications } = useQuery<any[]>({
    queryKey: ['/api/admin/users', userId, 'verifications'],
    enabled: open && userId !== null && activeTab === "verification-history",
    queryFn: async () => {
      const res = await apiRequest("GET", `/api/admin/users/${userId}/verifications`);
      return await res.json();
    }
  });

  // ดึงข้อมูลประวัติการเติมเงิน
  const { data: transactions } = useQuery<any[]>({
    queryKey: ['/api/admin/users', userId, 'transactions'],
    enabled: open && userId !== null && activeTab === "topup-history",
    queryFn: async () => {
      const res = await apiRequest("GET", `/api/admin/users/${userId}/transactions`);
      return await res.json();
    }
  });
  
  // ดึงข้อมูลสถิติของผู้ใช้
  const { data: userStatsData } = useQuery<any>({
    queryKey: ['/api/admin/users', userId, 'stats'],
    enabled: open && userId !== null && activeTab === "stats",
    queryFn: async () => {
      const res = await apiRequest("GET", `/api/admin/users/${userId}/stats`);
      return await res.json();
    }
  });

  // โหลดข้อมูลแพ็คเกจและ API keys เมื่อเปลี่ยนแท็บ
  useEffect(() => {
    if (!open || !userId) return;
    
    if (activeTab === "packages" || activeTab === "add-package") {
      console.log("ดึงข้อมูลแพคเกจเมื่อเปลี่ยนแท็บเป็น", activeTab);
      fetchUserPackages();
    } else if (activeTab === "api-keys") {
      fetchUserApiKeys();
    } else if (activeTab === "stats") {
      fetchUserStats();
    }
  }, [activeTab, open, userId]);

  // ฟอร์มสำหรับแก้ไขข้อมูลผู้ใช้
  const editUserForm = useForm<EditUserFormValues>({
    resolver: zodResolver(editUserSchema),
    defaultValues: {
      username: "",
      email: "",
      firstName: "",
      lastName: "",
      companyName: "",
      status: "active",
      role: "user"
    }
  });

  // รีเซ็ตฟอร์มเมื่อข้อมูลผู้ใช้เปลี่ยน
  useEffect(() => {
    if (user) {
      editUserForm.reset({
        username: user.username,
        email: user.email,
        firstName: user.firstName || "",
        lastName: user.lastName || "",
        companyName: user.companyName || "",
        status: user.status as "active" | "inactive" | "suspended",
        role: user.role as "user" | "admin"
      });
    }
  }, [user]);

  // ฟอร์มสำหรับเปลี่ยนรหัสผ่าน
  const changePasswordForm = useForm<ChangePasswordFormValues>({
    resolver: zodResolver(changePasswordSchema),
    defaultValues: {
      newPassword: "",
      confirmPassword: ""
    }
  });

  // ฟอร์มสำหรับเพิ่ม/หักเครดิต
  const creditForm = useForm<CreditActionFormValues>({
    resolver: zodResolver(creditActionSchema),
    defaultValues: {
      amount: 0,
      description: ""
    }
  });

  // ฟอร์มสำหรับเพิ่มแพ็คเกจ
  const addPackageForm = useForm<AddPackageFormValues>({
    resolver: zodResolver(addPackageSchema),
    defaultValues: {
      packageId: 0,
      durationMonths: 1,
      isActive: true
    }
  });
  
  // รีเซ็ตฟอร์มเมื่อเปิดแท็บ add-package
  useEffect(() => {
    if (activeTab === "add-package" && packages && packages.length > 0) {
      console.log("รีเซ็ตฟอร์มเมื่อเปิดแท็บ add-package", packages);
      addPackageForm.reset({
        packageId: packages[0].id,
        durationMonths: 1,
        isActive: true
      });
    }
  }, [activeTab, packages]);

  // ฟอร์มสำหรับเพิ่ม API Key
  const addApiKeyForm = useForm<AddApiKeyFormValues>({
    resolver: zodResolver(addApiKeySchema),
    defaultValues: {
      name: "",
      description: "",
      usageLimit: undefined,
      ipWhitelist: undefined,
      expiryDate: null
    }
  });

  // ดึงข้อมูลแพ็คเกจของผู้ใช้
  const fetchUserPackages = async () => {
    if (!userId) return;
    
    try {
      const res = await apiRequest("GET", `/api/admin/users/${userId}/packages`);
      const data = await res.json();
      setUserPackages(data);
    } catch (error) {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: "ไม่สามารถดึงข้อมูลแพ็คเกจได้",
        variant: "destructive"
      });
    }
  };

  // ดึงข้อมูล API keys ของผู้ใช้
  const fetchUserApiKeys = async () => {
    if (!userId) return;
    
    try {
      const res = await apiRequest("GET", `/api/admin/users/${userId}/api-keys`);
      const data = await res.json();
      setUserApiKeys(data);
    } catch (error) {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: "ไม่สามารถดึงข้อมูล API keys ได้",
        variant: "destructive"
      });
    }
  };

  // ดึงข้อมูลสถิติการใช้งานของผู้ใช้
  const fetchUserStats = async () => {
    if (!userId) return;
    
    try {
      const res = await apiRequest("GET", `/api/admin/users/${userId}/stats`);
      // ใช้ queryClient เพื่ออัปเดตข้อมูลแทนการใช้ useState
      queryClient.setQueryData(['/api/admin/users', userId, 'stats'], await res.json());
    } catch (error) {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: "ไม่สามารถดึงข้อมูลสถิติการใช้งานได้",
        variant: "destructive"
      });
    }
  };

  // Mutation สำหรับอัปเดตข้อมูลผู้ใช้
  const updateUserMutation = useMutation({
    mutationFn: async (userData: Partial<UserType>) => {
      const res = await apiRequest("PATCH", `/api/admin/users/${userId}`, userData);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "อัปเดตข้อมูลสำเร็จ",
        description: "ข้อมูลผู้ใช้งานถูกอัปเดตเรียบร้อยแล้ว",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users'] });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users', userId] });
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับเปลี่ยนรหัสผ่าน
  const changePasswordMutation = useMutation({
    mutationFn: async (passwordData: { newPassword: string }) => {
      const res = await apiRequest("PATCH", `/api/admin/users/${userId}/password`, passwordData);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "เปลี่ยนรหัสผ่านสำเร็จ",
        description: "รหัสผ่านถูกเปลี่ยนเรียบร้อยแล้ว",
      });
      changePasswordForm.reset();
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับเพิ่มเครดิต
  const addCreditMutation = useMutation({
    mutationFn: async (data: CreditActionFormValues) => {
      const res = await apiRequest("POST", `/api/admin/users/${userId}/credit`, data);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "เพิ่มเครดิตสำเร็จ",
        description: `เพิ่มเครดิต ${formatCurrency(creditForm.getValues().amount)} บาท ให้กับ ${user?.username} เรียบร้อยแล้ว`,
      });
      creditForm.reset({ amount: 0, description: creditAction === 'add' ? 'เติมเครดิต' : 'หักเครดิต' });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users'] });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users', userId] });
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับหักเครดิต
  const deductCreditMutation = useMutation({
    mutationFn: async (data: CreditActionFormValues) => {
      const res = await apiRequest("POST", `/api/admin/users/${userId}/deduct-credit`, data);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "หักเครดิตสำเร็จ",
        description: `หักเครดิต ${formatCurrency(creditForm.getValues().amount)} บาท จาก ${user?.username} เรียบร้อยแล้ว`,
      });
      creditForm.reset({ amount: 0, description: creditAction === 'add' ? 'เติมเครดิต' : 'หักเครดิต' });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users'] });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users', userId] });
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับเพิ่มแพ็คเกจให้ผู้ใช้
  const addPackageMutation = useMutation({
    mutationFn: async (data: AddPackageFormValues) => {
      const res = await apiRequest("POST", `/api/admin/users/${userId}/packages`, data);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "เพิ่มแพ็คเกจสำเร็จ",
        description: "เพิ่มแพ็คเกจให้ผู้ใช้งานเรียบร้อยแล้ว",
      });
      addPackageForm.reset();
      fetchUserPackages();
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users'] });
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับรีเซ็ตยอดการใช้งานแพ็คเกจ
  const resetPackageUsageMutation = useMutation({
    mutationFn: async (packageId: number) => {
      const res = await apiRequest("POST", `/api/admin/users/${userId}/packages/${packageId}/reset-quota`);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "รีเซ็ตยอดการใช้งานสำเร็จ",
        description: "รีเซ็ตยอดการใช้งานแพ็คเกจเรียบร้อยแล้ว",
      });
      fetchUserPackages();
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับเปลี่ยนสถานะแพ็คเกจ (active/inactive)
  const togglePackageStatusMutation = useMutation({
    mutationFn: async ({ packageId, isActive }: { packageId: number; isActive: boolean }) => {
      const res = await apiRequest("PATCH", `/api/admin/users/${userId}/packages/${packageId}`, { isActive });
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "อัปเดตสถานะแพ็คเกจสำเร็จ",
        description: "อัปเดตสถานะแพ็คเกจเรียบร้อยแล้ว",
      });
      fetchUserPackages();
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับเพิ่ม API Key
  const addApiKeyMutation = useMutation({
    mutationFn: async (data: AddApiKeyFormValues) => {
      const res = await apiRequest("POST", `/api/admin/users/${userId}/api-keys`, data);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "เพิ่ม API Key สำเร็จ",
        description: "เพิ่ม API Key ให้ผู้ใช้งานเรียบร้อยแล้ว",
      });
      addApiKeyForm.reset();
      fetchUserApiKeys();
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับลบ API Key
  const deleteApiKeyMutation = useMutation({
    mutationFn: async (apiKeyId: number) => {
      const res = await apiRequest("DELETE", `/api/admin/users/${userId}/api-keys/${apiKeyId}`);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "ลบ API Key สำเร็จ",
        description: "ลบ API Key เรียบร้อยแล้ว",
      });
      fetchUserApiKeys();
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับรีเจนเนอเรท API Key
  const regenerateApiKeyMutation = useMutation({
    mutationFn: async (apiKeyId: number) => {
      const res = await apiRequest("POST", `/api/admin/users/${userId}/api-keys/${apiKeyId}/regenerate`);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "สร้าง API Key ใหม่สำเร็จ",
        description: "API Key ถูกสร้างใหม่เรียบร้อยแล้ว",
      });
      fetchUserApiKeys();
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // ฟังก์ชันสำหรับบันทึกข้อมูลผู้ใช้
  const onSubmitEditForm = (data: EditUserFormValues) => {
    updateUserMutation.mutate(data);
  };

  // ฟังก์ชันสำหรับเปลี่ยนรหัสผ่าน
  const onSubmitChangePasswordForm = (data: ChangePasswordFormValues) => {
    changePasswordMutation.mutate({ newPassword: data.newPassword });
  };

  // ฟังก์ชันสำหรับเพิ่ม/หักเครดิต
  const onSubmitCreditForm = (data: CreditActionFormValues) => {
    if (creditAction === 'add') {
      addCreditMutation.mutate(data);
    } else {
      deductCreditMutation.mutate(data);
    }
  };

  // ฟังก์ชันสำหรับเพิ่มแพ็คเกจให้ผู้ใช้
  const onSubmitAddPackageForm = (data: AddPackageFormValues) => {
    addPackageMutation.mutate(data);
  };

  // ฟังก์ชันสำหรับเพิ่ม API Key
  const onSubmitAddApiKeyForm = (data: AddApiKeyFormValues) => {
    addApiKeyMutation.mutate(data);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center text-xl">
            <UserCog className="h-6 w-6 mr-2 text-primary" />
            จัดการผู้ใช้งาน: {user?.username}
          </DialogTitle>
          <DialogDescription>
            {user && (
              <div className="flex items-center gap-2 mt-2">
                <Badge variant={user.status === 'active' ? 'default' : user.status === 'inactive' ? 'outline' : 'destructive'}>
                  {translateUserStatus(user.status)}
                </Badge>
                <Badge variant={user.role === 'admin' ? 'default' : 'secondary'}>
                  {translateUserRole(user.role)}
                </Badge>
                <Badge variant="outline" className="ml-auto">
                  เครดิต: {formatCurrency(user.credit || 0)}
                </Badge>
              </div>
            )}
          </DialogDescription>
        </DialogHeader>
        
        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : (
          <Tabs defaultValue="profile" value={activeTab} onValueChange={handleTabChange} className="mt-2">
            <TabsList className="grid grid-cols-7 mb-4">
              <TabsTrigger value="profile" className="flex items-center gap-1">
                <User className="h-4 w-4" />
                <span className="hidden sm:inline">ข้อมูลทั่วไป</span>
              </TabsTrigger>
              <TabsTrigger value="packages" className="flex items-center gap-1">
                <Package className="h-4 w-4" />
                <span className="hidden sm:inline">แพ็คเกจ</span>
              </TabsTrigger>
              <TabsTrigger value="add-package" className="flex items-center gap-1">
                <PackagePlus className="h-4 w-4" />
                <span className="hidden sm:inline">เพิ่มแพ็คเกจ</span>
              </TabsTrigger>
              <TabsTrigger value="api-keys" className="flex items-center gap-1">
                <Key className="h-4 w-4" />
                <span className="hidden sm:inline">API Keys</span>
              </TabsTrigger>
              <TabsTrigger value="stats" className="flex items-center gap-1">
                <BarChart4 className="h-4 w-4" />
                <span className="hidden sm:inline">สถิติ</span>
              </TabsTrigger>
              <TabsTrigger value="verification-history" className="flex items-center gap-1">
                <History className="h-4 w-4" />
                <span className="hidden sm:inline">สลิป</span>
              </TabsTrigger>
              <TabsTrigger value="topup-history" className="flex items-center gap-1">
                <CreditCard className="h-4 w-4" />
                <span className="hidden sm:inline">เติมเงิน</span>
              </TabsTrigger>
            </TabsList>
            
            {/* แท็บข้อมูลทั่วไป */}
            <TabsContent value="profile" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center">
                      <User className="h-5 w-5 mr-2 text-primary" />
                      ข้อมูลส่วนตัว
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Form {...editUserForm}>
                      <form onSubmit={editUserForm.handleSubmit(onSubmitEditForm)} className="space-y-4">
                        <FormField
                          control={editUserForm.control}
                          name="username"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>ชื่อผู้ใช้</FormLabel>
                              <FormControl>
                                <Input placeholder="ชื่อผู้ใช้" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={editUserForm.control}
                          name="email"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>อีเมล</FormLabel>
                              <FormControl>
                                <Input type="email" placeholder="อีเมล" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <div className="grid grid-cols-2 gap-4">
                          <FormField
                            control={editUserForm.control}
                            name="firstName"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>ชื่อ</FormLabel>
                                <FormControl>
                                  <Input placeholder="ชื่อ" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          
                          <FormField
                            control={editUserForm.control}
                            name="lastName"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>นามสกุล</FormLabel>
                                <FormControl>
                                  <Input placeholder="นามสกุล" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        
                        <FormField
                          control={editUserForm.control}
                          name="companyName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>ชื่อบริษัท</FormLabel>
                              <FormControl>
                                <Input placeholder="ชื่อบริษัท" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <div className="flex justify-end">
                          <Button 
                            type="submit" 
                            disabled={updateUserMutation.isPending || !editUserForm.formState.isDirty}
                          >
                            {updateUserMutation.isPending && (
                              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                            )}
                            บันทึกข้อมูล
                          </Button>
                        </div>
                      </form>
                    </Form>
                  </CardContent>
                </Card>
                
                <div className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center">
                        <Shield className="h-5 w-5 mr-2 text-primary" />
                        สถานะและบทบาท
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Form {...editUserForm}>
                        <form className="space-y-4">
                          <FormField
                            control={editUserForm.control}
                            name="status"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>สถานะการใช้งาน</FormLabel>
                                <Select 
                                  onValueChange={field.onChange} 
                                  defaultValue={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="เลือกสถานะ" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="active">ใช้งาน</SelectItem>
                                    <SelectItem value="inactive">ไม่ใช้งาน</SelectItem>
                                    <SelectItem value="suspended">ระงับการใช้งาน</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          
                          <FormField
                            control={editUserForm.control}
                            name="role"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>บทบาท</FormLabel>
                                <Select 
                                  onValueChange={field.onChange} 
                                  defaultValue={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="เลือกบทบาท" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="user">ผู้ใช้งาน</SelectItem>
                                    <SelectItem value="admin">ผู้ดูแลระบบ</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          
                          <div className="flex justify-end">
                            <Button 
                              type="button" 
                              onClick={editUserForm.handleSubmit(onSubmitEditForm)}
                              disabled={updateUserMutation.isPending || !editUserForm.formState.isDirty}
                            >
                              {updateUserMutation.isPending && (
                                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                              )}
                              บันทึกสถานะ
                            </Button>
                          </div>
                        </form>
                      </Form>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center">
                        <AlertCircle className="h-5 w-5 mr-2 text-primary" />
                        เปลี่ยนรหัสผ่าน
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Form {...changePasswordForm}>
                        <form onSubmit={changePasswordForm.handleSubmit(onSubmitChangePasswordForm)} className="space-y-4">
                          <FormField
                            control={changePasswordForm.control}
                            name="newPassword"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>รหัสผ่านใหม่</FormLabel>
                                <FormControl>
                                  <Input type="password" placeholder="รหัสผ่านใหม่" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          
                          <FormField
                            control={changePasswordForm.control}
                            name="confirmPassword"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>ยืนยันรหัสผ่านใหม่</FormLabel>
                                <FormControl>
                                  <Input type="password" placeholder="ยืนยันรหัสผ่านใหม่" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          
                          <div className="flex justify-end">
                            <Button 
                              type="submit" 
                              disabled={changePasswordMutation.isPending}
                            >
                              {changePasswordMutation.isPending && (
                                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                              )}
                              เปลี่ยนรหัสผ่าน
                            </Button>
                          </div>
                        </form>
                      </Form>
                    </CardContent>
                  </Card>
                </div>
              </div>
              
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <CreditCard className="h-5 w-5 mr-2 text-primary" />
                    จัดการเครดิต
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="text-base font-medium">เครดิตคงเหลือ</h3>
                      <p className="text-2xl font-bold text-primary">{formatCurrency(user?.credit || 0)}</p>
                    </div>
                    <div className="flex gap-2">
                      <Button 
                        onClick={() => {
                          setCreditAction('add');
                          creditForm.reset({ 
                            amount: 0, 
                            description: 'เติมเครดิต' 
                          });
                        }} 
                        variant={creditAction === 'add' ? 'default' : 'outline'}
                        className="flex items-center"
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        เพิ่มเครดิต
                      </Button>
                      <Button 
                        onClick={() => {
                          setCreditAction('deduct');
                          creditForm.reset({ 
                            amount: 0, 
                            description: 'หักเครดิต' 
                          });
                        }} 
                        variant={creditAction === 'deduct' ? 'destructive' : 'outline'}
                        className="flex items-center"
                      >
                        <Minus className="h-4 w-4 mr-1" />
                        หักเครดิต
                      </Button>
                    </div>
                  </div>
                  
                  <Form {...creditForm}>
                    <form onSubmit={creditForm.handleSubmit(onSubmitCreditForm)} className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={creditForm.control}
                          name="amount"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>จำนวนเครดิต</FormLabel>
                              <FormControl>
                                <Input 
                                  type="number" 
                                  placeholder="จำนวนเครดิต" 
                                  min="1"
                                  {...field} 
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={creditForm.control}
                          name="description"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>คำอธิบาย</FormLabel>
                              <FormControl>
                                <Input 
                                  placeholder="คำอธิบายการเพิ่ม/หักเครดิต" 
                                  {...field} 
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      
                      <div className="flex justify-end">
                        <Button 
                          type="submit" 
                          disabled={
                            (creditAction === 'add' ? addCreditMutation.isPending : deductCreditMutation.isPending) ||
                            !creditForm.getValues().amount
                          }
                          variant={creditAction === 'add' ? 'default' : 'destructive'}
                        >
                          {(creditAction === 'add' ? addCreditMutation.isPending : deductCreditMutation.isPending) && (
                            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          )}
                          {creditAction === 'add' ? 'เพิ่มเครดิต' : 'หักเครดิต'}
                        </Button>
                      </div>
                    </form>
                  </Form>
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* แท็บแพ็คเกจ */}
            <TabsContent value="packages" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <Package className="h-5 w-5 mr-2 text-primary" />
                    แพ็คเกจที่ใช้งานอยู่
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {userPackages.length > 0 ? (
                    <div className="space-y-4">
                      {userPackages.map((userPackage) => (
                        <div key={userPackage.id} className="bg-muted/30 p-4 rounded-lg border">
                          <div className="flex justify-between items-start">
                            <div>
                              <h4 className="text-base font-medium">{userPackage.package.name}</h4>
                              <div className="mt-1 flex flex-wrap gap-x-4 gap-y-2 text-sm text-muted-foreground">
                                <span className="flex items-center">
                                  <CalendarIcon className="h-4 w-4 mr-1 text-primary" />
                                  เริ่มใช้: {formatDate(userPackage.startDate, "dd MMM yyyy")}
                                </span>
                                <span className="flex items-center">
                                  <Clock className="h-4 w-4 mr-1 text-primary" />
                                  สิ้นสุด: {formatDate(userPackage.endDate, "dd MMM yyyy")}
                                </span>
                                <span className="flex items-center">
                                  <RefreshCcw className="h-4 w-4 mr-1 text-primary" />
                                  ใช้ไปแล้ว: {userPackage.requestsUsed}/{userPackage.package.requestsLimit || 0} ครั้ง
                                </span>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => resetPackageUsageMutation.mutate(userPackage.id)}
                                disabled={resetPackageUsageMutation.isPending}
                                className="flex items-center"
                              >
                                <RotateCw className="h-4 w-4 mr-1" />
                                รีเซ็ตโควต้า
                              </Button>
                              <Button
                                variant={userPackage.isActive ? "destructive" : "default"}
                                size="sm"
                                onClick={() => togglePackageStatusMutation.mutate({ 
                                  packageId: userPackage.id, 
                                  isActive: !userPackage.isActive 
                                })}
                                disabled={togglePackageStatusMutation.isPending}
                                className="flex items-center"
                              >
                                {userPackage.isActive ? (
                                  <>
                                    <X className="h-4 w-4 mr-1" />
                                    ระงับการใช้งาน
                                  </>
                                ) : (
                                  <>
                                    <Check className="h-4 w-4 mr-1" />
                                    เปิดใช้งาน
                                  </>
                                )}
                              </Button>
                            </div>
                          </div>
                          <div className="mt-2">
                            <Badge variant={userPackage.isActive ? 'success' : 'destructive'}>
                              {userPackage.isActive ? 'กำลังใช้งาน' : 'ระงับการใช้งาน'}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="py-8 text-center text-muted-foreground">
                      ผู้ใช้รายนี้ยังไม่มีแพ็คเกจที่ใช้งาน
                    </div>
                  )}
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <PlusCircle className="h-5 w-5 mr-2 text-primary" />
                    เพิ่มแพ็คเกจใหม่
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Form {...addPackageForm}>
                    <form onSubmit={addPackageForm.handleSubmit(onSubmitAddPackageForm)} className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={addPackageForm.control}
                          name="packageId"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>เลือกแพ็คเกจ</FormLabel>
                              <Select 
                                onValueChange={(value) => field.onChange(parseInt(value))} 
                                defaultValue={field.value ? field.value.toString() : ""}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="เลือกแพ็คเกจ" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {packages?.map((pkg) => (
                                    <SelectItem key={pkg.id} value={pkg.id.toString()}>
                                      {pkg.name} ({formatCurrency(pkg.price || 0)})
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={addPackageForm.control}
                          name="durationMonths"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>ระยะเวลา (เดือน)</FormLabel>
                              <Select 
                                onValueChange={(value) => field.onChange(parseInt(value))} 
                                defaultValue={field.value.toString()}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="เลือกระยะเวลา" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="1">1 เดือน</SelectItem>
                                  <SelectItem value="3">3 เดือน (ส่วนลด)</SelectItem>
                                  <SelectItem value="6">6 เดือน (ส่วนลด)</SelectItem>
                                  <SelectItem value="12">12 เดือน (ส่วนลด)</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormDescription>
                                ส่วนลดจะถูกคำนวณตามระยะเวลาที่เลือก
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      
                      <FormField
                        control={addPackageForm.control}
                        name="isActive"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between p-3 border rounded-md">
                            <div className="space-y-0.5">
                              <FormLabel>สถานะการใช้งาน</FormLabel>
                              <FormDescription>
                                เปิดใช้งานแพ็คเกจนี้ทันทีหลังจากเพิ่ม
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                      
                      <div className="flex justify-end">
                        <Button 
                          type="submit" 
                          disabled={
                            addPackageMutation.isPending || 
                            !addPackageForm.getValues().packageId
                          }
                        >
                          {addPackageMutation.isPending && (
                            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          )}
                          เพิ่มแพ็คเกจ
                        </Button>
                      </div>
                    </form>
                  </Form>
                </CardContent>
              </Card>
            </TabsContent>

            {/* แท็บเพิ่มแพ็คเกจ */}
            <TabsContent value="add-package" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <Package className="h-5 w-5 mr-2 text-primary" />
                    เพิ่มแพ็คเกจให้ผู้ใช้
                  </CardTitle>
                  <CardDescription>
                    เลือกแพ็คเกจและระยะเวลาการใช้งานเพื่อเพิ่มให้กับผู้ใช้
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Form {...addPackageForm}>
                    <form onSubmit={addPackageForm.handleSubmit(onSubmitAddPackageForm)} className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={addPackageForm.control}
                          name="packageId"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>เลือกแพ็คเกจ</FormLabel>
                              <Select 
                                onValueChange={(value) => field.onChange(parseInt(value))} 
                                defaultValue={field.value ? field.value.toString() : ""}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="เลือกแพ็คเกจ" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {packages?.map((pkg) => (
                                    <SelectItem key={pkg.id} value={pkg.id.toString()}>
                                      {pkg.name} ({formatCurrency(pkg.price || 0)})
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={addPackageForm.control}
                          name="durationMonths"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>ระยะเวลา (เดือน)</FormLabel>
                              <Select 
                                onValueChange={(value) => field.onChange(parseInt(value))} 
                                defaultValue={field.value.toString()}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="เลือกระยะเวลา" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="1">1 เดือน</SelectItem>
                                  <SelectItem value="3">3 เดือน (ส่วนลด)</SelectItem>
                                  <SelectItem value="6">6 เดือน (ส่วนลด)</SelectItem>
                                  <SelectItem value="12">12 เดือน (ส่วนลด)</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormDescription>
                                ส่วนลดจะถูกคำนวณตามระยะเวลาที่เลือก
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      
                      <FormField
                        control={addPackageForm.control}
                        name="isActive"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between p-3 border rounded-md">
                            <div className="space-y-0.5">
                              <FormLabel>สถานะการใช้งาน</FormLabel>
                              <FormDescription>
                                เปิดใช้งานแพ็คเกจนี้ทันทีหลังจากเพิ่ม
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                      
                      <div className="flex justify-end">
                        <Button 
                          type="submit" 
                          disabled={
                            addPackageMutation.isPending || 
                            !addPackageForm.getValues().packageId
                          }
                        >
                          {addPackageMutation.isPending && (
                            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          )}
                          เพิ่มแพ็คเกจ
                        </Button>
                      </div>
                    </form>
                  </Form>
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* แท็บ API Keys */}
            <TabsContent value="api-keys" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <Key className="h-5 w-5 mr-2 text-primary" />
                    API Keys ที่ใช้งานอยู่
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {userApiKeys?.length > 0 ? (
                    <div className="space-y-4">
                      {userApiKeys.map((apiKey) => (
                        <div key={apiKey.id} className="bg-muted/30 p-4 rounded-lg border">
                          <div className="flex justify-between items-start">
                            <div>
                              <h4 className="text-base font-medium">{apiKey.name}</h4>
                              <p className="text-sm text-muted-foreground">{apiKey.description || "-"}</p>
                              <div className="mt-2 font-mono text-xs bg-muted p-2 rounded border">
                                {apiKey.apiKey}
                              </div>
                              <div className="mt-2 flex flex-wrap gap-x-4 gap-y-1 text-xs text-muted-foreground">
                                {apiKey.lastUsed && (
                                  <span className="flex items-center">
                                    <Clock className="h-3 w-3 mr-1" />
                                    ใช้ล่าสุด: {formatDate(apiKey.lastUsed, "dd MMM yyyy HH:mm")}
                                  </span>
                                )}
                                {apiKey.usageCount !== undefined && (
                                  <span className="flex items-center">
                                    <RefreshCcw className="h-3 w-3 mr-1" />
                                    จำนวนการใช้: {apiKey.usageCount} ครั้ง
                                  </span>
                                )}
                                {apiKey.expiresAt && (
                                  <span className="flex items-center">
                                    <CalendarIcon className="h-3 w-3 mr-1" />
                                    หมดอายุ: {formatDate(apiKey.expiresAt, "dd MMM yyyy")}
                                  </span>
                                )}
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => regenerateApiKeyMutation.mutate(apiKey.id)}
                                disabled={regenerateApiKeyMutation.isPending}
                                className="flex items-center"
                              >
                                <RotateCw className="h-4 w-4 mr-1" />
                                สร้างใหม่
                              </Button>
                              <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => deleteApiKeyMutation.mutate(apiKey.id)}
                                disabled={deleteApiKeyMutation.isPending}
                                className="flex items-center"
                              >
                                <Trash className="h-4 w-4 mr-1" />
                                ลบ
                              </Button>
                            </div>
                          </div>
                          <div className="mt-2">
                            <Badge variant={apiKey.status === 'active' ? 'success' : apiKey.status === 'inactive' ? 'outline' : 'destructive'}>
                              {apiKey.status === 'active' ? 'ใช้งาน' : apiKey.status === 'inactive' ? 'ไม่ใช้งาน' : 'ถูกเพิกถอน'}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="py-8 text-center text-muted-foreground">
                      ผู้ใช้รายนี้ยังไม่มี API Keys
                    </div>
                  )}
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <PlusCircle className="h-5 w-5 mr-2 text-primary" />
                    เพิ่ม API Key ใหม่
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Form {...addApiKeyForm}>
                    <form onSubmit={addApiKeyForm.handleSubmit(onSubmitAddApiKeyForm)} className="space-y-4">
                      <FormField
                        control={addApiKeyForm.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>ชื่อ API Key</FormLabel>
                            <FormControl>
                              <Input placeholder="ชื่อ API Key" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={addApiKeyForm.control}
                        name="description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>คำอธิบาย (ไม่บังคับ)</FormLabel>
                            <FormControl>
                              <Textarea placeholder="คำอธิบายการใช้งาน API Key นี้" {...field} value={field.value || ""} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={addApiKeyForm.control}
                          name="usageLimit"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>จำกัดจำนวนการใช้งาน (ไม่บังคับ)</FormLabel>
                              <FormControl>
                                <Input 
                                  type="number" 
                                  placeholder="จำนวนสูงสุดที่อนุญาตให้ใช้" 
                                  min="0"
                                  {...field}
                                  value={field.value === undefined ? "" : field.value}
                                />
                              </FormControl>
                              <FormDescription>
                                ปล่อยว่างไว้หากไม่ต้องการจำกัด
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={addApiKeyForm.control}
                          name="ipWhitelist"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>อนุญาต IP Address (ไม่บังคับ)</FormLabel>
                              <FormControl>
                                <Input 
                                  placeholder="***********, ********"
                                  {...field}
                                  value={field.value || ""}
                                />
                              </FormControl>
                              <FormDescription>
                                คั่นด้วยเครื่องหมาย ,
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      
                      <div className="flex justify-end">
                        <Button 
                          type="submit" 
                          disabled={
                            addApiKeyMutation.isPending || 
                            !addApiKeyForm.getValues().name
                          }
                        >
                          {addApiKeyMutation.isPending && (
                            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          )}
                          เพิ่ม API Key
                        </Button>
                      </div>
                    </form>
                  </Form>
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* แท็บสถิติการใช้งาน */}
            <TabsContent value="stats" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <BarChart4 className="h-5 w-5 mr-2 text-primary" />
                    สถิติการใช้งาน
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {userStats ? (
                    <div className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="bg-muted/30 p-4 rounded-lg border text-center">
                          <h3 className="text-sm font-medium text-muted-foreground">จำนวนการตรวจสอบทั้งหมด</h3>
                          <p className="text-3xl font-bold text-primary mt-2">{userStats.totalVerifications || 0}</p>
                        </div>
                        <div className="bg-muted/30 p-4 rounded-lg border text-center">
                          <h3 className="text-sm font-medium text-muted-foreground">การตรวจสอบสำเร็จ</h3>
                          <p className="text-3xl font-bold text-green-500 mt-2">{userStats.successfulVerifications || 0}</p>
                        </div>
                        <div className="bg-muted/30 p-4 rounded-lg border text-center">
                          <h3 className="text-sm font-medium text-muted-foreground">การตรวจสอบล้มเหลว</h3>
                          <p className="text-3xl font-bold text-red-500 mt-2">{userStats.failedVerifications || 0}</p>
                        </div>
                      </div>
                      
                      <div className="bg-muted/30 p-4 rounded-lg border">
                        <h3 className="text-base font-medium mb-2">การใช้งาน API</h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="bg-background p-3 rounded-md border text-center">
                            <h4 className="text-sm text-muted-foreground">เรียกใช้ API ทั้งหมด</h4>
                            <p className="text-xl font-bold mt-1">{userStats.apiUsage?.totalApiCalls || 0}</p>
                          </div>
                          <div className="bg-background p-3 rounded-md border text-center">
                            <h4 className="text-sm text-muted-foreground">การเรียกใช้สำเร็จ</h4>
                            <p className="text-xl font-bold text-green-500 mt-1">{userStats.apiUsage?.successfulApiCalls || 0}</p>
                          </div>
                          <div className="bg-background p-3 rounded-md border text-center">
                            <h4 className="text-sm text-muted-foreground">การเรียกใช้ล้มเหลว</h4>
                            <p className="text-xl font-bold text-red-500 mt-1">{userStats.apiUsage?.failedApiCalls || 0}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="py-8 text-center text-muted-foreground">
                      กำลังโหลดข้อมูลสถิติ...
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* แท็บประวัติการตรวจสอบสลิป */}
            <TabsContent value="verification-history" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <History className="h-5 w-5 mr-2 text-primary" />
                    ประวัติการตรวจสอบสลิป
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {slipVerifications?.length ? (
                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>วันที่</TableHead>
                            <TableHead>จำนวนเงิน</TableHead>
                            <TableHead>สถานะ</TableHead>
                            <TableHead>ที่มา</TableHead>
                            <TableHead>ใช้เครดิต</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {slipVerifications.map((verification) => (
                            <TableRow key={verification.id}>
                              <TableCell>
                                {formatDate(verification.createdAt, "dd/MM/yyyy HH:mm")}
                              </TableCell>
                              <TableCell>
                                {formatCurrency(verification.amount || 0)}
                              </TableCell>
                              <TableCell>
                                <Badge variant={
                                  verification.status === 'success' ? 'default' :
                                  verification.status === 'pending' ? 'outline' : 'destructive'
                                }>
                                  {verification.status === 'success' ? 'สำเร็จ' :
                                   verification.status === 'pending' ? 'รอดำเนินการ' : 'ล้มเหลว'}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                {verification.verificationSource === 'web' ? 'เว็บไซต์' : 'API'}
                              </TableCell>
                              <TableCell>
                                {verification.usedCredit ? `${verification.creditUsed} บาท` : 'ไม่'}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  ) : (
                    <div className="py-8 text-center text-muted-foreground">
                      ไม่พบประวัติการตรวจสอบสลิป
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* แท็บประวัติการเติมเงิน */}
            <TabsContent value="topup-history" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <CreditCard className="h-5 w-5 mr-2 text-primary" />
                    ประวัติการเติมเงิน
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {transactions?.length ? (
                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>วันที่</TableHead>
                            <TableHead>จำนวนเงิน</TableHead>
                            <TableHead>ประเภท</TableHead>
                            <TableHead>สถานะ</TableHead>
                            <TableHead>อ้างอิง</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {transactions.map((transaction) => (
                            <TableRow key={transaction.id}>
                              <TableCell>
                                {formatDate(transaction.createdAt, "dd/MM/yyyy HH:mm")}
                              </TableCell>
                              <TableCell>
                                {formatCurrency(transaction.amount || 0)}
                              </TableCell>
                              <TableCell>
                                {transaction.type || 'เติมเงิน'}
                              </TableCell>
                              <TableCell>
                                <Badge variant={
                                  transaction.status === 'completed' ? 'success' :
                                  transaction.status === 'pending' ? 'outline' : 'destructive'
                                }>
                                  {transaction.status === 'completed' ? 'สำเร็จ' :
                                   transaction.status === 'pending' ? 'รอดำเนินการ' : 'ล้มเหลว'}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                {transaction.referenceCode || '-'}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  ) : (
                    <div className="py-8 text-center text-muted-foreground">
                      ไม่พบประวัติการเติมเงิน
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        )}
        
        <DialogFooter className="flex justify-between items-center mt-4">
          <p className="text-sm text-muted-foreground">
            ID: {userId} • สร้างเมื่อ: {user && formatDate(user.createdAt, "dd/MM/yyyy")}
          </p>
          <Button onClick={() => onOpenChange(false)}>ปิด</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}