import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import { format, formatDistance } from "date-fns"
import { th } from "date-fns/locale"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// เพิ่มฟังก์ชันสำหรับจัดรูปแบบวันที่เป็นภาษาไทย
export function formatDate(date: Date | string | null, formatStr: string = "PPPPp"): string {
  try {
    if (!date) return "-";
    const validDate = date instanceof Date ? date : new Date(date);
    // ตรวจสอบว่าวันที่ถูกต้องหรือไม่
    if (isNaN(validDate.getTime())) return "-";
    return format(validDate, formatStr, { locale: th });
  } catch (error) {
    console.error("Error formatting date:", error);
    return "-";
  }
}

// ฟังก์ชันสำหรับแสดงระยะเวลาที่ผ่านมาเป็นภาษาไทย
export function formatDateDistance(date: Date | string | null): string {
  try {
    if (!date) return "-";
    const validDate = date instanceof Date ? date : new Date(date);
    // ตรวจสอบว่าวันที่ถูกต้องหรือไม่
    if (isNaN(validDate.getTime())) return "-";
    return formatDistance(validDate, new Date(), { 
      addSuffix: true,
      locale: th
    });
  } catch (error) {
    console.error("Error formatting date distance:", error);
    return "-";
  }
}

// ฟังก์ชันสำหรับจัดรูปแบบจำนวนเงิน
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('th-TH', {
    style: 'currency',
    currency: 'THB',
    minimumFractionDigits: 2
  }).format(amount);
}

// ฟังก์ชันสำหรับจัดรูปแบบจำนวนเงินแบบไทย (สำหรับหน้า advanced search)
export function formatThaiCurrency(amount: number | null | undefined): string {
  if (amount === null || amount === undefined) return "-";
  return new Intl.NumberFormat('th-TH', {
    style: 'currency',
    currency: 'THB',
    minimumFractionDigits: 2
  }).format(amount);
}

// ฟังก์ชันสำหรับจัดรูปแบบวันที่และเวลาแบบไทย (สำหรับหน้า advanced search)
export function formatThaiDateTime(dateTime: Date | string | null | undefined): string {
  if (!dateTime) return "-";
  try {
    const date = dateTime instanceof Date ? dateTime : new Date(dateTime);
    if (isNaN(date.getTime())) return "-";
    return format(date, "d MMM yyyy HH:mm", { locale: th });
  } catch (error) {
    return "-";
  }
}

// ฟังก์ชันสำหรับจัดรูปแบบจำนวนเต็ม
export function formatNumber(num: number): string {
  return new Intl.NumberFormat('th-TH').format(num);
}

// ฟังก์ชันแปลงข้อความยาวๆ ให้สั้นลง
export function truncateText(text: string, maxLength: number = 50): string {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
}

// ฟังก์ชันสำหรับแปลงค่าสถานะของผู้ใช้งานเป็นภาษาไทย
export function translateUserStatus(status: string): string {
  const statusMap: Record<string, string> = {
    'active': 'ใช้งาน',
    'inactive': 'ไม่ใช้งาน',
    'suspended': 'ระงับการใช้งาน'
  };
  return statusMap[status] || status;
}

// ฟังก์ชันสำหรับแปลงค่าบทบาทของผู้ใช้งานเป็นภาษาไทย
export function translateUserRole(role: string): string {
  const roleMap: Record<string, string> = {
    'user': 'ผู้ใช้งาน',
    'admin': 'ผู้ดูแลระบบ'
  };
  return roleMap[role] || role;
}

// ฟังก์ชันสำหรับแปลงค่าสถานะการตรวจสอบสลิปเป็นภาษาไทย
export function translateVerificationStatus(status: string): string {
  const statusMap: Record<string, string> = {
    'success': 'สำเร็จ',
    'failed': 'ล้มเหลว',
    'pending': 'กำลังดำเนินการ'
  };
  return statusMap[status] || status;
}
