/**
 * API Key Operations - แบบแก้ไขปัญหาการสร้าง API Key
 */
import { Express, Request, Response, NextFunction } from "express";
import { randomBytes } from "crypto";
import { db } from "../db";
import { eq } from "drizzle-orm";
import { apiKeys } from "@shared/schema";

// Middleware ตรวจสอบการเข้าสู่ระบบ
function isAuthenticated(req: Request, res: Response, next: NextFunction) {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ message: "ไม่ได้รับอนุญาต กรุณาเข้าสู่ระบบ" });
  }
  next();
}

// ฟังก์ชันสร้าง API key แบบสุ่ม
function generateApiKey(): string {
  return randomBytes(32).toString('hex');
}

/**
 * กำหนด routes สำหรับการจัดการ API key แบบตรงไปตรงมา
 */
export function setupDirectApiKeyRoutes(app: Express) {
  // สร้าง API key ใหม่
  app.post("/api/direct/api-keys", isAuthenticated, async (req, res) => {
    try {
      const { name, description = null, ipWhitelist = null, expiresAt = null } = req.body;
      const status = req.body.status || 'active';
      const limitEnabled = req.body.limitEnabled || false;
      const usageLimit = req.body.usageLimit || null;

      if (!name) {
        return res.status(400).json({ error: "กรุณาระบุชื่อ API Key" });
      }

      const userId = req.user!.id;

      // สร้าง API key
      const apiKey = generateApiKey();

      console.log("Creating new API key via direct method:", {
        name,
        userId,
        apiKeyLength: apiKey.length
      });

      // Insert โดยตรง โดยไม่ระบุค่า id เพื่อให้ auto-increment ทำงาน
      // แก้ไขปัญหา duplicate key value violates unique constraint "api_keys_pkey"
      const result = await db.insert(apiKeys).values({
        name,
        description,
        status,
        ipWhitelist: ipWhitelist ? ipWhitelist : null,
        expiresAt: expiresAt ? new Date(expiresAt) : null,
        limitEnabled,
        usageLimit,
        apiKey,
        userId,
        requestCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      }).returning();

      if (!result || result.length === 0) {
        throw new Error("ไม่สามารถสร้าง API key ได้");
      }

      res.status(201).json({
        ...result[0],
        apiKey // ส่ง API key กลับไปให้ผู้ใช้
      });
    } catch (error) {
      console.error("Error creating API key:", error);
      res.status(500).json({ error: "ไม่สามารถสร้าง API key ได้ กรุณาลองใหม่อีกครั้ง" });
    }
  });

  // ดูรายการ API key ของผู้ใช้
  app.get("/api/direct/api-keys", isAuthenticated, async (req, res) => {
    try {
      const userId = req.user!.id;

      const apiKeyList = await db.select().from(apiKeys).where(eq(apiKeys.userId, userId));

      // ปกปิด API key จริง ส่งเฉพาะส่วนที่จำเป็น
      const safeApiKeys = apiKeyList.map(key => {
        const { apiKey, ...safeKey } = key;
        return {
          ...safeKey,
          apiKey: apiKey ?
            apiKey.substring(0, 8) + '...' + apiKey.substring(apiKey.length - 8) :
            null
        };
      });

      res.json(safeApiKeys);
    } catch (error) {
      console.error("Error fetching API keys:", error);
      res.status(500).json({ error: "ไม่สามารถดึงข้อมูล API key ได้ กรุณาลองใหม่อีกครั้ง" });
    }
  });

  // ลบ API key ออกจากฐานข้อมูล
  app.delete("/api/direct/api-keys/:id", isAuthenticated, async (req, res) => {
    try {
      const apiKeyId = parseInt(req.params.id);
      if (isNaN(apiKeyId)) {
        return res.status(400).json({ error: "รหัส API key ไม่ถูกต้อง" });
      }

      const userId = req.user!.id;

      // ตรวจสอบว่า API key นี้เป็นของผู้ใช้คนนี้หรือไม่
      const apiKey = await db.select().from(apiKeys).where(eq(apiKeys.id, apiKeyId)).limit(1);

      if (!apiKey || apiKey.length === 0) {
        return res.status(404).json({ error: "ไม่พบ API key" });
      }

      if (apiKey[0].userId !== userId && req.user!.role !== 'admin') {
        return res.status(403).json({ error: "ไม่มีสิทธิ์ลบ API key นี้" });
      }

      // ลบ API key ออกจากฐานข้อมูล
      await db.delete(apiKeys).where(eq(apiKeys.id, apiKeyId));

      res.status(204).end();
    } catch (error) {
      console.error("Error deleting API key:", error);
      res.status(500).json({ error: "ไม่สามารถลบ API key ได้ กรุณาลองใหม่อีกครั้ง" });
    }
  });

  // ระงับการใช้งาน API key
  app.patch("/api/direct/api-keys/:id/suspend", isAuthenticated, async (req, res) => {
    try {
      const apiKeyId = parseInt(req.params.id);
      if (isNaN(apiKeyId)) {
        return res.status(400).json({ error: "รหัส API key ไม่ถูกต้อง" });
      }

      const userId = req.user!.id;

      // ตรวจสอบว่า API key นี้เป็นของผู้ใช้คนนี้หรือไม่
      const apiKey = await db.select().from(apiKeys).where(eq(apiKeys.id, apiKeyId)).limit(1);

      if (!apiKey || apiKey.length === 0) {
        return res.status(404).json({ error: "ไม่พบ API key" });
      }

      if (apiKey[0].userId !== userId && req.user!.role !== 'admin') {
        return res.status(403).json({ error: "ไม่มีสิทธิ์ระงับ API key นี้" });
      }

      // ระงับการใช้งาน API key
      await db.update(apiKeys)
        .set({ status: 'revoked', updatedAt: new Date() })
        .where(eq(apiKeys.id, apiKeyId));

      const updatedApiKey = await db.select().from(apiKeys).where(eq(apiKeys.id, apiKeyId)).limit(1);

      res.json(updatedApiKey[0]);
    } catch (error) {
      console.error("Error suspending API key:", error);
      res.status(500).json({ error: "ไม่สามารถระงับ API key ได้ กรุณาลองใหม่อีกครั้ง" });
    }
  });

  // เปิดใช้งาน API key
  app.patch("/api/direct/api-keys/:id/activate", isAuthenticated, async (req, res) => {
    try {
      const apiKeyId = parseInt(req.params.id);
      if (isNaN(apiKeyId)) {
        return res.status(400).json({ error: "รหัส API key ไม่ถูกต้อง" });
      }

      const userId = req.user!.id;

      // ตรวจสอบว่า API key นี้เป็นของผู้ใช้คนนี้หรือไม่
      const apiKey = await db.select().from(apiKeys).where(eq(apiKeys.id, apiKeyId)).limit(1);

      if (!apiKey || apiKey.length === 0) {
        return res.status(404).json({ error: "ไม่พบ API key" });
      }

      if (apiKey[0].userId !== userId && req.user!.role !== 'admin') {
        return res.status(403).json({ error: "ไม่มีสิทธิ์เปิดใช้งาน API key นี้" });
      }

      // เปิดใช้งาน API key
      await db.update(apiKeys)
        .set({ status: 'active', updatedAt: new Date() })
        .where(eq(apiKeys.id, apiKeyId));

      const updatedApiKey = await db.select().from(apiKeys).where(eq(apiKeys.id, apiKeyId)).limit(1);

      res.json(updatedApiKey[0]);
    } catch (error) {
      console.error("Error activating API key:", error);
      res.status(500).json({ error: "ไม่สามารถเปิดใช้งาน API key ได้ กรุณาลองใหม่อีกครั้ง" });
    }
  });

  // สร้าง API Key ใหม่ (regenerate)
  app.post("/api/direct/api-keys/:id/regenerate", isAuthenticated, async (req, res) => {
    try {
      const apiKeyId = parseInt(req.params.id);
      if (isNaN(apiKeyId)) {
        return res.status(400).json({ error: "รหัส API key ไม่ถูกต้อง" });
      }

      const userId = req.user!.id;

      // ตรวจสอบว่า API key นี้เป็นของผู้ใช้คนนี้หรือไม่
      const apiKey = await db.select().from(apiKeys).where(eq(apiKeys.id, apiKeyId)).limit(1);

      if (!apiKey || apiKey.length === 0) {
        return res.status(404).json({ error: "ไม่พบ API key" });
      }

      if (apiKey[0].userId !== userId && req.user!.role !== 'admin') {
        return res.status(403).json({ error: "ไม่มีสิทธิ์สร้าง API key ใหม่" });
      }

      // สร้าง API key ใหม่
      const newApiKeyValue = generateApiKey();

      // อัพเดท API key ในฐานข้อมูล
      await db.update(apiKeys)
        .set({ apiKey: newApiKeyValue, updatedAt: new Date() })
        .where(eq(apiKeys.id, apiKeyId));

      const updatedApiKey = await db.select().from(apiKeys).where(eq(apiKeys.id, apiKeyId)).limit(1);

      // ส่งค่า API key กลับ (นี่เป็นครั้งเดียวที่จะแสดง API key เต็ม)
      res.json({
        ...updatedApiKey[0],
        apiKey: newApiKeyValue
      });
    } catch (error) {
      console.error("Error regenerating API key:", error);
      res.status(500).json({ error: "ไม่สามารถสร้าง API key ใหม่ได้ กรุณาลองใหม่อีกครั้ง" });
    }
  });
}