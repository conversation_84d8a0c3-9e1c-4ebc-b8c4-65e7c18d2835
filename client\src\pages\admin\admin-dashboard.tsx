import { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  CartesianGrid,
  Legend
} from "recharts";
import {
  Users,
  Package,
  CreditCard,
  Activity,
  TrendingUp,
  BarChart3,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  CheckCircle,
  XCircle
} from "lucide-react";
import { Admin } from "@/components/layouts/admin-layout";
import { useAuth } from "@/hooks/use-auth";
import { formatNumber, formatCurrency } from "@/lib/utils";

// สร้างอินเตอร์เฟสสำหรับข้อมูลสถิติแดชบอร์ด
interface DashboardStats {
  users: {
    total: number;
    active: number;
    inactive: number;
    suspended: number;
  };
  slips: {
    total: number;
    success: number;
    failed: number;
  };
  signupsByDay: {
    date: string;
    count: number;
  }[];
  apiUsageByDay: {
    date: string;
    count: number;
  }[];
  topUsers: {
    userId: number;
    username: string;
    email: string;
    count: number;
  }[];
  topPackages: {
    packageId: number;
    packageName: string;
    count: number;
  }[];
}

// สีสำหรับกราฟ
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

export default function AdminDashboard() {
  const { user } = useAuth();
  const [activeChart, setActiveChart] = useState("bar");
  const [activePieChart, setActivePieChart] = useState("package");

  // ตรวจสอบว่าผู้ใช้เป็นแอดมินหรือไม่
  useEffect(() => {
    if (user && user.role !== 'admin') {
      // ถ้าล็อกอินแล้วแต่ไม่ใช่แอดมิน ให้รีไดเร็กไปหน้า dashboard
      window.location.href = '/dashboard';
    }
  }, [user]);

  // ดึงข้อมูลสถิติสำหรับแดชบอร์ด
  const { data: stats, isLoading } = useQuery<DashboardStats>({
    queryKey: ['/api/admin/stats'],
  });

  // หากกำลังโหลดข้อมูล
  if (isLoading) {
    return (
      <Admin>
        <div className="space-y-8">
          <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
            <div>
              <h1 className="text-2xl font-bold tracking-tight">แดชบอร์ดแอดมิน</h1>
              <p className="text-muted-foreground">
                ภาพรวมและสถิติของระบบทั้งหมด
              </p>
            </div>
            <Button
              className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 border-none"
              onClick={() => window.location.href = '/dashboard'}
            >
              <div className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                <span>ไปหน้าแดชบอร์ดผู้ใช้</span>
              </div>
            </Button>
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
            {[...Array(4)].map((_, i) => (
              <Card key={i}>
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <Skeleton className="h-5 w-20" />
                  <Skeleton className="h-4 w-4" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-8 w-28" />
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <Card className="col-span-1">
              <CardHeader>
                <Skeleton className="h-5 w-40" />
              </CardHeader>
              <CardContent className="h-[300px]">
                <Skeleton className="h-full w-full" />
              </CardContent>
            </Card>

            <Card className="col-span-1">
              <CardHeader>
                <Skeleton className="h-5 w-40" />
              </CardHeader>
              <CardContent className="h-[300px]">
                <Skeleton className="h-full w-full" />
              </CardContent>
            </Card>
          </div>
        </div>
      </Admin>
    );
  }

  // ถ้าไม่มีข้อมูล
  if (!stats) {
    return (
      <Admin>
        <div className="space-y-4">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">แดชบอร์ดแอดมิน</h1>
            <p className="text-muted-foreground">
              ไม่พบข้อมูลสถิติในระบบ
            </p>
          </div>

          <Button asChild>
            <a href="/admin/users">จัดการผู้ใช้งาน</a>
          </Button>
        </div>
      </Admin>
    );
  }

  return (
    <Admin>
      <div className="space-y-8">
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 rounded-lg blur-xl -z-10 opacity-70"></div>
          <div className="relative z-10 p-4 rounded-lg border border-indigo-200/30">
            <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
              <div>
                <h1 className="text-2xl font-bold tracking-tight divine-text">แดชบอร์ดเทพเจ้า</h1>
                <div className="lightning-bar w-32 my-2"></div>
                <p className="text-muted-foreground">
                  ภาพรวมและสถิติของระบบทั้งหมดสำหรับผู้ปกครองเทพเจ้า
                </p>
              </div>
              <Button
                className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 border-none"
                onClick={() => window.location.href = '/dashboard'}
              >
                <div className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  <span>ไปหน้าแดชบอร์ดผู้ใช้</span>
                </div>
              </Button>
            </div>
          </div>
        </div>

        {/* ไอคอนการ์ดแสดงสถิติ */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card className="border border-indigo-200/30 bg-gradient-to-b from-white to-indigo-50/30 overflow-hidden relative group transition-all duration-300 hover:border-amber-300/50 hover:shadow-lg">
            <div className="absolute inset-0 bg-gradient-to-r from-indigo-600/5 to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <CardHeader className="flex flex-row items-center justify-between pb-2 relative z-10">
              <CardTitle className="text-sm font-medium text-indigo-700">จำนวนผู้ใช้งาน</CardTitle>
              <div className="p-1.5 rounded-full bg-gradient-to-r from-indigo-100 to-indigo-200 flex items-center justify-center">
                <Users className="h-4 w-4 text-indigo-600" />
              </div>
            </CardHeader>
            <CardContent className="relative z-10">
              <div className="text-2xl font-bold gold-text">{formatNumber(stats.users.total)}</div>
              <p className="text-xs text-indigo-600/80 mt-1">
                ผู้ใช้งานที่ยังแอคทีฟ: {formatNumber(stats.users.active)}
              </p>
              <Progress
                value={(stats.users.active / stats.users.total) * 100}
                className="h-1.5 mt-2 bg-indigo-100"
              />
            </CardContent>
          </Card>

          <Card className="border border-indigo-200/30 bg-gradient-to-b from-white to-indigo-50/30 overflow-hidden relative group transition-all duration-300 hover:border-amber-300/50 hover:shadow-lg">
            <div className="absolute inset-0 bg-gradient-to-r from-indigo-600/5 to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <CardHeader className="flex flex-row items-center justify-between pb-2 relative z-10">
              <CardTitle className="text-sm font-medium text-indigo-700">รายได้รวม</CardTitle>
              <div className="p-1.5 rounded-full bg-gradient-to-r from-amber-100 to-amber-200 flex items-center justify-center">
                <CreditCard className="h-4 w-4 text-amber-600" />
              </div>
            </CardHeader>
            <CardContent className="relative z-10">
              <div className="text-2xl font-bold gold-text">{formatCurrency(0)}</div>
              <p className="text-xs text-indigo-600/80 mt-1">
                {Array.isArray(stats.topPackages) && stats.topPackages.length > 0
                  ? `แพ็กเกจยอดนิยม: ${stats.topPackages[0]?.packageName || 'ไม่มีข้อมูล'}`
                  : 'ไม่มีข้อมูลแพ็กเกจที่ใช้งาน'
                }
              </p>
            </CardContent>
          </Card>

          <Card className="border border-indigo-200/30 bg-gradient-to-b from-white to-indigo-50/30 overflow-hidden relative group transition-all duration-300 hover:border-amber-300/50 hover:shadow-lg">
            <div className="absolute inset-0 bg-gradient-to-r from-indigo-600/5 to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <CardHeader className="flex flex-row items-center justify-between pb-2 relative z-10">
              <CardTitle className="text-sm font-medium text-indigo-700">การตรวจสอบทั้งหมด</CardTitle>
              <div className="p-1.5 rounded-full bg-gradient-to-r from-purple-100 to-purple-200 flex items-center justify-center">
                <Activity className="h-4 w-4 text-purple-600" />
              </div>
            </CardHeader>
            <CardContent className="relative z-10">
              <div className="text-2xl font-bold gold-text">{formatNumber(stats.slips.total)}</div>
              <div className="flex items-center justify-between mt-2">
                <div className="flex items-center px-2 py-1 bg-green-50 rounded-full">
                  <CheckCircle className="h-3 w-3 text-green-500 mr-1" />
                  <span className="text-xs text-green-700">{formatNumber(stats.slips.success)}</span>
                </div>
                <div className="flex items-center px-2 py-1 bg-red-50 rounded-full">
                  <XCircle className="h-3 w-3 text-red-500 mr-1" />
                  <span className="text-xs text-red-700">{formatNumber(stats.slips.failed)}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border border-indigo-200/30 bg-gradient-to-b from-white to-indigo-50/30 overflow-hidden relative group transition-all duration-300 hover:border-amber-300/50 hover:shadow-lg">
            <div className="absolute inset-0 bg-gradient-to-r from-indigo-600/5 to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <CardHeader className="flex flex-row items-center justify-between pb-2 relative z-10">
              <CardTitle className="text-sm font-medium text-indigo-700">จำนวนแพ็กเกจ</CardTitle>
              <div className="p-1.5 rounded-full bg-gradient-to-r from-blue-100 to-blue-200 flex items-center justify-center">
                <Package className="h-4 w-4 text-blue-600" />
              </div>
            </CardHeader>
            <CardContent className="relative z-10">
              <div className="text-2xl font-bold gold-text">{formatNumber(Array.isArray(stats.topPackages) ? stats.topPackages.length : 0)}</div>
              <div className="text-xs mt-2">
                <Button variant="secondary" size="sm" className="bg-gradient-to-r from-indigo-100 to-indigo-200 text-indigo-700 hover:from-indigo-200 hover:to-indigo-300 h-7 rounded-full text-xs font-medium" asChild>
                  <a href="/admin/packages">จัดการแพ็กเกจ</a>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* แผนภูมิต่างๆ */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {/* กราฟซ้าย: สถิติการตรวจสอบ */}
          <Card className="col-span-1 border border-indigo-200/30 bg-gradient-to-b from-white to-indigo-50/20 overflow-hidden relative group">
            <div className="absolute inset-0 bg-gradient-to-r from-indigo-600/5 to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <CardHeader className="relative z-10">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-indigo-700">
                    <div className="flex items-center">
                      <Activity className="h-5 w-5 mr-2 text-purple-500" />
                      <span>สถิติการตรวจสอบล่าสุด</span>
                    </div>
                  </CardTitle>
                  <div className="lightning-bar w-20 my-1"></div>
                </div>
                <Tabs
                  value={activeChart}
                  onValueChange={setActiveChart}
                  className="w-auto"
                >
                  <TabsList className="grid w-auto grid-cols-2 bg-indigo-100/50 border border-indigo-200/50">
                    <TabsTrigger value="bar" className="px-3 py-1 data-[state=active]:bg-indigo-200/80 data-[state=active]:text-indigo-800">
                      <BarChart3 className="h-4 w-4" />
                    </TabsTrigger>
                    <TabsTrigger value="line" className="px-3 py-1 data-[state=active]:bg-indigo-200/80 data-[state=active]:text-indigo-800">
                      <TrendingUp className="h-4 w-4" />
                    </TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>
              <CardDescription className="text-indigo-600/70">แสดงจำนวนการตรวจสอบที่สำเร็จและล้มเหลวในแต่ละวัน</CardDescription>
            </CardHeader>
            <CardContent className="h-[300px] relative z-10">
              <Tabs value={activeChart}>
                <TabsContent value="bar" className="mt-0 h-full">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={stats.apiUsageByDay || []} margin={{ top: 10, right: 30, left: 0, bottom: 20 }}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip
                        content={({ active, payload, label }) => {
                          if (active && payload && payload.length) {
                            return (
                              <div className="bg-background p-3 border rounded-md shadow-sm">
                                <p className="font-medium">{label}</p>
                                <p className="text-indigo-600">
                                  จำนวนครั้ง: {payload[0]?.value || 0}
                                </p>
                              </div>
                            );
                          }
                          return null;
                        }}
                      />
                      <Legend />
                      <Bar dataKey="count" name="จำนวนการใช้งาน" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </TabsContent>

                <TabsContent value="line" className="mt-0 h-full">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={stats.apiUsageByDay || []} margin={{ top: 10, right: 30, left: 0, bottom: 20 }}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip
                        content={({ active, payload, label }) => {
                          if (active && payload && payload.length) {
                            return (
                              <div className="bg-background p-3 border rounded-md shadow-sm">
                                <p className="font-medium">{label}</p>
                                <p className="text-indigo-600">
                                  จำนวนครั้ง: {payload[0]?.value || 0}
                                </p>
                              </div>
                            );
                          }
                          return null;
                        }}
                      />
                      <Legend />
                      <Line
                        type="monotone"
                        dataKey="count"
                        name="จำนวนการใช้งาน"
                        stroke="#8884d8"
                        strokeWidth={2}
                        dot={{ r: 4 }}
                        activeDot={{ r: 6 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>

          {/* กราฟขวา: การเติบโตของผู้ใช้และแพ็กเกจ */}
          <Card className="col-span-1 border border-indigo-200/30 bg-gradient-to-b from-white to-indigo-50/20 overflow-hidden relative group">
            <div className="absolute inset-0 bg-gradient-to-r from-indigo-600/5 to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <CardHeader className="relative z-10">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-indigo-700">
                    <div className="flex items-center">
                      <PieChartIcon className="h-5 w-5 mr-2 text-blue-500" />
                      <span>สัดส่วนแพ็กเกจและการเติบโต</span>
                    </div>
                  </CardTitle>
                  <div className="lightning-bar w-20 my-1"></div>
                </div>
                <Tabs
                  value={activePieChart}
                  onValueChange={setActivePieChart}
                  className="w-auto"
                >
                  <TabsList className="grid w-auto grid-cols-2 bg-indigo-100/50 border border-indigo-200/50">
                    <TabsTrigger value="package" className="px-3 py-1 data-[state=active]:bg-indigo-200/80 data-[state=active]:text-indigo-800">
                      <PieChartIcon className="h-4 w-4" />
                    </TabsTrigger>
                    <TabsTrigger value="growth" className="px-3 py-1 data-[state=active]:bg-indigo-200/80 data-[state=active]:text-indigo-800">
                      <TrendingUp className="h-4 w-4" />
                    </TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>
              <CardDescription className="text-indigo-600/70">แสดงข้อมูลสัดส่วนแพ็กเกจและการเติบโตของผู้ใช้งาน</CardDescription>
            </CardHeader>
            <CardContent className="h-[300px] relative z-10">
              <Tabs value={activePieChart}>
                <TabsContent value="package" className="mt-0 h-full">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={Array.isArray(stats.topPackages) ? stats.topPackages : []}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        nameKey="packageName"
                        label={({
                          cx,
                          cy,
                          midAngle,
                          innerRadius,
                          outerRadius,
                          percent,
                          index,
                          packageName
                        }) => {
                          const RADIAN = Math.PI / 180;
                          const radius = 25 + innerRadius + (outerRadius - innerRadius);
                          const x = cx + radius * Math.cos(-midAngle * RADIAN);
                          const y = cy + radius * Math.sin(-midAngle * RADIAN);

                          return (
                            <text
                              x={x}
                              y={y}
                              textAnchor={x > cx ? "start" : "end"}
                              dominantBaseline="central"
                              className="text-xs font-medium"
                            >
                              {packageName} ({(percent * 100).toFixed(0)}%)
                            </text>
                          );
                        }}
                        dataKey="count"
                        outerRadius={80}
                      >
                        {Array.isArray(stats.topPackages)
                          ? stats.topPackages.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))
                          : null
                        }
                      </Pie>
                      <Tooltip
                        content={({ active, payload }) => {
                          if (active && payload && payload.length) {
                            const value = Number(payload[0]?.value || 0);
                            const total = Array.isArray(stats.topPackages)
                              ? stats.topPackages.reduce((sum, item) => sum + Number(item.count || 0), 0)
                              : 1;
                            const percentage = value > 0 ? (value * 100 / total).toFixed(1) : "0.0";

                            return (
                              <div className="bg-background p-3 border rounded-md shadow-sm">
                                <p className="font-medium">{payload[0]?.payload.packageName || ''}</p>
                                <p>จำนวน: {value}</p>
                                <p>สัดส่วน: {percentage}%</p>
                              </div>
                            );
                          }
                          return null;
                        }}
                      />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </TabsContent>

                <TabsContent value="growth" className="mt-0 h-full">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={stats.signupsByDay || []}
                      margin={{
                        top: 10,
                        right: 30,
                        left: 0,
                        bottom: 20,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip
                        content={({ active, payload, label }) => {
                          if (active && payload && payload.length) {
                            return (
                              <div className="bg-background p-3 border rounded-md shadow-sm">
                                <p className="font-medium">{label}</p>
                                <p>จำนวนผู้ลงทะเบียน: {payload[0]?.value || 0}</p>
                              </div>
                            );
                          }
                          return null;
                        }}
                      />
                      <Legend />
                      <Line
                        type="monotone"
                        dataKey="count"
                        name="ผู้ลงทะเบียน"
                        stroke="#8884d8"
                        strokeWidth={2}
                        dot={{ r: 4 }}
                        activeDot={{ r: 6 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
    </Admin>
  );
}