import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { User as UserType, Package as PackageType, UserPackage as UserPackageType, ApiKey as ApiKeyType } from "@shared/schema";
import { apiRequest } from "@/lib/queryClient";
import { formatCurrency, formatDate, translateUserStatus, translateUserRole } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";

import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle,
  CardDescription,
  CardFooter 
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  User, 
  UserCog, 
  Shield, 
  AlertCircle, 
  RefreshCw, 
  CreditCard,
  Plus,
  Minus,
  Package,
  Key,
  Trash2,
  RefreshCcw,
  Clock,
  CalendarIcon,
  Copy,
  History,
  BarChart4
} from "lucide-react";

// Schema สำหรับแก้ไขข้อมูลผู้ใช้
const editUserSchema = z.object({
  username: z.string().min(3, "ชื่อผู้ใช้ต้องมีอย่างน้อย 3 ตัวอักษร"),
  email: z.string().email("อีเมลไม่ถูกต้อง"),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  companyName: z.string().optional(),
  status: z.enum(["active", "inactive", "suspended"]),
  role: z.enum(["user", "admin"]),
});

type EditUserFormValues = z.infer<typeof editUserSchema>;

// Schema สำหรับเปลี่ยนรหัสผ่าน
const changePasswordSchema = z.object({
  newPassword: z.string().min(6, "รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร"),
  confirmPassword: z.string().min(6, "รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร"),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "รหัสผ่านไม่ตรงกัน",
  path: ["confirmPassword"],
});

type ChangePasswordFormValues = z.infer<typeof changePasswordSchema>;

// Schema สำหรับเพิ่ม/หักเครดิต
const creditActionSchema = z.object({
  amount: z.preprocess(
    (val) => Number(val),
    z.number().positive("จำนวนเครดิตต้องมากกว่า 0")
  ),
  description: z.string().optional(),
});

type CreditActionFormValues = z.infer<typeof creditActionSchema>;

// Schema สำหรับเพิ่มแพ็คเกจให้ผู้ใช้
const addPackageSchema = z.object({
  packageId: z.preprocess(
    (val) => Number(val),
    z.number().positive("กรุณาเลือกแพ็คเกจ")
  ),
  durationMonths: z.preprocess(
    (val) => Number(val),
    z.number().int().positive("ระยะเวลาต้องเป็นจำนวนเต็มบวก")
  ),
  isActive: z.boolean().default(true),
});

type AddPackageFormValues = z.infer<typeof addPackageSchema>;

// Schema สำหรับเพิ่ม API Key ใหม่
const addApiKeySchema = z.object({
  name: z.string().min(1, "กรุณาระบุชื่อ API Key"),
  description: z.string().optional(),
  usageLimit: z.preprocess(
    (val) => val === "" ? 0 : Number(val),
    z.number().min(0, "จำนวนการใช้งานต้องไม่น้อยกว่า 0")
  ).optional(),
  ipWhitelist: z.string().optional().transform(val => 
    val ? val.split(",").map(ip => ip.trim()).filter(ip => ip) : undefined
  ),
  expiryDate: z.date().optional().nullable(),
});

type AddApiKeyFormValues = z.infer<typeof addApiKeySchema>;

interface UserDetailDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  userId: number | null;
}

export function SimpleUserDetailDialog({ open, onOpenChange, userId }: UserDetailDialogProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState("profile");
  const [creditAction, setCreditAction] = useState<'add' | 'deduct'>('add');
  const [userPackages, setUserPackages] = useState<(UserPackageType & { package: PackageType })[]>([]);
  const [userApiKeys, setUserApiKeys] = useState<ApiKeyType[]>([]);

  // ดึงข้อมูลผู้ใช้
  const { data: user, isLoading } = useQuery<UserType>({
    queryKey: ['/api/admin/users', userId],
    enabled: open && userId !== null,
    queryFn: async () => {
      const res = await apiRequest("GET", `/api/admin/users/${userId}`);
      return await res.json();
    }
  });

  // ดึงข้อมูลแพ็คเกจของผู้ใช้
  const { data: packages } = useQuery<PackageType[]>({
    queryKey: ['/api/packages'],
    enabled: open && activeTab === "packages",
    queryFn: async () => {
      try {
        const res = await fetch('/api/packages', {
          credentials: 'include'
        });
        
        if (res.status === 401) {
          console.log('ไม่ได้รับอนุญาตให้เข้าถึงข้อมูลแพ็คเกจ, กำลังใช้ข้อมูลว่าง');
          return [];
        }
        
        if (!res.ok) {
          throw new Error(`Error fetching packages: ${res.status}`);
        }
        
        return await res.json();
      } catch (error) {
        console.error('เกิดข้อผิดพลาดในการดึงข้อมูลแพ็คเกจ:', error);
        return [];
      }
    }
  });

  // ดึงข้อมูลประวัติการตรวจสอบสลิป
  const { data: slipVerifications } = useQuery<any[]>({
    queryKey: ['/api/admin/users', userId, 'verifications'],
    enabled: open && userId !== null && activeTab === "verification-history",
    queryFn: async () => {
      const res = await apiRequest("GET", `/api/admin/users/${userId}/verifications`);
      return await res.json();
    }
  });

  // ดึงข้อมูลประวัติการเติมเงิน
  const { data: transactions } = useQuery<any[]>({
    queryKey: ['/api/admin/users', userId, 'transactions'],
    enabled: open && userId !== null && activeTab === "topup-history",
    queryFn: async () => {
      const res = await apiRequest("GET", `/api/admin/users/${userId}/transactions`);
      return await res.json();
    }
  });
  
  // ดึงข้อมูลสถิติของผู้ใช้
  const { data: userStatsData } = useQuery<any>({
    queryKey: ['/api/admin/users', userId, 'stats'],
    enabled: open && userId !== null && activeTab === "stats",
    queryFn: async () => {
      const res = await apiRequest("GET", `/api/admin/users/${userId}/stats`);
      return await res.json();
    }
  });

  // โหลดข้อมูลแพ็คเกจและ API keys เมื่อเปลี่ยนแท็บ
  useEffect(() => {
    if (!open || !userId) return;
    
    if (activeTab === "packages") {
      fetchUserPackages();
    } else if (activeTab === "api-keys") {
      fetchUserApiKeys();
    } else if (activeTab === "stats") {
      // ดึงข้อมูลสถิติจาก React Query แทน
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users', userId, 'stats'] });
    }
  }, [activeTab, open, userId]);

  // ฟอร์มสำหรับแก้ไขข้อมูลผู้ใช้
  const editUserForm = useForm<EditUserFormValues>({
    resolver: zodResolver(editUserSchema),
    defaultValues: {
      username: "",
      email: "",
      firstName: "",
      lastName: "",
      companyName: "",
      status: "active",
      role: "user"
    }
  });

  // รีเซ็ตฟอร์มเมื่อข้อมูลผู้ใช้เปลี่ยน
  useEffect(() => {
    if (user) {
      editUserForm.reset({
        username: user.username,
        email: user.email,
        firstName: user.firstName || "",
        lastName: user.lastName || "",
        companyName: user.companyName || "",
        status: user.status as "active" | "inactive" | "suspended",
        role: user.role as "user" | "admin"
      });
    }
  }, [user]);

  // ฟอร์มสำหรับเปลี่ยนรหัสผ่าน
  const changePasswordForm = useForm<ChangePasswordFormValues>({
    resolver: zodResolver(changePasswordSchema),
    defaultValues: {
      newPassword: "",
      confirmPassword: ""
    }
  });

  // ฟอร์มสำหรับเพิ่ม/หักเครดิต
  const creditForm = useForm<CreditActionFormValues>({
    resolver: zodResolver(creditActionSchema),
    defaultValues: {
      amount: 0,
      description: ""
    }
  });

  // ฟอร์มสำหรับเพิ่มแพ็คเกจ
  const addPackageForm = useForm<AddPackageFormValues>({
    resolver: zodResolver(addPackageSchema),
    defaultValues: {
      packageId: 0,
      durationMonths: 1,
      isActive: true
    }
  });

  // ฟอร์มสำหรับเพิ่ม API Key
  const addApiKeyForm = useForm<AddApiKeyFormValues>({
    resolver: zodResolver(addApiKeySchema),
    defaultValues: {
      name: "",
      description: "",
      usageLimit: undefined,
      ipWhitelist: undefined,
      expiryDate: null
    }
  });

  // ดึงข้อมูลแพ็คเกจของผู้ใช้
  const fetchUserPackages = async () => {
    if (!userId) return;
    
    try {
      const res = await apiRequest("GET", `/api/admin/users/${userId}/packages`);
      const data = await res.json();
      setUserPackages(data);
    } catch (error) {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: "ไม่สามารถดึงข้อมูลแพ็คเกจได้",
        variant: "destructive"
      });
    }
  };

  // ดึงข้อมูล API keys ของผู้ใช้
  const fetchUserApiKeys = async () => {
    if (!userId) return;
    
    try {
      const res = await apiRequest("GET", `/api/admin/users/${userId}/api-keys`);
      const data = await res.json();
      setUserApiKeys(data);
    } catch (error) {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: "ไม่สามารถดึงข้อมูล API keys ได้",
        variant: "destructive"
      });
    }
  };

  // ฟังก์ชันสำหรับบันทึกข้อมูลผู้ใช้
  const onSubmitEditForm = (data: EditUserFormValues) => {
    updateUserMutation.mutate(data);
  };

  // ฟังก์ชันสำหรับเปลี่ยนรหัสผ่าน
  const onSubmitChangePasswordForm = (data: ChangePasswordFormValues) => {
    changePasswordMutation.mutate({ newPassword: data.newPassword });
  };

  // ฟังก์ชันสำหรับเพิ่ม/หักเครดิต
  const onSubmitCreditForm = (data: CreditActionFormValues) => {
    if (creditAction === 'add') {
      addCreditMutation.mutate(data);
    } else {
      deductCreditMutation.mutate(data);
    }
  };

  // ฟังก์ชันสำหรับเพิ่มแพ็คเกจให้ผู้ใช้
  const onSubmitAddPackageForm = (data: AddPackageFormValues) => {
    addPackageMutation.mutate(data);
  };

  // ฟังก์ชันสำหรับเพิ่ม API Key
  const onSubmitAddApiKeyForm = (data: AddApiKeyFormValues) => {
    addApiKeyMutation.mutate(data);
  };

  // Mutation สำหรับอัปเดตข้อมูลผู้ใช้
  const updateUserMutation = useMutation({
    mutationFn: async (userData: Partial<UserType>) => {
      const res = await apiRequest("PATCH", `/api/admin/users/${userId}`, userData);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "อัปเดตข้อมูลสำเร็จ",
        description: "ข้อมูลผู้ใช้งานถูกอัปเดตเรียบร้อยแล้ว",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users'] });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users', userId] });
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับเปลี่ยนรหัสผ่าน
  const changePasswordMutation = useMutation({
    mutationFn: async (passwordData: { newPassword: string }) => {
      try {
        const res = await fetch(`/api/admin/users/${userId}/password`, {
          method: "PATCH",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(passwordData),
          credentials: "include"
        });
        
        if (!res.ok) {
          const errorText = await res.text();
          // ตรวจสอบว่าเป็น HTML หรือไม่
          if (errorText.trim().startsWith('<!DOCTYPE') || errorText.trim().startsWith('<html')) {
            throw new Error('เซสชั่นหมดอายุหรือไม่มีสิทธิ์เข้าถึง โปรดเข้าสู่ระบบใหม่');
          }
          
          try {
            // พยายามแปลงเป็น JSON ถ้าทำได้
            const errorData = JSON.parse(errorText);
            throw new Error(errorData.message || `HTTP error! status: ${res.status}`);
          } catch (e) {
            // ถ้าแปลงเป็น JSON ไม่ได้ ใช้ข้อความเดิม
            throw new Error(`เกิดข้อผิดพลาด: ${errorText.substring(0, 100)}`);
          }
        }
        
        return await res.json();
      } catch (error: any) {
        console.error("Error changing password:", error);
        throw new Error(error.message || "เกิดข้อผิดพลาดในการเปลี่ยนรหัสผ่าน");
      }
    },
    onSuccess: () => {
      toast({
        title: "เปลี่ยนรหัสผ่านสำเร็จ",
        description: "รหัสผ่านถูกเปลี่ยนเรียบร้อยแล้ว",
      });
      changePasswordForm.reset();
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับเพิ่มเครดิต
  const addCreditMutation = useMutation({
    mutationFn: async (data: CreditActionFormValues) => {
      try {
        const res = await fetch(`/api/admin/users/${userId}/credit`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(data),
          credentials: "include"
        });
        
        if (!res.ok) {
          const errorText = await res.text();
          // ตรวจสอบว่าเป็น HTML หรือไม่
          if (errorText.trim().startsWith('<!DOCTYPE') || errorText.trim().startsWith('<html')) {
            throw new Error('เซสชั่นหมดอายุหรือไม่มีสิทธิ์เข้าถึง โปรดเข้าสู่ระบบใหม่');
          }
          
          try {
            // พยายามแปลงเป็น JSON ถ้าทำได้
            const errorData = JSON.parse(errorText);
            throw new Error(errorData.message || `HTTP error! status: ${res.status}`);
          } catch (e) {
            // ถ้าแปลงเป็น JSON ไม่ได้ ใช้ข้อความเดิม
            throw new Error(`เกิดข้อผิดพลาด: ${errorText.substring(0, 100)}`);
          }
        }
        
        return await res.json();
      } catch (error: any) {
        console.error("Error adding credit:", error);
        throw new Error(error.message || "เกิดข้อผิดพลาดในการเพิ่มเครดิต");
      }
    },
    onSuccess: () => {
      toast({
        title: "เพิ่มเครดิตสำเร็จ",
        description: `เพิ่มเครดิต ${formatCurrency(creditForm.getValues().amount)} บาท ให้กับ ${user?.username} เรียบร้อยแล้ว`,
      });
      creditForm.reset({ amount: 0, description: creditAction === 'add' ? 'เติมเครดิต' : 'หักเครดิต' });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users'] });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users', userId] });
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับหักเครดิต
  const deductCreditMutation = useMutation({
    mutationFn: async (data: CreditActionFormValues) => {
      try {
        const res = await fetch(`/api/admin/users/${userId}/deduct-credit`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(data),
          credentials: "include"
        });
        
        if (!res.ok) {
          const errorText = await res.text();
          // ตรวจสอบว่าเป็น HTML หรือไม่
          if (errorText.trim().startsWith('<!DOCTYPE') || errorText.trim().startsWith('<html')) {
            throw new Error('เซสชั่นหมดอายุหรือไม่มีสิทธิ์เข้าถึง โปรดเข้าสู่ระบบใหม่');
          }
          
          try {
            // พยายามแปลงเป็น JSON ถ้าทำได้
            const errorData = JSON.parse(errorText);
            throw new Error(errorData.message || `HTTP error! status: ${res.status}`);
          } catch (e) {
            // ถ้าแปลงเป็น JSON ไม่ได้ ใช้ข้อความเดิม
            throw new Error(`เกิดข้อผิดพลาด: ${errorText.substring(0, 100)}`);
          }
        }
        
        return await res.json();
      } catch (error: any) {
        console.error("Error deducting credit:", error);
        throw new Error(error.message || "เกิดข้อผิดพลาดในการหักเครดิต");
      }
    },
    onSuccess: () => {
      toast({
        title: "หักเครดิตสำเร็จ",
        description: `หักเครดิต ${formatCurrency(creditForm.getValues().amount)} บาท จาก ${user?.username} เรียบร้อยแล้ว`,
      });
      creditForm.reset({ amount: 0, description: creditAction === 'add' ? 'เติมเครดิต' : 'หักเครดิต' });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users'] });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users', userId] });
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับเพิ่มแพ็คเกจให้ผู้ใช้
  const addPackageMutation = useMutation({
    mutationFn: async (data: AddPackageFormValues) => {
      try {
        const res = await fetch(`/api/admin/users/${userId}/packages`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(data),
          credentials: "include"
        });
        
        if (!res.ok) {
          const errorText = await res.text();
          // ตรวจสอบว่าเป็น HTML หรือไม่
          if (errorText.trim().startsWith('<!DOCTYPE') || errorText.trim().startsWith('<html')) {
            throw new Error('เซสชั่นหมดอายุหรือไม่มีสิทธิ์เข้าถึง โปรดเข้าสู่ระบบใหม่');
          }
          
          try {
            // พยายามแปลงเป็น JSON ถ้าทำได้
            const errorData = JSON.parse(errorText);
            throw new Error(errorData.message || `HTTP error! status: ${res.status}`);
          } catch (e) {
            // ถ้าแปลงเป็น JSON ไม่ได้ ใช้ข้อความเดิม
            throw new Error(`เกิดข้อผิดพลาด: ${errorText.substring(0, 100)}`);
          }
        }
        
        return await res.json();
      } catch (error: any) {
        console.error("Error adding package:", error);
        throw new Error(error.message || "เกิดข้อผิดพลาดในการเพิ่มแพ็คเกจ");
      }
    },
    onSuccess: () => {
      toast({
        title: "เพิ่มแพ็คเกจสำเร็จ",
        description: "เพิ่มแพ็คเกจให้ผู้ใช้งานเรียบร้อยแล้ว",
      });
      addPackageForm.reset();
      fetchUserPackages();
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users'] });
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับรีเซ็ตยอดการใช้งานแพ็คเกจ
  const resetPackageUsageMutation = useMutation({
    mutationFn: async (packageId: number) => {
      try {
        const res = await fetch(`/api/admin/users/${userId}/packages/${packageId}`, {
          method: "PATCH",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ reset: true }),
          credentials: "include"
        });
        
        if (!res.ok) {
          const errorText = await res.text();
          // ตรวจสอบว่าเป็น HTML หรือไม่
          if (errorText.trim().startsWith('<!DOCTYPE') || errorText.trim().startsWith('<html')) {
            throw new Error('เซสชั่นหมดอายุหรือไม่มีสิทธิ์เข้าถึง โปรดเข้าสู่ระบบใหม่');
          }
          
          try {
            // พยายามแปลงเป็น JSON ถ้าทำได้
            const errorData = JSON.parse(errorText);
            throw new Error(errorData.message || `HTTP error! status: ${res.status}`);
          } catch (e) {
            // ถ้าแปลงเป็น JSON ไม่ได้ ใช้ข้อความเดิม
            throw new Error(`เกิดข้อผิดพลาด: ${errorText.substring(0, 100)}`);
          }
        }
        
        return await res.json();
      } catch (error: any) {
        console.error("Error resetting package usage:", error);
        throw new Error(error.message || "เกิดข้อผิดพลาดในการรีเซ็ตยอดการใช้งานแพ็คเกจ");
      }
    },
    onSuccess: () => {
      toast({
        title: "รีเซ็ตยอดการใช้งานสำเร็จ",
        description: "รีเซ็ตยอดการใช้งานแพ็คเกจเรียบร้อยแล้ว",
      });
      fetchUserPackages();
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับเปลี่ยนสถานะแพ็คเกจ (active/inactive)
  const togglePackageStatusMutation = useMutation({
    mutationFn: async ({ packageId, isActive }: { packageId: number; isActive: boolean }) => {
      try {
        const res = await fetch(`/api/admin/users/${userId}/packages/${packageId}`, {
          method: "PATCH",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ isActive }),
          credentials: "include"
        });
        
        if (!res.ok) {
          const errorText = await res.text();
          // ตรวจสอบว่าเป็น HTML หรือไม่
          if (errorText.trim().startsWith('<!DOCTYPE') || errorText.trim().startsWith('<html')) {
            throw new Error('เซสชั่นหมดอายุหรือไม่มีสิทธิ์เข้าถึง โปรดเข้าสู่ระบบใหม่');
          }
          
          try {
            // พยายามแปลงเป็น JSON ถ้าทำได้
            const errorData = JSON.parse(errorText);
            throw new Error(errorData.message || `HTTP error! status: ${res.status}`);
          } catch (e) {
            // ถ้าแปลงเป็น JSON ไม่ได้ ใช้ข้อความเดิม
            throw new Error(`เกิดข้อผิดพลาด: ${errorText.substring(0, 100)}`);
          }
        }
        
        return await res.json();
      } catch (error: any) {
        console.error("Error updating package status:", error);
        throw new Error(error.message || "เกิดข้อผิดพลาดในการอัปเดตสถานะแพ็คเกจ");
      }
    },
    onSuccess: () => {
      toast({
        title: "อัปเดตสถานะแพ็คเกจสำเร็จ",
        description: "อัปเดตสถานะแพ็คเกจเรียบร้อยแล้ว",
      });
      fetchUserPackages();
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับเพิ่ม API Key
  const addApiKeyMutation = useMutation({
    mutationFn: async (data: AddApiKeyFormValues) => {
      try {
        const res = await fetch(`/api/admin/users/${userId}/api-keys`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(data),
          credentials: "include"
        });
        
        if (!res.ok) {
          const errorText = await res.text();
          // ตรวจสอบว่าเป็น HTML หรือไม่
          if (errorText.trim().startsWith('<!DOCTYPE') || errorText.trim().startsWith('<html')) {
            throw new Error('เซสชั่นหมดอายุหรือไม่มีสิทธิ์เข้าถึง โปรดเข้าสู่ระบบใหม่');
          }
          
          try {
            // พยายามแปลงเป็น JSON ถ้าทำได้
            const errorData = JSON.parse(errorText);
            throw new Error(errorData.message || `HTTP error! status: ${res.status}`);
          } catch (e) {
            // ถ้าแปลงเป็น JSON ไม่ได้ ใช้ข้อความเดิม
            throw new Error(`เกิดข้อผิดพลาด: ${errorText.substring(0, 100)}`);
          }
        }
        
        return await res.json();
      } catch (error: any) {
        console.error("Error adding API key:", error);
        throw new Error(error.message || "เกิดข้อผิดพลาดในการเพิ่ม API Key");
      }
    },
    onSuccess: () => {
      toast({
        title: "เพิ่ม API Key สำเร็จ",
        description: "เพิ่ม API Key ให้ผู้ใช้งานเรียบร้อยแล้ว",
      });
      addApiKeyForm.reset();
      fetchUserApiKeys();
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับลบ API Key
  const deleteApiKeyMutation = useMutation({
    mutationFn: async (apiKeyId: number) => {
      try {
        const res = await fetch(`/api/admin/users/${userId}/api-keys/${apiKeyId}`, {
          method: "DELETE",
          headers: { "Content-Type": "application/json" },
          credentials: "include"
        });
        
        if (!res.ok) {
          const errorText = await res.text();
          // ตรวจสอบว่าเป็น HTML หรือไม่
          if (errorText.trim().startsWith('<!DOCTYPE') || errorText.trim().startsWith('<html')) {
            throw new Error('เซสชั่นหมดอายุหรือไม่มีสิทธิ์เข้าถึง โปรดเข้าสู่ระบบใหม่');
          }
          
          try {
            // พยายามแปลงเป็น JSON ถ้าทำได้
            const errorData = JSON.parse(errorText);
            throw new Error(errorData.message || `HTTP error! status: ${res.status}`);
          } catch (e) {
            // ถ้าแปลงเป็น JSON ไม่ได้ ใช้ข้อความเดิม
            throw new Error(`เกิดข้อผิดพลาด: ${errorText.substring(0, 100)}`);
          }
        }
        
        return await res.json();
      } catch (error: any) {
        console.error("Error deleting API key:", error);
        throw new Error(error.message || "เกิดข้อผิดพลาดในการลบ API Key");
      }
    },
    onSuccess: () => {
      toast({
        title: "ลบ API Key สำเร็จ",
        description: "ลบ API Key เรียบร้อยแล้ว",
      });
      fetchUserApiKeys();
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Mutation สำหรับรีเจนเนอเรท API Key
  const regenerateApiKeyMutation = useMutation({
    mutationFn: async (apiKeyId: number) => {
      try {
        const res = await fetch(`/api/admin/users/${userId}/api-keys/${apiKeyId}/regenerate`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          credentials: "include"
        });
        
        if (!res.ok) {
          const errorText = await res.text();
          // ตรวจสอบว่าเป็น HTML หรือไม่
          if (errorText.trim().startsWith('<!DOCTYPE') || errorText.trim().startsWith('<html')) {
            throw new Error('เซสชั่นหมดอายุหรือไม่มีสิทธิ์เข้าถึง โปรดเข้าสู่ระบบใหม่');
          }
          
          try {
            // พยายามแปลงเป็น JSON ถ้าทำได้
            const errorData = JSON.parse(errorText);
            throw new Error(errorData.message || `HTTP error! status: ${res.status}`);
          } catch (e) {
            // ถ้าแปลงเป็น JSON ไม่ได้ ใช้ข้อความเดิม
            throw new Error(`เกิดข้อผิดพลาด: ${errorText.substring(0, 100)}`);
          }
        }
        
        return await res.json();
      } catch (error: any) {
        console.error("Error regenerating API key:", error);
        throw new Error(error.message || "เกิดข้อผิดพลาดในการสร้าง API Key ใหม่");
      }
    },
    onSuccess: () => {
      toast({
        title: "สร้าง API Key ใหม่สำเร็จ",
        description: "API Key ถูกสร้างใหม่เรียบร้อยแล้ว",
      });
      fetchUserApiKeys();
    },
    onError: (error: Error) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center text-xl">
            <UserCog className="h-6 w-6 mr-2 text-primary" />
            จัดการผู้ใช้งาน: {user?.username}
          </DialogTitle>
          <DialogDescription>
            {user && (
              <div className="flex items-center gap-2 mt-2">
                <Badge variant={user.status === 'active' ? 'default' : user.status === 'inactive' ? 'outline' : 'destructive'}>
                  {translateUserStatus(user.status)}
                </Badge>
                <Badge variant={user.role === 'admin' ? 'default' : 'secondary'}>
                  {translateUserRole(user.role)}
                </Badge>
                <Badge variant="outline" className="ml-auto">
                  เครดิต: {formatCurrency(user.credit || 0)}
                </Badge>
              </div>
            )}
          </DialogDescription>
        </DialogHeader>
        
        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : (
          <Tabs defaultValue="profile" value={activeTab} onValueChange={setActiveTab} className="mt-2">
            <TabsList className="grid grid-cols-6 mb-4">
              <TabsTrigger value="profile" className="flex items-center gap-1">
                <User className="h-4 w-4" />
                <span className="hidden sm:inline">ข้อมูลทั่วไป</span>
              </TabsTrigger>
              <TabsTrigger value="packages" className="flex items-center gap-1">
                <Package className="h-4 w-4" />
                <span className="hidden sm:inline">แพ็คเกจ</span>
              </TabsTrigger>
              <TabsTrigger value="api-keys" className="flex items-center gap-1">
                <Key className="h-4 w-4" />
                <span className="hidden sm:inline">API Keys</span>
              </TabsTrigger>
              <TabsTrigger value="stats" className="flex items-center gap-1">
                <BarChart4 className="h-4 w-4" />
                <span className="hidden sm:inline">สถิติ</span>
              </TabsTrigger>
              <TabsTrigger value="verification-history" className="flex items-center gap-1">
                <History className="h-4 w-4" />
                <span className="hidden sm:inline">สลิป</span>
              </TabsTrigger>
              <TabsTrigger value="topup-history" className="flex items-center gap-1">
                <CreditCard className="h-4 w-4" />
                <span className="hidden sm:inline">เติมเงิน</span>
              </TabsTrigger>
            </TabsList>
            
            {/* แท็บข้อมูลทั่วไป */}
            <TabsContent value="profile">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <User className="h-5 w-5 mr-2 text-primary" />
                    ข้อมูลผู้ใช้งาน
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Form {...editUserForm}>
                    <form onSubmit={editUserForm.handleSubmit(onSubmitEditForm)} className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={editUserForm.control}
                          name="username"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>ชื่อผู้ใช้</FormLabel>
                              <FormControl>
                                <Input {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={editUserForm.control}
                          name="email"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>อีเมล</FormLabel>
                              <FormControl>
                                <Input {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={editUserForm.control}
                          name="firstName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>ชื่อ</FormLabel>
                              <FormControl>
                                <Input {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={editUserForm.control}
                          name="lastName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>นามสกุล</FormLabel>
                              <FormControl>
                                <Input {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={editUserForm.control}
                          name="companyName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>ชื่อบริษัท</FormLabel>
                              <FormControl>
                                <Input {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <div className="grid grid-cols-2 gap-4">
                          <FormField
                            control={editUserForm.control}
                            name="status"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>สถานะ</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  defaultValue={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="เลือกสถานะ" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="active">ใช้งาน</SelectItem>
                                    <SelectItem value="inactive">ไม่ใช้งาน</SelectItem>
                                    <SelectItem value="suspended">ระงับการใช้งาน</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={editUserForm.control}
                            name="role"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>บทบาท</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  defaultValue={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="เลือกบทบาท" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="user">ผู้ใช้งาน</SelectItem>
                                    <SelectItem value="admin">ผู้ดูแลระบบ</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>
                      <div className="flex justify-end mt-4">
                        <Button type="submit" disabled={updateUserMutation.isPending}>
                          {updateUserMutation.isPending && <RefreshCw className="mr-2 h-4 w-4 animate-spin" />}
                          บันทึกข้อมูล
                        </Button>
                      </div>
                    </form>
                  </Form>
                </CardContent>
              </Card>

              <Card className="mt-4">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <CreditCard className="h-5 w-5 mr-2 text-primary" />
                    จัดการเครดิต
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Form {...creditForm}>
                    <form onSubmit={creditForm.handleSubmit(onSubmitCreditForm)} className="space-y-4">
                      <div className="flex gap-4 items-center">
                        <div className="space-x-2">
                          <Button
                            type="button"
                            variant={creditAction === 'add' ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setCreditAction('add')}
                          >
                            <Plus className="h-4 w-4 mr-1" />
                            เพิ่มเครดิต
                          </Button>
                          <Button
                            type="button"
                            variant={creditAction === 'deduct' ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setCreditAction('deduct')}
                          >
                            <Minus className="h-4 w-4 mr-1" />
                            หักเครดิต
                          </Button>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={creditForm.control}
                          name="amount"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>จำนวนเงิน (บาท)</FormLabel>
                              <FormControl>
                                <Input type="number" min="0" step="0.01" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={creditForm.control}
                          name="description"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>คำอธิบาย</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder={creditAction === 'add' ? 'เติมเครดิต' : 'หักเครดิต'} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <div className="flex justify-end">
                        <Button type="submit" disabled={creditAction === 'add' ? addCreditMutation.isPending : deductCreditMutation.isPending}>
                          {(creditAction === 'add' ? addCreditMutation.isPending : deductCreditMutation.isPending) && (
                            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                          )}
                          {creditAction === 'add' ? 'เพิ่มเครดิต' : 'หักเครดิต'}
                        </Button>
                      </div>
                    </form>
                  </Form>
                </CardContent>
              </Card>

              <Card className="mt-4">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <Shield className="h-5 w-5 mr-2 text-primary" />
                    เปลี่ยนรหัสผ่าน
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Form {...changePasswordForm}>
                    <form onSubmit={changePasswordForm.handleSubmit(onSubmitChangePasswordForm)} className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={changePasswordForm.control}
                          name="newPassword"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>รหัสผ่านใหม่</FormLabel>
                              <FormControl>
                                <Input type="password" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={changePasswordForm.control}
                          name="confirmPassword"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>ยืนยันรหัสผ่านใหม่</FormLabel>
                              <FormControl>
                                <Input type="password" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <div className="flex justify-end">
                        <Button type="submit" disabled={changePasswordMutation.isPending}>
                          {changePasswordMutation.isPending && <RefreshCw className="mr-2 h-4 w-4 animate-spin" />}
                          เปลี่ยนรหัสผ่าน
                        </Button>
                      </div>
                    </form>
                  </Form>
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* แท็บแพ็คเกจ */}
            <TabsContent value="packages">
              <Card>
                <CardHeader className="flex flex-row items-start justify-between">
                  <div>
                    <CardTitle className="text-lg flex items-center">
                      <Package className="h-5 w-5 mr-2 text-primary" />
                      แพ็คเกจของผู้ใช้
                    </CardTitle>
                    <CardDescription className="mt-1">
                      แพ็คเกจที่ผู้ใช้สมัครใช้งาน
                    </CardDescription>
                  </div>
                  {packages?.length ? (
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => setActiveTab("packages-add")}
                      className="hidden md:flex"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      เพิ่มแพ็คเกจ
                    </Button>
                  ) : null}
                </CardHeader>
                <CardContent>
                  {userPackages?.length ? (
                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>แพ็คเกจ</TableHead>
                            <TableHead>ระยะเวลา</TableHead>
                            <TableHead>การใช้งาน</TableHead>
                            <TableHead>สถานะ</TableHead>
                            <TableHead className="text-right">จัดการ</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {userPackages.map((userPackage) => {
                            const isActive = userPackage.isActive;
                            const isExpired = new Date(userPackage.endDate) < new Date();
                            
                            return (
                              <TableRow key={userPackage.id}>
                                <TableCell className="font-medium">
                                  {userPackage.package.name}
                                </TableCell>
                                <TableCell>
                                  <div className="flex flex-col">
                                    <span>{userPackage.durationMonths} เดือน</span>
                                    <span className="text-xs text-muted-foreground">
                                      {formatDate(userPackage.startDate, "dd/MM/yyyy")} - {formatDate(userPackage.endDate, "dd/MM/yyyy")}
                                    </span>
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <div className="flex flex-col">
                                    <span>
                                      {userPackage.requestsUsed} / {userPackage.package.requestsLimit || "ไม่จำกัด"}
                                    </span>
                                    <div className="h-2 w-full bg-muted mt-1 rounded-full overflow-hidden">
                                      <div 
                                        className={`h-full ${isActive ? 'bg-primary' : 'bg-muted-foreground'}`} 
                                        style={{ 
                                          width: userPackage.package.requestsLimit 
                                            ? `${Math.min(100, (userPackage.requestsUsed / userPackage.package.requestsLimit) * 100)}%` 
                                            : '5%' 
                                        }} 
                                      />
                                    </div>
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <Badge 
                                    variant={
                                      !isActive ? "outline" :
                                      isExpired ? "destructive" : "default"
                                    }
                                  >
                                    {!isActive 
                                      ? "ไม่ได้ใช้งาน" 
                                      : isExpired 
                                        ? "หมดอายุ" 
                                        : "ใช้งานอยู่"}
                                  </Badge>
                                </TableCell>
                                <TableCell className="text-right">
                                  <div className="flex items-center justify-end gap-2">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => resetPackageUsageMutation.mutate(userPackage.id)}
                                      disabled={resetPackageUsageMutation.isPending}
                                    >
                                      <RefreshCw className={`h-4 w-4 mr-1 ${resetPackageUsageMutation.isPending ? 'animate-spin' : ''}`} />
                                      รีเซ็ต
                                    </Button>
                                    
                                    <Button
                                      variant={isActive ? "outline" : "default"}
                                      size="sm"
                                      onClick={() => togglePackageStatusMutation.mutate({ 
                                        packageId: userPackage.id, 
                                        isActive: !isActive 
                                      })}
                                      disabled={togglePackageStatusMutation.isPending}
                                    >
                                      {togglePackageStatusMutation.isPending ? (
                                        <RefreshCw className="h-4 w-4 mr-1 animate-spin" />
                                      ) : isActive ? (
                                        <AlertCircle className="h-4 w-4 mr-1" />
                                      ) : (
                                        <RefreshCw className="h-4 w-4 mr-1" />
                                      )}
                                      {isActive ? "ระงับ" : "เปิดใช้งาน"}
                                    </Button>
                                  </div>
                                </TableCell>
                              </TableRow>
                            );
                          })}
                        </TableBody>
                      </Table>
                    </div>
                  ) : (
                    <div className="text-center py-8 border rounded-lg">
                      <Package className="h-12 w-12 mx-auto text-muted-foreground/60" />
                      <h3 className="mt-2 font-medium">ไม่พบแพ็คเกจ</h3>
                      <p className="text-sm text-muted-foreground mt-1">ผู้ใช้นี้ยังไม่มีแพ็คเกจที่สมัครใช้งาน</p>
                      {packages?.length ? (
                        <Button 
                          variant="outline" 
                          className="mt-4"
                          onClick={() => setActiveTab("packages-add")}
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          เพิ่มแพ็คเกจให้ผู้ใช้
                        </Button>
                      ) : null}
                    </div>
                  )}
                </CardContent>
                <CardFooter className="flex justify-center md:hidden">
                  {packages?.length ? (
                    <Button 
                      variant="outline" 
                      onClick={() => setActiveTab("packages-add")}
                      className="w-full"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      เพิ่มแพ็คเกจ
                    </Button>
                  ) : null}
                </CardFooter>
              </Card>
            </TabsContent>
            
            {/* แท็บ API Keys */}
            <TabsContent value="api-keys">
              <Card>
                <CardHeader className="flex flex-row items-start justify-between">
                  <div>
                    <CardTitle className="text-lg flex items-center">
                      <Key className="h-5 w-5 mr-2 text-primary" />
                      API Keys
                    </CardTitle>
                    <CardDescription className="mt-1">
                      จัดการ API Keys สำหรับการเข้าถึง API
                    </CardDescription>
                  </div>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setActiveTab("api-keys-add")}
                    className="hidden md:flex"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    สร้าง API Key ใหม่
                  </Button>
                </CardHeader>
                <CardContent>
                  {userApiKeys?.length ? (
                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>ชื่อ</TableHead>
                            <TableHead>การใช้งาน</TableHead>
                            <TableHead>สถานะ</TableHead>
                            <TableHead>สร้างเมื่อ</TableHead>
                            <TableHead className="text-right">จัดการ</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {userApiKeys.map((apiKey) => (
                            <TableRow key={apiKey.id}>
                              <TableCell className="font-medium">
                                <div className="flex flex-col">
                                  <span>{apiKey.name}</span>
                                  <span className="text-xs text-muted-foreground truncate max-w-[200px]" title={apiKey.apiKey}>
                                    {apiKey.apiKey.substring(0, 12)}...{apiKey.apiKey.substring(apiKey.apiKey.length - 8)}
                                  </span>
                                </div>
                              </TableCell>
                              <TableCell>
                                {apiKey.usageLimit ? (
                                  <div className="flex flex-col">
                                    <span>
                                      {apiKey.requestCount || 0} / {apiKey.usageLimit}
                                    </span>
                                    <div className="h-2 w-full bg-muted mt-1 rounded-full overflow-hidden">
                                      <div 
                                        className={`h-full ${apiKey.status === 'active' ? 'bg-primary' : 'bg-muted-foreground'}`} 
                                        style={{ width: `${Math.min(100, ((apiKey.requestCount || 0) / apiKey.usageLimit) * 100)}%` }} 
                                      />
                                    </div>
                                  </div>
                                ) : (
                                  <span>{apiKey.requestCount || 0} ครั้ง</span>
                                )}
                              </TableCell>
                              <TableCell>
                                <Badge 
                                  variant={
                                    apiKey.status === 'active' ? "default" : 
                                    apiKey.status === 'inactive' ? "outline" : "destructive"
                                  }
                                >
                                  {apiKey.status === 'active' ? "ใช้งานอยู่" : 
                                   apiKey.status === 'inactive' ? "ไม่ได้ใช้งาน" : "ถูกยกเลิก"}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center">
                                  <Clock className="h-3 w-3 mr-1 text-muted-foreground" />
                                  <span>{formatDate(apiKey.createdAt, "dd/MM/yyyy")}</span>
                                </div>
                              </TableCell>
                              <TableCell className="text-right">
                                <div className="flex items-center justify-end space-x-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      navigator.clipboard.writeText(apiKey.apiKey);
                                      toast({
                                        title: "คัดลอก API Key สำเร็จ",
                                        description: "คัดลอก API Key ไปยังคลิปบอร์ดเรียบร้อยแล้ว",
                                      });
                                    }}
                                  >
                                    <Copy className="h-4 w-4" />
                                    <span className="sr-only">คัดลอก API Key</span>
                                  </Button>
                                  
                                  <AlertDialog>
                                    <AlertDialogTrigger asChild>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="border-yellow-500 hover:border-yellow-600 hover:bg-yellow-50 text-yellow-600"
                                      >
                                        <RefreshCcw className="h-4 w-4" />
                                        <span className="sr-only">สร้าง API Key ใหม่</span>
                                      </Button>
                                    </AlertDialogTrigger>
                                    <AlertDialogContent>
                                      <AlertDialogHeader>
                                        <AlertDialogTitle>สร้าง API Key ใหม่</AlertDialogTitle>
                                        <AlertDialogDescription>
                                          การสร้าง API Key ใหม่จะทำให้ API Key เดิมไม่สามารถใช้งานได้ 
                                          และคุณจะต้องอัปเดต API Key ในแอปพลิเคชันของคุณ 
                                          คุณแน่ใจหรือไม่ว่าต้องการสร้าง API Key ใหม่?
                                        </AlertDialogDescription>
                                      </AlertDialogHeader>
                                      <AlertDialogFooter>
                                        <AlertDialogCancel>ยกเลิก</AlertDialogCancel>
                                        <AlertDialogAction
                                          onClick={() => regenerateApiKeyMutation.mutate(apiKey.id)}
                                          className="bg-yellow-500 hover:bg-yellow-600"
                                        >
                                          {regenerateApiKeyMutation.isPending && (
                                            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                                          )}
                                          สร้าง API Key ใหม่
                                        </AlertDialogAction>
                                      </AlertDialogFooter>
                                    </AlertDialogContent>
                                  </AlertDialog>
                                  
                                  <AlertDialog>
                                    <AlertDialogTrigger asChild>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="border-red-500 hover:border-red-600 hover:bg-red-50 text-red-600"
                                      >
                                        <Trash2 className="h-4 w-4" />
                                        <span className="sr-only">ลบ API Key</span>
                                      </Button>
                                    </AlertDialogTrigger>
                                    <AlertDialogContent>
                                      <AlertDialogHeader>
                                        <AlertDialogTitle>ลบ API Key</AlertDialogTitle>
                                        <AlertDialogDescription>
                                          การลบ API Key จะทำให้ไม่สามารถใช้งาน API ได้
                                          และไม่สามารถกู้คืนได้ คุณแน่ใจหรือไม่ว่าต้องการลบ?
                                        </AlertDialogDescription>
                                      </AlertDialogHeader>
                                      <AlertDialogFooter>
                                        <AlertDialogCancel>ยกเลิก</AlertDialogCancel>
                                        <AlertDialogAction
                                          onClick={() => deleteApiKeyMutation.mutate(apiKey.id)}
                                          className="bg-red-500 hover:bg-red-600"
                                        >
                                          {deleteApiKeyMutation.isPending && (
                                            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                                          )}
                                          ลบ API Key
                                        </AlertDialogAction>
                                      </AlertDialogFooter>
                                    </AlertDialogContent>
                                  </AlertDialog>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  ) : (
                    <div className="text-center py-8 border rounded-lg">
                      <Key className="h-12 w-12 mx-auto text-muted-foreground/60" />
                      <h3 className="mt-2 font-medium">ไม่พบ API Keys</h3>
                      <p className="text-sm text-muted-foreground mt-1">ผู้ใช้นี้ยังไม่มี API Keys</p>
                      <Button 
                        variant="outline" 
                        className="mt-4"
                        onClick={() => setActiveTab("api-keys-add")}
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        สร้าง API Key ใหม่
                      </Button>
                    </div>
                  )}
                </CardContent>
                <CardFooter className="flex justify-center md:hidden">
                  <Button 
                    variant="outline" 
                    onClick={() => setActiveTab("api-keys-add")}
                    className="w-full"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    สร้าง API Key ใหม่
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
            
            {/* แท็บสถิติ */}
            <TabsContent value="stats" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <BarChart4 className="h-5 w-5 mr-2 text-primary" />
                    สถิติการใช้งาน
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {userStatsData ? (
                    <div className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="bg-muted/30 p-4 rounded-lg border text-center">
                          <h3 className="text-sm font-medium text-muted-foreground">จำนวนการตรวจสอบทั้งหมด</h3>
                          <p className="text-3xl font-bold text-primary mt-2">{userStatsData.totalVerifications || 0}</p>
                        </div>
                        <div className="bg-muted/30 p-4 rounded-lg border text-center">
                          <h3 className="text-sm font-medium text-muted-foreground">การตรวจสอบสำเร็จ</h3>
                          <p className="text-3xl font-bold text-green-500 mt-2">{userStatsData.successfulVerifications || 0}</p>
                        </div>
                        <div className="bg-muted/30 p-4 rounded-lg border text-center">
                          <h3 className="text-sm font-medium text-muted-foreground">การตรวจสอบล้มเหลว</h3>
                          <p className="text-3xl font-bold text-red-500 mt-2">{userStatsData.failedVerifications || 0}</p>
                        </div>
                      </div>
                      
                      <div className="bg-muted/30 p-4 rounded-lg border">
                        <h3 className="text-base font-medium mb-2">การใช้งาน API</h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="bg-background p-3 rounded-md border text-center">
                            <h4 className="text-sm text-muted-foreground">เรียกใช้ API ทั้งหมด</h4>
                            <p className="text-xl font-bold mt-1">{userStatsData.apiUsage?.totalApiCalls || 0}</p>
                          </div>
                          <div className="bg-background p-3 rounded-md border text-center">
                            <h4 className="text-sm text-muted-foreground">การเรียกใช้สำเร็จ</h4>
                            <p className="text-xl font-bold text-green-500 mt-1">{userStatsData.apiUsage?.successfulApiCalls || 0}</p>
                          </div>
                          <div className="bg-background p-3 rounded-md border text-center">
                            <h4 className="text-sm text-muted-foreground">การเรียกใช้ล้มเหลว</h4>
                            <p className="text-xl font-bold text-red-500 mt-1">{userStatsData.apiUsage?.failedApiCalls || 0}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="py-8 text-center text-muted-foreground">
                      กำลังโหลดข้อมูลสถิติ...
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* แท็บประวัติการตรวจสอบสลิป */}
            <TabsContent value="verification-history" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <History className="h-5 w-5 mr-2 text-primary" />
                    ประวัติการตรวจสอบสลิป
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {slipVerifications?.length ? (
                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>วันที่</TableHead>
                            <TableHead>จำนวนเงิน</TableHead>
                            <TableHead>สถานะ</TableHead>
                            <TableHead>ที่มา</TableHead>
                            <TableHead>ใช้เครดิต</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {slipVerifications.map((verification) => (
                            <TableRow key={verification.id}>
                              <TableCell>
                                {formatDate(verification.createdAt, "dd/MM/yyyy HH:mm")}
                              </TableCell>
                              <TableCell>
                                {formatCurrency(verification.amount || 0)}
                              </TableCell>
                              <TableCell>
                                <Badge variant={
                                  verification.status === 'success' ? 'default' :
                                  verification.status === 'pending' ? 'outline' : 'destructive'
                                }>
                                  {verification.status === 'success' ? 'สำเร็จ' :
                                   verification.status === 'pending' ? 'รอดำเนินการ' : 'ล้มเหลว'}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                {verification.verificationSource === 'web' ? 'เว็บไซต์' : 'API'}
                              </TableCell>
                              <TableCell>
                                {verification.usedCredit ? `${verification.creditUsed} บาท` : 'ไม่'}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  ) : (
                    <div className="py-8 text-center text-muted-foreground">
                      ไม่พบประวัติการตรวจสอบสลิป
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* แท็บประวัติการเติมเงิน */}
            <TabsContent value="topup-history" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <CreditCard className="h-5 w-5 mr-2 text-primary" />
                    ประวัติการเติมเงิน
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {transactions?.length ? (
                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>วันที่</TableHead>
                            <TableHead>จำนวนเงิน</TableHead>
                            <TableHead>ประเภท</TableHead>
                            <TableHead>สถานะ</TableHead>
                            <TableHead>อ้างอิง</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {transactions.map((transaction) => (
                            <TableRow key={transaction.id}>
                              <TableCell>
                                {formatDate(transaction.createdAt, "dd/MM/yyyy HH:mm")}
                              </TableCell>
                              <TableCell>
                                {formatCurrency(transaction.amount || 0)}
                              </TableCell>
                              <TableCell>
                                {transaction.type || 'เติมเงิน'}
                              </TableCell>
                              <TableCell>
                                <Badge variant={
                                  transaction.status === 'completed' ? 'default' :
                                  transaction.status === 'pending' ? 'outline' : 'destructive'
                                }>
                                  {transaction.status === 'completed' ? 'สำเร็จ' :
                                   transaction.status === 'pending' ? 'รอดำเนินการ' : 'ล้มเหลว'}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                {transaction.referenceCode || '-'}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  ) : (
                    <div className="py-8 text-center text-muted-foreground">
                      ไม่พบประวัติการเติมเงิน
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        )}
        
        <DialogFooter className="flex justify-between items-center mt-4">
          <p className="text-sm text-muted-foreground">
            ID: {userId} • สร้างเมื่อ: {user && formatDate(user.createdAt, "dd/MM/yyyy")}
          </p>
          <Button onClick={() => onOpenChange(false)}>ปิด</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}