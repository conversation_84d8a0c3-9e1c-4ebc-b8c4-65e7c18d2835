import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/hooks/use-auth";
import { Navbar } from "@/components/layouts/navbar";
import { insertUserSchema } from "@shared/schema";

// กำหนด Schema สำหรับการเข้าสู่ระบบ
const loginSchema = z.object({
  username: z.string().min(1, "กรุณากรอกชื่อผู้ใช้"),
  password: z.string().min(1, "กรุณากรอกรหัสผ่าน"),
});

// กำหนด Schema สำหรับการลงทะเบียน (โดยใช้ Schema ที่มีอยู่แล้ว)
const registerSchema = insertUserSchema.extend({
  password: z.string().min(8, "รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร"),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "รหัสผ่านไม่ตรงกัน",
  path: ["confirmPassword"],
});

type LoginFormValues = z.infer<typeof loginSchema>;
type RegisterFormValues = z.infer<typeof registerSchema>;

export default function AuthPage() {
  const [activeTab, setActiveTab] = useState<"login" | "register">("login");
  const [location, setLocation] = useLocation();
  const { user, loginMutation, registerMutation } = useAuth();

  // สร้าง Form สำหรับการเข้าสู่ระบบ
  const loginForm = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: "",
      password: "",
    },
  });

  // สร้าง Form สำหรับการลงทะเบียน
  const registerForm = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      username: "",
      email: "",
      firstName: "",
      lastName: "",
      companyName: "",
      password: "",
      confirmPassword: "",
    },
  });

  // ส่งคำขอเข้าสู่ระบบ
  const onLoginSubmit = (values: LoginFormValues) => {
    loginMutation.mutate(values);
  };

  // ส่งคำขอลงทะเบียน
  const onRegisterSubmit = (values: RegisterFormValues) => {
    // ลบ confirmPassword ออกเพราะไม่ต้องส่งไปยัง API
    const { confirmPassword, ...registerData } = values;
    registerMutation.mutate(registerData);
  };

  // เมื่อมีผู้ใช้งานเข้าสู่ระบบแล้ว ให้ไปยังหน้าแดชบอร์ด
  useEffect(() => {
    if (user) {
      setLocation("/dashboard");
    }
  }, [user, setLocation]);

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-indigo-950 via-purple-900 to-indigo-900 relative">
      <Navbar />
      {/* เอฟเฟกต์พื้นหลังเทพเจ้า */}
      <div className="absolute inset-0 overflow-hidden">
        {/* ดาวระยิบระยับในจักรวาล */}
        <div className="absolute inset-0" style={{ 
          backgroundImage: 'radial-gradient(white, rgba(255, 255, 255, 0.2) 2px, transparent 2px)',
          backgroundSize: '50px 50px'
        }}></div>
        
        {/* รังสีเทพเจ้า */}
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[150vh] h-[150vh] bg-gradient-radial from-amber-500/10 via-purple-500/5 to-transparent"></div>
        
        {/* ออร่าเทพเจ้า */}
        <div className="absolute top-[30%] right-[10%] w-80 h-80 rounded-full bg-gradient-radial from-amber-400/20 to-transparent blur-xl"></div>
        <div className="absolute bottom-[20%] left-[10%] w-80 h-80 rounded-full bg-gradient-radial from-purple-400/20 to-transparent blur-xl"></div>
      </div>

      <div className="relative z-10 flex-1 flex flex-col items-center justify-center p-4 mt-10">
        {/* หัวข้อหลัก */}
        <div className="text-center mb-8">
          <h1 className="text-5xl font-bold bg-gradient-to-r from-amber-300 via-yellow-400 to-amber-500 bg-clip-text text-transparent font-serif">SLIPKUY</h1>
          <p className="mt-2 text-indigo-200">บริการตรวจสอบสลิป API ที่ทรงพลัง</p>
        </div>

        {/* กล่องเข้าสู่ระบบ */}
        <div className="w-full max-w-md">
          <div className="bg-indigo-950/40 backdrop-blur-md border border-indigo-700/50 rounded-lg overflow-hidden shadow-2xl">
            {/* แท็บเลือก */}
            <div className="flex border-b border-indigo-800/50">
              <button 
                onClick={() => setActiveTab("login")}
                className={`flex-1 py-4 text-center font-medium transition ${
                  activeTab === "login"
                    ? "bg-gradient-to-b from-amber-500/20 to-indigo-800/20 text-amber-300"
                    : "text-indigo-300 hover:text-indigo-100"
                }`}
              >
                <span className="relative">
                  เข้าสู่ระบบ
                  {activeTab === "login" && (
                    <span className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-amber-500 to-purple-500"></span>
                  )}
                </span>
              </button>
              <button 
                onClick={() => setActiveTab("register")}
                className={`flex-1 py-4 text-center font-medium transition ${
                  activeTab === "register"
                    ? "bg-gradient-to-b from-amber-500/20 to-indigo-800/20 text-amber-300"
                    : "text-indigo-300 hover:text-indigo-100"
                }`}
              >
                <span className="relative">
                  ลงทะเบียน
                  {activeTab === "register" && (
                    <span className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-amber-500 to-purple-500"></span>
                  )}
                </span>
              </button>
            </div>

            {/* เนื้อหาแบบฟอร์ม */}
            <div className="p-6">
              {activeTab === "login" ? (
                <Form {...loginForm}>
                  <form onSubmit={loginForm.handleSubmit(onLoginSubmit)} className="space-y-4">
                    <FormField
                      control={loginForm.control}
                      name="username"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-indigo-200">ชื่อผู้ใช้</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="ชื่อผู้ใช้ของคุณ"
                              className="bg-indigo-950/50 border-indigo-700 text-white"
                              value={field.value || ""}
                              onChange={field.onChange}
                              onBlur={field.onBlur}
                              name={field.name}
                              ref={field.ref}
                            />
                          </FormControl>
                          <FormMessage className="text-amber-300" />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={loginForm.control}
                      name="password"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-indigo-200">รหัสผ่าน</FormLabel>
                          <FormControl>
                            <Input
                              type="password"
                              placeholder="รหัสผ่านของคุณ"
                              className="bg-indigo-950/50 border-indigo-700 text-white"
                              value={field.value || ""}
                              onChange={field.onChange}
                              onBlur={field.onBlur}
                              name={field.name}
                              ref={field.ref}
                            />
                          </FormControl>
                          <FormMessage className="text-amber-300" />
                        </FormItem>
                      )}
                    />

                    <Button
                      type="submit"
                      className="w-full bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white mt-2"
                      disabled={loginMutation.isPending}
                    >
                      {loginMutation.isPending ? "กำลังเข้าสู่ระบบ..." : "เข้าสู่ระบบ"}
                    </Button>
                  </form>
                </Form>
              ) : (
                <Form {...registerForm}>
                  <form onSubmit={registerForm.handleSubmit(onRegisterSubmit)} className="space-y-4">
                    <FormField
                      control={registerForm.control}
                      name="username"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-indigo-200">ชื่อผู้ใช้</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="ชื่อผู้ใช้ที่ต้องการ"
                              className="bg-indigo-950/50 border-indigo-700 text-white"
                              value={field.value || ""}
                              onChange={field.onChange}
                              onBlur={field.onBlur}
                              name={field.name}
                              ref={field.ref}
                            />
                          </FormControl>
                          <FormMessage className="text-amber-300" />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={registerForm.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-indigo-200">อีเมล</FormLabel>
                          <FormControl>
                            <Input
                              type="email"
                              placeholder="<EMAIL>"
                              className="bg-indigo-950/50 border-indigo-700 text-white"
                              value={field.value || ""}
                              onChange={field.onChange}
                              onBlur={field.onBlur}
                              name={field.name}
                              ref={field.ref}
                            />
                          </FormControl>
                          <FormMessage className="text-amber-300" />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={registerForm.control}
                        name="firstName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-indigo-200">ชื่อ</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="ชื่อของคุณ"
                                className="bg-indigo-950/50 border-indigo-700 text-white"
                                value={field.value || ""}
                                onChange={field.onChange}
                                onBlur={field.onBlur}
                                name={field.name}
                                ref={field.ref}
                              />
                            </FormControl>
                            <FormMessage className="text-amber-300" />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={registerForm.control}
                        name="lastName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-indigo-200">นามสกุล</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="นามสกุลของคุณ"
                                className="bg-indigo-950/50 border-indigo-700 text-white"
                                value={field.value || ""}
                                onChange={field.onChange}
                                onBlur={field.onBlur}
                                name={field.name}
                                ref={field.ref}
                              />
                            </FormControl>
                            <FormMessage className="text-amber-300" />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={registerForm.control}
                      name="companyName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-indigo-200">ชื่อบริษัท (ไม่บังคับ)</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="ชื่อบริษัทของคุณ"
                              className="bg-indigo-950/50 border-indigo-700 text-white"
                              value={field.value || ""}
                              onChange={field.onChange}
                              onBlur={field.onBlur}
                              name={field.name}
                              ref={field.ref}
                            />
                          </FormControl>
                          <FormMessage className="text-amber-300" />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={registerForm.control}
                        name="password"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-indigo-200">รหัสผ่าน</FormLabel>
                            <FormControl>
                              <Input
                                type="password"
                                placeholder="รหัสผ่าน"
                                className="bg-indigo-950/50 border-indigo-700 text-white"
                                value={field.value || ""}
                                onChange={field.onChange}
                                onBlur={field.onBlur}
                                name={field.name}
                                ref={field.ref}
                              />
                            </FormControl>
                            <FormMessage className="text-amber-300" />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={registerForm.control}
                        name="confirmPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-indigo-200">ยืนยันรหัสผ่าน</FormLabel>
                            <FormControl>
                              <Input
                                type="password"
                                placeholder="ยืนยันรหัสผ่าน"
                                className="bg-indigo-950/50 border-indigo-700 text-white"
                                value={field.value || ""}
                                onChange={field.onChange}
                                onBlur={field.onBlur}
                                name={field.name}
                                ref={field.ref}
                              />
                            </FormControl>
                            <FormMessage className="text-amber-300" />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="flex items-start mt-2">
                      <div className="flex items-center h-5">
                        <input
                          id="terms"
                          type="checkbox"
                          className="h-4 w-4 border-indigo-600 rounded focus:ring-amber-500"
                          required
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <label htmlFor="terms" className="font-medium text-indigo-200">
                          ฉันยอมรับข้อกำหนดและเงื่อนไขการใช้งาน
                        </label>
                      </div>
                    </div>

                    <Button
                      type="submit"
                      className="w-full bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white mt-2"
                      disabled={registerMutation.isPending}
                    >
                      {registerMutation.isPending ? "กำลังลงทะเบียน..." : "ลงทะเบียน"}
                    </Button>
                  </form>
                </Form>
              )}
            </div>
          </div>
        </div>

        {/* ส่วนท้าย */}
        <div className="mt-6 text-center text-indigo-400 text-sm">
          &copy; {new Date().getFullYear()} SLIPKUY. All rights reserved.
        </div>
      </div>
    </div>
  );
}