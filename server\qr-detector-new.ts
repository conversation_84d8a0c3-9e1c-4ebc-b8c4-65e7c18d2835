// ปรับเปลี่ยนวิธีการตรวจสอบ QR Code
// ตรวจจับ QR code จริงๆ ด้วย jsQR library แทนการใช้เพียง sharp
import sharp from 'sharp';
// @ts-ignore เพื่อแก้ปัญหา TypeScript กับ jsQR
import jsQR from 'jsqr';
import { logger } from './logger';

/**
 * แปลงรูปภาพให้เหมาะสมสำหรับการอ่าน QR code
 * @param imageBuffer Buffer ของรูปภาพที่ต้องการแปลง
 * @returns Promise<{ data: Uint8ClampedArray; width: number; height: number }> ข้อมูลรูปภาพที่แปลงแล้ว
 */
async function convertImageForQRReader(imageBuffer: Buffer): Promise<{
  data: Uint8ClampedArray;
  width: number;
  height: number
} | null> {
  try {
    logger.debug('🔍 QR: แปลงรูปภาพเพื่อใช้กับตัวอ่าน QR Code');

    // ปรับแต่งภาพเพื่อให้อ่าน QR code ได้ง่ายขึ้น
    // ใช้วิธีการประมวลผลภาพที่มีประสิทธิภาพมากขึ้น

    // ทดลองหลายวิธีในการประมวลผลภาพ
    // วิธีที่ 1: ปรับความคมชัดและความเข้มของสี
    const processedImageData1 = await sharp(imageBuffer)
      .grayscale()
      .sharpen()
      .normalize()
      .resize({
        width: 800,
        height: 800,
        fit: 'inside',
        withoutEnlargement: true
      })
      .raw()
      .toBuffer({ resolveWithObject: true });

    // แปลงเป็น RGBA array (Uint8ClampedArray) ที่ jsQR ต้องการ
    const { data: data1, info: info1 } = processedImageData1;
    const { width: width1, height: height1, channels: channels1 } = info1;

    // ตรวจสอบว่าข้อมูลภาพถูกต้องหรือไม่
    if (!width1 || !height1 || !channels1) {
      logger.debug('❌ QR: ข้อมูลรูปภาพไม่สมบูรณ์');
      return null;
    }

    logger.debug(`🔍 QR: แปลงภาพสำเร็จ ขนาด ${width1}x${height1} channels: ${channels1}`);

    // แปลงเป็น RGBA array (Uint8ClampedArray) ที่ jsQR ต้องการ
    const rgba = new Uint8ClampedArray(width1 * height1 * 4);

    // ถ้าเป็นภาพ grayscale (1 channel)
    if (channels1 === 1) {
      for (let i = 0; i < data1.length; i++) {
        const value = data1[i];
        const rgbaIndex = i * 4;
        rgba[rgbaIndex] = value;     // R
        rgba[rgbaIndex + 1] = value; // G
        rgba[rgbaIndex + 2] = value; // B
        rgba[rgbaIndex + 3] = 255;   // A (ทึบแสง)
      }
    }
    // ถ้าเป็นภาพ RGB (3 channels)
    else if (channels1 === 3) {
      for (let i = 0; i < data1.length; i += 3) {
        const rgbaIndex = (i / 3) * 4;
        rgba[rgbaIndex] = data1[i];       // R
        rgba[rgbaIndex + 1] = data1[i+1]; // G
        rgba[rgbaIndex + 2] = data1[i+2]; // B
        rgba[rgbaIndex + 3] = 255;        // A (ทึบแสง)
      }
    }
    // ถ้าเป็นภาพ RGBA (4 channels)
    else if (channels1 === 4) {
      for (let i = 0; i < data1.length; i += 4) {
        const rgbaIndex = i;
        rgba[rgbaIndex] = data1[i];       // R
        rgba[rgbaIndex + 1] = data1[i+1]; // G
        rgba[rgbaIndex + 2] = data1[i+2]; // B
        rgba[rgbaIndex + 3] = data1[i+3]; // A
      }
    }

    return {
      data: rgba,
      width: width1,
      height: height1
    };

  } catch (error) {
    logger.error(`❌ QR: เกิดข้อผิดพลาดในการแปลงรูปภาพ: ${error instanceof Error ? error.message : 'ไม่ทราบสาเหตุ'}`);
    return null;
  }
}

/**
 * ตรวจสอบ QR Code จากรูปภาพโดยอ่าน QR code จริงๆ
 * @param imageBuffer Buffer ของรูปภาพ
 * @returns ข้อมูลที่พบใน QR Code หรือ undefined ถ้าไม่พบ
 */
export async function detectQRCode(imageBuffer: Buffer): Promise<{ hasQRCode: boolean; qrData?: string }> {
  try {
    logger.debug('🔍 QR: เริ่มประมวลผลรูปภาพเพื่อหา QR code...');

    // ตรวจสอบว่าเป็นรูปภาพที่รองรับหรือไม่
    const isJPEG = imageBuffer.slice(0, 3).toString('hex') === 'ffd8ff';
    const isPNG = imageBuffer.slice(0, 8).toString('hex') === '89504e470d0a1a0a';

    if (!isJPEG && !isPNG) {
      logger.debug('❌ QR: ไม่ใช่ไฟล์รูปภาพที่รองรับ (ต้องเป็น JPEG/PNG)');
      return { hasQRCode: false };
    }

    // ตรวจสอบขนาดไฟล์
    const fileSizeKB = Math.round(imageBuffer.length / 1024);
    if (imageBuffer.length < 1000 || imageBuffer.length > 10 * 1024 * 1024) {
      logger.debug(`❌ QR: ขนาดไฟล์ไม่อยู่ในเกณฑ์ที่กำหนด (${fileSizeKB} KB)`);
      return { hasQRCode: false };
    }

    try {
      // แปลงรูปภาพให้เหมาะสมสำหรับการอ่าน QR code
      const imageData = await convertImageForQRReader(imageBuffer);

      if (!imageData) {
        logger.debug('❌ QR: ไม่สามารถแปลงรูปภาพเพื่ออ่าน QR code ได้');
        return { hasQRCode: false };
      }

      // อ่าน QR code จากรูปภาพ
      logger.debug(`🔍 QR: เริ่มอ่าน QR code จากรูปภาพขนาด ${imageData.width}x${imageData.height}`);

      try {
        // ใช้ค่าพารามิเตอร์ที่เหมาะสมที่สุดสำหรับการอ่าน QR code ในสลิปธนาคาร
        const code = jsQR(imageData.data, imageData.width, imageData.height, {
          inversionAttempts: 'attemptBoth',  // ลองทั้งแบบปกติและกลับขาวดำ
          canOverwriteImage: true,           // อนุญาตให้แก้ไขข้อมูลภาพเพื่อเพิ่มประสิทธิภาพ
        });

        if (!code) {
          logger.debug('❌ QR: ไม่พบ QR code ในรูปภาพด้วยวิธีแรก - ลองวิธีที่สอง');

          // ทดลองปรับแต่งภาพอีกครั้งด้วยวิธีที่แตกต่าง
          // เพิ่มความคมชัดมากขึ้น และปรับความสว่าง
          const processedBuffer = await sharp(imageBuffer)
            .grayscale()
            .sharpen(2, 1.5, 1.5)  // เพิ่มความคมชัดมากขึ้น
            .normalize()
            .gamma(1.5)            // ปรับความสว่าง
            .toBuffer();

          // แปลงรูปภาพที่ผ่านการประมวลผลแล้วเป็นข้อมูลที่ jsQR สามารถใช้ได้
          const processedImageData = await sharp(processedBuffer)
            .raw()
            .toBuffer({ resolveWithObject: true });

          const { data, info } = processedImageData;
          const { width, height, channels } = info;

          if (!width || !height || !channels) {
            logger.debug('❌ QR: ข้อมูลรูปภาพไม่สมบูรณ์ในการประมวลผลครั้งที่สอง');
            return { hasQRCode: false };
          }

          // แปลงเป็น RGBA array
          const rgba = new Uint8ClampedArray(width * height * 4);

          if (channels === 1) {
            for (let i = 0; i < data.length; i++) {
              const value = data[i];
              const rgbaIndex = i * 4;
              rgba[rgbaIndex] = value;     // R
              rgba[rgbaIndex + 1] = value; // G
              rgba[rgbaIndex + 2] = value; // B
              rgba[rgbaIndex + 3] = 255;   // A
            }
          } else if (channels === 3) {
            for (let i = 0; i < data.length; i += 3) {
              const rgbaIndex = (i / 3) * 4;
              rgba[rgbaIndex] = data[i];       // R
              rgba[rgbaIndex + 1] = data[i+1]; // G
              rgba[rgbaIndex + 2] = data[i+2]; // B
              rgba[rgbaIndex + 3] = 255;       // A
            }
          } else if (channels === 4) {
            for (let i = 0; i < data.length; i++) {
              rgba[i] = data[i];
            }
          }

          // ลองอ่าน QR code อีกครั้งด้วยการตั้งค่าที่แตกต่าง
          const code2 = jsQR(rgba, width, height, {
            inversionAttempts: 'attemptBoth',
            canOverwriteImage: true,
            greyScaleWeights: {
              red: 0.3,
              green: 0.6,
              blue: 0.1
            }
          });

          if (!code2) {
            logger.debug('❌ QR: ไม่พบ QR code ในรูปภาพแม้จะลองหลายวิธี');
            return { hasQRCode: false };
          }

          // พบ QR code และอ่านข้อมูลได้จากวิธีที่สอง
          logger.debug(`✅ QR: พบ QR code ในรูปภาพด้วยวิธีที่สอง: ${code2.data.substring(0, 50)}${code2.data.length > 50 ? '...' : ''}`);

          // ตรวจสอบว่าข้อมูลใน QR code เป็นลิงก์เว็บไซต์หรือไม่
          const isWebsiteLink = /https?:\/\/|www\.|\.com|\.org|\.net|\.info|\.co|\.io/i.test(code2.data);

          if (isWebsiteLink) {
            logger.debug('❌ QR: ข้อมูลใน QR code เป็นลิงก์เว็บไซต์ซึ่งไม่ได้รับอนุญาต');
            return { hasQRCode: false };
          }

          // ตรวจสอบว่าข้อมูลใน QR code มีเฉพาะตัวเลขและตัวอักษรที่ยอมรับสำหรับสลิปธนาคารหรือไม่
          // รูปแบบที่ยอมรับคือต้องมีเลขตัวเลขเป็นหลัก อาจมีตัวอักษรบางตัวปนได้
          const containsMainlyNumbers = /^\d{2,}|[0-9A-Z]{10,}/i.test(code2.data);

          if (!containsMainlyNumbers) {
            logger.debug('❌ QR: ข้อมูลใน QR code ไม่เข้าข่ายรูปแบบของสลิปธนาคาร');
            return { hasQRCode: false };
          }

          return {
            hasQRCode: true,
            qrData: code2.data  // ข้อมูลจริงจาก QR code
          };
        }

        // พบ QR code และอ่านข้อมูลได้จากวิธีแรก
        logger.debug(`✅ QR: พบ QR code ในรูปภาพด้วยวิธีแรก: ${code.data.substring(0, 50)}${code.data.length > 50 ? '...' : ''}`);

        // ตรวจสอบว่าข้อมูลใน QR code เป็นลิงก์เว็บไซต์หรือไม่
        const isWebsiteLink = /https?:\/\/|www\.|\.com|\.org|\.net|\.info|\.co|\.io/i.test(code.data);

        if (isWebsiteLink) {
          logger.debug('❌ QR: ข้อมูลใน QR code เป็นลิงก์เว็บไซต์ซึ่งไม่ได้รับอนุญาต');
          return { hasQRCode: false };
        }

        // ตรวจสอบว่าข้อมูลใน QR code มีเฉพาะตัวเลขและตัวอักษรที่ยอมรับสำหรับสลิปธนาคารหรือไม่
        // รูปแบบที่ยอมรับคือต้องมีเลขตัวเลขเป็นหลัก อาจมีตัวอักษรบางตัวปนได้
        const containsMainlyNumbers = /^\d{2,}|[0-9A-Z]{10,}/i.test(code.data);

        if (!containsMainlyNumbers) {
          logger.debug('❌ QR: ข้อมูลใน QR code ไม่เข้าข่ายรูปแบบของสลิปธนาคาร');
          return { hasQRCode: false };
        }

        return {
          hasQRCode: true,
          qrData: code.data  // ข้อมูลจริงจาก QR code
        };
      } catch (qrError) {
        logger.error(`❌ QR: เกิดข้อผิดพลาดเมื่ออ่าน QR code: ${qrError instanceof Error ? qrError.message : 'ไม่ทราบสาเหตุ'}`);
        return { hasQRCode: false };
      }
    } catch (convertError) {
      logger.error(`❌ QR: เกิดข้อผิดพลาดเมื่อแปลงรูปภาพ: ${convertError instanceof Error ? convertError.message : 'ไม่ทราบสาเหตุ'}`);
      return { hasQRCode: false };
    }
  } catch (error) {
    logger.error(`❌ QR: เกิดข้อผิดพลาดในการอ่าน QR code: ${error instanceof Error ? error.message : 'ไม่ทราบสาเหตุ'}`);
    return { hasQRCode: false };
  }
}

/**
 * ตรวจสอบว่ารูปภาพมี QR Code หรือไม่
 *
 * @param imageBuffer Buffer ของรูปภาพที่ต้องการตรวจสอบ
 * @returns Promise<boolean> true ถ้าพบ QR Code, false ถ้าไม่พบ
 */
export async function hasQRCode(imageBuffer: Buffer): Promise<boolean> {
  const result = await detectQRCode(imageBuffer);
  return result.hasQRCode;
}