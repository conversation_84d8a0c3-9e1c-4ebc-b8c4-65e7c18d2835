export default {
  apps: [{
    name: "slipkuy",
    script: "dist/index.js",
    instances: "max",
    exec_mode: "cluster",
    env: {
      NODE_ENV: "production",
      PORT: 4000,
      DATABASE_URL: "postgresql://slipkuy_user:slipkuy_password@localhost:5432/slipkuy_db",
      REDIS_HOST: "localhost",
      REDIS_PORT: 6379,
      REDIS_PASSWORD: "",
      REDIS_DB: 0
    },
    watch: false,
    max_memory_restart: "1G",
    log_date_format: "YYYY-MM-DD HH:mm:ss Z",
    error_file: "logs/pm2-error.log",
    out_file: "logs/pm2-out.log",
    merge_logs: true
  }]
};
