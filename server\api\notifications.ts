import { Request, Response } from 'express';
import { db } from '../db';
import { eq, and, desc } from 'drizzle-orm';
import { isAdmin } from '../auth';
import { notifications, alertSettings, insertAlertSettingSchema, insertNotificationSchema } from '@shared/schema';

// ดึงการแจ้งเตือนของผู้ใช้
export async function getUserNotifications(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'กรุณาเข้าสู่ระบบ' });
    }

    const userId = req.user.id;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 20;
    const page = req.query.page ? parseInt(req.query.page as string) : 1;
    const unreadOnly = req.query.unreadOnly === 'true';
    const offset = (page - 1) * limit;

    let query = db.select().from(notifications)
      .where(eq(notifications.userId, userId))
      .orderBy(desc(notifications.createdAt))
      .limit(limit)
      .offset(offset);

    if (unreadOnly) {
      query = db.select().from(notifications)
        .where(and(
          eq(notifications.userId, userId),
          eq(notifications.isRead, false)
        ))
        .orderBy(desc(notifications.createdAt))
        .limit(limit)
        .offset(offset);
    }

    const notificationsList = await query;
    const totalCount = await db.select({ count: db.fn.count() }).from(notifications)
      .where(eq(notifications.userId, userId));

    res.json({
      notifications: notificationsList,
      pagination: {
        total: parseInt(totalCount[0].count as string),
        page,
        limit,
        totalPages: Math.ceil(parseInt(totalCount[0].count as string) / limit)
      }
    });
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการดึงการแจ้งเตือน:', error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการดึงการแจ้งเตือน' });
  }
}

// อ่านการแจ้งเตือน (ทำเครื่องหมายว่าอ่านแล้ว)
export async function markNotificationAsRead(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'กรุณาเข้าสู่ระบบ' });
    }

    const userId = req.user.id;
    const notificationId = parseInt(req.params.id);

    // ตรวจสอบว่าการแจ้งเตือนนี้เป็นของผู้ใช้จริงหรือไม่
    const notification = await db.select().from(notifications)
      .where(and(
        eq(notifications.id, notificationId),
        eq(notifications.userId, userId)
      ));

    if (notification.length === 0) {
      return res.status(404).json({ message: 'ไม่พบการแจ้งเตือน' });
    }

    // อัปเดตสถานะการอ่าน
    await db.update(notifications)
      .set({
        isRead: true,
        readAt: new Date(),
        updatedAt: new Date()
      })
      .where(eq(notifications.id, notificationId));

    res.json({ message: 'อ่านการแจ้งเตือนเรียบร้อย' });
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการอ่านการแจ้งเตือน:', error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการอ่านการแจ้งเตือน' });
  }
}

// ดึงการตั้งค่าการแจ้งเตือนของผู้ใช้
export async function getAlertSettings(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'กรุณาเข้าสู่ระบบ' });
    }

    const userId = req.user.id;
    const settings = await db.select().from(alertSettings)
      .where(eq(alertSettings.userId, userId));

    res.json(settings);
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการดึงการตั้งค่าการแจ้งเตือน:', error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการดึงการตั้งค่าการแจ้งเตือน' });
  }
}

// อัปเดตการตั้งค่าการแจ้งเตือน
export async function updateAlertSetting(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'กรุณาเข้าสู่ระบบ' });
    }

    const userId = req.user.id;
    const settingId = parseInt(req.params.id);

    // ตรวจสอบว่าการตั้งค่านี้เป็นของผู้ใช้จริงหรือไม่
    const setting = await db.select().from(alertSettings)
      .where(and(
        eq(alertSettings.id, settingId),
        eq(alertSettings.userId, userId)
      ));

    if (setting.length === 0) {
      return res.status(404).json({ message: 'ไม่พบการตั้งค่าการแจ้งเตือน' });
    }

    // อัปเดตการตั้งค่า
    const updatedSettings = {
      ...req.body,
      updatedAt: new Date()
    };

    await db.update(alertSettings)
      .set(updatedSettings)
      .where(eq(alertSettings.id, settingId));

    res.json({ message: 'อัปเดตการตั้งค่าการแจ้งเตือนเรียบร้อย' });
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการอัปเดตการตั้งค่าการแจ้งเตือน:', error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการอัปเดตการตั้งค่าการแจ้งเตือน' });
  }
}

// อ่านการแจ้งเตือนทั้งหมด (ทำเครื่องหมายว่าอ่านแล้วทั้งหมด)
export async function markAllNotificationsAsRead(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'กรุณาเข้าสู่ระบบ' });
    }

    const userId = req.user.id;

    // อัปเดตสถานะการอ่านทั้งหมด
    await db.update(notifications)
      .set({
        isRead: true,
        readAt: new Date(),
        updatedAt: new Date()
      })
      .where(and(
        eq(notifications.userId, userId),
        eq(notifications.isRead, false)
      ));

    res.json({ message: 'อ่านการแจ้งเตือนทั้งหมดเรียบร้อย' });
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการอ่านการแจ้งเตือนทั้งหมด:', error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการอ่านการแจ้งเตือนทั้งหมด' });
  }
}

// สร้างการแจ้งเตือนใหม่ (สำหรับผู้ดูแลระบบเท่านั้น)
export async function createNotification(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'กรุณาเข้าสู่ระบบ' });
    }

    if (!isAdmin(req)) {
      return res.status(403).json({ message: 'คุณไม่มีสิทธิ์ในการเข้าถึง' });
    }

    const data = insertNotificationSchema.parse(req.body);
    const result = await db.insert(notifications).values({
      ...data,
      createdAt: new Date(),
      updatedAt: new Date()
    }).returning();

    res.status(201).json(result[0]);
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการสร้างการแจ้งเตือน:', error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการสร้างการแจ้งเตือน' });
  }
}

// ลบการแจ้งเตือน
export async function deleteNotification(req: Request, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'กรุณาเข้าสู่ระบบ' });
    }

    const userId = req.user.id;
    const notificationId = parseInt(req.params.id);

    // ตรวจสอบว่าการแจ้งเตือนนี้เป็นของผู้ใช้จริงหรือไม่ หรือเป็นผู้ดูแลระบบ
    const notification = await db.select().from(notifications)
      .where(and(
        eq(notifications.id, notificationId),
        eq(notifications.userId, userId)
      ));

    if (notification.length === 0 && !isAdmin(req)) {
      return res.status(404).json({ message: 'ไม่พบการแจ้งเตือน' });
    }

    // ลบการแจ้งเตือน
    await db.delete(notifications)
      .where(eq(notifications.id, notificationId));

    res.json({ message: 'ลบการแจ้งเตือนเรียบร้อย' });
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการลบการแจ้งเตือน:', error);
    res.status(500).json({ message: 'เกิดข้อผิดพลาดในการลบการแจ้งเตือน' });
  }
}