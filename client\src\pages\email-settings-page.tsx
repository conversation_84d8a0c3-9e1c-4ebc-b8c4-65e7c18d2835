import React, { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import { useToast } from '@/hooks/use-toast';
import { Mail, Send, Save, Settings, RefreshCw, Plus, Eye, Edit, Trash, Check, Loader2, Alert<PERSON>riangle, Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useForm } from 'react-hook-form';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { apiRequest } from '@/lib/queryClient';
import { Badge } from '@/components/ui/badge';
import { formatThaiDateTime } from '@/lib/utils';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAuth } from '@/hooks/use-auth';

// สำหรับหน้าการตั้งค่าอีเมล
export default function EmailSettingsPage() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('settings');
  const [testEmail, setTestEmail] = useState('');

  // การตรวจสอบว่าผู้ใช้เป็นแอดมิน
  const isAdmin = user?.role === 'admin';

  // ดึงข้อมูลการตั้งค่าอีเมล
  const {
    data: emailSettings,
    isLoading: isLoadingSettings,
    isError: isErrorSettings,
    error: errorSettings,
    refetch: refetchSettings,
  } = useQuery({
    queryKey: ['/api/email/settings'],
    queryFn: async () => {
      const response = await fetch('/api/email/settings');
      if (!response.ok) {
        throw new Error('ไม่สามารถดึงข้อมูลการตั้งค่าอีเมลได้');
      }
      return response.json();
    },
    enabled: isAdmin, // เฉพาะแอดมินเท่านั้นที่สามารถดูการตั้งค่าอีเมลได้
  });

  // ดึงข้อมูลเทมเพลตอีเมล
  const {
    data: emailTemplates,
    isLoading: isLoadingTemplates,
    isError: isErrorTemplates,
    error: errorTemplates,
    refetch: refetchTemplates,
  } = useQuery({
    queryKey: ['/api/email/templates'],
    queryFn: async () => {
      const response = await fetch('/api/email/templates');
      if (!response.ok) {
        throw new Error('ไม่สามารถดึงข้อมูลเทมเพลตอีเมลได้');
      }
      return response.json();
    },
    enabled: isAdmin,
  });

  // ดึงข้อมูลประวัติการส่งอีเมล
  const {
    data: emailLogs,
    isLoading: isLoadingLogs,
    isError: isErrorLogs,
    error: errorLogs,
    refetch: refetchLogs,
  } = useQuery({
    queryKey: ['/api/email/logs'],
    queryFn: async () => {
      const response = await fetch('/api/email/logs');
      if (!response.ok) {
        throw new Error('ไม่สามารถดึงข้อมูลประวัติการส่งอีเมลได้');
      }
      return response.json();
    },
    enabled: isAdmin,
  });

  // Mutation สำหรับการอัปเดตการตั้งค่าอีเมล
  const updateEmailSettingsMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await apiRequest('PUT', '/api/email/settings', data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/email/settings'] });
      toast({
        title: 'สำเร็จ',
        description: 'อัปเดตการตั้งค่าอีเมลเรียบร้อย',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'เกิดข้อผิดพลาด',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Mutation สำหรับการส่งอีเมลทดสอบ
  const sendTestEmailMutation = useMutation({
    mutationFn: async (data: { to: string }) => {
      const response = await apiRequest('POST', '/api/email/settings/test', data);
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: 'สำเร็จ',
        description: 'ส่งอีเมลทดสอบเรียบร้อย',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'เกิดข้อผิดพลาด',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // ฟอร์มสำหรับการตั้งค่าอีเมล
  const { register, handleSubmit, setValue, formState: { errors } } = useForm({
    defaultValues: {
      service: emailSettings?.service || 'gmail',
      host: emailSettings?.host || 'smtp.gmail.com',
      port: emailSettings?.port || 587,
      secure: emailSettings?.secure || false,
      auth: {
        user: emailSettings?.auth?.user || '',
        pass: '',
      },
      from: emailSettings?.from || '<EMAIL>',
      defaultName: emailSettings?.defaultName || 'SLIPKUY - ระบบตรวจสอบสลิป',
    },
  });

  // เมื่อเปลี่ยนแท็บ
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    
    // รีเฟรชข้อมูลสำหรับแท็บที่เลือก
    if (value === 'settings') {
      refetchSettings();
    } else if (value === 'templates') {
      refetchTemplates();
    } else if (value === 'logs') {
      refetchLogs();
    }
  };

  // ส่งฟอร์มการตั้งค่าอีเมล
  const onSubmitEmailSettings = (data: any) => {
    // ถ้าไม่ได้ใส่รหัสผ่าน ให้ใช้รหัสผ่านเดิม (ไม่เปลี่ยนแปลง)
    if (!data.auth.pass) {
      delete data.auth.pass;
    }
    
    updateEmailSettingsMutation.mutate(data);
  };

  // ส่งอีเมลทดสอบ
  const handleSendTestEmail = () => {
    if (!testEmail) {
      toast({
        title: 'กรุณาระบุอีเมล',
        description: 'กรุณาระบุอีเมลปลายทางสำหรับการทดสอบ',
        variant: 'destructive',
      });
      return;
    }
    
    sendTestEmailMutation.mutate({ to: testEmail });
  };

  // การแสดงผลในกรณีที่ผู้ใช้ไม่ใช่แอดมิน
  if (!isAdmin) {
    return (
      <div className="container mx-auto py-8 px-4">
        <Card className="max-w-lg mx-auto">
          <CardHeader>
            <CardTitle className="text-center">ไม่มีสิทธิ์เข้าถึง</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-center text-gray-500">คุณไม่มีสิทธิ์ในการเข้าถึงหน้านี้</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="container mx-auto py-6 px-4 sm:px-6"
    >
      {/* พื้นหลังสไตล์เทพเจ้า */}
      <div className="fixed inset-0 -z-10 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-b from-gray-900 via-blue-950/50 to-gray-900"></div>
        <div className="absolute inset-0 opacity-20 bg-[radial-gradient(#0a3b5e_1px,transparent_1px)] [background-size:32px_32px]"></div>
        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-500/50 to-transparent"></div>
      </div>
      
      <div className="max-w-5xl mx-auto space-y-6">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-cyan-300 to-teal-400">
            ตั้งค่าการส่งอีเมล
          </h1>
          <p className="text-gray-400 mt-1">
            จัดการการตั้งค่าอีเมลและเทมเพลตสำหรับการส่งการแจ้งเตือน
          </p>
        </div>
        
        <Card className="bg-gray-900/60 backdrop-blur-sm border-gray-800 shadow-xl overflow-hidden">
          <Tabs defaultValue="settings" value={activeTab} onValueChange={handleTabChange}>
            <div className="p-4 border-b border-gray-800 bg-gradient-to-r from-blue-900/30 to-gray-900/30">
              <TabsList className="bg-gray-800/50 border border-gray-800">
                <TabsTrigger value="settings" className="data-[state=active]:bg-blue-900/40">
                  <Settings className="h-4 w-4 mr-1" />
                  ตั้งค่า SMTP
                </TabsTrigger>
                <TabsTrigger value="templates" className="data-[state=active]:bg-blue-900/40">
                  <Mail className="h-4 w-4 mr-1" />
                  เทมเพลตอีเมล
                </TabsTrigger>
                <TabsTrigger value="logs" className="data-[state=active]:bg-blue-900/40">
                  <Eye className="h-4 w-4 mr-1" />
                  ประวัติการส่ง
                </TabsTrigger>
                <TabsTrigger value="test" className="data-[state=active]:bg-blue-900/40">
                  <Send className="h-4 w-4 mr-1" />
                  ทดสอบส่งอีเมล
                </TabsTrigger>
              </TabsList>
            </div>
            
            {/* แท็บการตั้งค่า SMTP */}
            <TabsContent value="settings" className="p-0">
              <CardContent className="p-4 sm:p-6">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-blue-300 mb-2">
                    <Settings className="h-5 w-5 inline mr-1" />
                    ตั้งค่า SMTP สำหรับการส่งอีเมล
                  </h3>
                  <p className="text-gray-400 text-sm">
                    ตั้งค่าการเชื่อมต่อ SMTP เพื่อให้ระบบสามารถส่งอีเมลได้
                  </p>
                </div>
                
                {isLoadingSettings ? (
                  <div className="flex flex-col items-center justify-center p-8">
                    <div className="w-12 h-12 rounded-full border-4 border-t-blue-500 border-blue-800/30 animate-spin"></div>
                    <p className="mt-4 text-gray-400">กำลังโหลดการตั้งค่า...</p>
                  </div>
                ) : isErrorSettings ? (
                  <div className="flex flex-col items-center justify-center p-8 text-red-400">
                    <p>เกิดข้อผิดพลาดในการโหลดการตั้งค่า</p>
                    <p className="text-sm text-gray-500">{errorSettings instanceof Error ? errorSettings.message : 'เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ'}</p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => refetchSettings()}
                      className="mt-4 text-blue-300 hover:text-blue-200 hover:bg-blue-950 border-blue-800"
                    >
                      <RefreshCw className="h-4 w-4 mr-1" />
                      ลองอีกครั้ง
                    </Button>
                  </div>
                ) : (
                  <form onSubmit={handleSubmit(onSubmitEmailSettings)} className="space-y-6">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="service">บริการ (Service)</Label>
                        <Select 
                          defaultValue={emailSettings?.service || 'gmail'}
                          onValueChange={(value) => setValue('service', value)}
                        >
                          <SelectTrigger className="bg-gray-800 border-gray-700">
                            <SelectValue placeholder="เลือกบริการ" />
                          </SelectTrigger>
                          <SelectContent className="bg-gray-800 border-gray-700">
                            <SelectItem value="gmail">Gmail</SelectItem>
                            <SelectItem value="outlook">Outlook</SelectItem>
                            <SelectItem value="yahoo">Yahoo Mail</SelectItem>
                            <SelectItem value="sendgrid">SendGrid</SelectItem>
                            <SelectItem value="custom">อื่นๆ (กำหนดเอง)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="host">โฮสต์ (Host)</Label>
                        <Input
                          id="host"
                          placeholder="smtp.example.com"
                          className="bg-gray-800 border-gray-700"
                          {...register('host', { required: 'กรุณาระบุโฮสต์' })}
                        />
                        {errors.host && (
                          <p className="text-sm text-red-500">{String(errors.host.message)}</p>
                        )}
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="port">พอร์ต (Port)</Label>
                        <Input
                          id="port"
                          type="number"
                          placeholder="587"
                          className="bg-gray-800 border-gray-700"
                          {...register('port', { required: 'กรุณาระบุพอร์ต' })}
                        />
                        {errors.port && (
                          <p className="text-sm text-red-500">{String(errors.port.message)}</p>
                        )}
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="secure">ความปลอดภัย (Secure)</Label>
                        <Select 
                          defaultValue={emailSettings?.secure ? 'true' : 'false'}
                          onValueChange={(value) => setValue('secure', value === 'true')}
                        >
                          <SelectTrigger className="bg-gray-800 border-gray-700">
                            <SelectValue placeholder="เลือกความปลอดภัย" />
                          </SelectTrigger>
                          <SelectContent className="bg-gray-800 border-gray-700">
                            <SelectItem value="false">ไม่ใช้ SSL/TLS (False)</SelectItem>
                            <SelectItem value="true">ใช้ SSL/TLS (True)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="auth.user">อีเมลผู้ส่ง (Username)</Label>
                        <Input
                          id="auth.user"
                          placeholder="<EMAIL>"
                          className="bg-gray-800 border-gray-700"
                          {...register('auth.user', { required: 'กรุณาระบุอีเมลผู้ส่ง' })}
                        />
                        {errors.auth?.user && (
                          <p className="text-sm text-red-500">{String(errors.auth.user.message)}</p>
                        )}
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="auth.pass">รหัสผ่าน (Password)</Label>
                        <Input
                          id="auth.pass"
                          type="password"
                          placeholder="รหัสผ่านหรือ App Password"
                          className="bg-gray-800 border-gray-700"
                          {...register('auth.pass')}
                        />
                        <p className="text-xs text-gray-500">
                          ใส่เฉพาะเมื่อต้องการเปลี่ยนรหัสผ่าน
                        </p>
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="from">อีเมลสำหรับแสดงผล (From)</Label>
                        <Input
                          id="from"
                          placeholder="<EMAIL>"
                          className="bg-gray-800 border-gray-700"
                          {...register('from', { required: 'กรุณาระบุอีเมลสำหรับแสดงผล' })}
                        />
                        {errors.from && (
                          <p className="text-sm text-red-500">{String(errors.from.message)}</p>
                        )}
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="defaultName">ชื่อผู้ส่ง (Sender Name)</Label>
                        <Input
                          id="defaultName"
                          placeholder="SLIPKUY - ระบบตรวจสอบสลิป"
                          className="bg-gray-800 border-gray-700"
                          {...register('defaultName', { required: 'กรุณาระบุชื่อผู้ส่ง' })}
                        />
                        {errors.defaultName && (
                          <p className="text-sm text-red-500">{String(errors.defaultName.message)}</p>
                        )}
                      </div>
                    </div>
                    
                    <Button
                      type="submit"
                      className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700"
                      disabled={updateEmailSettingsMutation.isPending}
                    >
                      {updateEmailSettingsMutation.isPending ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          กำลังบันทึก...
                        </>
                      ) : (
                        <>
                          <Save className="h-4 w-4 mr-2" />
                          บันทึกการตั้งค่า
                        </>
                      )}
                    </Button>
                  </form>
                )}
              </CardContent>
            </TabsContent>
            
            {/* แท็บเทมเพลตอีเมล */}
            <TabsContent value="templates" className="p-0">
              <CardContent className="p-4 sm:p-6">
                <div className="mb-6">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-lg font-semibold text-blue-300">
                      <Mail className="h-5 w-5 inline mr-1" />
                      เทมเพลตอีเมล
                    </h3>
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-blue-300 hover:text-blue-200 hover:bg-blue-950 border-blue-800"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      สร้างเทมเพลตใหม่
                    </Button>
                  </div>
                  <p className="text-gray-400 text-sm">
                    จัดการเทมเพลตอีเมลสำหรับการส่งการแจ้งเตือนและรายงาน
                  </p>
                </div>
                
                {isLoadingTemplates ? (
                  <div className="flex flex-col items-center justify-center p-8">
                    <div className="w-12 h-12 rounded-full border-4 border-t-blue-500 border-blue-800/30 animate-spin"></div>
                    <p className="mt-4 text-gray-400">กำลังโหลดเทมเพลต...</p>
                  </div>
                ) : isErrorTemplates ? (
                  <div className="flex flex-col items-center justify-center p-8 text-red-400">
                    <p>เกิดข้อผิดพลาดในการโหลดเทมเพลต</p>
                    <p className="text-sm text-gray-500">{errorTemplates instanceof Error ? errorTemplates.message : 'เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ'}</p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => refetchTemplates()}
                      className="mt-4 text-blue-300 hover:text-blue-200 hover:bg-blue-950 border-blue-800"
                    >
                      <RefreshCw className="h-4 w-4 mr-1" />
                      ลองอีกครั้ง
                    </Button>
                  </div>
                ) : !emailTemplates || emailTemplates.length === 0 ? (
                  <div className="flex flex-col items-center justify-center p-8 text-gray-500 bg-gray-800/50 rounded-lg">
                    <Mail className="h-12 w-12 mb-2 text-gray-400 opacity-50" />
                    <p>ไม่พบเทมเพลตอีเมล</p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-4 text-blue-300 hover:text-blue-200 hover:bg-blue-950 border-blue-800"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      สร้างเทมเพลตแรก
                    </Button>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader className="bg-gray-800/50">
                        <TableRow>
                          <TableHead>ชื่อเทมเพลต</TableHead>
                          <TableHead>หัวข้อ</TableHead>
                          <TableHead>ค่าเริ่มต้น</TableHead>
                          <TableHead>อัปเดตล่าสุด</TableHead>
                          <TableHead>การจัดการ</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {emailTemplates.map((template: any) => (
                          <TableRow key={template.id} className="hover:bg-gray-800/30">
                            <TableCell className="font-medium text-blue-300">
                              {template.name}
                            </TableCell>
                            <TableCell>{template.subject}</TableCell>
                            <TableCell>
                              {template.isDefault ? (
                                <Badge className="bg-blue-600/40 text-blue-300">ค่าเริ่มต้น</Badge>
                              ) : (
                                <Badge className="bg-gray-600 text-gray-300">ไม่ใช่</Badge>
                              )}
                            </TableCell>
                            <TableCell className="text-gray-400 text-sm">
                              {formatThaiDateTime(new Date(template.updatedAt))}
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center space-x-2">
                                <Button 
                                  variant="ghost" 
                                  size="icon" 
                                  className="h-8 w-8 text-blue-400 hover:text-blue-300"
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button 
                                  variant="ghost" 
                                  size="icon" 
                                  className="h-8 w-8 text-red-400 hover:text-red-300"
                                >
                                  <Trash className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </TabsContent>
            
            {/* แท็บประวัติการส่ง */}
            <TabsContent value="logs" className="p-0">
              <CardContent className="p-4 sm:p-6">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-blue-300 mb-2">
                    <Eye className="h-5 w-5 inline mr-1" />
                    ประวัติการส่งอีเมล
                  </h3>
                  <p className="text-gray-400 text-sm">
                    ตรวจสอบประวัติการส่งอีเมลทั้งหมดในระบบ
                  </p>
                </div>
                
                {isLoadingLogs ? (
                  <div className="flex flex-col items-center justify-center p-8">
                    <div className="w-12 h-12 rounded-full border-4 border-t-blue-500 border-blue-800/30 animate-spin"></div>
                    <p className="mt-4 text-gray-400">กำลังโหลดประวัติการส่ง...</p>
                  </div>
                ) : isErrorLogs ? (
                  <div className="flex flex-col items-center justify-center p-8 text-red-400">
                    <p>เกิดข้อผิดพลาดในการโหลดประวัติการส่ง</p>
                    <p className="text-sm text-gray-500">{errorLogs instanceof Error ? errorLogs.message : 'เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ'}</p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => refetchLogs()}
                      className="mt-4 text-blue-300 hover:text-blue-200 hover:bg-blue-950 border-blue-800"
                    >
                      <RefreshCw className="h-4 w-4 mr-1" />
                      ลองอีกครั้ง
                    </Button>
                  </div>
                ) : !emailLogs?.logs || emailLogs.logs.length === 0 ? (
                  <div className="flex flex-col items-center justify-center p-8 text-gray-500 bg-gray-800/50 rounded-lg">
                    <Mail className="h-12 w-12 mb-2 text-gray-400 opacity-50" />
                    <p>ไม่พบประวัติการส่งอีเมล</p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader className="bg-gray-800/50">
                        <TableRow>
                          <TableHead>ผู้รับ</TableHead>
                          <TableHead>หัวข้อ</TableHead>
                          <TableHead>สถานะ</TableHead>
                          <TableHead>เวลาที่ส่ง</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {emailLogs.logs.map((log: any) => (
                          <TableRow key={log.id} className="hover:bg-gray-800/30">
                            <TableCell className="font-medium">{log.to}</TableCell>
                            <TableCell>{log.subject}</TableCell>
                            <TableCell>
                              {log.status === 'sent' ? (
                                <Badge className="bg-green-600/40 text-green-300">
                                  <Check className="h-3 w-3 mr-1" />
                                  ส่งแล้ว
                                </Badge>
                              ) : log.status === 'pending' ? (
                                <Badge className="bg-yellow-600/40 text-yellow-300">
                                  <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                                  กำลังส่ง
                                </Badge>
                              ) : (
                                <Badge className="bg-red-600/40 text-red-300">
                                  <AlertTriangle className="h-3 w-3 mr-1" />
                                  ล้มเหลว
                                </Badge>
                              )}
                            </TableCell>
                            <TableCell className="text-gray-400 text-sm">
                              {log.sentAt ? formatThaiDateTime(new Date(log.sentAt)) : 'ยังไม่ส่ง'}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                    
                    {/* การแบ่งหน้า */}
                    {emailLogs.pagination && emailLogs.pagination.totalPages > 1 && (
                      <div className="flex justify-center mt-4">
                        <div className="flex space-x-2">
                          {Array.from({ length: emailLogs.pagination.totalPages }, (_, i) => (
                            <Button
                              key={i}
                              variant={emailLogs.pagination.page === i + 1 ? "default" : "outline"}
                              size="sm"
                              className={emailLogs.pagination.page === i + 1 
                                ? "bg-blue-600 hover:bg-blue-700" 
                                : "text-blue-300 hover:text-blue-200 hover:bg-blue-950 border-blue-800"}
                            >
                              {i + 1}
                            </Button>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </TabsContent>
            
            {/* แท็บทดสอบส่งอีเมล */}
            <TabsContent value="test" className="p-0">
              <CardContent className="p-4 sm:p-6">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-blue-300 mb-2">
                    <Send className="h-5 w-5 inline mr-1" />
                    ทดสอบส่งอีเมล
                  </h3>
                  <p className="text-gray-400 text-sm">
                    ส่งอีเมลทดสอบเพื่อตรวจสอบว่าการตั้งค่า SMTP ทำงานได้อย่างถูกต้อง
                  </p>
                </div>
                
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="testEmail">อีเมลสำหรับทดสอบ</Label>
                    <Input
                      id="testEmail"
                      placeholder="<EMAIL>"
                      value={testEmail}
                      onChange={(e) => setTestEmail(e.target.value)}
                      className="bg-gray-800 border-gray-700"
                    />
                    <p className="text-xs text-gray-500">
                      ระบุอีเมลที่คุณต้องการใช้ในการทดสอบการส่ง
                    </p>
                  </div>
                  
                  <div className="flex justify-end">
                    <Button
                      variant="default"
                      className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700"
                      onClick={handleSendTestEmail}
                      disabled={!testEmail || sendTestEmailMutation.isPending}
                    >
                      {sendTestEmailMutation.isPending ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          กำลังส่ง...
                        </>
                      ) : (
                        <>
                          <Send className="h-4 w-4 mr-2" />
                          ส่งอีเมลทดสอบ
                        </>
                      )}
                    </Button>
                  </div>
                  
                  <div className="bg-blue-900/20 border border-blue-800/40 rounded-md p-4 mt-4">
                    <h4 className="font-medium text-blue-300 mb-2">
                      <Info className="h-4 w-4 inline mr-1" />
                      คำแนะนำสำหรับการตั้งค่า SMTP
                    </h4>
                    <ul className="space-y-2 text-sm text-gray-400 list-disc pl-5">
                      <li>
                        <span className="text-blue-300">Gmail:</span> หากใช้บัญชี Gmail คุณอาจต้องเปิดการเข้าถึงแอปที่มีความปลอดภัยน้อยกว่าหรือสร้าง App Password
                      </li>
                      <li>
                        <span className="text-blue-300">SendGrid:</span> แนะนำให้ใช้ API Key ของ SendGrid สำหรับการส่งอีเมลจำนวนมาก
                      </li>
                      <li>
                        <span className="text-blue-300">การทดสอบ:</span> อย่าลืมคลิกที่ "บันทึกการตั้งค่า" ก่อนทำการทดสอบส่งอีเมล
                      </li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </TabsContent>
          </Tabs>
        </Card>
      </div>
    </motion.div>
  );
}