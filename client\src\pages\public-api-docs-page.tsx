import { useState } from "react";
import { Helmet } from "react-helmet-async";
import {
  FileText,
  Code,
  Wand2,
  Key,
  Webhook,
  BookText,
  CheckCircle2,
  Bell,
  Shield,
  BrainCircuit,
  Zap,
  Server,
  History,
  Database,
  Filter,
  type LucideIcon,
} from "lucide-react";
import {
  Card,
  CardContent,
} from "@/components/ui/card";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { CopyBlock, dracula } from "react-code-blocks";
import { Link } from "wouter";
import { Button } from "@/components/ui/button";

interface ApiSection {
  id: string;
  title: string;
  icon: LucideIcon;
  content: React.ReactNode;
}

// Method badge component
const MethodBadge = ({ method }: { method: string }) => {
  const colors = {
    GET: "bg-blue-600 hover:bg-blue-700",
    POST: "bg-green-600 hover:bg-green-700",
    PUT: "bg-amber-600 hover:bg-amber-700",
    DELETE: "bg-red-600 hover:bg-red-700",
  } as { [key: string]: string };

  return (
    <div className={`px-3 py-1 text-sm font-mono font-semibold rounded ${colors[method] || "bg-gray-600"} text-white`}>
      {method}
    </div>
  );
};

// API Endpoint component
const ApiEndpoint = ({ endpoint, method, description, request, response }: {
  endpoint: string;
  method: string;
  description: string;
  request?: string;
  response?: string;
}) => {
  const [tab, setTab] = useState("description");

  return (
    <Card className="mb-6 bg-indigo-900/30 border-indigo-700/30">
      <CardContent className="p-0">
        <div className="p-4 border-b border-indigo-800/40 flex items-center justify-between">
          <div className="flex items-center">
            <MethodBadge method={method} />
            <code className="ml-3 font-semibold text-amber-300">{endpoint}</code>
          </div>
        </div>

        <Tabs value={tab} onValueChange={setTab} className="w-full">
          <TabsList className="grid grid-cols-3 m-3 bg-indigo-950/50">
            <TabsTrigger value="description">รายละเอียด</TabsTrigger>
            {request && <TabsTrigger value="request">คำขอ</TabsTrigger>}
            {response && <TabsTrigger value="response">การตอบกลับ</TabsTrigger>}
          </TabsList>

          <TabsContent value="description" className="p-4 text-indigo-200">
            {description}
          </TabsContent>

          {request && (
            <TabsContent value="request" className="p-4">
              <div className="font-mono text-sm">
                <CopyBlock
                  text={request}
                  language="json"
                  showLineNumbers={false}
                  theme={dracula}
                />
              </div>
            </TabsContent>
          )}

          {response && (
            <TabsContent value="response" className="p-4">
              <div className="font-mono text-sm">
                <CopyBlock
                  text={response}
                  language="json"
                  showLineNumbers={false}
                  theme={dracula}
                />
              </div>
            </TabsContent>
          )}
        </Tabs>
      </CardContent>
    </Card>
  );
};

// ส่วนแสดงหน้าคู่มือการใช้งาน API สำหรับผู้ใช้ที่ไม่ได้ล็อกอิน
export default function PublicApiDocsPage() {
  const [tab, setTab] = useState("introduction");

  // ตัวอย่างการตอบกลับสำหรับการตรวจสอบสลิป
  const verifySlipExample = `{
  "code": "200000",
  "message": "Slip found",
  "data": {
    "referenceId": "52423f93-1a5d-4210-a465-5e4cfd0a7ed1-1206",
    "decode": "0041000600000101030040220015109160510CTF015675102TH9104470D",
    "transRef": "015109160510CTF01567",
    "dateTime": "2025-04-19T16:05:10+07:00",
    "amount": 155,
    "ref1": null,
    "ref2": null,
    "ref3": null,
    "receiver": {
      "account": {
        "name": {
          "th": "นาย จิรายุ สัตย์ซื่อ",
          "en": null
        },
        "bank": {
          "type": "BANKAC",
          "account": "xxx-x-x4800-x"
        },
        "proxy": null
      },
      "bank": {
        "id": "006",
        "name": "ธนาคารกรุงไทย",
        "short": "KTB"
      },
      "merchantId": null
    },
    "sender": {
      "account": {
        "name": {
          "th": "น.ส. มาลี ดีงาม",
          "en": null
        },
        "bank": {
          "type": "BANKAC",
          "account": "xxx-x-x5902-x"
        },
        "proxy": null
      },
      "bank": {
        "id": "002",
        "name": "ธนาคารกรุงเทพ",
        "short": "BBL"
      }
    },
    "source": "api",
    "provider": "slip2go",
    "createdAt": "2025-04-17T05:10:22Z"
  }
}`;

  const usageExample = `{
  "code": "200000",
  "message": "Success",
  "data": {
    "apiKey": "a46b900ca74f835acf678b77e846e99c",
    "usageCount": 123,
    "usageLimit": 1000,
    "remainingUsage": 877,
    "usageByDay": [
      { "date": "2025-04-19", "count": 15 },
      { "date": "2025-04-18", "count": 22 },
      { "date": "2025-04-17", "count": 8 }
    ],
    "usageByStatus": {
      "success": 102,
      "failed": 21
    }
  }
}`;

  const docsConfig = {
    sections: [
      {
        id: "introduction",
        title: "บทนำ",
        icon: BrainCircuit as LucideIcon,
        content: (
          <div className="space-y-6">
            <div className="p-6 rounded-xl bg-gradient-to-br from-indigo-900/80 to-purple-900/90 border border-indigo-700/40 shadow-xl">
              <div className="flex items-center mb-4">
                <div className="p-3 rounded-full bg-amber-500/20 mr-4">
                  <Wand2 className="h-8 w-8 text-amber-400" />
                </div>
                <h3 className="text-2xl font-bold text-white">SLIPKUY API Documentation</h3>
              </div>
              <p className="text-lg text-indigo-200 leading-relaxed mb-4">
                ยินดีต้อนรับสู่คำแนะนำการใช้งาน API ของ SLIPKUY ซึ่งเป็นบริการตรวจสอบสลิปโอนเงินที่รวดเร็วและแม่นยำ
              </p>
              <div className="space-y-3">
                <div className="flex items-start">
                  <div className="bg-amber-500/20 p-2 rounded-full mr-3 mt-1">
                    <Zap className="h-4 w-4 text-amber-400" />
                  </div>
                  <div>
                    <h4 className="font-medium text-white">ตรวจสอบสลิปได้รวดเร็ว</h4>
                    <p className="text-indigo-300 text-sm">ส่งภาพสลิปเพื่อตรวจสอบความถูกต้องในเวลาเพียงไม่กี่วินาที</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="bg-amber-500/20 p-2 rounded-full mr-3 mt-1">
                    <Database className="h-4 w-4 text-amber-400" />
                  </div>
                  <div>
                    <h4 className="font-medium text-white">จัดเก็บประวัติการตรวจสอบ</h4>
                    <p className="text-indigo-300 text-sm">ข้อมูลธุรกรรมทั้งหมดถูกเก็บไว้อย่างปลอดภัยเพื่อการอ้างอิงในอนาคต</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="bg-amber-500/20 p-2 rounded-full mr-3 mt-1">
                    <Webhook className="h-4 w-4 text-amber-400" />
                  </div>
                  <div>
                    <h4 className="font-medium text-white">รองรับ Webhook</h4>
                    <p className="text-indigo-300 text-sm">ส่งข้อมูลการตรวจสอบไปยังระบบของคุณโดยอัตโนมัติผ่าน webhook</p>
                  </div>
                </div>
              </div>

              <div className="mt-6 text-center">
                <Link href="/auth">
                  <Button className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white">
                    เริ่มต้นใช้งานฟรี
                  </Button>
                </Link>
              </div>
            </div>

            <div className="p-6 rounded-lg bg-indigo-900/40 border border-indigo-800/40">
              <h3 className="text-xl font-semibold flex items-center text-white mb-4">
                <Server className="mr-2 h-5 w-5 text-amber-400" />
                การทำงานหลักของ API
              </h3>
              <p className="text-indigo-200 mb-6">
                SLIPKUY API ให้บริการตรวจสอบสลิปโอนเงินแบบครบวงจร ตั้งแต่การตรวจสอบความถูกต้อง การจัดเก็บประวัติ และการส่งการแจ้งเตือนผ่าน webhook
              </p>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="bg-indigo-900/30 p-4 rounded-lg border border-indigo-800/40">
                  <div className="flex items-center mb-3">
                    <div className="p-2 bg-amber-500/20 rounded-full mr-2">
                      <FileText className="h-5 w-5 text-amber-400" />
                    </div>
                    <h4 className="font-medium text-white">การตรวจสอบสลิป</h4>
                  </div>
                  <p className="text-indigo-300 text-sm">
                    ส่งภาพสลิปเพื่อตรวจสอบความถูกต้อง ได้ผลลัพธ์ที่ละเอียดและแม่นยำ
                  </p>
                </div>
                <div className="bg-indigo-900/30 p-4 rounded-lg border border-indigo-800/40">
                  <div className="flex items-center mb-3">
                    <div className="p-2 bg-amber-500/20 rounded-full mr-2">
                      <History className="h-5 w-5 text-amber-400" />
                    </div>
                    <h4 className="font-medium text-white">ประวัติการตรวจสอบ</h4>
                  </div>
                  <p className="text-indigo-300 text-sm">
                    ดูประวัติการตรวจสอบทั้งหมด ค้นหาและกรองตามวันที่หรือจำนวนเงิน
                  </p>
                </div>
                <div className="bg-indigo-900/30 p-4 rounded-lg border border-indigo-800/40">
                  <div className="flex items-center mb-3">
                    <div className="p-2 bg-amber-500/20 rounded-full mr-2">
                      <Key className="h-5 w-5 text-amber-400" />
                    </div>
                    <h4 className="font-medium text-white">การจัดการ API Key</h4>
                  </div>
                  <p className="text-indigo-300 text-sm">
                    สร้างและจัดการ API Key เพื่อเชื่อมต่อกับระบบของคุณได้อย่างปลอดภัย
                  </p>
                </div>
              </div>
            </div>
          </div>
        ),
      },
      {
        id: "authentication",
        title: "การยืนยันตัวตน",
        icon: Shield as LucideIcon,
        content: (
          <div className="space-y-6">
            <div className="p-6 rounded-lg bg-indigo-900/40 border border-indigo-800/40">
              <h3 className="text-xl font-semibold flex items-center text-white mb-4">
                <Key className="mr-2 h-5 w-5 text-amber-400" />
                การยืนยันตัวตนด้วย API Key
              </h3>
              <p className="text-indigo-200 mb-4">
                ทุก API Request ต้องมีการยืนยันตัวตนด้วย API Key ที่ถูกสร้างขึ้นจากระบบ SLIPKUY โดยส่งผ่าน HTTP Header
              </p>

              <div className="bg-indigo-950/50 p-4 rounded-md border border-indigo-900/50 font-mono text-sm text-indigo-200 mb-4">
                <div className="mb-2">
                  <span className="text-amber-400">X-API-KEY:</span> <span>your_api_key_here</span>
                </div>
              </div>

              <p className="text-indigo-200 mb-6">
                คุณสามารถสร้าง API Key ได้หลังจากลงทะเบียนและเข้าสู่ระบบที่ SLIPKUY แล้ว
              </p>

              <div className="bg-indigo-900/30 p-4 rounded-lg border border-indigo-800/40">
                <h4 className="font-medium text-white mb-2 flex items-center">
                  <Bell className="mr-2 h-4 w-4 text-amber-400" />
                  ข้อควรระวัง
                </h4>
                <ul className="list-disc list-inside text-indigo-300 text-sm space-y-2">
                  <li>อย่าแชร์ API Key ของคุณกับบุคคลอื่น</li>
                  <li>เก็บ API Key ไว้ในที่ปลอดภัย เช่น environment variable</li>
                  <li>หากคุณสงสัยว่า API Key ถูกเปิดเผย ให้รีบสร้าง Key ใหม่และลบ Key เดิมทันที</li>
                </ul>
              </div>
            </div>
          </div>
        ),
      },
      {
        id: "endpoints",
        title: "Endpoints",
        icon: Server as LucideIcon,
        content: (
          <div className="space-y-6">
            <div className="p-6 rounded-lg bg-indigo-900/40 border border-indigo-800/40">
              <h3 className="text-xl font-semibold flex items-center text-white mb-4">
                <FileText className="mr-2 h-5 w-5 text-amber-400" />
                API Endpoints
              </h3>
              <p className="text-indigo-200 mb-6">
                รายการ API Endpoints ทั้งหมดที่คุณสามารถใช้งานได้
              </p>

              <ApiEndpoint
                endpoint="/api/v1/verify-slip"
                method="POST"
                description="ส่งภาพสลิปเพื่อตรวจสอบความถูกต้อง ภาพสลิปต้องอยู่ในรูปแบบ base64 encoded string"
                request={`{
  "image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "options": {
    "validateAmount": true,
    "validateDate": true,
    "validateAccount": true,
    "expectedAmount": 1000,
    "expectedDate": "2025-04-19",
    "expectedAccount": "xxx-x-x4800-x"
  }
}`}
                response={verifySlipExample}
              />

              <ApiEndpoint
                endpoint="/api/verify-history"
                method="GET"
                description="ดึงประวัติการตรวจสอบสลิปทั้งหมด สามารถกำหนดพารามิเตอร์เพื่อกรองและจัดเรียงผลลัพธ์ได้"
                request=""
                response={`{
  "code": "200000",
  "message": "Success",
  "data": {
    "items": [
      {
        "id": "52423f93-1a5d-4210-a465-5e4cfd0a7ed1",
        "transRef": "015109160510CTF01567",
        "dateTime": "2025-04-19T16:05:10+07:00",
        "amount": 155,
        "sender": "น.ส. มาลี ดีงาม (BBL)",
        "receiver": "นาย จิรายุ สัตย์ซื่อ (KTB)",
        "status": "success",
        "source": "api",
        "createdAt": "2025-04-19T10:15:22Z"
      },
      /* รายการอื่นๆ */
    ],
    "pagination": {
      "total": 42,
      "page": 1,
      "limit": 10,
      "pages": 5
    }
  }
}`}
              />

              <ApiEndpoint
                endpoint="/api/usage/stats"
                method="GET"
                description="ดูสถิติการใช้งาน API ของคุณ รวมถึงจำนวนการใช้งานต่อวันและสถานะความสำเร็จ"
                request=""
                response={usageExample}
              />
            </div>

            <div className="p-6 rounded-lg bg-indigo-900/40 border border-indigo-800/40">
              <h3 className="text-xl font-semibold flex items-center text-white mb-4">
                <Code className="mr-2 h-5 w-5 text-amber-400" />
                รหัสสถานะการตอบกลับ
              </h3>
              <p className="text-indigo-200 mb-6">
                API ของเราใช้รหัสสถานะการตอบกลับในรูปแบบตัวเลข 6 หลัก โดยมีความหมายดังนี้
              </p>

              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead className="bg-indigo-900/50">
                    <tr>
                      <th className="py-3 px-4 text-left text-white">รหัส</th>
                      <th className="py-3 px-4 text-left text-white">ความหมาย</th>
                      <th className="py-3 px-4 text-left text-white">รายละเอียด</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-b border-indigo-800/40">
                      <td className="py-2 px-4 font-mono text-green-400">200000</td>
                      <td className="py-2 px-4 text-white">สำเร็จ</td>
                      <td className="py-2 px-4 text-indigo-300">การร้องขอสำเร็จ</td>
                    </tr>
                    <tr className="border-b border-indigo-800/40">
                      <td className="py-2 px-4 font-mono text-green-400">200001</td>
                      <td className="py-2 px-4 text-white">พบสลิป</td>
                      <td className="py-2 px-4 text-indigo-300">ตรวจพบสลิปในภาพที่ส่งมา</td>
                    </tr>
                    <tr className="border-b border-indigo-800/40">
                      <td className="py-2 px-4 font-mono text-amber-400">200002</td>
                      <td className="py-2 px-4 text-white">จำนวนเงินไม่ตรงกับที่คาดหวัง</td>
                      <td className="py-2 px-4 text-indigo-300">พบสลิปแต่จำนวนเงินไม่ตรงกับที่ระบุไว้ใน options</td>
                    </tr>
                    <tr className="border-b border-indigo-800/40">
                      <td className="py-2 px-4 font-mono text-amber-400">200003</td>
                      <td className="py-2 px-4 text-white">วันที่ไม่ตรงกับที่คาดหวัง</td>
                      <td className="py-2 px-4 text-indigo-300">พบสลิปแต่วันที่ไม่ตรงกับที่ระบุไว้ใน options</td>
                    </tr>
                    <tr className="border-b border-indigo-800/40">
                      <td className="py-2 px-4 font-mono text-amber-400">400001</td>
                      <td className="py-2 px-4 text-white">ข้อมูลไม่ถูกต้อง</td>
                      <td className="py-2 px-4 text-indigo-300">รูปแบบคำขอไม่ถูกต้อง</td>
                    </tr>
                    <tr className="border-b border-indigo-800/40">
                      <td className="py-2 px-4 font-mono text-amber-400">400002</td>
                      <td className="py-2 px-4 text-white">ไม่พบสลิปในภาพ</td>
                      <td className="py-2 px-4 text-indigo-300">ไม่สามารถตรวจพบสลิปในภาพที่ส่งมา</td>
                    </tr>
                    <tr className="border-b border-indigo-800/40">
                      <td className="py-2 px-4 font-mono text-amber-400">401001</td>
                      <td className="py-2 px-4 text-white">ไม่พบ API Key</td>
                      <td className="py-2 px-4 text-indigo-300">ไม่พบ API Key ในส่วนหัวของคำขอ</td>
                    </tr>
                    <tr className="border-b border-indigo-800/40">
                      <td className="py-2 px-4 font-mono text-amber-400">403001</td>
                      <td className="py-2 px-4 text-white">API Key ไม่ได้ใช้งาน</td>
                      <td className="py-2 px-4 text-indigo-300">API Key ถูกระงับการใช้งาน</td>
                    </tr>
                    <tr className="border-b border-indigo-800/40">
                      <td className="py-2 px-4 font-mono text-amber-400">403002</td>
                      <td className="py-2 px-4 text-white">บัญชีผู้ใช้ไม่ได้ใช้งาน</td>
                      <td className="py-2 px-4 text-indigo-300">บัญชีผู้ใช้ถูกระงับการใช้งาน</td>
                    </tr>
                    <tr className="border-b border-indigo-800/40">
                      <td className="py-2 px-4 font-mono text-amber-400">403003</td>
                      <td className="py-2 px-4 text-white">เกินขีดจำกัดการใช้งาน API Key</td>
                      <td className="py-2 px-4 text-indigo-300">จำนวนการใช้งาน API Key เกินกว่าที่กำหนดไว้</td>
                    </tr>
                    <tr className="border-b border-indigo-800/40">
                      <td className="py-2 px-4 font-mono text-amber-400">429001</td>
                      <td className="py-2 px-4 text-white">เกินโควตาการใช้งาน</td>
                      <td className="py-2 px-4 text-indigo-300">เกินโควตาการใช้งานรายเดือน (จะใช้เครดิตแทน)</td>
                    </tr>
                    <tr className="border-b border-indigo-800/40">
                      <td className="py-2 px-4 font-mono text-amber-400">429002</td>
                      <td className="py-2 px-4 text-white">เครดิตไม่เพียงพอ</td>
                      <td className="py-2 px-4 text-indigo-300">เครดิตไม่เพียงพอสำหรับการใช้งานเพิ่มเติม</td>
                    </tr>
                    <tr className="border-b border-indigo-800/40">
                      <td className="py-2 px-4 font-mono text-red-400">500001</td>
                      <td className="py-2 px-4 text-white">ข้อผิดพลาดภายในเซิร์ฟเวอร์</td>
                      <td className="py-2 px-4 text-indigo-300">เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์</td>
                    </tr>
                    <tr>
                      <td className="py-2 px-4 font-mono text-red-400">504001</td>
                      <td className="py-2 px-4 text-white">เซิร์ฟเวอร์ตรวจสอบสลิปไม่ตอบสนอง</td>
                      <td className="py-2 px-4 text-indigo-300">เซิร์ฟเวอร์ตรวจสอบสลิปไม่ตอบสนอง โปรดลองอีกครั้งในภายหลัง</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div className="bg-indigo-900/40 p-6 rounded-lg border border-indigo-800/40">
              <h3 className="text-xl font-semibold flex items-center text-white mb-4">
                <BookText className="mr-2 h-5 w-5 text-amber-400" />
                แนวทางการใช้งานที่แนะนำ
              </h3>

              <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="best-practices" className="border-indigo-800/40">
                  <AccordionTrigger className="text-white">
                    <div className="flex items-center">
                      <CheckCircle2 className="h-5 w-5 mr-2 text-amber-400" />
                      แนวปฏิบัติที่ดีที่สุด
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="text-indigo-200">
                    <ul className="space-y-2">
                      <li>ใช้ระบบ retry สำหรับการเรียกใช้งาน API ที่ล้มเหลวเนื่องจากข้อผิดพลาดของเซิร์ฟเวอร์ (5xx)</li>
                      <li>ตรวจสอบ <code className="text-amber-300">code</code> ในการตอบกลับเสมอ ไม่ใช่แค่ HTTP status code</li>
                      <li>สร้างและใช้งาน webhook เพื่อรับข้อมูลแบบ real-time</li>
                      <li>ตั้งค่าขีดจำกัดการใช้งาน API Key ให้เหมาะสมเพื่อป้องกันการใช้งานที่ผิดปกติ</li>
                      <li>ใช้ตัวเลือก validation ตามความต้องการของคุณ เช่น การตรวจสอบจำนวนเงินหรือวันที่</li>
                    </ul>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="security" className="border-indigo-800/40">
                  <AccordionTrigger className="text-white">
                    <div className="flex items-center">
                      <Shield className="h-5 w-5 mr-2 text-amber-400" />
                      คำแนะนำด้านความปลอดภัย
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="text-indigo-200">
                    <ul className="space-y-2">
                      <li>เก็บ API Key ในตัวแปรสภาพแวดล้อม (environment variables) ไม่ควรฝังลงไปในโค้ดโดยตรง</li>
                      <li>ใช้ IP Whitelist เพื่อจำกัดการเข้าถึง API จากที่อยู่ IP ที่เชื่อถือได้เท่านั้น</li>
                      <li>ตรวจสอบ API Usage อย่างสม่ำเสมอเพื่อติดตามกิจกรรมที่ผิดปกติ</li>
                      <li>หมั่นเปลี่ยน API Key ตามระยะเวลาที่เหมาะสม</li>
                    </ul>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="webhooks" className="border-indigo-800/40">
                  <AccordionTrigger className="text-white">
                    <div className="flex items-center">
                      <Webhook className="h-5 w-5 mr-2 text-amber-400" />
                      การใช้งาน Webhook
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="text-indigo-200">
                    <p className="mb-2">Webhook ช่วยให้คุณสามารถรับข้อมูลแบบ real-time เมื่อมีเหตุการณ์เกิดขึ้นในระบบ</p>
                    <ul className="space-y-2">
                      <li>ตั้งค่า endpoint ที่พร้อมรับข้อมูลจาก webhook และตอบกลับด้วย HTTP 200 เมื่อได้รับข้อมูลสำเร็จ</li>
                      <li>กำหนดเงื่อนไขเพื่อกรองข้อมูลที่คุณต้องการรับจาก webhook เช่น เฉพาะการตรวจสอบสลิปที่มีจำนวนเงินมากกว่า 5,000 บาท</li>
                      <li>ใช้ Secret Key เพื่อยืนยันความถูกต้องของข้อมูลที่ได้รับจาก webhook</li>
                    </ul>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>
          </div>
        ),
      },
      {
        id: "webhooks",
        title: "Webhooks",
        icon: Webhook as LucideIcon,
        content: (
          <div className="space-y-6">
            <div className="p-6 rounded-lg bg-indigo-900/40 border border-indigo-800/40">
              <h3 className="text-xl font-semibold flex items-center text-white mb-4">
                <Webhook className="mr-2 h-5 w-5 text-amber-400" />
                การใช้งาน Webhook
              </h3>
              <p className="text-indigo-200 mb-6">
                Webhook ช่วยให้ระบบของคุณสามารถรับข้อมูลแบบ real-time เมื่อมีเหตุการณ์ที่กำหนดเกิดขึ้นในระบบ SLIPKUY
              </p>

              <div className="space-y-4 mb-6">
                <div className="flex items-start">
                  <div className="bg-amber-500/20 p-2 rounded-full mr-3 mt-1">
                    <Bell className="h-4 w-4 text-amber-400" />
                  </div>
                  <div>
                    <h4 className="font-medium text-white">ประเภทเหตุการณ์</h4>
                    <p className="text-indigo-300 text-sm">เลือกประเภทเหตุการณ์ที่ต้องการรับการแจ้งเตือน</p>
                    <ul className="list-disc list-inside text-indigo-300 text-sm mt-2 space-y-1">
                      <li>slip_verification - เมื่อมีการตรวจสอบสลิป</li>
                      <li>quota_low - เมื่อโควตาการใช้งานเหลือน้อย</li>
                      <li>credit_low - เมื่อเครดิตเหลือน้อย</li>
                      <li>api_key_expire - เมื่อ API Key ใกล้หมดอายุ</li>
                      <li>package_expire - เมื่อแพ็กเกจใกล้หมดอายุ</li>
                      <li>system_update - เมื่อมีการอัปเดตระบบ</li>
                      <li>fraud_detected - เมื่อตรวจพบการทำธุรกรรมที่น่าสงสัย</li>
                    </ul>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="bg-amber-500/20 p-2 rounded-full mr-3 mt-1">
                    <Filter className="h-4 w-4 text-amber-400" />
                  </div>
                  <div>
                    <h4 className="font-medium text-white">เงื่อนไข (ไม่บังคับ)</h4>
                    <p className="text-indigo-300 text-sm">กำหนดเงื่อนไขเพื่อกรองข้อมูลที่ต้องการรับจาก webhook</p>
                    <ul className="list-disc list-inside text-indigo-300 text-sm mt-2 space-y-1">
                      <li>จำนวนเงินขั้นต่ำ/สูงสุด</li>
                      <li>รหัสธนาคารผู้รับ/ผู้ส่ง</li>
                      <li>ชื่อบัญชีผู้รับ/ผู้ส่ง</li>
                    </ul>
                  </div>
                </div>
              </div>

              <h4 className="text-lg font-semibold text-white mb-3">ตัวอย่างข้อมูลที่ส่งผ่าน Webhook</h4>
              <div className="font-mono text-sm mb-6">
                <CopyBlock
                  text={`{
  "event": "slip_verification",
  "timestamp": "2025-04-19T16:05:22Z",
  "data": {
    "referenceId": "52423f93-1a5d-4210-a465-5e4cfd0a7ed1-1206",
    "transRef": "015109160510CTF01567",
    "dateTime": "2025-04-19T16:05:10+07:00",
    "amount": 155,
    "receiver": {
      "name": "นาย จิรายุ สัตย์ซื่อ",
      "bank": "KTB",
      "account": "xxx-x-x4800-x"
    },
    "sender": {
      "name": "น.ส. มาลี ดีงาม",
      "bank": "BBL",
      "account": "xxx-x-x5902-x"
    },
    "status": "success"
  }
}`}
                  language="json"
                  showLineNumbers={false}
                  theme={dracula}
                />
              </div>

              <div className="mt-6 text-center">
                <Link href="/auth">
                  <Button className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white">
                    เริ่มต้นใช้งานเพื่อตั้งค่า Webhook
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        ),
      },
    ],
  };

  return (
    <>
      <Helmet>
        <title>API Documentation | SLIPKUY</title>
        <meta name="description" content="เอกสารการใช้งาน API สำหรับการตรวจสอบสลิปโอนเงิน" />
      </Helmet>

      <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 text-white">
        <div className="container mx-auto px-4 py-20">
          <h1 className="text-4xl md:text-5xl font-bold text-center mb-2">
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-amber-400 to-amber-500">API Documentation</span>
          </h1>
          <p className="text-center text-indigo-300 mb-12 text-lg max-w-2xl mx-auto">
            เอกสารการใช้งานอย่างละเอียดสำหรับนักพัฒนาที่ต้องการใช้บริการตรวจสอบสลิปโอนเงินของ SLIPKUY
          </p>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
            <div className="md:col-span-1 space-y-2">
              <div className="p-6 bg-indigo-900/40 rounded-lg border border-indigo-800/40 sticky top-20">
                <h3 className="text-xl font-semibold mb-4 text-white">
                  คู่มือการใช้งาน
                </h3>
                <div className="space-y-2">
                  {docsConfig.sections.map((section) => (
                    <button
                      key={section.id}
                      onClick={() => setTab(section.id)}
                      className={`w-full flex items-center p-3 rounded-md transition-colors ${
                        tab === section.id
                          ? "bg-gradient-to-r from-amber-500/20 to-amber-600/20 text-amber-400 border border-amber-500/30"
                          : "text-indigo-200 hover:bg-indigo-800/30 hover:text-amber-300"
                      }`}
                    >
                      <section.icon className="h-5 w-5 mr-2" />
                      <span>{section.title}</span>
                    </button>
                  ))}
                </div>

                <div className="mt-6 pt-6 border-t border-indigo-800/40">
                  <Link href="/auth">
                    <Button className="w-full bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white">
                      เริ่มต้นใช้งานฟรี
                    </Button>
                  </Link>
                </div>
              </div>
            </div>

            <div className="md:col-span-3">
              {docsConfig.sections.map((section) => (
                tab === section.id && (
                  <div key={section.id} className="space-y-6">
                    {section.content}
                  </div>
                )
              ))}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}