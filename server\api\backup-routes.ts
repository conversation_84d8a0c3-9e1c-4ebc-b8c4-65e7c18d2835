import { Router } from 'express';
import { adminRequired } from '../auth';
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';
import { db } from '../db';
import { systemSettings } from '@shared/schema';
import { eq } from 'drizzle-orm';
import { logger } from '../logger';

const execPromise = promisify(exec);
const router = Router();

// สร้างโฟลเดอร์สำหรับเก็บไฟล์สำรองข้อมูล
// ใช้ import.meta.url เพื่อให้ได้พาธที่สมบูรณ์โดยไม่มีช่องว่าง (สำหรับ ESM)
import { fileURLToPath } from 'url';
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const backupDir = path.resolve(__dirname, '../../backups');
if (!fs.existsSync(backupDir)) {
  fs.mkdirSync(backupDir, { recursive: true });
}
logger.info(`สร้างโฟลเดอร์สำรองข้อมูลที่: ${backupDir}`);

/**
 * สร้างไฟล์สำรองข้อมูลฐานข้อมูล
 */
router.post('/create', adminRequired, async (req, res) => {
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFileName = `slipkuy_backup_${timestamp}.sql`;
    const backupFilePath = path.join(backupDir, backupFileName);

    // ดึงค่า DATABASE_URL จาก environment variable
    const databaseUrl = process.env.DATABASE_URL;
    if (!databaseUrl) {
      return res.status(500).json({ success: false, message: 'ไม่พบค่า DATABASE_URL ในตัวแปรสภาพแวดล้อม' });
    }

    // แยกข้อมูลการเชื่อมต่อจาก DATABASE_URL
    // รูปแบบ: postgresql://username:password@host:port/database
    const dbUrlRegex = /postgresql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/([^?]+)/;
    const match = databaseUrl.match(dbUrlRegex);

    if (!match) {
      return res.status(500).json({ success: false, message: 'รูปแบบ DATABASE_URL ไม่ถูกต้อง' });
    }

    const [, username, password, host, port, database] = match;

    // สร้างคำสั่ง pg_dump
    // ใช้ quotes รอบพาธของไฟล์เพื่อป้องกันปัญหาเรื่องช่องว่าง
    // ใช้ pg_dump ที่มาพร้อมกับ PostgreSQL เวอร์ชัน 16.8
    const pgDumpPath = '/usr/bin/pg_dump';
    const command = `PGPASSWORD=${password} ${pgDumpPath} -h ${host} -p ${port} -U ${username} -d ${database} -F p -f "${backupFilePath}"`;

    logger.info(`กำลังสร้างไฟล์สำรองข้อมูล: ${backupFileName}`);
    logger.info(`คำสั่ง pg_dump: ${command.replace(password, '********')}`);
    const { stdout, stderr } = await execPromise(command);

    if (stderr && !stderr.includes('warning')) {
      throw new Error(stderr);
    }

    // ตรวจสอบว่าไฟล์ถูกสร้างขึ้นหรือไม่
    if (!fs.existsSync(backupFilePath)) {
      throw new Error('ไม่สามารถสร้างไฟล์สำรองข้อมูลได้');
    }

    const fileSize = fs.statSync(backupFilePath).size;
    const fileSizeInMB = (fileSize / (1024 * 1024)).toFixed(2);

    logger.success(`สร้างไฟล์สำรองข้อมูลสำเร็จ: ${backupFileName} (${fileSizeInMB} MB)`);

    return res.status(200).json({
      success: true,
      message: 'สร้างไฟล์สำรองข้อมูลสำเร็จ',
      data: {
        fileName: backupFileName,
        filePath: backupFilePath,
        fileSize: `${fileSizeInMB} MB`,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('เกิดข้อผิดพลาดในการสร้างไฟล์สำรองข้อมูล:', error);
    return res.status(500).json({
      success: false,
      message: `เกิดข้อผิดพลาดในการสร้างไฟล์สำรองข้อมูล: ${error.message}`
    });
  }
});

/**
 * ดึงรายการไฟล์สำรองข้อมูลทั้งหมด
 */
router.get('/list', adminRequired, (req, res) => {
  try {
    if (!fs.existsSync(backupDir)) {
      return res.status(200).json({ success: true, data: [] });
    }

    const files = fs.readdirSync(backupDir)
      .filter(file => file.endsWith('.sql'))
      .map(file => {
        const filePath = path.join(backupDir, file);
        const stats = fs.statSync(filePath);
        const fileSizeInMB = (stats.size / (1024 * 1024)).toFixed(2);

        return {
          fileName: file,
          filePath: filePath,
          fileSize: `${fileSizeInMB} MB`,
          createdAt: stats.birthtime.toISOString()
        };
      })
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()); // เรียงจากใหม่ไปเก่า

    return res.status(200).json({ success: true, data: files });
  } catch (error) {
    logger.error('เกิดข้อผิดพลาดในการดึงรายการไฟล์สำรองข้อมูล:', error);
    return res.status(500).json({
      success: false,
      message: `เกิดข้อผิดพลาดในการดึงรายการไฟล์สำรองข้อมูล: ${error.message}`
    });
  }
});

/**
 * ดาวน์โหลดไฟล์สำรองข้อมูล
 */
router.get('/download/:fileName', adminRequired, (req, res) => {
  try {
    const { fileName } = req.params;
    const filePath = path.join(backupDir, fileName);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: 'ไม่พบไฟล์สำรองข้อมูลที่ระบุ'
      });
    }

    res.download(filePath);
  } catch (error) {
    logger.error('เกิดข้อผิดพลาดในการดาวน์โหลดไฟล์สำรองข้อมูล:', error);
    return res.status(500).json({
      success: false,
      message: `เกิดข้อผิดพลาดในการดาวน์โหลดไฟล์สำรองข้อมูล: ${error.message}`
    });
  }
});

/**
 * ลบไฟล์สำรองข้อมูล
 */
router.delete('/delete/:fileName', adminRequired, (req, res) => {
  try {
    const { fileName } = req.params;
    const filePath = path.join(backupDir, fileName);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: 'ไม่พบไฟล์สำรองข้อมูลที่ระบุ'
      });
    }

    fs.unlinkSync(filePath);

    logger.success(`ลบไฟล์สำรองข้อมูลสำเร็จ: ${fileName}`);

    return res.status(200).json({
      success: true,
      message: 'ลบไฟล์สำรองข้อมูลสำเร็จ'
    });
  } catch (error) {
    logger.error('เกิดข้อผิดพลาดในการลบไฟล์สำรองข้อมูล:', error);
    return res.status(500).json({
      success: false,
      message: `เกิดข้อผิดพลาดในการลบไฟล์สำรองข้อมูล: ${error.message}`
    });
  }
});

/**
 * ตั้งค่าการสำรองข้อมูลอัตโนมัติ
 */
router.post('/schedule', adminRequired, async (req, res) => {
  try {
    const { enabled, frequency, time, keepCount } = req.body;

    if (enabled === undefined) {
      return res.status(400).json({
        success: false,
        message: 'กรุณาระบุค่า enabled'
      });
    }

    if (enabled && (!frequency || !time)) {
      return res.status(400).json({
        success: false,
        message: 'กรุณาระบุค่า frequency และ time'
      });
    }

    // บันทึกการตั้งค่าลงในฐานข้อมูล
    const backupSettings = {
      enabled,
      frequency: frequency || 'daily',
      time: time || '00:00',
      keepCount: keepCount || 7,
      lastBackup: null,
      nextBackup: null
    };

    // คำนวณเวลาสำรองข้อมูลครั้งถัดไป
    if (enabled) {
      const [hours, minutes] = time.split(':').map(Number);
      const nextBackup = new Date();
      nextBackup.setHours(hours, minutes, 0, 0);

      // ถ้าเวลาที่ตั้งค่าผ่านไปแล้วในวันนี้ ให้เลื่อนไปวันถัดไป
      if (nextBackup < new Date()) {
        nextBackup.setDate(nextBackup.getDate() + 1);
      }

      // ปรับตามความถี่
      if (frequency === 'weekly') {
        // ตั้งเป็นวันอาทิตย์ถัดไป
        const dayOfWeek = nextBackup.getDay();
        if (dayOfWeek !== 0) { // 0 คือวันอาทิตย์
          nextBackup.setDate(nextBackup.getDate() + (7 - dayOfWeek));
        }
      } else if (frequency === 'monthly') {
        // ตั้งเป็นวันที่ 1 ของเดือนถัดไป
        nextBackup.setDate(1);
        nextBackup.setMonth(nextBackup.getMonth() + 1);
      }

      backupSettings.nextBackup = nextBackup.toISOString();
    }

    // บันทึกการตั้งค่าลงในฐานข้อมูล
    await db.insert(systemSettings)
      .values({
        key: 'backup_settings',
        valueJson: backupSettings,
        description: 'การตั้งค่าการสำรองข้อมูลอัตโนมัติ'
      })
      .onConflictDoUpdate({
        target: systemSettings.key,
        set: {
          valueJson: backupSettings,
          updatedAt: new Date()
        }
      });

    logger.success('บันทึกการตั้งค่าการสำรองข้อมูลอัตโนมัติสำเร็จ');

    return res.status(200).json({
      success: true,
      message: 'บันทึกการตั้งค่าการสำรองข้อมูลอัตโนมัติสำเร็จ',
      data: backupSettings
    });
  } catch (error) {
    logger.error('เกิดข้อผิดพลาดในการตั้งค่าการสำรองข้อมูลอัตโนมัติ:', error);
    return res.status(500).json({
      success: false,
      message: `เกิดข้อผิดพลาดในการตั้งค่าการสำรองข้อมูลอัตโนมัติ: ${error.message}`
    });
  }
});

/**
 * ดึงการตั้งค่าการสำรองข้อมูลอัตโนมัติ
 */
router.get('/schedule', adminRequired, async (req, res) => {
  try {
    const setting = await db.select()
      .from(systemSettings)
      .where(eq(systemSettings.key, 'backup_settings'));

    if (setting.length === 0) {
      return res.status(200).json({
        success: true,
        data: {
          enabled: false,
          frequency: 'daily',
          time: '00:00',
          keepCount: 7,
          lastBackup: null,
          nextBackup: null
        }
      });
    }

    return res.status(200).json({
      success: true,
      data: setting[0].valueJson
    });
  } catch (error) {
    logger.error('เกิดข้อผิดพลาดในการดึงการตั้งค่าการสำรองข้อมูลอัตโนมัติ:', error);
    return res.status(500).json({
      success: false,
      message: `เกิดข้อผิดพลาดในการดึงการตั้งค่าการสำรองข้อมูลอัตโนมัติ: ${error.message}`
    });
  }
});

export default router;
