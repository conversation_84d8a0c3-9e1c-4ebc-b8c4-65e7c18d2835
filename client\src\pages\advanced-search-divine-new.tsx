import React, { useState } from 'react';
import { format } from 'date-fns';
import { th } from 'date-fns/locale';
import Papa from 'papapar<PERSON>';
import { saveAs } from 'file-saver';
import { useToast } from "@/hooks/use-toast";
import { DateRange } from 'react-day-picker';
import { DashboardLayout } from "@/components/layouts/dashboard-layout";

// UI Components
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { DatePickerWithRange } from "@/components/ui/date-range-picker";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";

// Icons
import {
  Award,
  BarChart2,
  BarChart as BarChartIcon,
  Calendar,
  CheckCircle,
  ChevronDown,
  Clock,
  Download,
  FileBarChart,
  FileSpreadsheet,
  Flame,
  Filter,
  InfoIcon,
  Search,
  Sigma,
  Sparkles,
  Target,
  TrendingUp,
  Users,
} from "lucide-react";

// Recharts
import {
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  Bar,
  BarChart,
  Pie,
  PieChart,
  ResponsiveContainer,
  Tooltip as RechartsTooltip,
  XAxis,
  YAxis,
  Cell,
  Scatter,
  ScatterChart,
} from "recharts";

// กำหนดสีสำหรับแผนภูมิ
const COLORS = ['#3b82f6', '#a855f7', '#ec4899', '#f97316', '#fbbf24', '#84cc16', '#06b6d4', '#8b5cf6'];

// Component หลัก
export default function AdvancedSearchDivineNew() {
  // นำเข้า toast hooks
  const { toast } = useToast();
  
  // State สำหรับเก็บข้อมูลการค้นหาและผลลัพธ์
  const [searchResults, setSearchResults] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [searchCriteria, setSearchCriteria] = useState<{
    bankName: string;
    amount: number | '';
    receiver: string;
    userType: string;
    status: string;
  }>({
    bankName: '',
    amount: '',
    receiver: '',
    userType: '',
    status: 'all',
  });
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(new Date().setDate(new Date().getDate() - 30)),
    to: new Date(),
  });
  const [currentTab, setCurrentTab] = useState<string>('overview');
  const [includeDetails, setIncludeDetails] = useState<boolean>(true);

  // ฟังก์ชันสำหรับส่งคำขอค้นหา
  const handleSearch = async () => {
    setLoading(true);
    
    try {
      // สร้างข้อมูลสำหรับส่งไปยัง API
      const searchData = {
        startDate: dateRange?.from ? dateRange.from.toISOString() : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        endDate: dateRange?.to ? dateRange.to.toISOString() : new Date().toISOString(),
        bankName: searchCriteria.bankName === 'all' ? null : searchCriteria.bankName,
        amount: searchCriteria.amount ? Number(searchCriteria.amount) : null,
        receiver: searchCriteria.receiver || null,
        userType: searchCriteria.userType === 'all' ? null : searchCriteria.userType,
        status: searchCriteria.status === 'all' ? null : searchCriteria.status,
        includeDetails: includeDetails,
        analysisType: 'transaction', // ประเภทการวิเคราะห์: transaction, user, api
        groupBy: 'bank', // กลุ่มตามธนาคาร
        currentUserOnly: true // ค้นหาเฉพาะข้อมูลของผู้ใช้ที่ล็อกอินเท่านั้น
      };
      
      // ส่งคำขอไปยัง API
      console.log("กำลังส่งคำขอค้นหาไปยัง API:", searchData);
      
      const response = await fetch('/api/advanced-search/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(searchData),
        credentials: 'include'
      });
      
      console.log("การตอบกลับจาก API:", {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok
      });
      
      if (!response.ok) {
        throw new Error(`ข้อผิดพลาด: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // ตรวจสอบความสำเร็จ
      if (data.success === false) {
        throw new Error(data.message || 'เกิดข้อผิดพลาดในการค้นหา');
      }
      
      // แปลงข้อมูลที่ได้รับให้เป็นรูปแบบที่ UI ใช้งานได้
      console.log("ข้อมูลที่ได้รับจาก API:", data);
      // สำรวจโครงสร้างข้อมูลเพื่อการแก้ไขปัญหา
      console.log("โครงสร้างข้อมูลหลักจาก API:", Object.keys(data));
      
      let apiData: any;
      
      if (data.success === true && data.data) {
        console.log("API ส่งข้อมูลในรูปแบบ {success: true, data: {...}}");
        apiData = data.data;
      } else {
        console.log("API ส่งข้อมูลในรูปแบบอื่น ใช้ข้อมูลจาก data โดยตรง");
        apiData = data;
      }
      
      // ตรวจสอบค่าจริงๆ ที่อยู่ในข้อมูล
      console.log("ตรวจสอบค่าจริง apiData.totalAmount:", apiData.totalAmount);
      console.log("ตรวจสอบค่าจริง apiData.avgAmount:", apiData.avgAmount);
      console.log("ตรวจสอบค่าจริง apiData.totalTransactions:", apiData.totalTransactions);
      console.log("ตรวจสอบค่าจริง apiData.transactionCount:", apiData.transactionCount);
      console.log("ตรวจสอบค่าจริง apiData.bankDistribution:", apiData.bankDistribution);
      console.log("ตรวจสอบค่าจริง apiData.timeDistribution:", apiData.timeDistribution);
      console.log("ตรวจสอบค่าจริง apiData.topReceivers:", apiData.topReceivers);
      console.log("ตรวจสอบค่าจริง apiData.monthlyData:", apiData.monthlyData);
      console.log("ตรวจสอบค่าจริง apiData.transactions:", Array.isArray(apiData.transactions) ? 
        `Array ขนาด ${apiData.transactions.length}` : apiData.transactions);
      
      // ตรวจสอบสถานะการล็อกอิน ถ้าไม่ได้ล็อกอิน จะได้ message ว่า "กรุณาล็อกอินเพื่อใช้งาน"
      if (data.message && data.message.includes("กรุณาล็อกอิน")) {
        toast({
          title: "กรุณาล็อกอิน",
          description: "Session หมดอายุ กรุณาล็อกอินใหม่เพื่อใช้งาน",
          variant: "destructive"
        });
        // รอ 2 วินาทีแล้วนำทางกลับไปหน้าล็อกอิน
        setTimeout(() => {
          window.location.href = "/auth";
        }, 2000);
        throw new Error("ไม่ได้ล็อกอิน");
      }
      
      // สร้างข้อมูลตั้งต้นที่จำเป็นสำหรับ UI
      // แปลงข้อมูลจาก API ให้อยู่ในรูปแบบที่ UI ต้องการ
      const mockData = generateMockResults(); // ใช้เพื่อดูโครงสร้างข้อมูลเท่านั้น
      
      // คำนวณข้อมูลสรุปจากรายการที่ได้รับ
      let totalAmount = 0;
      let transactions = [];
      
      if (apiData.transactions && Array.isArray(apiData.transactions)) {
        transactions = apiData.transactions;
      } else if (apiData.details && Array.isArray(apiData.details)) {
        transactions = apiData.details;
      } else {
        // หากไม่พบข้อมูลจากชื่อที่คาดการณ์ไว้ ให้ลองหารายการในโครงสร้างข้อมูลอื่น
        for (const key in apiData) {
          if (Array.isArray(apiData[key]) && apiData[key].length > 0 && 
              (apiData[key][0].id || apiData[key][0].transactionRef || apiData[key][0].amount)) {
            transactions = apiData[key];
            console.log(`พบข้อมูลรายการใน apiData.${key}`);
            break;
          }
        }
      }
      
      // สร้างข้อมูลสรุปจากรายการที่พบ
      if (transactions.length > 0) {
        // คำนวณยอดรวมและค่าเฉลี่ย
        totalAmount = transactions.reduce((sum, t) => sum + (parseFloat(t.amount) || 0), 0);
        const avgAmount = totalAmount / transactions.length;
        
        // สร้างการกระจายตามธนาคาร
        const bankCounts = {};
        const bankAmounts = {};
        
        transactions.forEach(t => {
          const bank = t.bankName || t.bank || 'ไม่ระบุ';
          bankCounts[bank] = (bankCounts[bank] || 0) + 1;
          bankAmounts[bank] = (bankAmounts[bank] || 0) + (parseFloat(t.amount) || 0);
        });
        
        const bankDistribution = Object.keys(bankCounts).map(bank => ({
          name: bank,
          count: bankCounts[bank],
          totalAmount: bankAmounts[bank],
          percentage: (bankCounts[bank] / transactions.length) * 100
        })).sort((a, b) => b.count - a.count);
        
        // สร้างการกระจายตามช่วงเวลา
        const timeDistribution = [
          { time: 'เช้า (6:00-12:00)', count: 0, totalAmount: 0, percentage: 0 },
          { time: 'บ่าย (12:00-18:00)', count: 0, totalAmount: 0, percentage: 0 },
          { time: 'เย็น (18:00-0:00)', count: 0, totalAmount: 0, percentage: 0 },
          { time: 'กลางคืน (0:00-6:00)', count: 0, totalAmount: 0, percentage: 0 },
        ];
        
        transactions.forEach(t => {
          const date = new Date(t.transactionDate || t.date || t.createdAt);
          const hour = date.getHours();
          let timeIndex = 0;
          
          if (hour >= 6 && hour < 12) timeIndex = 0; // เช้า
          else if (hour >= 12 && hour < 18) timeIndex = 1; // บ่าย
          else if (hour >= 18) timeIndex = 2; // เย็น
          else timeIndex = 3; // กลางคืน
          
          timeDistribution[timeIndex].count++;
          timeDistribution[timeIndex].totalAmount += (parseFloat(t.amount) || 0);
        });
        
        timeDistribution.forEach(t => {
          t.percentage = (t.count / transactions.length) * 100;
        });
        
        // สร้างผู้รับเงินยอดนิยม
        const receiverCounts = {};
        const receiverAmounts = {};
        
        transactions.forEach(t => {
          const receiver = t.receiver || 'ไม่ระบุ';
          receiverCounts[receiver] = (receiverCounts[receiver] || 0) + 1;
          receiverAmounts[receiver] = (receiverAmounts[receiver] || 0) + (parseFloat(t.amount) || 0);
        });
        
        const topReceivers = Object.keys(receiverCounts).map(receiver => ({
          name: receiver,
          count: receiverCounts[receiver],
          totalAmount: receiverAmounts[receiver],
          percentage: (receiverCounts[receiver] / transactions.length) * 100
        })).sort((a, b) => b.count - a.count).slice(0, 5);
        
        // สร้างข้อมูลรายเดือน
        const months = ['ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.', 'ก.ค.', 'ส.ค.', 'ก.ย.', 'ต.ค.', 'พ.ย.', 'ธ.ค.'];
        const monthlyData = Array(12).fill(0).map((_, i) => ({
          month: months[i],
          count: 0,
          amount: 0
        }));
        
        transactions.forEach(t => {
          const date = new Date(t.transactionDate || t.date || t.createdAt);
          const month = date.getMonth();
          monthlyData[month].count++;
          monthlyData[month].amount += (parseFloat(t.amount) || 0);
        });
        
        // ประกอบข้อมูลสำหรับแสดงผล
        const results = {
          totalAmount,
          avgAmount,
          totalTransactions: transactions.length,
          bankDistribution,
          timeDistribution,
          topReceivers,
          monthlyData
        };
        
        console.log("ข้อมูลที่จะส่งไปยัง UI (สร้างใหม่):", results);
        console.log("จำนวนธุรกรรมที่พบ:", transactions.length);
        
        // สร้างข้อมูลสำหรับแสดงผลใน UI
        setSearchResults({
          results: results,
          transactions: transactions
        });
      } else {
        // กรณีไม่พบข้อมูล
        toast({
          title: "ไม่พบข้อมูล",
          description: "ไม่พบข้อมูลที่ตรงกับเงื่อนไขการค้นหา กรุณาลองใหม่อีกครั้ง",
          variant: "default"
        });
        
        // สร้างข้อมูลว่างสำหรับแสดงผล
        setSearchResults({
          results: {
            totalAmount: 0,
            avgAmount: 0,
            totalTransactions: 0,
            bankDistribution: [],
            timeDistribution: [],
            topReceivers: [],
            monthlyData: []
          },
          transactions: []
        });
      }
    } catch (error) {
      console.error('เกิดข้อผิดพลาดในการดึงข้อมูล:', error);
      // กรณีเกิดข้อผิดพลาด แต่ต้องการแสดงข้อมูลตัวอย่างสำหรับการพัฒนา UI
      // (ในการใช้งานจริงควรแสดงข้อความข้อผิดพลาดแทน)
      // แสดงข้อความข้อผิดพลาดแทนการใช้ข้อมูลจำลอง
      setSearchResults(null);
      alert(`เกิดข้อผิดพลาด: ${error instanceof Error ? error.message : 'ไม่สามารถดึงข้อมูลได้ กรุณาลองใหม่อีกครั้ง'}`);
    } finally {
      setLoading(false);
    }
  };

  // สร้างข้อมูลจำลองสำหรับการพัฒนา UI
  const generateMockResults = () => {
    return {
      results: {
        totalAmount: 1257950.75,
        avgAmount: 9676.54,
        totalTransactions: 130,
        bankDistribution: [
          { name: 'กสิกรไทย', count: 45, totalAmount: 432500.50, percentage: 34.62 },
          { name: 'ไทยพาณิชย์', count: 30, totalAmount: 286750.25, percentage: 23.08 },
          { name: 'กรุงไทย', count: 25, totalAmount: 243000.00, percentage: 19.23 },
          { name: 'กรุงเทพ', count: 15, totalAmount: 145200.00, percentage: 11.54 },
          { name: 'ออมสิน', count: 10, totalAmount: 96000.00, percentage: 7.69 },
          { name: 'ทหารไทย', count: 5, totalAmount: 54500.00, percentage: 3.85 },
        ],
        timeDistribution: [
          { time: 'เช้า (6:00-12:00)', count: 42, totalAmount: 438750.25, percentage: 32.31 },
          { time: 'บ่าย (12:00-18:00)', count: 56, totalAmount: 596250.50, percentage: 43.08 },
          { time: 'เย็น (18:00-0:00)', count: 27, totalAmount: 192450.00, percentage: 20.77 },
          { time: 'กลางคืน (0:00-6:00)', count: 5, totalAmount: 30500.00, percentage: 3.85 },
        ],
        topReceivers: [
          { name: 'บริษัท เอบีซี จำกัด', count: 15, totalAmount: 187500.25, percentage: 11.54 },
          { name: 'นายสมชาย ใจดี', count: 12, totalAmount: 125400.00, percentage: 9.23 },
          { name: 'บริษัท เอ็กซ์วายแซด จำกัด', count: 10, totalAmount: 98750.50, percentage: 7.69 },
          { name: 'ร้านค้าออนไลน์ 123', count: 8, totalAmount: 65400.00, percentage: 6.15 },
          { name: 'นางสาวสมหญิง รักดี', count: 6, totalAmount: 43500.00, percentage: 4.62 },
        ],
        monthlyData: [
          { month: 'ม.ค.', count: 12, amount: 120500 },
          { month: 'ก.พ.', count: 15, amount: 156200 },
          { month: 'มี.ค.', count: 18, amount: 187300 },
          { month: 'เม.ย.', count: 22, amount: 210400 },
          { month: 'พ.ค.', count: 19, amount: 198500 },
          { month: 'มิ.ย.', count: 24, amount: 245600 },
          { month: 'ก.ค.', count: 21, amount: 220700 },
          { month: 'ส.ค.', count: 18, amount: 176800 },
          { month: 'ก.ย.', count: 15, amount: 156900 },
          { month: 'ต.ค.', count: 17, amount: 187000 },
          { month: 'พ.ย.', count: 20, amount: 210100 },
          { month: 'ธ.ค.', count: 23, amount: 254200 },
        ],
      },
      transactions: Array(20).fill(null).map((_, index) => ({
        id: 1000 + index,
        date: new Date(Date.now() - index * ******** * Math.random() * 10),
        amount: Math.floor(Math.random() * 50000) + 500,
        bank: ['กสิกรไทย', 'ไทยพาณิชย์', 'กรุงไทย', 'กรุงเทพ', 'ออมสิน'][Math.floor(Math.random() * 5)],
        receiver: ['บริษัท เอบีซี จำกัด', 'นายสมชาย ใจดี', 'บริษัท เอ็กซ์วายแซด จำกัด', 'ร้านค้าออนไลน์ 123', 'นางสาวสมหญิง รักดี'][Math.floor(Math.random() * 5)],
        sender: ['นายอภิชาติ มั่นคง', 'นางสาวศิริพร รุ่งเรือง', 'บริษัท 123 จำกัด', 'นายวิทยา ศรีสุข', 'นางนภา ใจงาม'][Math.floor(Math.random() * 5)],
        status: ['สำเร็จ', 'สำเร็จ', 'สำเร็จ', 'กำลังตรวจสอบ', 'มีปัญหา'][Math.floor(Math.random() * 5)],
        ref: `REF${Math.floor(Math.random() * 1000000000).toString().padStart(10, '0')}`,
      })),
    };
  };

  // ฟังก์ชันสำหรับ export ข้อมูลเป็น CSV
  const exportToCSV = () => {
    if (!searchResults) return;

    // สร้างชื่อไฟล์ CSV ที่ประกอบด้วยเวลาปัจจุบัน
    const formattedDate = format(new Date(), 'yyyy-MM-dd_HH-mm-ss');
    const filename = `SLIPKUY_PRO_รายงานการวิเคราะห์_${formattedDate}.csv`;
    
    // สร้างข้อมูล CSV ทั้งหมด
    let csvData = [];
    
    // === ส่วนที่ 1: ส่วนหัวเอกสาร ===
    csvData.push(['รายงานการวิเคราะห์ข้อมูลขั้นสูง - SLIPKUY PRO']);
    csvData.push(['สลีปคุย (SLIPKUY)']);
    csvData.push(['วันที่สร้างรายงาน:', format(new Date(), 'dd/MM/yyyy', { locale: th })]);
    csvData.push(['เวลาที่สร้างรายงาน:', format(new Date(), 'HH:mm:ss')]);
    csvData.push(['เลขที่อ้างอิงรายงาน:', `SLIPKUY-${Math.floor(Math.random() * 90000) + 10000}-${formattedDate}`]);
    csvData.push([]);
    
    // === ส่วนที่ 2: ข้อมูลการค้นหา ===
    csvData.push(['ข้อมูลการค้นหา']);
    csvData.push(['ช่วงวันที่:', 
      dateRange?.from ? format(dateRange.from, 'PPP', { locale: th }) : '-', 
      'ถึง', 
      dateRange?.to ? format(dateRange.to, 'PPP', { locale: th }) : '-'
    ]);
    
    if (searchCriteria.bankName) {
      csvData.push(['ธนาคาร:', searchCriteria.bankName === 'all' ? 'ทั้งหมด' : searchCriteria.bankName]);
    }
    
    if (searchCriteria.amount) {
      csvData.push(['จำนวนเงิน:', `${searchCriteria.amount} บาท`]);
    }
    
    if (searchCriteria.receiver) {
      csvData.push(['ผู้รับ:', searchCriteria.receiver]);
    }
    
    if (searchCriteria.status && searchCriteria.status !== 'all') {
      const statusMap: { [key: string]: string } = {
        'success': 'สำเร็จ',
        'pending': 'กำลังตรวจสอบ',
        'failed': 'มีปัญหา'
      };
      csvData.push(['สถานะ:', statusMap[searchCriteria.status] || searchCriteria.status]);
    }
    
    csvData.push([]);
    
    // === ส่วนที่ 3: ข้อมูลสรุป ===
    csvData.push(['สรุปข้อมูลการวิเคราะห์']);
    csvData.push(['จำนวนรายการทั้งหมด:', searchResults.results.totalTransactions]);
    csvData.push(['จำนวนเงินทั้งหมด:', `${searchResults.results.totalAmount?.toLocaleString(undefined, { minimumFractionDigits: 2 })} บาท`]);
    csvData.push(['จำนวนเงินเฉลี่ยต่อรายการ:', `${searchResults.results.avgAmount?.toLocaleString(undefined, { minimumFractionDigits: 2 })} บาท`]);
    csvData.push([]);
    
    // === ส่วนที่ 4: ข้อมูลสรุปตามหมวดหมู่ ===
    // แยกตามธนาคาร
    if (Array.isArray(searchResults.results.bankDistribution) && searchResults.results.bankDistribution.length > 0) {
      csvData.push(['การแจกแจงตามธนาคาร']);
      csvData.push(['ธนาคาร', 'จำนวนรายการ', 'เปอร์เซ็นต์', 'จำนวนเงินรวม']);
      
      searchResults.results.bankDistribution.forEach((bank: any) => {
        csvData.push([
          bank.name || 'ไม่ระบุ',
          bank.count,
          (bank.percentage?.toFixed(2) || '0') + '%',
          (bank.totalAmount?.toLocaleString(undefined, { minimumFractionDigits: 2 }) || '0') + ' บาท'
        ]);
      });
      
      csvData.push([]);
    }
    
    // แยกตามช่วงเวลา
    if (Array.isArray(searchResults.results.timeDistribution) && searchResults.results.timeDistribution.length > 0) {
      csvData.push(['การแจกแจงตามช่วงเวลา']);
      csvData.push(['ช่วงเวลา', 'จำนวนรายการ', 'เปอร์เซ็นต์', 'จำนวนเงินรวม']);
      
      searchResults.results.timeDistribution.forEach((time: any) => {
        csvData.push([
          time.time || 'ไม่ระบุ',
          time.count,
          (time.percentage?.toFixed(2) || '0') + '%',
          (time.totalAmount?.toLocaleString(undefined, { minimumFractionDigits: 2 }) || '0') + ' บาท'
        ]);
      });
      
      csvData.push([]);
    }
    
    // ผู้รับยอดนิยม
    if (Array.isArray(searchResults.results.topReceivers) && searchResults.results.topReceivers.length > 0) {
      csvData.push(['ผู้รับโอนมากที่สุด']);
      csvData.push(['ผู้รับ', 'จำนวนครั้ง', 'เปอร์เซ็นต์', 'จำนวนเงินรวม']);
      
      searchResults.results.topReceivers.forEach((receiver: any) => {
        csvData.push([
          receiver.name || 'ไม่ระบุ',
          receiver.count,
          (receiver.percentage?.toFixed(2) || '0') + '%',
          (receiver.totalAmount?.toLocaleString(undefined, { minimumFractionDigits: 2 }) || '0') + ' บาท'
        ]);
      });
      
      csvData.push([]);
    }
    
    // ข้อมูลรายเดือน
    if (Array.isArray(searchResults.results.monthlyData) && searchResults.results.monthlyData.length > 0) {
      csvData.push(['แนวโน้มรายเดือน']);
      csvData.push(['เดือน', 'จำนวนรายการ', 'จำนวนเงินรวม']);
      
      searchResults.results.monthlyData.forEach((month: any) => {
        csvData.push([
          month.month || 'ไม่ระบุ',
          month.count || 0,
          (month.amount?.toLocaleString(undefined, { minimumFractionDigits: 2 }) || '0') + ' บาท'
        ]);
      });
      
      csvData.push([]);
    }
    
    // === ส่วนที่ 5: รายละเอียดรายการทั้งหมด ===
    if (includeDetails && Array.isArray(searchResults.transactions) && searchResults.transactions.length > 0) {
      csvData.push(['รายละเอียดรายการทั้งหมด']);
      csvData.push(['ลำดับ', 'วันที่', 'เวลา', 'ธนาคาร', 'จำนวนเงิน', 'ผู้รับ', 'ผู้ส่ง', 'รหัสอ้างอิง', 'สถานะ', 'หมายเหตุ']);
      
      searchResults.transactions.forEach((tx: any, index: number) => {
        let dateObj;
        try {
          const dateValue = tx.date || tx.transactionDate || tx.createdAt;
          dateObj = dateValue ? new Date(dateValue) : null;
        } catch (e) {
          dateObj = null;
        }
        
        // กำหนดสีสถานะต่างๆ สำหรับการแสดงผลใน CSV ที่รองรับสี (เช่น Excel)
        let statusColor = '';
        let statusText = tx.status || '-';
        
        if (statusText.toLowerCase().includes('success') || statusText === 'สำเร็จ') {
          statusColor = '#4ade80'; // สีเขียว
          statusText = 'สำเร็จ';
        } else if (statusText.toLowerCase().includes('pend') || statusText === 'กำลังตรวจสอบ') {
          statusColor = '#facc15'; // สีเหลือง
          statusText = 'กำลังตรวจสอบ';
        } else if (statusText.toLowerCase().includes('fail') || statusText === 'มีปัญหา') {
          statusColor = '#f87171'; // สีแดง
          statusText = 'มีปัญหา';
        }
        
        csvData.push([
          index + 1,
          dateObj ? format(dateObj, 'dd/MM/yyyy', { locale: th }) : '-',
          dateObj ? format(dateObj, 'HH:mm:ss') : '-',
          tx.bank || tx.bankName || '-',
          `${parseFloat(String(tx.amount || 0)).toLocaleString(undefined, { minimumFractionDigits: 2 })} ฿`,
          tx.receiver || '-',
          tx.sender || '-',
          tx.ref || tx.transactionRef || '-',
          statusText,
          tx.note || ''
        ]);
      });
    }
    
    // === ส่วนที่ 6: ข้อมูลสรุปท้ายรายงาน ===
    csvData.push([]);
    csvData.push(['รายงานนี้สร้างโดยระบบ SLIPKUY PRM Analytics']);
    csvData.push(['© สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า']);
    csvData.push(['หมายเหตุ: รายงานนี้ใช้สำหรับการวิเคราะห์ภายในองค์กรเท่านั้น']);
    
    // สร้าง CSV จากข้อมูลและดาวน์โหลด
    // กำหนดค่า configuration สำหรับ Papa.unparse เพื่อให้แสดงผลภาษาไทยได้ถูกต้อง
    const csv = Papa.unparse(csvData, {
      quotes: true, // ใส่เครื่องหมายคำพูดรอบทุกข้อมูล
      delimiter: ",", // ใช้ comma เป็นตัวคั่น
      newline: "\r\n", // ใช้ CRLF สำหรับ Windows
    });
    
    // เพิ่ม BOM (Byte Order Mark) เพื่อให้ Excel รองรับ UTF-8 ได้ถูกต้อง
    // ใช้ UTF-8 BOM สำหรับรองรับภาษาไทยใน Excel และโปรแกรมอื่นๆ
    const BOM = new Uint8Array([0xEF, 0xBB, 0xBF]);
    const csvBlob = new Blob([BOM, csv], { type: 'text/csv;charset=utf-8' });
    
    saveAs(csvBlob, filename);
    
    toast({
      title: "ส่งออกข้อมูลสำเร็จ",
      description: "ไฟล์ CSV พร้อมข้อมูลถูกดาวน์โหลดแล้ว",
      variant: "default"
    });
  };

  // สร้าง animation กรอบไฟวิ่งเหมือนเวทีของเทพเจ้า
  const godlyBorderStyle = {
    boxShadow: "0 0 5px #b794f4, 0 0 10px #805ad5, 0 0 15px #553c9a",
    animation: "godlyBorderAnimation 3s infinite linear",
  };

  return (
    <DashboardLayout>
      <style jsx global>{`
        @keyframes godlyBorderAnimation {
          0% {
            box-shadow: 0 0 5px #b794f4, 0 0 10px #805ad5, 0 0 15px #553c9a;
          }
          25% {
            box-shadow: 0 0 5px #93c5fd, 0 0 10px #3b82f6, 0 0 15px #1d4ed8;
          }
          50% {
            box-shadow: 0 0 5px #fbcfe8, 0 0 10px #ec4899, 0 0 15px #db2777;
          }
          75% {
            box-shadow: 0 0 5px #86efac, 0 0 10px #22c55e, 0 0 15px #15803d;
          }
          100% {
            box-shadow: 0 0 5px #b794f4, 0 0 10px #805ad5, 0 0 15px #553c9a;
          }
        }
        
        @keyframes godlyTextGlow {
          0% {
            text-shadow: 0 0 5px rgba(183, 148, 244, 0.7);
          }
          25% {
            text-shadow: 0 0 5px rgba(147, 197, 253, 0.7);
          }
          50% {
            text-shadow: 0 0 5px rgba(251, 207, 232, 0.7);
          }
          75% {
            text-shadow: 0 0 5px rgba(134, 239, 172, 0.7);
          }
          100% {
            text-shadow: 0 0 5px rgba(183, 148, 244, 0.7);
          }
        }
      `}</style>
      <div className="space-y-8 bg-indigo-950/90 bg-gradient-to-b from-indigo-950 to-indigo-900 rounded-lg p-6 shadow-lg">
        <div 
          className="text-center space-y-4 bg-gradient-to-r from-indigo-900/40 via-purple-900/30 to-violet-900/40 py-8 px-4 rounded-lg shadow-md mb-6 border border-indigo-700/30 relative overflow-hidden"
          style={godlyBorderStyle}
        >
          <div className="inline-flex items-center mb-2">
            <Sparkles className="h-6 w-6 text-amber-500 mr-2" />
            <h1 
              className="text-4xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-pink-500 to-amber-400"
              style={{ animation: "godlyTextGlow 3s infinite linear" }}
            >
              ศูนย์วิเคราะห์ข้อมูลเทพเจ้า
            </h1>
            <Sparkles className="h-6 w-6 text-amber-500 ml-2" />
          </div>
          <p className="text-lg bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-500 font-medium max-w-3xl mx-auto">
            ค้นหาและวิเคราะห์ข้อมูลการโอนเงินผ่านสลิปด้วยพลังแห่งเทพเจ้า ค้นพบข้อมูลเชิงลึกและแนวโน้มที่ซ่อนอยู่
          </p>
          <div className="flex flex-wrap justify-center gap-2 mt-4">
            <Badge variant="outline" className="bg-blue-950/80 backdrop-blur-sm border-blue-700">
              <BarChart2 className="h-3.5 w-3.5 mr-1 text-blue-400" />
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-cyan-500">วิเคราะห์เชิงลึก</span>
            </Badge>
            <Badge variant="outline" className="bg-purple-950/80 backdrop-blur-sm border-purple-700">
              <Clock className="h-3.5 w-3.5 mr-1 text-purple-400" />
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-pink-500">ต่อเนื่องแบบเรียลไทม์</span>
            </Badge>
            <Badge variant="outline" className="bg-amber-950/80 backdrop-blur-sm border-amber-700">
              <FileSpreadsheet className="h-3.5 w-3.5 mr-1 text-amber-400" />
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-amber-400 to-red-500">รายงาน CSV</span>
            </Badge>
            <Badge variant="outline" className="bg-emerald-950/80 backdrop-blur-sm border-emerald-700">
              <Users className="h-3.5 w-3.5 mr-1 text-emerald-400" />
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-emerald-400 to-teal-500">สำหรับผู้บริหาร</span>
            </Badge>
          </div>
        </div>
      
        {/* แผงค้นหา */}
        <Card 
          className="border-indigo-800/50 dark:border-indigo-700/50 bg-indigo-900/40 dark:bg-indigo-900/60 backdrop-blur-sm shadow-md bg-gradient-to-br from-indigo-900/70 to-purple-900/60 relative overflow-hidden"
          style={godlyBorderStyle}
        >
          <CardHeader className="pb-3">
            <CardTitle className="bg-clip-text text-transparent bg-gradient-to-r from-pink-500 via-purple-500 to-cyan-500 font-bold">ค้นหาข้อมูลขั้นสูง</CardTitle>
            <CardDescription className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-300 via-purple-300 to-pink-300">
              กำหนดเงื่อนไขในการค้นหาข้อมูลการตรวจสอบสลิปและการโอนเงิน
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="space-y-2">
                <Label htmlFor="dateRange" className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-cyan-500 to-indigo-500 font-semibold">ช่วงเวลา</Label>
                <DatePickerWithRange
                  className="w-full"
                  date={dateRange}
                  setDate={setDateRange as any}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="bank" className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-indigo-500 to-blue-500 font-semibold">ธนาคาร</Label>
                <Select
                  value={searchCriteria.bankName}
                  onValueChange={(value) => 
                    setSearchCriteria({ ...searchCriteria, bankName: value })
                  }
                >
                  <SelectTrigger id="bank">
                    <SelectValue placeholder="เลือกธนาคาร" className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-indigo-500 to-blue-500" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-900/90 backdrop-blur-md border-indigo-700/50">
                    <SelectItem value="all" className="focus:bg-indigo-900/60 focus:text-transparent focus:bg-clip-text focus:bg-gradient-to-r focus:from-purple-400 focus:via-indigo-500 focus:to-blue-500">
                      <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-indigo-500 to-blue-500">ทั้งหมด</span>
                    </SelectItem>
                    <SelectItem value="กสิกรไทย" className="focus:bg-indigo-900/60 focus:text-transparent focus:bg-clip-text focus:bg-gradient-to-r focus:from-purple-400 focus:via-indigo-500 focus:to-blue-500">
                      <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-indigo-500 to-blue-500">กสิกรไทย</span>
                    </SelectItem>
                    <SelectItem value="ไทยพาณิชย์" className="focus:bg-indigo-900/60 focus:text-transparent focus:bg-clip-text focus:bg-gradient-to-r focus:from-purple-400 focus:via-indigo-500 focus:to-blue-500">
                      <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-indigo-500 to-blue-500">ไทยพาณิชย์</span>
                    </SelectItem>
                    <SelectItem value="กรุงไทย" className="focus:bg-indigo-900/60 focus:text-transparent focus:bg-clip-text focus:bg-gradient-to-r focus:from-purple-400 focus:via-indigo-500 focus:to-blue-500">
                      <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-indigo-500 to-blue-500">กรุงไทย</span>
                    </SelectItem>
                    <SelectItem value="กรุงเทพ" className="focus:bg-indigo-900/60 focus:text-transparent focus:bg-clip-text focus:bg-gradient-to-r focus:from-purple-400 focus:via-indigo-500 focus:to-blue-500">
                      <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-indigo-500 to-blue-500">กรุงเทพ</span>
                    </SelectItem>
                    <SelectItem value="กรุงศรี" className="focus:bg-indigo-900/60 focus:text-transparent focus:bg-clip-text focus:bg-gradient-to-r focus:from-purple-400 focus:via-indigo-500 focus:to-blue-500">
                      <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-indigo-500 to-blue-500">กรุงศรี</span>
                    </SelectItem>
                    <SelectItem value="ทหารไทย" className="focus:bg-indigo-900/60 focus:text-transparent focus:bg-clip-text focus:bg-gradient-to-r focus:from-purple-400 focus:via-indigo-500 focus:to-blue-500">
                      <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-indigo-500 to-blue-500">ทหารไทย</span>
                    </SelectItem>
                    <SelectItem value="ออมสิน" className="focus:bg-indigo-900/60 focus:text-transparent focus:bg-clip-text focus:bg-gradient-to-r focus:from-purple-400 focus:via-indigo-500 focus:to-blue-500">
                      <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-indigo-500 to-blue-500">ออมสิน</span>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="amount" className="bg-clip-text text-transparent bg-gradient-to-r from-pink-400 via-fuchsia-500 to-purple-500 font-semibold">จำนวนเงิน (บาท)</Label>
                <Input
                  id="amount"
                  type="number"
                  placeholder="ระบุจำนวนเงิน"
                  value={searchCriteria.amount}
                  onChange={(e) => 
                    setSearchCriteria({ 
                      ...searchCriteria, 
                      amount: e.target.value ? Number(e.target.value) : '' 
                    })
                  }
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="receiver" className="bg-clip-text text-transparent bg-gradient-to-r from-green-400 via-teal-500 to-cyan-500 font-semibold">ผู้รับเงิน</Label>
                <Input
                  id="receiver"
                  placeholder="ชื่อหรือเลขบัญชีผู้รับ"
                  value={searchCriteria.receiver}
                  onChange={(e) => 
                    setSearchCriteria({ ...searchCriteria, receiver: e.target.value })
                  }
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="user-type" className="bg-clip-text text-transparent bg-gradient-to-r from-amber-400 via-orange-500 to-red-500 font-semibold">ประเภทผู้ใช้</Label>
                <Select
                  value={searchCriteria.userType}
                  onValueChange={(value) => 
                    setSearchCriteria({ ...searchCriteria, userType: value })
                  }
                >
                  <SelectTrigger id="user-type">
                    <SelectValue placeholder="เลือกประเภทผู้ใช้" className="bg-clip-text text-transparent bg-gradient-to-r from-amber-400 via-orange-500 to-red-500" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-900/90 backdrop-blur-md border-amber-700/50">
                    <SelectItem value="all" className="focus:bg-amber-900/60 focus:text-transparent focus:bg-clip-text focus:bg-gradient-to-r focus:from-amber-400 focus:via-orange-500 focus:to-red-500">
                      <span className="bg-clip-text text-transparent bg-gradient-to-r from-amber-400 via-orange-500 to-red-500">ทั้งหมด</span>
                    </SelectItem>
                    <SelectItem value="individual" className="focus:bg-amber-900/60 focus:text-transparent focus:bg-clip-text focus:bg-gradient-to-r focus:from-amber-400 focus:via-orange-500 focus:to-red-500">
                      <span className="bg-clip-text text-transparent bg-gradient-to-r from-amber-400 via-orange-500 to-red-500">บุคคลธรรมดา</span>
                    </SelectItem>
                    <SelectItem value="business" className="focus:bg-amber-900/60 focus:text-transparent focus:bg-clip-text focus:bg-gradient-to-r focus:from-amber-400 focus:via-orange-500 focus:to-red-500">
                      <span className="bg-clip-text text-transparent bg-gradient-to-r from-amber-400 via-orange-500 to-red-500">นิติบุคคล</span>
                    </SelectItem>
                    <SelectItem value="government" className="focus:bg-amber-900/60 focus:text-transparent focus:bg-clip-text focus:bg-gradient-to-r focus:from-amber-400 focus:via-orange-500 focus:to-red-500">
                      <span className="bg-clip-text text-transparent bg-gradient-to-r from-amber-400 via-orange-500 to-red-500">หน่วยงานรัฐ</span>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="status" className="bg-clip-text text-transparent bg-gradient-to-r from-emerald-400 via-teal-500 to-green-500 font-semibold">สถานะรายการ</Label>
                <Select
                  value={searchCriteria.status}
                  onValueChange={(value) => 
                    setSearchCriteria({ ...searchCriteria, status: value })
                  }
                >
                  <SelectTrigger id="status">
                    <SelectValue placeholder="เลือกสถานะ" className="bg-clip-text text-transparent bg-gradient-to-r from-emerald-400 via-teal-500 to-green-500" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-900/90 backdrop-blur-md border-emerald-700/50">
                    <SelectItem value="all" className="focus:bg-emerald-900/60 focus:text-transparent focus:bg-clip-text focus:bg-gradient-to-r focus:from-emerald-400 focus:via-teal-500 focus:to-green-500">
                      <span className="bg-clip-text text-transparent bg-gradient-to-r from-emerald-400 via-teal-500 to-green-500">ทั้งหมด</span>
                    </SelectItem>
                    <SelectItem value="success" className="focus:bg-emerald-900/60 focus:text-transparent focus:bg-clip-text focus:bg-gradient-to-r focus:from-emerald-400 focus:via-teal-500 focus:to-green-500">
                      <span className="bg-clip-text text-transparent bg-gradient-to-r from-emerald-400 via-teal-500 to-green-500">สำเร็จ</span>
                    </SelectItem>
                    <SelectItem value="pending" className="focus:bg-amber-900/60 focus:text-transparent focus:bg-clip-text focus:bg-gradient-to-r focus:from-amber-400 focus:via-yellow-500 focus:to-orange-500">
                      <span className="bg-clip-text text-transparent bg-gradient-to-r from-amber-400 via-yellow-500 to-orange-500">กำลังตรวจสอบ</span>
                    </SelectItem>
                    <SelectItem value="failed" className="focus:bg-red-900/60 focus:text-transparent focus:bg-clip-text focus:bg-gradient-to-r focus:from-red-400 focus:via-rose-500 focus:to-pink-500">
                      <span className="bg-clip-text text-transparent bg-gradient-to-r from-red-400 via-rose-500 to-pink-500">มีปัญหา</span>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label className="text-transparent">-</Label>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="include-details" 
                    checked={includeDetails} 
                    onCheckedChange={(checked) => 
                      setIncludeDetails(checked as boolean)
                    }
                  />
                  <label
                    htmlFor="include-details"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 via-purple-500 to-pink-500 font-semibold"
                  >
                    แสดงรายการทั้งหมด
                  </label>
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button 
              onClick={handleSearch} 
              className="w-full sm:w-auto"
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="mr-2">กำลังค้นหา...</span>
                  <Progress value={45} className="h-2 w-16" />
                </>
              ) : (
                <>
                  <Search className="h-4 w-4 mr-2" />
                  ค้นหา
                </>
              )}
            </Button>
          </CardFooter>
        </Card>
        
        {/* ส่วนแสดงผลลัพธ์ */}
        {searchResults && (
          <div className="space-y-6">
            {/* Card หลักสำหรับผลลัพธ์การวิเคราะห์ */}
            <Card className="border-indigo-800 dark:border-indigo-700 bg-indigo-900/40 dark:bg-indigo-900/80 backdrop-blur-sm shadow-sm overflow-hidden">
              <CardHeader className="pb-3 bg-gradient-to-r from-indigo-900/60 via-purple-900/50 to-violet-900/60">
                <div className="flex items-center gap-2">
                  <FileBarChart className="h-5 w-5 text-purple-500" />
                  <CardTitle className="bg-clip-text text-transparent bg-gradient-to-r from-pink-500 via-purple-500 to-cyan-500 font-bold">ผลการวิเคราะห์ข้อมูล</CardTitle>
                </div>
                <CardDescription className="text-blue-300">
                  ข้อมูลระหว่าง <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-cyan-500 to-indigo-500">{dateRange?.from && format(dateRange.from, 'PPP', { locale: th })}</span> ถึง <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-cyan-500 to-indigo-500">{dateRange?.to && format(dateRange.to, 'PPP', { locale: th })}</span>
                </CardDescription>
              </CardHeader>
              
              <CardContent className="p-6">
                {/* สรุปสถิติ Key Metrics Cards แบบโดดเด่น */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                  {/* มูลค่ารวม */}
                  <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/50 dark:to-blue-900/30 border-blue-200 dark:border-blue-800/80 backdrop-blur-sm">
                    <CardContent className="p-6">
                      <div className="flex flex-col space-y-1.5">
                        <div className="flex items-center gap-2">
                          <div className="p-1.5 rounded-full bg-blue-100 dark:bg-blue-900">
                            <Award className="h-5 w-5 text-blue-700 dark:text-blue-400" />
                          </div>
                          <h3 className="text-sm font-medium bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-indigo-500 to-blue-700 font-semibold">มูลค่ารวม</h3>
                        </div>
                        <div className="mt-2">
                          <div className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-500 via-indigo-600 to-blue-700">
                            {searchResults.results.totalAmount?.toLocaleString(undefined, { minimumFractionDigits: 2 })} ฿
                          </div>
                          <p className="text-xs text-blue-700/70 dark:text-blue-400/70 mt-1">
                            จาก {searchResults.results.totalTransactions} รายการ
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* มูลค่าเฉลี่ย */}
                  <Card className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/50 dark:to-purple-900/30 border-purple-200 dark:border-purple-800/80 backdrop-blur-sm">
                    <CardContent className="p-6">
                      <div className="flex flex-col space-y-1.5">
                        <div className="flex items-center gap-2">
                          <div className="p-1.5 rounded-full bg-purple-100 dark:bg-purple-900">
                            <Target className="h-5 w-5 text-purple-700 dark:text-purple-400" />
                          </div>
                          <h3 className="text-sm font-medium bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-pink-500 to-purple-700 font-semibold">มูลค่าเฉลี่ย</h3>
                        </div>
                        <div className="mt-2">
                          <div className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-pink-600 to-purple-700">
                            {searchResults.results.avgAmount?.toLocaleString(undefined, { minimumFractionDigits: 2 })} ฿
                          </div>
                          <p className="text-xs text-purple-700/70 dark:text-purple-400/70 mt-1">
                            ต่อรายการทั้งหมด
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* ธนาคารยอดนิยม */}
                  <Card className="bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-950/50 dark:to-amber-900/30 border-amber-200 dark:border-amber-800/80 backdrop-blur-sm">
                    <CardContent className="p-6">
                      <div className="flex flex-col space-y-1.5">
                        <div className="flex items-center gap-2">
                          <div className="p-1.5 rounded-full bg-amber-100 dark:bg-amber-900">
                            <Flame className="h-5 w-5 text-amber-700 dark:text-amber-400" />
                          </div>
                          <h3 className="text-sm font-medium bg-clip-text text-transparent bg-gradient-to-r from-amber-400 via-orange-500 to-red-500 font-semibold">ธนาคารยอดนิยม</h3>
                        </div>
                        <div className="mt-2">
                          <div className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-amber-500 via-orange-600 to-red-700">
                            {searchResults.results.bankDistribution?.[0]?.name || 'ไม่มีข้อมูล'}
                          </div>
                          <p className="text-xs text-amber-700/70 dark:text-amber-400/70 mt-1">
                            {searchResults.results.bankDistribution?.[0]?.percentage.toFixed(1) || 0}% ของทั้งหมด
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* ผู้รับยอดนิยม */}
                  <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/50 dark:to-green-900/30 border-green-200 dark:border-green-800/80 backdrop-blur-sm">
                    <CardContent className="p-6">
                      <div className="flex flex-col space-y-1.5">
                        <div className="flex items-center gap-2">
                          <div className="p-1.5 rounded-full bg-green-100 dark:bg-green-900">
                            <TrendingUp className="h-5 w-5 text-green-700 dark:text-green-400" />
                          </div>
                          <h3 className="text-sm font-medium bg-clip-text text-transparent bg-gradient-to-r from-green-400 via-teal-500 to-cyan-500 font-semibold">ผู้รับยอดนิยม</h3>
                        </div>
                        <div className="mt-2">
                          <div className="text-lg font-bold truncate max-w-full bg-clip-text text-transparent bg-gradient-to-r from-green-500 via-teal-600 to-cyan-700" title={searchResults.results.topReceivers?.[0]?.name || 'ไม่มีข้อมูล'}>
                            {searchResults.results.topReceivers?.[0]?.name || 'ไม่มีข้อมูล'}
                          </div>
                          <p className="text-xs text-green-700/70 dark:text-green-400/70 mt-1">
                            {searchResults.results.topReceivers?.[0]?.count || 0} รายการ
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* แท็บและกราฟ */}
                <Tabs defaultValue="overview" className="mt-6" onValueChange={setCurrentTab}>
                  <TabsList className="grid w-full grid-cols-4 bg-indigo-800/50 dark:bg-indigo-900/50">
                    <TabsTrigger 
                      value="overview" 
                      className="data-[state=active]:bg-clip-text data-[state=active]:text-transparent data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-400 data-[state=active]:via-purple-500 data-[state=active]:to-pink-500 data-[state=active]:font-bold"
                    >
                      ภาพรวม
                    </TabsTrigger>
                    <TabsTrigger 
                      value="banks" 
                      className="data-[state=active]:bg-clip-text data-[state=active]:text-transparent data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-400 data-[state=active]:via-indigo-500 data-[state=active]:to-purple-600 data-[state=active]:font-bold"
                    >
                      ธนาคาร
                    </TabsTrigger>
                    <TabsTrigger 
                      value="time" 
                      className="data-[state=active]:bg-clip-text data-[state=active]:text-transparent data-[state=active]:bg-gradient-to-r data-[state=active]:from-emerald-400 data-[state=active]:via-teal-500 data-[state=active]:to-cyan-600 data-[state=active]:font-bold"
                    >
                      ช่วงเวลา
                    </TabsTrigger>
                    <TabsTrigger 
                      value="trends" 
                      className="data-[state=active]:bg-clip-text data-[state=active]:text-transparent data-[state=active]:bg-gradient-to-r data-[state=active]:from-amber-400 data-[state=active]:via-orange-500 data-[state=active]:to-red-600 data-[state=active]:font-bold"
                    >
                      แนวโน้ม
                    </TabsTrigger>
                  </TabsList>
                  
                  {/* แท็บภาพรวม */}
                  <TabsContent value="overview" className="mt-6 space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* กราฟแท่งแสดงข้อมูลธนาคาร */}
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-base bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-indigo-500 to-purple-500 font-semibold">การกระจายตามธนาคาร</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="h-[300px]">
                            <ResponsiveContainer width="100%" height="100%">
                              <BarChart
                                data={searchResults.results.bankDistribution}
                                margin={{ top: 20, right: 30, left: 0, bottom: 5 }}
                              >
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis dataKey="name" />
                                <YAxis />
                                <RechartsTooltip
                                  formatter={(value, name, props) => {
                                    if (name === 'count') return [`${value} รายการ`, 'จำนวนรายการ'];
                                    if (name === 'totalAmount') return [`${Number(value).toLocaleString()} บาท`, 'มูลค่ารวม'];
                                    return [value, name];
                                  }}
                                />
                                <Legend />
                                <Bar name="จำนวนรายการ" dataKey="count" fill="#8884d8" />
                                <Bar name="มูลค่ารวม (บาท)" dataKey="totalAmount" fill="#82ca9d" />
                              </BarChart>
                            </ResponsiveContainer>
                          </div>
                        </CardContent>
                      </Card>
                      
                      {/* กราฟวงกลมแสดงข้อมูลช่วงเวลา */}
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-base bg-clip-text text-transparent bg-gradient-to-r from-emerald-400 via-teal-500 to-cyan-500 font-semibold">การกระจายตามช่วงเวลา</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="h-[300px]">
                            <ResponsiveContainer width="100%" height="100%">
                              <PieChart>
                                <Pie
                                  data={searchResults.results.timeDistribution}
                                  cx="50%"
                                  cy="50%"
                                  labelLine={false}
                                  outerRadius={100}
                                  fill="#8884d8"
                                  dataKey="count"
                                  nameKey="time"
                                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                                >
                                  {searchResults.results.timeDistribution.map((entry, index) => (
                                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                  ))}
                                </Pie>
                                <RechartsTooltip
                                  formatter={(value, name, props) => {
                                    return [`${value} รายการ (${props.payload.percentage.toFixed(1)}%)`, name];
                                  }}
                                />
                                <Legend />
                              </PieChart>
                            </ResponsiveContainer>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                    
                    {/* กราฟเส้นแสดงข้อมูลรายเดือน */}
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-base bg-clip-text text-transparent bg-gradient-to-r from-pink-400 via-purple-500 to-indigo-500 font-semibold">แนวโน้มรายเดือน</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="h-[300px]">
                          <ResponsiveContainer width="100%" height="100%">
                            <LineChart
                              data={searchResults.results.monthlyData}
                              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                            >
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis dataKey="month" />
                              <YAxis yAxisId="left" orientation="left" stroke="#8884d8" />
                              <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
                              <RechartsTooltip />
                              <Legend />
                              <Line
                                yAxisId="left"
                                type="monotone"
                                dataKey="count"
                                name="จำนวนรายการ"
                                stroke="#8884d8"
                                activeDot={{ r: 8 }}
                              />
                              <Line
                                yAxisId="right"
                                type="monotone"
                                dataKey="amount"
                                name="มูลค่ารวม (บาท)"
                                stroke="#82ca9d"
                              />
                            </LineChart>
                          </ResponsiveContainer>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>
                  
                  {/* แท็บธนาคาร */}
                  <TabsContent value="banks" className="mt-6 space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* กราฟวงกลมแสดงส่วนแบ่งธนาคาร */}
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-base bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-blue-500 to-indigo-500 font-semibold">ส่วนแบ่งตามธนาคาร (จำนวนรายการ)</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="h-[300px]">
                            <ResponsiveContainer width="100%" height="100%">
                              <PieChart>
                                <Pie
                                  data={searchResults.results.bankDistribution}
                                  cx="50%"
                                  cy="50%"
                                  labelLine={false}
                                  outerRadius={100}
                                  fill="#8884d8"
                                  dataKey="count"
                                  nameKey="name"
                                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                                >
                                  {searchResults.results.bankDistribution.map((entry, index) => (
                                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                  ))}
                                </Pie>
                                <RechartsTooltip
                                  formatter={(value, name, props) => {
                                    return [`${value} รายการ (${props.payload.percentage.toFixed(1)}%)`, name];
                                  }}
                                />
                              </PieChart>
                            </ResponsiveContainer>
                          </div>
                        </CardContent>
                      </Card>
                      
                      {/* กราฟวงกลมแสดงมูลค่าตามธนาคาร */}
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-base bg-clip-text text-transparent bg-gradient-to-r from-amber-400 via-orange-500 to-red-500 font-semibold">ส่วนแบ่งตามธนาคาร (มูลค่า)</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="h-[300px]">
                            <ResponsiveContainer width="100%" height="100%">
                              <PieChart>
                                <Pie
                                  data={searchResults.results.bankDistribution}
                                  cx="50%"
                                  cy="50%"
                                  labelLine={false}
                                  outerRadius={100}
                                  fill="#8884d8"
                                  dataKey="totalAmount"
                                  nameKey="name"
                                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                                >
                                  {searchResults.results.bankDistribution.map((entry, index) => (
                                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                  ))}
                                </Pie>
                                <RechartsTooltip
                                  formatter={(value, name, props) => {
                                    return [`${Number(value).toLocaleString()} บาท`, name];
                                  }}
                                />
                              </PieChart>
                            </ResponsiveContainer>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                    
                    {/* ตารางข้อมูลธนาคาร */}
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-base bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-indigo-500 to-violet-500 font-semibold">รายละเอียดตามธนาคาร</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <Table>
                          <TableHeader className="bg-gradient-to-r from-blue-950/30 to-indigo-950/30 backdrop-blur-sm">
                            <TableRow>
                              <TableHead className="text-blue-300 font-semibold">ธนาคาร</TableHead>
                              <TableHead className="text-right text-indigo-300 font-semibold">จำนวนรายการ</TableHead>
                              <TableHead className="text-right text-violet-300 font-semibold">มูลค่ารวม (บาท)</TableHead>
                              <TableHead className="text-right text-purple-300 font-semibold">มูลค่าเฉลี่ย (บาท)</TableHead>
                              <TableHead className="text-right text-fuchsia-300 font-semibold">ส่วนแบ่ง (%)</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {searchResults.results.bankDistribution.map((bank, index) => (
                              <TableRow key={index}>
                                <TableCell className="font-medium">{bank.name}</TableCell>
                                <TableCell className="text-right">{bank.count}</TableCell>
                                <TableCell className="text-right">{bank.totalAmount.toLocaleString(undefined, { minimumFractionDigits: 2 })}</TableCell>
                                <TableCell className="text-right">{(bank.totalAmount / bank.count).toLocaleString(undefined, { minimumFractionDigits: 2 })}</TableCell>
                                <TableCell className="text-right">{bank.percentage.toFixed(2)}%</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </CardContent>
                    </Card>
                  </TabsContent>
                  
                  {/* แท็บช่วงเวลา */}
                  <TabsContent value="time" className="mt-6 space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* กราฟวงกลมแสดงช่วงเวลา */}
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-base bg-clip-text text-transparent bg-gradient-to-r from-sky-400 via-blue-500 to-indigo-500 font-semibold">การกระจายตามช่วงเวลา (จำนวนรายการ)</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="h-[300px]">
                            <ResponsiveContainer width="100%" height="100%">
                              <PieChart>
                                <Pie
                                  data={searchResults.results.timeDistribution}
                                  cx="50%"
                                  cy="50%"
                                  labelLine={false}
                                  outerRadius={100}
                                  fill="#8884d8"
                                  dataKey="count"
                                  nameKey="time"
                                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                                >
                                  {searchResults.results.timeDistribution.map((entry, index) => (
                                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                  ))}
                                </Pie>
                                <RechartsTooltip
                                  formatter={(value, name, props) => {
                                    return [`${value} รายการ (${props.payload.percentage.toFixed(1)}%)`, name];
                                  }}
                                />
                              </PieChart>
                            </ResponsiveContainer>
                          </div>
                        </CardContent>
                      </Card>
                      
                      {/* กราฟแท่งแสดงมูลค่าตามช่วงเวลา */}
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-base bg-clip-text text-transparent bg-gradient-to-r from-green-400 via-teal-500 to-emerald-500 font-semibold">มูลค่าตามช่วงเวลา</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="h-[300px]">
                            <ResponsiveContainer width="100%" height="100%">
                              <BarChart
                                data={searchResults.results.timeDistribution}
                                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                              >
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis dataKey="time" />
                                <YAxis />
                                <RechartsTooltip
                                  formatter={(value, name, props) => {
                                    if (name === 'totalAmount') return [`${Number(value).toLocaleString()} บาท`, 'มูลค่ารวม'];
                                    return [value, name];
                                  }}
                                />
                                <Legend />
                                <Bar name="มูลค่ารวม (บาท)" dataKey="totalAmount" fill="#82ca9d" />
                              </BarChart>
                            </ResponsiveContainer>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                    
                    {/* ตารางข้อมูลช่วงเวลา */}
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-base bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-teal-500 to-cyan-500 font-semibold">รายละเอียดตามช่วงเวลา</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>ช่วงเวลา</TableHead>
                              <TableHead className="text-right">จำนวนรายการ</TableHead>
                              <TableHead className="text-right">มูลค่ารวม (บาท)</TableHead>
                              <TableHead className="text-right">มูลค่าเฉลี่ย (บาท)</TableHead>
                              <TableHead className="text-right">ส่วนแบ่ง (%)</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {searchResults.results.timeDistribution.map((time, index) => (
                              <TableRow key={index}>
                                <TableCell className="font-medium">{time.time}</TableCell>
                                <TableCell className="text-right">{time.count}</TableCell>
                                <TableCell className="text-right">{time.totalAmount.toLocaleString(undefined, { minimumFractionDigits: 2 })}</TableCell>
                                <TableCell className="text-right">{(time.totalAmount / time.count).toLocaleString(undefined, { minimumFractionDigits: 2 })}</TableCell>
                                <TableCell className="text-right">{time.percentage.toFixed(2)}%</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </CardContent>
                    </Card>
                  </TabsContent>
                  
                  {/* แท็บแนวโน้ม */}
                  <TabsContent value="trends" className="mt-6 space-y-6">
                    {/* กราฟเส้นแสดงแนวโน้มรายเดือน */}
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-base bg-clip-text text-transparent bg-gradient-to-r from-pink-400 via-purple-500 to-indigo-500 font-semibold">แนวโน้มรายเดือน</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="h-[300px]">
                          <ResponsiveContainer width="100%" height="100%">
                            <LineChart
                              data={searchResults.results.monthlyData}
                              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                            >
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis dataKey="month" />
                              <YAxis yAxisId="left" orientation="left" stroke="#8884d8" />
                              <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
                              <RechartsTooltip />
                              <Legend />
                              <Line
                                yAxisId="left"
                                type="monotone"
                                dataKey="count"
                                name="จำนวนรายการ"
                                stroke="#8884d8"
                                activeDot={{ r: 8 }}
                              />
                              <Line
                                yAxisId="right"
                                type="monotone"
                                dataKey="amount"
                                name="มูลค่ารวม (บาท)"
                                stroke="#82ca9d"
                              />
                            </LineChart>
                          </ResponsiveContainer>
                        </div>
                      </CardContent>
                    </Card>
                    
                    {/* กราฟกระจายความสัมพันธ์ระหว่างจำนวนและมูลค่า */}
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-base bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 via-blue-500 to-indigo-500 font-semibold">ความสัมพันธ์ระหว่างจำนวนรายการและมูลค่า</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="h-[300px]">
                          <ResponsiveContainer width="100%" height="100%">
                            <ScatterChart
                              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                            >
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis type="number" dataKey="count" name="จำนวนรายการ" />
                              <YAxis type="number" dataKey="amount" name="มูลค่า" />
                              <RechartsTooltip
                                formatter={(value, name, props) => {
                                  if (name === 'amount') return [`${Number(value).toLocaleString()} บาท`, 'มูลค่า'];
                                  if (name === 'count') return [`${value} รายการ`, 'จำนวน'];
                                  return [value, name];
                                }}
                                cursor={{ strokeDasharray: '3 3' }}
                              />
                              <Scatter name="เดือน" data={searchResults.results.monthlyData} fill="#8884d8">
                                {searchResults.results.monthlyData.map((entry, index) => (
                                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                ))}
                              </Scatter>
                            </ScatterChart>
                          </ResponsiveContainer>
                        </div>
                      </CardContent>
                    </Card>
                    
                    {/* ตารางข้อมูลรายเดือน */}
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-base bg-clip-text text-transparent bg-gradient-to-r from-violet-400 via-indigo-500 to-purple-500 font-semibold">รายละเอียดรายเดือน</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>เดือน</TableHead>
                              <TableHead className="text-right">จำนวนรายการ</TableHead>
                              <TableHead className="text-right">มูลค่ารวม (บาท)</TableHead>
                              <TableHead className="text-right">มูลค่าเฉลี่ย (บาท)</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {searchResults.results.monthlyData.map((month, index) => (
                              <TableRow key={index}>
                                <TableCell className="font-medium">{month.month}</TableCell>
                                <TableCell className="text-right">{month.count}</TableCell>
                                <TableCell className="text-right">{month.amount.toLocaleString(undefined, { minimumFractionDigits: 2 })}</TableCell>
                                <TableCell className="text-right">{(month.amount / month.count).toLocaleString(undefined, { minimumFractionDigits: 2 })}</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </CardContent>
                    </Card>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
            
            {/* ตารางแสดงรายการทั้งหมด */}
            {includeDetails && searchResults.transactions && searchResults.transactions.length > 0 && (
              <Card>
                <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <FileBarChart className="h-5 w-5 text-blue-500" />
                      <CardTitle className="bg-clip-text text-transparent bg-gradient-to-r from-fuchsia-400 via-purple-500 to-indigo-500 font-semibold">รายการทั้งหมด</CardTitle>
                    </div>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={exportToCSV}
                      className="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-950/30 dark:to-purple-950/30 border-indigo-200 dark:border-indigo-800 hover:from-indigo-100 hover:to-purple-100 dark:hover:from-indigo-900/50 dark:hover:to-purple-900/50 text-transparent bg-clip-text bg-gradient-to-r from-indigo-500 via-purple-600 to-pink-600"
                    >
                      <Download className="h-4 w-4 mr-2 text-indigo-500" />
                      ส่งออก CSV
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="p-0">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-cyan-500 to-indigo-500 font-semibold">วันที่</TableHead>
                        <TableHead className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-blue-500 to-indigo-500 font-semibold">ธนาคาร</TableHead>
                        <TableHead className="text-right bg-clip-text text-transparent bg-gradient-to-r from-pink-400 via-fuchsia-500 to-purple-500 font-semibold">จำนวนเงิน</TableHead>
                        <TableHead className="bg-clip-text text-transparent bg-gradient-to-r from-green-400 via-teal-500 to-cyan-500 font-semibold">ผู้รับ</TableHead>
                        <TableHead className="bg-clip-text text-transparent bg-gradient-to-r from-amber-400 via-orange-500 to-red-500 font-semibold">ผู้ส่ง</TableHead>
                        <TableHead className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 via-purple-500 to-pink-500 font-semibold">รหัสอ้างอิง</TableHead>
                        <TableHead className="bg-clip-text text-transparent bg-gradient-to-r from-emerald-400 via-teal-500 to-green-600 font-semibold">สถานะ</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {searchResults.transactions.slice(0, 15).map((transaction, index) => (
                        <TableRow key={index}>
                          <TableCell className="font-semibold bg-clip-text text-transparent bg-gradient-to-r from-blue-500 via-cyan-600 to-indigo-700">
                            {(() => {
                              try {
                                // รองรับวันที่ในรูปแบบที่หลากหลาย
                                const date = transaction.date || transaction.transactionDate || transaction.createdAt;
                                return date ? format(new Date(date), 'dd/MM/yyyy HH:mm') : '-';
                              } catch (e) {
                                return 'วันที่ไม่ถูกต้อง';
                              }
                            })()}
                          </TableCell>
                          <TableCell className="font-semibold bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-blue-600 to-indigo-700">
                            {transaction.bank || transaction.bankName || '-'}
                          </TableCell>
                          <TableCell className="text-right font-bold bg-clip-text text-transparent bg-gradient-to-r from-pink-500 via-fuchsia-600 to-purple-700">
                            {(() => {
                              try {
                                const amount = parseFloat(String(transaction.amount));
                                return !isNaN(amount) ? `${amount.toLocaleString(undefined, { minimumFractionDigits: 2 })} ฿` : '0.00 ฿';
                              } catch (e) {
                                return '0.00 ฿';
                              }
                            })()}
                          </TableCell>
                          <TableCell className="max-w-[180px] truncate font-semibold bg-clip-text text-transparent bg-gradient-to-r from-green-500 via-teal-600 to-cyan-700" title={transaction.receiver || '-'}>
                            {transaction.receiver || '-'}
                          </TableCell>
                          <TableCell className="max-w-[180px] truncate font-semibold bg-clip-text text-transparent bg-gradient-to-r from-amber-500 via-orange-600 to-red-700" title={transaction.sender || '-'}>
                            {transaction.sender || '-'}
                          </TableCell>
                          <TableCell className="font-mono text-xs bg-clip-text text-transparent bg-gradient-to-r from-indigo-500 via-purple-600 to-pink-700 font-semibold">
                            {transaction.ref || transaction.transactionRef || '-'}
                          </TableCell>
                          <TableCell>
                            <Badge
                              className={(() => {
                                const status = transaction.status || '';
                                
                                // ตรวจสอบสถานะแบบยืดหยุ่น
                                if (status.toLowerCase().includes('success') || status === 'สำเร็จ' || status === 'success') {
                                  return 'border border-emerald-200 dark:border-emerald-800 font-bold text-transparent bg-clip-text bg-gradient-to-r from-emerald-500 via-green-600 to-teal-500';
                                } else if (status.toLowerCase().includes('check') || status === 'กำลังตรวจสอบ' || status === 'pending') {
                                  return 'border border-amber-200 dark:border-amber-800 font-bold text-transparent bg-clip-text bg-gradient-to-r from-amber-500 via-yellow-600 to-orange-500';
                                } else if (status.toLowerCase().includes('error') || status === 'มีปัญหา' || status === 'failed') {
                                  return 'border border-red-200 dark:border-red-800 font-bold text-transparent bg-clip-text bg-gradient-to-r from-red-500 via-rose-600 to-pink-500';
                                } else {
                                  return 'border border-blue-200 dark:border-blue-800 font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-500 via-indigo-600 to-purple-500';
                                }
                              })()}
                            >
                              {(() => {
                                const status = transaction.status || '';
                                
                                // แปลงสถานะภาษาอังกฤษเป็นไทย
                                if (status.toLowerCase().includes('success')) return 'สำเร็จ';
                                if (status.toLowerCase().includes('pend') || status.toLowerCase().includes('check')) return 'กำลังตรวจสอบ';
                                if (status.toLowerCase().includes('error') || status.toLowerCase().includes('fail')) return 'มีปัญหา';
                                
                                return status || 'ไม่ระบุ';
                              })()}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                    {searchResults.transactions.length > 15 && (
                      <TableCaption className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 via-purple-500 to-pink-500 font-semibold">
                        แสดง 15 รายการจากทั้งหมด {searchResults.transactions.length} รายการ กดปุ่มส่งออกเพื่อดูทั้งหมด
                      </TableCaption>
                    )}
                  </Table>
                </CardContent>
              </Card>
            )}
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}