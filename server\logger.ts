/**
 * บริการบันทึกข้อมูลระบบ (Logger) แบบมีสีสันสำหรับ SLIPKUY
 */

// ANSI Color Codes สำหรับทำสีข้อความใน console
const COLORS = {
  RESET: "\x1b[0m",
  BRIGHT: "\x1b[1m",
  DIM: "\x1b[2m",
  UNDERSCORE: "\x1b[4m",
  BLINK: "\x1b[5m",
  REVERSE: "\x1b[7m",
  HIDDEN: "\x1b[8m",
  
  FG_BLACK: "\x1b[30m",
  FG_RED: "\x1b[31m",
  FG_GREEN: "\x1b[32m",
  FG_YELLOW: "\x1b[33m",
  FG_BLUE: "\x1b[34m",
  FG_MAGENTA: "\x1b[35m",
  FG_CYAN: "\x1b[36m",
  FG_WHITE: "\x1b[37m",
  FG_GRAY: "\x1b[90m",

  BG_BLACK: "\x1b[40m",
  BG_RED: "\x1b[41m",
  BG_GREEN: "\x1b[42m",
  BG_YELLOW: "\x1b[43m",
  BG_BLUE: "\x1b[44m",
  BG_MAGENTA: "\x1b[45m",
  BG_CYAN: "\x1b[46m",
  BG_WHITE: "\x1b[47m",
  BG_GRAY: "\x1b[100m",
};

// LOGO ASCII Art ของ SLIPKUY สำหรับแสดงเมื่อเริ่มต้นระบบ
const SLIPKUY_LOGO = `
${COLORS.FG_MAGENTA}${COLORS.BRIGHT}  ███████╗██╗     ██╗██████╗ ██╗  ██╗██╗   ██╗██╗   ██╗
  ██╔════╝██║     ██║██╔══██╗██║ ██╔╝██║   ██║╚██╗ ██╔╝
  ███████╗██║     ██║██████╔╝█████╔╝ ██║   ██║ ╚████╔╝ 
  ╚════██║██║     ██║██╔═══╝ ██╔═██╗ ██║   ██║  ╚██╔╝  
  ███████║███████╗██║██║     ██║  ██╗╚██████╔╝   ██║   
  ╚══════╝╚══════╝╚═╝╚═╝     ╚═╝  ╚═╝ ╚═════╝    ╚═╝${COLORS.RESET}   
${COLORS.FG_YELLOW}     💫 ระบบตรวจสอบและยืนยันสลิปธนาคารระดับเทพเจ้า 💫${COLORS.RESET}
`;

// ประเภทของ Log ระดับต่างๆ
const LOG_LEVEL = {
  ERROR: 0,
  WARNING: 1,
  INFO: 2,
  DEBUG: 3
};

// ค่าเริ่มต้นคือ INFO ในโหมด production และ DEBUG ในโหมด development
const DEFAULT_LEVEL = process.env.NODE_ENV === 'production' ? LOG_LEVEL.INFO : LOG_LEVEL.DEBUG;

// ระดับการบันทึกข้อมูลปัจจุบัน (สามารถเปลี่ยนแปลงได้ในระหว่างที่แอปทำงาน)
let currentLogLevel = DEFAULT_LEVEL;

// เก็บสถานะว่าได้แสดง logo ไปแล้วหรือยัง
let hasShownLogo = false;

/**
 * บริการสำหรับบันทึกข้อมูลและข้อผิดพลาดของระบบแบบมีสีสัน
 */
class Logger {
  constructor() {
    // แสดง logo เมื่อเริ่มต้นใช้งาน logger
    this.showLogo();
  }

  /**
   * แสดง LOGO ของ SLIPKUY (แสดงเพียงครั้งเดียวเมื่อเริ่มต้นระบบ)
   */
  showLogo() {
    if (!hasShownLogo) {
      console.log(SLIPKUY_LOGO);
      const timestamp = new Date().toISOString();
      console.log(`${COLORS.FG_CYAN}[${timestamp}] ${COLORS.BRIGHT}SLIPKUY ระบบเริ่มทำงาน...${COLORS.RESET}\n`);
      hasShownLogo = true;
    }
  }

  /**
   * รูปแบบเวลาแบบมีสีสัน
   * @returns string เวลาปัจจุบันแบบมีรูปแบบ
   */
  private getTimestamp(): string {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const seconds = now.getSeconds().toString().padStart(2, '0');
    const ms = now.getMilliseconds().toString().padStart(3, '0');
    
    return `${COLORS.FG_GRAY}${hours}:${minutes}:${seconds}.${ms}${COLORS.RESET}`;
  }

  /**
   * ตั้งค่าระดับการบันทึกข้อมูล
   * @param level ระดับการบันทึกข้อมูล (0=ERROR, 1=WARNING, 2=INFO, 3=DEBUG)
   */
  setLogLevel(level: number) {
    if (level >= LOG_LEVEL.ERROR && level <= LOG_LEVEL.DEBUG) {
      currentLogLevel = level;
      this.info(`ตั้งค่าระดับการบันทึกข้อมูลเป็น: ${Object.keys(LOG_LEVEL)[level]}`);
    }
  }
  
  /**
   * บันทึกข้อมูลข้อผิดพลาด
   * @param message ข้อความหลัก
   * @param args ข้อมูลเพิ่มเติม
   */
  error(message: string, ...args: any[]) {
    if (currentLogLevel >= LOG_LEVEL.ERROR) {
      const timestamp = this.getTimestamp();
      const prefix = `${timestamp} ${COLORS.BG_RED}${COLORS.FG_WHITE}${COLORS.BRIGHT} ❌ ERROR ${COLORS.RESET} `;
      console.error(`${prefix}${COLORS.FG_RED}${message}${COLORS.RESET}`, ...args);
    }
  }
  
  /**
   * บันทึกข้อมูลการเตือน
   * @param message ข้อความหลัก
   * @param args ข้อมูลเพิ่มเติม
   */
  warn(message: string, ...args: any[]) {
    if (currentLogLevel >= LOG_LEVEL.WARNING) {
      const timestamp = this.getTimestamp();
      const prefix = `${timestamp} ${COLORS.BG_YELLOW}${COLORS.FG_BLACK}${COLORS.BRIGHT} ⚠️ WARNING ${COLORS.RESET} `;
      console.warn(`${prefix}${COLORS.FG_YELLOW}${message}${COLORS.RESET}`, ...args);
    }
  }
  
  /**
   * บันทึกข้อมูลทั่วไป
   * @param message ข้อความหลัก
   * @param args ข้อมูลเพิ่มเติม
   */
  info(message: string, ...args: any[]) {
    if (currentLogLevel >= LOG_LEVEL.INFO) {
      const timestamp = this.getTimestamp();
      const prefix = `${timestamp} ${COLORS.BG_BLUE}${COLORS.FG_WHITE}${COLORS.BRIGHT} ℹ INFO ${COLORS.RESET} `;
      console.log(`${prefix}${COLORS.FG_CYAN}${message}${COLORS.RESET}`, ...args);
    }
  }
  
  /**
   * บันทึกข้อมูลสำหรับการพัฒนา
   * @param message ข้อความหลัก
   * @param args ข้อมูลเพิ่มเติม
   */
  debug(message: string, ...args: any[]) {
    if (currentLogLevel >= LOG_LEVEL.DEBUG) {
      const timestamp = this.getTimestamp();
      const prefix = `${timestamp} ${COLORS.BG_GRAY}${COLORS.FG_WHITE} 🔍 DEBUG ${COLORS.RESET} `;
      console.log(`${prefix}${COLORS.FG_GRAY}${message}${COLORS.RESET}`, ...args);
    }
  }

  /**
   * บันทึกข้อมูลสำหรับความปลอดภัย
   * @param message ข้อความหลัก
   * @param args ข้อมูลเพิ่มเติม
   */
  security(message: string, ...args: any[]) {
    if (currentLogLevel >= LOG_LEVEL.WARNING) {
      const timestamp = this.getTimestamp();
      const prefix = `${timestamp} ${COLORS.BG_MAGENTA}${COLORS.FG_WHITE}${COLORS.BRIGHT} 🔒 SECURITY ${COLORS.RESET} `;
      console.warn(`${prefix}${COLORS.FG_MAGENTA}${message}${COLORS.RESET}`, ...args);
    }
  }

  /**
   * บันทึกข้อมูลสำหรับการเริ่มต้นและสำเร็จของระบบ
   * @param message ข้อความหลัก
   * @param args ข้อมูลเพิ่มเติม
   */
  success(message: string, ...args: any[]) {
    if (currentLogLevel >= LOG_LEVEL.INFO) {
      const timestamp = this.getTimestamp();
      const prefix = `${timestamp} ${COLORS.BG_GREEN}${COLORS.FG_WHITE}${COLORS.BRIGHT} ✅ SUCCESS ${COLORS.RESET} `;
      console.log(`${prefix}${COLORS.FG_GREEN}${message}${COLORS.RESET}`, ...args);
    }
  }
}

// สร้าง instance เดียวที่ใช้ร่วมกันทั้งระบบ
export const logger = new Logger();