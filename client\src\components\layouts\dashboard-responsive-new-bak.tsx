import { ReactNode, useEffect, useState, useMemo } from "react";
import { Link, useLocation } from "wouter";
import { useAuth } from "@/hooks/use-auth";
import { useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  LayoutDashboard, 
  FileCheck, 
  History, 
  Package, 
  Settings, 
  Users,
  FileBarChart2,
  Ticket,
  Wallet,
  Shield,
  Zap,
  Sun,
  Moon,
  Cloud,
  Star,
  Sparkles,
  Crown,
  MountainSnow,
  CloudLightning,
  CloudRainWind,
  Code,
  Terminal,
  LogOut,
  Menu,
  X,
  Activity
} from "lucide-react";

interface DashboardLayoutProps {
  children: ReactNode;
}

// พาร์ติเคิลสำหรับสร้างจุดทองคำที่ลอยอยู่ในอากาศ
const GoldenParticles = ({ count = 30 }) => {
  const [particles, setParticles] = useState<Array<{
    id: number,
    x: number,
    y: number,
    size: number,
    speed: number,
    opacity: number
  }>>([]);
  
  useEffect(() => {
    const newParticles = Array.from({ length: count }, (_, i) => ({
      id: i,
      x: Math.random() * window.innerWidth,
      y: Math.random() * window.innerHeight,
      size: Math.random() * 2 + 1,
      speed: Math.random() * 0.5 + 0.2,
      opacity: Math.random() * 0.5 + 0.3
    }));
    
    setParticles(newParticles);
    
    const animateParticles = () => {
      setParticles(currentParticles => 
        currentParticles.map(p => ({
          ...p,
          y: p.y - p.speed,
          // ถ้าอนุภาคขึ้นไปถึงด้านบนของหน้าจอ ให้กลับมาเริ่มใหม่ที่ด้านล่าง
          ...(p.y < 0 ? { 
            y: window.innerHeight,
            x: Math.random() * window.innerWidth 
          } : {})
        }))
      );
    };
    
    const interval = setInterval(animateParticles, 50);
    
    return () => clearInterval(interval);
  }, [count]);
  
  return (
    <div className="fixed inset-0 pointer-events-none">
      {particles.map(particle => (
        <div
          key={particle.id}
          className="absolute rounded-full bg-amber-300"
          style={{
            left: `${particle.x}px`,
            top: `${particle.y}px`,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            opacity: particle.opacity,
            boxShadow: '0 0 4px rgba(251, 191, 36, 0.7)'
          }}
        />
      ))}
    </div>
  );
};

// เมฆสวรรค์ที่ลอยอยู่
const HeavenlyClouds = () => {
  return (
    <div className="fixed inset-0 pointer-events-none overflow-hidden" style={{
      '--cloud-1-duration': '80s',
      '--cloud-2-duration': '115s',
      '--cloud-3-duration': '95s',
      '--cloud-4-duration': '70s',
    } as React.CSSProperties}>
      <style>{`
        .cloud {
          position: absolute;
          opacity: 0.4;
          animation-name: float-cloud;
          animation-timing-function: linear;
          animation-iteration-count: infinite;
        }
        .cloud-1 {
          top: 20%;
          animation-duration: var(--cloud-1-duration);
        }
        .cloud-2 {
          top: 45%;
          animation-duration: var(--cloud-2-duration);
          animation-delay: 10s;
        }
        .cloud-3 {
          top: 75%;
          animation-duration: var(--cloud-3-duration);
          animation-delay: 5s;
        }
        .cloud-4 {
          top: 30%;
          animation-duration: var(--cloud-4-duration);
          animation-delay: 25s;
        }
        @keyframes float-cloud {
          from { left: -15%; }
          to { left: 100%; }
        }
      `}</style>
      <div className="cloud cloud-1">
        <Cloud className="text-indigo-100/10" size={300} />
      </div>
      <div className="cloud cloud-2">
        <Cloud className="text-indigo-100/10" size={240} />
      </div>
      <div className="cloud cloud-3">
        <CloudLightning className="text-amber-100/10" size={160} />
      </div>
      <div className="cloud cloud-4">
        <CloudRainWind className="text-blue-100/10" size={200} />
      </div>
    </div>
  );
};

// ไอคอนแบบเทพเจ้า
const DeityIcon = ({ icon: Icon, active, pulse = false }: { 
  icon: any, 
  active: boolean,
  pulse?: boolean
}) => {
  return (
    <div className={`relative flex items-center justify-center ${pulse ? 'animate-pulse' : ''}`}>
      <div className={`absolute h-10 w-10 rounded-full ${active ? 'bg-gradient-to-tr from-amber-300 to-amber-500' : 'bg-gradient-to-tr from-purple-900/50 to-indigo-800/70'} blur-[2px]`}></div>
      <div className={`absolute h-9 w-9 rounded-full ${active ? 'bg-gradient-to-br from-amber-200 to-orange-300 border border-amber-100/20' : 'bg-gradient-to-br from-indigo-900 to-purple-900 border border-indigo-500/20'} flex items-center justify-center overflow-hidden`}>
        <div className="absolute inset-0 w-full h-full">
          <div className={`w-full h-full ${active ? 'bg-gradient-radial from-yellow-200/70 via-amber-300/20 to-transparent' : 'bg-gradient-radial from-indigo-600/30 via-purple-700/20 to-transparent'}`}></div>
        </div>
      </div>
      <Icon className={`h-4 w-4 z-10 relative ${active ? 'text-white' : 'text-indigo-200'}`} />
      {active && (
        <div className="absolute h-12 w-12 rounded-full animate-ping-slow opacity-50 bg-amber-400/20"></div>
      )}
    </div>
  );
};

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const [location, setLocation] = useLocation();
  const { user, logoutMutation } = useAuth();
  const [hoveredLink, setHoveredLink] = useState<string | null>(null);
  const [isAuraActive, setIsAuraActive] = useState(true);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  
  const isAdmin = user?.role === 'admin';
  
  // สร้าง timestamp สำหรับรูปโปรไฟล์เมื่อโหลดครั้งแรกเท่านั้น
  const profileImageTimestamp = useMemo(() => Date.now(), []);
  
  const navigate = useCallback((to: string) => {
    setLocation(to);
    // ปิดเมนูมือถือเมื่อกดลิงก์
    setIsMobileMenuOpen(false);
  }, [setLocation]);
  
  const handleLogout = () => {
    logoutMutation.mutate(undefined, {
      onSuccess: () => {
        navigate('/auth');
      }
    });
  };
  
  // เอฟเฟกต์เพื่อทำให้ Aura เคลื่อนไหว
  useEffect(() => {
    const interval = setInterval(() => {
      setIsAuraActive(prev => !prev);
    }, 5000);
    
    return () => clearInterval(interval);
  }, []);
  
  // ปิดเมนูมือถือเมื่อมีการเปลี่ยนขนาดหน้าจอเป็น desktop
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setIsMobileMenuOpen(false);
      }
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  // จัดกลุ่มเมนูตามหมวดหมู่
  const userMenuGroups = [
    {
      title: "หน้าหลัก",
      items: [
        { href: "/dashboard", label: "แดชบอร์ด", icon: LayoutDashboard, description: "ภาพรวมการใช้งานและสถิติ" },
      ]
    },
    {
      title: "การตรวจสอบสลิป",
      items: [
        { href: "/verify", label: "ตรวจสอบสลิป", icon: FileCheck, description: "อัปโหลดและยืนยันสลิปธนาคาร" },
        { href: "/history", label: "ประวัติการตรวจสอบ", icon: History, description: "ดูประวัติการตรวจสอบทั้งหมด" },
        { href: "/advanced-search", label: "ค้นหาขั้นสูง", icon: FileBarChart2, description: "ค้นหาและวิเคราะห์ข้อมูลเชิงลึก" },
      ]
    },
    {
      title: "บัญชีและการชำระเงิน",
      items: [
        { href: "/user-packages", label: "แพ็กเกจ", icon: Package, description: "เลือกซื้อและจัดการแพ็กเกจ" },
        { href: "/topup", label: "เติมเงิน", icon: Wallet, description: "เติมเครดิตเข้าระบบ" },
      ]
    },
    {
      title: "API และนักพัฒนา",
      items: [
        { href: "/api-keys", label: "API Keys", icon: Shield, description: "จัดการและดูประวัติการใช้งาน API" },
        { href: "/api-tester", label: "ทดสอบ API", icon: Terminal, description: "ทดสอบการใช้งาน API แบบรวดเร็ว" },
        { href: "/api-docs", label: "คู่มือการใช้งาน API", icon: Zap, description: "คำแนะนำและเอกสารการใช้งาน API" },
      ]
    },
    {
      title: "ตั้งค่าบัญชี",
      items: [
        { href: "/profile", label: "โปรไฟล์", icon: Sun, description: "แก้ไขข้อมูลส่วนตัวและรูปโปรไฟล์" },
        { href: "/change-password", label: "เปลี่ยนรหัสผ่าน", icon: Shield, description: "เปลี่ยนรหัสผ่านเข้าสู่ระบบ" },
        { href: "/notifications", label: "การแจ้งเตือน", icon: CloudLightning, description: "จัดการการแจ้งเตือนต่างๆ" },
      ]
    }
  ];
  
  // รวมทุกลิงก์เข้าด้วยกันสำหรับใช้ในการหา active link
  const userLinks = userMenuGroups.flatMap(group => group.items);
  
  // ลิงก์เมนูสำหรับแอดมิน แบ่งเป็นกลุ่ม
  const adminMenuGroups = [
    {
      title: "แดชบอร์ด",
      items: [
        { href: "/dashboard", label: "⭐ แดชบอร์ดผู้ใช้ ⭐", icon: Activity, description: "กลับไปยังหน้าแดชบอร์ดผู้ใช้ทั่วไป", 
          divineClass: "from-amber-500 to-amber-600", highlight: true, special: true },
        { href: "/admin", label: "แดชบอร์ดแอดมิน", icon: LayoutDashboard, description: "ภาพรวมการใช้งานทั้งระบบ" },
      ]
    },
    {
      title: "การจัดการระบบ",
      items: [
        { href: "/admin/users", label: "จัดการผู้ใช้งาน", icon: Users, description: "จัดการบัญชีผู้ใช้ทั้งหมด" },
        { href: "/admin/packages", label: "จัดการแพ็กเกจ", icon: Package, description: "จัดการแพ็กเกจทั้งหมด" },
        { href: "/admin/coupons", label: "จัดการคูปอง", icon: Ticket, description: "จัดการคูปองส่วนลด" },
        { href: "/admin/stats", label: "รายงานและสถิติ", icon: FileBarChart2, description: "ดูรายงานสถิติเชิงลึก" },
        { href: "/admin/settings", label: "ตั้งค่าระบบ", icon: Settings, description: "ตั้งค่าการทำงานของระบบ" },
      ]
    }
  ];
  
  // แปลงเป็นลิงก์เดียวสำหรับใช้งานในส่วนอื่น
  const adminLinks = adminMenuGroups.flatMap(group => group.items);
  
  // เลือกลิงก์ที่จะแสดงตามสิทธิ์การใช้งาน
  const links = location.startsWith('/admin') ? adminLinks : userLinks;
  
  // ไอคอนประจำตำแหน่งใน sidebar แบบเทพเจ้า
  const deityIcons = {
    user: <Sun className="h-7 w-7 text-amber-300" />,
    admin: <Crown className="h-7 w-7 text-amber-300" />
  };

  // ฟังก์ชันสำหรับเรนเดอร์เมนูลิงก์
  const renderMenuLinks = (linkGroup: { title: string, items: any[] }, groupIndex: number) => (
    <div key={groupIndex} className="space-y-1">
      <div className="mb-2">
        <h3 className="text-xs font-semibold text-indigo-300/80 uppercase tracking-wider px-4">
          {linkGroup.title}
        </h3>
        <div className="h-px w-full bg-gradient-to-r from-indigo-500/20 via-indigo-400/10 to-transparent mt-1"></div>
      </div>

      <div>
        {linkGroup.items.map((link) => (
          <div 
            key={link.href}
            className="relative"
            onMouseEnter={() => setHoveredLink(link.href)}
            onMouseLeave={() => setHoveredLink(null)}
          >
            <div 
              className={`flex items-center px-4 py-2.5 relative rounded-xl transition-all duration-300 cursor-pointer
                ${location === link.href
                  ? "bg-gradient-to-r from-amber-700/40 to-amber-500/20 text-amber-300 shadow-lg shadow-amber-700/20"
                  : "hover:bg-indigo-800/20"
              }`}
              onClick={() => navigate(link.href)}
            >
              {/* ฉากหลังเมื่อ hover */}
              {hoveredLink === link.href && location !== link.href && (
                <motion.div 
                  layoutId="hoverBackground"
                  className="absolute inset-0 bg-gradient-to-r from-indigo-800/40 to-indigo-700/20 rounded-xl"
                  animate={{ opacity: 1 }}
                  initial={{ opacity: 0 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.2 }}
                />
              )}

              {/* Icon */}
              <div className="mr-3">
                <DeityIcon 
                  icon={link.icon} 
                  active={location === link.href}
                  pulse={location === link.href}
                />
              </div>

              {/* Label */}
              <span className={`font-medium text-[15px] ${location === link.href ? 'text-amber-200' : 'text-indigo-100'}`}>
                {link.label}
              </span>

              {/* Glow effect on active */}
              {location === link.href && (
                <motion.div 
                  className="absolute right-3 top-1/2 transform -translate-y-1/2"
                  animate={{ opacity: [0.6, 1, 0.6] }}
                  transition={{ repeat: Infinity, duration: 2 }}
                >
                  <Star className="h-4 w-4 text-amber-300" />
                </motion.div>
              )}

              {/* คำอธิบายเมื่อ hover */}
              {hoveredLink === link.href && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 10 }}
                  className="absolute left-0 top-full mt-2 bg-indigo-950/95 backdrop-blur-sm p-3 rounded-lg shadow-lg shadow-indigo-950/50 border border-indigo-800/50 w-64 z-50"
                >
                  <div className="font-semibold text-amber-200 mb-1 flex items-center">
                    <link.icon className="h-4 w-4 mr-2" />
                    {link.label}
                  </div>
                  <p className="text-xs text-indigo-200">{link.description}</p>
                </motion.div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  // ฟังก์ชันเรนเดอร์เมนูแอดมิน
  const renderAdminLinks = () => (
    <div className="space-y-5 mt-5">
      {/* สำหรับแต่ละกลุ่มเมนูแอดมิน */}
      {adminMenuGroups.map((group, idx) => (
        <div key={idx} className="space-y-1">
          <div className="mb-2">
            <h3 className="text-xs font-semibold text-amber-400/80 uppercase tracking-wider px-4">
              {group.title}
            </h3>
            <div className="h-px w-full bg-gradient-to-r from-amber-500/20 via-amber-400/10 to-transparent mt-1"></div>
          </div>

          {group.items.map((link) => (
            <div 
              key={link.href}
              className="relative"
              onMouseEnter={() => setHoveredLink(link.href)}
              onMouseLeave={() => setHoveredLink(null)}
            >
              <div 
                className={`flex items-center px-4 py-3 relative rounded-xl transition-all duration-300 cursor-pointer
                  ${location === link.href
                    ? "bg-gradient-to-r from-amber-700/40 to-amber-500/20 text-amber-300 shadow-lg shadow-amber-700/20"
                    : "hover:bg-indigo-800/20"
                }`}
                onClick={() => navigate(link.href)}
              >
                {/* ฉากหลังเมื่อ hover */}
                {hoveredLink === link.href && location !== link.href && (
                  <motion.div 
                    layoutId="hoverBackground"
                    className={`absolute inset-0 bg-gradient-to-r ${link.highlight ? 'from-amber-700/30 to-amber-500/20' : 'from-indigo-800/40 to-indigo-700/20'} rounded-xl`}
                    animate={{ opacity: 1 }}
                    initial={{ opacity: 0 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  />
                )}

                {/* Icon */}
                <div className="mr-3">
                  <DeityIcon 
                    icon={link.icon} 
                    active={location === link.href}
                    pulse={location === link.href}
                  />
                </div>

                {/* Label */}
                <span className={`font-medium ${
                  link.special ? 'text-amber-300 font-bold' : 
                  (location === link.href ? 'text-amber-200' : 'text-indigo-100')
                }`}>
                  {link.label}
                </span>
                
                {/* เพิ่มเอฟเฟกต์สำหรับปุ่มพิเศษ */}
                {link.special && (
                  <motion.div
                    className="absolute inset-0 rounded-xl border border-amber-500/40"
                    animate={{ opacity: [0.3, 0.8, 0.3] }}
                    transition={{ repeat: Infinity, duration: 3 }}
                  />
                )}

                {/* คำอธิบายเมื่อ hover */}
                {hoveredLink === link.href && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 10 }}
                    className="absolute left-0 top-full mt-2 bg-indigo-950/95 backdrop-blur-sm p-3 rounded-lg shadow-lg shadow-indigo-950/50 border border-indigo-800/50 w-64 z-50"
                  >
                    <div className="font-semibold text-amber-200 mb-1 flex items-center">
                      <link.icon className="h-4 w-4 mr-2" />
                      {link.label}
                    </div>
                    <p className="text-xs text-indigo-200">{link.description}</p>
                  </motion.div>
                )}
              </div>
            </div>
          ))}
        </div>
      ))}
    </div>
  );

  return (
    <div className="min-h-screen flex flex-col lg:flex-row bg-gradient-to-b from-indigo-950 via-indigo-900 to-purple-950 text-white overflow-hidden">
      {/* เอฟเฟกต์พื้นหลังแบบเทพเจ้า */}
      <div className="absolute inset-0 bg-gradient-radial from-indigo-800/10 via-purple-900/5 to-transparent"></div>
      <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiMzMTJlODEiIGZpbGwtb3BhY2l0eT0iMC4wNSI+PHBhdGggZD0iTTM2IDM0aDR2MWgtNHYtMXptMC0xaDR2LTFoLTR2MXptMC00aDR2LTFoLTR2MXptLTYgOGg0di0xaC00djF6bTAtMWg0di0xaC00djF6bTAtNGg0di0xaC00djF6bS02IDhoNHYtMWgtNHYxem0wLTFoNHYtMWgtNHYxem0wLTRoNHYtMWgtNHYxem0wLTRoNHYtMWgtNHYxem0yNC00aDR2LTFoLTR2MXptLTQgOGg0di0xaC00djF6bTAtMWg0di0xaC00djF6bTAtNGg0di0xaC00djF6bTAtNGg0di0xaC00djF6Ii8+PC9nPjwvZz48L3N2Zz4=')] opacity-10"></div>

      {/* ทำให้มีอนุภาคทองคำลอยอยู่ - แสดงเอฟเฟกต์ด้านหน้า */}
      <div className="absolute inset-0 pointer-events-none">
        <GoldenParticles count={40} />
        <HeavenlyClouds />
      </div>
      
      {/* Mobile Header - แสดงเฉพาะบนมือถือ */}
      <div className="lg:hidden sticky top-0 z-50 bg-gradient-to-r from-indigo-950 to-purple-950 border-b border-indigo-800/30 py-4 px-5 flex items-center justify-between">
        <div className="flex items-center">
          {user?.profileImage ? (
            <div className="w-10 h-10 rounded-full overflow-hidden border-2 border-amber-400/50 mr-3">
              <img 
                src={`${user.profileImage}?t=${profileImageTimestamp}`}
                alt="โปรไฟล์" 
                className="w-full h-full object-cover"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                }}
              />
            </div>
          ) : (
            <div className="w-10 h-10 rounded-full bg-gradient-to-br from-indigo-800 to-purple-800 border-2 border-amber-400/50 flex items-center justify-center mr-3">
              {isAdmin ? (
                <Crown className="h-5 w-5 text-amber-300" />
              ) : (
                <Sun className="h-5 w-5 text-amber-300" />
              )}
            </div>
          )}
          <div>
            <h2 className="text-lg font-semibold">{user?.username || 'ผู้ใช้งาน'}</h2>
            <p className="text-xs text-indigo-300">{isAdmin ? 'เทพเจ้าสูงสุด' : 'ผู้ใช้งานทั่วไป'}</p>
          </div>
        </div>
        
        {/* ปุ่มเปิด/ปิดเมนู */}
        <button 
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="p-2 rounded-lg bg-indigo-900/50 hover:bg-indigo-800/70 transition-colors focus:outline-none focus:ring-2 focus:ring-amber-400/50"
        >
          {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </div>

      {/* Sidebar - ด้านซ้าย สำหรับ desktop และ mobile */}
      <AnimatePresence>
        {(isMobileMenuOpen || window.innerWidth >= 1024) && (
          <motion.aside
            initial={{ x: -320, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: -320, opacity: 0 }}
            transition={{ type: "spring", bounce: 0.1, duration: 0.5 }}
            className={`
              bg-gradient-to-br from-indigo-950/90 to-purple-950/90 backdrop-blur-md 
              border-r border-indigo-800/30 overflow-y-auto z-40
              ${isMobileMenuOpen ? 
                'fixed inset-0 w-[280px] pb-8 pt-4 px-4 lg:static lg:pt-6 lg:w-80 lg:p-6 lg:shrink-0' : 
                'hidden lg:block lg:w-80 lg:p-6 lg:shrink-0'
              }
            `}
          >
            {/* ส่วนแสดงข้อมูลผู้ใช้ - แสดงเฉพาะบน Desktop */}
            <div className="mb-8 pb-6 border-b border-indigo-800/30 hidden lg:block">
              <div className="flex flex-col items-center">
                {/* รูปโปรไฟล์ */}
                <div className="relative mb-4">
                  <div className="w-24 h-24 rounded-full overflow-hidden border-2 border-amber-400/50 relative">
                    <div className="absolute inset-0 bg-gradient-to-br from-indigo-800 to-purple-800"></div>
                    
                    {user?.profileImage ? (
                      <>
                        <div className="absolute inset-0 flex items-center justify-center">
                          {isAdmin ? (
                            <Crown className="h-12 w-12 text-amber-300" />
                          ) : (
                            <Sun className="h-12 w-12 text-amber-300" />
                          )}
                        </div>
                        <img 
                          src={`${user.profileImage}?t=${profileImageTimestamp}`}
                          alt="โปรไฟล์" 
                          className="w-full h-full object-cover relative z-10"
                          onError={(e) => {
                            console.error("Image failed to load:", user.profileImage);
                            e.currentTarget.style.display = 'none';
                          }}
                        />
                      </>
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        {isAdmin ? (
                          <Crown className="h-12 w-12 text-amber-300" />
                        ) : (
                          <Sun className="h-12 w-12 text-amber-300" />
                        )}
                      </div>
                    )}
                    
                    {/* รัศมีเรืองรอง */}
                    <div className="absolute -inset-1 rounded-full bg-gradient-to-r from-amber-400/20 to-purple-500/20 blur-sm animate-pulse-slow"></div>
                  </div>
                  
                  {/* ปุ่มอัปโหลดรูป */}
                  <Link href="/profile">
                    <motion.button 
                      whileHover={{ scale: 1.1 }}
                      className="absolute bottom-0 right-0 bg-amber-400 text-indigo-900 rounded-full p-1.5 border-2 border-indigo-900 shadow-md z-20"
                    >
                      <Settings className="h-4 w-4" />
                    </motion.button>
                  </Link>
                </div>
                
                {/* ชื่อและอีเมล */}
                <div className="text-center mb-3">
                  <h2 className="text-xl font-bold tracking-tight">
                    <span className="text-white">{user?.username || 'ผู้ใช้งาน'}</span>
                  </h2>
                  <p className="text-indigo-300 text-sm">
                    {user?.email || 'อีเมล์ไม่ระบุ'}
                  </p>
                </div>
                
                {/* แถบสถานะ */}
                <div className="w-full bg-indigo-900/50 rounded-full h-1.5 mb-1">
                  <div 
                    className="bg-gradient-to-r from-amber-400 to-amber-300 h-1.5 rounded-full" 
                    style={{ width: `${isAdmin ? '100%' : '50%'}` }}
                  ></div>
                </div>
                <p className="text-xs text-indigo-300">
                  {isAdmin ? 'เทพเจ้าสูงสุด' : 'ผู้ใช้งานระดับทั่วไป'}
                </p>
              </div>
            </div>

            {/* Mobile-only user description */}
            {isMobileMenuOpen && (
              <div className="lg:hidden mb-4 text-center">
                <p className="text-xs text-indigo-300 bg-indigo-900/30 py-1 px-3 rounded-md inline-block">
                  {user?.email || 'อีเมล์ไม่ระบุ'}
                </p>
              </div>
            )}
            
            {/* Menu Links */}
            <div className="space-y-5 max-h-[calc(100vh-210px)] lg:max-h-[calc(100vh-350px)] overflow-y-auto pr-1 scrollbar-thin">
              {/* แสดงเมนูตามหมวดหมู่ */}
              {!location.startsWith('/admin') && userMenuGroups.map((group, idx) => renderMenuLinks(group, idx))}

              {/* แสดงลิงก์แอดมินถ้าเป็นแอดมิน */}
              {isAdmin && renderAdminLinks()}
            </div>

            {/* ปุ่มออกจากระบบ */}
            <div className="mt-6 px-1">
              <motion.button 
                whileHover={{ scale: 1.02 }}
                className="w-full flex items-center justify-center px-4 py-3 bg-gradient-to-r from-red-900/40 to-red-800/20 rounded-xl text-red-300 hover:text-red-200 transition-colors hover:shadow-lg hover:shadow-red-900/20"
                onClick={handleLogout}
              >
                <div className="mr-2">
                  <LogOut className="h-4 w-4" />
                </div>
                <span className="font-medium">ออกจากระบบ</span>
              </motion.button>
            </div>
          </motion.aside>
        )}
      </AnimatePresence>

      {/* เนื้อหาหลัก - ด้านขวา */}
      <div className="flex-1 flex flex-col overflow-x-hidden">
        {/* Floating Quick Action Button - แสดงเฉพาะบนมือถือและเมนูปิดอยู่ */}
        {!isMobileMenuOpen && (
          <div className="lg:hidden fixed bottom-6 right-6 z-40">
            <button 
              onClick={() => setIsMobileMenuOpen(true)}
              className="w-14 h-14 flex items-center justify-center rounded-full bg-gradient-to-r from-amber-500 to-amber-400 text-indigo-950 shadow-lg shadow-amber-900/20"
            >
              <motion.div 
                animate={{ rotate: [0, 15, 0, -15, 0] }}
                transition={{ 
                  repeat: Infinity, 
                  repeatDelay: 5,
                  duration: 0.7,
                }}
              >
                <LayoutDashboard size={24} />
              </motion.div>
            </button>
          </div>
        )}
        
        {/* Main Content Area */}
        <main className="flex-1 p-4 sm:p-6 lg:p-8 overflow-y-auto relative">
          <div className="relative z-10">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}