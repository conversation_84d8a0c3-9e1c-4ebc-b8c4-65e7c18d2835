import React, { useState, useRef, useEffect } from 'react';
import { DashboardLayout } from "@/components/layouts/dashboard-layout-new";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Avatar,
  AvatarFallback,
  AvatarImage
} from "@/components/ui/avatar";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { motion, AnimatePresence } from "framer-motion";
import { User } from "@shared/schema";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  User as UserIcon,
  Mail,
  Phone,
  Building,
  MapPin,
  Upload,
  Sparkles,
  Star,
  SunMoon,
  Moon,
  Sun,
  CloudLightning,
  Zap,
  Crown,
  Image,
  X,
  Loader2,
  CheckCircle,
  Clock,
  AlertCircle,
  SendIcon,
  KeyIcon
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose
} from "@/components/ui/dialog";
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/components/ui/alert";

const profileSchema = z.object({
  firstName: z.string().min(2, "ชื่อต้องมีอย่างน้อย 2 ตัวอักษร").optional(),
  lastName: z.string().min(2, "นามสกุลต้องมีอย่างน้อย 2 ตัวอักษร").optional(),
  email: z.string().email("อีเมลไม่ถูกต้อง"),
  phoneNumber: z.string().optional(),
  company: z.string().optional(),
  address: z.string().optional(),
  bio: z.string().optional(),
});

type ProfileFormValues = z.infer<typeof profileSchema>;

// ประเภทข้อมูลสำหรับอวตารเริ่มต้น
type DefaultAvatar = {
  id: number;
  name: string;
  path: string;
};

export default function ProfilePage() {
  const { user } = useAuth();
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [showAvatarSelector, setShowAvatarSelector] = useState(false);
  const [selectedAvatarId, setSelectedAvatarId] = useState<number | null>(null);

  // Verification states
  const [emailVerifyMode, setEmailVerifyMode] = useState<'send' | 'verify' | null>(null);
  const [phoneVerifyMode, setPhoneVerifyMode] = useState<'send' | 'verify' | null>(null);
  const [emailCode, setEmailCode] = useState('');
  const [phoneCode, setPhoneCode] = useState('');
  const [otpRef, setOtpRef] = useState('');
  const [isLoadingEmail, setIsLoadingEmail] = useState(false);
  const [isLoadingPhone, setIsLoadingPhone] = useState(false);

  // สถานะการยืนยัน
  const { data: verificationStatus, isLoading: isLoadingStatus, refetch: refetchVerificationStatus } = useQuery({
    queryKey: ['/api/verify/status'],
    queryFn: async () => {
      const res = await fetch('/api/verify/status');
      if (!res.ok) throw new Error('ไม่สามารถตรวจสอบสถานะการยืนยันได้');
      return res.json();
    }
  });

  // ดึงข้อมูลอวตารเริ่มต้น
  const { data: defaultAvatars = [], isLoading: isLoadingAvatars } = useQuery<DefaultAvatar[]>({
    queryKey: ['/api/user/default-avatars'],
    queryFn: async () => {
      const res = await fetch('/api/user/default-avatars');
      if (!res.ok) throw new Error('ไม่สามารถดึงข้อมูลอวตารเริ่มต้นได้');
      return res.json();
    }
  });

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      firstName: user?.firstName || "",
      lastName: user?.lastName || "",
      email: user?.email || "",
      phoneNumber: user?.phoneNumber || "",
      company: user?.companyName || "",
      address: user?.address || "",
      bio: user?.bio || "",
    },
  });

  const updateProfileMutation = useMutation({
    mutationFn: async (data: ProfileFormValues) => {
      const res = await apiRequest("PATCH", "/api/user/profile", data);
      return await res.json();
    },
    onSuccess: (data: User) => {
      queryClient.setQueryData(["/api/user"], data);
      toast({
        title: "อัปเดตโปรไฟล์สำเร็จ",
        description: "ข้อมูลส่วนตัวของคุณถูกบันทึกเรียบร้อยแล้ว",
      });
    },
    onError: (error: any) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message || "ไม่สามารถอัปเดตโปรไฟล์ได้ กรุณาลองใหม่อีกครั้ง",
        variant: "destructive",
      });
    }
  });

  // ส่งรหัสยืนยันอีเมล
  const sendEmailVerificationMutation = useMutation({
    mutationFn: async (email: string) => {
      const res = await apiRequest("POST", "/api/verify/email/send", { email, type: "email" });
      return await res.json();
    },
    onSuccess: () => {
      setEmailVerifyMode('verify');
      setIsLoadingEmail(false);
      toast({
        title: "ส่งรหัสยืนยันสำเร็จ",
        description: "กรุณาตรวจสอบกล่องจดหมายของคุณและกรอกรหัสยืนยัน",
      });
    },
    onError: (error: any) => {
      setIsLoadingEmail(false);
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message || "ไม่สามารถส่งรหัสยืนยันได้ กรุณาลองใหม่อีกครั้ง",
        variant: "destructive",
      });
    }
  });

  // ตรวจสอบรหัสยืนยันอีเมล
  const verifyEmailMutation = useMutation({
    mutationFn: async ({ email, code }: { email: string, code: string }) => {
      const res = await apiRequest("POST", "/api/verify/email", { email, code, type: "email" });
      return await res.json();
    },
    onSuccess: (data) => {
      setIsLoadingEmail(false);
      setEmailVerifyMode(null);
      setEmailCode('');

      if (data.verified) {
        toast({
          title: "ยืนยันอีเมลสำเร็จ",
          description: "อีเมลของคุณได้รับการยืนยันเรียบร้อยแล้ว",
        });
        refetchVerificationStatus();
      } else {
        toast({
          title: "ยืนยันอีเมลไม่สำเร็จ",
          description: data.message || "กรุณาตรวจสอบรหัสยืนยันและลองใหม่อีกครั้ง",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      setIsLoadingEmail(false);
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message || "ไม่สามารถยืนยันรหัสได้ กรุณาลองใหม่อีกครั้ง",
        variant: "destructive",
      });
    }
  });

  // ส่ง OTP ไปยังเบอร์โทรศัพท์
  const sendPhoneOTPMutation = useMutation({
    mutationFn: async (phoneNumber: string) => {
      const res = await apiRequest("POST", "/api/verify/phone/send", { phoneNumber });
      return await res.json();
    },
    onSuccess: (data) => {
      setPhoneVerifyMode('verify');
      setIsLoadingPhone(false);
      setOtpRef(data.otpRef || '');

      toast({
        title: "ส่ง OTP สำเร็จ",
        description: "กรุณาตรวจสอบ SMS บนโทรศัพท์ของคุณและกรอกรหัส OTP",
      });
    },
    onError: (error: any) => {
      setIsLoadingPhone(false);
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message || "ไม่สามารถส่ง OTP ได้ กรุณาลองใหม่อีกครั้ง",
        variant: "destructive",
      });
    }
  });

  // ตรวจสอบ OTP
  const verifyPhoneMutation = useMutation({
    mutationFn: async ({ phoneNumber, code, otpRef }: { phoneNumber: string, code: string, otpRef: string }) => {
      const res = await apiRequest("POST", "/api/verify/phone", { phoneNumber, code, otpRef });
      return await res.json();
    },
    onSuccess: (data) => {
      setIsLoadingPhone(false);
      setPhoneVerifyMode(null);
      setPhoneCode('');
      setOtpRef('');

      if (data.verified) {
        toast({
          title: "ยืนยันเบอร์โทรศัพท์สำเร็จ",
          description: "เบอร์โทรศัพท์ของคุณได้รับการยืนยันเรียบร้อยแล้ว",
        });
        refetchVerificationStatus();
      } else {
        toast({
          title: "ยืนยันเบอร์โทรศัพท์ไม่สำเร็จ",
          description: data.message || "กรุณาตรวจสอบรหัส OTP และลองใหม่อีกครั้ง",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      setIsLoadingPhone(false);
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message || "ไม่สามารถยืนยันรหัสได้ กรุณาลองใหม่อีกครั้ง",
        variant: "destructive",
      });
    }
  });

  // อัปโหลดรูปโปรไฟล์
  const uploadProfileImageMutation = useMutation({
    mutationFn: async (file: File) => {
      const formData = new FormData();
      formData.append("profileImage", file);

      const res = await fetch("/api/user/profile-image", {
        method: "POST",
        body: formData,
      });

      if (!res.ok) {
        throw new Error("อัปโหลดรูปโปรไฟล์ล้มเหลว");
      }

      return await res.json();
    },
    onSuccess: (data: { imageUrl: string }) => {
      setUploadedImage(data.imageUrl);
      queryClient.invalidateQueries({ queryKey: ["/api/user"] });

      toast({
        title: "อัปโหลดรูปโปรไฟล์สำเร็จ",
        description: "รูปโปรไฟล์ของคุณถูกอัปเดตเรียบร้อยแล้ว",
      });

      setIsUploading(false);
    },
    onError: (error: any) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message || "ไม่สามารถอัปโหลดรูปโปรไฟล์ได้ กรุณาลองใหม่อีกครั้ง",
        variant: "destructive",
      });

      setIsUploading(false);
    }
  });

  // เลือกอวตารเริ่มต้น
  const selectDefaultAvatarMutation = useMutation({
    mutationFn: async (avatarId: number) => {
      const res = await apiRequest("POST", "/api/user/select-default-avatar", { avatarId });
      return await res.json();
    },
    onSuccess: (data: { imageUrl: string, name: string }) => {
      setUploadedImage(data.imageUrl);
      queryClient.invalidateQueries({ queryKey: ["/api/user"] });

      toast({
        title: "เลือกอวตารสำเร็จ",
        description: `ร่างเทพเจ้า "${data.name}" ถูกอัญเชิญเรียบร้อยแล้ว`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message || "ไม่สามารถเลือกอวตารได้ กรุณาลองใหม่อีกครั้ง",
        variant: "destructive",
      });
    }
  });

  // จัดการเลือกอวตารเริ่มต้น
  function handleSelectDefaultAvatar(avatarId: number) {
    selectDefaultAvatarMutation.mutate(avatarId);
  }

  function onSubmit(data: ProfileFormValues) {
    // ไม่ส่งอีเมลไปอัพเดทเพื่อป้องกันการเปลี่ยนแปลงอีเมล
    const { email, ...dataWithoutEmail } = data;

    // ใช้อีเมลเดิมจาก user object
    const dataToUpdate = {
      ...dataWithoutEmail,
      email: user?.email || email // ใช้อีเมลเดิมจาก user object หรือใช้อีเมลจากฟอร์มถ้าไม่มี user object
    };

    updateProfileMutation.mutate(dataToUpdate);
  }

  // ส่งรหัสยืนยันอีเมล
  function handleSendEmailVerification() {
    // ใช้อีเมลจาก user object แทนการดึงจากฟอร์ม
    const email = user?.email || '';

    if (!email || !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      toast({
        title: "อีเมลไม่ถูกต้อง",
        description: "กรุณากรอกอีเมลที่ถูกต้อง",
        variant: "destructive",
      });
      return;
    }

    setIsLoadingEmail(true);
    sendEmailVerificationMutation.mutate(email);
  }

  // ยืนยันรหัสอีเมล
  function handleVerifyEmail() {
    // ใช้อีเมลจาก user object แทนการดึงจากฟอร์ม
    const email = user?.email || '';

    if (!emailCode || emailCode.length < 6) {
      toast({
        title: "รหัสยืนยันไม่ถูกต้อง",
        description: "กรุณากรอกรหัสยืนยัน 6 หลัก",
        variant: "destructive",
      });
      return;
    }

    setIsLoadingEmail(true);
    verifyEmailMutation.mutate({ email, code: emailCode });
  }

  // ส่ง OTP ไปยังเบอร์โทรศัพท์
  function handleSendPhoneOTP() {
    const phoneNumber = form.getValues('phoneNumber');

    if (!phoneNumber || phoneNumber.length < 10) {
      toast({
        title: "เบอร์โทรศัพท์ไม่ถูกต้อง",
        description: "กรุณากรอกเบอร์โทรศัพท์ที่ถูกต้อง",
        variant: "destructive",
      });
      return;
    }

    setIsLoadingPhone(true);
    sendPhoneOTPMutation.mutate(phoneNumber);
  }

  // ยืนยัน OTP
  function handleVerifyPhone() {
    const phoneNumber = form.getValues('phoneNumber');

    if (!phoneCode || phoneCode.length < 6) {
      toast({
        title: "รหัส OTP ไม่ถูกต้อง",
        description: "กรุณากรอกรหัส OTP 6 หลัก",
        variant: "destructive",
      });
      return;
    }

    if (!otpRef) {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: "ไม่พบข้อมูลอ้างอิง OTP กรุณาส่งรหัส OTP ใหม่อีกครั้ง",
        variant: "destructive",
      });
      return;
    }

    setIsLoadingPhone(true);
    verifyPhoneMutation.mutate({ phoneNumber, code: phoneCode, otpRef });
  }

  function handleImageUpload(event: React.ChangeEvent<HTMLInputElement>) {
    const file = event.target.files?.[0];
    if (!file) return;

    // ขนาดไฟล์สูงสุด 2MB
    if (file.size > 2 * 1024 * 1024) {
      toast({
        title: "ไฟล์มีขนาดใหญ่เกินไป",
        description: "กรุณาเลือกไฟล์ที่มีขนาดไม่เกิน 2MB",
        variant: "destructive",
      });
      return;
    }

    // ตรวจสอบประเภทไฟล์
    if (!file.type.startsWith("image/")) {
      toast({
        title: "ประเภทไฟล์ไม่ถูกต้อง",
        description: "กรุณาเลือกไฟล์รูปภาพเท่านั้น",
        variant: "destructive",
      });
      return;
    }

    setIsUploading(true);

    // สร้าง URL สำหรับแสดงตัวอย่างรูปภาพ
    const previewUrl = URL.createObjectURL(file);
    setUploadedImage(previewUrl);

    // ส่งไฟล์ไปยัง API
    uploadProfileImageMutation.mutate(file);
  }

  // โปรติเคิลเทพเจ้า
  const FloatingParticle = ({ icon: Icon, size, delay, duration, x, y }: {
    icon: any,
    size: number,
    delay: number,
    duration: number,
    x: number,
    y: number
  }) => {
    return (
      <motion.div
        initial={{ opacity: 0, x, y }}
        animate={{
          opacity: [0, 1, 0],
          x: x + Math.random() * 100 - 50,
          y: y - 100 - Math.random() * 100,
          scale: [0.5, 1, 0.5],
          rotate: [0, 360]
        }}
        transition={{
          delay,
          duration,
          repeat: Infinity,
          repeatType: "loop"
        }}
        className="absolute pointer-events-none"
      >
        <Icon size={size} className="text-amber-300/30" />
      </motion.div>
    );
  };

  // Radial progress แบบเทพเจ้า
  const CelestialRadialProgress = ({ value, label }: { value: number, label: string }) => {
    const circleRef = useRef<SVGCircleElement>(null);
    const circumference = 2 * Math.PI * 40;

    useEffect(() => {
      if (circleRef.current) {
        const offset = circumference - (value / 100) * circumference;
        circleRef.current.style.strokeDashoffset = offset.toString();
      }
    }, [value, circumference]);

    return (
      <div className="relative flex flex-col items-center">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <span className="text-amber-300 text-2xl font-bold">{value}%</span>
            <p className="text-indigo-300 text-xs mt-1">{label}</p>
          </div>
        </div>
        <svg className="w-28 h-28 transform -rotate-90" viewBox="0 0 100 100">
          {/* คลื่นรัศมีด้านนอก */}
          <circle
            cx="50"
            cy="50"
            r="45"
            fill="none"
            stroke="rgba(124, 58, 237, 0.1)"
            strokeWidth="1"
            className="animate-pulse-slow"
          />
          {/* พื้นหลัง */}
          <circle
            cx="50"
            cy="50"
            r="40"
            fill="none"
            stroke="rgba(79, 70, 229, 0.2)"
            strokeWidth="4"
          />
          {/* ตัวชี้ค่า */}
          <circle
            ref={circleRef}
            cx="50"
            cy="50"
            r="40"
            fill="none"
            stroke="url(#celestialGradient)"
            strokeWidth="4"
            strokeDasharray={circumference}
            strokeDashoffset={circumference}
            strokeLinecap="round"
          />
          {/* ชุดดาวตกแต่ง */}
          <circle cx="50" cy="10" r="1" fill="#f3c677" className="animate-twinkle-slow" />
          <circle cx="85" cy="30" r="0.7" fill="#f3c677" className="animate-twinkle-medium" />
          <circle cx="20" cy="75" r="0.5" fill="#f3c677" className="animate-twinkle-fast" />
          <defs>
            <linearGradient id="celestialGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#f59e0b" />
              <stop offset="100%" stopColor="#fcd34d" />
            </linearGradient>
          </defs>
        </svg>
      </div>
    );
  };

  // อิโมติคอนเทพเจ้า
  const DeityEmoji = ({ emoji, x, y, scale = 1, className = "" }: {
    emoji: string,
    x: number,
    y: number,
    scale?: number,
    className?: string
  }) => {
    return (
      <motion.div
        className={`absolute text-3xl ${className}`}
        style={{ left: `${x}%`, top: `${y}%`, transform: `scale(${scale})` }}
        animate={{ y: [0, -10, 0], opacity: [0.7, 1, 0.7] }}
        transition={{
          duration: 3 + Math.random() * 2,
          repeat: Infinity,
          repeatType: "mirror"
        }}
      >
        {emoji}
      </motion.div>
    );
  };

  return (
    <DashboardLayout>
      {/* เอฟเฟกต์เทพเจ้าลอยตลอดหน้า */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <FloatingParticle icon={Star} size={24} delay={0} duration={15} x={100} y={300} />
        <FloatingParticle icon={Star} size={16} delay={5} duration={20} x={500} y={400} />
        <FloatingParticle icon={SunMoon} size={30} delay={2} duration={25} x={200} y={500} />
        <FloatingParticle icon={CloudLightning} size={24} delay={8} duration={18} x={700} y={300} />
        <FloatingParticle icon={Zap} size={20} delay={12} duration={22} x={300} y={200} />
        <FloatingParticle icon={Crown} size={18} delay={16} duration={24} x={600} y={100} />
      </div>

      <div className="container mx-auto max-w-5xl px-4 py-6 relative">
        {/* อิโมจิเทพเจ้าลอยอยู่รอบ ๆ */}
        <DeityEmoji emoji="✨" x={10} y={20} scale={0.8} />
        <DeityEmoji emoji="⚡" x={90} y={15} scale={0.9} />
        <DeityEmoji emoji="🌟" x={5} y={70} scale={1.1} />
        <DeityEmoji emoji="🔮" x={85} y={80} scale={1} />
        <DeityEmoji emoji="👑" x={50} y={5} scale={0.7} />

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex flex-col gap-8">
            <div className="flex items-center justify-between">
              <div>
                <motion.h1
                  className="text-4xl font-bold tracking-tight mb-1 bg-clip-text text-transparent bg-gradient-to-r from-amber-200 via-amber-400 to-amber-300"
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3, duration: 0.7 }}
                >
                  ตั้งค่าการแสดงตนของเทพเจ้า
                </motion.h1>
                <motion.p
                  className="text-indigo-300 text-lg"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5, duration: 0.7 }}
                >
                  ปรับแต่งการปรากฏตัวของท่านในโลกแห่งมนุษย์
                </motion.p>
              </div>

              <motion.div
                initial={{ opacity: 0, scale: 0.8, rotate: -10 }}
                animate={{
                  opacity: 1,
                  scale: 1,
                  rotate: 0,
                  y: [0, -5, 0]
                }}
                transition={{
                  delay: 0.2,
                  duration: 0.8,
                  y: {
                    duration: 2,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }
                }}
                className="h-24 w-24 rounded-full bg-gradient-to-r from-indigo-800 to-purple-900 flex items-center justify-center relative overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-radial from-purple-600/30 via-indigo-900/20 to-transparent"></div>
                <motion.div
                  animate={{
                    rotate: 360,
                    scale: [1, 1.1, 1]
                  }}
                  transition={{
                    rotate: { duration: 20, repeat: Infinity, ease: "linear" },
                    scale: { duration: 3, repeat: Infinity, repeatType: "reverse" }
                  }}
                  className="absolute inset-0 opacity-30"
                >
                  <div className="w-full h-full rounded-full border-4 border-dashed border-amber-300"></div>
                </motion.div>
                <motion.div
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 30, repeat: Infinity, ease: "linear" }}
                >
                  <Crown className="h-12 w-12 text-amber-300" />
                </motion.div>
                <div className="absolute inset-0 bg-gradient-to-t from-indigo-800/80 via-indigo-900/20 to-transparent"></div>

                {/* รัศมีเรืองรองรอบไอคอน */}
                <div className="absolute -inset-1 rounded-full bg-amber-400/20 blur-md animate-pulse-slow"></div>
              </motion.div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* โปรไฟล์และรูปภาพ */}
              <Card className="bg-indigo-950/50 border-amber-500/20 overflow-hidden relative">
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-900/30 via-purple-900/20 to-indigo-950/30 pointer-events-none"></div>

                {/* ลวดลายดาวฝังในพื้นหลัง */}
                <div className="absolute inset-0 overflow-hidden opacity-10">
                  <div className="absolute h-1 w-1 bg-amber-300 rounded-full top-[20%] left-[30%] animate-twinkle-slow"></div>
                  <div className="absolute h-1.5 w-1.5 bg-amber-300 rounded-full top-[40%] left-[85%] animate-twinkle-medium"></div>
                  <div className="absolute h-0.5 w-0.5 bg-white rounded-full top-[70%] left-[65%] animate-twinkle-fast"></div>
                  <div className="absolute h-0.5 w-0.5 bg-white rounded-full top-[15%] left-[50%] animate-twinkle-medium"></div>
                  <div className="absolute h-2 w-2 bg-amber-200 rounded-full top-[80%] left-[20%] animate-twinkle-slow"></div>
                  <div className="absolute h-0.5 w-0.5 bg-amber-100 rounded-full top-[60%] left-[40%] animate-twinkle-fast"></div>
                </div>

                <CardHeader className="relative z-10">
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3, duration: 0.5 }}
                  >
                    <CardTitle className="flex items-center text-transparent bg-clip-text bg-gradient-to-r from-amber-200 via-amber-400 to-amber-300 text-xl tracking-wide">
                      <motion.div
                        animate={{ rotate: [0, 10, -10, 0] }}
                        transition={{ duration: 5, repeat: Infinity }}
                      >
                        <Sparkles className="h-6 w-6 mr-2 text-amber-300" />
                      </motion.div>
                      ภาพลักษณ์เทพเจ้า
                    </CardTitle>
                    <CardDescription className="text-indigo-200 mt-1">
                      อัญเชิญภาพลักษณ์ของท่านจากสวรรค์สู่โลกมนุษย์
                    </CardDescription>
                  </motion.div>
                </CardHeader>

                <CardContent className="flex flex-col items-center relative">
                  {/* วงกลมพลังงานเทพเจ้า */}
                  <motion.div
                    className="absolute top-14 w-full h-40 pointer-events-none"
                    animate={{ rotate: 360 }}
                    transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                  >
                    <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-40 h-40 rounded-full border-2 border-dashed border-amber-300/20"></div>
                  </motion.div>

                  <motion.div
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{
                      type: "spring",
                      stiffness: 100,
                      delay: 0.2
                    }}
                    className="relative group my-6 z-10"
                  >
                    {/* แสงสว่างด้านบนของรูป */}
                    <motion.div
                      className="absolute -top-6 left-1/2 -translate-x-1/2 w-20 h-20 bg-amber-300/20 blur-xl rounded-full"
                      animate={{ opacity: [0.4, 0.8, 0.4] }}
                      transition={{ duration: 3, repeat: Infinity }}
                    />

                    <motion.div
                      className={`h-40 w-40 rounded-full overflow-hidden relative ${isUploading ? 'animate-pulse' : ''}`}
                      animate={{
                        y: [0, -5, 0],
                        boxShadow: [
                          "0 0 20px 5px rgba(251, 191, 36, 0.3)",
                          "0 0 25px 8px rgba(251, 191, 36, 0.5)",
                          "0 0 20px 5px rgba(251, 191, 36, 0.3)"
                        ]
                      }}
                      transition={{
                        y: { duration: 3, repeat: Infinity },
                        boxShadow: { duration: 2, repeat: Infinity }
                      }}
                    >
                      {/* ออร่าด้านหลังรูป */}
                      <div className="absolute -inset-1 bg-gradient-to-r from-amber-500/30 to-purple-600/30 rounded-full blur-md"></div>

                      {/* ลวดลายรอบรูป */}
                      <motion.div
                        className="absolute inset-0 rounded-full border-4 border-amber-400/40"
                        animate={{ scale: [1, 1.05, 1] }}
                        transition={{ duration: 3, repeat: Infinity }}
                      />

                      <motion.div
                        className="absolute inset-0 rounded-full border border-amber-200/20"
                        animate={{ scale: [1, 1.15, 1] }}
                        transition={{ duration: 5, repeat: Infinity }}
                      />

                      <div className="absolute inset-0 bg-gradient-to-br from-indigo-700 to-purple-900 z-0"></div>

                      <Avatar className="h-full w-full relative z-10">
                        {uploadedImage || user?.profileImage ? (
                          <AvatarImage
                            src={uploadedImage || (user?.profileImage || '')}
                            alt="รูปโปรไฟล์"
                            className="h-full w-full object-cover"
                          />
                        ) : (
                          <AvatarFallback className="bg-gradient-to-r from-indigo-800 to-purple-900 text-amber-300 text-5xl h-full w-full flex items-center justify-center">
                            {user?.firstName?.[0] || user?.username?.[0] || 'เทพ'}
                          </AvatarFallback>
                        )}
                      </Avatar>

                      {/* รังสีทองคำเรืองรอง */}
                      <motion.div
                        className="absolute -inset-1 rounded-full bg-gradient-to-r from-amber-400/20 via-yellow-300/10 to-amber-400/20 blur-md z-20"
                        animate={{ opacity: [0.4, 0.7, 0.4] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                    </motion.div>

                    {/* ปุ่มอัพโหลดที่มีแอนิเมชัน */}
                    <motion.label
                      htmlFor="profile-image-upload"
                      className="absolute -bottom-2 -right-2 h-12 w-12 bg-gradient-to-r from-amber-400 to-amber-500 rounded-full p-2.5 cursor-pointer border-2 border-indigo-900 shadow-lg flex items-center justify-center z-20"
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      whileTap={{ scale: 0.95 }}
                      animate={{
                        boxShadow: [
                          "0 0 8px 2px rgba(251, 191, 36, 0.5)",
                          "0 0 12px 4px rgba(251, 191, 36, 0.7)",
                          "0 0 8px 2px rgba(251, 191, 36, 0.5)"
                        ]
                      }}
                      transition={{
                        boxShadow: { duration: 2, repeat: Infinity }
                      }}
                    >
                      <Upload className="h-5 w-5 text-indigo-900" />
                    </motion.label>

                    <input
                      id="profile-image-upload"
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={handleImageUpload}
                      disabled={isUploading}
                    />

                    {/* ปุ่มเลือกอวตารเริ่มต้น */}
                    <Dialog>
                      <DialogTrigger asChild>
                        <motion.button
                          type="button"
                          className="absolute -bottom-3 -left-3 bg-gradient-to-r from-amber-400 to-amber-600 w-10 h-10 rounded-full flex items-center justify-center shadow-lg z-20"
                          whileHover={{
                            scale: 1.1,
                            boxShadow: "0 0 15px 3px rgba(251, 191, 36, 0.4)"
                          }}
                          whileTap={{ scale: 0.95 }}
                          title="เลือกอวตารเทพเจ้า"
                        >
                          <Crown className="h-5 w-5 text-indigo-900" />
                        </motion.button>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-md bg-indigo-950 border border-amber-500/30">
                        <DialogHeader>
                          <DialogTitle className="text-amber-300 flex items-center gap-2">
                            <Crown className="h-5 w-5" />
                            <span>อัญเชิญร่างเทพเจ้า</span>
                          </DialogTitle>
                          <DialogDescription className="text-indigo-300">
                            เลือกร่างทรงที่เหมาะสมกับพลังของท่าน
                          </DialogDescription>
                        </DialogHeader>

                        {isLoadingAvatars ? (
                          <div className="min-h-40 flex items-center justify-center">
                            <div className="h-10 w-10 rounded-full border-4 border-t-amber-400 border-r-amber-400/30 border-b-amber-400/10 border-l-amber-400/50 animate-spin"></div>
                          </div>
                        ) : (
                          <div className="grid grid-cols-3 sm:grid-cols-5 gap-3 mt-3 pt-3 border-t border-indigo-700/50">
                            {defaultAvatars.map((avatar) => (
                              <div
                                key={avatar.id}
                                className={`relative cursor-pointer group ${selectedAvatarId === avatar.id ? 'ring-2 ring-amber-400' : ''}`}
                                onClick={() => setSelectedAvatarId(avatar.id)}
                              >
                                <div className="overflow-hidden rounded-lg aspect-square">
                                  <img
                                    src={avatar.path || `/uploads/default-avatars/avatar-${avatar.id}.webp`}
                                    alt={avatar.name}
                                    className="w-full h-full object-cover transition-transform group-hover:scale-110"
                                    onError={(e) => {
                                      console.log(`Failed to load image: ${avatar.path}`);
                                      const target = e.target as HTMLImageElement;
                                      target.src = `/uploads/default-avatars/avatar-${avatar.id}.webp`;
                                    }}
                                  />
                                </div>
                                <div className="absolute inset-0 flex items-center justify-center bg-indigo-900/60 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg">
                                  <span className="text-amber-300 text-xs font-medium text-center px-1">{avatar.name}</span>
                                </div>
                                {selectedAvatarId === avatar.id && (
                                  <div className="absolute top-1 right-1 bg-amber-400 rounded-full w-4 h-4 flex items-center justify-center">
                                    <span className="text-indigo-900 text-[8px]">✓</span>
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        )}

                        <div className="flex justify-end gap-3 mt-4 pt-3 border-t border-indigo-700/50">
                          <DialogClose asChild>
                            <Button
                              variant="outline"
                              className="border-amber-500/30 text-amber-100 hover:bg-indigo-800 hover:text-amber-200"
                            >
                              <X className="h-4 w-4 mr-2" />
                              ยกเลิก
                            </Button>
                          </DialogClose>
                          <DialogClose asChild>
                            <Button
                              type="button"
                              className="bg-gradient-to-r from-amber-500 to-amber-600 text-indigo-950 hover:from-amber-400 hover:to-amber-500"
                              disabled={selectedAvatarId === null}
                              onClick={() => {
                                if (selectedAvatarId !== null) {
                                  handleSelectDefaultAvatar(selectedAvatarId);
                                }
                              }}
                            >
                              <Crown className="h-4 w-4 mr-2" />
                              เลือกร่างเทพเจ้า
                            </Button>
                          </DialogClose>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                    className="text-center mt-2 relative z-10"
                  >
                    <h3 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-amber-200 via-amber-300 to-amber-200">
                      {user?.firstName ? `${user.firstName} ${user.lastName}` : user?.username}
                    </h3>
                    <p className="text-indigo-200 text-sm mt-1">{user?.email}</p>

                    {/* แสดงสถานะเทพเจ้า */}
                    <div className="mt-4 flex items-center justify-center gap-3">
                      <motion.div
                        className="flex items-center bg-indigo-900/50 px-3 py-1 rounded-full border border-indigo-700/50"
                        whileHover={{ scale: 1.05 }}
                      >
                        <Crown className="h-4 w-4 text-amber-300 mr-1" />
                        <span className="text-xs font-medium text-amber-200">เทพเจ้าระดับสูง</span>
                      </motion.div>

                      <motion.div
                        className="flex items-center bg-indigo-900/50 px-3 py-1 rounded-full border border-indigo-700/50"
                        whileHover={{ scale: 1.05 }}
                      >
                        <Sparkles className="h-4 w-4 text-indigo-300 mr-1" />
                        <span className="text-xs font-medium text-indigo-200">พลังเต็มเปี่ยม</span>
                      </motion.div>
                    </div>
                  </motion.div>

                  {/* ตัวชี้วัดพลังเทพเจ้าแบบ Celestial */}
                  <div className="mt-6 grid grid-cols-3 gap-2 w-full">
                    <CelestialRadialProgress value={98} label="พลังเทพเจ้า" />
                    <CelestialRadialProgress value={85} label="อิทธิฤทธิ์" />
                    <CelestialRadialProgress value={92} label="เวทย์มนตร์" />
                  </div>
                </CardContent>
              </Card>

              {/* ข้อมูลส่วนตัว */}
              <Card className="md:col-span-2 bg-indigo-950/50 border-indigo-800/50 overflow-hidden relative">
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-900/30 via-purple-900/20 to-indigo-950/30 pointer-events-none"></div>

                <CardHeader>
                  <CardTitle className="flex items-center text-amber-300">
                    <UserIcon className="h-5 w-5 mr-2" /> ข้อมูลส่วนตัว
                  </CardTitle>
                  <CardDescription className="text-indigo-300">
                    อัปเดตข้อมูลส่วนตัวของคุณ
                  </CardDescription>
                </CardHeader>

                <CardContent>
                  <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="firstName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="bg-gradient-to-r from-cyan-300 via-blue-400 to-purple-400 text-transparent bg-clip-text font-bold">ชื่อจริง</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="ชื่อจริง"
                                  {...field}
                                  className="bg-indigo-900/40 border-blue-600/50 focus:border-cyan-400 focus:ring-cyan-400/50 shadow-inner shadow-blue-500/10 text-cyan-100 placeholder:text-indigo-400"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="lastName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="bg-gradient-to-r from-fuchsia-300 via-purple-400 to-indigo-400 text-transparent bg-clip-text font-bold">นามสกุล</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="นามสกุล"
                                  {...field}
                                  className="bg-indigo-900/40 border-purple-600/50 focus:border-fuchsia-400 focus:ring-fuchsia-400/50 shadow-inner shadow-purple-500/10 text-purple-100 placeholder:text-indigo-400"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="bg-gradient-to-r from-green-300 via-teal-400 to-cyan-400 text-transparent bg-clip-text font-bold">อีเมล</FormLabel>
                            <FormControl>
                              <div className="relative">
                                <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-teal-300" />
                                <Input
                                  placeholder="อีเมล"
                                  {...field}
                                  disabled={true}
                                  title="ไม่สามารถเปลี่ยนอีเมลได้เนื่องจากมีผลต่อการยืนยันตัวตน"
                                  className="bg-indigo-900/40 border-teal-600/50 focus:border-green-400 focus:ring-green-400/50 shadow-inner shadow-teal-500/10 text-teal-100 placeholder:text-indigo-400 pl-10 cursor-not-allowed opacity-80"
                                />
                              </div>
                            </FormControl>
                            <FormDescription className="text-amber-300/80 text-xs mt-1 flex items-center">
                              <AlertCircle className="h-3 w-3 mr-1" />
                              ไม่สามารถเปลี่ยนอีเมลได้เนื่องจากมีผลต่อการยืนยันตัวตน
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="phoneNumber"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="bg-gradient-to-r from-amber-300 via-orange-400 to-rose-400 text-transparent bg-clip-text font-bold">เบอร์โทรศัพท์</FormLabel>
                            <FormControl>
                              <div className="relative">
                                <Phone className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-orange-300" />
                                <Input
                                  placeholder="เบอร์โทรศัพท์"
                                  {...field}
                                  className="bg-indigo-900/40 border-orange-600/50 focus:border-amber-400 focus:ring-amber-400/50 shadow-inner shadow-orange-500/10 text-amber-100 placeholder:text-indigo-400 pl-10"
                                />
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="company"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="bg-gradient-to-r from-sky-300 via-blue-400 to-indigo-400 text-transparent bg-clip-text font-bold">บริษัท/องค์กร</FormLabel>
                              <FormControl>
                                <div className="relative">
                                  <Building className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-blue-300" />
                                  <Input
                                    placeholder="บริษัท/องค์กร"
                                    {...field}
                                    className="bg-indigo-900/40 border-blue-600/50 focus:border-sky-400 focus:ring-sky-400/50 shadow-inner shadow-blue-500/10 text-blue-100 placeholder:text-indigo-400 pl-10"
                                  />
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="address"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="bg-gradient-to-r from-pink-300 via-purple-400 to-indigo-400 text-transparent bg-clip-text font-bold">ที่อยู่</FormLabel>
                              <FormControl>
                                <div className="relative">
                                  <MapPin className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-pink-300" />
                                  <Input
                                    placeholder="ที่อยู่"
                                    {...field}
                                    className="bg-indigo-900/40 border-pink-600/50 focus:border-fuchsia-400 focus:ring-fuchsia-400/50 shadow-inner shadow-pink-500/10 text-pink-100 placeholder:text-indigo-400 pl-10"
                                  />
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={form.control}
                        name="bio"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="bg-gradient-to-r from-violet-300 via-indigo-400 to-blue-400 text-transparent bg-clip-text font-bold">เกี่ยวกับคุณ</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="เขียนเกี่ยวกับตัวคุณสั้นๆ"
                                {...field}
                                className="bg-indigo-900/40 border-violet-600/50 focus:border-blue-400 focus:ring-blue-400/50 shadow-inner shadow-violet-500/10 text-blue-100 placeholder:text-indigo-400 min-h-[100px]"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="flex justify-end">
                        <Button
                          type="submit"
                          disabled={updateProfileMutation.isPending}
                          className="relative bg-gradient-to-br from-amber-400 to-orange-600 hover:from-amber-500 hover:to-orange-700
                            text-indigo-950 font-semibold text-lg px-6 py-2 shadow-lg shadow-orange-600/30
                            overflow-hidden transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-orange-600/40
                            after:content-[''] after:absolute after:inset-0 after:bg-gradient-to-r after:from-transparent
                            after:via-white/20 after:to-transparent after:translate-x-[-200%] after:hover:translate-x-[200%]
                            after:transition-all after:duration-1000 after:ease-in-out"
                        >
                          {updateProfileMutation.isPending ?
                            <>
                              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              กำลังบันทึก...
                            </> :
                            "✨ บันทึกการเปลี่ยนแปลง"}
                        </Button>
                      </div>
                    </form>
                  </Form>
                </CardContent>
              </Card>

              {/* การยืนยันตัวตน */}
              <Card className="md:col-span-2 bg-indigo-950/50 border-amber-500/20 overflow-hidden relative">
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-900/30 via-purple-900/20 to-indigo-950/30 pointer-events-none"></div>

                {/* ลวดลายดาวฝังในพื้นหลัง */}
                <div className="absolute inset-0 overflow-hidden opacity-10">
                  <div className="absolute h-1 w-1 bg-amber-300 rounded-full top-[30%] left-[20%] animate-twinkle-slow"></div>
                  <div className="absolute h-1.5 w-1.5 bg-amber-300 rounded-full top-[60%] left-[75%] animate-twinkle-medium"></div>
                  <div className="absolute h-0.5 w-0.5 bg-white rounded-full top-[40%] left-[85%] animate-twinkle-fast"></div>
                  <div className="absolute h-0.5 w-0.5 bg-white rounded-full top-[25%] left-[60%] animate-twinkle-medium"></div>
                  <div className="absolute h-2 w-2 bg-amber-200 rounded-full top-[70%] left-[40%] animate-twinkle-slow"></div>
                  <div className="absolute h-0.5 w-0.5 bg-amber-100 rounded-full top-[10%] left-[30%] animate-twinkle-fast"></div>
                </div>

                <CardHeader className="relative z-10">
                  <CardTitle className="bg-clip-text text-transparent bg-gradient-to-r from-amber-200 to-amber-400">
                    การยืนยันตัวตน
                  </CardTitle>
                  <CardDescription className="text-indigo-300">
                    ยืนยันตัวตนของท่านผ่านหลายช่องทาง
                  </CardDescription>
                </CardHeader>

                <CardContent className="relative z-10 space-y-6">
                  {isLoadingStatus ? (
                    <div className="flex justify-center items-center p-6">
                      <Loader2 className="h-8 w-8 animate-spin text-amber-400" />
                    </div>
                  ) : (
                    <>
                      {/* ยืนยันอีเมล */}
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <Mail className="h-5 w-5 text-amber-300" />
                            <h3 className="text-lg font-medium text-indigo-100">ยืนยันอีเมล</h3>
                          </div>

                          <div>
                            {verificationStatus?.email_verified ? (
                              <div className="flex items-center text-green-400 gap-1.5">
                                <CheckCircle className="h-5 w-5" />
                                <span className="text-sm">ยืนยันแล้ว</span>
                              </div>
                            ) : (
                              <div className="flex items-center text-amber-400 gap-1.5">
                                <Clock className="h-5 w-5" />
                                <span className="text-sm">ยังไม่ยืนยัน</span>
                              </div>
                            )}
                          </div>
                        </div>

                        {!verificationStatus?.email_verified && (
                          <div className="rounded-lg bg-indigo-900/30 p-4">
                            <p className="text-indigo-200 mb-4">
                              ยืนยันอีเมลเพื่อเพิ่มความปลอดภัยให้บัญชีของท่าน
                            </p>

                            {emailVerifyMode === 'send' && (
                              <div className="space-y-3">
                                <p className="text-indigo-300 text-sm">ส่งรหัสยืนยันไปยัง: {form.getValues('email')}</p>
                                <Button
                                  className="w-full bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-indigo-950"
                                  onClick={handleSendEmailVerification}
                                  disabled={isLoadingEmail}
                                >
                                  {isLoadingEmail ? (
                                    <><Loader2 className="h-4 w-4 mr-2 animate-spin" /> กำลังส่งรหัส...</>
                                  ) : (
                                    <><SendIcon className="h-4 w-4 mr-2" /> ส่งรหัสยืนยัน</>
                                  )}
                                </Button>
                                <Button
                                  variant="ghost"
                                  className="w-full text-indigo-300 hover:text-indigo-100 hover:bg-indigo-800/30"
                                  onClick={() => setEmailVerifyMode(null)}
                                  disabled={isLoadingEmail}
                                >
                                  ยกเลิก
                                </Button>
                              </div>
                            )}

                            {emailVerifyMode === 'verify' && (
                              <div className="space-y-3">
                                <div className="space-y-2">
                                  <Label htmlFor="email-code" className="text-indigo-300">รหัสยืนยัน (6 หลัก)</Label>
                                  <Input
                                    id="email-code"
                                    type="text"
                                    maxLength={6}
                                    className="bg-indigo-950/50 border-amber-500/30 text-amber-100 placeholder-indigo-600"
                                    placeholder="กรอกรหัส 6 หลัก"
                                    value={emailCode}
                                    onChange={(e) => setEmailCode(e.target.value)}
                                  />
                                </div>
                                <Button
                                  className="w-full bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-indigo-950"
                                  onClick={handleVerifyEmail}
                                  disabled={isLoadingEmail || emailCode.length < 6}
                                >
                                  {isLoadingEmail ? (
                                    <><Loader2 className="h-4 w-4 mr-2 animate-spin" /> กำลังตรวจสอบ...</>
                                  ) : (
                                    <><KeyIcon className="h-4 w-4 mr-2" /> ยืนยันรหัส</>
                                  )}
                                </Button>
                                <div className="flex space-x-2">
                                  <Button
                                    variant="ghost"
                                    className="flex-1 text-indigo-300 hover:text-indigo-100 hover:bg-indigo-800/30"
                                    onClick={() => setEmailVerifyMode('send')}
                                    disabled={isLoadingEmail}
                                  >
                                    ส่งรหัสใหม่
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    className="flex-1 text-indigo-300 hover:text-indigo-100 hover:bg-indigo-800/30"
                                    onClick={() => setEmailVerifyMode(null)}
                                    disabled={isLoadingEmail}
                                  >
                                    ยกเลิก
                                  </Button>
                                </div>
                              </div>
                            )}

                            {!emailVerifyMode && (
                              <Button
                                className="w-full bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-indigo-950"
                                onClick={() => setEmailVerifyMode('send')}
                              >
                                <Mail className="h-4 w-4 mr-2" /> ยืนยันอีเมลของท่าน
                              </Button>
                            )}
                          </div>
                        )}

                        {verificationStatus?.email_verified && (
                          <Alert className="bg-green-950/30 border-green-500/30">
                            <CheckCircle className="h-4 w-4 text-green-400" />
                            <AlertTitle className="text-green-200">อีเมลยืนยันแล้ว</AlertTitle>
                            <AlertDescription className="text-green-300">
                              อีเมล {verificationStatus.email} ได้รับการยืนยันเรียบร้อยแล้ว
                            </AlertDescription>
                          </Alert>
                        )}
                      </div>

                      {/* ยืนยันเบอร์โทรศัพท์ */}
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <Phone className="h-5 w-5 text-amber-300" />
                            <h3 className="text-lg font-medium text-indigo-100">ยืนยันเบอร์โทรศัพท์</h3>
                          </div>

                          <div>
                            {verificationStatus?.phone_verified ? (
                              <div className="flex items-center text-green-400 gap-1.5">
                                <CheckCircle className="h-5 w-5" />
                                <span className="text-sm">ยืนยันแล้ว</span>
                              </div>
                            ) : (
                              <div className="flex items-center text-amber-400 gap-1.5">
                                <Clock className="h-5 w-5" />
                                <span className="text-sm">ยังไม่ยืนยัน</span>
                              </div>
                            )}
                          </div>
                        </div>

                        {!verificationStatus?.phone_verified && (
                          <div className="rounded-lg bg-indigo-900/30 p-4">
                            <p className="text-indigo-200 mb-4">
                              ยืนยันเบอร์โทรศัพท์เพื่อใช้ฟีเจอร์การยืนยันผ่าน SMS และปลดล็อคความสามารถเพิ่มเติม
                            </p>

                            {phoneVerifyMode === 'send' && (
                              <div className="space-y-3">
                                <p className="text-indigo-300 text-sm">ส่ง OTP ไปยัง: {form.getValues('phoneNumber')}</p>
                                <Button
                                  className="w-full bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-indigo-950"
                                  onClick={handleSendPhoneOTP}
                                  disabled={isLoadingPhone}
                                >
                                  {isLoadingPhone ? (
                                    <><Loader2 className="h-4 w-4 mr-2 animate-spin" /> กำลังส่ง OTP...</>
                                  ) : (
                                    <><SendIcon className="h-4 w-4 mr-2" /> ส่ง OTP</>
                                  )}
                                </Button>
                                <Button
                                  variant="ghost"
                                  className="w-full text-indigo-300 hover:text-indigo-100 hover:bg-indigo-800/30"
                                  onClick={() => setPhoneVerifyMode(null)}
                                  disabled={isLoadingPhone}
                                >
                                  ยกเลิก
                                </Button>
                              </div>
                            )}

                            {phoneVerifyMode === 'verify' && (
                              <div className="space-y-3">
                                <div className="space-y-2">
                                  <Label htmlFor="phone-code" className="text-indigo-300">รหัส OTP (6 หลัก)</Label>
                                  <Input
                                    id="phone-code"
                                    type="text"
                                    maxLength={6}
                                    className="bg-indigo-950/50 border-amber-500/30 text-amber-100 placeholder-indigo-600"
                                    placeholder="กรอกรหัส OTP 6 หลัก"
                                    value={phoneCode}
                                    onChange={(e) => setPhoneCode(e.target.value)}
                                  />
                                </div>
                                <Button
                                  className="w-full bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-indigo-950"
                                  onClick={handleVerifyPhone}
                                  disabled={isLoadingPhone || phoneCode.length < 6}
                                >
                                  {isLoadingPhone ? (
                                    <><Loader2 className="h-4 w-4 mr-2 animate-spin" /> กำลังตรวจสอบ...</>
                                  ) : (
                                    <><KeyIcon className="h-4 w-4 mr-2" /> ยืนยันรหัส OTP</>
                                  )}
                                </Button>
                                <div className="flex space-x-2">
                                  <Button
                                    variant="ghost"
                                    className="flex-1 text-indigo-300 hover:text-indigo-100 hover:bg-indigo-800/30"
                                    onClick={() => setPhoneVerifyMode('send')}
                                    disabled={isLoadingPhone}
                                  >
                                    ส่ง OTP ใหม่
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    className="flex-1 text-indigo-300 hover:text-indigo-100 hover:bg-indigo-800/30"
                                    onClick={() => setPhoneVerifyMode(null)}
                                    disabled={isLoadingPhone}
                                  >
                                    ยกเลิก
                                  </Button>
                                </div>
                              </div>
                            )}

                            {!phoneVerifyMode && (
                              <Button
                                className="w-full bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-indigo-950"
                                onClick={() => setPhoneVerifyMode('send')}
                              >
                                <Phone className="h-4 w-4 mr-2" /> ยืนยันเบอร์โทรศัพท์ของท่าน
                              </Button>
                            )}
                          </div>
                        )}

                        {verificationStatus?.phone_verified && (
                          <Alert className="bg-green-950/30 border-green-500/30">
                            <CheckCircle className="h-4 w-4 text-green-400" />
                            <AlertTitle className="text-green-200">เบอร์โทรศัพท์ยืนยันแล้ว</AlertTitle>
                            <AlertDescription className="text-green-300">
                              เบอร์โทรศัพท์ {verificationStatus.phone} ได้รับการยืนยันเรียบร้อยแล้ว
                            </AlertDescription>
                          </Alert>
                        )}
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </motion.div>
      </div>
    </DashboardLayout>
  );
}