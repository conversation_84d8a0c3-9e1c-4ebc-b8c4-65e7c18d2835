# SLIPKUY AI Guide - คู่มือการพัฒนาสำหรับ AI

คู่มือนี้ออกแบบมาเพื่อช่วยให้ AI เข้าใจโครงสร้างและวิธีการทำงานของโปรเจค SLIPKUY เพื่อให้สามารถพัฒนาระบบต่อไปได้อย่างราบรื่น

## ภาพรวมโปรเจค

SLIPKUY เป็นแพลตฟอร์มตรวจสอบสลิปที่ทำหน้าที่เป็น middleware API service ระหว่างผู้ใช้กับบริการ slip2go โดยมีฟีเจอร์สำคัญ:
- ตรวจสอบภาพสลิปโอนเงินด้วย API
- จัดการผู้ใช้และระบบสมาชิก
- ระบบแพ็คเกจและเครดิต
- ระบบจัดการ API keys
- ระบบค้นหาและจัดการประวัติการใช้งาน
- ส่วนจัดการสำหรับแอดมิน

## โครงสร้างโปรเจค

โปรเจคนี้ใช้สถาปัตยกรรม 3-tier ที่แยกส่วนหน้าจอ, ส่วนตรรกะ, และฐานข้อมูลออกจากกัน:

```
slipkuy/
├── client/          # ส่วนหน้าบ้าน (React + Tailwind CSS)
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── lib/            # ไลบรารีและฟังก์ชันช่วยเหลือ
│   │   ├── hooks/          # React custom hooks
│   │   └── pages/          # หน้าหลักของแอปพลิเคชัน
├── server/          # ส่วนหลังบ้าน (Express + TypeScript)
│   ├── api/              # API endpoints
│   ├── db.ts             # การเชื่อมต่อฐานข้อมูล
│   ├── auth.ts           # ระบบยืนยันตัวตน
│   ├── routes.ts         # การกำหนดเส้นทาง
│   ├── storage.ts        # ฟังก์ชันสำหรับจัดการข้อมูล
│   ├── slip-service.ts   # บริการตรวจสอบสลิป
│   └── index.ts          # จุดเริ่มต้นของเซิร์ฟเวอร์
├── shared/          # โค้ดที่ใช้ร่วมกันระหว่าง client และ server
│   └── schema.ts         # นิยามแบบจำลองข้อมูลและ schema สำหรับ Drizzle ORM
├── public/          # ไฟล์สาธารณะ
│   └── uploads/          # ไฟล์อัพโหลด (รูปภาพสลิป, โปรไฟล์ ฯลฯ)
└── backup/          # ไฟล์แบคอัพฐานข้อมูลและไฟล์อัพโหลด
```

## เทคโนโลยีที่ใช้

โปรเจคนี้ใช้เทคโนโลยีดังนี้:

1. **Front-end**:
   - React
   - Tailwind CSS และ shadcn/ui
   - Wouter สำหรับเส้นทาง (routing)
   - TanStack Query สำหรับการจัดการสถานะและการเรียก API
   - TypeScript

2. **Back-end**:
   - Node.js + Express
   - TypeScript
   - Drizzle ORM สำหรับจัดการฐานข้อมูล
   - Passport.js สำหรับการพิสูจน์ตัวตน
   - Zod สำหรับการตรวจสอบความถูกต้องของข้อมูล

3. **Database**:
   - PostgreSQL

4. **Others**:
   - Vite สำหรับการพัฒนาและการสร้าง
   - SendGrid หรือ SMTP สำหรับอีเมล
   - Stripe (อนาคต) สำหรับการชำระเงิน

## โครงสร้างฐานข้อมูล

แบบจำลองข้อมูลหลักมีดังนี้:

1. **users**: ข้อมูลผู้ใช้งาน
2. **packages**: แพ็คเกจที่ระบบให้บริการ
3. **userPackages**: แพ็คเกจที่ผู้ใช้สมัคร
4. **apiKeys**: API keys ที่ผู้ใช้สร้าง
5. **apiLogs**: บันทึกการใช้งาน API
6. **slipVerifications**: ประวัติการตรวจสอบสลิป
7. **transactions**: ประวัติการทำธุรกรรม (เติมเงิน/ใช้เครดิต)
8. **coupons**: คูปองส่วนลด
9. **systemSettings**: การตั้งค่าระบบ

นิยามแบบจำลองข้อมูลทั้งหมดอยู่ในไฟล์ `shared/schema.ts` ซึ่งใช้ Drizzle ORM

## การเชื่อมต่อกับบริการภายนอก

- **slip2go API**: บริการหลักสำหรับตรวจสอบสลิป (ต้องใช้ API key จริง)
- **Easy Slip API**: บริการสำรองสำหรับตรวจสอบสลิป (ต้องใช้ API key จริง)
- **SMTP/SendGrid**: สำหรับส่งอีเมล (configurable)
- **Perplexity API**: สำหรับฟีเจอร์ AI (ถ้ามี)

## ระบบการทำงานสำคัญ

### 1. การตรวจสอบสลิป

1. **เส้นทางรับคำขอ**: 
   - เว็บไซต์: `POST /api/verify-slip`
   - API: `POST /api/v1/verify-slip`

2. **การประมวลผล**:
   - รับรูปภาพสลิป (รองรับไฟล์ .jpg, .jpeg, .png)
   - ส่งต่อไปยัง slip2go API หรือ Easy Slip API
   - รับผลลัพธ์และแปลงให้อยู่ในรูปแบบมาตรฐานของ SLIPKUY
   - บันทึกประวัติการตรวจสอบและภาพสลิป
   - หักเครดิตจากบัญชีผู้ใช้หรือแพ็คเกจ

3. **รูปแบบการตอบกลับ**:
   ```json
   {
     "code": "200000",
     "message": "เสร็จสมบูรณ์",
     "data": {
       "transactionRef": "xyz123",
       "date": "2025-02-18T15:30:45Z",
       "amount": 500.00,
       "sender": {...},
       "receiver": {...}
     }
   }
   ```

### 2. ระบบสมาชิกและแพ็คเกจ

1. **การสมัครแพ็คเกจ** (`POST /api/user/subscribe`):
   - ตรวจสอบสิทธิ์การสมัครแพ็คเกจ
   - ตรวจสอบเครดิตและการชำระเงิน
   - คำนวณส่วนลดตามระยะเวลาและคูปอง
   - สร้างแพ็คเกจสำหรับผู้ใช้

2. **การรีเซ็ทโควต้า**:
   - ทุกเดือนสำหรับแพ็คเกจที่มีระยะเวลามากกว่า 1 เดือน
   - API: `POST /api/admin/users/:userId/packages/:id/reset-quota`

### 3. ระบบ API Keys

1. **การสร้าง API Key** (`POST /api/user/api-keys`):
   - สร้าง API key เป็นสตริงแบบสุ่ม 64 ตัวอักษร
   - บันทึกข้อมูล API key (ชื่อ, ข้อจำกัด, วันหมดอายุ)
   - ส่งคืนค่า API key เต็มเพียงครั้งเดียว

2. **การยืนยัน API Key** (ใช้ใน middleware):
   - ตรวจสอบ header `X-API-KEY`
   - ตรวจสอบสถานะและข้อจำกัดการใช้งาน
   - ตรวจสอบเครดิตและแพ็คเกจที่เหลือ

### 4. ระบบแอดมิน

1. **การจัดการผู้ใช้**:
   - สร้าง/แก้ไข/ลบผู้ใช้
   - เพิ่ม/ลดเครดิต
   - จำกัดการเข้าถึงแพ็คเกจ

2. **การจัดการแพ็คเกจ**:
   - สร้าง/แก้ไข/ลบแพ็คเกจ
   - กำหนดราคาและส่วนลด
   - กำหนดโควต้าและเงื่อนไข

3. **การติดตามการใช้งาน**:
   - ดูประวัติการตรวจสอบสลิป
   - ดูประวัติการใช้งาน API
   - ดูสถิติและรายงาน

## หลักการการเขียนโค้ด

1. **การแก้ไขข้อมูลในฐานข้อมูล**:
   - ใช้ฟังก์ชันของ Drizzle ORM ผ่าน `storage.ts`
   - ไม่เขียน SQL หรือทำ raw queries โดยตรง
   - Migrations ควรทำผ่าน Drizzle ORM

2. **การจัดการข้อผิดพลาด**:
   - ใช้ try-catch เพื่อดักจับข้อผิดพลาด
   - คืนค่า HTTP status code ที่เหมาะสม (400, 401, 403, 404, 500)
   - บันทึกข้อผิดพลาดที่สำคัญใน console

3. **การยืนยันตัวตน**:
   - ใช้ middleware `isAuthenticated` สำหรับเส้นทางที่ต้องล็อกอิน
   - ใช้ middleware `isAdmin` สำหรับเส้นทางของผู้ดูแลระบบ

4. **การตรวจสอบความถูกต้องของข้อมูล**:
   - ใช้ Zod schemas จาก `drizzle-zod`
   - ตรวจสอบข้อมูลก่อนบันทึกลงฐานข้อมูลเสมอ

## การแก้ไขและพัฒนาต่อยอด

### การเพิ่ม API Endpoint ใหม่

1. **สร้าง endpoint ใน server/api/*.ts**:
   ```typescript
   // ตัวอย่าง: server/api/new-feature.ts
   export function setupNewFeatureRoutes(app: Express) {
     app.get("/api/new-feature", isAuthenticated, async (req, res) => {
       try {
         // ตรรกะการทำงาน
         res.json({ result: "success" });
       } catch (error) {
         console.error("Error:", error);
         res.status(500).json({ error: "Internal server error" });
       }
     });
   }
   ```

2. **นำเข้าและลงทะเบียนใน server/routes.ts**:
   ```typescript
   import { setupNewFeatureRoutes } from "./api/new-feature";
   
   export async function registerRoutes(app: Express) {
     // เพิ่มบรรทัดนี้
     setupNewFeatureRoutes(app);
     
     // โค้ดอื่นๆ ที่มีอยู่แล้ว
   }
   ```

### การเพิ่ม feature ใหม่ในฝั่ง frontend

1. **สร้าง component ใหม่**:
   ```tsx
   // ตัวอย่าง: client/src/components/new-feature.tsx
   export function NewFeatureComponent() {
     const { data, isLoading } = useQuery({
       queryKey: ['/api/new-feature'],
       queryFn: getQueryFn()
     });
     
     if (isLoading) return <div>Loading...</div>;
     
     return (
       <div>
         {/* UI ของ feature ใหม่ */}
       </div>
     );
   }
   ```

2. **สร้างหน้าใหม่**:
   ```tsx
   // ตัวอย่าง: client/src/pages/new-feature-page.tsx
   import { NewFeatureComponent } from "@/components/new-feature";
   
   export default function NewFeaturePage() {
     return (
       <div>
         <h1>Feature ใหม่</h1>
         <NewFeatureComponent />
       </div>
     );
   }
   ```

3. **เพิ่มเส้นทางใน client/src/App.tsx**:
   ```tsx
   import NewFeaturePage from "@/pages/new-feature-page";
   
   function Router() {
     return (
       <Switch>
         {/* เส้นทางที่มีอยู่แล้ว */}
         <ProtectedRoute path="/new-feature" component={NewFeaturePage} />
         <Route component={NotFound} />
       </Switch>
     );
   }
   ```

### การเพิ่มตารางใหม่ในฐานข้อมูล

1. **เพิ่มตารางใน shared/schema.ts**:
   ```typescript
   import { ...existing imports... } from "drizzle-orm/pg-core";
   
   // เพิ่มนิยามตาราง
   export const newFeatureTable = pgTable("new_feature", {
     id: serial("id").primaryKey(),
     name: text("name").notNull(),
     userId: integer("user_id").references(() => users.id),
     createdAt: timestamp("created_at").defaultNow(),
     updatedAt: timestamp("updated_at").defaultNow()
   });
   
   // เพิ่ม type สำหรับตาราง
   export type NewFeature = typeof newFeatureTable.$inferSelect;
   export type InsertNewFeature = typeof newFeatureTable.$inferInsert;
   
   // เพิ่ม Zod schema สำหรับการตรวจสอบข้อมูล
   export const insertNewFeatureSchema = createInsertSchema(newFeatureTable);
   ```

2. **เพิ่มฟังก์ชันใน storage.ts**:
   ```typescript
   import { newFeatureTable, type NewFeature, type InsertNewFeature } from "@shared/schema";
   
   // เพิ่มใน interface IStorage
   interface IStorage {
     // existing methods...
     getNewFeature(id: number): Promise<NewFeature | undefined>;
     listNewFeatures(userId: number): Promise<NewFeature[]>;
     createNewFeature(data: InsertNewFeature): Promise<NewFeature>;
     // ...
   }
   
   // เพิ่มการ implement ใน DatabaseStorage
   class DatabaseStorage implements IStorage {
     // existing methods...
     
     async getNewFeature(id: number): Promise<NewFeature | undefined> {
       const [result] = await db
         .select()
         .from(newFeatureTable)
         .where(eq(newFeatureTable.id, id));
       return result;
     }
     
     async listNewFeatures(userId: number): Promise<NewFeature[]> {
       return await db
         .select()
         .from(newFeatureTable)
         .where(eq(newFeatureTable.userId, userId));
     }
     
     async createNewFeature(data: InsertNewFeature): Promise<NewFeature> {
       const [result] = await db
         .insert(newFeatureTable)
         .values(data)
         .returning();
       return result;
     }
     
     // ...
   }
   ```

3. **อัพเดทฐานข้อมูล**:
   ```bash
   npm run db:push
   ```

## ข้อควรระวังและ Pitfalls

1. **API Keys**:
   - ระบบจะแสดง API key เต็มเพียงครั้งเดียวตอนสร้างหรือ regenerate
   - ในหน้าจอจะแสดงเพียงบางส่วนของ API key เท่านั้น

2. **การเครดิต**:
   - ใช้ฟังก์ชัน `addCredits` และ `deductCredits` ใน `storage.ts` เสมอ
   - ตรวจสอบเครดิตคงเหลือก่อนทำธุรกรรม

3. **แพ็คเกจฟรี**:
   - ระบบไม่อนุญาตให้ผู้ใช้สมัครแพ็คเกจฟรีซ้ำ
   - ตรวจสอบผ่าน `getUserPreviousPackagesByType`

4. **รหัสผ่าน**:
   - ใช้ฟังก์ชัน `hashPassword` ใน `auth.ts` เสมอ
   - ไม่ควรจัดเก็บหรือส่งรหัสผ่านเป็นข้อความธรรมดา

5. **การจัดการไฟล์**:
   - ไฟล์อัพโหลดควรอยู่ใน `public/uploads` เท่านั้น
   - บันทึก path ของไฟล์ในฐานข้อมูล ไม่ใช่ไฟล์เอง

## การรันระบบ

1. **โหมดพัฒนา**:
   ```bash
   npm run dev
   ```

2. **โหมดการผลิต**:
   ```bash
   npm run build
   npm start
   ```

## เอกสารอ้างอิง

1. **ดูไฟล์ backup/README.md** สำหรับวิธีการนำเข้าข้อมูล
2. **ดูไฟล์ README.md** สำหรับคำแนะนำการติดตั้งโดยละเอียด

---

หวังว่าคู่มือนี้จะช่วยให้ AI สามารถเข้าใจโครงสร้างและการทำงานของโปรเจค SLIPKUY และพัฒนาต่อยอดได้อย่างมีประสิทธิภาพ!