import { Router } from 'express';
import * as fraudDetectionController from './fraud-detection';

const router = Router();

// สำหรับผู้ดูแลระบบ
router.get('/admin/rules', fraudDetectionController.getFraudRules);
router.get('/admin/rules/:id', fraudDetectionController.getFraudRuleById);
router.post('/admin/rules', fraudDetectionController.createFraudRule);
router.put('/admin/rules/:id', fraudDetectionController.updateFraudRule);
router.delete('/admin/rules/:id', fraudDetectionController.deleteFraudRule);

router.get('/admin/detections', fraudDetectionController.getFraudDetections);
router.put('/admin/detections/:id/status', fraudDetectionController.updateFraudDetectionStatus);

// สำหรับผู้ใช้ทั่วไป
router.get('/', fraudDetectionController.getUserFraudDetections);

export default router;