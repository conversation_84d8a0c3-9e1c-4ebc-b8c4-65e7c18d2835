// script to clean temporary files from tmp/uploads folder
const fs = require('fs');
const path = require('path');

// Define the temporary uploads directory
const tmpUploadsDir = path.join(process.cwd(), 'tmp', 'uploads');

// Check if the directory exists
if (fs.existsSync(tmpUploadsDir)) {
  console.log('Cleaning temporary files from:', tmpUploadsDir);
  
  // Read directory contents
  const files = fs.readdirSync(tmpUploadsDir);
  
  // Print the number of files found
  console.log(`Found ${files.length} files in temporary directory`);
  
  // Delete each file
  let deletedCount = 0;
  let errorCount = 0;
  
  for (const file of files) {
    const filePath = path.join(tmpUploadsDir, file);
    
    try {
      // Get file stats to check if it's a file (not a directory)
      const stats = fs.statSync(filePath);
      
      if (stats.isFile()) {
        // Delete the file
        fs.unlinkSync(filePath);
        deletedCount++;
        console.log(`Deleted: ${filePath}`);
      }
    } catch (error) {
      console.error(`Error deleting ${filePath}: ${error.message}`);
      errorCount++;
    }
  }
  
  console.log(`Operation complete: ${deletedCount} files deleted, ${errorCount} errors`);
} else {
  console.log('Temporary uploads directory does not exist:', tmpUploadsDir);
}
