import React, { useState, useRef, useEffect } from 'react';
import { DashboardLayout } from "@/components/layouts/dashboard-layout-new";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, <PERSON><PERSON>ooter, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input"; 
import { Label } from "@/components/ui/label";
import { 
  Form, 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage
} from "@/components/ui/form";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { motion, AnimatePresence } from "framer-motion";
import { useMutation } from "@tanstack/react-query";
import { 
  <PERSON>Rou<PERSON>, 
  Shield, 
  Alert<PERSON><PERSON>gle,
  Lock,
  EyeOff,
  Eye,
  Sparkles,
  Flame,
  Star,
  Zap,
  CloudLightning
} from "lucide-react";

const passwordSchema = z.object({
  currentPassword: z.string().min(1, "กรุณากรอกรหัสผ่านปัจจุบัน"),
  newPassword: z.string().min(8, "รหัสผ่านใหม่ต้องมีอย่างน้อย 8 ตัวอักษร"),
  confirmPassword: z.string().min(8, "รหัสผ่านยืนยันต้องมีอย่างน้อย 8 ตัวอักษร"),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "รหัสผ่านยืนยันไม่ตรงกับรหัสผ่านใหม่",
  path: ["confirmPassword"],
});

type PasswordFormValues = z.infer<typeof passwordSchema>;

export default function ChangePasswordPage() {
  const { user } = useAuth();
  const [showCurrentPassword, setShowCurrentPassword] = React.useState(false);
  const [showNewPassword, setShowNewPassword] = React.useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = React.useState(false);

  const form = useForm<PasswordFormValues>({
    resolver: zodResolver(passwordSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  const changePasswordMutation = useMutation({
    mutationFn: async (data: PasswordFormValues) => {
      const res = await apiRequest("POST", "/api/user/change-password", {
        currentPassword: data.currentPassword,
        newPassword: data.newPassword,
      });
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "เปลี่ยนรหัสผ่านสำเร็จ",
        description: "รหัสผ่านของคุณถูกเปลี่ยนเรียบร้อยแล้ว",
      });
      
      form.reset({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      });
    },
    onError: (error: any) => {
      toast({
        title: "เกิดข้อผิดพลาด",
        description: error.message || "ไม่สามารถเปลี่ยนรหัสผ่านได้ กรุณาลองใหม่อีกครั้ง",
        variant: "destructive",
      });
    }
  });

  function onSubmit(data: PasswordFormValues) {
    changePasswordMutation.mutate(data);
  }

  const togglePasswordVisibility = (field: 'current' | 'new' | 'confirm') => {
    if (field === 'current') {
      setShowCurrentPassword(!showCurrentPassword);
    } else if (field === 'new') {
      setShowNewPassword(!showNewPassword);
    } else {
      setShowConfirmPassword(!showConfirmPassword);
    }
  };

  // อนิเมชั่นประกายเทพเจ้า
  const DivineSparks = () => {
    return (
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        {[...Array(15)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute bg-amber-300 rounded-full"
            style={{
              width: Math.random() * 3 + 1 + 'px',
              height: Math.random() * 3 + 1 + 'px',
              left: Math.random() * 100 + '%',
              top: Math.random() * 100 + '%',
            }}
            animate={{
              y: [0, -(20 + Math.random() * 80)],
              x: [0, (Math.random() - 0.5) * 40],
              opacity: [0, 0.8, 0],
              scale: [1, 0.5]
            }}
            transition={{
              duration: 2 + Math.random() * 3,
              repeat: Infinity,
              delay: Math.random() * 5,
              ease: "easeOut"
            }}
          />
        ))}
      </div>
    );
  };

  // ลักษณะคลื่นพลังเทพเจ้า
  const DivineEnergyWave = ({ top, opacity, duration }: { top: string, opacity: number, duration: number }) => {
    return (
      <motion.div
        className="absolute left-0 w-full h-40 pointer-events-none"
        style={{ top }}
        initial={{ opacity: 0 }}
        animate={{ opacity }}
        transition={{ duration: 1 }}
      >
        <motion.div
          className="w-full h-full bg-gradient-to-r from-amber-500/5 via-amber-300/10 to-amber-500/5 rounded-full"
          animate={{ 
            scale: [1, 1.2, 1],
            y: [0, -10, 0]
          }}
          transition={{ 
            duration, 
            repeat: Infinity, 
            ease: "easeInOut" 
          }}
        />
      </motion.div>
    );
  };

  // ตัวชี้วัดความเข้มแข็งของรหัสผ่าน
  const PasswordStrengthIndicator = ({ value }: { value: number }) => {
    const getColor = () => {
      if (value < 30) return "bg-red-500";
      if (value < 60) return "bg-yellow-500";
      return "bg-emerald-500";
    };

    return (
      <div className="relative h-2 w-full bg-indigo-900/70 rounded-full overflow-hidden">
        <motion.div 
          className={`absolute h-full ${getColor()}`}
          initial={{ width: 0 }}
          animate={{ width: `${value}%` }}
          transition={{ type: "spring", stiffness: 100 }}
        />
        <motion.div 
          className="absolute h-full w-full bg-gradient-to-r from-transparent via-white/20 to-transparent"
          animate={{ x: [-200, 200] }}
          transition={{ duration: 1.5, repeat: Infinity }}
        />
      </div>
    );
  };

  return (
    <DashboardLayout>
      <div className="container mx-auto max-w-4xl px-4 py-6 relative">
        {/* พื้นหลังเทพเจ้า */}
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          <div className="absolute w-full h-full bg-gradient-radial from-indigo-900/0 via-purple-900/0 to-indigo-950/0"></div>
          
          <DivineEnergyWave top="10%" opacity={0.3} duration={6} />
          <DivineEnergyWave top="30%" opacity={0.2} duration={7} />
          <DivineEnergyWave top="60%" opacity={0.4} duration={8} />
          
          {/* ดาวพื้นหลัง */}
          <div className="absolute h-1 w-1 bg-amber-200 rounded-full top-[15%] left-[10%] animate-twinkle-slow"></div>
          <div className="absolute h-1.5 w-1.5 bg-amber-300 rounded-full top-[25%] left-[80%] animate-twinkle-medium"></div>
          <div className="absolute h-0.5 w-0.5 bg-white rounded-full top-[45%] left-[25%] animate-twinkle-fast"></div>
          <div className="absolute h-0.5 w-0.5 bg-white rounded-full top-[75%] left-[85%] animate-twinkle-slow"></div>
          <div className="absolute h-2 w-2 bg-amber-200 rounded-full top-[85%] left-[15%] animate-twinkle-medium"></div>
        </div>
        
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="relative z-10"
        >
          <div className="flex flex-col gap-8">
            <div className="flex items-center justify-between">
              <div>
                <motion.h1 
                  className="text-4xl font-bold tracking-tight mb-1 bg-clip-text text-transparent bg-gradient-to-r from-amber-200 via-amber-400 to-amber-300"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3, duration: 0.7 }}
                >
                  เปลี่ยนมนตราแห่งเทพเจ้า
                </motion.h1>
                <motion.p 
                  className="text-indigo-200 text-lg"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.5, duration: 0.7 }}
                >
                  สร้างพลังป้องกันอันทรงอำนาจสำหรับการเข้าถึงสวรรค์ของท่าน
                </motion.p>
              </div>
              
              <motion.div
                initial={{ opacity: 0, scale: 0.8, rotate: -20 }}
                animate={{ 
                  opacity: 1, 
                  scale: 1, 
                  rotate: 0,
                  y: [0, -5, 0]
                }}
                transition={{ 
                  delay: 0.2, 
                  duration: 0.7,
                  y: {
                    duration: 3,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }
                }}
                className="h-24 w-24 rounded-full bg-gradient-to-r from-indigo-800 to-purple-900 flex items-center justify-center relative overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-radial from-purple-600/40 via-indigo-900/20 to-transparent"></div>
                
                {/* วงแหวนหมุนรอบไอคอน */}
                <motion.div
                  className="absolute inset-[-5px] rounded-full border-2 border-dashed border-amber-400/30"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                />
                
                <motion.div
                  animate={{ 
                    scale: [1, 1.1, 1],
                    rotate: [0, 5, -5, 0]
                  }}
                  transition={{ 
                    scale: { duration: 2, repeat: Infinity },
                    rotate: { duration: 5, repeat: Infinity }
                  }}
                >
                  <Shield className="h-10 w-10 text-amber-300" />
                </motion.div>
                
                {/* ประกายรอบไอคอน */}
                <motion.div 
                  className="absolute inset-0 opacity-60 pointer-events-none"
                  animate={{ scale: [0.8, 1.2, 0.8], opacity: [0.3, 0.6, 0.3] }}
                  transition={{ duration: 3, repeat: Infinity }}
                >
                  <div className="w-full h-full flex items-center justify-center">
                    <div className="absolute w-4 h-4 bg-amber-400/40 rounded-full blur-md"></div>
                    <div className="absolute w-8 h-8 bg-amber-300/20 rounded-full blur-md"></div>
                    <div className="absolute w-12 h-12 bg-amber-200/10 rounded-full blur-md"></div>
                  </div>
                </motion.div>
              </motion.div>
            </div>

            <Card className="bg-indigo-950/40 border-amber-500/20 overflow-hidden relative backdrop-blur-sm">
              <DivineSparks />
              
              <div className="absolute inset-0 bg-gradient-to-br from-indigo-900/30 via-purple-900/10 to-indigo-950/20 pointer-events-none"></div>
              
              <CardHeader className="relative z-10">
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <CardTitle className="flex items-center text-transparent bg-clip-text bg-gradient-to-r from-amber-300 via-amber-200 to-amber-300 text-2xl">
                    <motion.div
                      animate={{ 
                        rotate: [0, 10, -10, 0],
                        scale: [1, 1.1, 1]
                      }}
                      transition={{ 
                        rotate: { duration: 5, repeat: Infinity },
                        scale: { duration: 2, repeat: Infinity, repeatType: "reverse" }
                      }}
                      className="mr-2"
                    >
                      <Shield className="h-6 w-6 text-amber-300" />
                    </motion.div>
                    เกราะป้องกันแห่งสวรรค์
                  </CardTitle>
                  <CardDescription className="text-indigo-200 mt-1 text-base">
                    สร้างมนตราลับที่ทรงพลังเพื่อปกป้องประตูสู่อาณาจักรเทพเจ้าของท่าน
                  </CardDescription>
                </motion.div>
              </CardHeader>
              
              <CardContent className="relative z-10">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                  className="p-4 mb-8 rounded-xl bg-gradient-to-r from-amber-500/10 via-amber-300/15 to-amber-500/10 border border-amber-500/30 relative overflow-hidden"
                >
                  {/* เอฟเฟ็กต์ไฟเคลื่อนไหว */}
                  <motion.div 
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-amber-200/10 to-transparent"
                    animate={{ x: [-200, 200] }}
                    transition={{ duration: 3, repeat: Infinity }}
                  />
                  
                  <div className="flex items-start gap-3 relative z-10">
                    <motion.div
                      animate={{ rotate: [0, 10, -10, 0] }}
                      transition={{ duration: 5, repeat: Infinity }}
                    >
                      <Flame className="h-6 w-6 text-amber-400 mt-0.5 shrink-0" />
                    </motion.div>
                    <div>
                      <h4 className="text-amber-200 font-medium text-base flex items-center">
                        คำแนะนำจากเทพผู้พิทักษ์
                        <motion.div
                          animate={{ opacity: [0.7, 1, 0.7] }}
                          transition={{ duration: 2, repeat: Infinity }}
                          className="ml-2"
                        >
                          <Sparkles className="h-4 w-4 text-amber-300" />
                        </motion.div>
                      </h4>
                      <p className="text-base text-indigo-100 mt-2 leading-relaxed">
                        มนตราที่แข็งแกร่งควรประกอบด้วย
                        <span className="text-amber-300 font-semibold"> อักษรเวทย์อย่างน้อย 8 ตัว</span> 
                        ผสมผสานทั้ง<span className="text-amber-300 font-semibold">ตัวอักษรพิมพ์ใหญ่</span>, 
                        <span className="text-amber-300 font-semibold">พิมพ์เล็ก</span>, 
                        <span className="text-amber-300 font-semibold">ตัวเลข</span> และ
                        <span className="text-amber-300 font-semibold">อักขระพิเศษ</span> 
                        เพื่อเพิ่มความแข็งแกร่งของมนตราป้องกัน
                      </p>
                    </div>
                  </div>
                </motion.div>
                
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
                    {/* ช่องกรอกรหัสผ่านปัจจุบัน */}
                    <motion.div
                      initial={{ opacity: 0, x: -30 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.5, duration: 0.5 }}
                    >
                      <FormField
                        control={form.control}
                        name="currentPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-amber-200 text-base font-medium flex items-center">
                              <motion.div
                                animate={{ rotate: [0, -10, 10, 0] }}
                                transition={{ duration: 8, repeat: Infinity }}
                                className="mr-2"
                              >
                                <Lock className="h-5 w-5 text-amber-300" />
                              </motion.div>
                              มนตราปัจจุบัน
                            </FormLabel>
                            <FormControl>
                              <div className="relative group">
                                {/* เอฟเฟกต์รอบอินพุต */}
                                <div className="absolute -inset-0.5 bg-gradient-to-r from-amber-400/20 to-purple-500/20 rounded-md blur opacity-0 group-hover:opacity-100 transition duration-500"></div>
                                
                                <div className="relative">
                                  <CloudLightning className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-amber-400" />
                                  <Input 
                                    type={showCurrentPassword ? "text" : "password"}
                                    placeholder="มนตราเข้าถึงปัจจุบัน" 
                                    {...field} 
                                    className="bg-indigo-900/40 border-indigo-700/50 focus:border-amber-400/50 pl-10 pr-10 py-6 text-lg placeholder:text-indigo-400"
                                  />
                                  <motion.button 
                                    type="button" 
                                    className="absolute right-3 top-1/2 -translate-y-1/2 text-amber-400 hover:text-amber-300"
                                    onClick={() => togglePasswordVisibility('current')}
                                    whileHover={{ scale: 1.1 }}
                                    whileTap={{ scale: 0.95 }}
                                  >
                                    {showCurrentPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                                  </motion.button>
                                </div>
                              </div>
                            </FormControl>
                            <FormMessage className="text-red-400" />
                          </FormItem>
                        )}
                      />
                    </motion.div>
                    
                    {/* ช่องกรอกรหัสผ่านใหม่ */}
                    <motion.div
                      initial={{ opacity: 0, x: -30 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.7, duration: 0.5 }}
                      className="pt-4 border-t border-indigo-800/30"
                    >
                      <FormField
                        control={form.control}
                        name="newPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-amber-200 text-base font-medium flex items-center">
                              <motion.div
                                animate={{ 
                                  scale: [1, 1.1, 1],
                                  rotate: [0, 5, 0, -5, 0] 
                                }}
                                transition={{ duration: 6, repeat: Infinity }}
                                className="mr-2"
                              >
                                <Zap className="h-5 w-5 text-amber-300" />
                              </motion.div>
                              สร้างมนตราใหม่
                            </FormLabel>
                            <FormControl>
                              <div className="relative group">
                                {/* เอฟเฟกต์รอบอินพุต */}
                                <div className="absolute -inset-0.5 bg-gradient-to-r from-amber-400/20 to-purple-500/20 rounded-md blur opacity-0 group-hover:opacity-100 transition duration-500"></div>
                                
                                <div className="relative">
                                  <Star className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-amber-400" />
                                  <Input 
                                    type={showNewPassword ? "text" : "password"}
                                    placeholder="มนตราเวทย์ใหม่" 
                                    {...field} 
                                    className="bg-indigo-900/40 border-indigo-700/50 focus:border-amber-400/50 pl-10 pr-10 py-6 text-lg placeholder:text-indigo-400"
                                  />
                                  <motion.button 
                                    type="button" 
                                    className="absolute right-3 top-1/2 -translate-y-1/2 text-amber-400 hover:text-amber-300"
                                    onClick={() => togglePasswordVisibility('new')}
                                    whileHover={{ scale: 1.1 }}
                                    whileTap={{ scale: 0.95 }}
                                  >
                                    {showNewPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                                  </motion.button>
                                </div>
                              </div>
                            </FormControl>
                            
                            {/* ตัวชี้วัดความแข็งแกร่งของรหัสผ่าน */}
                            <div className="mt-3">
                              <div className="flex items-center justify-between mb-1.5">
                                <span className="text-xs text-indigo-300">ความแข็งแกร่งของมนตรา</span>
                                <motion.span 
                                  className="text-xs text-amber-300"
                                  animate={{ opacity: [0.7, 1, 0.7] }}
                                  transition={{ duration: 2, repeat: Infinity }}
                                >
                                  {field.value.length < 4 ? "อ่อนแอ" : 
                                   field.value.length < 8 ? "ปานกลาง" : 
                                   /[A-Z]/.test(field.value) && /[0-9]/.test(field.value) && /[^A-Za-z0-9]/.test(field.value) ? 
                                   "ทรงพลังสูงสุด" : "แข็งแกร่ง"}
                                </motion.span>
                              </div>
                              <PasswordStrengthIndicator 
                                value={
                                  field.value.length < 4 ? 20 : 
                                  field.value.length < 8 ? 50 : 
                                  /[A-Z]/.test(field.value) && /[0-9]/.test(field.value) && /[^A-Za-z0-9]/.test(field.value) ? 
                                  95 : 75
                                } 
                              />
                            </div>
                            
                            <FormDescription className="text-amber-300/80 text-xs mt-2 flex items-center">
                              <motion.div
                                animate={{ rotate: [0, 360] }}
                                transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                                className="mr-2 opacity-60"
                              >
                                <Sparkles className="h-3 w-3" />
                              </motion.div>
                              อักษรเวทย์ต้องมีอย่างน้อย 8 ตัว
                            </FormDescription>
                            <FormMessage className="text-red-400" />
                          </FormItem>
                        )}
                      />
                    </motion.div>
                    
                    {/* ช่องยืนยันรหัสผ่านใหม่ */}
                    <motion.div
                      initial={{ opacity: 0, x: -30 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.9, duration: 0.5 }}
                    >
                      <FormField
                        control={form.control}
                        name="confirmPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-amber-200 text-base font-medium flex items-center">
                              <motion.div
                                animate={{ 
                                  y: [0, -3, 0],
                                  x: [0, 2, 0, -2, 0],
                                }}
                                transition={{ duration: 4, repeat: Infinity }}
                                className="mr-2"
                              >
                                <Shield className="h-5 w-5 text-amber-300" />
                              </motion.div>
                              ยืนยันมนตราใหม่
                            </FormLabel>
                            <FormControl>
                              <div className="relative group">
                                {/* เอฟเฟกต์รอบอินพุต */}
                                <div className="absolute -inset-0.5 bg-gradient-to-r from-amber-400/20 to-purple-500/20 rounded-md blur opacity-0 group-hover:opacity-100 transition duration-500"></div>
                                
                                <div className="relative">
                                  <Star className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-amber-400" />
                                  <Input 
                                    type={showConfirmPassword ? "text" : "password"}
                                    placeholder="ยืนยันมนตราเวทย์ใหม่" 
                                    {...field} 
                                    className="bg-indigo-900/40 border-indigo-700/50 focus:border-amber-400/50 pl-10 pr-10 py-6 text-lg placeholder:text-indigo-400"
                                  />
                                  <motion.button 
                                    type="button" 
                                    className="absolute right-3 top-1/2 -translate-y-1/2 text-amber-400 hover:text-amber-300"
                                    onClick={() => togglePasswordVisibility('confirm')}
                                    whileHover={{ scale: 1.1 }}
                                    whileTap={{ scale: 0.95 }}
                                  >
                                    {showConfirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                                  </motion.button>
                                </div>
                              </div>
                            </FormControl>
                            <FormMessage className="text-red-400" />
                          </FormItem>
                        )}
                      />
                    </motion.div>
                    
                    {/* ปุ่มบันทึก */}
                    <motion.div 
                      className="flex justify-end pt-4"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 1.1, duration: 0.5 }}
                    >
                      <motion.div
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <Button 
                          type="submit" 
                          disabled={changePasswordMutation.isPending}
                          className="relative overflow-hidden bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-indigo-950 font-semibold py-6 px-8 text-lg"
                        >
                          {/* เอฟเฟกต์ประกายในปุ่ม */}
                          <motion.div 
                            className="absolute inset-0 w-full bg-gradient-to-r from-transparent via-white/20 to-transparent"
                            animate={{ x: [-200, 200] }}
                            transition={{ duration: 2, repeat: Infinity }}
                          />
                          
                          <div className="relative flex items-center">
                            <motion.div
                              animate={{ rotate: [0, 360] }}
                              transition={{ duration: 10, repeat: Infinity, ease: "linear" }}
                              className="mr-2"
                            >
                              <Shield className={`h-5 w-5 ${changePasswordMutation.isPending ? 'text-amber-800' : 'text-indigo-900'}`} />
                            </motion.div>
                            {changePasswordMutation.isPending ? "กำลังปรับเปลี่ยนมนตรา..." : "เปลี่ยนมนตราป้องกัน"}
                          </div>
                        </Button>
                      </motion.div>
                    </motion.div>
                  </form>
                </Form>
              </CardContent>
              
              <div className="absolute bottom-0 right-0 opacity-10 pointer-events-none">
                <Shield className="h-40 w-40 text-amber-300" />
              </div>
            </Card>
          </div>
        </motion.div>
      </div>
    </DashboardLayout>
  );
}