--
-- PostgreSQL database dump
--

-- Dumped from database version 16.8
-- Dumped by pg_dump version 16.5

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: achievement_type_enum; Type: TYPE; Schema: public; Owner: neondb_owner
--

CREATE TYPE public.achievement_type_enum AS ENUM (
    'first_time',
    'milestone',
    'streak',
    'usage',
    'package',
    'special',
    'progress'
);


ALTER TYPE public.achievement_type_enum OWNER TO neondb_owner;

--
-- Name: alert_channel; Type: TYPE; Schema: public; Owner: neondb_owner
--

CREATE TYPE public.alert_channel AS ENUM (
    'email',
    'in_app',
    'line',
    'sms'
);


ALTER TYPE public.alert_channel OWNER TO neondb_owner;

--
-- Name: alert_priority; Type: TYPE; Schema: public; Owner: neondb_owner
--

CREATE TYPE public.alert_priority AS ENUM (
    'low',
    'medium',
    'high',
    'critical'
);


ALTER TYPE public.alert_priority OWNER TO neondb_owner;

--
-- Name: alert_status; Type: TYPE; Schema: public; Owner: neondb_owner
--

CREATE TYPE public.alert_status AS ENUM (
    'pending',
    'sent',
    'failed',
    'read'
);


ALTER TYPE public.alert_status OWNER TO neondb_owner;

--
-- Name: alert_type; Type: TYPE; Schema: public; Owner: neondb_owner
--

CREATE TYPE public.alert_type AS ENUM (
    'unusual_transaction',
    'quota_low',
    'api_key_expiring',
    'fraud_detection',
    'daily_report',
    'weekly_report',
    'monthly_report',
    'system_update',
    'package_recommendation'
);


ALTER TYPE public.alert_type OWNER TO neondb_owner;

--
-- Name: api_key_status; Type: TYPE; Schema: public; Owner: neondb_owner
--

CREATE TYPE public.api_key_status AS ENUM (
    'active',
    'inactive',
    'revoked'
);


ALTER TYPE public.api_key_status OWNER TO neondb_owner;

--
-- Name: api_request_type; Type: TYPE; Schema: public; Owner: neondb_owner
--

CREATE TYPE public.api_request_type AS ENUM (
    'verify_slip',
    'get_usage',
    'get_history'
);


ALTER TYPE public.api_request_type OWNER TO neondb_owner;

--
-- Name: api_response_status; Type: TYPE; Schema: public; Owner: neondb_owner
--

CREATE TYPE public.api_response_status AS ENUM (
    'success',
    'error',
    'invalid_request',
    'quota_exceeded',
    'unauthorized'
);


ALTER TYPE public.api_response_status OWNER TO neondb_owner;

--
-- Name: auth_provider; Type: TYPE; Schema: public; Owner: neondb_owner
--

CREATE TYPE public.auth_provider AS ENUM (
    'line',
    'facebook',
    'google',
    'apple',
    'phone'
);


ALTER TYPE public.auth_provider OWNER TO neondb_owner;

--
-- Name: customer_tier; Type: TYPE; Schema: public; Owner: neondb_owner
--

CREATE TYPE public.customer_tier AS ENUM (
    'standard',
    'premium',
    'vip',
    'enterprise'
);


ALTER TYPE public.customer_tier OWNER TO neondb_owner;

--
-- Name: report_frequency; Type: TYPE; Schema: public; Owner: neondb_owner
--

CREATE TYPE public.report_frequency AS ENUM (
    'daily',
    'weekly',
    'monthly'
);


ALTER TYPE public.report_frequency OWNER TO neondb_owner;

--
-- Name: user_role; Type: TYPE; Schema: public; Owner: neondb_owner
--

CREATE TYPE public.user_role AS ENUM (
    'user',
    'admin'
);


ALTER TYPE public.user_role OWNER TO neondb_owner;

--
-- Name: user_status; Type: TYPE; Schema: public; Owner: neondb_owner
--

CREATE TYPE public.user_status AS ENUM (
    'active',
    'inactive',
    'suspended'
);


ALTER TYPE public.user_status OWNER TO neondb_owner;

--
-- Name: verification_type; Type: TYPE; Schema: public; Owner: neondb_owner
--

CREATE TYPE public.verification_type AS ENUM (
    'email',
    'phone',
    'password_reset',
    'two_factor',
    'account_deletion'
);


ALTER TYPE public.verification_type OWNER TO neondb_owner;

--
-- Name: webhook_event_type; Type: TYPE; Schema: public; Owner: neondb_owner
--

CREATE TYPE public.webhook_event_type AS ENUM (
    'slip_verification',
    'quota_low',
    'credit_low',
    'api_key_expire',
    'package_expire',
    'fraud_detected',
    'system_update'
);


ALTER TYPE public.webhook_event_type OWNER TO neondb_owner;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: achievements; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.achievements (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    description text NOT NULL,
    type public.achievement_type_enum NOT NULL,
    level integer DEFAULT 1 NOT NULL,
    requirement integer DEFAULT 1 NOT NULL,
    points integer DEFAULT 0 NOT NULL,
    "packageId" integer,
    icon character varying(255) DEFAULT 'award'::character varying,
    "sortOrder" integer DEFAULT 0,
    color character varying(50),
    "createdAt" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.achievements OWNER TO neondb_owner;

--
-- Name: achievements_id_seq; Type: SEQUENCE; Schema: public; Owner: neondb_owner
--

CREATE SEQUENCE public.achievements_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.achievements_id_seq OWNER TO neondb_owner;

--
-- Name: achievements_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: neondb_owner
--

ALTER SEQUENCE public.achievements_id_seq OWNED BY public.achievements.id;


--
-- Name: alert_settings; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.alert_settings (
    id integer NOT NULL,
    user_id integer NOT NULL,
    alert_type public.alert_type NOT NULL,
    enabled boolean DEFAULT true NOT NULL,
    channels public.alert_channel[],
    threshold jsonb,
    frequency public.report_frequency,
    time_preference jsonb,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.alert_settings OWNER TO neondb_owner;

--
-- Name: alert_settings_id_seq; Type: SEQUENCE; Schema: public; Owner: neondb_owner
--

CREATE SEQUENCE public.alert_settings_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.alert_settings_id_seq OWNER TO neondb_owner;

--
-- Name: alert_settings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: neondb_owner
--

ALTER SEQUENCE public.alert_settings_id_seq OWNED BY public.alert_settings.id;


--
-- Name: api_keys; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.api_keys (
    id integer NOT NULL,
    user_id integer NOT NULL,
    api_key character varying(64) NOT NULL,
    name text NOT NULL,
    description text,
    status public.api_key_status DEFAULT 'active'::public.api_key_status NOT NULL,
    last_used timestamp without time zone,
    request_count integer DEFAULT 0 NOT NULL,
    ip_whitelist text[],
    expires_at timestamp without time zone,
    limit_enabled boolean DEFAULT false,
    usage_limit integer,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    duplicate_slip_check boolean DEFAULT true NOT NULL
);


ALTER TABLE public.api_keys OWNER TO neondb_owner;

--
-- Name: api_keys_id_seq; Type: SEQUENCE; Schema: public; Owner: neondb_owner
--

CREATE SEQUENCE public.api_keys_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.api_keys_id_seq OWNER TO neondb_owner;

--
-- Name: api_keys_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: neondb_owner
--

ALTER SEQUENCE public.api_keys_id_seq OWNED BY public.api_keys.id;


--
-- Name: api_logs; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.api_logs (
    id integer NOT NULL,
    api_key_id integer NOT NULL,
    request_type public.api_request_type NOT NULL,
    request_data jsonb,
    response_status public.api_response_status NOT NULL,
    response_data jsonb,
    slip_verification_id integer,
    ip_address text NOT NULL,
    user_agent text,
    processing_time integer NOT NULL,
    error_message text,
    created_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.api_logs OWNER TO neondb_owner;

--
-- Name: api_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: neondb_owner
--

CREATE SEQUENCE public.api_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.api_logs_id_seq OWNER TO neondb_owner;

--
-- Name: api_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: neondb_owner
--

ALTER SEQUENCE public.api_logs_id_seq OWNED BY public.api_logs.id;


--
-- Name: api_response_templates; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.api_response_templates (
    id integer NOT NULL,
    status_code integer NOT NULL,
    status_type text NOT NULL,
    message text NOT NULL,
    description text,
    template jsonb NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.api_response_templates OWNER TO neondb_owner;

--
-- Name: api_response_templates_id_seq; Type: SEQUENCE; Schema: public; Owner: neondb_owner
--

CREATE SEQUENCE public.api_response_templates_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.api_response_templates_id_seq OWNER TO neondb_owner;

--
-- Name: api_response_templates_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: neondb_owner
--

ALTER SEQUENCE public.api_response_templates_id_seq OWNED BY public.api_response_templates.id;


--
-- Name: coupons; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.coupons (
    id integer NOT NULL,
    code text NOT NULL,
    discount_percent integer DEFAULT 0,
    discount_amount integer DEFAULT 0,
    max_usage integer DEFAULT 1 NOT NULL,
    usage_count integer DEFAULT 0 NOT NULL,
    start_date timestamp without time zone NOT NULL,
    end_date timestamp without time zone NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.coupons OWNER TO neondb_owner;

--
-- Name: coupons_id_seq; Type: SEQUENCE; Schema: public; Owner: neondb_owner
--

CREATE SEQUENCE public.coupons_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.coupons_id_seq OWNER TO neondb_owner;

--
-- Name: coupons_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: neondb_owner
--

ALTER SEQUENCE public.coupons_id_seq OWNED BY public.coupons.id;


--
-- Name: customer_behavior; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.customer_behavior (
    id integer NOT NULL,
    user_id integer NOT NULL,
    avg_daily_verifications integer DEFAULT 0,
    avg_weekly_verifications integer DEFAULT 0,
    avg_monthly_verifications integer DEFAULT 0,
    peak_usage_time jsonb,
    most_used_bank text,
    avg_transaction_amount double precision,
    last_activity_date timestamp without time zone,
    api_usage_pattern jsonb,
    recommended_package_id integer,
    analysis_date timestamp without time zone DEFAULT now() NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.customer_behavior OWNER TO neondb_owner;

--
-- Name: customer_behavior_id_seq; Type: SEQUENCE; Schema: public; Owner: neondb_owner
--

CREATE SEQUENCE public.customer_behavior_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.customer_behavior_id_seq OWNER TO neondb_owner;

--
-- Name: customer_behavior_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: neondb_owner
--

ALTER SEQUENCE public.customer_behavior_id_seq OWNED BY public.customer_behavior.id;


--
-- Name: email_logs; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.email_logs (
    id integer NOT NULL,
    user_id integer NOT NULL,
    "to" text NOT NULL,
    subject text NOT NULL,
    content text,
    template_id integer,
    status text DEFAULT 'pending'::text NOT NULL,
    error_message text,
    metadata jsonb,
    sent_at timestamp without time zone,
    created_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.email_logs OWNER TO neondb_owner;

--
-- Name: email_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: neondb_owner
--

CREATE SEQUENCE public.email_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.email_logs_id_seq OWNER TO neondb_owner;

--
-- Name: email_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: neondb_owner
--

ALTER SEQUENCE public.email_logs_id_seq OWNED BY public.email_logs.id;


--
-- Name: email_templates; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.email_templates (
    id integer NOT NULL,
    name text NOT NULL,
    subject text NOT NULL,
    html_content text NOT NULL,
    text_content text NOT NULL,
    variables text[],
    is_default boolean DEFAULT false NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.email_templates OWNER TO neondb_owner;

--
-- Name: email_templates_id_seq; Type: SEQUENCE; Schema: public; Owner: neondb_owner
--

CREATE SEQUENCE public.email_templates_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.email_templates_id_seq OWNER TO neondb_owner;

--
-- Name: email_templates_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: neondb_owner
--

ALTER SEQUENCE public.email_templates_id_seq OWNED BY public.email_templates.id;


--
-- Name: external_auth; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.external_auth (
    id integer NOT NULL,
    user_id integer NOT NULL,
    provider public.auth_provider NOT NULL,
    external_id text NOT NULL,
    display_name text,
    profile_image text,
    email text,
    access_token text,
    refresh_token text,
    token_expiry timestamp without time zone,
    metadata jsonb,
    last_login timestamp without time zone,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.external_auth OWNER TO neondb_owner;

--
-- Name: external_auth_id_seq; Type: SEQUENCE; Schema: public; Owner: neondb_owner
--

CREATE SEQUENCE public.external_auth_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.external_auth_id_seq OWNER TO neondb_owner;

--
-- Name: external_auth_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: neondb_owner
--

ALTER SEQUENCE public.external_auth_id_seq OWNED BY public.external_auth.id;


--
-- Name: fraud_detections; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.fraud_detections (
    id integer NOT NULL,
    slip_verification_id integer NOT NULL,
    user_id integer NOT NULL,
    rule_id integer NOT NULL,
    status text DEFAULT 'detected'::text NOT NULL,
    details jsonb,
    reviewed_by integer,
    reviewed_at timestamp without time zone,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.fraud_detections OWNER TO neondb_owner;

--
-- Name: fraud_detections_id_seq; Type: SEQUENCE; Schema: public; Owner: neondb_owner
--

CREATE SEQUENCE public.fraud_detections_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.fraud_detections_id_seq OWNER TO neondb_owner;

--
-- Name: fraud_detections_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: neondb_owner
--

ALTER SEQUENCE public.fraud_detections_id_seq OWNED BY public.fraud_detections.id;


--
-- Name: fraud_rules; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.fraud_rules (
    id integer NOT NULL,
    name text NOT NULL,
    description text,
    rule_type text NOT NULL,
    conditions jsonb NOT NULL,
    action text NOT NULL,
    severity text DEFAULT 'medium'::text NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.fraud_rules OWNER TO neondb_owner;

--
-- Name: fraud_rules_id_seq; Type: SEQUENCE; Schema: public; Owner: neondb_owner
--

CREATE SEQUENCE public.fraud_rules_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.fraud_rules_id_seq OWNER TO neondb_owner;

--
-- Name: fraud_rules_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: neondb_owner
--

ALTER SEQUENCE public.fraud_rules_id_seq OWNED BY public.fraud_rules.id;


--
-- Name: notifications; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.notifications (
    id integer NOT NULL,
    user_id integer NOT NULL,
    title text NOT NULL,
    message text NOT NULL,
    type public.alert_type NOT NULL,
    priority public.alert_priority DEFAULT 'medium'::public.alert_priority NOT NULL,
    status public.alert_status DEFAULT 'pending'::public.alert_status NOT NULL,
    channel public.alert_channel NOT NULL,
    metadata jsonb,
    is_read boolean DEFAULT false NOT NULL,
    read_at timestamp without time zone,
    sent_at timestamp without time zone,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.notifications OWNER TO neondb_owner;

--
-- Name: notifications_id_seq; Type: SEQUENCE; Schema: public; Owner: neondb_owner
--

CREATE SEQUENCE public.notifications_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.notifications_id_seq OWNER TO neondb_owner;

--
-- Name: notifications_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: neondb_owner
--

ALTER SEQUENCE public.notifications_id_seq OWNED BY public.notifications.id;


--
-- Name: packages; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.packages (
    id integer NOT NULL,
    name text NOT NULL,
    description text NOT NULL,
    price integer NOT NULL,
    discount_3_months integer DEFAULT 0,
    discount_6_months integer DEFAULT 0,
    discount_12_months integer DEFAULT 0,
    duration_days integer DEFAULT 30 NOT NULL,
    requests_limit integer NOT NULL,
    credit_per_verification double precision,
    is_active boolean DEFAULT true NOT NULL,
    features text[],
    tag text,
    sort_order integer DEFAULT 0,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.packages OWNER TO neondb_owner;

--
-- Name: packages_id_seq; Type: SEQUENCE; Schema: public; Owner: neondb_owner
--

CREATE SEQUENCE public.packages_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.packages_id_seq OWNER TO neondb_owner;

--
-- Name: packages_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: neondb_owner
--

ALTER SEQUENCE public.packages_id_seq OWNED BY public.packages.id;


--
-- Name: reports; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.reports (
    id integer NOT NULL,
    user_id integer NOT NULL,
    name text NOT NULL,
    type public.report_frequency NOT NULL,
    format text DEFAULT 'html'::text NOT NULL,
    data jsonb,
    start_date timestamp without time zone NOT NULL,
    end_date timestamp without time zone NOT NULL,
    status text DEFAULT 'generated'::text NOT NULL,
    email_log_id integer,
    created_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.reports OWNER TO neondb_owner;

--
-- Name: reports_id_seq; Type: SEQUENCE; Schema: public; Owner: neondb_owner
--

CREATE SEQUENCE public.reports_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.reports_id_seq OWNER TO neondb_owner;

--
-- Name: reports_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: neondb_owner
--

ALTER SEQUENCE public.reports_id_seq OWNED BY public.reports.id;


--
-- Name: session; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.session (
    sid character varying NOT NULL,
    sess json NOT NULL,
    expire timestamp(6) without time zone NOT NULL
);


ALTER TABLE public.session OWNER TO neondb_owner;

--
-- Name: slip_verifications; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.slip_verifications (
    id integer NOT NULL,
    user_id integer NOT NULL,
    transaction_ref text,
    bank_name text,
    amount double precision,
    sender text,
    receiver text,
    transaction_date timestamp without time zone,
    status text DEFAULT 'pending'::text NOT NULL,
    response_data text,
    credit_used double precision,
    used_credit boolean DEFAULT false,
    verification_source text DEFAULT 'web'::text,
    api_key_id integer,
    image_path text,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    qr_data text
);


ALTER TABLE public.slip_verifications OWNER TO neondb_owner;

--
-- Name: slip_verifications_id_seq; Type: SEQUENCE; Schema: public; Owner: neondb_owner
--

CREATE SEQUENCE public.slip_verifications_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.slip_verifications_id_seq OWNER TO neondb_owner;

--
-- Name: slip_verifications_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: neondb_owner
--

ALTER SEQUENCE public.slip_verifications_id_seq OWNED BY public.slip_verifications.id;


--
-- Name: system_settings; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.system_settings (
    id integer NOT NULL,
    key text NOT NULL,
    value text,
    value_json jsonb,
    description text,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.system_settings OWNER TO neondb_owner;

--
-- Name: system_settings_id_seq; Type: SEQUENCE; Schema: public; Owner: neondb_owner
--

CREATE SEQUENCE public.system_settings_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.system_settings_id_seq OWNER TO neondb_owner;

--
-- Name: system_settings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: neondb_owner
--

ALTER SEQUENCE public.system_settings_id_seq OWNED BY public.system_settings.id;


--
-- Name: top_up_transactions; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.top_up_transactions (
    id integer NOT NULL,
    user_id integer NOT NULL,
    amount double precision NOT NULL,
    status text DEFAULT 'pending'::text NOT NULL,
    verification_id integer,
    reference_code text,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.top_up_transactions OWNER TO neondb_owner;

--
-- Name: top_up_transactions_id_seq; Type: SEQUENCE; Schema: public; Owner: neondb_owner
--

CREATE SEQUENCE public.top_up_transactions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.top_up_transactions_id_seq OWNER TO neondb_owner;

--
-- Name: top_up_transactions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: neondb_owner
--

ALTER SEQUENCE public.top_up_transactions_id_seq OWNED BY public.top_up_transactions.id;


--
-- Name: user_achievements; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.user_achievements (
    id integer NOT NULL,
    "userId" integer NOT NULL,
    "achievementId" integer NOT NULL,
    progress integer DEFAULT 0 NOT NULL,
    completed boolean DEFAULT false NOT NULL,
    "completedAt" timestamp with time zone,
    "rewardClaimed" boolean DEFAULT false NOT NULL,
    "lastUpdated" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "createdAt" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.user_achievements OWNER TO neondb_owner;

--
-- Name: user_achievements_id_seq; Type: SEQUENCE; Schema: public; Owner: neondb_owner
--

CREATE SEQUENCE public.user_achievements_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.user_achievements_id_seq OWNER TO neondb_owner;

--
-- Name: user_achievements_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: neondb_owner
--

ALTER SEQUENCE public.user_achievements_id_seq OWNED BY public.user_achievements.id;


--
-- Name: user_auth_logs; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.user_auth_logs (
    id integer NOT NULL,
    user_id integer NOT NULL,
    ip_address text NOT NULL,
    user_agent text,
    auth_method text NOT NULL,
    success boolean DEFAULT false NOT NULL,
    fail_reason text,
    created_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.user_auth_logs OWNER TO neondb_owner;

--
-- Name: user_auth_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: neondb_owner
--

CREATE SEQUENCE public.user_auth_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.user_auth_logs_id_seq OWNER TO neondb_owner;

--
-- Name: user_auth_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: neondb_owner
--

ALTER SEQUENCE public.user_auth_logs_id_seq OWNED BY public.user_auth_logs.id;


--
-- Name: user_packages; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.user_packages (
    id integer NOT NULL,
    user_id integer NOT NULL,
    package_id integer NOT NULL,
    start_date date NOT NULL,
    end_date date NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    requests_used integer DEFAULT 0 NOT NULL,
    last_quota_reset_date date,
    duration_months integer DEFAULT 1 NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.user_packages OWNER TO neondb_owner;

--
-- Name: user_packages_id_seq; Type: SEQUENCE; Schema: public; Owner: neondb_owner
--

CREATE SEQUENCE public.user_packages_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.user_packages_id_seq OWNER TO neondb_owner;

--
-- Name: user_packages_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: neondb_owner
--

ALTER SEQUENCE public.user_packages_id_seq OWNED BY public.user_packages.id;


--
-- Name: user_settings; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.user_settings (
    id integer NOT NULL,
    user_id integer NOT NULL,
    duplicate_slip_check boolean DEFAULT true,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now(),
    email_notifications boolean DEFAULT true,
    theme text DEFAULT 'dark'::text,
    language text DEFAULT 'th'::text
);


ALTER TABLE public.user_settings OWNER TO neondb_owner;

--
-- Name: user_settings_id_seq; Type: SEQUENCE; Schema: public; Owner: neondb_owner
--

CREATE SEQUENCE public.user_settings_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.user_settings_id_seq OWNER TO neondb_owner;

--
-- Name: user_settings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: neondb_owner
--

ALTER SEQUENCE public.user_settings_id_seq OWNED BY public.user_settings.id;


--
-- Name: users; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.users (
    id integer NOT NULL,
    username text NOT NULL,
    password text NOT NULL,
    email text NOT NULL,
    first_name text,
    last_name text,
    company_name text,
    phone_number text,
    address text,
    bio text,
    profile_image text,
    credit double precision DEFAULT 0 NOT NULL,
    tier public.customer_tier DEFAULT 'standard'::public.customer_tier NOT NULL,
    role public.user_role DEFAULT 'user'::public.user_role NOT NULL,
    status public.user_status DEFAULT 'active'::public.user_status NOT NULL,
    allowed_packages integer[],
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    phone_verified boolean DEFAULT false,
    email_verified boolean DEFAULT false,
    auth_providers text[] DEFAULT ARRAY[]::text[]
);


ALTER TABLE public.users OWNER TO neondb_owner;

--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: neondb_owner
--

CREATE SEQUENCE public.users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.users_id_seq OWNER TO neondb_owner;

--
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: neondb_owner
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- Name: verification_codes; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.verification_codes (
    id integer NOT NULL,
    user_id integer,
    type public.verification_type NOT NULL,
    code text NOT NULL,
    identifier text NOT NULL,
    is_used boolean DEFAULT false NOT NULL,
    expires_at timestamp without time zone NOT NULL,
    attempts integer DEFAULT 0 NOT NULL,
    metadata jsonb,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    used_at timestamp without time zone
);


ALTER TABLE public.verification_codes OWNER TO neondb_owner;

--
-- Name: verification_codes_id_seq; Type: SEQUENCE; Schema: public; Owner: neondb_owner
--

CREATE SEQUENCE public.verification_codes_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.verification_codes_id_seq OWNER TO neondb_owner;

--
-- Name: verification_codes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: neondb_owner
--

ALTER SEQUENCE public.verification_codes_id_seq OWNED BY public.verification_codes.id;


--
-- Name: webhook_logs; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.webhook_logs (
    id integer NOT NULL,
    webhook_id integer NOT NULL,
    event_type public.webhook_event_type NOT NULL,
    payload jsonb NOT NULL,
    response_status integer,
    response_body text,
    duration integer,
    success boolean DEFAULT false NOT NULL,
    error text,
    retry_count integer DEFAULT 0 NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.webhook_logs OWNER TO neondb_owner;

--
-- Name: webhook_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: neondb_owner
--

CREATE SEQUENCE public.webhook_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.webhook_logs_id_seq OWNER TO neondb_owner;

--
-- Name: webhook_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: neondb_owner
--

ALTER SEQUENCE public.webhook_logs_id_seq OWNED BY public.webhook_logs.id;


--
-- Name: webhooks; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.webhooks (
    id integer NOT NULL,
    user_id integer NOT NULL,
    name text NOT NULL,
    url text NOT NULL,
    secret text,
    event_types public.webhook_event_type[],
    is_active boolean DEFAULT true NOT NULL,
    headers jsonb,
    conditions jsonb,
    api_key_id integer,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    last_triggered_at timestamp without time zone
);


ALTER TABLE public.webhooks OWNER TO neondb_owner;

--
-- Name: webhooks_id_seq; Type: SEQUENCE; Schema: public; Owner: neondb_owner
--

CREATE SEQUENCE public.webhooks_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.webhooks_id_seq OWNER TO neondb_owner;

--
-- Name: webhooks_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: neondb_owner
--

ALTER SEQUENCE public.webhooks_id_seq OWNED BY public.webhooks.id;


--
-- Name: achievements id; Type: DEFAULT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.achievements ALTER COLUMN id SET DEFAULT nextval('public.achievements_id_seq'::regclass);


--
-- Name: alert_settings id; Type: DEFAULT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.alert_settings ALTER COLUMN id SET DEFAULT nextval('public.alert_settings_id_seq'::regclass);


--
-- Name: api_keys id; Type: DEFAULT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.api_keys ALTER COLUMN id SET DEFAULT nextval('public.api_keys_id_seq'::regclass);


--
-- Name: api_logs id; Type: DEFAULT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.api_logs ALTER COLUMN id SET DEFAULT nextval('public.api_logs_id_seq'::regclass);


--
-- Name: api_response_templates id; Type: DEFAULT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.api_response_templates ALTER COLUMN id SET DEFAULT nextval('public.api_response_templates_id_seq'::regclass);


--
-- Name: coupons id; Type: DEFAULT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.coupons ALTER COLUMN id SET DEFAULT nextval('public.coupons_id_seq'::regclass);


--
-- Name: customer_behavior id; Type: DEFAULT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.customer_behavior ALTER COLUMN id SET DEFAULT nextval('public.customer_behavior_id_seq'::regclass);


--
-- Name: email_logs id; Type: DEFAULT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.email_logs ALTER COLUMN id SET DEFAULT nextval('public.email_logs_id_seq'::regclass);


--
-- Name: email_templates id; Type: DEFAULT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.email_templates ALTER COLUMN id SET DEFAULT nextval('public.email_templates_id_seq'::regclass);


--
-- Name: external_auth id; Type: DEFAULT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.external_auth ALTER COLUMN id SET DEFAULT nextval('public.external_auth_id_seq'::regclass);


--
-- Name: fraud_detections id; Type: DEFAULT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.fraud_detections ALTER COLUMN id SET DEFAULT nextval('public.fraud_detections_id_seq'::regclass);


--
-- Name: fraud_rules id; Type: DEFAULT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.fraud_rules ALTER COLUMN id SET DEFAULT nextval('public.fraud_rules_id_seq'::regclass);


--
-- Name: notifications id; Type: DEFAULT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.notifications ALTER COLUMN id SET DEFAULT nextval('public.notifications_id_seq'::regclass);


--
-- Name: packages id; Type: DEFAULT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.packages ALTER COLUMN id SET DEFAULT nextval('public.packages_id_seq'::regclass);


--
-- Name: reports id; Type: DEFAULT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.reports ALTER COLUMN id SET DEFAULT nextval('public.reports_id_seq'::regclass);


--
-- Name: slip_verifications id; Type: DEFAULT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.slip_verifications ALTER COLUMN id SET DEFAULT nextval('public.slip_verifications_id_seq'::regclass);


--
-- Name: system_settings id; Type: DEFAULT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.system_settings ALTER COLUMN id SET DEFAULT nextval('public.system_settings_id_seq'::regclass);


--
-- Name: top_up_transactions id; Type: DEFAULT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.top_up_transactions ALTER COLUMN id SET DEFAULT nextval('public.top_up_transactions_id_seq'::regclass);


--
-- Name: user_achievements id; Type: DEFAULT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.user_achievements ALTER COLUMN id SET DEFAULT nextval('public.user_achievements_id_seq'::regclass);


--
-- Name: user_auth_logs id; Type: DEFAULT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.user_auth_logs ALTER COLUMN id SET DEFAULT nextval('public.user_auth_logs_id_seq'::regclass);


--
-- Name: user_packages id; Type: DEFAULT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.user_packages ALTER COLUMN id SET DEFAULT nextval('public.user_packages_id_seq'::regclass);


--
-- Name: user_settings id; Type: DEFAULT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.user_settings ALTER COLUMN id SET DEFAULT nextval('public.user_settings_id_seq'::regclass);


--
-- Name: users id; Type: DEFAULT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- Name: verification_codes id; Type: DEFAULT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.verification_codes ALTER COLUMN id SET DEFAULT nextval('public.verification_codes_id_seq'::regclass);


--
-- Name: webhook_logs id; Type: DEFAULT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.webhook_logs ALTER COLUMN id SET DEFAULT nextval('public.webhook_logs_id_seq'::regclass);


--
-- Name: webhooks id; Type: DEFAULT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.webhooks ALTER COLUMN id SET DEFAULT nextval('public.webhooks_id_seq'::regclass);


--
-- Data for Name: achievements; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.achievements (id, name, description, type, level, requirement, points, "packageId", icon, "sortOrder", color, "createdAt", "updatedAt") FROM stdin;
1	เริ่มต้นใช้งาน	ล็อกอินเข้าสู่ระบบ SLIPKUY เป็นครั้งแรก	first_time	1	1	10	\N	award	1	#4CAF50	2025-04-29 10:50:27.161685+00	2025-04-29 10:50:27.161685+00
2	นักสำรวจ	เข้าชมทุกหน้าในระบบ	milestone	1	5	20	\N	compass	2	#2196F3	2025-04-29 10:50:27.161685+00	2025-04-29 10:50:27.161685+00
3	ช่างตรวจสอบมือใหม่	ตรวจสอบสลิปครั้งแรก	first_time	1	1	15	\N	check-circle	3	#FF9800	2025-04-29 10:50:27.161685+00	2025-04-29 10:50:27.161685+00
4	ช่างตรวจสอบมืออาชีพ	ตรวจสอบสลิป 10 รายการ	milestone	2	10	30	\N	shield	4	#9C27B0	2025-04-29 10:50:27.161685+00	2025-04-29 10:50:27.161685+00
5	ช่างตรวจสอบระดับเทพ	ตรวจสอบสลิป 100 รายการ	milestone	3	100	100	\N	shield-check	5	#E91E63	2025-04-29 10:50:27.161685+00	2025-04-29 10:50:27.161685+00
6	เจ้าแห่ง API	สร้าง API key และเรียกใช้งาน	usage	1	1	25	\N	key	6	#3F51B5	2025-04-29 10:50:27.161685+00	2025-04-29 10:50:27.161685+00
7	นักวิเคราะห์	ใช้งานการค้นหาขั้นสูง	usage	1	1	25	\N	search	7	#009688	2025-04-29 10:50:27.161685+00	2025-04-29 10:50:27.161685+00
8	ภักดีต่อเทพเจ้า	ล็อกอินเข้าระบบติดต่อกัน 7 วัน	streak	2	7	50	\N	heart	8	#F44336	2025-04-29 10:50:27.161685+00	2025-04-29 10:50:27.161685+00
\.


--
-- Data for Name: alert_settings; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.alert_settings (id, user_id, alert_type, enabled, channels, threshold, frequency, time_preference, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: api_keys; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.api_keys (id, user_id, api_key, name, description, status, last_used, request_count, ip_whitelist, expires_at, limit_enabled, usage_limit, created_at, updated_at, duplicate_slip_check) FROM stdin;
1	1	5ad6f8191e59e3e72d7337b9d30ced9ce1a05fea1807a13007e8f143ce659ffe	testapi		active	2025-04-29 13:14:19.472	7	\N	\N	f	\N	2025-04-19 05:19:52.564268	2025-04-29 13:14:19.472	t
3	2	7ffd90bb3a13a159787c378cff451e88bed4c634ff76204b8c0e3db2dd3daabf	ทดสอบ	\N	active	2025-04-24 18:44:52.404	2	\N	\N	f	\N	2025-04-19 08:23:31.581364	2025-04-24 18:44:52.404	t
5	5	76baa96794f280c6903234f448bb69f75bdd49660904316bc7ade8b4d1e55e26	test66	\N	active	2025-04-27 07:38:57.869	2	\N	\N	f	\N	2025-04-26 15:14:43.273	2025-04-27 07:38:57.869	t
2	1	ef6b1f54c325163ae98c95102c700a09e080c9c2744ceeea9465186b51d6abf0	test1	""	revoked	2025-04-20 18:42:05.331	0	\N	\N	f	60	2025-04-19 05:32:05.253389	2025-04-29 13:05:54.581	f
4	1	04bfbe235883dfc4b56b52878789bf9bff0245fcaa9af812b9eb8486dde0f7cf	ทดสอบ	""	active	2025-04-29 13:11:56.781	55	\N	\N	f	2	2025-04-21 19:22:02.910882	2025-04-29 13:11:56.781	t
\.


--
-- Data for Name: api_logs; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.api_logs (id, api_key_id, request_type, request_data, response_status, response_data, slip_verification_id, ip_address, user_agent, processing_time, error_message, created_at) FROM stdin;
48	4	verify_slip	"{\\"path\\":\\"/api/v1/verify-slip\\"}"	success	"{\\"statusCode\\":200}"	\N	***********	\N	3750	\N	2025-04-29 12:29:12.23162
49	4	verify_slip	"{\\"path\\":\\"/api/v1/verify-slip\\"}"	error	"{\\"statusCode\\":409}"	\N	***********	\N	843	Duplicate slip detected	2025-04-29 12:29:21.36865
50	4	verify_slip	"{\\"path\\":\\"/api/v1/verify-slip\\"}"	error	"{\\"statusCode\\":409}"	\N	***********	\N	928	Duplicate slip detected	2025-04-29 12:52:38.338647
51	4	verify_slip	"{\\"path\\":\\"/api/v1/verify-slip\\"}"	error	"{\\"statusCode\\":409}"	\N	***********	\N	1020	Duplicate slip detected	2025-04-29 12:52:49.235442
52	4	verify_slip	"{\\"path\\":\\"/api/v1/verify-slip\\"}"	error	"{\\"statusCode\\":409}"	\N	***********	\N	1224	Duplicate slip detected	2025-04-29 12:52:59.415438
53	4	verify_slip	"{\\"path\\":\\"/api/v1/verify-slip\\"}"	success	"{\\"statusCode\\":200}"	\N	***********	\N	3944	\N	2025-04-29 12:53:17.46872
54	4	verify_slip	"{\\"path\\":\\"/api/v1/verify-slip\\"}"	success	"{\\"statusCode\\":200}"	\N	***********	\N	3793	\N	2025-04-29 12:53:28.958885
55	4	verify_slip	"{\\"path\\":\\"/api/v1/verify-slip\\"}"	error	"{\\"statusCode\\":409}"	\N	***********	\N	1245	Duplicate slip detected	2025-04-29 13:05:29.557524
56	4	verify_slip	"{\\"path\\":\\"/api/v1/verify-slip\\"}"	error	"{\\"statusCode\\":409}"	\N	***********	\N	765	Duplicate slip detected	2025-04-29 13:05:33.783371
57	4	verify_slip	"{\\"path\\":\\"/api/v1/verify-slip\\"}"	error	"{\\"statusCode\\":409}"	\N	***********	\N	1063	\N	2025-04-29 13:05:40.650513
58	4	verify_slip	"{\\"path\\":\\"/api/v1/verify-slip\\"}"	error	"{\\"statusCode\\":409}"	\N	***********	\N	1070	\N	2025-04-29 13:05:47.236054
59	4	verify_slip	"{\\"path\\":\\"/api/v1/verify-slip\\"}"	error	"{\\"statusCode\\":409}"	\N	***********	\N	1703	\N	2025-04-29 13:06:03.71869
60	4	verify_slip	"{\\"path\\":\\"/api/v1/verify-slip\\"}"	error	"{\\"statusCode\\":409}"	\N	***********	\N	1345	\N	2025-04-29 13:06:14.906732
61	4	verify_slip	"{\\"path\\":\\"/api/v1/verify-slip\\"}"	error	"{\\"statusCode\\":409}"	\N	***********	\N	1066	\N	2025-04-29 13:06:17.894731
62	4	verify_slip	"{\\"path\\":\\"/api/v1/verify-slip\\"}"	success	"{\\"statusCode\\":200}"	\N	***********	\N	3468	\N	2025-04-29 13:11:49.415467
63	4	verify_slip	"{\\"path\\":\\"/api/v1/verify-slip\\"}"	error	"{\\"statusCode\\":409}"	\N	***********	\N	875	Duplicate slip detected	2025-04-29 13:11:57.832798
64	1	verify_slip	"{\\"path\\":\\"/api/v1/verify-slip\\"}"	success	"{\\"statusCode\\":200}"	\N	***********	\N	3323	\N	2025-04-29 13:12:13.167716
65	1	verify_slip	"{\\"path\\":\\"/api/v1/verify-slip\\"}"	error	"{\\"statusCode\\":409}"	\N	***********	\N	695	Duplicate slip detected	2025-04-29 13:12:25.910881
66	1	verify_slip	"{\\"path\\":\\"/api/v1/verify-slip\\"}"	success	"{\\"statusCode\\":200}"	\N	***********	\N	3706	\N	2025-04-29 13:13:37.340245
67	1	verify_slip	"{\\"path\\":\\"/api/v1/verify-slip\\"}"	success	"{\\"statusCode\\":200}"	\N	***********	\N	3417	\N	2025-04-29 13:14:23.045408
\.


--
-- Data for Name: api_response_templates; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.api_response_templates (id, status_code, status_type, message, description, template, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: coupons; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.coupons (id, code, discount_percent, discount_amount, max_usage, usage_count, start_date, end_date, is_active, created_at, updated_at) FROM stdin;
2	haha1111	10	100	1	1	2025-04-28 00:00:00	2025-05-28 00:00:00	t	2025-04-28 18:36:14.417262	2025-04-29 13:47:58.482
3	SLIPKUY50	50	0	5	1	2025-04-29 00:00:00	2025-05-29 00:00:00	t	2025-04-29 13:49:43.092964	2025-04-29 14:06:53.699
\.


--
-- Data for Name: customer_behavior; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.customer_behavior (id, user_id, avg_daily_verifications, avg_weekly_verifications, avg_monthly_verifications, peak_usage_time, most_used_bank, avg_transaction_amount, last_activity_date, api_usage_pattern, recommended_package_id, analysis_date, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: email_logs; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.email_logs (id, user_id, "to", subject, content, template_id, status, error_message, metadata, sent_at, created_at) FROM stdin;
1	1	<EMAIL>	SLIPKUY - email_verification	{"code":"974319","expiresAt":"25/4/2568 14:43:30","email":"<EMAIL>"}	\N	failed	EmailService: ไม่พบเทมเพลต email_verification	\N	\N	2025-04-25 14:32:39.373304
2	1	<EMAIL>	SLIPKUY - email_verification	{"code":"974319","expiresAt":"25/4/2568 14:43:30","email":"<EMAIL>"}	\N	failed	EmailService: ไม่พบเทมเพลต email_verification	\N	\N	2025-04-25 14:33:52.735556
3	1	<EMAIL>	SLIPKUY - email_verification	{"code":"974319","expiresAt":"25/4/2568 14:43:30","email":"<EMAIL>"}	\N	failed	EmailService: ไม่พบเทมเพลต email_verification	\N	\N	2025-04-25 14:33:57.0215
4	1	<EMAIL>	รหัสยืนยันการสมัครสมาชิก SLIPKUY	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #222; color: #fff;">\n  <h2 style="color: #ff5e00; background: linear-gradient(90deg, #ff5e00, #ffb000); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">รหัสยืนยันอีเมล SLIPKUY</h2>\n  <p>สวัสดีคุณ <EMAIL>,</p>\n  <p>ขอบคุณที่สมัครใช้งาน SLIPKUY</p>\n  <p>รหัสยืนยันของคุณคือ:</p>\n  <div style="font-family: monospace; font-size: 24px; letter-spacing: 2px; text-align: center; margin: 20px 0; padding: 10px; background-color: #333; border-radius: 5px; color: #fff; font-weight: bold;">974319</div>\n  <p>รหัสนี้จะหมดอายุใน 25/4/2568 14:43:30</p>\n  <p>หากคุณไม่ได้ทำการสมัครสมาชิก SLIPKUY กรุณาละเลยอีเมลนี้</p>\n  <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #444; text-align: center; font-size: 12px; color: #999;">\n    <p>อีเมลนี้ส่งจากระบบอัตโนมัติของ SLIPKUY กรุณาอย่าตอบกลับ</p>\n    <p>&copy; สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n  </div>\n</div>	\N	sent	\N	{"data": {"code": "974319", "email": "<EMAIL>", "expiresAt": "25/4/2568 14:43:30", "currentYear": 2025}, "messageId": "<<EMAIL>>", "templateName": "email_verification"}	2025-04-25 14:36:48.261	2025-04-25 14:36:48.279713
5	1	<EMAIL>	รหัสยืนยันการสมัครสมาชิก SLIPKUY	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #222; color: #fff;">\n  <h2 style="color: #ff5e00; background: linear-gradient(90deg, #ff5e00, #ffb000); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">รหัสยืนยันอีเมล SLIPKUY</h2>\n  <p>สวัสดีคุณ <EMAIL>,</p>\n  <p>ขอบคุณที่สมัครใช้งาน SLIPKUY</p>\n  <p>รหัสยืนยันของคุณคือ:</p>\n  <div style="font-family: monospace; font-size: 24px; letter-spacing: 2px; text-align: center; margin: 20px 0; padding: 10px; background-color: #333; border-radius: 5px; color: #fff; font-weight: bold;">296219</div>\n  <p>รหัสนี้จะหมดอายุใน 25/4/2568 14:53:45</p>\n  <p>หากคุณไม่ได้ทำการสมัครสมาชิก SLIPKUY กรุณาละเลยอีเมลนี้</p>\n  <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #444; text-align: center; font-size: 12px; color: #999;">\n    <p>อีเมลนี้ส่งจากระบบอัตโนมัติของ SLIPKUY กรุณาอย่าตอบกลับ</p>\n    <p>&copy; สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n  </div>\n</div>	\N	sent	\N	{"data": {"code": "296219", "email": "<EMAIL>", "expiresAt": "25/4/2568 14:53:45", "currentYear": 2025}, "messageId": "<<EMAIL>>", "templateName": "email_verification"}	2025-04-25 14:38:45.545	2025-04-25 14:38:45.563299
6	1	<EMAIL>	รหัสยืนยันการสมัครสมาชิก SLIPKUY	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #222; color: #fff;">\n  <h2 style="color: #ff5e00; background: linear-gradient(90deg, #ff5e00, #ffb000); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">รหัสยืนยันอีเมล SLIPKUY</h2>\n  <p>สวัสดีคุณ <EMAIL>,</p>\n  <p>ขอบคุณที่สมัครใช้งาน SLIPKUY</p>\n  <p>รหัสยืนยันของคุณคือ:</p>\n  <div style="font-family: monospace; font-size: 24px; letter-spacing: 2px; text-align: center; margin: 20px 0; padding: 10px; background-color: #333; border-radius: 5px; color: #fff; font-weight: bold;">487952</div>\n  <p>รหัสนี้จะหมดอายุใน 27/4/2568 09:25:46</p>\n  <p>หากคุณไม่ได้ทำการสมัครสมาชิก SLIPKUY กรุณาละเลยอีเมลนี้</p>\n  <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #444; text-align: center; font-size: 12px; color: #999;">\n    <p>อีเมลนี้ส่งจากระบบอัตโนมัติของ SLIPKUY กรุณาอย่าตอบกลับ</p>\n    <p>&copy; สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n  </div>\n</div>	\N	sent	\N	{"data": {"code": "487952", "email": "<EMAIL>", "expiresAt": "27/4/2568 09:25:46", "currentYear": 2025}, "messageId": "<<EMAIL>>", "templateName": "email_verification"}	2025-04-27 09:10:46.76	2025-04-27 09:10:46.777686
29	1	<EMAIL>	รหัสยืนยันรีเซ็ตรหัสผ่าน SLIPKUY	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #222; color: #fff;">\n  <h2 style="color: #ff5e00; background: linear-gradient(90deg, #ff5e00, #ffb000); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">รหัสยืนยันรีเซ็ตรหัสผ่าน SLIPKUY</h2>\n  <p>สวัสดีคุณ <EMAIL>,</p>\n  <p>คุณได้ขอรีเซ็ตรหัสผ่านสำหรับบัญชี SLIPKUY ของคุณ</p>\n  <p>รหัสยืนยันของคุณคือ:</p>\n  <div style="font-family: monospace; font-size: 24px; letter-spacing: 2px; text-align: center; margin: 20px 0; padding: 10px; background-color: #333; border-radius: 5px; color: #fff; font-weight: bold;">937428</div>\n  <p>รหัสนี้จะหมดอายุใน 29/4/2568 16:18:47</p>\n  <p>หากคุณไม่ได้ขอรีเซ็ตรหัสผ่าน กรุณาละเลยอีเมลนี้และตรวจสอบความปลอดภัยบัญชีของคุณ</p>\n  <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #444; text-align: center; font-size: 12px; color: #999;">\n    <p>อีเมลนี้ส่งจากระบบอัตโนมัติของ SLIPKUY กรุณาอย่าตอบกลับ</p>\n    <p>&copy; สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n  </div>\n</div>	\N	sent	\N	{"data": {"code": "937428", "email": "<EMAIL>", "expiresAt": "29/4/2568 16:18:47", "currentYear": 2025}, "messageId": "<<EMAIL>>", "templateName": "password_reset"}	2025-04-29 16:13:37.403	2025-04-29 16:13:37.42214
7	1	<EMAIL>	รหัสยืนยันการสมัครสมาชิก SLIPKUY	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #222; color: #fff;">\n  <h2 style="color: #ff5e00; background: linear-gradient(90deg, #ff5e00, #ffb000); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">รหัสยืนยันอีเมล SLIPKUY</h2>\n  <p>สวัสดีคุณ <EMAIL>,</p>\n  <p>ขอบคุณที่สมัครใช้งาน SLIPKUY</p>\n  <p>รหัสยืนยันของคุณคือ:</p>\n  <div style="font-family: monospace; font-size: 24px; letter-spacing: 2px; text-align: center; margin: 20px 0; padding: 10px; background-color: #333; border-radius: 5px; color: #fff; font-weight: bold;">468724</div>\n  <p>รหัสนี้จะหมดอายุใน 27/4/2568 09:37:03</p>\n  <p>หากคุณไม่ได้ทำการสมัครสมาชิก SLIPKUY กรุณาละเลยอีเมลนี้</p>\n  <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #444; text-align: center; font-size: 12px; color: #999;">\n    <p>อีเมลนี้ส่งจากระบบอัตโนมัติของ SLIPKUY กรุณาอย่าตอบกลับ</p>\n    <p>&copy; สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n  </div>\n</div>	\N	sent	\N	{"data": {"code": "468724", "email": "<EMAIL>", "expiresAt": "27/4/2568 09:37:03", "currentYear": 2025}, "messageId": "<<EMAIL>>", "templateName": "email_verification"}	2025-04-27 09:22:03.699	2025-04-27 09:22:03.715599
8	1	<EMAIL>	รหัสยืนยันการสมัครสมาชิก SLIPKUY	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #222; color: #fff;">\n  <h2 style="color: #ff5e00; background: linear-gradient(90deg, #ff5e00, #ffb000); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">รหัสยืนยันอีเมล SLIPKUY</h2>\n  <p>สวัสดีคุณ <EMAIL>,</p>\n  <p>ขอบคุณที่สมัครใช้งาน SLIPKUY</p>\n  <p>รหัสยืนยันของคุณคือ:</p>\n  <div style="font-family: monospace; font-size: 24px; letter-spacing: 2px; text-align: center; margin: 20px 0; padding: 10px; background-color: #333; border-radius: 5px; color: #fff; font-weight: bold;">468724</div>\n  <p>รหัสนี้จะหมดอายุใน 27/4/2568 09:37:03</p>\n  <p>หากคุณไม่ได้ทำการสมัครสมาชิก SLIPKUY กรุณาละเลยอีเมลนี้</p>\n  <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #444; text-align: center; font-size: 12px; color: #999;">\n    <p>อีเมลนี้ส่งจากระบบอัตโนมัติของ SLIPKUY กรุณาอย่าตอบกลับ</p>\n    <p>&copy; สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n  </div>\n</div>	\N	sent	\N	{"data": {"code": "468724", "email": "<EMAIL>", "expiresAt": "27/4/2568 09:37:03", "currentYear": 2025}, "messageId": "<<EMAIL>>", "templateName": "email_verification"}	2025-04-27 09:22:11.246	2025-04-27 09:22:11.262145
9	1	<EMAIL>	รหัสยืนยันการสมัครสมาชิก SLIPKUY	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #222; color: #fff;">\n  <h2 style="color: #ff5e00; background: linear-gradient(90deg, #ff5e00, #ffb000); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">รหัสยืนยันอีเมล SLIPKUY</h2>\n  <p>สวัสดีคุณ <EMAIL>,</p>\n  <p>ขอบคุณที่สมัครใช้งาน SLIPKUY</p>\n  <p>รหัสยืนยันของคุณคือ:</p>\n  <div style="font-family: monospace; font-size: 24px; letter-spacing: 2px; text-align: center; margin: 20px 0; padding: 10px; background-color: #333; border-radius: 5px; color: #fff; font-weight: bold;">468724</div>\n  <p>รหัสนี้จะหมดอายุใน 27/4/2568 09:37:03</p>\n  <p>หากคุณไม่ได้ทำการสมัครสมาชิก SLIPKUY กรุณาละเลยอีเมลนี้</p>\n  <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #444; text-align: center; font-size: 12px; color: #999;">\n    <p>อีเมลนี้ส่งจากระบบอัตโนมัติของ SLIPKUY กรุณาอย่าตอบกลับ</p>\n    <p>&copy; สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n  </div>\n</div>	\N	sent	\N	{"data": {"code": "468724", "email": "<EMAIL>", "expiresAt": "27/4/2568 09:37:03", "currentYear": 2025}, "messageId": "<<EMAIL>>", "templateName": "email_verification"}	2025-04-27 09:22:44.059	2025-04-27 09:22:44.075774
10	1	<EMAIL>	รหัสยืนยันการสมัครสมาชิก SLIPKUY	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #222; color: #fff;">\n  <h2 style="color: #ff5e00; background: linear-gradient(90deg, #ff5e00, #ffb000); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">รหัสยืนยันอีเมล SLIPKUY</h2>\n  <p>สวัสดีคุณ <EMAIL>,</p>\n  <p>ขอบคุณที่สมัครใช้งาน SLIPKUY</p>\n  <p>รหัสยืนยันของคุณคือ:</p>\n  <div style="font-family: monospace; font-size: 24px; letter-spacing: 2px; text-align: center; margin: 20px 0; padding: 10px; background-color: #333; border-radius: 5px; color: #fff; font-weight: bold;">468724</div>\n  <p>รหัสนี้จะหมดอายุใน 27/4/2568 09:37:03</p>\n  <p>หากคุณไม่ได้ทำการสมัครสมาชิก SLIPKUY กรุณาละเลยอีเมลนี้</p>\n  <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #444; text-align: center; font-size: 12px; color: #999;">\n    <p>อีเมลนี้ส่งจากระบบอัตโนมัติของ SLIPKUY กรุณาอย่าตอบกลับ</p>\n    <p>&copy; สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n  </div>\n</div>	\N	sent	\N	{"data": {"code": "468724", "email": "<EMAIL>", "expiresAt": "27/4/2568 09:37:03", "currentYear": 2025}, "messageId": "<<EMAIL>>", "templateName": "email_verification"}	2025-04-27 09:22:45.545	2025-04-27 09:22:45.559469
11	1	<EMAIL>	รหัสยืนยันการสมัครสมาชิก SLIPKUY	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #222; color: #fff;">\n  <h2 style="color: #ff5e00; background: linear-gradient(90deg, #ff5e00, #ffb000); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">รหัสยืนยันอีเมล SLIPKUY</h2>\n  <p>สวัสดีคุณ <EMAIL>,</p>\n  <p>ขอบคุณที่สมัครใช้งาน SLIPKUY</p>\n  <p>รหัสยืนยันของคุณคือ:</p>\n  <div style="font-family: monospace; font-size: 24px; letter-spacing: 2px; text-align: center; margin: 20px 0; padding: 10px; background-color: #333; border-radius: 5px; color: #fff; font-weight: bold;">468724</div>\n  <p>รหัสนี้จะหมดอายุใน 27/4/2568 09:37:03</p>\n  <p>หากคุณไม่ได้ทำการสมัครสมาชิก SLIPKUY กรุณาละเลยอีเมลนี้</p>\n  <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #444; text-align: center; font-size: 12px; color: #999;">\n    <p>อีเมลนี้ส่งจากระบบอัตโนมัติของ SLIPKUY กรุณาอย่าตอบกลับ</p>\n    <p>&copy; สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n  </div>\n</div>	\N	sent	\N	{"data": {"code": "468724", "email": "<EMAIL>", "expiresAt": "27/4/2568 09:37:03", "currentYear": 2025}, "messageId": "<<EMAIL>>", "templateName": "email_verification"}	2025-04-27 09:22:46.993	2025-04-27 09:22:47.008265
12	1	<EMAIL>	รหัสยืนยันการสมัครสมาชิก SLIPKUY	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #222; color: #fff;">\n  <h2 style="color: #ff5e00; background: linear-gradient(90deg, #ff5e00, #ffb000); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">รหัสยืนยันอีเมล SLIPKUY</h2>\n  <p>สวัสดีคุณ <EMAIL>,</p>\n  <p>ขอบคุณที่สมัครใช้งาน SLIPKUY</p>\n  <p>รหัสยืนยันของคุณคือ:</p>\n  <div style="font-family: monospace; font-size: 24px; letter-spacing: 2px; text-align: center; margin: 20px 0; padding: 10px; background-color: #333; border-radius: 5px; color: #fff; font-weight: bold;">468724</div>\n  <p>รหัสนี้จะหมดอายุใน 27/4/2568 09:37:03</p>\n  <p>หากคุณไม่ได้ทำการสมัครสมาชิก SLIPKUY กรุณาละเลยอีเมลนี้</p>\n  <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #444; text-align: center; font-size: 12px; color: #999;">\n    <p>อีเมลนี้ส่งจากระบบอัตโนมัติของ SLIPKUY กรุณาอย่าตอบกลับ</p>\n    <p>&copy; สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n  </div>\n</div>	\N	sent	\N	{"data": {"code": "468724", "email": "<EMAIL>", "expiresAt": "27/4/2568 09:37:03", "currentYear": 2025}, "messageId": "<<EMAIL>>", "templateName": "email_verification"}	2025-04-27 09:22:52.943	2025-04-27 09:22:52.958229
13	1	<EMAIL>	รหัสยืนยันการสมัครสมาชิก SLIPKUY	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #222; color: #fff;">\n  <h2 style="color: #ff5e00; background: linear-gradient(90deg, #ff5e00, #ffb000); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">รหัสยืนยันอีเมล SLIPKUY</h2>\n  <p>สวัสดีคุณ <EMAIL>,</p>\n  <p>ขอบคุณที่สมัครใช้งาน SLIPKUY</p>\n  <p>รหัสยืนยันของคุณคือ:</p>\n  <div style="font-family: monospace; font-size: 24px; letter-spacing: 2px; text-align: center; margin: 20px 0; padding: 10px; background-color: #333; border-radius: 5px; color: #fff; font-weight: bold;">468724</div>\n  <p>รหัสนี้จะหมดอายุใน 27/4/2568 09:37:03</p>\n  <p>หากคุณไม่ได้ทำการสมัครสมาชิก SLIPKUY กรุณาละเลยอีเมลนี้</p>\n  <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #444; text-align: center; font-size: 12px; color: #999;">\n    <p>อีเมลนี้ส่งจากระบบอัตโนมัติของ SLIPKUY กรุณาอย่าตอบกลับ</p>\n    <p>&copy; สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n  </div>\n</div>	\N	sent	\N	{"data": {"code": "468724", "email": "<EMAIL>", "expiresAt": "27/4/2568 09:37:03", "currentYear": 2025}, "messageId": "<<EMAIL>>", "templateName": "email_verification"}	2025-04-27 09:23:53.952	2025-04-27 09:23:53.966701
14	1	<EMAIL>	รหัสยืนยันการสมัครสมาชิก SLIPKUY	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #222; color: #fff;">\n  <h2 style="color: #ff5e00; background: linear-gradient(90deg, #ff5e00, #ffb000); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">รหัสยืนยันอีเมล SLIPKUY</h2>\n  <p>สวัสดีคุณ <EMAIL>,</p>\n  <p>ขอบคุณที่สมัครใช้งาน SLIPKUY</p>\n  <p>รหัสยืนยันของคุณคือ:</p>\n  <div style="font-family: monospace; font-size: 24px; letter-spacing: 2px; text-align: center; margin: 20px 0; padding: 10px; background-color: #333; border-radius: 5px; color: #fff; font-weight: bold;">468724</div>\n  <p>รหัสนี้จะหมดอายุใน 27/4/2568 09:37:03</p>\n  <p>หากคุณไม่ได้ทำการสมัครสมาชิก SLIPKUY กรุณาละเลยอีเมลนี้</p>\n  <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #444; text-align: center; font-size: 12px; color: #999;">\n    <p>อีเมลนี้ส่งจากระบบอัตโนมัติของ SLIPKUY กรุณาอย่าตอบกลับ</p>\n    <p>&copy; สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n  </div>\n</div>	\N	sent	\N	{"data": {"code": "468724", "email": "<EMAIL>", "expiresAt": "27/4/2568 09:37:03", "currentYear": 2025}, "messageId": "<<EMAIL>>", "templateName": "email_verification"}	2025-04-27 09:25:27.082	2025-04-27 09:25:27.096998
15	1	<EMAIL>	รหัสยืนยันการสมัครสมาชิก SLIPKUY	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #222; color: #fff;">\n  <h2 style="color: #ff5e00; background: linear-gradient(90deg, #ff5e00, #ffb000); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">รหัสยืนยันอีเมล SLIPKUY</h2>\n  <p>สวัสดีคุณ <EMAIL>,</p>\n  <p>ขอบคุณที่สมัครใช้งาน SLIPKUY</p>\n  <p>รหัสยืนยันของคุณคือ:</p>\n  <div style="font-family: monospace; font-size: 24px; letter-spacing: 2px; text-align: center; margin: 20px 0; padding: 10px; background-color: #333; border-radius: 5px; color: #fff; font-weight: bold;">468724</div>\n  <p>รหัสนี้จะหมดอายุใน 27/4/2568 09:37:03</p>\n  <p>หากคุณไม่ได้ทำการสมัครสมาชิก SLIPKUY กรุณาละเลยอีเมลนี้</p>\n  <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #444; text-align: center; font-size: 12px; color: #999;">\n    <p>อีเมลนี้ส่งจากระบบอัตโนมัติของ SLIPKUY กรุณาอย่าตอบกลับ</p>\n    <p>&copy; สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n  </div>\n</div>	\N	sent	\N	{"data": {"code": "468724", "email": "<EMAIL>", "expiresAt": "27/4/2568 09:37:03", "currentYear": 2025}, "messageId": "<<EMAIL>>", "templateName": "email_verification"}	2025-04-27 09:25:27.337	2025-04-27 09:25:27.351271
16	1	<EMAIL>	รหัสยืนยันการสมัครสมาชิก SLIPKUY	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #222; color: #fff;">\n  <h2 style="color: #ff5e00; background: linear-gradient(90deg, #ff5e00, #ffb000); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">รหัสยืนยันอีเมล SLIPKUY</h2>\n  <p>สวัสดีคุณ <EMAIL>,</p>\n  <p>ขอบคุณที่สมัครใช้งาน SLIPKUY</p>\n  <p>รหัสยืนยันของคุณคือ:</p>\n  <div style="font-family: monospace; font-size: 24px; letter-spacing: 2px; text-align: center; margin: 20px 0; padding: 10px; background-color: #333; border-radius: 5px; color: #fff; font-weight: bold;">468724</div>\n  <p>รหัสนี้จะหมดอายุใน 27/4/2568 09:37:03</p>\n  <p>หากคุณไม่ได้ทำการสมัครสมาชิก SLIPKUY กรุณาละเลยอีเมลนี้</p>\n  <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #444; text-align: center; font-size: 12px; color: #999;">\n    <p>อีเมลนี้ส่งจากระบบอัตโนมัติของ SLIPKUY กรุณาอย่าตอบกลับ</p>\n    <p>&copy; สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n  </div>\n</div>	\N	sent	\N	{"data": {"code": "468724", "email": "<EMAIL>", "expiresAt": "27/4/2568 09:37:03", "currentYear": 2025}, "messageId": "<<EMAIL>>", "templateName": "email_verification"}	2025-04-27 09:27:18.213	2025-04-27 09:27:18.234638
17	1	<EMAIL>	รหัสยืนยันการสมัครสมาชิก SLIPKUY	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #222; color: #fff;">\n  <h2 style="color: #ff5e00; background: linear-gradient(90deg, #ff5e00, #ffb000); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">รหัสยืนยันอีเมล SLIPKUY</h2>\n  <p>สวัสดีคุณ <EMAIL>,</p>\n  <p>ขอบคุณที่สมัครใช้งาน SLIPKUY</p>\n  <p>รหัสยืนยันของคุณคือ:</p>\n  <div style="font-family: monospace; font-size: 24px; letter-spacing: 2px; text-align: center; margin: 20px 0; padding: 10px; background-color: #333; border-radius: 5px; color: #fff; font-weight: bold;">391411</div>\n  <p>รหัสนี้จะหมดอายุใน 27/4/2568 09:47:54</p>\n  <p>หากคุณไม่ได้ทำการสมัครสมาชิก SLIPKUY กรุณาละเลยอีเมลนี้</p>\n  <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #444; text-align: center; font-size: 12px; color: #999;">\n    <p>อีเมลนี้ส่งจากระบบอัตโนมัติของ SLIPKUY กรุณาอย่าตอบกลับ</p>\n    <p>&copy; สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n  </div>\n</div>	\N	sent	\N	{"data": {"code": "391411", "email": "<EMAIL>", "expiresAt": "27/4/2568 09:47:54", "currentYear": 2025}, "messageId": "<<EMAIL>>", "templateName": "email_verification"}	2025-04-27 09:32:54.917	2025-04-27 09:32:54.934463
18	1	<EMAIL>	รหัสยืนยันการสมัครสมาชิก SLIPKUY	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #222; color: #fff;">\n  <h2 style="color: #ff5e00; background: linear-gradient(90deg, #ff5e00, #ffb000); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">รหัสยืนยันอีเมล SLIPKUY</h2>\n  <p>สวัสดีคุณ <EMAIL>,</p>\n  <p>ขอบคุณที่สมัครใช้งาน SLIPKUY</p>\n  <p>รหัสยืนยันของคุณคือ:</p>\n  <div style="font-family: monospace; font-size: 24px; letter-spacing: 2px; text-align: center; margin: 20px 0; padding: 10px; background-color: #333; border-radius: 5px; color: #fff; font-weight: bold;">391411</div>\n  <p>รหัสนี้จะหมดอายุใน 27/4/2568 09:47:54</p>\n  <p>หากคุณไม่ได้ทำการสมัครสมาชิก SLIPKUY กรุณาละเลยอีเมลนี้</p>\n  <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #444; text-align: center; font-size: 12px; color: #999;">\n    <p>อีเมลนี้ส่งจากระบบอัตโนมัติของ SLIPKUY กรุณาอย่าตอบกลับ</p>\n    <p>&copy; สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n  </div>\n</div>	\N	sent	\N	{"data": {"code": "391411", "email": "<EMAIL>", "expiresAt": "27/4/2568 09:47:54", "currentYear": 2025}, "messageId": "<<EMAIL>>", "templateName": "email_verification"}	2025-04-27 09:33:15.707	2025-04-27 09:33:15.722018
19	1	<EMAIL>	รหัสยืนยันการสมัครสมาชิก SLIPKUY	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #222; color: #fff;">\n  <h2 style="color: #ff5e00; background: linear-gradient(90deg, #ff5e00, #ffb000); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">รหัสยืนยันอีเมล SLIPKUY</h2>\n  <p>สวัสดีคุณ <EMAIL>,</p>\n  <p>ขอบคุณที่สมัครใช้งาน SLIPKUY</p>\n  <p>รหัสยืนยันของคุณคือ:</p>\n  <div style="font-family: monospace; font-size: 24px; letter-spacing: 2px; text-align: center; margin: 20px 0; padding: 10px; background-color: #333; border-radius: 5px; color: #fff; font-weight: bold;">391411</div>\n  <p>รหัสนี้จะหมดอายุใน 27/4/2568 09:47:54</p>\n  <p>หากคุณไม่ได้ทำการสมัครสมาชิก SLIPKUY กรุณาละเลยอีเมลนี้</p>\n  <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #444; text-align: center; font-size: 12px; color: #999;">\n    <p>อีเมลนี้ส่งจากระบบอัตโนมัติของ SLIPKUY กรุณาอย่าตอบกลับ</p>\n    <p>&copy; สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n  </div>\n</div>	\N	sent	\N	{"data": {"code": "391411", "email": "<EMAIL>", "expiresAt": "27/4/2568 09:47:54", "currentYear": 2025}, "messageId": "<<EMAIL>>", "templateName": "email_verification"}	2025-04-27 09:33:22.692	2025-04-27 09:33:22.70857
20	1	<EMAIL>	รหัสยืนยันการสมัครสมาชิก SLIPKUY	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #222; color: #fff;">\n  <h2 style="color: #ff5e00; background: linear-gradient(90deg, #ff5e00, #ffb000); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">รหัสยืนยันอีเมล SLIPKUY</h2>\n  <p>สวัสดีคุณ <EMAIL>,</p>\n  <p>ขอบคุณที่สมัครใช้งาน SLIPKUY</p>\n  <p>รหัสยืนยันของคุณคือ:</p>\n  <div style="font-family: monospace; font-size: 24px; letter-spacing: 2px; text-align: center; margin: 20px 0; padding: 10px; background-color: #333; border-radius: 5px; color: #fff; font-weight: bold;">298498</div>\n  <p>รหัสนี้จะหมดอายุใน 27/4/2568 09:56:22</p>\n  <p>หากคุณไม่ได้ทำการสมัครสมาชิก SLIPKUY กรุณาละเลยอีเมลนี้</p>\n  <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #444; text-align: center; font-size: 12px; color: #999;">\n    <p>อีเมลนี้ส่งจากระบบอัตโนมัติของ SLIPKUY กรุณาอย่าตอบกลับ</p>\n    <p>&copy; สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n  </div>\n</div>	\N	sent	\N	{"data": {"code": "298498", "email": "<EMAIL>", "expiresAt": "27/4/2568 09:56:22", "currentYear": 2025}, "messageId": "<<EMAIL>>", "templateName": "email_verification"}	2025-04-27 09:41:23.422	2025-04-27 09:41:23.438837
21	1	<EMAIL>	รหัสยืนยันการสมัครสมาชิก SLIPKUY	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #222; color: #fff;">\n  <h2 style="color: #ff5e00; background: linear-gradient(90deg, #ff5e00, #ffb000); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">รหัสยืนยันอีเมล SLIPKUY</h2>\n  <p>สวัสดีคุณ <EMAIL>,</p>\n  <p>ขอบคุณที่สมัครใช้งาน SLIPKUY</p>\n  <p>รหัสยืนยันของคุณคือ:</p>\n  <div style="font-family: monospace; font-size: 24px; letter-spacing: 2px; text-align: center; margin: 20px 0; padding: 10px; background-color: #333; border-radius: 5px; color: #fff; font-weight: bold;">986226</div>\n  <p>รหัสนี้จะหมดอายุใน 27/4/2568 09:57:13</p>\n  <p>หากคุณไม่ได้ทำการสมัครสมาชิก SLIPKUY กรุณาละเลยอีเมลนี้</p>\n  <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #444; text-align: center; font-size: 12px; color: #999;">\n    <p>อีเมลนี้ส่งจากระบบอัตโนมัติของ SLIPKUY กรุณาอย่าตอบกลับ</p>\n    <p>&copy; สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n  </div>\n</div>	\N	sent	\N	{"data": {"code": "986226", "email": "<EMAIL>", "expiresAt": "27/4/2568 09:57:13", "currentYear": 2025}, "messageId": "<<EMAIL>>", "templateName": "email_verification"}	2025-04-27 09:42:14.34	2025-04-27 09:42:14.358336
22	1	<EMAIL>	รหัสยืนยันการสมัครสมาชิก SLIPKUY	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #222; color: #fff;">\n  <h2 style="color: #ff5e00; background: linear-gradient(90deg, #ff5e00, #ffb000); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">รหัสยืนยันอีเมล SLIPKUY</h2>\n  <p>สวัสดีคุณ <EMAIL>,</p>\n  <p>ขอบคุณที่สมัครใช้งาน SLIPKUY</p>\n  <p>รหัสยืนยันของคุณคือ:</p>\n  <div style="font-family: monospace; font-size: 24px; letter-spacing: 2px; text-align: center; margin: 20px 0; padding: 10px; background-color: #333; border-radius: 5px; color: #fff; font-weight: bold;">457237</div>\n  <p>รหัสนี้จะหมดอายุใน 29/4/2568 05:22:22</p>\n  <p>หากคุณไม่ได้ทำการสมัครสมาชิก SLIPKUY กรุณาละเลยอีเมลนี้</p>\n  <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #444; text-align: center; font-size: 12px; color: #999;">\n    <p>อีเมลนี้ส่งจากระบบอัตโนมัติของ SLIPKUY กรุณาอย่าตอบกลับ</p>\n    <p>&copy; สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n  </div>\n</div>	\N	sent	\N	{"data": {"code": "457237", "email": "<EMAIL>", "expiresAt": "29/4/2568 05:22:22", "currentYear": 2025}, "messageId": "<<EMAIL>>", "templateName": "email_verification"}	2025-04-29 05:07:23.084	2025-04-29 05:07:23.102989
23	2	<EMAIL>	SLIPKUY - รหัสยืนยันสำหรับรีเซ็ตรหัสผ่าน	\n        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">\n          <h2 style="color: #8B5CF6;">รีเซ็ตรหัสผ่าน SLIPKUY</h2>\n          <p>สวัสดี test,</p>\n          <p>คุณได้ขอรีเซ็ตรหัสผ่านสำหรับบัญชี SLIPKUY ของคุณ</p>\n          <p>รหัสยืนยันของคุณคือ:</p>\n          <div style="text-align: center; margin: 20px 0;">\n            <div style="display: inline-block; background-color: #f0f0f0; padding: 15px 30px; border-radius: 5px; letter-spacing: 5px; font-size: 24px; font-weight: bold;">\n              001189\n            </div>\n          </div>\n          <p>รหัสนี้จะหมดอายุใน 10 นาที</p>\n          <p>หากคุณไม่ได้ร้องขอการรีเซ็ตรหัสผ่าน คุณสามารถเพิกเฉยอีเมลนี้ได้</p>\n          \n          <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #e0e0e0; text-align: center; font-size: 12px; color: #757575;">\n            <p>อีเมลนี้ถูกส่งโดยอัตโนมัติจากระบบ SLIPKUY กรุณาอย่าตอบกลับ</p>\n            <p>&copy; 2025 สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n          </div>\n        </div>\n      	\N	sent	\N	{"type": "password_reset", "messageId": "<<EMAIL>>"}	2025-04-29 06:33:24.617	2025-04-29 06:33:24.634336
24	1	<EMAIL>	SLIPKUY - password_reset	{"code":"205639","expiresAt":"29/4/2568 15:48:45","email":"<EMAIL>"}	\N	failed	EmailService: ไม่พบเทมเพลต password_reset	\N	\N	2025-04-29 15:33:45.563942
25	1	<EMAIL>	รหัสยืนยันรีเซ็ตรหัสผ่าน SLIPKUY	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #222; color: #fff;">\n  <h2 style="color: #ff5e00; background: linear-gradient(90deg, #ff5e00, #ffb000); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">รหัสยืนยันรีเซ็ตรหัสผ่าน SLIPKUY</h2>\n  <p>สวัสดีคุณ <EMAIL>,</p>\n  <p>คุณได้ขอรีเซ็ตรหัสผ่านสำหรับบัญชี SLIPKUY ของคุณ</p>\n  <p>รหัสยืนยันของคุณคือ:</p>\n  <div style="font-family: monospace; font-size: 24px; letter-spacing: 2px; text-align: center; margin: 20px 0; padding: 10px; background-color: #333; border-radius: 5px; color: #fff; font-weight: bold;">205639</div>\n  <p>รหัสนี้จะหมดอายุใน 29/4/2568 15:48:45</p>\n  <p>หากคุณไม่ได้ขอรีเซ็ตรหัสผ่าน กรุณาละเลยอีเมลนี้และตรวจสอบความปลอดภัยบัญชีของคุณ</p>\n  <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #444; text-align: center; font-size: 12px; color: #999;">\n    <p>อีเมลนี้ส่งจากระบบอัตโนมัติของ SLIPKUY กรุณาอย่าตอบกลับ</p>\n    <p>&copy; สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n  </div>\n</div>	\N	sent	\N	{"data": {"code": "205639", "email": "<EMAIL>", "expiresAt": "29/4/2568 15:48:45", "currentYear": 2025}, "messageId": "<<EMAIL>>", "templateName": "password_reset"}	2025-04-29 15:37:39.8	2025-04-29 15:37:39.819273
26	1	<EMAIL>	รหัสยืนยันรีเซ็ตรหัสผ่าน SLIPKUY	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #222; color: #fff;">\n  <h2 style="color: #ff5e00; background: linear-gradient(90deg, #ff5e00, #ffb000); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">รหัสยืนยันรีเซ็ตรหัสผ่าน SLIPKUY</h2>\n  <p>สวัสดีคุณ <EMAIL>,</p>\n  <p>คุณได้ขอรีเซ็ตรหัสผ่านสำหรับบัญชี SLIPKUY ของคุณ</p>\n  <p>รหัสยืนยันของคุณคือ:</p>\n  <div style="font-family: monospace; font-size: 24px; letter-spacing: 2px; text-align: center; margin: 20px 0; padding: 10px; background-color: #333; border-radius: 5px; color: #fff; font-weight: bold;">205639</div>\n  <p>รหัสนี้จะหมดอายุใน 29/4/2568 15:48:45</p>\n  <p>หากคุณไม่ได้ขอรีเซ็ตรหัสผ่าน กรุณาละเลยอีเมลนี้และตรวจสอบความปลอดภัยบัญชีของคุณ</p>\n  <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #444; text-align: center; font-size: 12px; color: #999;">\n    <p>อีเมลนี้ส่งจากระบบอัตโนมัติของ SLIPKUY กรุณาอย่าตอบกลับ</p>\n    <p>&copy; สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n  </div>\n</div>	\N	sent	\N	{"data": {"code": "205639", "email": "<EMAIL>", "expiresAt": "29/4/2568 15:48:45", "currentYear": 2025}, "messageId": "<<EMAIL>>", "templateName": "password_reset"}	2025-04-29 15:44:06.979	2025-04-29 15:44:06.998058
27	1	<EMAIL>	รหัสยืนยันรีเซ็ตรหัสผ่าน SLIPKUY	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #222; color: #fff;">\n  <h2 style="color: #ff5e00; background: linear-gradient(90deg, #ff5e00, #ffb000); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">รหัสยืนยันรีเซ็ตรหัสผ่าน SLIPKUY</h2>\n  <p>สวัสดีคุณ <EMAIL>,</p>\n  <p>คุณได้ขอรีเซ็ตรหัสผ่านสำหรับบัญชี SLIPKUY ของคุณ</p>\n  <p>รหัสยืนยันของคุณคือ:</p>\n  <div style="font-family: monospace; font-size: 24px; letter-spacing: 2px; text-align: center; margin: 20px 0; padding: 10px; background-color: #333; border-radius: 5px; color: #fff; font-weight: bold;">205639</div>\n  <p>รหัสนี้จะหมดอายุใน 29/4/2568 15:48:45</p>\n  <p>หากคุณไม่ได้ขอรีเซ็ตรหัสผ่าน กรุณาละเลยอีเมลนี้และตรวจสอบความปลอดภัยบัญชีของคุณ</p>\n  <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #444; text-align: center; font-size: 12px; color: #999;">\n    <p>อีเมลนี้ส่งจากระบบอัตโนมัติของ SLIPKUY กรุณาอย่าตอบกลับ</p>\n    <p>&copy; สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n  </div>\n</div>	\N	sent	\N	{"data": {"code": "205639", "email": "<EMAIL>", "expiresAt": "29/4/2568 15:48:45", "currentYear": 2025}, "messageId": "<<EMAIL>>", "templateName": "password_reset"}	2025-04-29 15:48:28.255	2025-04-29 15:48:28.274004
28	1	<EMAIL>	รหัสยืนยันรีเซ็ตรหัสผ่าน SLIPKUY	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #222; color: #fff;">\n  <h2 style="color: #ff5e00; background: linear-gradient(90deg, #ff5e00, #ffb000); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">รหัสยืนยันรีเซ็ตรหัสผ่าน SLIPKUY</h2>\n  <p>สวัสดีคุณ <EMAIL>,</p>\n  <p>คุณได้ขอรีเซ็ตรหัสผ่านสำหรับบัญชี SLIPKUY ของคุณ</p>\n  <p>รหัสยืนยันของคุณคือ:</p>\n  <div style="font-family: monospace; font-size: 24px; letter-spacing: 2px; text-align: center; margin: 20px 0; padding: 10px; background-color: #333; border-radius: 5px; color: #fff; font-weight: bold;">937428</div>\n  <p>รหัสนี้จะหมดอายุใน 29/4/2568 16:18:47</p>\n  <p>หากคุณไม่ได้ขอรีเซ็ตรหัสผ่าน กรุณาละเลยอีเมลนี้และตรวจสอบความปลอดภัยบัญชีของคุณ</p>\n  <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #444; text-align: center; font-size: 12px; color: #999;">\n    <p>อีเมลนี้ส่งจากระบบอัตโนมัติของ SLIPKUY กรุณาอย่าตอบกลับ</p>\n    <p>&copy; สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n  </div>\n</div>	\N	sent	\N	{"data": {"code": "937428", "email": "<EMAIL>", "expiresAt": "29/4/2568 16:18:47", "currentYear": 2025}, "messageId": "<<EMAIL>>", "templateName": "password_reset"}	2025-04-29 16:03:47.861	2025-04-29 16:03:47.880247
30	1	<EMAIL>	รหัสยืนยันรีเซ็ตรหัสผ่าน SLIPKUY	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #222; color: #fff;">\n  <h2 style="color: #ff5e00; background: linear-gradient(90deg, #ff5e00, #ffb000); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">รหัสยืนยันรีเซ็ตรหัสผ่าน SLIPKUY</h2>\n  <p>สวัสดีคุณ <EMAIL>,</p>\n  <p>คุณได้ขอรีเซ็ตรหัสผ่านสำหรับบัญชี SLIPKUY ของคุณ</p>\n  <p>รหัสยืนยันของคุณคือ:</p>\n  <div style="font-family: monospace; font-size: 24px; letter-spacing: 2px; text-align: center; margin: 20px 0; padding: 10px; background-color: #333; border-radius: 5px; color: #fff; font-weight: bold;">926097</div>\n  <p>รหัสนี้จะหมดอายุใน 29/4/2568 16:36:03</p>\n  <p>หากคุณไม่ได้ขอรีเซ็ตรหัสผ่าน กรุณาละเลยอีเมลนี้และตรวจสอบความปลอดภัยบัญชีของคุณ</p>\n  <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #444; text-align: center; font-size: 12px; color: #999;">\n    <p>อีเมลนี้ส่งจากระบบอัตโนมัติของ SLIPKUY กรุณาอย่าตอบกลับ</p>\n    <p>&copy; สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n  </div>\n</div>	\N	sent	\N	{"data": {"code": "926097", "email": "<EMAIL>", "expiresAt": "29/4/2568 16:36:03", "currentYear": 2025}, "messageId": "<<EMAIL>>", "templateName": "password_reset"}	2025-04-29 16:21:03.79	2025-04-29 16:21:03.808238
31	1	<EMAIL>	SLIPKUY - รหัสยืนยันสำหรับรีเซ็ตรหัสผ่าน	\n        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">\n          <h2 style="color: #8B5CF6;">รีเซ็ตรหัสผ่าน SLIPKUY</h2>\n          <p>สวัสดี tmognot,</p>\n          <p>คุณได้ขอรีเซ็ตรหัสผ่านสำหรับบัญชี SLIPKUY ของคุณ</p>\n          <p>รหัสยืนยันของคุณคือ:</p>\n          <div style="text-align: center; margin: 20px 0;">\n            <div style="display: inline-block; background-color: #f0f0f0; padding: 15px 30px; border-radius: 5px; letter-spacing: 5px; font-size: 24px; font-weight: bold;">\n              597721\n            </div>\n          </div>\n          <p>รหัสนี้จะหมดอายุใน 10 นาที</p>\n          <p>หากคุณไม่ได้ร้องขอการรีเซ็ตรหัสผ่าน คุณสามารถเพิกเฉยอีเมลนี้ได้</p>\n          \n          <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #e0e0e0; text-align: center; font-size: 12px; color: #757575;">\n            <p>อีเมลนี้ถูกส่งโดยอัตโนมัติจากระบบ SLIPKUY กรุณาอย่าตอบกลับ</p>\n            <p>&copy; 2025 สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n          </div>\n        </div>\n      	\N	sent	\N	{"type": "password_reset", "messageId": "<<EMAIL>>"}	2025-04-29 16:25:12.494	2025-04-29 16:25:12.510777
32	1	<EMAIL>	SLIPKUY - รหัสยืนยันสำหรับรีเซ็ตรหัสผ่าน	\n        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">\n          <h2 style="color: #8B5CF6;">รีเซ็ตรหัสผ่าน SLIPKUY</h2>\n          <p>สวัสดี tmognot,</p>\n          <p>คุณได้ขอรีเซ็ตรหัสผ่านสำหรับบัญชี SLIPKUY ของคุณ</p>\n          <p>รหัสยืนยันของคุณคือ:</p>\n          <div style="text-align: center; margin: 20px 0;">\n            <div style="display: inline-block; background-color: #f0f0f0; padding: 15px 30px; border-radius: 5px; letter-spacing: 5px; font-size: 24px; font-weight: bold;">\n              881764\n            </div>\n          </div>\n          <p>รหัสนี้จะหมดอายุใน 10 นาที</p>\n          <p>หากคุณไม่ได้ร้องขอการรีเซ็ตรหัสผ่าน คุณสามารถเพิกเฉยอีเมลนี้ได้</p>\n          \n          <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #e0e0e0; text-align: center; font-size: 12px; color: #757575;">\n            <p>อีเมลนี้ถูกส่งโดยอัตโนมัติจากระบบ SLIPKUY กรุณาอย่าตอบกลับ</p>\n            <p>&copy; 2025 สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n          </div>\n        </div>\n      	\N	sent	\N	{"type": "password_reset", "messageId": "<<EMAIL>>"}	2025-04-29 17:31:04.228	2025-04-29 17:31:04.2448
\.


--
-- Data for Name: email_templates; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.email_templates (id, name, subject, html_content, text_content, variables, is_default, created_at, updated_at) FROM stdin;
1	verification_success	การตรวจสอบสลิปสำเร็จ	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background: linear-gradient(to bottom right, #1f2937, #111827); color: white;">\n    <h2 style="color: #10b981; background: linear-gradient(90deg, #10b981, #3b82f6); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">การตรวจสอบสลิปสำเร็จ</h2>\n    <p>สวัสดี {{userName}},</p>\n    <p>ระบบได้ตรวจสอบสลิปเรียบร้อยแล้ว โดยมีรายละเอียดดังนี้:</p>\n    <div style="background-color: rgba(255, 255, 255, 0.1); border-radius: 5px; padding: 15px; margin: 15px 0;">\n      <p><strong style="color: #10b981;">เลขอ้างอิง:</strong> {{transactionRef}}</p>\n      <p><strong style="color: #10b981;">จำนวนเงิน:</strong> {{amount}} บาท</p>\n      <p><strong style="color: #10b981;">วันที่ทำรายการ:</strong> {{date}}</p>\n      <p><strong style="color: #10b981;">ผู้รับเงิน:</strong> {{receiver}}</p>\n    </div>\n    <p>คุณสามารถตรวจสอบรายละเอียดเพิ่มเติมได้ที่ <a href="{{dashboardUrl}}" style="color: #3b82f6; text-decoration: none;">หน้าแดชบอร์ด</a></p>\n    <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid rgba(255, 255, 255, 0.2); text-align: center; font-size: 12px; color: #9ca3af;">\n      <p>อีเมลนี้ส่งจากระบบอัตโนมัติ กรุณาอย่าตอบกลับ</p>\n      <p>&copy; {{currentYear}} สลีปคุย (SLIPKUY) - ระบบตรวจสอบสลิป</p>\n    </div>\n  </div>	การตรวจสอบสลิปสำเร็จ\\n\\nสวัสดี {{userName}},\\n\\nระบบได้ตรวจสอบสลิปเรียบร้อยแล้ว โดยมีรายละเอียดดังนี้:\\n\\nเลขอ้างอิง: {{transactionRef}}\\nจำนวนเงิน: {{amount}} บาท\\nวันที่ทำรายการ: {{date}}\\nผู้รับเงิน: {{receiver}}\\n\\nคุณสามารถตรวจสอบรายละเอียดเพิ่มเติมได้ที่หน้าแดชบอร์ด: {{dashboardUrl}}\\n\\nอีเมลนี้ส่งจากระบบอัตโนมัติ กรุณาอย่าตอบกลับ\\n© {{currentYear}} สลีปคุย (SLIPKUY) - ระบบตรวจสอบสลิป	{userName,transactionRef,amount,date,receiver,dashboardUrl,currentYear}	t	2025-04-25 06:52:28.066788	2025-04-25 06:52:28.066788
2	credit_low	เครดิตของคุณเหลือน้อย	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background: linear-gradient(to bottom right, #1f2937, #111827); color: white;">\n    <h2 style="color: #ef4444; background: linear-gradient(90deg, #ef4444, #f59e0b); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">แจ้งเตือน: เครดิตเหลือน้อย</h2>\n    <p>สวัสดี {{userName}},</p>\n    <p>ระบบตรวจพบว่าเครดิตในบัญชีของคุณเหลือน้อย ซึ่งอาจส่งผลต่อการใช้งานระบบของคุณ</p>\n    <div style="background-color: rgba(255, 255, 255, 0.1); border-radius: 5px; padding: 15px; margin: 15px 0;">\n      <p><strong style="color: #ef4444;">เครดิตคงเหลือ:</strong> {{remainingCredit}} เครดิต</p>\n      <p><strong style="color: #ef4444;">แพ็กเกจปัจจุบัน:</strong> {{packageName}}</p>\n    </div>\n    <p>เพื่อให้การใช้งานเป็นไปอย่างต่อเนื่อง กรุณาเติมเครดิตหรืออัพเกรดแพ็กเกจของคุณ</p>\n    <div style="text-align: center; margin: 25px 0;">\n      <a href="{{topupUrl}}" style="display: inline-block; background: linear-gradient(90deg, #3b82f6, #8b5cf6); color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;">เติมเครดิตเลย</a>\n    </div>\n    <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid rgba(255, 255, 255, 0.2); text-align: center; font-size: 12px; color: #9ca3af;">\n      <p>อีเมลนี้ส่งจากระบบอัตโนมัติ กรุณาอย่าตอบกลับ</p>\n      <p>&copy; {{currentYear}} สลีปคุย (SLIPKUY) - ระบบตรวจสอบสลิป</p>\n    </div>\n  </div>	แจ้งเตือน: เครดิตเหลือน้อย\\n\\nสวัสดี {{userName}},\\n\\nระบบตรวจพบว่าเครดิตในบัญชีของคุณเหลือน้อย ซึ่งอาจส่งผลต่อการใช้งานระบบของคุณ\\n\\nเครดิตคงเหลือ: {{remainingCredit}} เครดิต\\nแพ็กเกจปัจจุบัน: {{packageName}}\\n\\nเพื่อให้การใช้งานเป็นไปอย่างต่อเนื่อง กรุณาเติมเครดิตหรืออัพเกรดแพ็กเกจของคุณ: {{topupUrl}}\\n\\nอีเมลนี้ส่งจากระบบอัตโนมัติ กรุณาอย่าตอบกลับ\\n© {{currentYear}} สลีปคุย (SLIPKUY) - ระบบตรวจสอบสลิป	{userName,remainingCredit,packageName,topupUrl,currentYear}	t	2025-04-25 06:52:28.066788	2025-04-25 06:52:28.066788
3	suspicious_activity	พบกิจกรรมที่น่าสงสัยในบัญชีของคุณ	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background: linear-gradient(to bottom right, #1f2937, #111827); color: white;">\n    <h2 style="color: #ef4444; background: linear-gradient(90deg, #ef4444, #f59e0b); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">แจ้งเตือนความปลอดภัย: พบกิจกรรมที่น่าสงสัย</h2>\n    <p>สวัสดี {{userName}},</p>\n    <p>ระบบตรวจพบกิจกรรมที่น่าสงสัยในบัญชีของคุณ:</p>\n    <div style="background-color: rgba(255, 255, 255, 0.1); border-radius: 5px; padding: 15px; margin: 15px 0;">\n      <p><strong style="color: #ef4444;">เวลาที่พบ:</strong> {{timestamp}}</p>\n      <p><strong style="color: #ef4444;">ไอพีแอดเดรส:</strong> {{ipAddress}}</p>\n      <p><strong style="color: #ef4444;">สถานที่:</strong> {{location}}</p>\n      <p><strong style="color: #ef4444;">อุปกรณ์:</strong> {{device}}</p>\n      <p><strong style="color: #ef4444;">กิจกรรม:</strong> {{activity}}</p>\n    </div>\n    <p>หากคุณไม่ได้เป็นผู้ดำเนินการนี้ กรุณาเปลี่ยนรหัสผ่านทันที</p>\n    <div style="text-align: center; margin: 25px 0;">\n      <a href="{{changePasswordUrl}}" style="display: inline-block; background: linear-gradient(90deg, #ef4444, #b91c1c); color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;">เปลี่ยนรหัสผ่านเดี๋ยวนี้</a>\n    </div>\n    <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid rgba(255, 255, 255, 0.2); text-align: center; font-size: 12px; color: #9ca3af;">\n      <p>อีเมลนี้ส่งจากระบบอัตโนมัติ กรุณาอย่าตอบกลับ</p>\n      <p>&copy; {{currentYear}} สลีปคุย (SLIPKUY) - ระบบตรวจสอบสลิป</p>\n    </div>\n  </div>	แจ้งเตือนความปลอดภัย: พบกิจกรรมที่น่าสงสัย\\n\\nสวัสดี {{userName}},\\n\\nระบบตรวจพบกิจกรรมที่น่าสงสัยในบัญชีของคุณ:\\n\\nเวลาที่พบ: {{timestamp}}\\nไอพีแอดเดรส: {{ipAddress}}\\nสถานที่: {{location}}\\nอุปกรณ์: {{device}}\\nกิจกรรม: {{activity}}\\n\\nหากคุณไม่ได้เป็นผู้ดำเนินการนี้ กรุณาเปลี่ยนรหัสผ่านทันที: {{changePasswordUrl}}\\n\\nอีเมลนี้ส่งจากระบบอัตโนมัติ กรุณาอย่าตอบกลับ\\n© {{currentYear}} สลีปคุย (SLIPKUY) - ระบบตรวจสอบสลิป	{userName,timestamp,ipAddress,location,device,activity,changePasswordUrl,currentYear}	t	2025-04-25 06:52:28.066788	2025-04-25 06:52:28.066788
4	email_verification	รหัสยืนยันการสมัครสมาชิก SLIPKUY	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #222; color: #fff;">\n  <h2 style="color: #ff5e00; background: linear-gradient(90deg, #ff5e00, #ffb000); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">รหัสยืนยันอีเมล SLIPKUY</h2>\n  <p>สวัสดีคุณ {{email}},</p>\n  <p>ขอบคุณที่สมัครใช้งาน SLIPKUY</p>\n  <p>รหัสยืนยันของคุณคือ:</p>\n  <div style="font-family: monospace; font-size: 24px; letter-spacing: 2px; text-align: center; margin: 20px 0; padding: 10px; background-color: #333; border-radius: 5px; color: #fff; font-weight: bold;">{{code}}</div>\n  <p>รหัสนี้จะหมดอายุใน {{expiresAt}}</p>\n  <p>หากคุณไม่ได้ทำการสมัครสมาชิก SLIPKUY กรุณาละเลยอีเมลนี้</p>\n  <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #444; text-align: center; font-size: 12px; color: #999;">\n    <p>อีเมลนี้ส่งจากระบบอัตโนมัติของ SLIPKUY กรุณาอย่าตอบกลับ</p>\n    <p>&copy; สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n  </div>\n</div>	รหัสยืนยันของคุณคือ {{code}} (ใช้ได้ถึง {{expiresAt}})	{code,expiresAt,email}	t	2025-04-25 14:34:25.332215	2025-04-25 14:34:25.332215
5	general_verification	รหัสยืนยันจาก SLIPKUY	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #222; color: #fff;">\n  <h2 style="color: #ff5e00; background: linear-gradient(90deg, #ff5e00, #ffb000); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">รหัสยืนยันจาก SLIPKUY</h2>\n  <p>สวัสดีคุณ {{email}},</p>\n  <p>รหัสยืนยันของคุณคือ:</p>\n  <div style="font-family: monospace; font-size: 24px; letter-spacing: 2px; text-align: center; margin: 20px 0; padding: 10px; background-color: #333; border-radius: 5px; color: #fff; font-weight: bold;">{{code}}</div>\n  <p>รหัสนี้จะหมดอายุใน {{expiresAt}}</p>\n  <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #444; text-align: center; font-size: 12px; color: #999;">\n    <p>อีเมลนี้ส่งจากระบบอัตโนมัติของ SLIPKUY กรุณาอย่าตอบกลับ</p>\n    <p>&copy; สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n  </div>\n</div>	รหัสยืนยันของคุณคือ {{code}} (ใช้ได้ถึง {{expiresAt}})	{code,expiresAt,email}	t	2025-04-25 14:34:41.801166	2025-04-25 14:34:41.801166
6	password_reset	รหัสยืนยันรีเซ็ตรหัสผ่าน SLIPKUY	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #222; color: #fff;">\n  <h2 style="color: #ff5e00; background: linear-gradient(90deg, #ff5e00, #ffb000); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">รหัสยืนยันรีเซ็ตรหัสผ่าน SLIPKUY</h2>\n  <p>สวัสดีคุณ {{email}},</p>\n  <p>คุณได้ขอรีเซ็ตรหัสผ่านสำหรับบัญชี SLIPKUY ของคุณ</p>\n  <p>รหัสยืนยันของคุณคือ:</p>\n  <div style="font-family: monospace; font-size: 24px; letter-spacing: 2px; text-align: center; margin: 20px 0; padding: 10px; background-color: #333; border-radius: 5px; color: #fff; font-weight: bold;">{{code}}</div>\n  <p>รหัสนี้จะหมดอายุใน {{expiresAt}}</p>\n  <p>หากคุณไม่ได้ขอรีเซ็ตรหัสผ่าน กรุณาละเลยอีเมลนี้และตรวจสอบความปลอดภัยบัญชีของคุณ</p>\n  <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #444; text-align: center; font-size: 12px; color: #999;">\n    <p>อีเมลนี้ส่งจากระบบอัตโนมัติของ SLIPKUY กรุณาอย่าตอบกลับ</p>\n    <p>&copy; สลีปคุย (SLIPKUY) สงวนลิขสิทธิ์ให้เทพพระเจ้า</p>\n  </div>\n</div>	รหัสยืนยันรีเซ็ตรหัสผ่านของคุณคือ {{code}} (ใช้ได้ถึง {{expiresAt}})	{code,expiresAt,email}	f	2025-04-29 15:36:38.830597	2025-04-29 15:36:38.830597
\.


--
-- Data for Name: external_auth; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.external_auth (id, user_id, provider, external_id, display_name, profile_image, email, access_token, refresh_token, token_expiry, metadata, last_login, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: fraud_detections; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.fraud_detections (id, slip_verification_id, user_id, rule_id, status, details, reviewed_by, reviewed_at, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: fraud_rules; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.fraud_rules (id, name, description, rule_type, conditions, action, severity, is_active, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: notifications; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.notifications (id, user_id, title, message, type, priority, status, channel, metadata, is_read, read_at, sent_at, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: packages; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.packages (id, name, description, price, discount_3_months, discount_6_months, discount_12_months, duration_days, requests_limit, credit_per_verification, is_active, features, tag, sort_order, created_at, updated_at) FROM stdin;
12	แพ็กเกจราย API Call	จ่ายตามการใช้งานจริง	1	0	0	0	30	2	0.5	t	{จ่ายตามการใช้งานจริง,"มีค่าใช้จ่ายในการต่ออายุ 1 บาท ต่อเดือน/เมื่อเปิดใช้งาน"}	ยืดหยุ่น	1	2025-04-24 17:49:43.137613	2025-04-29 04:59:11.027
11	แพ็กเกจทดลองใช้	แพ็กเกจสำหรับทดลองใช้ 7 วัน	0	0	0	0	7	30	1	t	{ตรวจสอบสลิปเบื้องต้น,เก็บประวัติการตรวจสอบ,"ทดลองใช้ API"}	ทดลองใช้	0	2025-04-24 17:49:43.137613	2025-04-29 04:59:10.998
2	แพ็กเกจมาตรฐาน	แพ็กเกจสำหรับลูกค้าทั่วไป	199	5	10	17	30	100	0.4	t	{ตรวจสอบสลิปเต็มรูปแบบ,เก็บประวัติการตรวจสอบ,ออกรายงานรายเดือน}	แนะนำ	2	2025-04-24 17:49:43.137613	2025-04-29 04:59:11.054
8	แพ็กเกจธุรกิจเล็ก	แพ็กเกจสำหรับธุรกิจขนาดเล็ก	399	5	10	17	30	300	0.35	t	{ตรวจสอบสลิปเต็มรูปแบบ,เก็บประวัติการตรวจสอบ,ออกรายงานรายเดือน,"API สำหรับนักพัฒนา"}	\N	3	2025-04-24 17:49:43.137613	2025-04-29 04:59:11.077
9	แพ็กเกจธุรกิจ	แพ็กเกจสำหรับธุรกิจทั่วไป	999	5	10	17	30	1000	0.3	t	{ตรวจสอบสลิปไม่จำกัด,เก็บประวัติการตรวจสอบ,ออกรายงานรายวัน,"API สำหรับนักพัฒนา",การแจ้งเตือนเรียลไทม์}	คุ้มค่า	4	2025-04-24 17:49:43.137613	2025-04-29 04:59:11.105
10	แพ็กเกจองค์กร	แพ็กเกจสำหรับองค์กรขนาดใหญ่	2999	5	10	17	30	5000	0.25	t	{ตรวจสอบสลิปไม่จำกัด,เก็บประวัติการตรวจสอบไม่จำกัด,ออกรายงานแบบกำหนดเอง,"API แบบไม่จำกัด",การแจ้งเตือนเรียลไทม์,ผู้ดูแลระบบส่วนตัว}	พรีเมียม	5	2025-04-24 17:49:43.137613	2025-04-29 04:59:11.135
\.


--
-- Data for Name: reports; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.reports (id, user_id, name, type, format, data, start_date, end_date, status, email_log_id, created_at) FROM stdin;
\.


--
-- Data for Name: session; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.session (sid, sess, expire) FROM stdin;
Ec75edOOpy1txm0JlGZChvcTKsHZIIia	{"cookie":{"originalMaxAge":2591999998,"expires":"2025-05-24T20:41:58.711Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":2},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"571e9b0fd85fba64","lastLogin":"2025-04-24T20:38:50.613Z","location":"ประเทศไทย","fingerprints":["b529bc761fc91abd","40a2e0643c2b9f70","571e9b0fd85fba64","e6e8d0c018f7e0f7"],"lastActivity":"2025-04-24T20:41:58.712Z","loginTime":"2025-04-24T20:38:51.375Z"}	2025-05-24 20:41:59
g-NXFTCRGCBbUQnXVMFLiEuTFU_qQ7Ou	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T06:19:05.868Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"d8b3ef97da475e2c","lastLogin":"2025-04-25T06:19:05.867Z","location":"ประเทศไทย","lastActivity":"2025-04-25T06:19:05.868Z"}	2025-05-25 06:19:06
Hh4VGOy6hmTgDLctZNEyueVfvsoqdiHB	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T05:33:41.951Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"127.0.0.1","userAgent":"curl/8.11.1","fingerprint":"d400f0c5e29c","lastLogin":"2025-04-29T05:33:41.928Z","location":"ประเทศไทย","lastActivity":"2025-04-29T05:33:41.928Z"}	2025-05-29 05:33:42
yc_9NT7YT-unW25GfG9geNB3Qwi4mykP	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-24T20:37:19.579Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"47cd62b6b8bf028b","lastLogin":"2025-04-24T20:37:05.674Z","location":"ประเทศไทย","lastActivity":"2025-04-24T20:37:19.577Z"}	2025-05-24 20:37:20
wRuXw-5k94HlxFspXHK0pnekPr5sa8_g	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T06:23:10.640Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/118.0.5993.88 Safari/537.36","fingerprint":"fa2f65a7f5c64591","lastLogin":"2025-04-25T06:23:10.613Z","location":"ประเทศไทย","lastActivity":"2025-04-25T06:23:10.613Z"}	2025-05-25 06:23:11
j-D99HzqRbBNL2VwzuiRCKfZh09fiR7z	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T12:37:14.733Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"5f568ef909ccc82f","lastLogin":"2025-04-25T12:37:14.733Z","location":"ประเทศไทย","lastActivity":"2025-04-25T12:37:14.733Z"}	2025-05-25 12:37:15
0fqFKr-HQUmG979AlqI2nDNnrcV5qY-y	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T12:41:19.867Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"c6f5d09d2165d656","lastLogin":"2025-04-25T12:41:19.867Z","location":"ประเทศไทย","lastActivity":"2025-04-25T12:41:19.867Z"}	2025-05-25 12:41:20
fugBVbYnkB9OgcP5Z_EiG-c7gaqVK2TV	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T09:39:58.853Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"5f568ef909ccc82f","lastLogin":"2025-04-25T09:39:58.851Z","location":"ประเทศไทย","lastActivity":"2025-04-25T09:39:58.851Z"}	2025-05-25 09:39:59
UyTFlm91IVXS23VN-RAjQT1eKIr97rl5	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T12:41:43.553Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"c6f5d09d2165d656","lastLogin":"2025-04-25T12:41:43.551Z","location":"ประเทศไทย","lastActivity":"2025-04-25T12:41:43.551Z"}	2025-05-25 12:41:44
ZqvcgAgVae8yLUjtQ0QPlMUcGqALfmY6	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T07:19:20.526Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"c57009bd51dbd6b1","lastLogin":"2025-04-25T07:19:20.523Z","location":"ประเทศไทย","lastActivity":"2025-04-25T07:19:20.523Z"}	2025-05-25 07:19:21
yw-G3USQTa7L9nbq-s8mrFuy1fl2fkxz	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T11:22:20.856Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T11:22:20.463Z","location":"ประเทศไทย","lastActivity":"2025-04-29T11:22:20.853Z"}	2025-05-29 11:22:21
o2MfKba_PMyOh-gRGs_dOWoRayRWWSmq	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T07:57:04.571Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"f917a9a39b89b599","lastLogin":"2025-04-25T07:57:04.566Z","location":"ประเทศไทย","lastActivity":"2025-04-25T07:57:04.566Z"}	2025-05-25 07:57:05
rPj-SJg66a-yjCxumZxQFdG9JOlFFFZn	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T09:39:58.570Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"f917a9a39b89b599","lastLogin":"2025-04-25T09:39:58.569Z","location":"ประเทศไทย","lastActivity":"2025-04-25T09:39:58.569Z"}	2025-05-25 09:39:59
-2r6pwL7FVXwystG_0eaEHvH-brMvE0L	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T09:39:58.574Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"5f568ef909ccc82f","lastLogin":"2025-04-25T09:39:58.574Z","location":"ประเทศไทย","lastActivity":"2025-04-25T09:39:58.574Z"}	2025-05-25 09:39:59
F6Xz9nzFJugbFkB0bbuxB6bbjLgCveUC	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T07:16:38.017Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"8b9df6b3afc4521d","lastLogin":"2025-04-25T07:16:38.015Z","location":"ประเทศไทย","lastActivity":"2025-04-25T07:16:38.015Z"}	2025-05-25 07:16:39
yxqfvlb9nD81HB15KHZ2ZI-itLd4c5-G	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T07:19:20.530Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"5f568ef909ccc82f","lastLogin":"2025-04-25T07:19:20.530Z","location":"ประเทศไทย","lastActivity":"2025-04-25T07:19:20.530Z"}	2025-05-25 07:19:21
O2k1TgJZrvoigvjdJYPlVtfj326toH8M	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T12:37:14.217Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"f39c45802fb15efc","lastLogin":"2025-04-25T12:37:14.213Z","location":"ประเทศไทย","lastActivity":"2025-04-25T12:37:14.213Z"}	2025-05-25 12:37:15
Rjy2pza1TVokiBD83SVsOeVRpLqhPOMN	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T07:19:20.906Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"c6f5d09d2165d656","lastLogin":"2025-04-25T07:19:20.906Z","location":"ประเทศไทย","lastActivity":"2025-04-25T07:19:20.906Z"}	2025-05-25 07:19:21
xopwSDcBFoMQzKNN3-PXKX_9U7gc7Srr	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T07:57:04.548Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"5f568ef909ccc82f","lastLogin":"2025-04-25T07:57:04.545Z","location":"ประเทศไทย","lastActivity":"2025-04-25T07:57:04.545Z"}	2025-05-25 07:57:05
llhgomv4rTpwZ8bcexCBahsc4p7qwu7l	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T12:37:14.763Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"c6f5d09d2165d656","lastLogin":"2025-04-25T12:37:14.763Z","location":"ประเทศไทย","lastActivity":"2025-04-25T12:37:14.763Z"}	2025-05-25 12:37:15
Sxbeth5Z0KPM6RhoUe9K-prulrXNt5Ba	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T07:56:22.294Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":1},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"c6f5d09d2165d656","lastLogin":"2025-04-25T07:56:21.971Z","location":"ประเทศไทย","fingerprints":["c6f5d09d2165d656","5f568ef909ccc82f"],"lastActivity":"2025-04-25T07:56:22.293Z","loginTime":"2025-04-25T07:56:22.294Z"}	2025-05-25 07:56:23
1P_THBLSDznDCMbH9Vwzl8MrL5i7P5jT	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T06:19:05.874Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"e6e8d0c018f7e0f7","lastLogin":"2025-04-25T06:19:05.872Z","location":"ประเทศไทย","lastActivity":"2025-04-25T06:19:05.872Z"}	2025-05-25 06:19:06
4gMKIPWTzsNtj5zXeDr76-IFQIpMtnGJ	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T12:41:19.870Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"5f568ef909ccc82f","lastLogin":"2025-04-25T12:41:19.864Z","location":"ประเทศไทย","lastActivity":"2025-04-25T12:41:19.864Z"}	2025-05-25 12:41:20
EEhu9X0Hb6gEEfUkDDfuAQVg1NxTHewi	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T11:22:20.473Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T11:22:20.473Z","location":"ประเทศไทย","lastActivity":"2025-04-29T11:22:20.473Z"}	2025-05-29 11:22:21
XQhM2NWnXRCXKAodXc3TA0_otksjIpom	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T09:39:58.580Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"c6f5d09d2165d656","lastLogin":"2025-04-25T09:39:58.580Z","location":"ประเทศไทย","lastActivity":"2025-04-25T09:39:58.580Z"}	2025-05-25 09:39:59
5dUMy7xr9rUn8FsrB33SdJLM5T5B4Mso	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T09:39:58.577Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"c57009bd51dbd6b1","lastLogin":"2025-04-25T09:39:58.571Z","location":"ประเทศไทย","lastActivity":"2025-04-25T09:39:58.571Z"}	2025-05-25 09:39:59
VX7DQHE_d7QROnOWS4NY27JfSp_FwYqR	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-26T18:25:40.427Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"127.0.0.1","userAgent":"curl/8.11.1","fingerprint":"403a1c7f18f952ad","lastLogin":"2025-04-26T18:25:40.426Z","location":"ประเทศไทย","lastActivity":"2025-04-26T18:25:40.426Z"}	2025-05-26 18:25:41
NHsJDGUC-geM9eHNmlS6Di9BRx-R6uSn	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T12:41:38.046Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":1},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"068ec8fffffdef6f","lastLogin":"2025-04-25T12:41:37.720Z","location":"ประเทศไทย","fingerprints":["068ec8fffffdef6f","c6f5d09d2165d656"],"lastActivity":"2025-04-25T12:41:38.045Z","loginTime":"2025-04-25T12:41:38.046Z"}	2025-05-25 12:41:39
x7DW3YuF4TGijiJCTcS5H2gn0d6foHwv	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T07:19:20.533Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"c6f5d09d2165d656","lastLogin":"2025-04-25T07:19:20.533Z","location":"ประเทศไทย","lastActivity":"2025-04-25T07:19:20.533Z"}	2025-05-25 07:19:21
Kd-B-hBK96Vf2fiiCC2Sj2fS2JCaAqjf	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T13:08:44.795Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"127.0.0.1","userAgent":"curl/8.11.1","fingerprint":"403a1c7f18f952ad","lastLogin":"2025-04-25T13:08:44.792Z","location":"ประเทศไทย","lastActivity":"2025-04-25T13:08:44.792Z"}	2025-05-25 13:08:45
DOy46OYlirT4bUT26hkuAEM2_cnvSC-w	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T06:23:16.459Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/118.0.5993.88 Safari/537.36","fingerprint":"c2b892c2f0eee117","lastLogin":"2025-04-25T06:23:16.458Z","location":"ประเทศไทย","lastActivity":"2025-04-25T06:23:16.459Z"}	2025-05-25 06:23:17
EX7Pdgwq3deMuQ8W9IAHN_UoR47bTTuS	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T12:37:14.747Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"cc9c65abed687c28","lastLogin":"2025-04-25T12:37:14.747Z","location":"ประเทศไทย","lastActivity":"2025-04-25T12:37:14.747Z"}	2025-05-25 12:37:15
c1SqbjuqG0_LwBPWx1hOoWaC10wSDYYI	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T06:23:17.722Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/118.0.5993.88 Safari/537.36","fingerprint":"e2113218a87309e6","lastLogin":"2025-04-25T06:23:16.457Z","location":"ประเทศไทย","lastActivity":"2025-04-25T06:23:17.721Z"}	2025-05-25 06:23:18
X_Gp7YsDeo0mcL29BeSb2j68lNqRHRFh	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T07:19:20.800Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"c6f5d09d2165d656","lastLogin":"2025-04-25T07:19:20.797Z","location":"ประเทศไทย","lastActivity":"2025-04-25T07:19:20.797Z"}	2025-05-25 07:19:21
eay1OynH5rVA4PyY32W4-hIe4H7B5m4Y	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T12:41:19.865Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"15aea0bb84579087","lastLogin":"2025-04-25T12:41:19.865Z","location":"ประเทศไทย","lastActivity":"2025-04-25T12:41:19.865Z"}	2025-05-25 12:41:20
OWsatZDzRPF0Pneswe21fZrE-5neDyX0	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-27T08:55:53.284Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"127.0.0.1","userAgent":"curl/8.11.1","fingerprint":"403a1c7f18f952ad","lastLogin":"2025-04-27T08:55:51.439Z","location":"ประเทศไทย","lastActivity":"2025-04-27T08:55:51.439Z"}	2025-05-27 08:55:54
gX5IlegIp-Tc4ZulCumHu7thDvHawFbm	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-24T20:43:22.805Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/118.0.5993.88 Safari/537.36","fingerprint":"c7b61ac3f35c3991","lastLogin":"2025-04-24T20:40:48.170Z","location":"ประเทศไทย","lastActivity":"2025-04-24T20:43:22.803Z"}	2025-05-24 20:43:23
yrzzpW2XMfnLniQLusInf_8jY_6j_xQf	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T11:22:20.471Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T11:22:20.471Z","location":"ประเทศไทย","lastActivity":"2025-04-29T11:22:20.471Z"}	2025-05-29 11:22:21
uEHmev5ixtgdLp0qXhL0CvOhcUgroI1y	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T07:16:37.997Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"d8b3ef97da475e2c","lastLogin":"2025-04-25T07:16:37.997Z","location":"ประเทศไทย","lastActivity":"2025-04-25T07:16:37.997Z"}	2025-05-25 07:16:38
VDkjCQhF-P-KxPSGBNHxS2oogElv-m7l	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T13:17:39.143Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"068ec8fffffdef6f","lastLogin":"2025-04-25T13:17:39.143Z","location":"ประเทศไทย","lastActivity":"2025-04-25T13:17:39.143Z"}	2025-05-25 13:17:40
gALBsz-CwOaLucrhdUlxsilJChHgValX	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T11:22:21.833Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T11:22:20.836Z","location":"ประเทศไทย","lastActivity":"2025-04-29T11:22:21.833Z"}	2025-05-29 11:22:22
6zLdLftDbeaxMpnvsOGudG0ssQQG_uEq	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T11:22:20.476Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T11:22:20.469Z","location":"ประเทศไทย","lastActivity":"2025-04-29T11:22:20.469Z"}	2025-05-29 11:22:21
INUSbTqtNoH5eYjqj_L2fecDCVt_aiYy	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T11:22:20.742Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T11:22:20.738Z","location":"ประเทศไทย","lastActivity":"2025-04-29T11:22:20.738Z"}	2025-05-29 11:22:21
-Ah27AE070zgzQAZI7WzqCRwGsbd_u2M	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T12:37:14.751Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"cc9c65abed687c28","lastLogin":"2025-04-25T12:37:14.744Z","location":"ประเทศไทย","lastActivity":"2025-04-25T12:37:14.744Z"}	2025-05-25 12:37:15
oqYIO0koG6_883B5MhY3Y4cTh1F5uyk-	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T12:41:19.862Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"068ec8fffffdef6f","lastLogin":"2025-04-25T12:41:19.862Z","location":"ประเทศไทย","lastActivity":"2025-04-25T12:41:19.862Z"}	2025-05-25 12:41:20
8Jj-WMBN7AxLG7WuiNZfdVTQkcy6-W_l	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T12:41:43.549Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"068ec8fffffdef6f","lastLogin":"2025-04-25T12:41:43.549Z","location":"ประเทศไทย","lastActivity":"2025-04-25T12:41:43.549Z"}	2025-05-25 12:41:44
6GSE4b9pUZ2FKq1BGInQYXZil7upQMws	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T11:22:21.348Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T11:22:21.346Z","location":"ประเทศไทย","lastActivity":"2025-04-29T11:22:21.346Z"}	2025-05-29 11:22:22
Vax0TT_tu-pmFE6X5lQGoLWW80buOFtz	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T16:03:31.533Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"068ec8fffffdef6f","lastLogin":"2025-04-25T16:03:31.531Z","location":"ประเทศไทย","lastActivity":"2025-04-25T16:03:31.531Z"}	2025-05-25 16:03:32
BZlGkcJZxObVfKfLafSwOpMTM62giRHv	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T16:03:31.567Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"c57009bd51dbd6b1","lastLogin":"2025-04-25T16:03:31.567Z","location":"ประเทศไทย","lastActivity":"2025-04-25T16:03:31.567Z"}	2025-05-25 16:03:32
bdhMBK8ZK6i3FAUum6PTfflrb-SyC7k_	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T13:03:40.398Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"c57009bd51dbd6b1","lastLogin":"2025-04-25T13:03:40.398Z","location":"ประเทศไทย","lastActivity":"2025-04-25T13:03:40.398Z"}	2025-05-25 13:03:41
5SVvcq5fn0mVCz0FMlIjll57mRLTHvaX	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T13:03:40.402Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"f39c45802fb15efc","lastLogin":"2025-04-25T13:03:40.395Z","location":"ประเทศไทย","lastActivity":"2025-04-25T13:03:40.395Z"}	2025-05-25 13:03:41
7IHeO3y5rYpv8GzdXivqIuYv9QmFgxSm	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T16:03:31.539Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"f39c45802fb15efc","lastLogin":"2025-04-25T16:03:31.539Z","location":"ประเทศไทย","lastActivity":"2025-04-25T16:03:31.539Z"}	2025-05-25 16:03:32
6pNhX-SgZfIGeE2OfSkhKk9pHRY5tH64	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T13:03:40.400Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"068ec8fffffdef6f","lastLogin":"2025-04-25T13:03:40.400Z","location":"ประเทศไทย","lastActivity":"2025-04-25T13:03:40.400Z"}	2025-05-25 13:03:41
3PJV73YEEuwy9-l5hL3meTLjh5Hz9vrM	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T13:03:40.404Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"5f568ef909ccc82f","lastLogin":"2025-04-25T13:03:40.404Z","location":"ประเทศไทย","lastActivity":"2025-04-25T13:03:40.404Z"}	2025-05-25 13:03:41
pCSWSU3zwZoocSD9kvIILUf49LChmovu	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T16:03:31.550Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"068ec8fffffdef6f","lastLogin":"2025-04-25T16:03:31.550Z","location":"ประเทศไทย","lastActivity":"2025-04-25T16:03:31.550Z"}	2025-05-25 16:03:32
Q2zX8tF9iBfaWGeLCeBiEy98zMnGbP2Y	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-26T13:24:58.367Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"2da655a038ce0e2b","lastLogin":"2025-04-26T13:24:58.364Z","location":"ประเทศไทย","lastActivity":"2025-04-26T13:24:58.364Z"}	2025-05-26 13:24:59
s7BZsBOpqSoOzjDnEiMnHaqV2LV1rQDr	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T13:42:54.502Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"00a39a35f4d9de84","lastLogin":"2025-04-25T13:08:33.487Z","location":"ประเทศไทย","lastActivity":"2025-04-25T13:42:54.500Z"}	2025-05-25 13:42:55
HsDhX1figjG2lo1y6Vows2DFQDQ7ESv_	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-26T13:24:58.713Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"06963c4ae05e57ce","lastLogin":"2025-04-26T13:24:58.710Z","location":"ประเทศไทย","lastActivity":"2025-04-26T13:24:58.710Z"}	2025-05-26 13:24:59
0olvd-WRFfzF8TXEJgnnG77yxSFNAPP_	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-27T08:58:36.460Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"127.0.0.1","userAgent":"curl/8.11.1","fingerprint":"403a1c7f18f952ad","lastLogin":"2025-04-27T08:58:36.458Z","location":"ประเทศไทย","lastActivity":"2025-04-27T08:58:36.458Z"}	2025-05-27 08:58:37
t2LvVjtHbUAPaSiQEbxoxTkx1FCPWU9t	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-26T18:16:21.403Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"3d98d6c5a9fc2434","lastLogin":"2025-04-26T18:16:21.401Z","location":"ประเทศไทย","lastActivity":"2025-04-26T18:16:21.401Z"}	2025-05-26 18:16:22
f3wwGcvhkpk1qczGqzpvyfkDGd9mtc5R	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-26T18:16:21.411Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"ef90de741f35317d","lastLogin":"2025-04-26T18:16:21.409Z","location":"ประเทศไทย","lastActivity":"2025-04-26T18:16:21.409Z"}	2025-05-26 18:16:22
q4cVTQf5-uzjuOdBREjMZexBrX-NiW7z	{"cookie":{"originalMaxAge":2591999988,"expires":"2025-05-25T13:07:42.748Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":3},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"d8b3ef97da475e2c","lastLogin":"2025-04-25T06:32:54.386Z","location":"ประเทศไทย","fingerprints":["40a2e0643c2b9f70","d8b3ef97da475e2c","dc261f4507c44231","00a39a35f4d9de84","e6e8d0c018f7e0f7"],"lastActivity":"2025-04-25T13:07:42.760Z","loginTime":"2025-04-25T06:32:54.743Z"}	2025-05-25 13:07:43
b85qUUUpGWaIqJa-4gjWBVntxMA7lcr9	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T13:08:33.544Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"d8b3ef97da475e2c","lastLogin":"2025-04-25T13:08:33.542Z","location":"ประเทศไทย","lastActivity":"2025-04-25T13:08:33.542Z"}	2025-05-25 13:08:34
7H-iwWzi-dWr3oYmUvEerWrd_YN5knsV	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T13:08:33.519Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"00a39a35f4d9de84","lastLogin":"2025-04-25T13:08:33.517Z","location":"ประเทศไทย","lastActivity":"2025-04-25T13:08:33.517Z"}	2025-05-25 13:08:34
Vyd92XYnlUJ6YvAKjWROcEC38bmTXwep	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T13:08:33.527Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"e6e8d0c018f7e0f7","lastLogin":"2025-04-25T13:08:33.522Z","location":"ประเทศไทย","lastActivity":"2025-04-25T13:08:33.522Z"}	2025-05-25 13:08:34
QIdMllpkSLtAX0N7wMHecCY9nsONxDAF	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T13:08:33.787Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"dc261f4507c44231","lastLogin":"2025-04-25T13:08:33.785Z","location":"ประเทศไทย","lastActivity":"2025-04-25T13:08:33.785Z"}	2025-05-25 13:08:34
GRSR4UaXFWAqx7ZcxJFOY3FNuFRF7nh4	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T06:03:14.574Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T06:03:14.508Z","location":"ประเทศไทย","lastActivity":"2025-04-29T06:03:14.508Z"}	2025-05-29 06:03:15
JIUwgPdy39TNGkQVj_-4-TWFBZ8oEvhE	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T13:16:54.576Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"5f568ef909ccc82f","lastLogin":"2025-04-25T13:16:54.574Z","location":"ประเทศไทย","lastActivity":"2025-04-25T13:16:54.574Z"}	2025-05-25 13:16:55
1IxsEZXTTiBJIFoF9zhdCapwWZ_7dh5L	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T13:16:54.597Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"15aea0bb84579087","lastLogin":"2025-04-25T13:16:54.594Z","location":"ประเทศไทย","lastActivity":"2025-04-25T13:16:54.595Z"}	2025-05-25 13:16:55
QvKfegD0k3vSCVm7Q0cA8aa_uQD0NDE0	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T13:17:39.147Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"c57009bd51dbd6b1","lastLogin":"2025-04-25T13:17:39.141Z","location":"ประเทศไทย","lastActivity":"2025-04-25T13:17:39.141Z"}	2025-05-25 13:17:40
WVbGVOsJGDBUDvyuv89FG1WpfC4yHjOI	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T13:17:35.545Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":4},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"15aea0bb84579087","lastLogin":"2025-04-25T13:17:35.431Z","location":"ประเทศไทย","fingerprints":["f917a9a39b89b599","15aea0bb84579087"],"lastActivity":"2025-04-25T13:17:35.545Z","loginTime":"2025-04-25T13:17:35.545Z"}	2025-05-25 13:17:36
nKIAVxviKO-ZuLf9yyNJaFbrTijvNQuJ	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T06:03:14.782Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T06:03:14.780Z","location":"ประเทศไทย","lastActivity":"2025-04-29T06:03:14.780Z"}	2025-05-29 06:03:15
hxKMounreRD-4FRKVpbcrCIeo2INTguZ	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T14:18:42.539Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"00a39a35f4d9de84","lastLogin":"2025-04-25T14:18:42.534Z","location":"ประเทศไทย","lastActivity":"2025-04-25T14:18:42.534Z"}	2025-05-25 14:18:43
K-WdACUbo7JJGZxe0RthTPlGLvMA08wr	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T14:18:40.693Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":1},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"a4c98a74c5a3ebaa","lastLogin":"2025-04-25T14:18:15.797Z","location":"ประเทศไทย","fingerprints":["76778dcfb28e5142","e6e8d0c018f7e0f7","a4c98a74c5a3ebaa"],"lastActivity":"2025-04-25T14:18:40.692Z","loginTime":"2025-04-25T14:18:15.798Z"}	2025-05-25 14:18:41
LRJk7ICsmq_V9Gtej8czi_u9VcUn0iLj	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T14:18:42.563Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"47cd62b6b8bf028b","lastLogin":"2025-04-25T14:18:42.559Z","location":"ประเทศไทย","lastActivity":"2025-04-25T14:18:42.559Z"}	2025-05-25 14:18:43
sVayCoayPZOEzzOanVIaLMhv-S4ne6jr	{"cookie":{"originalMaxAge":2591999997,"expires":"2025-05-25T13:45:13.391Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"40a2e0643c2b9f70","lastLogin":"2025-04-25T13:44:37.363Z","location":"ประเทศไทย","lastActivity":"2025-04-25T13:45:13.392Z"}	2025-05-25 13:45:14
334adKQLjHFQb58ReI_lsPnPOXwraT1s	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T14:18:42.570Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"00a39a35f4d9de84","lastLogin":"2025-04-25T14:18:42.565Z","location":"ประเทศไทย","lastActivity":"2025-04-25T14:18:42.565Z"}	2025-05-25 14:18:43
UwfLTdCZ3nkPqxS9pYcGz7Ps_l_FP3Sp	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T14:18:42.845Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"76778dcfb28e5142","lastLogin":"2025-04-25T14:18:42.843Z","location":"ประเทศไทย","lastActivity":"2025-04-25T14:18:42.843Z"}	2025-05-25 14:18:43
rZxWurlaDx_pZtZEIgLi-YwcxsF11gwt	{"cookie":{"originalMaxAge":2591999984,"expires":"2025-05-30T08:06:37.740Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":5},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T11:22:33.175Z","location":"ประเทศไทย","fingerprints":["690bb07b4cf9"],"lastActivity":"2025-04-30T08:06:37.754Z","loginTime":"2025-04-29T11:22:33.175Z"}	2025-05-30 08:06:38
1_4cwV_2yStbqRyR9dhggFQzRUVipjRI	{"cookie":{"originalMaxAge":2591999999,"expires":"2025-05-29T11:21:18.061Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":2},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T11:10:35.148Z","location":"ประเทศไทย","fingerprints":["690bb07b4cf9"],"lastActivity":"2025-04-29T11:21:18.062Z","loginTime":"2025-04-29T11:10:35.148Z"}	2025-05-29 11:21:19
nPbxoVw-gyiu4duDFVoBmFqn8QI-lSDx	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T06:04:40.048Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T06:04:40.045Z","location":"ประเทศไทย","lastActivity":"2025-04-29T06:04:40.045Z"}	2025-05-29 06:04:41
6iKa32k5jRv2n_XQt_Hx550wVQY2ksz8	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T06:04:40.044Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T06:04:40.043Z","location":"ประเทศไทย","lastActivity":"2025-04-29T06:04:40.043Z"}	2025-05-29 06:04:41
h_uipPV4gCngyTHtS3JXRcQoJEi4Br3v	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-26T13:24:26.759Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":2},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"e81092809832dc71","lastLogin":"2025-04-26T13:24:26.759Z","location":"ประเทศไทย","fingerprints":["e81092809832dc71"],"lastActivity":"2025-04-26T13:24:26.759Z","loginTime":"2025-04-26T13:24:26.759Z"}	2025-05-26 13:24:27
L0ANunLch6BtKc4HdyOL8jsKqRVHjWSj	{"cookie":{"originalMaxAge":2591999993,"expires":"2025-05-25T15:12:15.643Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/118.0.5993.88 Safari/537.36","fingerprint":"e2113218a87309e6","lastLogin":"2025-04-25T06:23:18.353Z","location":"ประเทศไทย","lastActivity":"2025-04-25T15:12:15.645Z"}	2025-05-25 15:12:16
O-vkcHwcD4AvwpkYMlSO25wVsYifWiOT	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-27T08:43:42.457Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"127.0.0.1","userAgent":"curl/8.11.1","fingerprint":"403a1c7f18f952ad","lastLogin":"2025-04-27T08:43:41.993Z","location":"ประเทศไทย","lastActivity":"2025-04-27T08:43:41.993Z"}	2025-05-27 08:43:43
DOdbIekpR3vo7ifuQTFr7dVaIX30uN4p	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-26T13:23:53.341Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"2da655a038ce0e2b","lastLogin":"2025-04-26T13:23:53.341Z","location":"ประเทศไทย","lastActivity":"2025-04-26T13:23:53.341Z"}	2025-05-26 13:23:54
FqVYMZEOdpdtlWJhRryJuKEgoM1fz-MG	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-26T13:23:53.343Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"2da655a038ce0e2b","lastLogin":"2025-04-26T13:23:53.342Z","location":"ประเทศไทย","lastActivity":"2025-04-26T13:23:53.342Z"}	2025-05-26 13:23:54
SCCD-IeNyqO0o6Ni2zL71KGiRZDMkA29	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-26T13:23:53.345Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"3d98d6c5a9fc2434","lastLogin":"2025-04-26T13:23:53.345Z","location":"ประเทศไทย","lastActivity":"2025-04-26T13:23:53.345Z"}	2025-05-26 13:23:54
xuPz79bmFZZeEIMcyc8YG37GStSP346p	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-26T13:23:53.349Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"3d98d6c5a9fc2434","lastLogin":"2025-04-26T13:23:53.346Z","location":"ประเทศไทย","lastActivity":"2025-04-26T13:23:53.346Z"}	2025-05-26 13:23:54
gGn2LP5qRvYNO74Nw70HpnK07qR86vPh	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-26T13:23:53.887Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"f283e277aedf93b4","lastLogin":"2025-04-26T13:23:53.884Z","location":"ประเทศไทย","lastActivity":"2025-04-26T13:23:53.884Z"}	2025-05-26 13:23:54
MoxpmBFGk673VwFRHwgxRUWkndDwyo6d	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-25T16:03:31.012Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"75ac862904c5c97a","lastLogin":"2025-04-25T16:03:31.008Z","location":"ประเทศไทย","lastActivity":"2025-04-25T16:03:31.008Z"}	2025-05-25 16:03:32
NAPmMZJxZMbrYUWqWyx0WzQjjh3-Ica6	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-26T13:24:58.415Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"3d98d6c5a9fc2434","lastLogin":"2025-04-26T13:24:58.408Z","location":"ประเทศไทย","lastActivity":"2025-04-26T13:24:58.408Z"}	2025-05-26 13:24:59
1WFjbbdt9jRhNewxzDfY8pbMl1DnIijL	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-26T13:23:53.616Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"2da655a038ce0e2b","lastLogin":"2025-04-26T13:23:53.613Z","location":"ประเทศไทย","lastActivity":"2025-04-26T13:23:53.613Z"}	2025-05-26 13:23:54
Bc2SRUYcr9zsdGZj2IqzcFCGyDFzbip2	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-26T13:23:53.624Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"3d98d6c5a9fc2434","lastLogin":"2025-04-26T13:23:53.618Z","location":"ประเทศไทย","lastActivity":"2025-04-26T13:23:53.618Z"}	2025-05-26 13:23:54
luSGMltP5hBJimrZWQHRJf3iTGdd449d	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-26T13:24:58.409Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"ef90de741f35317d","lastLogin":"2025-04-26T13:24:58.406Z","location":"ประเทศไทย","lastActivity":"2025-04-26T13:24:58.406Z"}	2025-05-26 13:24:59
IPjUPuI99am2LF_LyZ8QFEOwGOnXBPYe	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-26T18:25:03.303Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"127.0.0.1","userAgent":"curl/8.11.1","fingerprint":"403a1c7f18f952ad","lastLogin":"2025-04-26T18:25:03.303Z","location":"ประเทศไทย","lastActivity":"2025-04-26T18:25:03.303Z"}	2025-05-26 18:25:04
6NQMUx1DvO7isOOPWkLexDWNOgRjhcjr	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-26T18:25:16.147Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"127.0.0.1","userAgent":"curl/8.11.1","fingerprint":"403a1c7f18f952ad","lastLogin":"2025-04-26T18:25:16.124Z","location":"ประเทศไทย","lastActivity":"2025-04-26T18:25:16.124Z"}	2025-05-26 18:25:17
jynLTHDwDS4v8gK5135O0jQ2KDDU4n9Y	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T06:04:27.476Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":2},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T06:04:26.958Z","location":"ประเทศไทย","fingerprints":["690bb07b4cf9"],"lastActivity":"2025-04-29T06:04:27.476Z","loginTime":"2025-04-29T06:04:27.399Z"}	2025-05-29 06:04:28
eLauY-UqlRI2m02yQzAmPBiE-GiKoN-6	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-26T13:25:30.684Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"2da655a038ce0e2b","lastLogin":"2025-04-26T13:25:30.683Z","location":"ประเทศไทย","lastActivity":"2025-04-26T13:25:30.683Z"}	2025-05-26 13:25:31
0GbQLyIWMtnWncwj_eW3pdXLbTkrbVFq	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-26T13:25:30.701Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"f13e42d15670686a","lastLogin":"2025-04-26T13:25:30.698Z","location":"ประเทศไทย","lastActivity":"2025-04-26T13:25:30.698Z"}	2025-05-26 13:25:31
JBlzZWLDa3pS9GBolGch_WwstLCstmuL	{"cookie":{"originalMaxAge":2591999999,"expires":"2025-05-29T18:42:50.592Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":1},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-29T17:43:42.593Z","location":"ประเทศไทย","fingerprints":["ab8a1dbe6ff6"],"lastActivity":"2025-04-29T18:42:50.592Z","loginTime":"2025-04-29T17:43:43.047Z"}	2025-05-29 18:42:51
ZEBHmLJHNGarI3HVZwLKz0bLVnIGHIR3	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T16:25:36.914Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-29T16:25:36.914Z","location":"ประเทศไทย","lastActivity":"2025-04-29T16:25:36.914Z"}	2025-05-29 16:25:37
194XJXmHRkNGTkmYy-r7i4uvpdHW1dNT	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T17:32:00.323Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-29T17:32:00.323Z","location":"ประเทศไทย","lastActivity":"2025-04-29T17:32:00.323Z"}	2025-05-29 17:32:01
qtbeXeaS71xG6ptoDAot8s7MofOSotqJ	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T06:04:40.326Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T06:04:40.323Z","location":"ประเทศไทย","lastActivity":"2025-04-29T06:04:40.323Z"}	2025-05-29 06:04:41
eD3q_oROz-PYLmRj3UhlaTlvYdlpDw-1	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T06:04:40.404Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T06:04:40.402Z","location":"ประเทศไทย","lastActivity":"2025-04-29T06:04:40.402Z"}	2025-05-29 06:04:41
bP60bUNRaiMzO9yel5nGK7WWxaXOqUZ_	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T16:25:36.918Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-29T16:25:36.915Z","location":"ประเทศไทย","lastActivity":"2025-04-29T16:25:36.915Z"}	2025-05-29 16:25:37
I9elMsPxDwswa4VjTBrUGkoJyUpEpPJv	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T16:25:35.654Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":1},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-29T16:25:35.353Z","location":"ประเทศไทย","fingerprints":["ab8a1dbe6ff6"],"lastActivity":"2025-04-29T16:25:35.653Z","loginTime":"2025-04-29T16:25:35.653Z"}	2025-05-29 16:25:36
k6FO-BO03NM4li5A_6IgLs_l6S7H5sHB	{"cookie":{"originalMaxAge":2591999987,"expires":"2025-05-29T18:29:05.431Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/118.0.5993.88 Safari/537.36","fingerprint":"ee67e1c4c157","lastLogin":"2025-04-29T04:46:52.837Z","location":"ประเทศไทย","lastActivity":"2025-04-29T18:29:05.443Z"}	2025-05-29 18:29:06
qsIz66PzdIHVX9Okxr_0YakfJGrZbVdE	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T17:32:00.333Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-29T17:32:00.333Z","location":"ประเทศไทย","lastActivity":"2025-04-29T17:32:00.333Z"}	2025-05-29 17:32:01
qYtV84nDSbTs-QzINGaf4eLz0Tcphxac	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T06:33:24.655Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"127.0.0.1","userAgent":"curl/8.11.1","fingerprint":"d400f0c5e29c","lastLogin":"2025-04-29T06:33:23.801Z","location":"ประเทศไทย","lastActivity":"2025-04-29T06:33:23.801Z"}	2025-05-29 06:33:25
AylLNIKgWiNd2Y0Nyzxkp6vbe4R_QfRz	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T16:25:36.950Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-29T16:25:36.948Z","location":"ประเทศไทย","lastActivity":"2025-04-29T16:25:36.948Z"}	2025-05-29 16:25:37
_iE0OWufHctCr8_1nu6Vm5nUWFXZHmQQ	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-27T08:58:46.282Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":1}}	2025-05-27 08:58:47
iULOl1JkBM6sR_fXnsUzh1A4Z7v1lkJj	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T14:43:24.508Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"127.0.0.1","userAgent":"curl/8.11.1","fingerprint":"d400f0c5e29c","lastLogin":"2025-04-29T14:43:24.508Z","location":"ประเทศไทย","lastActivity":"2025-04-29T14:43:24.508Z"}	2025-05-29 14:43:25
GMPW7RL-J3qdipIaZKZqU92UofSdmP7A	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-28T16:55:57.948Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":1},"ip":"127.0.0.1","userAgent":"curl/8.11.1","fingerprint":"d400f0c5e29c","lastLogin":"2025-04-28T16:55:52.433Z","location":"ประเทศไทย","fingerprints":["d400f0c5e29c"],"lastActivity":"2025-04-28T16:55:57.881Z"}	2025-05-28 16:55:58
D5eH0GH4JE1elr_NPDuy_Aq6Cxa_eGxg	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-26T14:51:14.394Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"3d98d6c5a9fc2434","lastLogin":"2025-04-26T14:51:14.394Z","location":"ประเทศไทย","lastActivity":"2025-04-26T14:51:14.394Z"}	2025-05-26 14:51:15
x09GmZkALtWstig8ZbRcnTaV5H_aJZKc	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-26T14:51:14.406Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"f13e42d15670686a","lastLogin":"2025-04-26T14:51:14.406Z","location":"ประเทศไทย","lastActivity":"2025-04-26T14:51:14.406Z"}	2025-05-26 14:51:15
w7rMHULBcBlzHJtqCSALhh6mfg-069xS	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-28T17:42:31.825Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":1}}	2025-05-28 17:42:32
roiMF8oaPJo3UgQqtHpsiRuy7qRg3_08	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-26T14:51:14.391Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"2da655a038ce0e2b","lastLogin":"2025-04-26T14:51:14.381Z","location":"ประเทศไทย","lastActivity":"2025-04-26T14:51:14.381Z"}	2025-05-26 14:51:15
u5wdbkpSXhe8hyYbAlMKxvp6fwV8fJ2d	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-26T14:51:14.670Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"2da655a038ce0e2b","lastLogin":"2025-04-26T14:51:14.660Z","location":"ประเทศไทย","lastActivity":"2025-04-26T14:51:14.660Z"}	2025-05-26 14:51:15
j6FKth1tam75x4Sq182299gfmgL8P_Si	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-26T14:51:14.764Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"f13e42d15670686a","lastLogin":"2025-04-26T14:51:14.764Z","location":"ประเทศไทย","lastActivity":"2025-04-26T14:51:14.764Z"}	2025-05-26 14:51:15
YHxL4owxQpMy3ncARPrCMATCEDtbSG5v	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-28T17:52:28.798Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":1}}	2025-05-28 17:52:29
oQtwqFocLzg_k_77_YMz1Hwym3oevdl-	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-26T14:51:14.996Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"2da655a038ce0e2b","lastLogin":"2025-04-26T14:51:14.993Z","location":"ประเทศไทย","lastActivity":"2025-04-26T14:51:14.993Z"}	2025-05-26 14:51:15
GJCuMQiaHW4XpdiFb79-nnzyCeZHJYW6	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-28T17:55:49.804Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":1},"ip":"127.0.0.1","userAgent":"curl/8.11.1","fingerprint":"d400f0c5e29c","lastLogin":"2025-04-28T17:55:21.729Z","location":"ประเทศไทย","fingerprints":["d400f0c5e29c"],"lastActivity":"2025-04-28T17:55:49.797Z","loginTime":"2025-04-28T17:55:31.218Z"}	2025-05-28 17:55:50
LmQTx4bV8NB9NtuvuqA7DVjL7gqb7C4V	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T14:45:08.049Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"127.0.0.1","userAgent":"curl/8.11.1","fingerprint":"d400f0c5e29c","lastLogin":"2025-04-29T14:45:08.049Z","location":"ประเทศไทย","lastActivity":"2025-04-29T14:45:08.049Z"}	2025-05-29 14:45:09
zV1SutwvFYYDAdrCfXTFah5uGfWwIDGC	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T17:32:00.327Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-29T17:32:00.325Z","location":"ประเทศไทย","lastActivity":"2025-04-29T17:32:00.325Z"}	2025-05-29 17:32:01
c8oV_tYmhn1PCv104ZWl4JNCV2Mx_HiI	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T17:31:46.805Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":1},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-29T17:31:46.524Z","location":"ประเทศไทย","fingerprints":["ab8a1dbe6ff6"],"lastActivity":"2025-04-29T17:31:46.805Z","loginTime":"2025-04-29T17:31:46.524Z"}	2025-05-29 17:31:47
z1uoJkrSkCLm4otKvXMwZKRooh9cJJKn	{"cookie":{"originalMaxAge":2591999981,"expires":"2025-05-29T14:48:26.693Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":1},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-28T18:19:41.739Z","location":"ประเทศไทย","fingerprints":["ab8a1dbe6ff6"],"lastActivity":"2025-04-29T14:48:26.711Z","loginTime":"2025-04-28T18:19:42.300Z"}	2025-05-29 14:48:27
eqUGgijYiaue7yY-f0o4_yS17KHfaa-N	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-26T18:16:22.045Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"f283e277aedf93b4","lastLogin":"2025-04-26T18:16:21.355Z","location":"ประเทศไทย","lastActivity":"2025-04-26T18:16:22.034Z"}	2025-05-26 18:16:23
Sdw0znhUZhFazrp9oP0qNJFvjJji9dop	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T14:56:50.338Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-29T14:56:50.335Z","location":"ประเทศไทย","lastActivity":"2025-04-29T14:56:50.335Z"}	2025-05-29 14:56:51
TYFK08erBWNs6CPyMKSlp7A2_2As7l9b	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T14:56:50.902Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-29T14:56:50.899Z","location":"ประเทศไทย","lastActivity":"2025-04-29T14:56:50.899Z"}	2025-05-29 14:56:51
oY7jHPdisF1KO6LFS_qodjfdn9ixUz6x	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-26T18:16:21.370Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"3d98d6c5a9fc2434","lastLogin":"2025-04-26T18:16:21.367Z","location":"ประเทศไทย","lastActivity":"2025-04-26T18:16:21.368Z"}	2025-05-26 18:16:22
mQ4RJ-xax2eISoZT_uiRaAhpoUOV9ShR	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T14:56:50.867Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-29T14:56:50.867Z","location":"ประเทศไทย","lastActivity":"2025-04-29T14:56:50.867Z"}	2025-05-29 14:56:51
Hqxor-faSM6aBDYw6Lz9to8f3TI62bcf	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T14:57:17.032Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-29T14:57:17.031Z","location":"ประเทศไทย","lastActivity":"2025-04-29T14:57:17.031Z"}	2025-05-29 14:57:18
mEMuwLpHLq8Ok8B7l0bmEAWAfSZVQ2le	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-27T09:10:29.312Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"9d75dbeb2d3d4147","lastLogin":"2025-04-27T09:10:29.311Z","location":"ประเทศไทย","lastActivity":"2025-04-27T09:10:29.311Z"}	2025-05-27 09:10:30
NzrxutsULAG2CHZj26GbT8eZVIigQRQG	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-27T09:10:29.318Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"e3ecdca8c7ad28cb","lastLogin":"2025-04-27T09:10:29.314Z","location":"ประเทศไทย","lastActivity":"2025-04-27T09:10:29.315Z"}	2025-05-27 09:10:30
R1XU2k6tbx5mupxYu0s2U6dtcJ4XhItZ	{"cookie":{"originalMaxAge":2591999992,"expires":"2025-05-27T09:10:02.226Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":1},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"df53f352c27976c6","lastLogin":"2025-04-26T13:22:07.712Z","location":"ประเทศไทย","fingerprints":["9d75dbeb2d3d4147","797481c955ec00a4","e3ecdca8c7ad28cb","df53f352c27976c6","a64c5ca7df3cc810"],"lastActivity":"2025-04-27T09:10:02.234Z","loginTime":"2025-04-26T13:22:08.033Z"}	2025-05-27 09:10:03
PKJROY2CrtELhc3DZanLq4gI9T4wkRkE	{"cookie":{"originalMaxAge":2591999996,"expires":"2025-05-26T18:41:09.865Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/118.0.5993.88 Safari/537.36","fingerprint":"12b1554963f91603","lastLogin":"2025-04-26T13:32:58.650Z","location":"ประเทศไทย","lastActivity":"2025-04-26T18:41:09.865Z"}	2025-05-26 18:41:10
2tFnwMiXcPs-Zvbg-6Me_5sN9k-XrNbK	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-27T08:34:23.599Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/118.0.5993.88 Safari/537.36","fingerprint":"c8c6aba53097d2f3","lastLogin":"2025-04-27T08:34:22.984Z","location":"ประเทศไทย","lastActivity":"2025-04-27T08:34:23.598Z"}	2025-05-27 08:34:24
rdpEKtOoFEegyi3kCjYt0YqQIWVa8evv	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-28T17:59:00.066Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":1},"ip":"127.0.0.1","userAgent":"curl/8.11.1","fingerprint":"d400f0c5e29c","lastLogin":"2025-04-28T17:59:00.044Z","location":"ประเทศไทย","fingerprints":["d400f0c5e29c"],"lastActivity":"2025-04-28T17:59:00.044Z"}	2025-05-28 17:59:01
Vd_9mB3VweNK_zNWj_A-J8frtEvzlhvJ	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-27T15:22:27.084Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"127.0.0.1","userAgent":"curl/8.11.1","fingerprint":"d400f0c5e29c","lastLogin":"2025-04-27T15:22:24.922Z","location":"ประเทศไทย","lastActivity":"2025-04-27T15:22:24.923Z"}	2025-05-27 15:22:28
qgUSeGJ6uG_PXvHp8c60qmEew0YEpb8T	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T13:14:23.076Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"PostmanRuntime/7.43.3","fingerprint":"1c0d236c51024f08","lastLogin":"2025-04-26T15:15:03.479Z","location":"ประเทศไทย","lastActivity":"2025-04-29T13:14:19.403Z"}	2025-05-29 13:14:24
HSjUVUrQeqOvyBei1N-J4QtP26JSjHPn	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T14:57:17.068Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-29T14:57:17.065Z","location":"ประเทศไทย","lastActivity":"2025-04-29T14:57:17.065Z"}	2025-05-29 14:57:18
PxL14rafnj8wmxG31Efw7JnsrFAGhsYf	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-28T13:48:34.794Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"127.0.0.1","userAgent":"curl/8.11.1","fingerprint":"d400f0c5e29c","lastLogin":"2025-04-28T13:48:34.792Z","location":"ประเทศไทย","lastActivity":"2025-04-28T13:48:34.792Z"}	2025-05-28 13:48:35
CPBY6d7hrW_8jlz998PHDVwySQn8O7DD	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-28T13:48:47.199Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"127.0.0.1","userAgent":"curl/8.11.1","fingerprint":"d400f0c5e29c","lastLogin":"2025-04-28T13:48:47.191Z","location":"ประเทศไทย","lastActivity":"2025-04-28T13:48:47.191Z"}	2025-05-28 13:48:48
Z-mOm-KHtjPVM1ZrwFWhEdOondMMOysI	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-27T09:40:39.263Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"1e567433feee0c4a","lastLogin":"2025-04-27T09:40:39.259Z","location":"ประเทศไทย","lastActivity":"2025-04-27T09:40:39.259Z"}	2025-05-27 09:40:40
K2rUJplbXp4io5w-66xZ18uQ_LhC8q7c	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-27T09:40:39.284Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"1e567433feee0c4a","lastLogin":"2025-04-27T09:40:39.281Z","location":"ประเทศไทย","lastActivity":"2025-04-27T09:40:39.281Z"}	2025-05-27 09:40:40
ajiUkw2dQyjxX43EeB2PeJRhREq2_dit	{"cookie":{"originalMaxAge":2591999997,"expires":"2025-05-27T09:40:37.066Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":1},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"e3ecdca8c7ad28cb","lastLogin":"2025-04-27T09:10:46.023Z","location":"ประเทศไทย","fingerprints":["a64c5ca7df3cc810","797481c955ec00a4","e3ecdca8c7ad28cb","df53f352c27976c6","1e567433feee0c4a"],"lastActivity":"2025-04-27T09:40:37.069Z","loginTime":"2025-04-27T09:10:46.023Z"}	2025-05-27 09:40:38
wEbff924TuxfO-2nGHaahaekwsimE1nf	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T14:57:17.040Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-29T14:57:17.038Z","location":"ประเทศไทย","lastActivity":"2025-04-29T14:57:17.038Z"}	2025-05-29 14:57:18
ZoX3KNWmS9xIinWJ9HzILA2KW_2htCZq	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T14:57:15.308Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":1},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-29T14:57:14.998Z","location":"ประเทศไทย","fingerprints":["ab8a1dbe6ff6"],"lastActivity":"2025-04-29T14:57:15.308Z","loginTime":"2025-04-29T14:57:15.308Z"}	2025-05-29 14:57:16
JZjawdud71j3mrqFNZd5PVFd2FY5wMA7	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-27T15:22:44.438Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"127.0.0.1","userAgent":"curl/8.11.1","fingerprint":"d400f0c5e29c","lastLogin":"2025-04-27T15:22:44.184Z","location":"ประเทศไทย","lastActivity":"2025-04-27T15:22:44.184Z"}	2025-05-27 15:22:45
ohugegzmPQxf4kHNyf5IDVx199YW6hvD	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-28T13:49:04.333Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"127.0.0.1","userAgent":"curl/8.11.1","fingerprint":"d400f0c5e29c","lastLogin":"2025-04-28T13:49:02.092Z","location":"ประเทศไทย","lastActivity":"2025-04-28T13:49:02.092Z"}	2025-05-28 13:49:05
x_7Xl09f2dJaSywrInybHwAQAo1QbHjO	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-28T13:49:14.114Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":1}}	2025-05-28 13:49:15
3Rv5jJwaQ4t0pGB-r63qArpde4ujGQ1U	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-28T17:59:25.882Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":1}}	2025-05-28 17:59:26
xdrkLwRQYWDyB7E7Z6pv8DoHc7tz6SgA	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-27T15:24:51.245Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"127.0.0.1","userAgent":"curl/8.11.1","fingerprint":"d400f0c5e29c","lastLogin":"2025-04-27T15:24:46.348Z","location":"ประเทศไทย","lastActivity":"2025-04-27T15:24:46.348Z"}	2025-05-27 15:24:52
n22gEGwTr6sHCEayAn8h9ywMwrzmRGzR	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-28T18:08:27.569Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":1}}	2025-05-28 18:08:28
M8g6dZqmO9PtxMkl5J0UjM4Wn-JmFwYw	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T14:57:45.554Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-29T14:57:45.554Z","location":"ประเทศไทย","lastActivity":"2025-04-29T14:57:45.554Z"}	2025-05-29 14:57:46
V9BLpjtIp2X6-B38n54DrFNjPjJ8rOGb	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T14:57:45.556Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-29T14:57:45.555Z","location":"ประเทศไทย","lastActivity":"2025-04-29T14:57:45.556Z"}	2025-05-29 14:57:46
wzysK-T7JfOJ7cbNqUMpMyXN4YH4ehoi	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T14:57:43.777Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":1},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-29T14:57:43.478Z","location":"ประเทศไทย","fingerprints":["ab8a1dbe6ff6"],"lastActivity":"2025-04-29T14:57:43.777Z","loginTime":"2025-04-29T14:57:43.777Z"}	2025-05-29 14:57:44
Rk_iAhMwHJrEdJDGFeqlHZw_hdVSkvcP	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T14:57:45.601Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-29T14:57:45.587Z","location":"ประเทศไทย","lastActivity":"2025-04-29T14:57:45.587Z"}	2025-05-29 14:57:46
vTEEq8RB-OcWsIh-dZAegUUZtsvPDn2d	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T15:17:48.423Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-29T15:17:48.422Z","location":"ประเทศไทย","lastActivity":"2025-04-29T15:17:48.422Z"}	2025-05-29 15:17:49
up9q11BxRKE3gNHzSMTdRtX7y0iK36lk	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T15:17:47.202Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":1},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-29T15:17:46.277Z","location":"ประเทศไทย","fingerprints":["ab8a1dbe6ff6"],"lastActivity":"2025-04-29T15:17:47.202Z","loginTime":"2025-04-29T15:17:46.596Z"}	2025-05-29 15:17:48
ZdUb7i90BQf7B44ymqjZ-USNfVzqeT-w	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T15:17:48.427Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-29T15:17:48.425Z","location":"ประเทศไทย","lastActivity":"2025-04-29T15:17:48.425Z"}	2025-05-29 15:17:49
sLtOVhQZLe-18DbTgVT5qKSdTVVZvzx1	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T15:17:48.459Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-29T15:17:48.457Z","location":"ประเทศไทย","lastActivity":"2025-04-29T15:17:48.457Z"}	2025-05-29 15:17:49
aYQPxK7MtZ1v0I9KoArURFxcU1hk-Bop	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T15:18:17.770Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-29T15:18:17.770Z","location":"ประเทศไทย","lastActivity":"2025-04-29T15:18:17.770Z"}	2025-05-29 15:18:18
zsxoj4OVuPmW0k3_qJFSl5H3ykj6NDNz	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T15:18:10.251Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":1},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-29T15:18:09.957Z","location":"ประเทศไทย","fingerprints":["ab8a1dbe6ff6"],"lastActivity":"2025-04-29T15:18:10.251Z","loginTime":"2025-04-29T15:18:10.251Z"}	2025-05-29 15:18:11
FUtIi9Jr3vNfF-u0V75fTi80RuNg9YS1	{"cookie":{"originalMaxAge":2591999995,"expires":"2025-05-27T17:41:39.631Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/118.0.5993.88 Safari/537.36","fingerprint":"c8c6aba53097d2f3","lastLogin":"2025-04-27T08:34:18.671Z","location":"ประเทศไทย","lastActivity":"2025-04-27T17:41:39.633Z"}	2025-05-27 17:41:40
HoJQwpJuqo1TQvdJt9MaZ5EyfjbeqtVi	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T15:18:17.774Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-29T15:18:17.772Z","location":"ประเทศไทย","lastActivity":"2025-04-29T15:18:17.772Z"}	2025-05-29 15:18:18
IdGLaf7J1IX1EEhn-5yokKwVgPx-piTq	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T15:18:17.803Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-29T15:18:17.801Z","location":"ประเทศไทย","lastActivity":"2025-04-29T15:18:17.801Z"}	2025-05-29 15:18:18
wfp0ajQW37HIKlwtErQZBuQ6c0TmwiQO	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-28T18:19:17.866Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-28T18:19:17.866Z","location":"ประเทศไทย","lastActivity":"2025-04-28T18:19:17.866Z"}	2025-05-28 18:19:18
aGfWk6Sfdq_y1IArkvlsFhNSSO2Jm-pN	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-28T18:19:17.861Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-28T18:19:17.860Z","location":"ประเทศไทย","lastActivity":"2025-04-28T18:19:17.860Z"}	2025-05-28 18:19:18
vNYqHkHzpPC8xZcpW6h14dUEimuV3A-L	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-28T18:19:17.872Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-28T18:19:17.868Z","location":"ประเทศไทย","lastActivity":"2025-04-28T18:19:17.868Z"}	2025-05-28 18:19:18
NiYhwt4gBLciY4IGyCOvHbeGH_Hie0_A	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-28T18:19:17.863Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-28T18:19:17.863Z","location":"ประเทศไทย","lastActivity":"2025-04-28T18:19:17.863Z"}	2025-05-28 18:19:18
ybmc67O7bElg_qQrJD9WtdNuEuJXqiTp	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-28T18:19:18.157Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-28T18:19:18.154Z","location":"ประเทศไทย","lastActivity":"2025-04-28T18:19:18.154Z"}	2025-05-28 18:19:19
AN4FVfARcqL9F4tnS6S9wyhYLNMR58Hj	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-28T18:19:17.865Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Replit/1.0.14 Chrome/124.0.6367.119 Electron/30.0.3 Safari/537.36","fingerprint":"ab8a1dbe6ff6","lastLogin":"2025-04-28T18:19:17.864Z","location":"ประเทศไทย","lastActivity":"2025-04-28T18:19:17.864Z"}	2025-05-28 18:19:18
TmUw06qt9zmVumIBIFcAhbefRN3DpBGt	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T04:46:52.833Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/118.0.5993.88 Safari/537.36","fingerprint":"ee67e1c4c157","lastLogin":"2025-04-29T04:46:52.833Z","location":"ประเทศไทย","lastActivity":"2025-04-29T04:46:52.833Z"}	2025-05-29 04:46:53
xH_Rc_HCETKkk5nia1hHXRg6vA8mZK3O	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T05:00:58.689Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T05:00:58.688Z","location":"ประเทศไทย","lastActivity":"2025-04-29T05:00:58.689Z"}	2025-05-29 05:00:59
aMQO7EOesPZG7tD41xWf-Ng4BHq98q86	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T05:00:58.950Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T05:00:58.946Z","location":"ประเทศไทย","lastActivity":"2025-04-29T05:00:58.946Z"}	2025-05-29 05:00:59
jz_Zr75jV0t03EU8TE5rs1WJ-72v9EZl	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T05:00:59.048Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T05:00:59.046Z","location":"ประเทศไทย","lastActivity":"2025-04-29T05:00:59.046Z"}	2025-05-29 05:01:00
hqV1UHMQYCYsGkkMzi4Ovq0rPr4U1m5I	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T05:00:59.712Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T05:00:59.296Z","location":"ประเทศไทย","lastActivity":"2025-04-29T05:00:59.712Z"}	2025-05-29 05:01:00
Hc5_tSFiEAaEuzrA6M3QmKi4F77PBgR1	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T05:06:57.364Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T05:06:57.361Z","location":"ประเทศไทย","lastActivity":"2025-04-29T05:06:57.361Z"}	2025-05-29 05:06:58
NQ1PpB2MBVa_vBXcxcte0LpXEXw_Q9-Y	{"cookie":{"originalMaxAge":2591999996,"expires":"2025-05-28T20:46:37.000Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/118.0.5993.88 Safari/537.36","fingerprint":"ee67e1c4c157","lastLogin":"2025-04-28T13:34:45.054Z","location":"ประเทศไทย","lastActivity":"2025-04-28T20:46:36.980Z"}	2025-05-28 20:46:37
7kR7v9dGxMwt9AK5X-8ZFRMzT0e2itKr	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T04:46:52.836Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/118.0.5993.88 Safari/537.36","fingerprint":"ee67e1c4c157","lastLogin":"2025-04-29T04:46:52.836Z","location":"ประเทศไทย","lastActivity":"2025-04-29T04:46:52.836Z"}	2025-05-29 04:46:53
pccnlIlpnCoRlvcLgHvVXKT5r5kHEl-2	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T05:00:58.677Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T05:00:58.676Z","location":"ประเทศไทย","lastActivity":"2025-04-29T05:00:58.676Z"}	2025-05-29 05:00:59
agogzoIQ73fPRFLt-5F5oDMdSTqMpXez	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T05:08:17.011Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":2},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T05:08:16.942Z","location":"ประเทศไทย","fingerprints":["690bb07b4cf9"],"lastActivity":"2025-04-29T05:08:17.011Z","loginTime":"2025-04-29T05:08:17.011Z"}	2025-05-29 05:08:18
Yq3uMsko9aumQk49Qeo7qI3AbATsvE5r	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T05:06:55.258Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":1},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T05:05:12.781Z","location":"ประเทศไทย","fingerprints":["690bb07b4cf9"],"lastActivity":"2025-04-29T05:06:55.257Z","loginTime":"2025-04-29T05:05:12.781Z"}	2025-05-29 05:06:56
rW9Ebl5Y68EVNenVSF2ouWn9EQqtAGs9	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T05:00:58.674Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T05:00:58.666Z","location":"ประเทศไทย","lastActivity":"2025-04-29T05:00:58.666Z"}	2025-05-29 05:00:59
Xor5HUrxOU51_Sz9i4kvAssvIA-fBbP5	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T05:06:57.577Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T05:06:57.575Z","location":"ประเทศไทย","lastActivity":"2025-04-29T05:06:57.575Z"}	2025-05-29 05:06:58
lEMsupfj7qu78RZo7T6Zx7VPvBZSPP2S	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T05:06:57.829Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T05:06:57.827Z","location":"ประเทศไทย","lastActivity":"2025-04-29T05:06:57.827Z"}	2025-05-29 05:06:58
i76pD0slxh4_fAe6hiin-yKwt5QkKGMi	{"cookie":{"originalMaxAge":2591999992,"expires":"2025-05-28T20:46:56.005Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":1},"ip":"***********","userAgent":"Mozilla/5.0 (Linux; Android 13; SM-G988B Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/135.0.7049.111 Mobile Safari/537.36 Replit-Bonsai/2.135.3 (samsung/z3sxxx/z3s:13/TP1A.220624.014/G988BXXUIHWH9:user/release-keys 13)","fingerprint":"1215d38d0743","lastLogin":"2025-04-28T06:40:41.883Z","location":"ประเทศไทย","fingerprints":["1215d38d0743"],"lastActivity":"2025-04-28T20:46:55.989Z","loginTime":"2025-04-28T06:40:42.238Z"}	2025-05-28 20:46:57
QtX03NhhM4ln-PhSbFemdQ4fUDVrefOd	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T05:08:02.748Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T05:08:02.748Z","location":"ประเทศไทย","lastActivity":"2025-04-29T05:08:02.748Z"}	2025-05-29 05:08:03
EK4JiCamqd5zbzAuNc5m1K_VMFg6aWkz	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T05:08:23.261Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T05:08:16.700Z","location":"ประเทศไทย","lastActivity":"2025-04-29T05:08:23.261Z"}	2025-05-29 05:08:24
PhzhXCba8zV5fM1Cx1doibwqNY6vWCvX	{"cookie":{"originalMaxAge":2591999976,"expires":"2025-05-28T23:32:49.725Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"passport":{"user":2},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-24T20:36:42.143Z","location":"ประเทศไทย","fingerprints":["a4c98a74c5a3ebaa","40a2e0643c2b9f70","e6e8d0c018f7e0f7","d8b3ef97da475e2c","690bb07b4cf9"],"lastActivity":"2025-04-28T23:32:49.749Z","loginTime":"2025-04-24T20:36:42.931Z"}	2025-05-28 23:32:50
AZmxU9jHkdbNwjl3K2JNJmOkOUGsloHq	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T04:46:52.698Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/118.0.5993.88 Safari/537.36","fingerprint":"ee67e1c4c157","lastLogin":"2025-04-29T04:46:52.696Z","location":"ประเทศไทย","lastActivity":"2025-04-29T04:46:52.696Z"}	2025-05-29 04:46:53
_nVHXEvT97YZkq_PYv_4eX8n5ZeDrxMp	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T05:06:57.298Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T05:06:57.297Z","location":"ประเทศไทย","lastActivity":"2025-04-29T05:06:57.298Z"}	2025-05-29 05:06:58
BDWsfy19wsCWVYx7_6_DhjR1G_chrkiw	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T05:00:58.669Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T05:00:58.668Z","location":"ประเทศไทย","lastActivity":"2025-04-29T05:00:58.668Z"}	2025-05-29 05:00:59
MdLB9As3uzLl2wA8gYtNK_0jehwVWCWQ	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T05:06:57.299Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T05:06:57.298Z","location":"ประเทศไทย","lastActivity":"2025-04-29T05:06:57.298Z"}	2025-05-29 05:06:58
nB3dDO88IrMevLnCHCD0rSjUMGIeMyCq	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T05:00:58.673Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T05:00:58.673Z","location":"ประเทศไทย","lastActivity":"2025-04-29T05:00:58.673Z"}	2025-05-29 05:00:59
jvwnxsQ_e3L0jmvACPOVvXzlF2w9FVjR	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T05:08:20.246Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T05:08:16.647Z","location":"ประเทศไทย","lastActivity":"2025-04-29T05:08:20.246Z"}	2025-05-29 05:08:21
Epl4VQvT96biRB6A9EeiQyebD4W1O1ng	{"cookie":{"originalMaxAge":2591999999,"expires":"2025-05-29T05:08:27.461Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T05:08:16.624Z","location":"ประเทศไทย","lastActivity":"2025-04-29T05:08:27.462Z"}	2025-05-29 05:08:28
uNNdllVrw48BiWCAzWC51xm3Xuw31t1V	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T05:08:27.020Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"***********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T05:08:16.601Z","location":"ประเทศไทย","lastActivity":"2025-04-29T05:08:27.020Z"}	2025-05-29 05:08:28
9jC6FtFDjnv5uviOU26HQbB6bamTr7DJ	{"cookie":{"originalMaxAge":**********,"expires":"2025-05-29T05:08:18.557Z","secure":false,"httpOnly":true,"path":"/","sameSite":"lax"},"ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","fingerprint":"690bb07b4cf9","lastLogin":"2025-04-29T05:08:16.671Z","location":"ประเทศไทย","lastActivity":"2025-04-29T05:08:18.557Z"}	2025-05-29 05:08:19
\.


--
-- Data for Name: slip_verifications; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.slip_verifications (id, user_id, transaction_ref, bank_name, amount, sender, receiver, transaction_date, status, response_data, credit_used, used_credit, verification_source, api_key_id, image_path, created_at, qr_data) FROM stdin;
74	1	015098144654COR09084	ธนาคารกสิกรไทย	1200	Mr. Jirayut S	นาง เกวลิน สมนึก	2025-04-08 07:46:54	success	{"originalFilename":"13419_0 - Copy.jpg","savedFilename":"slip_image-*************-*********.jpg","filePath":"tmp/uploads/slip_image-*************-*********.jpg","fileSize":139271,"mimeType":"image/jpeg","processingTime":3920,"providerUsed":"slipkuy","overQuotaRequest":false,"creditDeducted":0,"qrData":"0041000600000101030040220015098144654COR090845102TH9104DD8A","status":200,"data":{"payload":"0041000600000101030040220015098144654COR090845102TH9104DD8A","transRef":"015098144654COR09084","date":"2025-04-08T14:46:54+07:00","countryCode":"TH","amount":{"amount":1200,"local":{"amount":1200,"currency":"THB"}},"sender":{"bank":{"id":"004","name":"ธนาคารกสิกรไทย","short":""},"account":{"name":{"th":"Mr. Jirayut S","en":""},"bank":{"type":"BANKAC","account":"xxx-x-x4800-x"}}},"receiver":{"bank":{"id":"030","name":"ธนาคารออมสิน","short":""},"account":{"name":{"th":"นาง เกวลิน สมนึก","en":""},"bank":{"type":"BANKAC","account":"xxx-x-x1516-xxx"}}}},"apiProvider":"slipkuy"}	0	f	api	4	/uploads/slips/slip_image-1-*************.jpg	2025-04-29 12:53:17.444395	0041000600000101030040220015098144654COR090845102TH9104DD8A
82	1	015098144654COR09084	ธนาคารกสิกรไทย	1200	Mr. Jirayut S	นาง เกวลิน สมนึก	2025-04-08 07:46:54	success	{"originalFilename":"13419_0 - Copy.jpg","savedFilename":"slip_image-*************-********.jpg","filePath":"tmp/uploads/slip_image-*************-********.jpg","fileSize":139271,"mimeType":"image/jpeg","processingTime":3296,"providerUsed":"slipkuy","overQuotaRequest":false,"creditDeducted":0,"qrData":"0041000600000101030040220015098144654COR090845102TH9104DD8A","status":200,"data":{"payload":"0041000600000101030040220015098144654COR090845102TH9104DD8A","transRef":"015098144654COR09084","date":"2025-04-08T14:46:54+07:00","countryCode":"TH","amount":{"amount":1200,"local":{"amount":1200,"currency":"THB"}},"sender":{"bank":{"id":"004","name":"ธนาคารกสิกรไทย","short":""},"account":{"name":{"th":"Mr. Jirayut S","en":""},"bank":{"type":"BANKAC","account":"xxx-x-x4800-x"}}},"receiver":{"bank":{"id":"030","name":"ธนาคารออมสิน","short":""},"account":{"name":{"th":"นาง เกวลิน สมนึก","en":""},"bank":{"type":"BANKAC","account":"xxx-x-x1516-xxx"}}}},"apiProvider":"slipkuy"}	0	f	api	1	/uploads/slips/slip_image-1-*************.jpg	2025-04-29 13:12:13.140565	0041000600000101030040220015098144654COR090845102TH9104DD8A
75	1	015098144654COR09084	ธนาคารกสิกรไทย	1200	Mr. Jirayut S	นาง เกวลิน สมนึก	2025-04-08 07:46:54	success	{"originalFilename":"13419_0 - Copy.jpg","savedFilename":"slip_image-*************-*********.jpg","filePath":"tmp/uploads/slip_image-*************-*********.jpg","fileSize":139271,"mimeType":"image/jpeg","processingTime":3770,"providerUsed":"slipkuy","overQuotaRequest":false,"creditDeducted":0,"qrData":"0041000600000101030040220015098144654COR090845102TH9104DD8A","status":200,"data":{"payload":"0041000600000101030040220015098144654COR090845102TH9104DD8A","transRef":"015098144654COR09084","date":"2025-04-08T14:46:54+07:00","countryCode":"TH","amount":{"amount":1200,"local":{"amount":1200,"currency":"THB"}},"sender":{"bank":{"id":"004","name":"ธนาคารกสิกรไทย","short":""},"account":{"name":{"th":"Mr. Jirayut S","en":""},"bank":{"type":"BANKAC","account":"xxx-x-x4800-x"}}},"receiver":{"bank":{"id":"030","name":"ธนาคารออมสิน","short":""},"account":{"name":{"th":"นาง เกวลิน สมนึก","en":""},"bank":{"type":"BANKAC","account":"xxx-x-x1516-xxx"}}}},"apiProvider":"slipkuy"}	0	f	api	4	/uploads/slips/slip_image-1-*************.jpg	2025-04-29 12:53:28.936451	0041000600000101030040220015098144654COR090845102TH9104DD8A
83	1	015098144654COR09084	ธนาคารกสิกรไทย	1200	Mr. Jirayut S	นาง เกวลิน สมนึก	2025-04-08 07:46:54	success	{"status":200,"data":{"payload":"0041000600000101030040220015098144654COR090845102TH9104DD8A","transRef":"015098144654COR09084","date":"2025-04-08T14:46:54+07:00","countryCode":"TH","amount":{"amount":1200,"local":{"amount":1200,"currency":"THB"}},"sender":{"bank":{"id":"004","name":"ธนาคารกสิกรไทย","short":""},"account":{"name":{"th":"Mr. Jirayut S","en":""},"bank":{"type":"BANKAC","account":"xxx-x-x4800-x"}}},"receiver":{"bank":{"id":"030","name":"ธนาคารออมสิน","short":""},"account":{"name":{"th":"นาง เกวลิน สมนึก","en":""},"bank":{"type":"BANKAC","account":"xxx-x-x1516-xxx"}}}},"apiProvider":"slipkuy"}	\N	f	web	\N	/uploads/slips/slip-1-83-*************.png	2025-04-29 13:12:55.768488	0041000600000101030040220015098144654COR090845102TH9104DD8A
76	1	\N		0			\N	error	{"originalFilename":"13419_0 - Copy.jpg","savedFilename":"slip_image-*************-*********.jpg","filePath":"tmp/uploads/slip_image-*************-*********.jpg","fileSize":139271,"mimeType":"image/jpeg","processingTime":1003,"providerUsed":"unknown","overQuotaRequest":false,"creditDeducted":0,"qrData":"0041000600000101030040220015098144654COR090845102TH9104DD8A","status":409,"message":"สลิปฉบับนี้ถูกตรวจสอบไปแล้วเมื่อ 29/4/2568 19:53:28","data":{"transactionRef":"015098144654COR09084","amount":1200,"verifiedAt":"2025-04-29T12:53:28.936Z"}}	0	f	api	4	/uploads/slips/slip_image-1-*************.jpg	2025-04-29 13:05:40.604607	0041000600000101030040220015098144654COR090845102TH9104DD8A
78	1	\N		0			\N	error	{"originalFilename":"13419_0 - Copy.jpg","savedFilename":"slip_image-1745931961897-838687216.jpg","filePath":"tmp/uploads/slip_image-1745931961897-838687216.jpg","fileSize":139271,"mimeType":"image/jpeg","processingTime":1678,"providerUsed":"unknown","overQuotaRequest":false,"creditDeducted":0,"qrData":"0041000600000101030040220015098144654COR090845102TH9104DD8A","status":409,"message":"สลิปฉบับนี้ถูกตรวจสอบไปแล้วเมื่อ 29/4/2568 20:05:47","data":{"transactionRef":null,"amount":0,"verifiedAt":"2025-04-29T13:05:47.214Z"}}	0	f	api	4	/uploads/slips/slip_image-1-1745931963676.jpg	2025-04-29 13:06:03.692859	0041000600000101030040220015098144654COR090845102TH9104DD8A
84	1	015088083128BPP04696	ธนาคารกสิกรไทย	175	นาย จิรายุ ส	นาง ละเมียด ทิพรักษ์	2025-03-29 01:31:28	success	{"status":200,"data":{"payload":"0041000600000101030040220015088083128BPP046965102TH910488AA","transRef":"015088083128BPP04696","date":"2025-03-29T08:31:28+07:00","countryCode":"TH","amount":{"amount":175,"local":{"amount":175,"currency":"THB"}},"sender":{"bank":{"id":"004","name":"ธนาคารกสิกรไทย","short":""},"account":{"name":{"th":"นาย จิรายุ ส","en":""},"bank":{"type":"BANKAC","account":"xxx-x-x4800-x"}}},"receiver":{"bank":{"id":"006","name":"ธนาคารกรุงไทย","short":""},"account":{"name":{"th":"นาง ละเมียด ทิพรักษ์","en":""},"bank":{"type":"BANKAC","account":""}}}},"apiProvider":"slipkuy"}	\N	f	web	\N	/uploads/slips/slip-1-84-*************.png	2025-04-29 13:13:16.290759	0041000600000101030040220015088083128BPP046965102TH910488AA
85	1	015087201022CTF04630	ธนาคารกสิกรไทย	100	นาย จิรายุ ส	น.ส. วริอร บุญเจริญ	2025-03-28 13:10:22	success	{"originalFilename":"8712 - Copy.jpg","savedFilename":"slip_image-*************-*********.jpg","filePath":"tmp/uploads/slip_image-*************-*********.jpg","fileSize":139178,"mimeType":"image/jpeg","processingTime":3684,"providerUsed":"slipkuy","overQuotaRequest":false,"creditDeducted":0,"qrData":"0041000600000101030040220015087201022CTF046305102TH9104B581","status":200,"data":{"payload":"0041000600000101030040220015087201022CTF046305102TH9104B581","transRef":"015087201022CTF04630","date":"2025-03-28T20:10:22+07:00","countryCode":"TH","amount":{"amount":100,"local":{"amount":100,"currency":"THB"}},"sender":{"bank":{"id":"004","name":"ธนาคารกสิกรไทย","short":""},"account":{"name":{"th":"นาย จิรายุ ส","en":""},"bank":{"type":"BANKAC","account":"xxx-x-x4800-x"}}},"receiver":{"bank":{"id":"004","name":"ธนาคารกสิกรไทย","short":""},"account":{"name":{"th":"น.ส. วริอร บุญเจริญ","en":""},"bank":{"type":"BANKAC","account":"xxx-x-x4997-x"}}}},"apiProvider":"slipkuy"}	0	f	api	1	/uploads/slips/slip_image-1-*************.jpg	2025-04-29 13:13:37.317471	0041000600000101030040220015087201022CTF046305102TH9104B581
77	1	\N		0			\N	error	{"originalFilename":"13419_0 - Copy.jpg","savedFilename":"slip_image-*************-*********.jpg","filePath":"tmp/uploads/slip_image-*************-*********.jpg","fileSize":139271,"mimeType":"image/jpeg","processingTime":1048,"providerUsed":"unknown","overQuotaRequest":false,"creditDeducted":0,"qrData":"0041000600000101030040220015098144654COR090845102TH9104DD8A","status":409,"message":"สลิปฉบับนี้ถูกตรวจสอบไปแล้วเมื่อ 29/4/2568 20:05:40","data":{"transactionRef":null,"amount":0,"verifiedAt":"2025-04-29T13:05:40.604Z"}}	0	f	api	4	/uploads/slips/slip_image-1-*************.jpg	2025-04-29 13:05:47.214207	0041000600000101030040220015098144654COR090845102TH9104DD8A
86	1	015109160510CTF01567	ธนาคารกสิกรไทย	155	MR. Jirayut S	นาย จิรายุ สัตย์ซื่อ	2025-04-19 09:05:10	success	{"originalFilename":"13474.jpg","savedFilename":"slip_image-*************-*********.jpg","filePath":"tmp/uploads/slip_image-*************-*********.jpg","fileSize":138778,"mimeType":"image/jpeg","processingTime":3394,"providerUsed":"slipkuy","overQuotaRequest":false,"creditDeducted":0,"qrData":"0041000600000101030040220015109160510CTF015675102TH9104470D","status":200,"data":{"payload":"0041000600000101030040220015109160510CTF015675102TH9104470D","transRef":"015109160510CTF01567","date":"2025-04-19T16:05:10+07:00","countryCode":"TH","amount":{"amount":155,"local":{"amount":155,"currency":"THB"}},"sender":{"bank":{"id":"004","name":"ธนาคารกสิกรไทย","short":""},"account":{"name":{"th":"MR. Jirayut S","en":""},"bank":{"type":"BANKAC","account":"xxx-x-x4936-x"}}},"receiver":{"bank":{"id":"004","name":"ธนาคารกสิกรไทย","short":""},"account":{"name":{"th":"นาย จิรายุ สัตย์ซื่อ","en":""},"bank":{"type":"BANKAC","account":"xxx-x-x4800-x"}}}},"apiProvider":"slipkuy"}	0	f	api	1	/uploads/slips/slip_image-1-*************.jpg	2025-04-29 13:14:23.022359	0041000600000101030040220015109160510CTF015675102TH9104470D
72	5	015098144654COR09084	ธนาคารกสิกรไทย	1200	Mr. Jirayut S	นาง เกวลิน สมนึก	2025-04-08 07:46:54	success	{"status":200,"data":{"payload":"0041000600000101030040220015098144654COR090845102TH9104DD8A","transRef":"015098144654COR09084","date":"2025-04-08T14:46:54+07:00","countryCode":"TH","amount":{"amount":1200,"local":{"amount":1200,"currency":"THB"}},"sender":{"bank":{"id":"004","name":"ธนาคารกสิกรไทย","short":""},"account":{"name":{"th":"Mr. Jirayut S","en":""},"bank":{"type":"BANKAC","account":"xxx-x-x4800-x"}}},"receiver":{"bank":{"id":"030","name":"ธนาคารออมสิน","short":""},"account":{"name":{"th":"นาง เกวลิน สมนึก","en":""},"bank":{"type":"BANKAC","account":"xxx-x-x1516-xxx"}}}},"apiProvider":"slipkuy"}	\N	f	web	\N	/uploads/slips/slip-5-72-*************.png	2025-04-29 12:27:57.855642	0041000600000101030040220015098144654COR090845102TH9104DD8A
79	1	\N		0			\N	error	{"originalFilename":"13419_0 - Copy.jpg","savedFilename":"slip_image-*************-*********.jpg","filePath":"tmp/uploads/slip_image-*************-*********.jpg","fileSize":139271,"mimeType":"image/jpeg","processingTime":1315,"providerUsed":"unknown","overQuotaRequest":false,"creditDeducted":0,"qrData":"0041000600000101030040220015098144654COR090845102TH9104DD8A","status":409,"message":"สลิปฉบับนี้ถูกตรวจสอบไปแล้วเมื่อ 29/4/2568 20:06:03","data":{"transactionRef":null,"amount":0,"verifiedAt":"2025-04-29T13:06:03.692Z"}}	0	f	api	4	/uploads/slips/slip_image-1-*************.jpg	2025-04-29 13:06:14.879306	0041000600000101030040220015098144654COR090845102TH9104DD8A
80	1	\N		0			\N	error	{"originalFilename":"13419_0 - Copy.jpg","savedFilename":"slip_image-1745931976680-208656936.jpg","filePath":"tmp/uploads/slip_image-1745931976680-208656936.jpg","fileSize":139271,"mimeType":"image/jpeg","processingTime":1039,"providerUsed":"unknown","overQuotaRequest":false,"creditDeducted":0,"qrData":"0041000600000101030040220015098144654COR090845102TH9104DD8A","status":409,"message":"สลิปฉบับนี้ถูกตรวจสอบไปแล้วเมื่อ 29/4/2568 20:06:14","data":{"transactionRef":null,"amount":0,"verifiedAt":"2025-04-29T13:06:14.879Z"}}	0	f	api	4	/uploads/slips/slip_image-1-1745931977852.jpg	2025-04-29 13:06:17.870463	0041000600000101030040220015098144654COR090845102TH9104DD8A
73	1	015098144654COR09084	ธนาคารกสิกรไทย	1200	Mr. Jirayut S	นาง เกวลิน สมนึก	2025-04-08 07:46:54	success	{"originalFilename":"13419_0 - Copy.jpg","savedFilename":"slip_image-*************-*********.jpg","filePath":"tmp/uploads/slip_image-*************-*********.jpg","fileSize":139271,"mimeType":"image/jpeg","processingTime":3724,"providerUsed":"slipkuy","overQuotaRequest":false,"creditDeducted":0,"qrData":"0041000600000101030040220015098144654COR090845102TH9104DD8A","status":200,"data":{"payload":"0041000600000101030040220015098144654COR090845102TH9104DD8A","transRef":"015098144654COR09084","date":"2025-04-08T14:46:54+07:00","countryCode":"TH","amount":{"amount":1200,"local":{"amount":1200,"currency":"THB"}},"sender":{"bank":{"id":"004","name":"ธนาคารกสิกรไทย","short":""},"account":{"name":{"th":"Mr. Jirayut S","en":""},"bank":{"type":"BANKAC","account":"xxx-x-x4800-x"}}},"receiver":{"bank":{"id":"030","name":"ธนาคารออมสิน","short":""},"account":{"name":{"th":"นาง เกวลิน สมนึก","en":""},"bank":{"type":"BANKAC","account":"xxx-x-x1516-xxx"}}}},"apiProvider":"slipkuy"}	0	f	api	4	/uploads/slips/slip_image-1-*************.jpg	2025-04-29 12:29:12.204151	0041000600000101030040220015098144654COR090845102TH9104DD8A
81	1	015098144654COR09084	ธนาคารกสิกรไทย	1200	Mr. Jirayut S	นาง เกวลิน สมนึก	2025-04-08 07:46:54	success	{"originalFilename":"13419_0 - Copy.jpg","savedFilename":"slip_image-*************-*********.jpg","filePath":"tmp/uploads/slip_image-*************-*********.jpg","fileSize":139271,"mimeType":"image/jpeg","processingTime":3443,"providerUsed":"slipkuy","overQuotaRequest":false,"creditDeducted":0,"qrData":"0041000600000101030040220015098144654COR090845102TH9104DD8A","status":200,"data":{"payload":"0041000600000101030040220015098144654COR090845102TH9104DD8A","transRef":"015098144654COR09084","date":"2025-04-08T14:46:54+07:00","countryCode":"TH","amount":{"amount":1200,"local":{"amount":1200,"currency":"THB"}},"sender":{"bank":{"id":"004","name":"ธนาคารกสิกรไทย","short":""},"account":{"name":{"th":"Mr. Jirayut S","en":""},"bank":{"type":"BANKAC","account":"xxx-x-x4800-x"}}},"receiver":{"bank":{"id":"030","name":"ธนาคารออมสิน","short":""},"account":{"name":{"th":"นาง เกวลิน สมนึก","en":""},"bank":{"type":"BANKAC","account":"xxx-x-x1516-xxx"}}}},"apiProvider":"slipkuy"}	0	f	api	4	/uploads/slips/slip_image-1-*************.jpg	2025-04-29 13:11:49.392249	0041000600000101030040220015098144654COR090845102TH9104DD8A
\.


--
-- Data for Name: system_settings; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.system_settings (id, key, value, value_json, description, created_at, updated_at) FROM stdin;
1	slip_verification_provider	slip2go	\N	ผู้ให้บริการตรวจสอบสลิปที่ใช้งานอยู่ (slip2go, easyslip)	2025-04-24 17:53:58.050629	2025-04-24 17:53:58.050629
3	api_request_limit	1000	\N	จำนวนคำขอ API สูงสุดต่อวันโดยเฉลี่ย	2025-04-24 17:53:58.050629	2025-04-24 17:53:58.050629
4	system_maintenance	false	\N	ระบบอยู่ในช่วงบำรุงรักษาหรือไม่	2025-04-24 17:53:58.050629	2025-04-24 17:53:58.050629
5	credit_price	1	\N	ราคาของเครดิต (บาทต่อเครดิต)	2025-04-24 17:53:58.050629	2025-04-24 17:53:58.050629
6	email_settings	\N	{"auth": {"pass": "yzjecbqmurjyyxsu", "user": "<EMAIL>"}, "from": "SLIPKUY Alert <<EMAIL>>", "host": "smtp.gmail.com", "port": 465, "secure": true, "service": "gmail", "defaultName": "SLIPKUY Alert"}	การตั้งค่าระบบอีเมล	2025-04-24 18:09:00.343596	2025-04-24 18:09:00.343596
12	social_login_settings	{"enableLineLogin":false,"enableFacebookLogin":true,"facebookAppId":"1465647268109245","facebookAppSecret":"********************************","enableGoogleLogin":false}	\N	\N	2025-04-25 12:38:29.779597	2025-04-25 12:41:06.648
2	email_service_enabled	true	\N	เปิดใช้งานบริการส่งอีเมลหรือไม่	2025-04-24 17:53:58.050629	2025-04-24 17:53:58.050629
7	otp_settings	{"otpLength":6,"otpExpiry":5,"otpResendDelay":60,"smsmktApiKey":"f8359be091e9ec80e28d66e44f0a2294","smsmktSecret":"UIRhiYbCFM4miHzH","offlineSimulation":false}	\N	\N	2025-04-25 09:50:15.601243	2025-04-25 14:42:15.644
8	sms_settings	\N	{"secret": "UIRhiYbCFM4miHzH", "sender": "SLIPKUY", "api_key": "f8359be091e9ec80e28d66e44f0a2294", "offline_mode": false}	การตั้งค่าสำหรับบริการ SMS	2025-04-25 09:50:15.664197	2025-04-25 14:42:15.678
15	payment_accounts	{"bankAccounts":[{"bankCode":"004","accountNumber":"**********","accountName":"นาย จิรายุ สัตย์ซื่อ","accountNameEn":"Mr. jirayut setsue","qrPaymentImage":"data:image/png;base64,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"},{"bankCode":"006","accountNumber":"**********","accountName":"กดเกดเ กดเกดเ","accountNameEn":"dsfsdfer fdgergerg","qrPaymentImage":"data:image/png;base64,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"}],"promptpays":["**********","0123456789"]}	\N	\N	2025-04-28 16:36:52.927117	2025-04-29 18:02:38.773
\.


--
-- Data for Name: top_up_transactions; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.top_up_transactions (id, user_id, amount, status, verification_id, reference_code, created_at, updated_at) FROM stdin;
12	1	55	failed	47	SLP769149C372F7	2025-04-28 17:36:09.164892	2025-04-28 17:36:20.524
13	1	55	completed	48	SLP231541D30DAA	2025-04-28 17:43:51.559286	2025-04-28 17:44:07.096
14	1	105.75	failed	49	SLP2531816E052E	2025-04-28 18:00:53.1983	2025-04-28 18:01:05.257
15	1	105.75	completed	50	SLP28816017B0B7	2025-04-28 18:01:28.17765	2025-04-28 18:02:02.104
16	1	55	completed	52	SLP413948A3D43E	2025-04-28 18:20:13.963899	2025-04-28 18:20:30.074
17	1	1000	failed	53	SLP4340574C2838	2025-04-28 18:20:34.071684	2025-04-28 18:20:43.694
18	5	50000	success	\N	\N	2025-04-29 05:00:42.763161	2025-04-29 05:00:42.763161
19	5	-50000	success	\N	\N	2025-04-29 05:24:48.276359	2025-04-29 05:24:48.276359
20	5	5000	success	\N	\N	2025-04-29 11:23:43.676582	2025-04-29 11:23:43.676582
21	1	-4077.49	success	\N	\N	2025-04-29 14:38:11.429473	2025-04-29 14:38:11.429473
22	1	-800	success	\N	\N	2025-04-29 14:38:21.325471	2025-04-29 14:38:21.325471
23	1	1000	pending	\N	SLP9607662B58B1	2025-04-29 17:49:20.782198	2025-04-29 17:49:20.782198
24	1	1000	pending	\N	SLP006379102707	2025-04-29 17:50:06.39458	2025-04-29 17:50:06.39458
25	1	1000	pending	\N	SLP686021DB73B9	2025-04-29 18:01:26.036702	2025-04-29 18:01:26.036702
26	1	1000	pending	\N	SLP764265C472D7	2025-04-29 18:02:44.279847	2025-04-29 18:02:44.279847
27	1	1000	pending	\N	SLP009559BEA6AA	2025-04-29 18:06:49.575695	2025-04-29 18:06:49.575695
28	1	1000	pending	\N	SLP5558438A477F	2025-04-29 18:15:55.857921	2025-04-29 18:15:55.857921
\.


--
-- Data for Name: user_achievements; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.user_achievements (id, "userId", "achievementId", progress, completed, "completedAt", "rewardClaimed", "lastUpdated", "createdAt") FROM stdin;
1	1	1	1	t	\N	f	2025-04-29 10:55:38.206299+00	2025-04-29 10:55:38.206299+00
2	1	2	3	f	\N	f	2025-04-29 10:55:38.206299+00	2025-04-29 10:55:38.206299+00
3	1	3	1	t	\N	f	2025-04-29 10:55:38.206299+00	2025-04-29 10:55:38.206299+00
4	1	4	5	f	\N	f	2025-04-29 10:55:38.206299+00	2025-04-29 10:55:38.206299+00
5	1	5	0	f	\N	f	2025-04-29 10:55:38.206299+00	2025-04-29 10:55:38.206299+00
6	1	6	1	t	\N	t	2025-04-29 10:55:38.206299+00	2025-04-29 10:55:38.206299+00
7	1	7	1	t	\N	f	2025-04-29 10:55:38.206299+00	2025-04-29 10:55:38.206299+00
8	1	8	3	f	\N	f	2025-04-29 10:55:38.206299+00	2025-04-29 10:55:38.206299+00
9	2	1	1	t	\N	f	2025-04-29 11:20:11.728474+00	2025-04-29 11:20:11.728474+00
10	2	2	3	f	\N	f	2025-04-29 11:20:11.728474+00	2025-04-29 11:20:11.728474+00
11	2	3	1	t	\N	f	2025-04-29 11:20:11.728474+00	2025-04-29 11:20:11.728474+00
12	2	4	5	f	\N	f	2025-04-29 11:20:11.728474+00	2025-04-29 11:20:11.728474+00
\.


--
-- Data for Name: user_auth_logs; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.user_auth_logs (id, user_id, ip_address, user_agent, auth_method, success, fail_reason, created_at) FROM stdin;
\.


--
-- Data for Name: user_packages; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.user_packages (id, user_id, package_id, start_date, end_date, is_active, requests_used, last_quota_reset_date, duration_months, created_at, updated_at) FROM stdin;
38	1	10	2025-04-19	2025-07-19	f	0	\N	3	2025-04-19 07:37:55.039063	2025-04-19 07:38:10.069
39	1	8	2025-04-19	2025-05-19	f	0	\N	1	2025-04-19 07:38:10.145982	2025-04-19 07:38:20.448
40	1	9	2025-04-19	2025-07-19	f	0	\N	3	2025-04-19 07:38:20.526471	2025-04-19 07:38:31.353
42	1	9	2025-04-19	2025-10-19	f	0	\N	6	2025-04-19 07:39:11.721989	2025-04-19 18:00:37.563
44	2	11	2025-04-19	2025-04-26	f	0	2025-04-21	1	2025-04-19 08:23:55.096467	2025-04-21 19:06:23.515
49	1	8	2025-04-22	2025-05-22	f	21	\N	1	2025-04-22 22:45:43.553139	2025-04-23 13:51:25.677
43	1	11	2025-04-19	2025-04-26	f	0	2025-04-21	1	2025-04-19 07:47:22.791801	2025-04-21 21:21:59.18
41	1	9	2025-04-19	2025-07-19	f	0	\N	3	2025-04-19 07:38:31.432081	2025-04-21 21:22:09.565
47	1	9	2025-04-22	2033-07-22	f	32	\N	99	2025-04-21 19:27:55.929379	2025-04-22 22:43:06.517
48	1	12	2025-04-22	2025-05-22	f	0	\N	1	2025-04-22 22:45:30.188366	2025-04-22 22:45:42.968
51	2	12	2025-04-23	2026-04-23	f	0	2025-04-24	12	2025-04-23 14:11:56.124583	2025-04-24 18:55:23.026
7	1	10	2025-04-29	2026-04-29	f	10	\N	12	2025-04-29 10:23:45.181197	2025-04-29 13:47:58.54
11	1	9	2025-04-29	2025-05-29	f	0	\N	1	2025-04-29 13:47:58.60177	2025-04-29 13:48:51.982
12	1	2	2025-04-29	2025-05-29	f	0	\N	1	2025-04-29 13:48:52.045194	2025-04-29 14:06:53.749
13	1	10	2025-04-29	2026-04-29	f	0	\N	12	2025-04-29 14:06:53.817024	2025-04-29 14:09:54.403
14	1	2	2025-04-29	2025-05-29	t	0	\N	1	2025-04-29 14:09:54.465603	2025-04-29 14:09:54.465603
1	1	2	2025-04-24	2025-05-24	f	101	\N	1	2025-04-24 18:05:26.681707	2025-04-29 04:43:26.111
4	1	10	2025-04-29	2026-04-29	f	0	\N	12	2025-04-29 04:43:26.174706	2025-04-29 10:23:33.764
5	1	2	2025-04-29	2025-05-29	f	0	\N	1	2025-04-29 10:23:33.8242	2025-04-29 10:23:39.177
6	1	9	2025-04-29	2025-05-29	f	0	\N	1	2025-04-29 10:23:39.237429	2025-04-29 10:23:45.116
2	2	2	2025-04-24	2025-05-24	f	0	\N	1	2025-04-24 18:55:23.08471	2025-04-29 11:12:16.727
8	2	10	2025-04-29	2025-05-29	t	0	\N	1	2025-04-29 11:12:16.79987	2025-04-29 11:12:16.79987
3	5	11	2025-04-26	2025-05-03	f	0	2025-04-29	1	2025-04-26 15:15:12.372284	2025-04-29 11:24:08.794
9	5	12	2025-04-29	2025-05-29	f	0	\N	1	2025-04-29 11:24:08.858719	2025-04-29 11:24:10.637
10	5	2	2025-04-29	2025-05-29	t	3	\N	1	2025-04-29 11:24:10.706613	2025-04-29 12:27:57.919
\.


--
-- Data for Name: user_settings; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.user_settings (id, user_id, duplicate_slip_check, created_at, updated_at, email_notifications, theme, language) FROM stdin;
1	2	t	2025-04-27 13:41:21.923872	2025-04-27 13:41:21.923872	t	dark	th
3	3	t	2025-04-27 13:41:21.923872	2025-04-27 13:41:21.923872	t	dark	th
4	4	t	2025-04-27 13:41:21.923872	2025-04-27 13:41:21.923872	t	dark	th
5	5	t	2025-04-27 13:41:21.923872	2025-04-29 11:45:28.446	t	dark	th
2	1	t	2025-04-27 13:41:21.923872	2025-04-29 13:12:57.97	t	dark	th
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.users (id, username, password, email, first_name, last_name, company_name, phone_number, address, bio, profile_image, credit, tier, role, status, allowed_packages, created_at, updated_at, phone_verified, email_verified, auth_providers) FROM stdin;
1	tmognot	568b032f9bcd0f1b1212613b62a92b54ec2c08511361eb55438d7e3a0e90a30eb55598f942ef864533f1a7bbe89fdf6cd5b8e91a05256ec5612093f174fd35ed.dc1ec1fa3ea5ce15f356d04f13a74bc9	<EMAIL>	jirayut	setsue	ผู้สร้าง	**********	บางกรวย	"	/uploads/default-avatars/avatar-9.webp	1300000	standard	admin	active	\N	2025-04-17 19:20:28.951189	2025-04-29 17:31:37.077	f	t	{}
3	test1	09f80117c60bb619b5a98c3664bce2efcae7e524f7d12dc529ff5e54a3be0122e783788d0860112d3eb7128d4c251727fba33dd3670a7cdcde2402155ff4783b.851cfada3fbd1cb0018f1c8a9c78b71d	<EMAIL>	fawdfwf	awfawef	""		\N	\N	\N	0	standard	user	active	\N	2025-04-24 06:53:05.874	2025-04-25 13:02:00.677	f	f	{}
4	test2	b751c7f4acf1a0c654578d74cbaffbcceed0238afd1fffed14e6cd3a1f2753db983ec84edccec4d7dc40892d575ff44e2b0ebe85939e1b190b7ec9e708f84ead.6d8ed2558c68db8716f1e837aafac294	<EMAIL>	tt	tt		\N	\N	\N	\N	0	standard	user	active	\N	2025-04-25 13:17:35.01134	2025-04-25 13:17:35.01134	f	f	{}
2	test	09e90047128637a7677bb8aace975e13c9b37b8f5d11516c5bc953802cf7a37203191b9d4c67125b0c9d76dab0008eb37886286dc525681da43b891389358ca1.e6195c584d441138a7afc708fe050f1c	<EMAIL>	test	test	test		\N	\N	/uploads/default-avatars/avatar-1.webp	4886	standard	user	active	\N	2025-04-17 23:40:23.03398	2025-04-29 11:12:16.663	f	t	{}
5	vip	1e656faf609cc59fb7e7e9bb12ad8abbfb9b284c45470d7ab07323d8db98775818fdbb5aa61d48c7a864e6548664735cf00e6df188856af5ddc79e6c567390d2.8a73740f747b4f10e687e37f52ab8b42	<EMAIL>	TMOGNOT	OMGZZ		**********	\N	\N	\N	4800	standard	user	active	\N	2025-04-25 14:37:21.660768	2025-04-29 11:24:10.587	f	t	{}
\.


--
-- Data for Name: verification_codes; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.verification_codes (id, user_id, type, code, identifier, is_used, expires_at, attempts, metadata, created_at, used_at) FROM stdin;
2	1	email	988858	<EMAIL>	f	2025-04-25 14:33:15.833	0	\N	2025-04-25 14:18:15.851061	\N
3	\N	email	974319	<EMAIL>	t	2025-04-25 14:43:30.509	0	\N	2025-04-25 14:28:30.524438	2025-04-25 14:37:21.219
4	5	email	296219	<EMAIL>	t	2025-04-25 14:53:45.002	0	\N	2025-04-25 14:38:45.018848	2025-04-25 14:39:05.844
5	\N	phone	250671	**********	f	2025-04-25 14:53:51.758	0	\N	2025-04-25 14:48:51.8107	\N
6	1	email	487952	<EMAIL>	t	2025-04-27 09:25:46.065	0	\N	2025-04-27 09:10:46.081459	2025-04-27 09:11:07.632
7	1	email	468724	<EMAIL>	t	2025-04-27 09:37:03.043	0	\N	2025-04-27 09:22:03.059368	2025-04-27 09:27:49.463
8	1	email	391411	<EMAIL>	t	2025-04-27 09:47:54.29	0	\N	2025-04-27 09:32:54.306045	2025-04-27 09:34:06.512
9	1	email	298498	<EMAIL>	f	2025-04-27 09:56:22.825	0	\N	2025-04-27 09:41:22.84108	\N
10	1	email	986226	<EMAIL>	t	2025-04-27 09:57:13.806	0	\N	2025-04-27 09:42:13.822925	2025-04-27 09:43:26.394
11	2	email	457237	<EMAIL>	t	2025-04-29 05:22:22.333	0	\N	2025-04-29 05:07:22.349001	2025-04-29 05:07:44.846
12	2	password_reset	001189	test	f	2025-04-29 06:43:23.925	0	\N	2025-04-29 06:33:23.941161	\N
16	1	password_reset	597721	<EMAIL>	t	2025-04-29 16:35:11.92	1	\N	2025-04-29 16:25:11.935069	2025-04-29 16:25:24.988
17	1	password_reset	881764	<EMAIL>	t	2025-04-29 17:41:03.486	1	\N	2025-04-29 17:31:03.501558	2025-04-29 17:31:37.102
\.


--
-- Data for Name: webhook_logs; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.webhook_logs (id, webhook_id, event_type, payload, response_status, response_body, duration, success, error, retry_count, created_at) FROM stdin;
\.


--
-- Data for Name: webhooks; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.webhooks (id, user_id, name, url, secret, event_types, is_active, headers, conditions, api_key_id, created_at, updated_at, last_triggered_at) FROM stdin;
\.


--
-- Name: achievements_id_seq; Type: SEQUENCE SET; Schema: public; Owner: neondb_owner
--

SELECT pg_catalog.setval('public.achievements_id_seq', 8, true);


--
-- Name: alert_settings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: neondb_owner
--

SELECT pg_catalog.setval('public.alert_settings_id_seq', 1, false);


--
-- Name: api_keys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: neondb_owner
--

SELECT pg_catalog.setval('public.api_keys_id_seq', 5, true);


--
-- Name: api_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: neondb_owner
--

SELECT pg_catalog.setval('public.api_logs_id_seq', 67, true);


--
-- Name: api_response_templates_id_seq; Type: SEQUENCE SET; Schema: public; Owner: neondb_owner
--

SELECT pg_catalog.setval('public.api_response_templates_id_seq', 1, false);


--
-- Name: coupons_id_seq; Type: SEQUENCE SET; Schema: public; Owner: neondb_owner
--

SELECT pg_catalog.setval('public.coupons_id_seq', 3, true);


--
-- Name: customer_behavior_id_seq; Type: SEQUENCE SET; Schema: public; Owner: neondb_owner
--

SELECT pg_catalog.setval('public.customer_behavior_id_seq', 1, false);


--
-- Name: email_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: neondb_owner
--

SELECT pg_catalog.setval('public.email_logs_id_seq', 32, true);


--
-- Name: email_templates_id_seq; Type: SEQUENCE SET; Schema: public; Owner: neondb_owner
--

SELECT pg_catalog.setval('public.email_templates_id_seq', 6, true);


--
-- Name: external_auth_id_seq; Type: SEQUENCE SET; Schema: public; Owner: neondb_owner
--

SELECT pg_catalog.setval('public.external_auth_id_seq', 1, false);


--
-- Name: fraud_detections_id_seq; Type: SEQUENCE SET; Schema: public; Owner: neondb_owner
--

SELECT pg_catalog.setval('public.fraud_detections_id_seq', 1, false);


--
-- Name: fraud_rules_id_seq; Type: SEQUENCE SET; Schema: public; Owner: neondb_owner
--

SELECT pg_catalog.setval('public.fraud_rules_id_seq', 1, false);


--
-- Name: notifications_id_seq; Type: SEQUENCE SET; Schema: public; Owner: neondb_owner
--

SELECT pg_catalog.setval('public.notifications_id_seq', 1, false);


--
-- Name: packages_id_seq; Type: SEQUENCE SET; Schema: public; Owner: neondb_owner
--

SELECT pg_catalog.setval('public.packages_id_seq', 1, false);


--
-- Name: reports_id_seq; Type: SEQUENCE SET; Schema: public; Owner: neondb_owner
--

SELECT pg_catalog.setval('public.reports_id_seq', 1, false);


--
-- Name: slip_verifications_id_seq; Type: SEQUENCE SET; Schema: public; Owner: neondb_owner
--

SELECT pg_catalog.setval('public.slip_verifications_id_seq', 86, true);


--
-- Name: system_settings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: neondb_owner
--

SELECT pg_catalog.setval('public.system_settings_id_seq', 15, true);


--
-- Name: top_up_transactions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: neondb_owner
--

SELECT pg_catalog.setval('public.top_up_transactions_id_seq', 28, true);


--
-- Name: user_achievements_id_seq; Type: SEQUENCE SET; Schema: public; Owner: neondb_owner
--

SELECT pg_catalog.setval('public.user_achievements_id_seq', 12, true);


--
-- Name: user_auth_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: neondb_owner
--

SELECT pg_catalog.setval('public.user_auth_logs_id_seq', 1, false);


--
-- Name: user_packages_id_seq; Type: SEQUENCE SET; Schema: public; Owner: neondb_owner
--

SELECT pg_catalog.setval('public.user_packages_id_seq', 14, true);


--
-- Name: user_settings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: neondb_owner
--

SELECT pg_catalog.setval('public.user_settings_id_seq', 5, true);


--
-- Name: users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: neondb_owner
--

SELECT pg_catalog.setval('public.users_id_seq', 5, true);


--
-- Name: verification_codes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: neondb_owner
--

SELECT pg_catalog.setval('public.verification_codes_id_seq', 17, true);


--
-- Name: webhook_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: neondb_owner
--

SELECT pg_catalog.setval('public.webhook_logs_id_seq', 1, false);


--
-- Name: webhooks_id_seq; Type: SEQUENCE SET; Schema: public; Owner: neondb_owner
--

SELECT pg_catalog.setval('public.webhooks_id_seq', 1, false);


--
-- Name: achievements achievements_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.achievements
    ADD CONSTRAINT achievements_pkey PRIMARY KEY (id);


--
-- Name: alert_settings alert_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.alert_settings
    ADD CONSTRAINT alert_settings_pkey PRIMARY KEY (id);


--
-- Name: api_keys api_keys_api_key_unique; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.api_keys
    ADD CONSTRAINT api_keys_api_key_unique UNIQUE (api_key);


--
-- Name: api_keys api_keys_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.api_keys
    ADD CONSTRAINT api_keys_pkey PRIMARY KEY (id);


--
-- Name: api_logs api_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.api_logs
    ADD CONSTRAINT api_logs_pkey PRIMARY KEY (id);


--
-- Name: api_response_templates api_response_templates_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.api_response_templates
    ADD CONSTRAINT api_response_templates_pkey PRIMARY KEY (id);


--
-- Name: coupons coupons_code_unique; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.coupons
    ADD CONSTRAINT coupons_code_unique UNIQUE (code);


--
-- Name: coupons coupons_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.coupons
    ADD CONSTRAINT coupons_pkey PRIMARY KEY (id);


--
-- Name: customer_behavior customer_behavior_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.customer_behavior
    ADD CONSTRAINT customer_behavior_pkey PRIMARY KEY (id);


--
-- Name: email_logs email_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.email_logs
    ADD CONSTRAINT email_logs_pkey PRIMARY KEY (id);


--
-- Name: email_templates email_templates_name_unique; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.email_templates
    ADD CONSTRAINT email_templates_name_unique UNIQUE (name);


--
-- Name: email_templates email_templates_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.email_templates
    ADD CONSTRAINT email_templates_pkey PRIMARY KEY (id);


--
-- Name: external_auth external_auth_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.external_auth
    ADD CONSTRAINT external_auth_pkey PRIMARY KEY (id);


--
-- Name: fraud_detections fraud_detections_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.fraud_detections
    ADD CONSTRAINT fraud_detections_pkey PRIMARY KEY (id);


--
-- Name: fraud_rules fraud_rules_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.fraud_rules
    ADD CONSTRAINT fraud_rules_pkey PRIMARY KEY (id);


--
-- Name: notifications notifications_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_pkey PRIMARY KEY (id);


--
-- Name: packages packages_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.packages
    ADD CONSTRAINT packages_pkey PRIMARY KEY (id);


--
-- Name: reports reports_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.reports
    ADD CONSTRAINT reports_pkey PRIMARY KEY (id);


--
-- Name: session session_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.session
    ADD CONSTRAINT session_pkey PRIMARY KEY (sid);


--
-- Name: slip_verifications slip_verifications_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.slip_verifications
    ADD CONSTRAINT slip_verifications_pkey PRIMARY KEY (id);


--
-- Name: system_settings system_settings_key_unique; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.system_settings
    ADD CONSTRAINT system_settings_key_unique UNIQUE (key);


--
-- Name: system_settings system_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.system_settings
    ADD CONSTRAINT system_settings_pkey PRIMARY KEY (id);


--
-- Name: top_up_transactions top_up_transactions_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.top_up_transactions
    ADD CONSTRAINT top_up_transactions_pkey PRIMARY KEY (id);


--
-- Name: user_achievements user_achievements_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.user_achievements
    ADD CONSTRAINT user_achievements_pkey PRIMARY KEY (id);


--
-- Name: user_achievements user_achievements_userId_achievementId_key; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.user_achievements
    ADD CONSTRAINT "user_achievements_userId_achievementId_key" UNIQUE ("userId", "achievementId");


--
-- Name: user_auth_logs user_auth_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.user_auth_logs
    ADD CONSTRAINT user_auth_logs_pkey PRIMARY KEY (id);


--
-- Name: user_packages user_packages_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.user_packages
    ADD CONSTRAINT user_packages_pkey PRIMARY KEY (id);


--
-- Name: user_settings user_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.user_settings
    ADD CONSTRAINT user_settings_pkey PRIMARY KEY (id);


--
-- Name: user_settings user_settings_user_id_key; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.user_settings
    ADD CONSTRAINT user_settings_user_id_key UNIQUE (user_id);


--
-- Name: users users_email_unique; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_unique UNIQUE (email);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: users users_username_unique; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_username_unique UNIQUE (username);


--
-- Name: verification_codes verification_codes_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.verification_codes
    ADD CONSTRAINT verification_codes_pkey PRIMARY KEY (id);


--
-- Name: webhook_logs webhook_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.webhook_logs
    ADD CONSTRAINT webhook_logs_pkey PRIMARY KEY (id);


--
-- Name: webhooks webhooks_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.webhooks
    ADD CONSTRAINT webhooks_pkey PRIMARY KEY (id);


--
-- Name: user_auth_logs_created_at_idx; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE INDEX user_auth_logs_created_at_idx ON public.user_auth_logs USING btree (created_at);


--
-- Name: user_auth_logs_user_id_idx; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE INDEX user_auth_logs_user_id_idx ON public.user_auth_logs USING btree (user_id);


--
-- Name: alert_settings alert_settings_user_id_users_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.alert_settings
    ADD CONSTRAINT alert_settings_user_id_users_id_fk FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: api_keys api_keys_user_id_users_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.api_keys
    ADD CONSTRAINT api_keys_user_id_users_id_fk FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: api_logs api_logs_api_key_id_api_keys_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.api_logs
    ADD CONSTRAINT api_logs_api_key_id_api_keys_id_fk FOREIGN KEY (api_key_id) REFERENCES public.api_keys(id);


--
-- Name: api_logs api_logs_slip_verification_id_slip_verifications_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.api_logs
    ADD CONSTRAINT api_logs_slip_verification_id_slip_verifications_id_fk FOREIGN KEY (slip_verification_id) REFERENCES public.slip_verifications(id);


--
-- Name: customer_behavior customer_behavior_recommended_package_id_packages_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.customer_behavior
    ADD CONSTRAINT customer_behavior_recommended_package_id_packages_id_fk FOREIGN KEY (recommended_package_id) REFERENCES public.packages(id);


--
-- Name: customer_behavior customer_behavior_user_id_users_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.customer_behavior
    ADD CONSTRAINT customer_behavior_user_id_users_id_fk FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: email_logs email_logs_template_id_email_templates_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.email_logs
    ADD CONSTRAINT email_logs_template_id_email_templates_id_fk FOREIGN KEY (template_id) REFERENCES public.email_templates(id);


--
-- Name: email_logs email_logs_user_id_users_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.email_logs
    ADD CONSTRAINT email_logs_user_id_users_id_fk FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: external_auth external_auth_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.external_auth
    ADD CONSTRAINT external_auth_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: fraud_detections fraud_detections_reviewed_by_users_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.fraud_detections
    ADD CONSTRAINT fraud_detections_reviewed_by_users_id_fk FOREIGN KEY (reviewed_by) REFERENCES public.users(id);


--
-- Name: fraud_detections fraud_detections_rule_id_fraud_rules_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.fraud_detections
    ADD CONSTRAINT fraud_detections_rule_id_fraud_rules_id_fk FOREIGN KEY (rule_id) REFERENCES public.fraud_rules(id);


--
-- Name: fraud_detections fraud_detections_slip_verification_id_slip_verifications_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.fraud_detections
    ADD CONSTRAINT fraud_detections_slip_verification_id_slip_verifications_id_fk FOREIGN KEY (slip_verification_id) REFERENCES public.slip_verifications(id);


--
-- Name: fraud_detections fraud_detections_user_id_users_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.fraud_detections
    ADD CONSTRAINT fraud_detections_user_id_users_id_fk FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: notifications notifications_user_id_users_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_user_id_users_id_fk FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: reports reports_email_log_id_email_logs_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.reports
    ADD CONSTRAINT reports_email_log_id_email_logs_id_fk FOREIGN KEY (email_log_id) REFERENCES public.email_logs(id);


--
-- Name: reports reports_user_id_users_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.reports
    ADD CONSTRAINT reports_user_id_users_id_fk FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: slip_verifications slip_verifications_api_key_id_api_keys_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.slip_verifications
    ADD CONSTRAINT slip_verifications_api_key_id_api_keys_id_fk FOREIGN KEY (api_key_id) REFERENCES public.api_keys(id);


--
-- Name: slip_verifications slip_verifications_user_id_users_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.slip_verifications
    ADD CONSTRAINT slip_verifications_user_id_users_id_fk FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: top_up_transactions top_up_transactions_user_id_users_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.top_up_transactions
    ADD CONSTRAINT top_up_transactions_user_id_users_id_fk FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: user_achievements user_achievements_achievementId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.user_achievements
    ADD CONSTRAINT "user_achievements_achievementId_fkey" FOREIGN KEY ("achievementId") REFERENCES public.achievements(id) ON DELETE CASCADE;


--
-- Name: user_achievements user_achievements_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.user_achievements
    ADD CONSTRAINT "user_achievements_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: user_auth_logs user_auth_logs_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.user_auth_logs
    ADD CONSTRAINT user_auth_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: user_packages user_packages_package_id_packages_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.user_packages
    ADD CONSTRAINT user_packages_package_id_packages_id_fk FOREIGN KEY (package_id) REFERENCES public.packages(id);


--
-- Name: user_packages user_packages_user_id_users_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.user_packages
    ADD CONSTRAINT user_packages_user_id_users_id_fk FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: user_settings user_settings_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.user_settings
    ADD CONSTRAINT user_settings_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: verification_codes verification_codes_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.verification_codes
    ADD CONSTRAINT verification_codes_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: webhook_logs webhook_logs_webhook_id_webhooks_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.webhook_logs
    ADD CONSTRAINT webhook_logs_webhook_id_webhooks_id_fk FOREIGN KEY (webhook_id) REFERENCES public.webhooks(id);


--
-- Name: webhooks webhooks_api_key_id_api_keys_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.webhooks
    ADD CONSTRAINT webhooks_api_key_id_api_keys_id_fk FOREIGN KEY (api_key_id) REFERENCES public.api_keys(id);


--
-- Name: webhooks webhooks_user_id_users_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.webhooks
    ADD CONSTRAINT webhooks_user_id_users_id_fk FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: public; Owner: cloud_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE cloud_admin IN SCHEMA public GRANT ALL ON SEQUENCES TO neon_superuser WITH GRANT OPTION;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: public; Owner: cloud_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE cloud_admin IN SCHEMA public GRANT ALL ON TABLES TO neon_superuser WITH GRANT OPTION;


--
-- PostgreSQL database dump complete
--

