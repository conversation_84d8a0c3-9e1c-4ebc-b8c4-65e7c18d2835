declare module 'nodemailer-express-handlebars' {
  import nodemailer from 'nodemailer';
  
  interface HandlebarsOptions {
    viewEngine: {
      extName: string;
      partialsDir: string;
      defaultLayout: boolean | string;
      layoutsDir?: string;
      helpers?: Record<string, Function>;
    };
    viewPath: string;
    extName: string;
  }
  
  function hbs(options: HandlebarsOptions): any;
  
  export = hbs;
}