# การตั้งค่าฐานข้อมูลสำหรับ SLIPKUY

เอกสารนี้อธิบายการตั้งค่าและการแก้ไขปัญหาการเชื่อมต่อฐานข้อมูลสำหรับโปรเจค SLIPKUY

## การตั้งค่าฐานข้อมูล

โปรเจคนี้ใช้ฐานข้อมูล PostgreSQL จาก NeonDB โดยมีการตั้งค่าดังนี้:

```
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require
```

## การจัดการกับปัญหาการเชื่อมต่อ

เนื่องจากอาจมีปัญหาในการเชื่อมต่อกับฐานข้อมูล NeonDB เราได้ปรับปรุงระบบให้สามารถทำงานได้แม้ในกรณีที่ไม่สามารถเชื่อมต่อกับฐานข้อมูลได้ โดย:

1. **ฐานข้อมูลจำลอง (Mock Database)**: เมื่อไม่สามารถเชื่อมต่อกับฐานข้อมูลจริงได้ ระบบจะใช้ฐานข้อมูลจำลองแทน
2. **การจัดการข้อผิดพลาด**: ระบบจะไม่หยุดทำงานเมื่อไม่สามารถเชื่อมต่อกับฐานข้อมูลได้
3. **การแสดงข้อความเตือน**: ระบบจะแสดงข้อความเตือนว่ากำลังใช้ฐานข้อมูลจำลอง

## ไฟล์ที่เกี่ยวข้อง

- **server/db.ts**: ไฟล์หลักสำหรับการเชื่อมต่อกับฐานข้อมูล
- **server/mock-db.ts**: ไฟล์สำหรับจำลองการเชื่อมต่อกับฐานข้อมูล
- **.env**: ไฟล์สำหรับเก็บตัวแปรสภาพแวดล้อม รวมถึง DATABASE_URL

## การทดสอบการเชื่อมต่อ

คุณสามารถทดสอบการเชื่อมต่อกับฐานข้อมูลได้โดยใช้คำสั่ง:

```bash
node scripts/test-db-connection.js
```

## การแก้ไขปัญหาการเชื่อมต่อ

หากพบปัญหาในการเชื่อมต่อกับฐานข้อมูล คุณสามารถลองแก้ไขได้ดังนี้:

1. **ตรวจสอบ DATABASE_URL**: ตรวจสอบว่า DATABASE_URL ถูกต้องและเป็นปัจจุบัน
2. **ตรวจสอบการเชื่อมต่ออินเทอร์เน็ต**: ตรวจสอบว่าเซิร์ฟเวอร์สามารถเชื่อมต่ออินเทอร์เน็ตได้
3. **ตรวจสอบไฟร์วอลล์**: ตรวจสอบว่าไฟร์วอลล์ไม่ได้บล็อกการเชื่อมต่อไปยัง NeonDB
4. **ตรวจสอบสถานะ NeonDB**: ตรวจสอบว่า NeonDB ไม่มีปัญหาหรือการบำรุงรักษา

## การใช้งานฐานข้อมูลจำลอง

ฐานข้อมูลจำลองถูกออกแบบมาเพื่อให้แอพพลิเคชันสามารถทำงานได้แม้ในกรณีที่ไม่สามารถเชื่อมต่อกับฐานข้อมูลจริงได้ อย่างไรก็ตาม ฟังก์ชันที่ต้องใช้ฐานข้อมูลจะไม่ทำงานอย่างถูกต้อง เช่น:

- การบันทึกข้อมูล
- การอ่านข้อมูล
- การอัปเดตข้อมูล
- การลบข้อมูล

ดังนั้น ควรใช้ฐานข้อมูลจำลองเฉพาะในกรณีที่ต้องการทดสอบหรือพัฒนาส่วนอื่นของแอพพลิเคชันที่ไม่เกี่ยวข้องกับฐานข้อมูลเท่านั้น
