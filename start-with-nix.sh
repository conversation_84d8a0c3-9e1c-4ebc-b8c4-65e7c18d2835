#!/bin/bash

# สร้างโฟลเดอร์ logs ถ้ายังไม่มี
mkdir -p logs

# ตรวจสอบว่ามีการ build แล้วหรือไม่
if [ ! -d "dist" ] || [ ! -f "dist/index.js" ]; then
  echo "🔄 ไม่พบไฟล์ที่ build แล้ว กำลังทำการ build..."
  npm run build
fi

# ตรวจสอบว่า PM2 กำลังทำงานอยู่หรือไม่
pm2 list | grep slipkuy > /dev/null
if [ $? -eq 0 ]; then
  echo "🔄 กำลังรีสตาร์ท SLIPKUY ด้วย PM2..."
  pm2 reload slipkuy
else
  echo "🚀 กำลังเริ่มต้น SLIPKUY ด้วย PM2..."
  pm2 start ecosystem.config.js
fi

# แสดงสถานะของแอพพลิเคชัน
echo "✅ SLIPKUY กำลังทำงานบน PM2"
pm2 status

echo "📊 ดูบันทึกการทำงานด้วยคำสั่ง: pm2 logs slipkuy"
echo "🛑 หยุดการทำงานด้วยคำสั่ง: pm2 stop slipkuy"
