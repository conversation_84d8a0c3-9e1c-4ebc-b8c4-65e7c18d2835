import * as React from "react"
import * as DialogPrimitive from "@radix-ui/react-dialog"
import { X } from "lucide-react"

import { cn } from "@/lib/utils"

// คอมโพเนนต์ Dialog ที่แก้ไขปัญหาป๊อปอัพปิดเอง
const Dialog = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Root>
>((props, forwardedRef) => {
  // สร้าง ref ภายในเพื่อใช้ในกรณีที่ไม่มี ref จากภายนอก
  const localRef = React.useRef<React.ElementRef<typeof DialogPrimitive.Root>>(null);
  const ref = forwardedRef || localRef;

  // สร้าง state ภายในเพื่อควบคุมการเปิด/ปิด Dialog
  const [internalOpen, setInternalOpen] = React.useState<boolean | undefined>(props.open);

  // สร้าง ref เพื่อเก็บค่า onOpenChange callback ดั้งเดิม
  const originalOnOpenChangeRef = React.useRef(props.onOpenChange);

  // อัปเดต ref เมื่อ props.onOpenChange เปลี่ยน
  React.useEffect(() => {
    originalOnOpenChangeRef.current = props.onOpenChange;
  }, [props.onOpenChange]);

  // อัปเดต state ภายในเมื่อ props.open เปลี่ยน
  React.useEffect(() => {
    console.log('Dialog props.open changed:', props.open);
    if (props.open !== undefined) {
      setInternalOpen(props.open);
    }
  }, [props.open]);

  // สร้าง custom onOpenChange handler
  const handleOpenChange = React.useCallback((open: boolean) => {
    console.log('Dialog handleOpenChange called with:', open, 'current internalOpen:', internalOpen);

    // อัปเดต state ภายใน
    setInternalOpen(open);

    // เรียก callback ดั้งเดิมถ้ามี
    if (originalOnOpenChangeRef.current) {
      originalOnOpenChangeRef.current(open);
    }
  }, [internalOpen]);

  // ใช้ state ภายในแทน props.open และ custom onOpenChange handler
  return (
    <DialogPrimitive.Root
      {...props}
      ref={ref as React.Ref<HTMLDivElement>}
      open={internalOpen}
      onOpenChange={handleOpenChange}
      modal={props.modal !== undefined ? props.modal : true}
    />
  );
});

const DialogTrigger = DialogPrimitive.Trigger

const DialogPortal = DialogPrimitive.Portal

const DialogClose = DialogPrimitive.Close

const DialogOverlay = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Overlay
    ref={ref}
    className={cn(
      "fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
      className
    )}
    {...props}
  />
))
DialogOverlay.displayName = DialogPrimitive.Overlay.displayName

const DialogContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <DialogPortal>
    <DialogOverlay />
    <DialogPrimitive.Content
      ref={ref}
      className={cn(
        "fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",
        className
      )}
      {...props}
    >
      {children}
      <DialogPrimitive.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
        <X className="h-4 w-4" />
        <span className="sr-only">Close</span>
      </DialogPrimitive.Close>
    </DialogPrimitive.Content>
  </DialogPortal>
))
DialogContent.displayName = DialogPrimitive.Content.displayName

const DialogHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "flex flex-col space-y-1.5 text-center sm:text-left",
      className
    )}
    {...props}
  />
)
DialogHeader.displayName = "DialogHeader"

const DialogFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",
      className
    )}
    {...props}
  />
)
DialogFooter.displayName = "DialogFooter"

const DialogTitle = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Title
    ref={ref}
    className={cn(
      "text-lg font-semibold leading-none tracking-tight",
      className
    )}
    {...props}
  />
))
DialogTitle.displayName = DialogPrimitive.Title.displayName

const DialogDescription = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Description>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Description
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
))
DialogDescription.displayName = DialogPrimitive.Description.displayName

export {
  Dialog,
  DialogPortal,
  DialogOverlay,
  DialogClose,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
}
