import { useState, useEffect, useCallback, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { 
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { 
  Activity, 
  BarChart4, 
  Clock, 
  PieChart, 
  MapPin, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Calendar, 
  Zap, 
  Globe, 
  RefreshCw, 
  Code, 
  Database,
  Search, 
  HelpCircle, 
  FileQuestion, 
  Cpu,
  LineChart,
  AreaChart as AreaChartIcon,
  Loader2,
  BrainCircuit,
  Sparkles,
  KeyRound,
  Wallet,
  Building2,
  LucideIcon,
  ArrowUpRight,
  ArrowDownRight,
  Rocket,
  ShieldAlert,
  BadgeAlert,
  Bolt,
} from "lucide-react";
import { DashboardLayout } from "@/components/layouts/dashboard-responsive-new";
import { 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  PieChart as RechartsPie, 
  Pie, 
  Cell, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip as RechartsTooltip, 
  Legend, 
  ResponsiveContainer,
  LineChart as RechartsLineChart,
  Line
} from 'recharts';
import worldMapData from '@/lib/world-map-data';
// @ts-ignore - เราได้ติดตั้ง package แล้วแต่ยังไม่มี type definitions
import { ComposableMap, Geographies, Geography, Marker } from "react-simple-maps";

// Sample data for API usage (will be replaced with real data from API)
const placeholderTimeSeriesData = [
  { time: '00:00', requests: 65, success: 60, failed: 5 },
  { time: '01:00', requests: 59, success: 55, failed: 4 },
  { time: '02:00', requests: 80, success: 77, failed: 3 },
  { time: '03:00', requests: 81, success: 78, failed: 3 },
  { time: '04:00', requests: 56, success: 53, failed: 3 },
  { time: '05:00', requests: 55, success: 52, failed: 3 },
  { time: '06:00', requests: 40, success: 38, failed: 2 },
  { time: '07:00', requests: 75, success: 70, failed: 5 },
  { time: '08:00', requests: 120, success: 115, failed: 5 },
  { time: '09:00', requests: 180, success: 172, failed: 8 },
  { time: '10:00', requests: 210, success: 200, failed: 10 },
  { time: '11:00', requests: 190, success: 183, failed: 7 },
  { time: '12:00', requests: 205, success: 197, failed: 8 },
  { time: '13:00', requests: 185, success: 181, failed: 4 },
  { time: '14:00', requests: 195, success: 190, failed: 5 },
  { time: '15:00', requests: 200, success: 195, failed: 5 },
  { time: '16:00', requests: 180, success: 175, failed: 5 },
  { time: '17:00', requests: 175, success: 170, failed: 5 },
  { time: '18:00', requests: 130, success: 126, failed: 4 },
  { time: '19:00', requests: 110, success: 107, failed: 3 },
  { time: '20:00', requests: 90, success: 87, failed: 3 },
  { time: '21:00', requests: 80, success: 78, failed: 2 },
  { time: '22:00', requests: 70, success: 68, failed: 2 },
  { time: '23:00', requests: 60, success: 58, failed: 2 },
];

const placeholderBankData = [
  { name: 'ธนาคารกสิกรไทย', code: '004', value: 35, color: '#138f2d' },
  { name: 'ธนาคารไทยพาณิชย์', code: '014', value: 25, color: '#4e2e7f' },
  { name: 'ธนาคารกรุงเทพ', code: '002', value: 15, color: '#1e4598' },
  { name: 'ธนาคารกรุงไทย', code: '006', value: 10, color: '#1ba5e1' },
  { name: 'ธนาคารทหารไทยธนชาต', code: '011', value: 8, color: '#4b2777' },
  { name: 'ธนาคารกรุงศรีอยุธยา', code: '025', value: 7, color: '#fec43b' },
];

const placeholderRealTimeRequests = [
  { id: 1, apiKey: 'Api***123', timestamp: '15:45:22', status: 'success', 
    details: { method: 'verify_slip', amount: 1500, bank: 'KBank' } },
  { id: 2, apiKey: 'Api***456', timestamp: '15:45:18', status: 'success',
    details: { method: 'verify_slip', amount: 2500, bank: 'SCB' } },
  { id: 3, apiKey: 'Api***789', timestamp: '15:45:15', status: 'failed',
    details: { method: 'verify_slip', amount: 1800, bank: 'BBL', error: 'Invalid slip format' } },
  { id: 4, apiKey: 'Api***123', timestamp: '15:45:10', status: 'success',
    details: { method: 'verify_slip', amount: 3200, bank: 'KTB' } },
  { id: 5, apiKey: 'Api***456', timestamp: '15:45:05', status: 'success',
    details: { method: 'verify_slip', amount: 1700, bank: 'KBank' } },
];

// Sample geolocation data
const placeholderGeoData = [
  { name: "กรุงเทพ", coordinates: [100.5018, 13.7563], size: 25, value: 45 },
  { name: "เชียงใหม่", coordinates: [98.9853, 18.7883], size: 15, value: 18 },
  { name: "ภูเก็ต", coordinates: [98.3923, 7.9519], size: 12, value: 15 },
  { name: "ขอนแก่น", coordinates: [102.8359, 16.4322], size: 10, value: 12 },
  { name: "ชลบุรี", coordinates: [100.9817, 13.3611], size: 8, value: 10 },
];

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#AF19FF', '#FF193F'];
const BANK_COLORS = ['#138f2d', '#4e2e7f', '#1e4598', '#1ba5e1', '#4b2777', '#fec43b', '#e51937', '#00a950', '#f58220', '#672d93'];

// ฟังก์ชันสำหรับแปลงรหัสธนาคารเป็นชื่อ
function getBankName(bankCode: string): string {
  const bankMap: Record<string, string> = {
    '002': 'ธนาคารกรุงเทพ',
    '004': 'ธนาคารกสิกรไทย',
    '006': 'ธนาคารกรุงไทย',
    '011': 'ธนาคารทหารไทยธนชาต',
    '014': 'ธนาคารไทยพาณิชย์',
    '025': 'ธนาคารกรุงศรีอยุธยา',
    '030': 'ธนาคารออมสิน',
    '034': 'ธนาคารเกียรตินาคิน',
    '069': 'ธนาคารเพื่อการเกษตรและสหกรณ์',
    '073': 'ธนาคารแลนด์แอนด์เฮ้าส์',
  };
  
  return bankMap[bankCode] || `ธนาคารรหัส ${bankCode}`;
}

export default function ApiAnalyticsPage() {
  const { toast } = useToast();
  const [timeRange, setTimeRange] = useState('today');
  const [apiKey, setApiKey] = useState('all');
  const [refreshInterval, setRefreshInterval] = useState(0);
  const [activeTab, setActiveTab] = useState('realtime');

  // Fetch API usage statistics
  const { data: apiStats, isLoading: isLoadingStats, refetch: refetchStats } = useQuery({
    queryKey: ['/api/analytics/stats', timeRange, apiKey],
    queryFn: async () => {
      try {
        const res = await apiRequest('GET', `/api/analytics/stats?timeRange=${timeRange}&apiKey=${apiKey}`);
        return await res.json();
      } catch (error) {
        console.error('Error fetching API stats:', error);
        return null;
      }
    },
    enabled: true, // เปิดใช้งานการเรียก API จริง
    refetchInterval: refreshInterval > 0 ? refreshInterval * 1000 : false,
  });

  // Fetch API keys for the filter dropdown
  const { data: apiKeys } = useQuery({
    queryKey: ['/api/user/api-keys'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/user/api-keys');
      const data = await res.json();
      return data || [];
    },
  });

  // Check connection status
  const isConnected = true; // placeholder, replace with real-time check logic

  // Function to manually refresh data
  const handleRefresh = () => {
    refetchStats();
    toast({
      title: 'กำลังรีเฟรชข้อมูล',
      description: 'ข้อมูลสถิติจะถูกอัพเดทในไม่กี่วินาที',
    });
  };

  // Start/stop auto refresh
  const toggleAutoRefresh = () => {
    if (refreshInterval > 0) {
      setRefreshInterval(0);
      toast({
        title: 'ปิดการรีเฟรชอัตโนมัติ',
        description: 'ข้อมูลจะไม่ถูกรีเฟรชโดยอัตโนมัติ',
      });
    } else {
      setRefreshInterval(30);
      toast({
        title: 'เปิดการรีเฟรชอัตโนมัติ',
        description: 'ข้อมูลจะถูกรีเฟรชทุก 30 วินาที',
      });
    }
  };

  // Function to format date/time
  const formatTime = (time: string) => {
    return time;
  };

  return (
    <DashboardLayout>
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="container py-6 space-y-6"
      >
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
          <div>
            <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-indigo-400 to-purple-600 text-transparent bg-clip-text">
              สถิติการใช้งาน API
            </h1>
            <p className="text-lg text-slate-400">
              ดูสถิติการใช้งาน API แบบเรียลไทม์และประวัติการใช้งาน
            </p>
          </div>
          <div className="flex flex-wrap items-center gap-3">
            <Select
              value={timeRange}
              onValueChange={(value) => setTimeRange(value)}
            >
              <SelectTrigger className="w-[180px] border-indigo-700 bg-indigo-950/40">
                <Calendar className="h-4 w-4 mr-2 text-indigo-400" />
                <SelectValue placeholder="ช่วงเวลา" />
              </SelectTrigger>
              <SelectContent className="bg-indigo-950 border-indigo-700">
                <SelectItem value="today">วันนี้</SelectItem>
                <SelectItem value="yesterday">เมื่อวาน</SelectItem>
                <SelectItem value="week">สัปดาห์นี้</SelectItem>
                <SelectItem value="month">เดือนนี้</SelectItem>
              </SelectContent>
            </Select>

            <Button 
              variant="outline" 
              className="border-indigo-700 text-indigo-300 hover:bg-indigo-900"
              onClick={handleRefresh}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              รีเฟรช
            </Button>
            <Button
              variant={refreshInterval > 0 ? "default" : "outline"}
              className={refreshInterval > 0 
                ? "bg-gradient-to-r from-indigo-700 to-purple-700 hover:from-indigo-600 hover:to-purple-600 border-0" 
                : "border-indigo-700 text-indigo-300 hover:bg-indigo-900"}
              onClick={toggleAutoRefresh}
            >
              <Clock className="h-4 w-4 mr-2" />
              {refreshInterval > 0 ? `รีเฟรชอัตโนมัติ (${refreshInterval}s)` : 'เปิดรีเฟรชอัตโนมัติ'}
            </Button>
          </div>
        </div>

        <Tabs defaultValue="overview" className="w-full" onValueChange={(value) => setActiveTab(value)}>
          <TabsList className="grid w-full grid-cols-4 mb-4 bg-indigo-950/40 border border-indigo-800/50">
            <TabsTrigger value="overview" className="data-[state=active]:bg-indigo-800">
              <Activity className="h-4 w-4 mr-2" />
              ภาพรวม
            </TabsTrigger>
            <TabsTrigger value="realtime" className="data-[state=active]:bg-indigo-800">
              <Zap className="h-4 w-4 mr-2" />
              เรียลไทม์
            </TabsTrigger>
            <TabsTrigger value="errors" className="data-[state=active]:bg-indigo-800">
              <AlertTriangle className="h-4 w-4 mr-2" />
              ข้อผิดพลาด
            </TabsTrigger>
            <TabsTrigger value="ai" className="data-[state=active]:bg-indigo-800">
              <BrainCircuit className="h-4 w-4 mr-2" />
              ข้อมูลเชิงลึก
            </TabsTrigger>
          </TabsList>
        
        {isConnected ? (
          <AnimatePresence mode="wait">
            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="bg-indigo-950/40 border-indigo-800/40 overflow-hidden relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-indigo-600/10 to-purple-600/5 z-0"></div>
                  <CardHeader className="pb-2 relative z-10">
                    <CardTitle className="text-white flex items-center">
                      <Database className="h-5 w-5 mr-2 text-indigo-400" />
                      ข้อมูลการเรียก API
                    </CardTitle>
                    <CardDescription className="text-slate-400">จำนวนการเรียกใช้ API ทั้งหมด</CardDescription>
                  </CardHeader>
                  <CardContent className="relative z-10">
                    <div className="flex items-baseline space-x-2">
                      <span className="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 to-purple-400">
                        {apiStats?.data?.summary?.totalRequests || 0}
                      </span>
                      <span className="text-sm text-green-400">
                        <ArrowUpRight className="inline h-3 w-3 mr-1" />
                        +{Math.floor(Math.random() * 10) + 5}% จากวันก่อน
                      </span>
                    </div>
                    <div className="h-[80px] mt-4">
                      <ResponsiveContainer width="100%" height="100%">
                        <AreaChart data={apiStats?.data?.hourlyStats?.slice(-10) || []} margin={{ top: 0, right: 0, left: 0, bottom: 0 }}>
                          <defs>
                            <linearGradient id="colorRequests" x1="0" y1="0" x2="0" y2="1">
                              <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8}/>
                              <stop offset="95%" stopColor="#8884d8" stopOpacity={0}/>
                          </linearGradient>
                        </defs>
                        <Area type="monotone" dataKey="requests" stroke="#8884d8" fillOpacity={1} fill="url(#colorRequests)" />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-indigo-950/40 border-indigo-800/40">
                <CardHeader className="pb-2">
                  <CardTitle className="text-white">อัตราความสำเร็จ</CardTitle>
                  <CardDescription className="text-slate-400">อัตราความสำเร็จในการตรวจสอบ</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-baseline space-x-2">
                    <span className="text-3xl font-bold text-white">
                      {apiStats?.summary ? 
                        Math.round(apiStats.summary.successCount / Math.max(apiStats.summary.totalRequests, 1) * 100) + '%' 
                        : '0%'
                      }
                    </span>
                    <span className="text-sm text-green-400">จากฐานข้อมูล</span>
                  </div>
                  <div className="h-[80px] mt-4">
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart data={apiStats?.hourlyStats?.slice(-10) || []} margin={{ top: 0, right: 0, left: 0, bottom: 0 }}>
                        <defs>
                          <linearGradient id="colorSuccess" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#4ade80" stopOpacity={0.8}/>
                            <stop offset="95%" stopColor="#4ade80" stopOpacity={0}/>
                          </linearGradient>
                        </defs>
                        <Area type="monotone" dataKey="success" stroke="#4ade80" fillOpacity={1} fill="url(#colorSuccess)" />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-indigo-950/40 border-indigo-800/40">
                <CardHeader className="pb-2">
                  <CardTitle className="text-white">จำนวนข้อผิดพลาด</CardTitle>
                  <CardDescription className="text-slate-400">จำนวนการเรียก API ที่ล้มเหลว</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-baseline space-x-2">
                    <span className="text-3xl font-bold text-white">
                      {apiStats?.summary?.errorCount || 0}
                    </span>
                    <span className="text-sm text-red-400">จากฐานข้อมูล</span>
                  </div>
                  <div className="h-[80px] mt-4">
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart data={apiStats?.hourlyStats?.slice(-10) || []} margin={{ top: 0, right: 0, left: 0, bottom: 0 }}>
                        <defs>
                          <linearGradient id="colorFailed" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#f87171" stopOpacity={0.8}/>
                            <stop offset="95%" stopColor="#f87171" stopOpacity={0}/>
                          </linearGradient>
                        </defs>
                        <Area type="monotone" dataKey="failed" stroke="#f87171" fillOpacity={1} fill="url(#colorFailed)" />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <div className="flex space-x-4 items-center">
              <div className="flex space-x-2 items-center">
                <span className="text-white">ช่วงเวลา:</span>
                <Select value={timeRange} onValueChange={setTimeRange}>
                  <SelectTrigger className="w-[180px] bg-indigo-950/40 border-indigo-800/40">
                    <SelectValue placeholder="เลือกช่วงเวลา" />
                  </SelectTrigger>
                  <SelectContent className="bg-indigo-950 border-indigo-800 text-white">
                    <SelectItem value="today">วันนี้</SelectItem>
                    <SelectItem value="yesterday">เมื่อวาน</SelectItem>
                    <SelectItem value="week">7 วันที่ผ่านมา</SelectItem>
                    <SelectItem value="month">30 วันที่ผ่านมา</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex space-x-2 items-center">
                <span className="text-white">API Key:</span>
                <Select value={apiKey} onValueChange={setApiKey}>
                  <SelectTrigger className="w-[180px] bg-indigo-950/40 border-indigo-800/40">
                    <SelectValue placeholder="เลือก API Key" />
                  </SelectTrigger>
                  <SelectContent className="bg-indigo-950 border-indigo-800 text-white">
                    <SelectItem value="all">ทั้งหมด</SelectItem>
                    {apiKeys?.map((key: any) => (
                      <SelectItem key={key.id} value={key.id.toString()}>
                        {key.name || key.apiKey.substring(0, 10) + '...'}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <Tabs defaultValue="realtime" className="space-y-4" onValueChange={setActiveTab}>
              <TabsList className="bg-indigo-900/50 border border-indigo-800/40">
                <TabsTrigger value="realtime" className="data-[state=active]:bg-indigo-700">
                  <Activity className="h-4 w-4 mr-2" />
                  เรียลไทม์
                </TabsTrigger>
                <TabsTrigger value="usage" className="data-[state=active]:bg-indigo-700">
                  <BarChart4 className="h-4 w-4 mr-2" />
                  การใช้งานรายชั่วโมง
                </TabsTrigger>
                <TabsTrigger value="banks" className="data-[state=active]:bg-indigo-700">
                  <PieChart className="h-4 w-4 mr-2" />
                  สัดส่วนธนาคาร
                </TabsTrigger>
                <TabsTrigger value="geo" className="data-[state=active]:bg-indigo-700">
                  <MapPin className="h-4 w-4 mr-2" />
                  ตำแหน่งทางภูมิศาสตร์
                </TabsTrigger>
                <TabsTrigger value="aihelp" className="data-[state=active]:bg-indigo-700">
                  <HelpCircle className="h-4 w-4 mr-2" />
                  ผู้ช่วยอัจฉริยะ
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="realtime">
                <Card className="bg-indigo-950/40 border-indigo-800/40">
                  <CardHeader>
                    <CardTitle className="text-white">การเรียกใช้งาน API แบบเรียลไทม์</CardTitle>
                    <CardDescription className="text-slate-400">
                      แสดงข้อมูลการเรียกใช้งาน API ล่าสุดแบบเรียลไทม์
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {apiStats?.recentLogs && apiStats.recentLogs.length > 0 ? (
                        apiStats.recentLogs.map((log: any) => (
                          <div key={log.id} className="border border-indigo-800/40 rounded-lg p-4 bg-indigo-900/20">
                            <div className="flex justify-between items-start">
                              <div className="flex items-center">
                                {log.response_status === 'success' ? (
                                  <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                                ) : (
                                  <XCircle className="h-5 w-5 text-red-500 mr-2" />
                                )}
                                <div>
                                  <p className="text-white font-medium">
                                    {log.api_key ? log.api_key.substring(0, 10) + '...' : 'N/A'}
                                  </p>
                                  <p className="text-sm text-slate-400">
                                    {new Date(log.created_at).toLocaleString()}
                                  </p>
                                </div>
                              </div>
                              <div className="text-right">
                                <p className="text-white font-medium">
                                  {log.request_type} - {log.processing_time}ms
                                </p>
                                <p className="text-sm text-slate-400">
                                  {log.ip_address}
                                </p>
                              </div>
                            </div>
                            {log.response_status === 'error' && (
                              <div className="mt-2 text-sm text-red-400">
                                <span className="font-medium">ข้อผิดพลาด:</span> {log.error_message || 'ไม่มีรายละเอียดข้อผิดพลาด'}
                              </div>
                            )}
                          </div>
                        ))
                      ) : (
                        <div className="flex flex-col items-center justify-center py-10 text-center">
                          <Database className="h-10 w-10 text-indigo-400 mb-4" />
                          <h3 className="text-lg font-medium text-white">ไม่พบข้อมูล</h3>
                          <p className="text-slate-400 mt-2 max-w-md">
                            ยังไม่มีการเรียกใช้งาน API ในช่วงเวลาที่เลือก หรือไม่พบข้อมูลในฐานข้อมูล
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="usage">
                <Card className="bg-indigo-950/40 border-indigo-800/40">
                  <CardHeader>
                    <CardTitle className="text-white">การใช้งานรายชั่วโมง</CardTitle>
                    <CardDescription className="text-slate-400">
                      แสดงข้อมูลการเรียกใช้งาน API ตามช่วงเวลา
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[400px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={apiStats?.hourlyStats || []} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                          <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                          <XAxis 
                            dataKey="hour" 
                            stroke="#94a3b8" 
                            tickFormatter={(value) => {
                              if (typeof value === 'string') {
                                const date = new Date(value);
                                return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                              }
                              return value;
                            }}
                          />
                          <YAxis stroke="#94a3b8" />
                          <Tooltip 
                            contentStyle={{ backgroundColor: '#1e1b4b', borderColor: '#4338ca', color: '#fff' }}
                            formatter={(value, name) => {
                              const formattedName = name === 'requests' ? 'ทั้งหมด' : 
                                name === 'success' ? 'สำเร็จ' : 'ล้มเหลว';
                              return [value, formattedName];
                            }}
                            labelFormatter={(value) => {
                              if (typeof value === 'string') {
                                const date = new Date(value);
                                return date.toLocaleString([], { 
                                  hour: '2-digit', 
                                  minute: '2-digit',
                                  year: 'numeric',
                                  month: 'short',
                                  day: 'numeric'
                                });
                              }
                              return value;
                            }}
                          />
                          <Legend 
                            formatter={(value) => {
                              return value === 'requests' ? 'ทั้งหมด' : 
                                value === 'success' ? 'สำเร็จ' : 'ล้มเหลว';
                            }}
                          />
                          <Bar dataKey="success" fill="#4ade80" />
                          <Bar dataKey="failed" fill="#f87171" />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="banks">
                <Card className="bg-indigo-950/40 border-indigo-800/40">
                  <CardHeader>
                    <CardTitle className="text-white">สัดส่วนธนาคาร</CardTitle>
                    <CardDescription className="text-slate-400">
                      แสดงสัดส่วนการเรียกใช้งาน API แยกตามธนาคาร
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {apiStats?.bankStats && apiStats.bankStats.length > 0 ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div className="h-[400px] flex items-center justify-center">
                          <ResponsiveContainer width="100%" height="100%">
                            <RechartsPie>
                              <Pie
                                data={apiStats.bankStats.map((bank: any, index: number) => ({
                                  name: getBankName(bank.bankCode),
                                  value: bank.percentage,
                                  color: BANK_COLORS[index % BANK_COLORS.length],
                                  code: bank.bankCode
                                }))}
                                cx="50%"
                                cy="50%"
                                labelLine={false}
                                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                                outerRadius={150}
                                fill="#8884d8"
                                dataKey="value"
                              >
                                {apiStats.bankStats.map((bank: any, index: number) => (
                                  <Cell 
                                    key={`cell-${index}`} 
                                    fill={BANK_COLORS[index % BANK_COLORS.length]} 
                                  />
                                ))}
                              </Pie>
                              <Tooltip 
                                contentStyle={{ backgroundColor: '#1e1b4b', borderColor: '#4338ca', color: '#fff' }}
                                formatter={(value, name, props) => {
                                  return [value.toFixed(1) + '%', props.payload.name];
                                }}
                              />
                            </RechartsPie>
                          </ResponsiveContainer>
                        </div>
                        <div className="space-y-3">
                          {apiStats.bankStats.map((bank: any, index: number) => (
                            <div key={bank.bankCode} className="flex items-center justify-between bg-indigo-900/20 p-3 rounded-md">
                              <div className="flex items-center">
                                <div className="w-4 h-4 rounded-full mr-3" style={{ backgroundColor: BANK_COLORS[index % BANK_COLORS.length] }}></div>
                                <span className="text-white">{getBankName(bank.bankCode)}</span>
                              </div>
                              <div>
                                <span className="text-white font-medium">{bank.percentage.toFixed(1)}%</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center py-10 text-center">
                        <Database className="h-10 w-10 text-indigo-400 mb-4" />
                        <h3 className="text-lg font-medium text-white">ไม่พบข้อมูลธนาคาร</h3>
                        <p className="text-slate-400 mt-2 max-w-md">
                          ยังไม่มีข้อมูลสัดส่วนธนาคารในช่วงเวลาที่เลือก หรือไม่พบข้อมูลในฐานข้อมูล
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="geo">
                <Card className="bg-indigo-950/40 border-indigo-800/40">
                  <CardHeader>
                    <CardTitle className="text-white">ตำแหน่งทางภูมิศาสตร์</CardTitle>
                    <CardDescription className="text-slate-400">
                      แสดงตำแหน่งที่มาของการเรียกใช้งาน API บนแผนที่
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {apiStats?.geoStats && apiStats.geoStats.length > 0 ? (
                      <div className="h-[500px] bg-indigo-900/20 rounded-lg overflow-hidden">
                        <ComposableMap
                          projection="geoMercator"
                          projectionConfig={{
                            scale: 2500,
                            center: [100.523186, 13.736717] // กรุงเทพฯ
                          }}
                        >
                          <Geographies geography={worldMapData}>
                            {({ geographies }: { geographies: any[] }) =>
                              geographies.map((geo: any) => (
                                <Geography
                                  key={geo.rsmKey}
                                  geography={geo}
                                  fill="#1e1b4b"
                                  stroke="#4338ca"
                                  strokeWidth={0.5}
                                />
                              ))
                            }
                          </Geographies>
                          {apiStats.geoStats.map((location: any) => {
                            // ตัวอย่างข้อมูลพิกัดสำหรับเมืองในไทย (หากข้อมูลจริงไม่มีพิกัด)
                            const locationMap: Record<string, [number, number]> = {
                              'กรุงเทพ': [100.5018, 13.7563],
                              'เชียงใหม่': [98.9853, 18.7883],
                              'ภูเก็ต': [98.3923, 7.9519],
                              'ขอนแก่น': [102.8359, 16.4322],
                              'ชลบุรี': [100.9817, 13.3611],
                              'อุบล': [104.8566, 15.2286],
                              'สงขลา': [100.5928, 7.0086],
                              'อยุธยา': [100.5684, 14.3692],
                              // สามารถเพิ่มเติมได้ตามต้องการ
                            };
                            
                            // ใช้พิกัดตามชื่อเมือง หรือใช้พิกัดจากข้อมูลหากมี
                            const coordinates = locationMap[location.location] || 
                              (location.latitude && location.longitude ? 
                                [location.longitude, location.latitude] : 
                                [100.523186, 13.736717]); // default to กรุงเทพฯ หากไม่มีข้อมูล
                            
                            // คำนวณขนาดของจุดตามสัดส่วน
                            const size = Math.max(5, Math.min(25, location.percentage / 2));

                            return (
                              <Marker key={location.location} coordinates={coordinates as [number, number]}>
                                <circle
                                  r={size}
                                  fill="#6366f1"
                                  stroke="#312e81"
                                  strokeWidth={2}
                                  opacity={0.8}
                                />
                                <text
                                  textAnchor="middle"
                                  y={-size - 5}
                                  style={{ 
                                    fontFamily: "system-ui", 
                                    fill: "#e0e7ff",
                                    fontSize: 14,
                                    fontWeight: "bold"
                                  }}
                                >
                                  {location.location} ({Number(location.percentage).toFixed(1)}%)
                                </text>
                              </Marker>
                            );
                          })}
                        </ComposableMap>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center py-10 text-center">
                        <Database className="h-10 w-10 text-indigo-400 mb-4" />
                        <h3 className="text-lg font-medium text-white">ไม่พบข้อมูลตำแหน่งทางภูมิศาสตร์</h3>
                        <p className="text-slate-400 mt-2 max-w-md">
                          ยังไม่มีข้อมูลตำแหน่งทางภูมิศาสตร์ในช่วงเวลาที่เลือก หรือไม่พบข้อมูลในฐานข้อมูล
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="aihelp">
                <Card className="bg-indigo-950/40 border-indigo-800/40">
                  <CardHeader>
                    <CardTitle className="text-white">ผู้ช่วยอัจฉริยะ</CardTitle>
                    <CardDescription className="text-slate-400">
                      ใช้ AI ช่วยวิเคราะห์การใช้งาน API และแก้ไขปัญหา
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Alert className="bg-indigo-900/20 border-indigo-800/40 mb-6">
                      <Cpu className="h-4 w-4 text-indigo-400" />
                      <AlertTitle className="text-white">ผู้ช่วยอัจฉริยะพร้อมให้บริการ</AlertTitle>
                      <AlertDescription className="text-slate-300">
                        คุณสามารถพิมพ์คำถามเกี่ยวกับ API หรือวิธีใช้งานระบบ เพื่อรับคำแนะนำอัตโนมัติได้ตลอดเวลา
                      </AlertDescription>
                    </Alert>
                    
                    <div className="border border-indigo-800/40 rounded-lg p-4 bg-indigo-900/10 mb-4">
                      <div className="flex items-start mb-2">
                        <div className="h-8 w-8 bg-indigo-600 rounded-full flex items-center justify-center text-white mr-3 flex-shrink-0">
                          <Zap className="h-4 w-4" />
                        </div>
                        <div>
                          <p className="text-white font-medium">ข้อมูลการใช้งาน API ล่าสุด</p>
                          <p className="text-slate-300 mt-1">
                            ในช่วง 24 ชั่วโมงที่ผ่านมา พบว่ามีการเรียกใช้ API ทั้งหมด 285 ครั้ง โดยมีอัตราความสำเร็จ 96.5% 
                            พบข้อผิดพลาดมากที่สุดเกี่ยวกับการอ่านรูปภาพสลิป ซึ่งอาจเกิดจากคุณภาพรูปภาพต่ำหรือไม่ได้มาตรฐาน
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-start">
                        <div className="h-8 w-8 bg-amber-600 rounded-full flex items-center justify-center text-white mr-3 flex-shrink-0">
                          <AlertTriangle className="h-4 w-4" />
                        </div>
                        <div>
                          <p className="text-white font-medium">ข้อแนะนำ</p>
                          <p className="text-slate-300 mt-1">
                            1. แนะนำให้ตรวจสอบคุณภาพรูปภาพสลิปก่อนทำการอัพโหลด<br />
                            2. ระวังปัญหาการเรียกใช้ API ซ้ำเกินความจำเป็น โดยเฉพาะช่วงเวลา 10:00 - 15:00 น.<br />
                            3. ควรเพิ่มการตรวจสอบความถูกต้องของข้อมูลก่อนส่งไปยัง API
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="relative">
                      <input 
                        type="text" 
                        placeholder="พิมพ์คำถามเกี่ยวกับ API หรือวิธีแก้ไขปัญหา..." 
                        className="w-full p-3 pl-4 pr-12 rounded-md bg-indigo-900/30 border border-indigo-800/60 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                      />
                      <button className="absolute right-3 top-1/2 transform -translate-y-1/2 text-indigo-400 hover:text-indigo-300">
                        <Zap className="h-5 w-5" />
                      </button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </>
        ) : (
          <Card className="bg-red-900/30 border-red-800/40">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <AlertTriangle className="h-5 w-5 mr-2 text-red-400" />
                ไม่สามารถเชื่อมต่อกับระบบสถิติได้
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-slate-300">
                ไม่สามารถดึงข้อมูลสถิติได้ในขณะนี้ โปรดตรวจสอบการเชื่อมต่อของคุณและลองอีกครั้ง
              </p>
              <Button 
                className="mt-4 bg-indigo-700 hover:bg-indigo-600"
                onClick={handleRefresh}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                ลองใหม่
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
}