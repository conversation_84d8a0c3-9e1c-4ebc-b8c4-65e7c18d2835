import { Router } from 'express';
import * as emailSettingsController from './email-settings';

const router = Router();

// การตั้งค่าอีเมล (สำหรับผู้ดูแลระบบเท่านั้น)
router.get('/settings', emailSettingsController.getEmailSettings);
router.put('/settings', emailSettingsController.updateEmailSettings);
router.post('/settings/test', emailSettingsController.sendTestEmail);

// เทมเพลตอีเมล
router.get('/templates', emailSettingsController.getEmailTemplates);
router.get('/templates/:id', emailSettingsController.getEmailTemplateById);
router.put('/templates/:id', emailSettingsController.updateEmailTemplate);
router.post('/templates', emailSettingsController.createEmailTemplate);

// ประวัติการส่งอีเมล
router.get('/logs', emailSettingsController.getEmailLogs);

export default router;